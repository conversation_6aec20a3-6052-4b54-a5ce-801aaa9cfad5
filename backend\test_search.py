"""
Test script for Supabase search functionality with specific queries.
"""
import logging
import json
from typing import List, Dict, Any
import numpy as np
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import Supabase client
from supabase_client import supabase

def generate_embedding(query: str) -> List[float]:
    """Generate an embedding for a query using the LLM router."""
    try:
        return llm_router.generate_embedding(query)
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Fall back to mock embedding
        return generate_mock_embedding()

def generate_mock_embedding(dimension=768) -> List[float]:
    """Generate a mock embedding vector for testing."""
    np.random.seed(42)  # For reproducibility
    return list(np.random.rand(dimension))

def test_search_documents():
    """Test searching for documents."""
    logger.info("Testing search_documents...")
    result = supabase.search_documents(
        query_embedding=generate_mock_embedding(),
        match_threshold=0.1,  # Lower threshold to get more results
        match_count=10
    )
    logger.info(f"Found {len(result)} document chunks")
    for i, chunk in enumerate(result):
        logger.info(f"Document chunk {i+1}:")
        logger.info(f"  ID: {chunk.get('id')}")
        logger.info(f"  Document ID: {chunk.get('document_id')}")
        logger.info(f"  Text: {chunk.get('text')[:100]}...")
        logger.info(f"  Similarity: {chunk.get('similarity')}")
        logger.info(f"  Source Type: {chunk.get('source_type')}")

def test_search_websites():
    """Test searching for websites."""
    logger.info("Testing search_websites...")
    result = supabase.search_websites(
        query_embedding=generate_mock_embedding(),
        match_threshold=0.1,  # Lower threshold to get more results
        match_count=10
    )
    logger.info(f"Found {len(result)} website chunks")
    for i, chunk in enumerate(result):
        logger.info(f"Website chunk {i+1}:")
        logger.info(f"  ID: {chunk.get('id')}")
        logger.info(f"  Website ID: {chunk.get('website_id')}")
        logger.info(f"  Text: {chunk.get('text')[:100]}...")
        logger.info(f"  Similarity: {chunk.get('similarity')}")
        logger.info(f"  Source Type: {chunk.get('source_type')}")

def test_hybrid_search_documents():
    """Test hybrid searching for documents."""
    logger.info("Testing hybrid_search_documents...")
    result = supabase.hybrid_search_documents(
        query_text="test document",
        query_embedding=generate_mock_embedding(),
        match_threshold=0.1,  # Lower threshold to get more results
        match_count=10
    )
    logger.info(f"Found {len(result)} document chunks")
    for i, chunk in enumerate(result):
        logger.info(f"Document chunk {i+1}:")
        logger.info(f"  ID: {chunk.get('id')}")
        logger.info(f"  Document ID: {chunk.get('document_id')}")
        logger.info(f"  Text: {chunk.get('text')[:100]}...")
        logger.info(f"  Similarity: {chunk.get('similarity')}")
        logger.info(f"  Source Type: {chunk.get('source_type')}")

def test_hybrid_search_websites():
    """Test hybrid searching for websites."""
    logger.info("Testing hybrid_search_websites...")
    result = supabase.hybrid_search_websites(
        query_text="example website",
        query_embedding=generate_mock_embedding(),
        match_threshold=0.1,  # Lower threshold to get more results
        match_count=10
    )
    logger.info(f"Found {len(result)} website chunks")
    for i, chunk in enumerate(result):
        logger.info(f"Website chunk {i+1}:")
        logger.info(f"  ID: {chunk.get('id')}")
        logger.info(f"  Website ID: {chunk.get('website_id')}")
        logger.info(f"  Text: {chunk.get('text')[:100]}...")
        logger.info(f"  Similarity: {chunk.get('similarity')}")
        logger.info(f"  Source Type: {chunk.get('source_type')}")

def test_specific_query(query: str):
    """Test a specific query against both document and website search."""
    logger.info(f"\n=== Testing query: '{query}' ===\n")

    # Generate embedding for the query
    query_embedding = generate_embedding(query)
    embedding_str = json.dumps(query_embedding)

    # Test direct document search
    logger.info("Testing direct document search...")
    doc_direct_query = f"""
    SELECT * FROM direct_search_document_chunks(
        '{embedding_str}'::vector,
        0.01,  # Very low threshold to find any matches
        10
    )
    """
    doc_direct = supabase.execute_query(doc_direct_query)

    if isinstance(doc_direct, dict) and "error" in doc_direct:
        logger.error(f"Error in direct document search: {doc_direct['error']}")
        doc_direct = []

    logger.info(f"Direct document search found {len(doc_direct) if isinstance(doc_direct, list) else 0} results")

    # Test hybrid document search
    logger.info("Testing hybrid document search...")
    doc_hybrid_query = f"""
    SELECT * FROM hybrid_search_document_chunks(
        '{query.replace("'", "''")}',
        '{embedding_str}'::vector,
        0.01,  # Very low threshold to find any matches
        10
    )
    """
    doc_hybrid = supabase.execute_query(doc_hybrid_query)

    if isinstance(doc_hybrid, dict) and "error" in doc_hybrid:
        logger.error(f"Error in hybrid document search: {doc_hybrid['error']}")
        doc_hybrid = []

    logger.info(f"Hybrid document search found {len(doc_hybrid) if isinstance(doc_hybrid, list) else 0} results")

    # Test direct website search
    logger.info("Testing direct website search...")
    web_direct_query = f"""
    SELECT * FROM direct_search_website_chunks(
        '{embedding_str}'::vector,
        0.01,  # Very low threshold to find any matches
        10
    )
    """
    web_direct = supabase.execute_query(web_direct_query)

    if isinstance(web_direct, dict) and "error" in web_direct:
        logger.error(f"Error in direct website search: {web_direct['error']}")
        web_direct = []

    logger.info(f"Direct website search found {len(web_direct) if isinstance(web_direct, list) else 0} results")

    # Test hybrid website search
    logger.info("Testing hybrid website search...")
    web_hybrid_query = f"""
    SELECT * FROM hybrid_search_website_chunks(
        '{query.replace("'", "''")}',
        '{embedding_str}'::vector,
        0.01,  # Very low threshold to find any matches
        10
    )
    """
    web_hybrid = supabase.execute_query(web_hybrid_query)

    if isinstance(web_hybrid, dict) and "error" in web_hybrid:
        logger.error(f"Error in hybrid website search: {web_hybrid['error']}")
        web_hybrid = []

    logger.info(f"Hybrid website search found {len(web_hybrid) if isinstance(web_hybrid, list) else 0} results")

    # Print results
    if isinstance(doc_direct, list) and len(doc_direct) > 0:
        logger.info("Direct document search results:")
        for i, result in enumerate(doc_direct[:3]):  # Show top 3 results
            logger.info(f"Result {i+1}: similarity={result.get('similarity', 0):.4f}")
            logger.info(f"Text snippet: {result.get('text', '')[:100]}...")

    if isinstance(doc_hybrid, list) and len(doc_hybrid) > 0:
        logger.info("Hybrid document search results:")
        for i, result in enumerate(doc_hybrid[:3]):  # Show top 3 results
            logger.info(f"Result {i+1}: similarity={result.get('similarity', 0):.4f}")
            logger.info(f"Text snippet: {result.get('text', '')[:100]}...")

    if isinstance(web_direct, list) and len(web_direct) > 0:
        logger.info("Direct website search results:")
        for i, result in enumerate(web_direct[:3]):  # Show top 3 results
            logger.info(f"Result {i+1}: similarity={result.get('similarity', 0):.4f}")
            logger.info(f"Text snippet: {result.get('text', '')[:100]}...")

    if isinstance(web_hybrid, list) and len(web_hybrid) > 0:
        logger.info("Hybrid website search results:")
        for i, result in enumerate(web_hybrid[:3]):  # Show top 3 results
            logger.info(f"Result {i+1}: similarity={result.get('similarity', 0):.4f}")
            logger.info(f"Text snippet: {result.get('text', '')[:100]}...")

    logger.info("\n=== Summary ===")
    logger.info(f"Query: '{query}'")
    logger.info(f"Document direct search: {len(doc_direct) if isinstance(doc_direct, list) else 0} results")
    logger.info(f"Document hybrid search: {len(doc_hybrid) if isinstance(doc_hybrid, list) else 0} results")
    logger.info(f"Website direct search: {len(web_direct) if isinstance(web_direct, list) else 0} results")
    logger.info(f"Website hybrid search: {len(web_hybrid) if isinstance(web_hybrid, list) else 0} results")
    logger.info("=" * 50)

    return {
        "doc_direct": doc_direct,
        "doc_hybrid": doc_hybrid,
        "web_direct": web_direct,
        "web_hybrid": web_hybrid
    }

if __name__ == "__main__":
    logger.info("Testing search functionality with specific queries...")

    # Test specific queries
    test_queries = [
        "What is the full form of ACP?",
        "What is the full form of FSDS?",
        "What is the Rapid Response app?",
        "What is VASP and who developed it?"
    ]

    # Run tests for each query
    results = {}
    for query in test_queries:
        results[query] = test_specific_query(query)

    # Print overall summary
    logger.info("\n=== OVERALL SUMMARY ===\n")
    for query, result in results.items():
        doc_direct = result["doc_direct"]
        doc_hybrid = result["doc_hybrid"]
        web_direct = result["web_direct"]
        web_hybrid = result["web_hybrid"]

        logger.info(f"Query: '{query}'")
        logger.info(f"Document direct search: {len(doc_direct) if isinstance(doc_direct, list) else 0} results")
        logger.info(f"Document hybrid search: {len(doc_hybrid) if isinstance(doc_hybrid, list) else 0} results")
        logger.info(f"Website direct search: {len(web_direct) if isinstance(web_direct, list) else 0} results")
        logger.info(f"Website hybrid search: {len(web_hybrid) if isinstance(web_hybrid, list) else 0} results")
        logger.info("-" * 50)

    logger.info("Done!")
