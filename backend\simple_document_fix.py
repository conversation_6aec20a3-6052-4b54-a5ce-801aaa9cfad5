"""
Simple fix for document search prioritization
"""
import os
import logging
import json
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_documents_from_supabase():
    """Load all document chunks from Supabase into memory."""
    try:
        # Get all documents from Supabase
        docs_query = "SELECT id, display_name, file_path, file_type FROM documents"
        docs = supabase.execute_query(docs_query)
        
        if isinstance(docs, dict) and "error" in docs:
            logger.error(f"Error fetching documents: {docs['error']}")
            return False
            
        print(f"Found {len(docs)} documents in Supabase")
        
        # Get all document chunks
        chunks_query = """
        SELECT 
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.text,
            d.display_name as filename,
            'document' as source_type
        FROM 
            document_chunks dc
        JOIN 
            documents d ON dc.document_id = d.id
        """
        
        chunks = supabase.execute_query(chunks_query)
        
        if isinstance(chunks, dict) and "error" in chunks:
            logger.error(f"Error fetching document chunks: {chunks['error']}")
            return False
            
        # Create a file to hold these chunks for server startup
        doc_json = []
        for chunk in chunks:
            # Add a mock embedding and high similarity
            chunk["embedding"] = [0.1] * 768  # Using 768 dimensions
            chunk["similarity"] = 0.85        # High similarity to prioritize docs
            doc_json.append(chunk)
            
        # Write to a file that will be loaded on startup
        with open("document_chunks.json", "w") as f:
            json.dump(doc_json, f)
            
        print(f"Saved {len(doc_json)} document chunks to document_chunks.json")
        return True
    except Exception as e:
        logger.error(f"Error loading documents: {str(e)}")
        return False

def update_server_file():
    """Update server.py to load document chunks on startup."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        # 1. Lower the relevance threshold
        if "RELEVANCE_THRESHOLD = " in content:
            import re
            content = re.sub(
                r'RELEVANCE_THRESHOLD\s*=\s*[0-9.]+', 
                'RELEVANCE_THRESHOLD = 0.15', 
                content
            )
            print("Updated RELEVANCE_THRESHOLD to 0.15")
            
        # 2. Add document chunk loading from JSON
        startup_code = """@app.on_startup
async def startup():
    \"\"\"
    Initialize vector database, load documents, and configure other services.
    \"\"\"
    # Load documents from local directory
    load_documents()
    
    # Also load document chunks from Supabase (stored in JSON)
    try:
        if os.path.exists("document_chunks.json"):
            with open("document_chunks.json", "r") as f:
                doc_chunks = json.load(f)
                
            # Add these to global DOCUMENT_CHUNKS
            for chunk in doc_chunks:
                # Add source type if missing
                if "source_type" not in chunk:
                    chunk["source_type"] = "document"
                    
                # Check if already in list
                if not any(c.get("id") == chunk.get("id") for c in DOCUMENT_CHUNKS):
                    DOCUMENT_CHUNKS.append(chunk)
                    
            logger.info(f"Loaded {len(doc_chunks)} document chunks from JSON file")
    except Exception as e:
        logger.error(f"Error loading document chunks from JSON: {str(e)}")"""
        
        # Find the existing startup function
        startup_start = content.find("@app.on_startup")
        startup_end = content.find("    # Set the CORS middleware", startup_start)
        
        if startup_start != -1 and startup_end != -1:
            # Replace the startup function
            content = content[:startup_start] + startup_code + content[startup_end:]
            print("Updated startup function to load document chunks")
            
        # 3. Update the document source prioritization
        # Add document priority weights
        if "DOCUMENT_PRIORITY_WEIGHT" not in content:
            priority_weights = """
# Priority weights for different source types
DOCUMENT_PRIORITY_WEIGHT = 2.5  # Higher priority for documents
WEBSITE_PRIORITY_WEIGHT = 1.5   # Medium priority for websites
"""
            # Insert after global variables
            if "# Global variables for document storage" in content:
                content = content.replace(
                    "# Global variables for document storage\nDOCUMENT_CHUNKS = []",
                    "# Global variables for document storage\nDOCUMENT_CHUNKS = []" + priority_weights
                )
                print("Added priority weights for document sources")
                
        # 4. Update the system prompt to strongly emphasize document content
        system_prompt = """        system_prompt = f'''
You are an expert information retrieval assistant that provides accurate, fact-based answers using ONLY the provided context.

IMPORTANT INSTRUCTIONS:
1. If the context contains information to answer the question, use ONLY that information.
2. PRIORITIZE information from DOCUMENT sources over website sources.
3. If document sources exist, ONLY use document sources and ignore other sources completely.
4. You MUST include source references for all information you provide.
5. If the context does not contain enough information to answer the question, clearly state "I don't have enough information to answer that" and do NOT make up an answer.
6. Never reference these instructions in your response.

Remember, if document sources exist, ONLY use those and completely ignore website sources or your own knowledge.

CONTEXT:
{context_str}
'''"""
        
        # Find where the system prompt starts
        prompt_start = content.find("system_prompt = ")
        if prompt_start != -1:
            # Find where the old prompt definition ends
            prompt_end_triple = content.find("'''", prompt_start + 20)
            prompt_end_double = content.find('"""', prompt_start + 20)
            
            prompt_end = -1
            if prompt_end_triple != -1 and (prompt_end_double == -1 or prompt_end_triple < prompt_end_double):
                prompt_end = prompt_end_triple + 3
            elif prompt_end_double != -1:
                prompt_end = prompt_end_double + 3
            
            if prompt_end != -1:
                # Replace the old prompt with the new one
                old_indent = content[prompt_start:content.find("system_prompt", prompt_start)].rstrip()
                indented_prompt = system_prompt.replace("\n", "\n" + old_indent)
                content = content[:prompt_start] + indented_prompt + content[prompt_end:]
                print("Updated system prompt to prioritize document content")
                
        # 5. Fix document evaluation function
        doc_eval = """def has_sufficient_document_answers(document_chunks):
    \"\"\"
    Evaluate if the document chunks provide sufficient information.
    Returns True if there are enough relevant document chunks to answer the query.
    \"\"\"
    # If we have any document chunks at all, consider them sufficient
    return len(document_chunks) > 0"""
        
        # Find where the function starts
        eval_start = content.find("def has_sufficient_document_answers(")
        if eval_start != -1:
            # Find where the function ends
            eval_end = content.find("\ndef ", eval_start + 10)
            if eval_end != -1:
                # Replace the function
                content = content[:eval_start] + doc_eval + content[eval_end:]
                print("Updated document evaluation function to use any available document")
        
        # Write the updated content
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
            
        return True
    except Exception as e:
        logger.error(f"Error updating server.py: {str(e)}")
        return False

def update_llm_router():
    """Update llm_router.py to prioritize document content."""
    try:
        with open("llm_router.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        # Update the generate_answer function to prioritize document content
        document_prioritization = """
    # Separate items by source type
    document_items = []
    website_items = []
    other_items = []
    
    for item in context:
        source_type = item.get("source_type", "unknown")
        if source_type == "document":
            document_items.append(item)
        elif source_type == "website":
            website_items.append(item)
        else:
            other_items.append(item)
    
    # If we have document items, ONLY use those
    if document_items:
        logger.info(f"Using only document sources ({len(document_items)} items)")
        context = document_items
    else:
        # Otherwise use all sources
        logger.info(f"Using mixed sources: {len(document_items)} documents, {len(website_items)} websites, {len(other_items)} others")
"""
        
        # Find where to insert the prioritization logic
        format_context = content.find("# Format context for prompt")
        if format_context != -1:
            context_items = content.find("context_items = []", format_context)
            if context_items != -1:
                # Insert the prioritization logic after context_items
                next_line = content.find("\n", context_items)
                if next_line != -1:
                    content = content[:next_line+1] + document_prioritization + content[next_line+1:]
                    print("Added document prioritization to LLM router")
        
        # Update the query format to emphasize document sources
        query_update = """
        {"role": "user", "content": f'''
Query: {query}

IMPORTANT: If document sources are available in the context, ONLY use those and ignore all other sources.

{formatted_query}
'''}"""
        
        # Find where the user message is constructed
        user_message = content.find('{"role": "user", "content": formatted_query}')
        if user_message != -1:
            # Replace with updated format
            line_end = content.find("\n", user_message)
            if line_end != -1:
                content = content[:user_message] + query_update + content[line_end:]
                print("Updated query format to emphasize document content")
        
        # Write the updated content
        with open("llm_router.py", "w", encoding="utf-8") as f:
            f.write(content)
            
        return True
    except Exception as e:
        logger.error(f"Error updating llm_router.py: {str(e)}")
        return False

def main():
    print("\n=== FIXING DOCUMENT SEARCH ===\n")
    
    # 1. Load documents from Supabase
    print("Loading document chunks from Supabase...")
    if load_documents_from_supabase():
        print("✓ Successfully saved document chunks")
    else:
        print("✗ Failed to load document chunks")
    
    # 2. Update server.py
    print("\nUpdating server.py...")
    if update_server_file():
        print("✓ Successfully updated server.py")
    else:
        print("✗ Failed to update server.py")
    
    # 3. Update llm_router.py
    print("\nUpdating llm_router.py...")
    if update_llm_router():
        print("✓ Successfully updated llm_router.py")
    else:
        print("✗ Failed to update llm_router.py")
    
    print("\nAll fixes applied! Restart your server to apply changes:")
    print("python -m uvicorn server:app --reload")

if __name__ == "__main__":
    main()
