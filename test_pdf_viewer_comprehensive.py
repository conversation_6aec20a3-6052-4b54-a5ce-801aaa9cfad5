#!/usr/bin/env python3
"""
Comprehensive test script for RailGPT PDF viewer functionality.
Tests document source link generation and PDF viewer features.
"""

import requests
import time
import json
import sys

def test_backend_health():
    """Test that the backend server is running."""
    print("=== Testing Backend Health ===")
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend server not accessible: {str(e)}")
        return False

def test_pdf_serving():
    """Test that PDF files are served correctly by the backend."""
    print("\n=== Testing PDF File Serving ===")
    
    test_files = [
        "ACP%20110V.pdf",
        "Authority%20Transfer%20Declaration.pdf", 
        "SampleRailwayDoc.pdf"
    ]
    
    success_count = 0
    for filename in test_files:
        try:
            url = f"http://localhost:8000/api/documents/view/{filename}"
            print(f"\nTesting: {filename}")
            print(f"URL: {url}")
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                content_length = len(response.content)
                
                print(f"✅ Status: {response.status_code}")
                print(f"✅ Content-Type: {content_type}")
                print(f"✅ File size: {content_length:,} bytes")
                
                if 'pdf' in content_type.lower():
                    print(f"✅ Correct PDF content type")
                else:
                    print(f"⚠️  Unexpected content type: {content_type}")
                    
                # Check if content looks like a PDF
                if response.content.startswith(b'%PDF'):
                    print(f"✅ Valid PDF file signature")
                    success_count += 1
                else:
                    print(f"❌ Invalid PDF file signature")
                    
            else:
                print(f"❌ Failed with status {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {str(e)}")
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")
    
    return success_count == len(test_files)

def test_document_search_and_links():
    """Test that document search returns proper source links."""
    print("\n=== Testing Document Search and Source Links ===")
    
    test_queries = [
        "What is authority transfer?",
        "What is ACP?",
        "railway operations",
        "maintenance procedures"
    ]
    
    success_count = 0
    for query in test_queries:
        try:
            print(f"\nTesting query: '{query}'")
            
            # Send query to the backend
            response = requests.post(
                "http://localhost:8000/api/query",
                json={"query": query, "model": "gemini-2.0-flash"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Query successful")
                
                # Check for document sources
                document_sources = data.get('document_sources', [])
                website_sources = data.get('website_sources', [])
                llm_fallback = data.get('llm_fallback', True)
                
                print(f"   Document sources: {len(document_sources)}")
                print(f"   Website sources: {len(website_sources)}")
                print(f"   LLM fallback: {llm_fallback}")
                
                if document_sources:
                    print("✅ Found document sources")
                    for i, source in enumerate(document_sources[:2]):
                        if isinstance(source, dict):
                            filename = source.get('filename', 'Unknown')
                            page = source.get('page', 1)
                            link = source.get('link', '')
                            print(f"   Source {i+1}: {filename} (Page {page})")
                            if '/viewer?file=' in link:
                                print(f"   ✅ Correct link format: {link}")
                                success_count += 1
                            else:
                                print(f"   ❌ Incorrect link format: {link}")
                        else:
                            print(f"   Source {i+1}: {source}")
                else:
                    print("⚠️  No document sources found")
                    if not llm_fallback:
                        print("❌ Expected document sources but got none")
                
            else:
                print(f"❌ Query failed with status {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ Query error: {str(e)}")
    
    return success_count > 0

def test_frontend_access():
    """Test that the frontend document viewer page can be accessed."""
    print("\n=== Testing Frontend Document Viewer Access ===")
    
    test_urls = [
        "http://localhost:3000/viewer?file=ACP%20110V.pdf&page=1",
        "http://localhost:3000/viewer?file=Authority%20Transfer%20Declaration.pdf&page=1",
        "http://localhost:3000/viewer?file=SampleRailwayDoc.pdf&page=1"
    ]
    
    success_count = 0
    for url in test_urls:
        try:
            print(f"\nTesting frontend URL: {url}")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ Frontend accessible (Status: {response.status_code})")
                if 'RailGPT' in response.text:
                    print(f"✅ RailGPT app loaded correctly")
                    success_count += 1
                else:
                    print(f"⚠️  Page loaded but may not be RailGPT app")
            else:
                print(f"❌ Frontend not accessible (Status: {response.status_code})")
                
        except Exception as e:
            print(f"❌ Frontend access error: {str(e)}")
    
    return success_count > 0

def main():
    """Run comprehensive PDF viewer tests."""
    print("🔧 RailGPT PDF Viewer Comprehensive Testing")
    print("=" * 60)
    
    # Test results
    results = {
        'backend_health': False,
        'pdf_serving': False,
        'document_search': False,
        'frontend_access': False
    }
    
    # Run tests
    results['backend_health'] = test_backend_health()
    
    if results['backend_health']:
        results['pdf_serving'] = test_pdf_serving()
        results['document_search'] = test_document_search_and_links()
    
    results['frontend_access'] = test_frontend_access()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 Test Results Summary:")
    print(f"   Backend Health: {'✅ PASS' if results['backend_health'] else '❌ FAIL'}")
    print(f"   PDF Serving: {'✅ PASS' if results['pdf_serving'] else '❌ FAIL'}")
    print(f"   Document Search: {'✅ PASS' if results['document_search'] else '❌ FAIL'}")
    print(f"   Frontend Access: {'✅ PASS' if results['frontend_access'] else '❌ FAIL'}")
    
    all_passed = all(results.values())
    print(f"\n🏆 Overall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if not all_passed:
        print("\n📝 Manual Testing Steps:")
        print("1. Ensure backend server is running: python -m uvicorn server:app --reload --port 8000")
        print("2. Ensure frontend is running: npm start (in frontend directory)")
        print("3. Open: http://localhost:3000")
        print("4. Ask: 'What is authority transfer?' or 'What is ACP?'")
        print("5. Click on document source links to test PDF viewer")
        print("6. Test different viewer options: Pro PDF, React PDF, Simple")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
