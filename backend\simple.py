from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

app = FastAPI()

# Allow CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class Query(BaseModel):
    query: str

@app.get("/")
def read_root():
    return {"message": "API is running"}

@app.post("/api/query")
async def process_query(query: Query):
    return {
        "answer": "This is a mocked response from RailGPT backend.",
        "sources": ["SampleDoc.pdf Page 1", "SampleDoc.pdf Page 2"]
    }
