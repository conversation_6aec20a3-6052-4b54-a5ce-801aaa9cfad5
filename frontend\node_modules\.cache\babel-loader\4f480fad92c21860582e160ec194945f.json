{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\components\\\\documents\\\\DocumentUploadForm.tsx\";\nimport React, { useState, useEffect } from 'react';\nimport { uploadDocument } from '../../services/api';\nimport { Alert, AlertDescription, AlertTitle } from \"../ui/alert\";\n\n// Component for uploading documents with category selection\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DocumentUploadForm = () => {\n  // Form state\n  const [documentName, setDocumentName] = useState('');\n  const [mainCategory, setMainCategory] = useState('');\n  const [category, setCategory] = useState('');\n  const [subCategory, setSubCategory] = useState('');\n  const [minorCategory, setMinorCategory] = useState('');\n  const [file, setFile] = useState(null);\n\n  // UI state\n  const [isUploading, setIsUploading] = useState(false);\n  const [newCategoryInput, setNewCategoryInput] = useState('');\n  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);\n  const [categoryType, setCategoryType] = useState('Main');\n\n  // Upload status tracking\n  const [uploadStatus, setUploadStatus] = useState('idle');\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [extractedChunks, setExtractedChunks] = useState([]);\n\n  // Form validation and feedback\n  const [errors, setErrors] = useState({});\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [touched, setTouched] = useState({});\n\n  // Categories from API (would fetch from backend in production)\n  const [mainCategories, setMainCategories] = useState([{\n    id: '1',\n    name: 'Operations',\n    type: 'Main'\n  }, {\n    id: '2',\n    name: 'Technical',\n    type: 'Main'\n  }, {\n    id: '3',\n    name: 'Administrative',\n    type: 'Main'\n  }, {\n    id: '4',\n    name: 'Safety',\n    type: 'Main'\n  }]);\n  const [categories, setCategories] = useState([{\n    id: '1',\n    name: 'Manuals',\n    type: 'Category',\n    parentId: '1'\n  }, {\n    id: '2',\n    name: 'Circulars',\n    type: 'Category',\n    parentId: '1'\n  }, {\n    id: '3',\n    name: 'Guidelines',\n    type: 'Category',\n    parentId: '2'\n  }, {\n    id: '4',\n    name: 'Reports',\n    type: 'Category',\n    parentId: '3'\n  }]);\n  const [subCategories, setSubCategories] = useState([{\n    id: '1',\n    name: 'Diesel Loco',\n    type: 'Sub',\n    parentId: '1'\n  }, {\n    id: '2',\n    name: 'Electric Loco',\n    type: 'Sub',\n    parentId: '1'\n  }, {\n    id: '3',\n    name: 'Passenger',\n    type: 'Sub',\n    parentId: '2'\n  }, {\n    id: '4',\n    name: 'Freight',\n    type: 'Sub',\n    parentId: '2'\n  }]);\n\n  // Effect to validate form\n  useEffect(() => {\n    let isMounted = true;\n    const newErrors = {};\n    if (touched.documentName && !documentName.trim()) {\n      newErrors.documentName = 'Document name is required';\n    }\n    if (touched.mainCategory && !mainCategory) {\n      newErrors.mainCategory = 'Main category is required';\n    }\n    if (touched.category && !category) {\n      newErrors.category = 'Category is required';\n    }\n    if (touched.file && !file) {\n      newErrors.file = 'Document file is required';\n    } else if (file) {\n      // Validate file type\n      const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'image/jpeg', 'image/png', 'text/plain'];\n      if (!allowedTypes.includes(file.type)) {\n        newErrors.file = 'File type not supported. Please upload PDF, DOCX, XLSX, JPG, PNG, or TXT';\n      }\n\n      // Validate file size (200MB max)\n      const maxSize = 200 * 1024 * 1024; // 200MB\n      if (file.size > maxSize) {\n        newErrors.file = 'File too large. Maximum size is 200MB';\n      }\n    }\n\n    // Only update state if component is still mounted\n    if (isMounted) {\n      setErrors(newErrors);\n    }\n\n    // Cleanup function\n    return () => {\n      isMounted = false;\n    };\n  }, [documentName, mainCategory, category, file, touched]);\n\n  // Mark fields as touched when user interacts with them\n  const markAsTouched = field => {\n    setTouched(prev => ({\n      ...prev,\n      [field]: true\n    }));\n  };\n\n  // useEffect to clear success message after 5 seconds\n  useEffect(() => {\n    if (successMessage) {\n      const timer = setTimeout(() => {\n        setSuccessMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage]);\n\n  // useEffect to clear error message after 5 seconds\n  useEffect(() => {\n    if (errorMessage) {\n      const timer = setTimeout(() => {\n        setErrorMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [errorMessage]);\n  const handleFileChange = e => {\n    if (e.target.files && e.target.files[0]) {\n      const selectedFile = e.target.files[0];\n      setFile(selectedFile);\n      markAsTouched('file');\n\n      // Set document name from filename if empty\n      if (!documentName.trim()) {\n        // Remove extension and replace underscores/hyphens with spaces\n        const nameFromFile = selectedFile.name.replace(/\\.[^/.]+$/, '').replace(/[_-]/g, ' ');\n        setDocumentName(nameFromFile);\n        markAsTouched('documentName');\n      }\n    }\n  };\n  const handleNewCategorySubmit = async () => {\n    if (!newCategoryInput.trim()) return;\n    setIsUploading(true);\n    try {\n      // In a real app, you would make an API call to create the category\n      // Example API call:\n      // const response = await fetch(`${API_URL}/api/categories`, {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ name: newCategoryInput, type: categoryType })\n      // });\n      // if (!response.ok) throw new Error('Failed to create category');\n      // const data = await response.json();\n\n      // For now, simulate API call with a delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      // Update the appropriate category state\n      if (categoryType === 'Main') {\n        const newCategory = {\n          id: `new-${Date.now()}`,\n          name: newCategoryInput,\n          type: 'Main'\n        };\n        setMainCategories(prev => [...prev, newCategory]);\n        setMainCategory(newCategoryInput);\n      } else if (categoryType === 'Category') {\n        const newCategory = {\n          id: `new-${Date.now()}`,\n          name: newCategoryInput,\n          type: 'Category',\n          parentId: mainCategory\n        };\n        setCategories(prev => [...prev, newCategory]);\n        setCategory(newCategoryInput);\n      } else if (categoryType === 'Sub') {\n        const newCategory = {\n          id: `new-${Date.now()}`,\n          name: newCategoryInput,\n          type: 'Sub',\n          parentId: category\n        };\n        setSubCategories(prev => [...prev, newCategory]);\n        setSubCategory(newCategoryInput);\n      } else {\n        // Minor category handling\n        setMinorCategory(newCategoryInput);\n      }\n      setSuccessMessage(`Created new ${categoryType} category: ${newCategoryInput}`);\n    } catch (error) {\n      setErrorMessage(`Failed to create category: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    } finally {\n      setIsUploading(false);\n      setNewCategoryInput('');\n      setShowNewCategoryInput(false);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Mark all fields as touched to show any errors\n    markAsTouched('documentName');\n    markAsTouched('mainCategory');\n    markAsTouched('category');\n    markAsTouched('file');\n\n    // Check if there are any errors\n    if (Object.keys(errors).length > 0 || !file) {\n      setErrorMessage('Please correct the errors before submitting');\n      return;\n    }\n\n    // Track if component is still mounted\n    let isMounted = true;\n    let progressInterval = null;\n\n    // Update UI state\n    const safeSetState = updater => {\n      if (isMounted) {\n        updater();\n      }\n    };\n    safeSetState(() => {\n      setIsUploading(true);\n      setUploadStatus('uploading');\n      setUploadProgress(10);\n      setSuccessMessage('');\n      setErrorMessage('');\n      setExtractedChunks([]);\n    });\n    try {\n      // Simulate upload progress updates (in a real app, use XHR or fetch with progress events)\n      progressInterval = setInterval(() => {\n        if (isMounted) {\n          setUploadProgress(prev => {\n            const newProgress = prev + 5;\n            if (newProgress >= 90) {\n              if (progressInterval) clearInterval(progressInterval);\n              return 90; // Hold at 90% until processing is complete\n            }\n            return newProgress;\n          });\n        }\n      }, 300);\n\n      // Upload the document\n      safeSetState(() => setUploadStatus('uploading'));\n\n      // Upload the document using the API function\n      const response = await uploadDocument(file, \"<EMAIL>\");\n\n      // Clear the progress interval if it exists\n      if (progressInterval) {\n        clearInterval(progressInterval);\n        progressInterval = null;\n      }\n\n      // Only update state if component is still mounted\n      if (!isMounted) return;\n      if (response.success) {\n        var _response$data, _file$name$split$pop;\n        // Set progress to 100%\n        setUploadProgress(100);\n        setUploadStatus('success');\n\n        // Store extracted chunks if available\n        if (response.chunks && response.chunks.length > 0) {\n          setExtractedChunks(response.chunks);\n          console.log('Extracted chunks:', response.chunks);\n        }\n\n        // Create event data for custom event\n        const eventData = {\n          detail: {\n            documentName,\n            mainCategory,\n            category,\n            subCategory,\n            minorCategory,\n            file,\n            uploadedAt: new Date().toISOString(),\n            id: ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.id) || `doc-${Date.now()}`,\n            status: 'Extracted',\n            // Update to real status from backend\n            fileType: (_file$name$split$pop = file.name.split('.').pop()) === null || _file$name$split$pop === void 0 ? void 0 : _file$name$split$pop.toLowerCase(),\n            qualityScore: 90,\n            // This would come from backend in real app\n            chunks: response.chunks || [],\n            extractedContent: response.chunks ? response.chunks.map(chunk => chunk.text).join('\\n\\n') : '',\n            chunks_extracted: response.chunks_extracted || 0\n          }\n        };\n\n        // Dispatch event to notify the DocumentsPage\n        const event = new CustomEvent('documentUploaded', {\n          detail: eventData.detail\n        });\n        window.dispatchEvent(event);\n\n        // Set success message (ensure error is cleared)\n        setErrorMessage('');\n        setSuccessMessage(`Document \"${documentName}\" uploaded successfully!`);\n\n        // Auto-clear success message after 5 seconds\n        const clearSuccessMessage = () => {\n          if (isMounted) {\n            setSuccessMessage('');\n          }\n        };\n        setTimeout(clearSuccessMessage, 5000);\n\n        // Reset form after successful upload\n        const resetForm = () => {\n          if (isMounted) {\n            setDocumentName('');\n            setMainCategory('');\n            setCategory('');\n            setSubCategory('');\n            setMinorCategory('');\n            setFile(null);\n            setUploadStatus('idle');\n            setUploadProgress(0);\n            setExtractedChunks([]);\n            setIsUploading(false);\n            setTouched({});\n          }\n        };\n        setTimeout(resetForm, 3000);\n      } else {\n        // Handle upload error\n        setUploadStatus('error');\n        setErrorMessage(response.message || 'Failed to upload document');\n      }\n    } catch (error) {\n      console.error('Upload failed:', error);\n      if (isMounted) {\n        setUploadStatus('error');\n        setErrorMessage(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      }\n    } finally {\n      if (isMounted) {\n        setIsUploading(false);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-md p-6 transition-colors duration-300\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-lg font-semibold mb-4 text-gray-900\",\n      children: \"Upload Document\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"success\",\n        children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n          children: \"Success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AlertDescription, {\n          children: successMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 9\n    }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"destructive\",\n        children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AlertDescription, {\n          children: errorMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: [\"Document Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 27\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: documentName,\n          onChange: e => setDocumentName(e.target.value),\n          onBlur: () => markAsTouched('documentName'),\n          className: `w-full p-2 border ${errors.documentName ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), errors.documentName && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-red-500\",\n          children: errors.documentName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: [\"Main Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 27\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: mainCategory,\n            onChange: e => setMainCategory(e.target.value),\n            onBlur: () => markAsTouched('mainCategory'),\n            className: `flex-1 p-2 border ${errors.mainCategory ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Main Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), mainCategories.map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: cat.name,\n              children: cat.name\n            }, cat.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => {\n              setCategoryType('Main');\n              setShowNewCategoryInput(true);\n            },\n            className: \"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\",\n            children: \"+ New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this), errors.mainCategory && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-red-500\",\n          children: errors.mainCategory\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 22\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: category,\n            onChange: e => setCategory(e.target.value),\n            onBlur: () => markAsTouched('category'),\n            className: `flex-1 p-2 border ${errors.category ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`,\n            required: true,\n            disabled: !mainCategory,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), categories.filter(cat => {\n              var _mainCategories$find;\n              return !mainCategory || cat.parentId === ((_mainCategories$find = mainCategories.find(m => m.name === mainCategory)) === null || _mainCategories$find === void 0 ? void 0 : _mainCategories$find.id);\n            }).map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: cat.name,\n              children: cat.name\n            }, cat.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => {\n              setCategoryType('Category');\n              setShowNewCategoryInput(true);\n            },\n            className: \"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\",\n            disabled: !mainCategory,\n            children: \"+ New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), errors.category && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-red-500\",\n          children: errors.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: \"Sub Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: subCategory,\n            onChange: e => setSubCategory(e.target.value),\n            className: \"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\",\n            disabled: !category,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Sub Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this), subCategories.filter(sub => {\n              var _categories$find;\n              return !category || sub.parentId === ((_categories$find = categories.find(c => c.name === category)) === null || _categories$find === void 0 ? void 0 : _categories$find.id);\n            }).map(sub => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: sub.name,\n              children: sub.name\n            }, sub.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => {\n              setCategoryType('Sub');\n              setShowNewCategoryInput(true);\n            },\n            className: \"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\",\n            disabled: !category,\n            children: \"+ New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: \"Minor Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: minorCategory,\n            onChange: e => setMinorCategory(e.target.value),\n            className: \"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\",\n            placeholder: \"Enter Minor Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => {\n              setCategoryType('Minor');\n              setShowNewCategoryInput(true);\n            },\n            className: \"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\",\n            children: \"+ New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: [\"Upload Document \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: `flex flex-col w-full h-32 border-2 ${errors.file ? 'border-red-500' : 'border-blue-300'} border-dashed hover:bg-gray-50 hover:border-blue-500 rounded-lg cursor-pointer`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center justify-center pt-7\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-8 h-8 text-gray-400 group-hover:text-gray-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600\",\n                children: file ? file.name : 'Attach document (PDF, DOCX, XLSX, etc.)'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              className: \"opacity-0\",\n              accept: \".pdf,.docx,.xlsx,.txt,.png,.jpg,.jpeg\",\n              onChange: handleFileChange,\n              onBlur: () => markAsTouched('file'),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this), errors.file && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-red-500\",\n          children: errors.file\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isUploading,\n          className: `w-full px-4 py-2 text-white font-medium rounded-md ${isUploading ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'} focus:outline-none focus:ring-2 focus:ring-blue-500`,\n          children: isUploading ? 'Uploading...' : 'Upload Document'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this), uploadStatus !== 'idle' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-700\",\n            children: uploadStatus === 'uploading' ? 'Uploading...' : uploadStatus === 'processing' ? 'Processing...' : uploadStatus === 'success' ? 'Upload Complete' : 'Upload Failed'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-700\",\n            children: [uploadProgress, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-gray-200 rounded-full h-2.5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `h-2.5 rounded-full ${uploadStatus === 'error' ? 'bg-red-600' : uploadStatus === 'success' ? 'bg-green-600' : 'bg-blue-600'}`,\n            style: {\n              width: `${uploadProgress}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 11\n      }, this), uploadStatus === 'success' && extractedChunks.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-700 mb-2\",\n          children: \"Extracted Content Preview:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-h-40 overflow-y-auto text-sm text-gray-600\",\n          children: [extractedChunks.slice(0, 3).map((chunk, index) => {\n            var _chunk$text;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2 p-2 bg-white rounded border border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mb-1\",\n                children: chunk.source_type === 'document' ? `Page ${chunk.page || 'N/A'}` : `Source: ${chunk.source || 'Unknown'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [(_chunk$text = chunk.text) === null || _chunk$text === void 0 ? void 0 : _chunk$text.substring(0, 150), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 17\n            }, this);\n          }), extractedChunks.length > 3 && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 text-center mt-2\",\n            children: [\"+ \", extractedChunks.length - 3, \" more chunks not shown\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this), showNewCategoryInput && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium mb-4\",\n          children: [\"Add New \", categoryType, \" Category\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: newCategoryInput,\n          onChange: e => setNewCategoryInput(e.target.value),\n          className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4\",\n          placeholder: `Enter ${categoryType} Category name`,\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNewCategoryInput(false),\n            className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleNewCategorySubmit,\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n            children: \"Add Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 646,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 360,\n    columnNumber: 5\n  }, this);\n};\nexport default DocumentUploadForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "uploadDocument", "<PERSON><PERSON>", "AlertDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "DocumentUploadForm", "documentName", "setDocumentName", "mainCategory", "setMainCategory", "category", "setCategory", "subCategory", "setSubCategory", "minorCategory", "setMinorCategory", "file", "setFile", "isUploading", "setIsUploading", "newCategoryInput", "setNewCategoryInput", "showNewCategoryInput", "setShowNewCategoryInput", "categoryType", "setCategoryType", "uploadStatus", "setUploadStatus", "uploadProgress", "setUploadProgress", "extractedChunks", "setExtractedChunks", "errors", "setErrors", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "touched", "setTouched", "mainCategories", "setMainCategories", "id", "name", "type", "categories", "setCategories", "parentId", "subCategories", "setSubCategories", "isMounted", "newErrors", "trim", "allowedTypes", "includes", "maxSize", "size", "<PERSON><PERSON><PERSON><PERSON>ched", "field", "prev", "timer", "setTimeout", "clearTimeout", "handleFileChange", "e", "target", "files", "selectedFile", "nameFromFile", "replace", "handleNewCategorySubmit", "Promise", "resolve", "newCategory", "Date", "now", "error", "Error", "message", "handleSubmit", "preventDefault", "Object", "keys", "length", "progressInterval", "safeSetState", "updater", "setInterval", "newProgress", "clearInterval", "response", "success", "_response$data", "_file$name$split$pop", "chunks", "console", "log", "eventData", "detail", "uploadedAt", "toISOString", "data", "status", "fileType", "split", "pop", "toLowerCase", "qualityScore", "extractedContent", "map", "chunk", "text", "join", "chunks_extracted", "event", "CustomEvent", "window", "dispatchEvent", "clearSuccessMessage", "resetForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onSubmit", "value", "onChange", "onBlur", "required", "cat", "onClick", "disabled", "filter", "_mainCategories$find", "find", "m", "sub", "_categories$find", "c", "placeholder", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "accept", "style", "width", "slice", "index", "_chunk$text", "source_type", "page", "source", "substring", "autoFocus"], "sources": ["C:/IR App/frontend/src/components/documents/DocumentUploadForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { uploadDocument } from '../../services/api';\r\nimport { Alert, AlertDescription, AlertTitle } from \"../ui/alert\";\r\nimport { DocumentCategory } from '../../types/documents';\r\nimport CategoryCreator from '../categories/CategoryCreator';\r\nimport { FolderPlus } from 'lucide-react';\r\n\r\n// Component for uploading documents with category selection\r\ninterface FormErrors {\r\n  documentName?: string;\r\n  mainCategory?: string;\r\n  category?: string;\r\n  file?: string;\r\n}\r\n\r\nconst DocumentUploadForm: React.FC = (): React.ReactElement => {\r\n  // Form state\r\n  const [documentName, setDocumentName] = useState('');\r\n  const [mainCategory, setMainCategory] = useState('');\r\n  const [category, setCategory] = useState('');\r\n  const [subCategory, setSubCategory] = useState('');\r\n  const [minorCategory, setMinorCategory] = useState('');\r\n  const [file, setFile] = useState<File | null>(null);\r\n\r\n  // UI state\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [newCategoryInput, setNewCategoryInput] = useState('');\r\n  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);\r\n  const [categoryType, setCategoryType] = useState<'Main' | 'Category' | 'Sub' | 'Minor'>('Main');\r\n\r\n  // Upload status tracking\r\n  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'processing' | 'success' | 'error'>('idle');\r\n  const [uploadProgress, setUploadProgress] = useState(0);\r\n  const [extractedChunks, setExtractedChunks] = useState<any[]>([]);\r\n\r\n  // Form validation and feedback\r\n  const [errors, setErrors] = useState<FormErrors>({});\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [touched, setTouched] = useState<Record<string, boolean>>({});\r\n\r\n  // Categories from API (would fetch from backend in production)\r\n  const [mainCategories, setMainCategories] = useState<DocumentCategory[]>([\r\n    { id: '1', name: 'Operations', type: 'Main' },\r\n    { id: '2', name: 'Technical', type: 'Main' },\r\n    { id: '3', name: 'Administrative', type: 'Main' },\r\n    { id: '4', name: 'Safety', type: 'Main' }\r\n  ]);\r\n\r\n  const [categories, setCategories] = useState<DocumentCategory[]>([\r\n    { id: '1', name: 'Manuals', type: 'Category', parentId: '1' },\r\n    { id: '2', name: 'Circulars', type: 'Category', parentId: '1' },\r\n    { id: '3', name: 'Guidelines', type: 'Category', parentId: '2' },\r\n    { id: '4', name: 'Reports', type: 'Category', parentId: '3' }\r\n  ]);\r\n\r\n  const [subCategories, setSubCategories] = useState<DocumentCategory[]>([\r\n    { id: '1', name: 'Diesel Loco', type: 'Sub', parentId: '1' },\r\n    { id: '2', name: 'Electric Loco', type: 'Sub', parentId: '1' },\r\n    { id: '3', name: 'Passenger', type: 'Sub', parentId: '2' },\r\n    { id: '4', name: 'Freight', type: 'Sub', parentId: '2' }\r\n  ]);\r\n\r\n  // Effect to validate form\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const newErrors: FormErrors = {};\r\n\r\n    if (touched.documentName && !documentName.trim()) {\r\n      newErrors.documentName = 'Document name is required';\r\n    }\r\n\r\n    if (touched.mainCategory && !mainCategory) {\r\n      newErrors.mainCategory = 'Main category is required';\r\n    }\r\n\r\n    if (touched.category && !category) {\r\n      newErrors.category = 'Category is required';\r\n    }\r\n\r\n    if (touched.file && !file) {\r\n      newErrors.file = 'Document file is required';\r\n    } else if (file) {\r\n      // Validate file type\r\n      const allowedTypes = [\r\n        'application/pdf',\r\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n        'image/jpeg',\r\n        'image/png',\r\n        'text/plain'\r\n      ];\r\n\r\n      if (!allowedTypes.includes(file.type)) {\r\n        newErrors.file = 'File type not supported. Please upload PDF, DOCX, XLSX, JPG, PNG, or TXT';\r\n      }\r\n\r\n      // Validate file size (200MB max)\r\n      const maxSize = 200 * 1024 * 1024; // 200MB\r\n      if (file.size > maxSize) {\r\n        newErrors.file = 'File too large. Maximum size is 200MB';\r\n      }\r\n    }\r\n\r\n    // Only update state if component is still mounted\r\n    if (isMounted) {\r\n      setErrors(newErrors);\r\n    }\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [documentName, mainCategory, category, file, touched]);\r\n\r\n  // Mark fields as touched when user interacts with them\r\n  const markAsTouched = (field: string) => {\r\n    setTouched(prev => ({ ...prev, [field]: true }));\r\n  };\r\n\r\n\r\n\r\n  // useEffect to clear success message after 5 seconds\r\n  useEffect(() => {\r\n    if (successMessage) {\r\n      const timer = setTimeout(() => {\r\n        setSuccessMessage('');\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [successMessage]);\r\n\r\n  // useEffect to clear error message after 5 seconds\r\n  useEffect(() => {\r\n    if (errorMessage) {\r\n      const timer = setTimeout(() => {\r\n        setErrorMessage('');\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [errorMessage]);\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files[0]) {\r\n      const selectedFile = e.target.files[0];\r\n      setFile(selectedFile);\r\n      markAsTouched('file');\r\n\r\n      // Set document name from filename if empty\r\n      if (!documentName.trim()) {\r\n        // Remove extension and replace underscores/hyphens with spaces\r\n        const nameFromFile = selectedFile.name.replace(/\\.[^/.]+$/, '').replace(/[_-]/g, ' ');\r\n        setDocumentName(nameFromFile);\r\n        markAsTouched('documentName');\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleNewCategorySubmit = async () => {\r\n    if (!newCategoryInput.trim()) return;\r\n\r\n    setIsUploading(true);\r\n    try {\r\n      // In a real app, you would make an API call to create the category\r\n      // Example API call:\r\n      // const response = await fetch(`${API_URL}/api/categories`, {\r\n      //   method: 'POST',\r\n      //   headers: { 'Content-Type': 'application/json' },\r\n      //   body: JSON.stringify({ name: newCategoryInput, type: categoryType })\r\n      // });\r\n      // if (!response.ok) throw new Error('Failed to create category');\r\n      // const data = await response.json();\r\n\r\n      // For now, simulate API call with a delay\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      // Update the appropriate category state\r\n      if (categoryType === 'Main') {\r\n        const newCategory: DocumentCategory = { id: `new-${Date.now()}`, name: newCategoryInput, type: 'Main' };\r\n        setMainCategories(prev => [...prev, newCategory]);\r\n        setMainCategory(newCategoryInput);\r\n      } else if (categoryType === 'Category') {\r\n        const newCategory: DocumentCategory = { id: `new-${Date.now()}`, name: newCategoryInput, type: 'Category', parentId: mainCategory };\r\n        setCategories(prev => [...prev, newCategory]);\r\n        setCategory(newCategoryInput);\r\n      } else if (categoryType === 'Sub') {\r\n        const newCategory: DocumentCategory = { id: `new-${Date.now()}`, name: newCategoryInput, type: 'Sub', parentId: category };\r\n        setSubCategories(prev => [...prev, newCategory]);\r\n        setSubCategory(newCategoryInput);\r\n      } else {\r\n        // Minor category handling\r\n        setMinorCategory(newCategoryInput);\r\n      }\r\n\r\n      setSuccessMessage(`Created new ${categoryType} category: ${newCategoryInput}`);\r\n    } catch (error) {\r\n      setErrorMessage(`Failed to create category: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    } finally {\r\n      setIsUploading(false);\r\n      setNewCategoryInput('');\r\n      setShowNewCategoryInput(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent): Promise<void> => {\r\n    e.preventDefault();\r\n\r\n    // Mark all fields as touched to show any errors\r\n    markAsTouched('documentName');\r\n    markAsTouched('mainCategory');\r\n    markAsTouched('category');\r\n    markAsTouched('file');\r\n\r\n    // Check if there are any errors\r\n    if (Object.keys(errors).length > 0 || !file) {\r\n      setErrorMessage('Please correct the errors before submitting');\r\n      return;\r\n    }\r\n\r\n    // Track if component is still mounted\r\n    let isMounted = true;\r\n    let progressInterval: NodeJS.Timeout | null = null;\r\n\r\n    // Update UI state\r\n    const safeSetState = (updater: () => void) => {\r\n      if (isMounted) {\r\n        updater();\r\n      }\r\n    };\r\n\r\n    safeSetState(() => {\r\n      setIsUploading(true);\r\n      setUploadStatus('uploading');\r\n      setUploadProgress(10);\r\n      setSuccessMessage('');\r\n      setErrorMessage('');\r\n      setExtractedChunks([]);\r\n    });\r\n\r\n\r\n\r\n    try {\r\n      // Simulate upload progress updates (in a real app, use XHR or fetch with progress events)\r\n      progressInterval = setInterval(() => {\r\n        if (isMounted) {\r\n          setUploadProgress(prev => {\r\n            const newProgress = prev + 5;\r\n            if (newProgress >= 90) {\r\n              if (progressInterval) clearInterval(progressInterval);\r\n              return 90; // Hold at 90% until processing is complete\r\n            }\r\n            return newProgress;\r\n          });\r\n        }\r\n      }, 300);\r\n\r\n\r\n\r\n      // Upload the document\r\n      safeSetState(() => setUploadStatus('uploading'));\r\n\r\n      // Upload the document using the API function\r\n      const response = await uploadDocument(file, \"<EMAIL>\");\r\n\r\n      // Clear the progress interval if it exists\r\n      if (progressInterval) {\r\n        clearInterval(progressInterval);\r\n        progressInterval = null;\r\n      }\r\n\r\n      // Only update state if component is still mounted\r\n      if (!isMounted) return;\r\n\r\n      if (response.success) {\r\n        // Set progress to 100%\r\n        setUploadProgress(100);\r\n        setUploadStatus('success');\r\n\r\n        // Store extracted chunks if available\r\n        if (response.chunks && response.chunks.length > 0) {\r\n          setExtractedChunks(response.chunks);\r\n          console.log('Extracted chunks:', response.chunks);\r\n        }\r\n\r\n        // Create event data for custom event\r\n        const eventData = {\r\n          detail: {\r\n            documentName,\r\n            mainCategory,\r\n            category,\r\n            subCategory,\r\n            minorCategory,\r\n            file,\r\n            uploadedAt: new Date().toISOString(),\r\n            id: response.data?.id || `doc-${Date.now()}`,\r\n            status: 'Extracted', // Update to real status from backend\r\n            fileType: file.name.split('.').pop()?.toLowerCase(),\r\n            qualityScore: 90, // This would come from backend in real app\r\n            chunks: response.chunks || [],\r\n            extractedContent: response.chunks ? response.chunks.map((chunk: any) => chunk.text).join('\\n\\n') : '',\r\n            chunks_extracted: response.chunks_extracted || 0\r\n          }\r\n        };\r\n\r\n        // Dispatch event to notify the DocumentsPage\r\n        const event = new CustomEvent('documentUploaded', { detail: eventData.detail });\r\n        window.dispatchEvent(event);\r\n\r\n        // Set success message (ensure error is cleared)\r\n        setErrorMessage('');\r\n        setSuccessMessage(`Document \"${documentName}\" uploaded successfully!`);\r\n\r\n        // Auto-clear success message after 5 seconds\r\n        const clearSuccessMessage = () => {\r\n          if (isMounted) {\r\n            setSuccessMessage('');\r\n          }\r\n        };\r\n\r\n        setTimeout(clearSuccessMessage, 5000);\r\n\r\n        // Reset form after successful upload\r\n        const resetForm = () => {\r\n          if (isMounted) {\r\n            setDocumentName('');\r\n            setMainCategory('');\r\n            setCategory('');\r\n            setSubCategory('');\r\n            setMinorCategory('');\r\n            setFile(null);\r\n            setUploadStatus('idle');\r\n            setUploadProgress(0);\r\n            setExtractedChunks([]);\r\n            setIsUploading(false);\r\n            setTouched({});\r\n          }\r\n        };\r\n\r\n        setTimeout(resetForm, 3000);\r\n      } else {\r\n        // Handle upload error\r\n        setUploadStatus('error');\r\n        setErrorMessage(response.message || 'Failed to upload document');\r\n      }\r\n    } catch (error) {\r\n      console.error('Upload failed:', error);\r\n      if (isMounted) {\r\n        setUploadStatus('error');\r\n        setErrorMessage(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n      }\r\n    } finally {\r\n      if (isMounted) {\r\n        setIsUploading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md p-6 transition-colors duration-300\">\r\n      <h2 className=\"text-lg font-semibold mb-4 text-gray-900\">Upload Document</h2>\r\n\r\n      {/* Success message */}\r\n      {successMessage && (\r\n        <div className=\"mb-4\">\r\n          <Alert variant=\"success\">\r\n            <AlertTitle>Success</AlertTitle>\r\n            <AlertDescription>{successMessage}</AlertDescription>\r\n          </Alert>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error message */}\r\n      {errorMessage && (\r\n        <div className=\"mb-4\">\r\n          <Alert variant=\"destructive\">\r\n            <AlertTitle>Error</AlertTitle>\r\n            <AlertDescription>{errorMessage}</AlertDescription>\r\n          </Alert>\r\n        </div>\r\n      )}\r\n\r\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n        {/* Document Name */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Document Name <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            value={documentName}\r\n            onChange={(e) => setDocumentName(e.target.value)}\r\n            onBlur={() => markAsTouched('documentName')}\r\n            className={`w-full p-2 border ${errors.documentName ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}\r\n            required\r\n          />\r\n          {errors.documentName && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.documentName}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Main Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Main Category <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={mainCategory}\r\n              onChange={(e) => setMainCategory(e.target.value)}\r\n              onBlur={() => markAsTouched('mainCategory')}\r\n              className={`flex-1 p-2 border ${errors.mainCategory ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}\r\n              required\r\n            >\r\n              <option value=\"\">Select Main Category</option>\r\n              {mainCategories.map((cat) => (\r\n                <option key={cat.id} value={cat.name}>\r\n                  {cat.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Main');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n          {errors.mainCategory && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.mainCategory}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Category <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={category}\r\n              onChange={(e) => setCategory(e.target.value)}\r\n              onBlur={() => markAsTouched('category')}\r\n              className={`flex-1 p-2 border ${errors.category ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}\r\n              required\r\n              disabled={!mainCategory}\r\n            >\r\n              <option value=\"\">Select Category</option>\r\n              {categories\r\n                .filter(cat => !mainCategory || cat.parentId === mainCategories.find(m => m.name === mainCategory)?.id)\r\n                .map((cat) => (\r\n                  <option key={cat.id} value={cat.name}>\r\n                    {cat.name}\r\n                  </option>\r\n                ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Category');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n              disabled={!mainCategory}\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n          {errors.category && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.category}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Sub Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Sub Category\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={subCategory}\r\n              onChange={(e) => setSubCategory(e.target.value)}\r\n              className=\"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\r\n              disabled={!category}\r\n            >\r\n              <option value=\"\">Select Sub Category</option>\r\n              {subCategories\r\n                .filter(sub => !category || sub.parentId === categories.find(c => c.name === category)?.id)\r\n                .map((sub) => (\r\n                  <option key={sub.id} value={sub.name}>\r\n                    {sub.name}\r\n                  </option>\r\n                ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Sub');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n              disabled={!category}\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Minor Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Minor Category\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <input\r\n              type=\"text\"\r\n              value={minorCategory}\r\n              onChange={(e) => setMinorCategory(e.target.value)}\r\n              className=\"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\r\n              placeholder=\"Enter Minor Category\"\r\n            />\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Minor');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* File Upload */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Upload Document <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div className=\"flex items-center justify-center w-full\">\r\n            <label\r\n              className={`flex flex-col w-full h-32 border-2 ${errors.file ? 'border-red-500' : 'border-blue-300'} border-dashed hover:bg-gray-50 hover:border-blue-500 rounded-lg cursor-pointer`}\r\n            >\r\n              <div className=\"flex flex-col items-center justify-center pt-7\">\r\n                <svg\r\n                  className=\"w-8 h-8 text-gray-400 group-hover:text-gray-600\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth=\"2\"\r\n                    d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\r\n                  ></path>\r\n                </svg>\r\n                <p className=\"pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600\">\r\n                  {file ? file.name : 'Attach document (PDF, DOCX, XLSX, etc.)'}\r\n                </p>\r\n              </div>\r\n              <input\r\n                type=\"file\"\r\n                className=\"opacity-0\"\r\n                accept=\".pdf,.docx,.xlsx,.txt,.png,.jpg,.jpeg\"\r\n                onChange={handleFileChange}\r\n                onBlur={() => markAsTouched('file')}\r\n                required\r\n              />\r\n            </label>\r\n          </div>\r\n          {errors.file && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.file}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Submit Button */}\r\n        <div>\r\n          <button\r\n            type=\"submit\"\r\n            disabled={isUploading}\r\n            className={`w-full px-4 py-2 text-white font-medium rounded-md ${\r\n              isUploading ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'\r\n            } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n          >\r\n            {isUploading ? 'Uploading...' : 'Upload Document'}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Upload Progress */}\r\n        {uploadStatus !== 'idle' && (\r\n          <div className=\"mt-4\">\r\n            <div className=\"flex items-center justify-between mb-1\">\r\n              <span className=\"text-sm font-medium text-gray-700\">\r\n                {uploadStatus === 'uploading' ? 'Uploading...' :\r\n                 uploadStatus === 'processing' ? 'Processing...' :\r\n                 uploadStatus === 'success' ? 'Upload Complete' :\r\n                 'Upload Failed'}\r\n              </span>\r\n              <span className=\"text-sm font-medium text-gray-700\">{uploadProgress}%</span>\r\n            </div>\r\n            <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n              <div\r\n                className={`h-2.5 rounded-full ${\r\n                  uploadStatus === 'error' ? 'bg-red-600' :\r\n                  uploadStatus === 'success' ? 'bg-green-600' : 'bg-blue-600'\r\n                }`}\r\n                style={{ width: `${uploadProgress}%` }}\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Extracted Chunks Preview (shown after successful upload) */}\r\n        {uploadStatus === 'success' && extractedChunks.length > 0 && (\r\n          <div className=\"mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50\">\r\n            <h3 className=\"text-sm font-medium text-gray-700 mb-2\">Extracted Content Preview:</h3>\r\n            <div className=\"max-h-40 overflow-y-auto text-sm text-gray-600\">\r\n              {extractedChunks.slice(0, 3).map((chunk, index) => (\r\n                <div key={index} className=\"mb-2 p-2 bg-white rounded border border-gray-200\">\r\n                  <p className=\"text-xs text-gray-500 mb-1\">\r\n                    {chunk.source_type === 'document' ?\r\n                      `Page ${chunk.page || 'N/A'}` :\r\n                      `Source: ${chunk.source || 'Unknown'}`}\r\n                  </p>\r\n                  <p>{chunk.text?.substring(0, 150)}...</p>\r\n                </div>\r\n              ))}\r\n              {extractedChunks.length > 3 && (\r\n                <p className=\"text-xs text-gray-500 text-center mt-2\">\r\n                  + {extractedChunks.length - 3} more chunks not shown\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </form>\r\n\r\n      {/* New Category Modal */}\r\n      {showNewCategoryInput && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\r\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\r\n            <h3 className=\"text-lg font-medium mb-4\">Add New {categoryType} Category</h3>\r\n            <input\r\n              type=\"text\"\r\n              value={newCategoryInput}\r\n              onChange={(e) => setNewCategoryInput(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4\"\r\n              placeholder={`Enter ${categoryType} Category name`}\r\n              autoFocus\r\n            />\r\n            <div className=\"flex justify-end gap-2\">\r\n              <button\r\n                onClick={() => setShowNewCategoryInput(false)}\r\n                className=\"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={handleNewCategorySubmit}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\r\n              >\r\n                Add Category\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DocumentUploadForm;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,KAAK,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,aAAa;;AAKjE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAQA,MAAMC,kBAA4B,GAAGA,CAAA,KAA0B;EAC7D;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAc,IAAI,CAAC;;EAEnD;EACA,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAwC,MAAM,CAAC;;EAE/F;EACA,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAA4D,MAAM,CAAC;EACnH,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAQ,EAAE,CAAC;;EAEjE;EACA,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAa,CAAC,CAAC,CAAC;EACpD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAA0B,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAqB,CACvE;IAAE6C,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC7C;IAAEF,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC5C;IAAEF,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAO,CAAC,EACjD;IAAEF,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAO,CAAC,CAC1C,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAqB,CAC/D;IAAE6C,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEG,QAAQ,EAAE;EAAI,CAAC,EAC7D;IAAEL,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,UAAU;IAAEG,QAAQ,EAAE;EAAI,CAAC,EAC/D;IAAEL,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE,UAAU;IAAEG,QAAQ,EAAE;EAAI,CAAC,EAChE;IAAEL,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEG,QAAQ,EAAE;EAAI,CAAC,CAC9D,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAqB,CACrE;IAAE6C,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE,KAAK;IAAEG,QAAQ,EAAE;EAAI,CAAC,EAC5D;IAAEL,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,eAAe;IAAEC,IAAI,EAAE,KAAK;IAAEG,QAAQ,EAAE;EAAI,CAAC,EAC9D;IAAEL,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,KAAK;IAAEG,QAAQ,EAAE;EAAI,CAAC,EAC1D;IAAEL,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,KAAK;IAAEG,QAAQ,EAAE;EAAI,CAAC,CACzD,CAAC;;EAEF;EACAjD,SAAS,CAAC,MAAM;IACd,IAAIoD,SAAS,GAAG,IAAI;IAEpB,MAAMC,SAAqB,GAAG,CAAC,CAAC;IAEhC,IAAIb,OAAO,CAAChC,YAAY,IAAI,CAACA,YAAY,CAAC8C,IAAI,CAAC,CAAC,EAAE;MAChDD,SAAS,CAAC7C,YAAY,GAAG,2BAA2B;IACtD;IAEA,IAAIgC,OAAO,CAAC9B,YAAY,IAAI,CAACA,YAAY,EAAE;MACzC2C,SAAS,CAAC3C,YAAY,GAAG,2BAA2B;IACtD;IAEA,IAAI8B,OAAO,CAAC5B,QAAQ,IAAI,CAACA,QAAQ,EAAE;MACjCyC,SAAS,CAACzC,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI4B,OAAO,CAACtB,IAAI,IAAI,CAACA,IAAI,EAAE;MACzBmC,SAAS,CAACnC,IAAI,GAAG,2BAA2B;IAC9C,CAAC,MAAM,IAAIA,IAAI,EAAE;MACf;MACA,MAAMqC,YAAY,GAAG,CACnB,iBAAiB,EACjB,yEAAyE,EACzE,mEAAmE,EACnE,YAAY,EACZ,WAAW,EACX,YAAY,CACb;MAED,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACtC,IAAI,CAAC4B,IAAI,CAAC,EAAE;QACrCO,SAAS,CAACnC,IAAI,GAAG,0EAA0E;MAC7F;;MAEA;MACA,MAAMuC,OAAO,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;MACnC,IAAIvC,IAAI,CAACwC,IAAI,GAAGD,OAAO,EAAE;QACvBJ,SAAS,CAACnC,IAAI,GAAG,uCAAuC;MAC1D;IACF;;IAEA;IACA,IAAIkC,SAAS,EAAE;MACbjB,SAAS,CAACkB,SAAS,CAAC;IACtB;;IAEA;IACA,OAAO,MAAM;MACXD,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,CAAC5C,YAAY,EAAEE,YAAY,EAAEE,QAAQ,EAAEM,IAAI,EAAEsB,OAAO,CAAC,CAAC;;EAEzD;EACA,MAAMmB,aAAa,GAAIC,KAAa,IAAK;IACvCnB,UAAU,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,KAAK,GAAG;IAAK,CAAC,CAAC,CAAC;EAClD,CAAC;;EAID;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIoC,cAAc,EAAE;MAClB,MAAM0B,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B1B,iBAAiB,CAAC,EAAE,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAM2B,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAC1B,cAAc,CAAC,CAAC;;EAEpB;EACApC,SAAS,CAAC,MAAM;IACd,IAAIsC,YAAY,EAAE;MAChB,MAAMwB,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BxB,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMyB,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACxB,YAAY,CAAC,CAAC;EAElB,MAAM2B,gBAAgB,GAAIC,CAAsC,IAAK;IACnE,IAAIA,CAAC,CAACC,MAAM,CAACC,KAAK,IAAIF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;MACvC,MAAMC,YAAY,GAAGH,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MACtCjD,OAAO,CAACkD,YAAY,CAAC;MACrBV,aAAa,CAAC,MAAM,CAAC;;MAErB;MACA,IAAI,CAACnD,YAAY,CAAC8C,IAAI,CAAC,CAAC,EAAE;QACxB;QACA,MAAMgB,YAAY,GAAGD,YAAY,CAACxB,IAAI,CAAC0B,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;QACrF9D,eAAe,CAAC6D,YAAY,CAAC;QAC7BX,aAAa,CAAC,cAAc,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMa,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,CAAClD,gBAAgB,CAACgC,IAAI,CAAC,CAAC,EAAE;IAE9BjC,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAM,IAAIoD,OAAO,CAACC,OAAO,IAAIX,UAAU,CAACW,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA,IAAIhD,YAAY,KAAK,MAAM,EAAE;QAC3B,MAAMiD,WAA6B,GAAG;UAAE/B,EAAE,EAAE,OAAOgC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAAEhC,IAAI,EAAEvB,gBAAgB;UAAEwB,IAAI,EAAE;QAAO,CAAC;QACvGH,iBAAiB,CAACkB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEc,WAAW,CAAC,CAAC;QACjDhE,eAAe,CAACW,gBAAgB,CAAC;MACnC,CAAC,MAAM,IAAII,YAAY,KAAK,UAAU,EAAE;QACtC,MAAMiD,WAA6B,GAAG;UAAE/B,EAAE,EAAE,OAAOgC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAAEhC,IAAI,EAAEvB,gBAAgB;UAAEwB,IAAI,EAAE,UAAU;UAAEG,QAAQ,EAAEvC;QAAa,CAAC;QACnIsC,aAAa,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEc,WAAW,CAAC,CAAC;QAC7C9D,WAAW,CAACS,gBAAgB,CAAC;MAC/B,CAAC,MAAM,IAAII,YAAY,KAAK,KAAK,EAAE;QACjC,MAAMiD,WAA6B,GAAG;UAAE/B,EAAE,EAAE,OAAOgC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAAEhC,IAAI,EAAEvB,gBAAgB;UAAEwB,IAAI,EAAE,KAAK;UAAEG,QAAQ,EAAErC;QAAS,CAAC;QAC1HuC,gBAAgB,CAACU,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEc,WAAW,CAAC,CAAC;QAChD5D,cAAc,CAACO,gBAAgB,CAAC;MAClC,CAAC,MAAM;QACL;QACAL,gBAAgB,CAACK,gBAAgB,CAAC;MACpC;MAEAe,iBAAiB,CAAC,eAAeX,YAAY,cAAcJ,gBAAgB,EAAE,CAAC;IAChF,CAAC,CAAC,OAAOwD,KAAK,EAAE;MACdvC,eAAe,CAAC,8BAA8BuC,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG,eAAe,EAAE,CAAC;IAC3G,CAAC,SAAS;MACR3D,cAAc,CAAC,KAAK,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBE,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAMwD,YAAY,GAAG,MAAOf,CAAkB,IAAoB;IAChEA,CAAC,CAACgB,cAAc,CAAC,CAAC;;IAElB;IACAvB,aAAa,CAAC,cAAc,CAAC;IAC7BA,aAAa,CAAC,cAAc,CAAC;IAC7BA,aAAa,CAAC,UAAU,CAAC;IACzBA,aAAa,CAAC,MAAM,CAAC;;IAErB;IACA,IAAIwB,MAAM,CAACC,IAAI,CAAClD,MAAM,CAAC,CAACmD,MAAM,GAAG,CAAC,IAAI,CAACnE,IAAI,EAAE;MAC3CqB,eAAe,CAAC,6CAA6C,CAAC;MAC9D;IACF;;IAEA;IACA,IAAIa,SAAS,GAAG,IAAI;IACpB,IAAIkC,gBAAuC,GAAG,IAAI;;IAElD;IACA,MAAMC,YAAY,GAAIC,OAAmB,IAAK;MAC5C,IAAIpC,SAAS,EAAE;QACboC,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAEDD,YAAY,CAAC,MAAM;MACjBlE,cAAc,CAAC,IAAI,CAAC;MACpBQ,eAAe,CAAC,WAAW,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBM,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;MACnBN,kBAAkB,CAAC,EAAE,CAAC;IACxB,CAAC,CAAC;IAIF,IAAI;MACF;MACAqD,gBAAgB,GAAGG,WAAW,CAAC,MAAM;QACnC,IAAIrC,SAAS,EAAE;UACbrB,iBAAiB,CAAC8B,IAAI,IAAI;YACxB,MAAM6B,WAAW,GAAG7B,IAAI,GAAG,CAAC;YAC5B,IAAI6B,WAAW,IAAI,EAAE,EAAE;cACrB,IAAIJ,gBAAgB,EAAEK,aAAa,CAACL,gBAAgB,CAAC;cACrD,OAAO,EAAE,CAAC,CAAC;YACb;YACA,OAAOI,WAAW;UACpB,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,GAAG,CAAC;;MAIP;MACAH,YAAY,CAAC,MAAM1D,eAAe,CAAC,WAAW,CAAC,CAAC;;MAEhD;MACA,MAAM+D,QAAQ,GAAG,MAAM3F,cAAc,CAACiB,IAAI,EAAE,mBAAmB,CAAC;;MAEhE;MACA,IAAIoE,gBAAgB,EAAE;QACpBK,aAAa,CAACL,gBAAgB,CAAC;QAC/BA,gBAAgB,GAAG,IAAI;MACzB;;MAEA;MACA,IAAI,CAAClC,SAAS,EAAE;MAEhB,IAAIwC,QAAQ,CAACC,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,oBAAA;QACpB;QACAhE,iBAAiB,CAAC,GAAG,CAAC;QACtBF,eAAe,CAAC,SAAS,CAAC;;QAE1B;QACA,IAAI+D,QAAQ,CAACI,MAAM,IAAIJ,QAAQ,CAACI,MAAM,CAACX,MAAM,GAAG,CAAC,EAAE;UACjDpD,kBAAkB,CAAC2D,QAAQ,CAACI,MAAM,CAAC;UACnCC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEN,QAAQ,CAACI,MAAM,CAAC;QACnD;;QAEA;QACA,MAAMG,SAAS,GAAG;UAChBC,MAAM,EAAE;YACN5F,YAAY;YACZE,YAAY;YACZE,QAAQ;YACRE,WAAW;YACXE,aAAa;YACbE,IAAI;YACJmF,UAAU,EAAE,IAAIzB,IAAI,CAAC,CAAC,CAAC0B,WAAW,CAAC,CAAC;YACpC1D,EAAE,EAAE,EAAAkD,cAAA,GAAAF,QAAQ,CAACW,IAAI,cAAAT,cAAA,uBAAbA,cAAA,CAAelD,EAAE,KAAI,OAAOgC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;YAC5C2B,MAAM,EAAE,WAAW;YAAE;YACrBC,QAAQ,GAAAV,oBAAA,GAAE7E,IAAI,CAAC2B,IAAI,CAAC6D,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAZ,oBAAA,uBAA1BA,oBAAA,CAA4Ba,WAAW,CAAC,CAAC;YACnDC,YAAY,EAAE,EAAE;YAAE;YAClBb,MAAM,EAAEJ,QAAQ,CAACI,MAAM,IAAI,EAAE;YAC7Bc,gBAAgB,EAAElB,QAAQ,CAACI,MAAM,GAAGJ,QAAQ,CAACI,MAAM,CAACe,GAAG,CAAEC,KAAU,IAAKA,KAAK,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACrGC,gBAAgB,EAAEvB,QAAQ,CAACuB,gBAAgB,IAAI;UACjD;QACF,CAAC;;QAED;QACA,MAAMC,KAAK,GAAG,IAAIC,WAAW,CAAC,kBAAkB,EAAE;UAAEjB,MAAM,EAAED,SAAS,CAACC;QAAO,CAAC,CAAC;QAC/EkB,MAAM,CAACC,aAAa,CAACH,KAAK,CAAC;;QAE3B;QACA7E,eAAe,CAAC,EAAE,CAAC;QACnBF,iBAAiB,CAAC,aAAa7B,YAAY,0BAA0B,CAAC;;QAEtE;QACA,MAAMgH,mBAAmB,GAAGA,CAAA,KAAM;UAChC,IAAIpE,SAAS,EAAE;YACbf,iBAAiB,CAAC,EAAE,CAAC;UACvB;QACF,CAAC;QAED0B,UAAU,CAACyD,mBAAmB,EAAE,IAAI,CAAC;;QAErC;QACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;UACtB,IAAIrE,SAAS,EAAE;YACb3C,eAAe,CAAC,EAAE,CAAC;YACnBE,eAAe,CAAC,EAAE,CAAC;YACnBE,WAAW,CAAC,EAAE,CAAC;YACfE,cAAc,CAAC,EAAE,CAAC;YAClBE,gBAAgB,CAAC,EAAE,CAAC;YACpBE,OAAO,CAAC,IAAI,CAAC;YACbU,eAAe,CAAC,MAAM,CAAC;YACvBE,iBAAiB,CAAC,CAAC,CAAC;YACpBE,kBAAkB,CAAC,EAAE,CAAC;YACtBZ,cAAc,CAAC,KAAK,CAAC;YACrBoB,UAAU,CAAC,CAAC,CAAC,CAAC;UAChB;QACF,CAAC;QAEDsB,UAAU,CAAC0D,SAAS,EAAE,IAAI,CAAC;MAC7B,CAAC,MAAM;QACL;QACA5F,eAAe,CAAC,OAAO,CAAC;QACxBU,eAAe,CAACqD,QAAQ,CAACZ,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdmB,OAAO,CAACnB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI1B,SAAS,EAAE;QACbvB,eAAe,CAAC,OAAO,CAAC;QACxBU,eAAe,CAAC,kBAAkBuC,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG,eAAe,EAAE,CAAC;MAC/F;IACF,CAAC,SAAS;MACR,IAAI5B,SAAS,EAAE;QACb/B,cAAc,CAAC,KAAK,CAAC;MACvB;IACF;EACF,CAAC;EAED,oBACEf,OAAA;IAAKoH,SAAS,EAAC,kEAAkE;IAAAC,QAAA,gBAC/ErH,OAAA;MAAIoH,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAG5E3F,cAAc,iBACb9B,OAAA;MAAKoH,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBrH,OAAA,CAACJ,KAAK;QAAC8H,OAAO,EAAC,SAAS;QAAAL,QAAA,gBACtBrH,OAAA,CAACF,UAAU;UAAAuH,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChCzH,OAAA,CAACH,gBAAgB;UAAAwH,QAAA,EAAEvF;QAAc;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EAGAzF,YAAY,iBACXhC,OAAA;MAAKoH,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBrH,OAAA,CAACJ,KAAK;QAAC8H,OAAO,EAAC,aAAa;QAAAL,QAAA,gBAC1BrH,OAAA,CAACF,UAAU;UAAAuH,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC9BzH,OAAA,CAACH,gBAAgB;UAAAwH,QAAA,EAAErF;QAAY;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAEDzH,OAAA;MAAM2H,QAAQ,EAAEhD,YAAa;MAACyC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAEjDrH,OAAA;QAAAqH,QAAA,gBACErH,OAAA;UAAOoH,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,gBAChD,eAAArH,OAAA;YAAMoH,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACRzH,OAAA;UACEwC,IAAI,EAAC,MAAM;UACXoF,KAAK,EAAE1H,YAAa;UACpB2H,QAAQ,EAAGjE,CAAC,IAAKzD,eAAe,CAACyD,CAAC,CAACC,MAAM,CAAC+D,KAAK,CAAE;UACjDE,MAAM,EAAEA,CAAA,KAAMzE,aAAa,CAAC,cAAc,CAAE;UAC5C+D,SAAS,EAAE,qBAAqBxF,MAAM,CAAC1B,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,uHAAwH;UAClN6H,QAAQ;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACD7F,MAAM,CAAC1B,YAAY,iBAClBF,OAAA;UAAGoH,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEzF,MAAM,CAAC1B;QAAY;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzH,OAAA;QAAAqH,QAAA,gBACErH,OAAA;UAAOoH,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,gBAChD,eAAArH,OAAA;YAAMoH,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACRzH,OAAA;UAAKoH,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrH,OAAA;YACE4H,KAAK,EAAExH,YAAa;YACpByH,QAAQ,EAAGjE,CAAC,IAAKvD,eAAe,CAACuD,CAAC,CAACC,MAAM,CAAC+D,KAAK,CAAE;YACjDE,MAAM,EAAEA,CAAA,KAAMzE,aAAa,CAAC,cAAc,CAAE;YAC5C+D,SAAS,EAAE,qBAAqBxF,MAAM,CAACxB,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,uHAAwH;YAClN2H,QAAQ;YAAAV,QAAA,gBAERrH,OAAA;cAAQ4H,KAAK,EAAC,EAAE;cAAAP,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC7CrF,cAAc,CAACqE,GAAG,CAAEuB,GAAG,iBACtBhI,OAAA;cAAqB4H,KAAK,EAAEI,GAAG,CAACzF,IAAK;cAAA8E,QAAA,EAClCW,GAAG,CAACzF;YAAI,GADEyF,GAAG,CAAC1F,EAAE;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTzH,OAAA;YACEwC,IAAI,EAAC,QAAQ;YACbyF,OAAO,EAAEA,CAAA,KAAM;cACb5G,eAAe,CAAC,MAAM,CAAC;cACvBF,uBAAuB,CAAC,IAAI,CAAC;YAC/B,CAAE;YACFiG,SAAS,EAAC,2KAA2K;YAAAC,QAAA,EACtL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL7F,MAAM,CAACxB,YAAY,iBAClBJ,OAAA;UAAGoH,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEzF,MAAM,CAACxB;QAAY;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzH,OAAA;QAAAqH,QAAA,gBACErH,OAAA;UAAOoH,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,WACrD,eAAArH,OAAA;YAAMoH,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACRzH,OAAA;UAAKoH,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrH,OAAA;YACE4H,KAAK,EAAEtH,QAAS;YAChBuH,QAAQ,EAAGjE,CAAC,IAAKrD,WAAW,CAACqD,CAAC,CAACC,MAAM,CAAC+D,KAAK,CAAE;YAC7CE,MAAM,EAAEA,CAAA,KAAMzE,aAAa,CAAC,UAAU,CAAE;YACxC+D,SAAS,EAAE,qBAAqBxF,MAAM,CAACtB,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,uHAAwH;YAC9MyH,QAAQ;YACRG,QAAQ,EAAE,CAAC9H,YAAa;YAAAiH,QAAA,gBAExBrH,OAAA;cAAQ4H,KAAK,EAAC,EAAE;cAAAP,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACxChF,UAAU,CACR0F,MAAM,CAACH,GAAG;cAAA,IAAAI,oBAAA;cAAA,OAAI,CAAChI,YAAY,IAAI4H,GAAG,CAACrF,QAAQ,OAAAyF,oBAAA,GAAKhG,cAAc,CAACiG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/F,IAAI,KAAKnC,YAAY,CAAC,cAAAgI,oBAAA,uBAAjDA,oBAAA,CAAmD9F,EAAE;YAAA,EAAC,CACtGmE,GAAG,CAAEuB,GAAG,iBACPhI,OAAA;cAAqB4H,KAAK,EAAEI,GAAG,CAACzF,IAAK;cAAA8E,QAAA,EAClCW,GAAG,CAACzF;YAAI,GADEyF,GAAG,CAAC1F,EAAE;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACTzH,OAAA;YACEwC,IAAI,EAAC,QAAQ;YACbyF,OAAO,EAAEA,CAAA,KAAM;cACb5G,eAAe,CAAC,UAAU,CAAC;cAC3BF,uBAAuB,CAAC,IAAI,CAAC;YAC/B,CAAE;YACFiG,SAAS,EAAC,2KAA2K;YACrLc,QAAQ,EAAE,CAAC9H,YAAa;YAAAiH,QAAA,EACzB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL7F,MAAM,CAACtB,QAAQ,iBACdN,OAAA;UAAGoH,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEzF,MAAM,CAACtB;QAAQ;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC9D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzH,OAAA;QAAAqH,QAAA,gBACErH,OAAA;UAAOoH,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzH,OAAA;UAAKoH,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrH,OAAA;YACE4H,KAAK,EAAEpH,WAAY;YACnBqH,QAAQ,EAAGjE,CAAC,IAAKnD,cAAc,CAACmD,CAAC,CAACC,MAAM,CAAC+D,KAAK,CAAE;YAChDR,SAAS,EAAC,wJAAwJ;YAClKc,QAAQ,EAAE,CAAC5H,QAAS;YAAA+G,QAAA,gBAEpBrH,OAAA;cAAQ4H,KAAK,EAAC,EAAE;cAAAP,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC5C7E,aAAa,CACXuF,MAAM,CAACI,GAAG;cAAA,IAAAC,gBAAA;cAAA,OAAI,CAAClI,QAAQ,IAAIiI,GAAG,CAAC5F,QAAQ,OAAA6F,gBAAA,GAAK/F,UAAU,CAAC4F,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAClG,IAAI,KAAKjC,QAAQ,CAAC,cAAAkI,gBAAA,uBAAzCA,gBAAA,CAA2ClG,EAAE;YAAA,EAAC,CAC1FmE,GAAG,CAAE8B,GAAG,iBACPvI,OAAA;cAAqB4H,KAAK,EAAEW,GAAG,CAAChG,IAAK;cAAA8E,QAAA,EAClCkB,GAAG,CAAChG;YAAI,GADEgG,GAAG,CAACjG,EAAE;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACTzH,OAAA;YACEwC,IAAI,EAAC,QAAQ;YACbyF,OAAO,EAAEA,CAAA,KAAM;cACb5G,eAAe,CAAC,KAAK,CAAC;cACtBF,uBAAuB,CAAC,IAAI,CAAC;YAC/B,CAAE;YACFiG,SAAS,EAAC,2KAA2K;YACrLc,QAAQ,EAAE,CAAC5H,QAAS;YAAA+G,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzH,OAAA;QAAAqH,QAAA,gBACErH,OAAA;UAAOoH,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzH,OAAA;UAAKoH,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrH,OAAA;YACEwC,IAAI,EAAC,MAAM;YACXoF,KAAK,EAAElH,aAAc;YACrBmH,QAAQ,EAAGjE,CAAC,IAAKjD,gBAAgB,CAACiD,CAAC,CAACC,MAAM,CAAC+D,KAAK,CAAE;YAClDR,SAAS,EAAC,wJAAwJ;YAClKsB,WAAW,EAAC;UAAsB;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFzH,OAAA;YACEwC,IAAI,EAAC,QAAQ;YACbyF,OAAO,EAAEA,CAAA,KAAM;cACb5G,eAAe,CAAC,OAAO,CAAC;cACxBF,uBAAuB,CAAC,IAAI,CAAC;YAC/B,CAAE;YACFiG,SAAS,EAAC,2KAA2K;YAAAC,QAAA,EACtL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzH,OAAA;QAAAqH,QAAA,gBACErH,OAAA;UAAOoH,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,kBAC9C,eAAArH,OAAA;YAAMoH,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACRzH,OAAA;UAAKoH,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDrH,OAAA;YACEoH,SAAS,EAAE,sCAAsCxF,MAAM,CAAChB,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,iFAAkF;YAAAyG,QAAA,gBAErLrH,OAAA;cAAKoH,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC7DrH,OAAA;gBACEoH,SAAS,EAAC,iDAAiD;gBAC3DuB,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBACnBC,KAAK,EAAC,4BAA4B;gBAAAzB,QAAA,eAElCrH,OAAA;kBACE+I,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAC,GAAG;kBACfC,CAAC,EAAC;gBAAuF;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNzH,OAAA;gBAAGoH,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAC/EzG,IAAI,GAAGA,IAAI,CAAC2B,IAAI,GAAG;cAAyC;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzH,OAAA;cACEwC,IAAI,EAAC,MAAM;cACX4E,SAAS,EAAC,WAAW;cACrB+B,MAAM,EAAC,uCAAuC;cAC9CtB,QAAQ,EAAElE,gBAAiB;cAC3BmE,MAAM,EAAEA,CAAA,KAAMzE,aAAa,CAAC,MAAM,CAAE;cACpC0E,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACL7F,MAAM,CAAChB,IAAI,iBACVZ,OAAA;UAAGoH,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEzF,MAAM,CAAChB;QAAI;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC1D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzH,OAAA;QAAAqH,QAAA,eACErH,OAAA;UACEwC,IAAI,EAAC,QAAQ;UACb0F,QAAQ,EAAEpH,WAAY;UACtBsG,SAAS,EAAE,sDACTtG,WAAW,GAAG,aAAa,GAAG,+BAA+B,sDACR;UAAAuG,QAAA,EAEtDvG,WAAW,GAAG,cAAc,GAAG;QAAiB;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLnG,YAAY,KAAK,MAAM,iBACtBtB,OAAA;QAAKoH,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrH,OAAA;UAAKoH,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDrH,OAAA;YAAMoH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAChD/F,YAAY,KAAK,WAAW,GAAG,cAAc,GAC7CA,YAAY,KAAK,YAAY,GAAG,eAAe,GAC/CA,YAAY,KAAK,SAAS,GAAG,iBAAiB,GAC9C;UAAe;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACPzH,OAAA;YAAMoH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,GAAE7F,cAAc,EAAC,GAAC;UAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACNzH,OAAA;UAAKoH,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDrH,OAAA;YACEoH,SAAS,EAAE,sBACT9F,YAAY,KAAK,OAAO,GAAG,YAAY,GACvCA,YAAY,KAAK,SAAS,GAAG,cAAc,GAAG,aAAa,EAC1D;YACH8H,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAG7H,cAAc;YAAI;UAAE;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAnG,YAAY,KAAK,SAAS,IAAII,eAAe,CAACqD,MAAM,GAAG,CAAC,iBACvD/E,OAAA;QAAKoH,SAAS,EAAC,uDAAuD;QAAAC,QAAA,gBACpErH,OAAA;UAAIoH,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFzH,OAAA;UAAKoH,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAC5D3F,eAAe,CAAC4H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC7C,GAAG,CAAC,CAACC,KAAK,EAAE6C,KAAK;YAAA,IAAAC,WAAA;YAAA,oBAC5CxJ,OAAA;cAAiBoH,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC3ErH,OAAA;gBAAGoH,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACtCX,KAAK,CAAC+C,WAAW,KAAK,UAAU,GAC/B,QAAQ/C,KAAK,CAACgD,IAAI,IAAI,KAAK,EAAE,GAC7B,WAAWhD,KAAK,CAACiD,MAAM,IAAI,SAAS;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACJzH,OAAA;gBAAAqH,QAAA,IAAAmC,WAAA,GAAI9C,KAAK,CAACC,IAAI,cAAA6C,WAAA,uBAAVA,WAAA,CAAYI,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,GANjC8B,KAAK;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CAAC;UAAA,CACP,CAAC,EACD/F,eAAe,CAACqD,MAAM,GAAG,CAAC,iBACzB/E,OAAA;YAAGoH,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,IAClD,EAAC3F,eAAe,CAACqD,MAAM,GAAG,CAAC,EAAC,wBAChC;UAAA;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGNvG,oBAAoB,iBACnBlB,OAAA;MAAKoH,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FrH,OAAA;QAAKoH,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDrH,OAAA;UAAIoH,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GAAC,UAAQ,EAACjG,YAAY,EAAC,WAAS;QAAA;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EzH,OAAA;UACEwC,IAAI,EAAC,MAAM;UACXoF,KAAK,EAAE5G,gBAAiB;UACxB6G,QAAQ,EAAGjE,CAAC,IAAK3C,mBAAmB,CAAC2C,CAAC,CAACC,MAAM,CAAC+D,KAAK,CAAE;UACrDR,SAAS,EAAC,uGAAuG;UACjHsB,WAAW,EAAE,SAAStH,YAAY,gBAAiB;UACnDyI,SAAS;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACFzH,OAAA;UAAKoH,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCrH,OAAA;YACEiI,OAAO,EAAEA,CAAA,KAAM9G,uBAAuB,CAAC,KAAK,CAAE;YAC9CiG,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAC1E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzH,OAAA;YACEiI,OAAO,EAAE/D,uBAAwB;YACjCkD,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAC1E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAED,eAAexH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}