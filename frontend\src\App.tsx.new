import React, { useState, useRef, useEffect } from 'react';
import './App.css';
import { sendQuery } from './services/api';

interface Source {
  source_type: string;
  filename?: string;
  page?: number;
  url?: string;
  link?: string; // For document viewer links
  name?: string; // For display name
}

interface Message {
  id: string;
  content: string;  // For user messages and fallback AI messages
  document_content?: string;  // Answer from document sources
  website_content?: string;  // Answer from website sources
  sender: 'user' | 'ai';
  loading?: boolean;
  sources?: Array<Source | string>;  // All sources
  document_sources?: Array<Source | string>;  // Document sources only
  website_sources?: Array<Source | string>;  // Website sources only
}

// Helper function to extract and format document names for display in header
const getDocumentNames = (sources?: Array<Source | string>) => {
  if (!sources || sources.length === 0) {
    return 'Uploaded Documents';
  }
  
  // Extract document names
  const names = sources.map(source => {
    if (typeof source === 'string') {
      return 'Document';
    }
    return source.name || source.filename || 'Document';
  });
  
  // Deduplicate names
  const uniqueNames = Array.from(new Set(names));
  
  // Format based on number of unique documents
  if (uniqueNames.length === 1) {
    return uniqueNames[0];
  } else if (uniqueNames.length === 2) {
    return `${uniqueNames[0]} and ${uniqueNames[1]}`;
  } else if (uniqueNames.length > 2) {
    return `${uniqueNames[0]} and ${uniqueNames.length - 1} other documents`;
  }
  
  return 'Uploaded Documents';
};

// Helper function to extract and format website domains for display in header
const getWebsiteDomains = (sources?: Array<Source | string>) => {
  if (!sources || sources.length === 0) {
    return 'Extracted Websites';
  }
  
  // Extract domain names from URLs
  const domains = sources.map(source => {
    if (typeof source === 'string') {
      return 'Website';
    }
    
    if (source.url) {
      try {
        // Extract domain from URL
        const url = new URL(source.url);
        return url.hostname.replace(/^www\./, '');
      } catch {
        return 'Website';
      }
    }
    
    return 'Website';
  });
  
  // Deduplicate domains
  const uniqueDomains = Array.from(new Set(domains));
  
  // Format based on number of unique domains
  if (uniqueDomains.length === 1) {
    return uniqueDomains[0];
  } else if (uniqueDomains.length === 2) {
    return `${uniqueDomains[0]} and ${uniqueDomains[1]}`;
  } else if (uniqueDomains.length > 2) {
    return `${uniqueDomains[0]} and ${uniqueDomains.length - 1} other websites`;
  }
  
  return 'Extracted Websites';
};

function App() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of chat whenever messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  
  // Function to test API connection - removes in production
  const testApiConnection = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: 'What is the Indian Railway?' }),
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('TEST: API connection successful!', data);
        alert('API connection successful! Check console for details.');
      } else {
        console.error('TEST: API connection failed!');
        alert('API connection failed! Check console for details.');
      }
    } catch (error) {
      console.error('TEST: API connection error:', error);
      alert(`API connection error: ${error}`);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim() || isSubmitting) return;
    
    // Generate a unique ID for this message
    const messageId = `msg-${Date.now()}`;
    
    // Add user message to chat
    const userMessage: Message = {
      id: messageId,
      content: input,
      sender: 'user',
    };
    
    // Create a loading placeholder for AI response
    const aiLoadingMessage: Message = {
      id: `ai-${messageId}`,
      content: '',
      sender: 'ai',
      loading: true
    };
    
    // Update messages state with user message and loading AI message
    setMessages(prevMessages => [...prevMessages, userMessage, aiLoadingMessage]);
    
    // Clear input
    setInput('');
    
    // Set loading state
    setIsSubmitting(true);
    
    try {
      // Send message to API
      const response = await sendQuery(input);
      
      // Process response
      const hasDocumentAnswer = response.document_answer && response.document_answer.trim() !== '';
      const hasWebsiteAnswer = response.website_answer && response.website_answer.trim() !== '';
      
      // Create AI response message with document and website answers
      const aiResponseMessage: Message = {
        id: `ai-${messageId}`,
        content: response.answer || 'I don\'t have an answer for that.',
        document_content: response.document_answer,
        website_content: response.website_answer,
        sender: 'ai',
        sources: response.sources,
        document_sources: response.document_sources,
        website_sources: response.website_sources,
      };
      
      // Replace loading message with actual response
      setMessages(prevMessages => 
        prevMessages.map(msg => 
          msg.id === `ai-${messageId}` ? aiResponseMessage : msg
        )
      );
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Update AI message with error
      setMessages(prevMessages => 
        prevMessages.map(msg => 
          msg.id === `ai-${messageId}` ? {
            ...msg,
            content: 'Sorry, there was an error processing your request. Please try again.',
            loading: false
          } : msg
        )
      );
    } finally {
      // Reset loading state
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gray-100">
      <div className="bg-blue-600 text-white p-4 shadow-md flex justify-between items-center">
        <h1 className="text-xl font-bold">RailGPT Chat</h1>
        <button 
          onClick={testApiConnection}
          className="px-4 py-1 bg-blue-700 hover:bg-blue-800 rounded text-sm"
          title="Test API Connection"
        >
          Test Backend Connection
        </button>
      </div>
      
      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <p className="text-lg mb-2">Welcome to RailGPT!</p>
              <p>Ask questions about Indian Railways...</p>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`mb-4 ${
                message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'
              }`}
            >
              <div
                className={`max-w-3/4 rounded-lg p-4 ${
                  message.sender === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-800 shadow-md'
                }`}
                style={{ maxWidth: '80%' }}
              >
                {message.loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-pulse mr-2">Generating response</div>
                    <div className="animate-bounce flex">
                      <div className="h-2 w-2 bg-blue-400 rounded-full mx-1"></div>
                      <div className="h-2 w-2 bg-blue-400 rounded-full mx-1 animate-delay-200"></div>
                      <div className="h-2 w-2 bg-blue-400 rounded-full mx-1 animate-delay-500"></div>
                    </div>
                  </div>
                ) : (
                  <div>
                    {message.sender === 'ai' ? (
                      <div>
                        {/* Show document answer if available */}
                        {message.document_content && (
                          <div className="mb-4">
                            <div className="flex items-center mb-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                              <span className="text-xs font-semibold text-green-600">From Documents</span>
                            </div>
                            <div className="whitespace-pre-wrap">{message.document_content}</div>
                            {message.document_sources && message.document_sources.length > 0 && (
                              <div className="mt-2 pt-2 border-t border-gray-100">
                                <p className="text-xs text-gray-500 font-semibold">Document Sources:</p>
                                <ul className="text-xs text-gray-500 list-disc pl-4">
                                  {message.document_sources.map((source, index) => {
                                    if (typeof source === 'string') {
                                      return <li key={index}>{source}</li>;
                                    } else if (source.source_type === 'document') {
                                      const displayName = source.name || source.filename || 'Document';
                                      const pageInfo = source.page ? ` (Page ${source.page})` : '';
                                      const viewerLink = source.link || 
                                        `http://localhost:3000/viewer?file=${encodeURIComponent(source.filename || '')}&page=${source.page || 1}`;
                                      
                                      return (
                                        <li key={index}>
                                          <button 
                                            onClick={() => window.open(viewerLink, "_blank", "noopener,noreferrer")}
                                            className="text-blue-600 hover:underline bg-transparent border-none cursor-pointer text-left p-0"
                                          >
                                            {displayName}{pageInfo}
                                          </button>
                                        </li>
                                      );
                                    }
                                    return <li key={index}>Unknown source</li>;
                                  })}
                                </ul>
                              </div>
                            )}
                          </div>
                        )}
                        
                        {/* Show website answer if available */}
                        {message.website_content && (
                          <div className="mb-4">
                            <div className="flex items-center mb-2">
                              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                              <span className="text-xs font-semibold text-blue-600">From Websites</span>
                            </div>
                            <div className="whitespace-pre-wrap">{message.website_content}</div>
                            {message.website_sources && message.website_sources.length > 0 && (
                              <div className="mt-2 pt-2 border-t border-gray-100">
                                <p className="text-xs text-gray-500 font-semibold">Website Sources:</p>
                                <ul className="text-xs text-gray-500 list-disc pl-4">
                                  {message.website_sources.map((source, index) => {
                                    if (typeof source === 'string') {
                                      return <li key={index}>{source}</li>;
                                    } else if (source.source_type === 'website') {
                                      return (
                                        <li key={index}>
                                          <a 
                                            href={source.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:underline"
                                          >
                                            {source.url}
                                          </a>
                                        </li>
                                      );
                                    }
                                    return <li key={index}>Unknown source</li>;
                                  })}
                                </ul>
                              </div>
                            )}
                          </div>
                        )}
                        
                        {/* If no document or website content, show general answer */}
                        {!message.document_content && !message.website_content && (
                          <div className="whitespace-pre-wrap">{message.content}</div>
                        )}
                        
                        {/* Show all sources if available and there's no specific document or website content */}
                        {!message.document_content && !message.website_content && message.sources && message.sources.length > 0 && (
                          <div className="mt-2 pt-2 border-t border-gray-100">
                            <p className="text-xs text-gray-500 font-semibold">Sources:</p>
                            <ul className="text-xs text-gray-500 list-disc pl-4">
                              {message.sources.map((source, index) => {
                                let sourceText = '';
                                if (typeof source === 'string') {
                                  // Handle legacy string format
                                  sourceText = source;
                                  return <li key={index}>{sourceText}</li>;
                                } else if (source.source_type === 'document') {
                                  // Document source
                                  const displayName = source.name || source.filename || 'Document';
                                  const pageInfo = source.page ? ` (Page ${source.page})` : '';
                                  const viewerLink = source.link || 
                                    `http://localhost:3000/viewer?file=${encodeURIComponent(source.filename || '')}&page=${source.page || 1}`;
                                  
                                  return (
                                    <li key={index}>
                                      <button 
                                        onClick={() => window.open(viewerLink, "_blank", "noopener,noreferrer")}
                                        className="text-blue-600 hover:underline bg-transparent border-none cursor-pointer text-left p-0"
                                      >
                                        {displayName}{pageInfo}
                                      </button>
                                    </li>
                                  );
                                } else if (source.source_type === 'website') {
                                  // Website source
                                  return (
                                    <li key={index}>
                                      <a 
                                        href={source.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:underline"
                                      >
                                        {source.url}
                                      </a>
                                    </li>
                                  );
                                }
                                return <li key={index}>Unknown source</li>;
                              })}
                            </ul>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="whitespace-pre-wrap">{message.content}</div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
      
      <div className="border-t border-gray-300 p-4 bg-white">
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Type your message..."
            className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isSubmitting}
          />
          <button
            type="submit"
            disabled={isSubmitting || !input.trim()}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            Send
          </button>
        </form>
      </div>
    </div>
  );
}

export default App;
