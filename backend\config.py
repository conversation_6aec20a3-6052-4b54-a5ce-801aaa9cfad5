"""
Secure configuration management for RailGPT.
"""
import os
import logging
from dotenv import load_dotenv
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
env_path = Path('.env')
load_dotenv(dotenv_path=env_path)

# Default configuration
DEFAULT_CONFIG = {
    # Supabase configuration
    "SUPABASE_URL": "",
    "SUPABASE_KEY": "",
    "SUPABASE_ANON_KEY": "",
    
    # API keys for LLM providers
    "OPENAI_API_KEY": "",
    "GEMINI_API_KEY": "",
    "GROQ_API_KEY": "",
    
    # Application settings
    "API_HOST": "0.0.0.0",
    "API_PORT": "8000",
    "ENVIRONMENT": "development",
    "LOG_LEVEL": "INFO",
    
    # Vector database settings
    "USE_SUPABASE": "true",
    "VECTOR_DIMENSION": "768",
    
    # Search settings
    "DOCUMENT_PRIORITY_WEIGHT": "1.5",
    "WEBSITE_PRIORITY_WEIGHT": "1.2",
    "RELEVANCE_THRESHOLD": "0.2",
    
    # Default user ID
    "DEFAULT_USER_ID": "a7fbeebf-9025-4a39-aefb-e128ccb6060f"
}

class Config:
    """Secure configuration management with environment variable fallbacks."""
    
    def __init__(self):
        """Initialize configuration from environment variables."""
        self._config = {}
        self._load_config()
        
    def _load_config(self):
        """Load configuration from environment variables with defaults."""
        for key, default_value in DEFAULT_CONFIG.items():
            value = os.getenv(key, default_value)
            self._config[key] = value
            
        # Validate required configuration
        missing_keys = []
        for key in ["SUPABASE_URL", "SUPABASE_KEY"]:
            if not self._config.get(key):
                missing_keys.append(key)
                
        if missing_keys:
            logger.warning(f"Missing required configuration: {', '.join(missing_keys)}")
            
    def get(self, key: str, default=None):
        """Get configuration value with optional default."""
        return self._config.get(key, default)
        
    def __getitem__(self, key: str):
        """Get configuration value using dictionary syntax."""
        return self._config.get(key)
        
    def __contains__(self, key: str):
        """Check if configuration contains key."""
        return key in self._config
        
# Create a global configuration instance
config = Config()
