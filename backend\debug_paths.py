#!/usr/bin/env python3
"""
Debug script to check server paths
"""

import os
import sys

def debug_paths():
    """Debug current working directory and file paths"""
    print("=== Debug Server Paths ===")
    
    # Current working directory
    cwd = os.getcwd()
    print(f"Current working directory: {cwd}")
    
    # Script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Script directory: {script_dir}")
    
    # Test files
    test_files = [
        "SampleRailwayDoc.pdf",
        "ACP 110V.docx", 
        "Local component Declaration.pdf"
    ]
    
    # Possible paths (same as in server.py)
    for filename in test_files:
        print(f"\nChecking {filename}:")
        
        possible_paths = [
            os.path.join(script_dir, "data", "uploads", filename),
            os.path.join(script_dir, "data", filename),
            f"./backend/data/uploads/{filename}",
            f"./backend/data/{filename}",
            f"./data/uploads/{filename}",
            f"./data/{filename}",
            f"./uploads/{filename}",
            f"./documents/{filename}",
            f"./{filename}"
        ]
        
        for i, path in enumerate(possible_paths):
            exists = os.path.exists(path)
            print(f"  Path {i+1}: {path} - {'EXISTS' if exists else 'NOT FOUND'}")
            if exists:
                print(f"    ✅ FOUND!")
                break

if __name__ == "__main__":
    debug_paths() 