{"ast": null, "code": "import React,{useState,useEffect}from'react';import WebsiteExtractForm from'../../components/websites/WebsiteExtractForm';import WebsitesTable from'../../components/websites/WebsitesTable';import WebsiteViewModal from'../../components/websites/WebsiteViewModal';import WebsiteCategoryManagement from'../../components/websites/WebsiteCategoryManagement';import{getWebsites}from'../../services/api';import{Settings}from'lucide-react';// Sample data for demonstration\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SAMPLE_WEBSITES=[{id:'1',url:'https://indianrailways.gov.in/railwayboard/',domain:'indianrailways.gov.in',extractedAt:'2025-05-01T14:30:00Z',status:'Success',submittedBy:'<EMAIL>',domainCategory:'Official Railways'},{id:'2',url:'https://irctc.co.in/nget/train-search',domain:'irctc.co.in',extractedAt:'2025-04-29T11:45:00Z',status:'Success',submittedBy:'<EMAIL>',domainCategory:'Official Railways'},{id:'3',url:'https://www.trainman.in/railway-stations',domain:'trainman.in',extractedAt:'2025-04-25T09:15:00Z',status:'Success',submittedBy:'<EMAIL>',domainCategory:'Travel Guides'},{id:'4',url:'https://rail-info.indianrailways.gov.in/mntes/',domain:'rail-info.indianrailways.gov.in',extractedAt:'2025-04-20T16:30:00Z',status:'Failed',submittedBy:'<EMAIL>',domainCategory:'Official Railways'},{id:'5',url:'https://www.ndtv.com/topic/indian-railways',domain:'ndtv.com',extractedAt:'2025-05-02T10:20:00Z',status:'Manual Required',submittedBy:'<EMAIL>',domainCategory:'News Portals'}];// Sample extraction details for demonstration\nconst SAMPLE_EXTRACTION_DETAILS={extractionMethod:'Trafilatura',fallbackHistory:['Trafilatura','BeautifulSoup'],contentQuality:85,warnings:['Dynamic content may be missing','Some tables could not be extracted completely'],extractedContent:`\n# Indian Railways\n\nThe Indian Railways is a statutory body under the jurisdiction of Ministry of Railways, Government of India that operates India's national railway system. It manages the fourth-largest railway network in the world by size, with a route length of 67,956 km (42,226 mi) and total track length of 99,235 km (61,662 mi) as of March 2020. As of the end of 2019, 71.23% of its 121,407 kilometres of track is electrified.\n\n## Passenger Services\n\nIndian Railways operates more than 20,000 passenger trains daily, on both long-distance and suburban routes, from approximately 7,349 stations across India.\n\n### Train Categories\n\nIndian Railways operates different categories of trains:\n\n1. **Shatabdi Express**: 160 km/h (99 mph) trains that connect major cities and state capitals over short distances, typically within a day's journey.\n2. **Rajdhani Express**: 130 to 140 km/h (81 to 87 mph) trains that link major state capitals to New Delhi.\n3. **Duronto Express**: 130 km/h (81 mph) point-to-point non-stop trains designed to connect major cities.\n4. **Vande Bharat Express**: 180 km/h (110 mph) semi-high-speed train, currently running on select routes.\n5. **Jan Shatabdi Express**: 110 to 120 km/h (68 to 75 mph) intercity services serving middle-distance connectivity.\n6. **Garib Rath Express**: 130 km/h (81 mph) trains that aim to provide affordable air-conditioned travel.\n7. **Mail/Express Trains**: Regular long-distance train services operating throughout the country.\n8. **Passenger & Fast Passenger Trains**: Short-distance services connecting smaller towns and villages.\n9. **Suburban Trains**: High-frequency train services in major metropolitan areas.\n\n### Ticket Booking\n\nTicket booking is primarily done through the Indian Railway Catering and Tourism Corporation (IRCTC) portal. Tickets can be booked up to 120 days in advance.\n\n## Freight Services\n\nIndian Railways is one of the world's largest freight carriers, moving over 1.23 billion tonnes of freight annually in FY 2019-20.\n\nMajor categories of freight traffic:\n- Coal (and petroleum products): 49%\n- Raw materials: 17%\n- Iron and steel: 12%\n- Cement: the 10%\n- Food grains: 7%\n- Fertilizers: 5%\n\n## Current Projects\n\n### High-Speed Rail Projects\n\n1. **Mumbai–Ahmedabad Corridor**: India's first high-speed rail line (bullet train) with operational speeds of 320 km/h. Currently under construction.\n2. **National High Speed Rail Corporation Limited (NHSRCL)**: Responsible for implementing high-speed rail projects in the country.\n\n### Infrastructure Development\n\n1. **Dedicated Freight Corridors (DFCs)**\n   - Eastern DFC: 1,856 km from Ludhiana to Dankuni\n   - Western DFC: 1,504 km from Dadri to Jawaharlal Nehru Port Trust\n\n2. **Station Redevelopment Program**\n   - Modernizing and upgrading major railway stations across the country\n   - Public-private partnership model for development\n  `,processingTime:2850,chunks:8};const WebsitesPage=()=>{const[websites,setWebsites]=useState(SAMPLE_WEBSITES);const[selectedWebsite,setSelectedWebsite]=useState(null);const[isViewModalOpen,setIsViewModalOpen]=useState(false);const[extractionDetails,setExtractionDetails]=useState(SAMPLE_EXTRACTION_DETAILS);const[isCategoryManagementOpen,setIsCategoryManagementOpen]=useState(false);// Fetch websites from the backend\nuseEffect(()=>{fetchWebsites();// Set up website extraction event listener\nconst handleWebsiteExtracted=event=>{const newWebsite=event.detail;setWebsites(prev=>[newWebsite,...prev]);// Refresh the websites list to get the latest from Supabase\nsetTimeout(()=>fetchWebsites(),2000);// Small delay to allow backend processing\n};// Add event listener\nwindow.addEventListener('websiteExtracted',handleWebsiteExtracted);// Cleanup\nreturn()=>{window.removeEventListener('websiteExtracted',handleWebsiteExtracted);};},[]);const fetchWebsites=async()=>{try{const data=await getWebsites();if(data&&data.length>0){// Use real data from Supabase, don't mix with sample data\nsetWebsites(data);console.log(`Loaded ${data.length} websites from Supabase`);}else{console.log('No websites found from API, using sample data');// Only use sample data if no real data is available\nsetWebsites(SAMPLE_WEBSITES);}}catch(error){console.error('Error fetching websites:',error);// Keep using sample data if API call fails\nsetWebsites(SAMPLE_WEBSITES);}};const handleViewWebsite=website=>{setSelectedWebsite(website);// Fetch actual extraction details from the backend\nasync function fetchExtractionDetails(){try{// Import our API functions\nconst{getWebsiteExtractionDetails,getWebsiteContent}=await import('../../services/api');// First try the specific extraction details endpoint\ntry{const data=await getWebsiteExtractionDetails(website.id);console.log('Website extraction details response:',data);setExtractionDetails(data);return;}catch(extractionError){console.warn('Failed to get website extraction details, trying content endpoint:',extractionError);}// If that fails, try the general website content endpoint\ntry{const contentData=await getWebsiteContent(website.id);console.log('Website content response:',contentData);// Create extraction details from content data\nconst details={extractedContent:contentData.content||contentData.text||'No content available',extractionMethod:contentData.extraction_method||'Direct Scraping',processingTime:contentData.processing_time||850,warnings:contentData.warnings||[],// Required fields from WebsiteExtractionDetails interface\nfallbackHistory:contentData.fallback_history||['API Response'],contentQuality:contentData.quality_score||80,chunks:contentData.chunks||5};setExtractionDetails(details);return;}catch(contentError){console.error('Failed to get website content:',contentError);throw contentError;// Re-throw to be caught by the outer try-catch\n}}catch(error){console.error('All API attempts failed, using fallback data:',error);// Fallback data in case of error\nconst errorFallbackDetails={extractedContent:`Unable to retrieve content for ${website.url} due to an error: ${error instanceof Error?error.message:'Unknown error'}. Please try again later.`,extractionMethod:'Error Fallback',processingTime:0,warnings:['Error retrieving content',error instanceof Error?error.message:'Unknown error'],fallbackHistory:['API Error','Error Handler'],contentQuality:30,chunks:0};setExtractionDetails(errorFallbackDetails);}}fetchExtractionDetails();setIsViewModalOpen(true);};const handleRetryWebsite=website=>{// In a real app, you would implement retry logic\nalert(`Retry extraction for: ${website.url}`);};const handleDeleteWebsite=website=>{if(window.confirm(`Are you sure you want to delete \"${website.url}\"?`)){// In a real app, you would make an API call to delete the website\n// async function deleteWebsite() {\n//   try {\n//     await fetch(`/api/websites/${website.id}`, { method: 'DELETE' });\n//     setWebsites(websites.filter(site => site.id !== website.id));\n//   } catch (error) {\n//     console.error('Error deleting website:', error);\n//   }\n// }\n// deleteWebsite();\n// For the demo, just filter it out\nsetWebsites(websites.filter(site=>site.id!==website.id));}};const handleRetryWithParser=async(website,parser)=>{// In a real app, you would make an API call to retry with the selected parser\nalert(`Retrying extraction of ${website.url} with ${parser}`);// Simulate processing time\nawait new Promise(resolve=>setTimeout(resolve,1500));return true;};const handleCategoryUpdate=updatedWebsite=>{// Update the website in the local state\nsetWebsites(prevWebsites=>prevWebsites.map(site=>site.id===updatedWebsite.id?updatedWebsite:site));};return/*#__PURE__*/_jsxs(\"div\",{className:\"h-full flex flex-col bg-gray-50 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white p-4 shadow-sm z-10 transition-colors duration-300\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"Website Management\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setIsCategoryManagementOpen(true),className:\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\",children:[/*#__PURE__*/_jsx(Settings,{className:\"h-4 w-4 mr-2\"}),\"Manage Categories\"]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 overflow-y-auto p-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-1\",children:/*#__PURE__*/_jsx(WebsiteExtractForm,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-2\",children:/*#__PURE__*/_jsx(WebsitesTable,{websites:websites,onView:handleViewWebsite,onRetry:handleRetryWebsite,onDelete:handleDeleteWebsite,onCategoryUpdate:handleCategoryUpdate})})]})})}),selectedWebsite&&/*#__PURE__*/_jsx(WebsiteViewModal,{website:selectedWebsite,extractionDetails:extractionDetails,isOpen:isViewModalOpen,onClose:()=>setIsViewModalOpen(false),onRetry:handleRetryWithParser}),/*#__PURE__*/_jsx(WebsiteCategoryManagement,{isOpen:isCategoryManagementOpen,onClose:()=>setIsCategoryManagementOpen(false)})]});};export default WebsitesPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "WebsiteExtractForm", "WebsitesTable", "WebsiteViewModal", "WebsiteCategoryManagement", "getWebsites", "Settings", "jsx", "_jsx", "jsxs", "_jsxs", "SAMPLE_WEBSITES", "id", "url", "domain", "extractedAt", "status", "submittedBy", "domainCategory", "SAMPLE_EXTRACTION_DETAILS", "extractionMethod", "fallbackHistory", "contentQuality", "warnings", "extractedContent", "processingTime", "chunks", "WebsitesPage", "websites", "setWebsites", "selectedWebsite", "setSelectedWebsite", "isViewModalOpen", "setIsViewModalOpen", "extractionDetails", "setExtractionDetails", "isCategoryManagementOpen", "setIsCategoryManagementOpen", "fetchWebsites", "handleWebsiteExtracted", "event", "newWebsite", "detail", "prev", "setTimeout", "window", "addEventListener", "removeEventListener", "data", "length", "console", "log", "error", "handleViewWebsite", "website", "fetchExtractionDetails", "getWebsiteExtractionDetails", "getWebsiteContent", "extractionError", "warn", "contentData", "details", "content", "text", "extraction_method", "processing_time", "fallback_history", "quality_score", "contentError", "errorFallbackDetails", "Error", "message", "handleRetryWebsite", "alert", "handleDeleteWebsite", "confirm", "filter", "site", "handleRetryWithParser", "parser", "Promise", "resolve", "handleCategoryUpdate", "updatedWebsite", "prevWebsites", "map", "className", "children", "onClick", "onView", "onRetry", "onDelete", "onCategoryUpdate", "isOpen", "onClose"], "sources": ["C:/IR App/frontend/src/pages/websites/WebsitesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Website, WebsiteExtractionDetails, ExtractionParser } from '../../types/websites';\nimport WebsiteExtractForm from '../../components/websites/WebsiteExtractForm';\nimport WebsitesTable from '../../components/websites/WebsitesTable';\nimport WebsiteViewModal from '../../components/websites/WebsiteViewModal';\nimport WebsiteCategoryManagement from '../../components/websites/WebsiteCategoryManagement';\nimport { getWebsites } from '../../services/api';\nimport { Settings } from 'lucide-react';\n\n// Sample data for demonstration\nconst SAMPLE_WEBSITES: Website[] = [\n  {\n    id: '1',\n    url: 'https://indianrailways.gov.in/railwayboard/',\n    domain: 'indianrailways.gov.in',\n    extractedAt: '2025-05-01T14:30:00Z',\n    status: 'Success',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'Official Railways',\n  },\n  {\n    id: '2',\n    url: 'https://irctc.co.in/nget/train-search',\n    domain: 'irctc.co.in',\n    extractedAt: '2025-04-29T11:45:00Z',\n    status: 'Success',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'Official Railways',\n  },\n  {\n    id: '3',\n    url: 'https://www.trainman.in/railway-stations',\n    domain: 'trainman.in',\n    extractedAt: '2025-04-25T09:15:00Z',\n    status: 'Success',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'Travel Guides',\n  },\n  {\n    id: '4',\n    url: 'https://rail-info.indianrailways.gov.in/mntes/',\n    domain: 'rail-info.indianrailways.gov.in',\n    extractedAt: '2025-04-20T16:30:00Z',\n    status: 'Failed',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'Official Railways',\n  },\n  {\n    id: '5',\n    url: 'https://www.ndtv.com/topic/indian-railways',\n    domain: 'ndtv.com',\n    extractedAt: '2025-05-02T10:20:00Z',\n    status: 'Manual Required',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'News Portals',\n  },\n];\n\n// Sample extraction details for demonstration\nconst SAMPLE_EXTRACTION_DETAILS: WebsiteExtractionDetails = {\n  extractionMethod: 'Trafilatura',\n  fallbackHistory: ['Trafilatura', 'BeautifulSoup'],\n  contentQuality: 85,\n  warnings: ['Dynamic content may be missing', 'Some tables could not be extracted completely'],\n  extractedContent: `\n# Indian Railways\n\nThe Indian Railways is a statutory body under the jurisdiction of Ministry of Railways, Government of India that operates India's national railway system. It manages the fourth-largest railway network in the world by size, with a route length of 67,956 km (42,226 mi) and total track length of 99,235 km (61,662 mi) as of March 2020. As of the end of 2019, 71.23% of its 121,407 kilometres of track is electrified.\n\n## Passenger Services\n\nIndian Railways operates more than 20,000 passenger trains daily, on both long-distance and suburban routes, from approximately 7,349 stations across India.\n\n### Train Categories\n\nIndian Railways operates different categories of trains:\n\n1. **Shatabdi Express**: 160 km/h (99 mph) trains that connect major cities and state capitals over short distances, typically within a day's journey.\n2. **Rajdhani Express**: 130 to 140 km/h (81 to 87 mph) trains that link major state capitals to New Delhi.\n3. **Duronto Express**: 130 km/h (81 mph) point-to-point non-stop trains designed to connect major cities.\n4. **Vande Bharat Express**: 180 km/h (110 mph) semi-high-speed train, currently running on select routes.\n5. **Jan Shatabdi Express**: 110 to 120 km/h (68 to 75 mph) intercity services serving middle-distance connectivity.\n6. **Garib Rath Express**: 130 km/h (81 mph) trains that aim to provide affordable air-conditioned travel.\n7. **Mail/Express Trains**: Regular long-distance train services operating throughout the country.\n8. **Passenger & Fast Passenger Trains**: Short-distance services connecting smaller towns and villages.\n9. **Suburban Trains**: High-frequency train services in major metropolitan areas.\n\n### Ticket Booking\n\nTicket booking is primarily done through the Indian Railway Catering and Tourism Corporation (IRCTC) portal. Tickets can be booked up to 120 days in advance.\n\n## Freight Services\n\nIndian Railways is one of the world's largest freight carriers, moving over 1.23 billion tonnes of freight annually in FY 2019-20.\n\nMajor categories of freight traffic:\n- Coal (and petroleum products): 49%\n- Raw materials: 17%\n- Iron and steel: 12%\n- Cement: the 10%\n- Food grains: 7%\n- Fertilizers: 5%\n\n## Current Projects\n\n### High-Speed Rail Projects\n\n1. **Mumbai–Ahmedabad Corridor**: India's first high-speed rail line (bullet train) with operational speeds of 320 km/h. Currently under construction.\n2. **National High Speed Rail Corporation Limited (NHSRCL)**: Responsible for implementing high-speed rail projects in the country.\n\n### Infrastructure Development\n\n1. **Dedicated Freight Corridors (DFCs)**\n   - Eastern DFC: 1,856 km from Ludhiana to Dankuni\n   - Western DFC: 1,504 km from Dadri to Jawaharlal Nehru Port Trust\n\n2. **Station Redevelopment Program**\n   - Modernizing and upgrading major railway stations across the country\n   - Public-private partnership model for development\n  `,\n  processingTime: 2850,\n  chunks: 8,\n};\n\nconst WebsitesPage: React.FC = () => {\n  const [websites, setWebsites] = useState<Website[]>(SAMPLE_WEBSITES);\n  const [selectedWebsite, setSelectedWebsite] = useState<Website | null>(null);\n  const [isViewModalOpen, setIsViewModalOpen] = useState(false);\n  const [extractionDetails, setExtractionDetails] = useState<WebsiteExtractionDetails>(SAMPLE_EXTRACTION_DETAILS);\n  const [isCategoryManagementOpen, setIsCategoryManagementOpen] = useState(false);\n\n  // Fetch websites from the backend\n  useEffect(() => {\n    fetchWebsites();\n\n    // Set up website extraction event listener\n    const handleWebsiteExtracted = (event: CustomEvent) => {\n      const newWebsite = event.detail;\n      setWebsites(prev => [newWebsite, ...prev]);\n      // Refresh the websites list to get the latest from Supabase\n      setTimeout(() => fetchWebsites(), 2000); // Small delay to allow backend processing\n    };\n\n    // Add event listener\n    window.addEventListener('websiteExtracted', handleWebsiteExtracted as EventListener);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('websiteExtracted', handleWebsiteExtracted as EventListener);\n    };\n  }, []);\n\n  const fetchWebsites = async () => {\n    try {\n      const data = await getWebsites();\n      if (data && data.length > 0) {\n        // Use real data from Supabase, don't mix with sample data\n        setWebsites(data);\n        console.log(`Loaded ${data.length} websites from Supabase`);\n      } else {\n        console.log('No websites found from API, using sample data');\n        // Only use sample data if no real data is available\n        setWebsites(SAMPLE_WEBSITES);\n      }\n    } catch (error) {\n      console.error('Error fetching websites:', error);\n      // Keep using sample data if API call fails\n      setWebsites(SAMPLE_WEBSITES);\n    }\n  };\n\n  const handleViewWebsite = (website: Website) => {\n    setSelectedWebsite(website);\n\n    // Fetch actual extraction details from the backend\n    async function fetchExtractionDetails() {\n      try {\n        // Import our API functions\n        const { getWebsiteExtractionDetails, getWebsiteContent } = await import('../../services/api');\n\n        // First try the specific extraction details endpoint\n        try {\n          const data = await getWebsiteExtractionDetails(website.id);\n          console.log('Website extraction details response:', data);\n          setExtractionDetails(data);\n          return;\n        } catch (extractionError) {\n          console.warn('Failed to get website extraction details, trying content endpoint:', extractionError);\n        }\n\n        // If that fails, try the general website content endpoint\n        try {\n          const contentData = await getWebsiteContent(website.id);\n          console.log('Website content response:', contentData);\n\n          // Create extraction details from content data\n          const details = {\n            extractedContent: contentData.content || contentData.text || 'No content available',\n            extractionMethod: contentData.extraction_method || 'Direct Scraping',\n            processingTime: contentData.processing_time || 850,\n            warnings: contentData.warnings || [],\n            // Required fields from WebsiteExtractionDetails interface\n            fallbackHistory: contentData.fallback_history || ['API Response'],\n            contentQuality: contentData.quality_score || 80,\n            chunks: contentData.chunks || 5\n          };\n\n          setExtractionDetails(details);\n          return;\n        } catch (contentError) {\n          console.error('Failed to get website content:', contentError);\n          throw contentError; // Re-throw to be caught by the outer try-catch\n        }\n      } catch (error) {\n        console.error('All API attempts failed, using fallback data:', error);\n\n        // Fallback data in case of error\n        const errorFallbackDetails: WebsiteExtractionDetails = {\n          extractedContent: `Unable to retrieve content for ${website.url} due to an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again later.`,\n          extractionMethod: 'Error Fallback',\n          processingTime: 0,\n          warnings: ['Error retrieving content', error instanceof Error ? error.message : 'Unknown error'],\n          fallbackHistory: ['API Error', 'Error Handler'],\n          contentQuality: 30,\n          chunks: 0\n        };\n\n        setExtractionDetails(errorFallbackDetails);\n      }\n    }\n\n    fetchExtractionDetails();\n    setIsViewModalOpen(true);\n  };\n\n  const handleRetryWebsite = (website: Website) => {\n    // In a real app, you would implement retry logic\n    alert(`Retry extraction for: ${website.url}`);\n  };\n\n  const handleDeleteWebsite = (website: Website) => {\n    if (window.confirm(`Are you sure you want to delete \"${website.url}\"?`)) {\n      // In a real app, you would make an API call to delete the website\n      // async function deleteWebsite() {\n      //   try {\n      //     await fetch(`/api/websites/${website.id}`, { method: 'DELETE' });\n      //     setWebsites(websites.filter(site => site.id !== website.id));\n      //   } catch (error) {\n      //     console.error('Error deleting website:', error);\n      //   }\n      // }\n      // deleteWebsite();\n\n      // For the demo, just filter it out\n      setWebsites(websites.filter(site => site.id !== website.id));\n    }\n  };\n\n  const handleRetryWithParser = async (website: Website, parser: ExtractionParser) => {\n    // In a real app, you would make an API call to retry with the selected parser\n    alert(`Retrying extraction of ${website.url} with ${parser}`);\n\n    // Simulate processing time\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    return true;\n  };\n\n  const handleCategoryUpdate = (updatedWebsite: Website) => {\n    // Update the website in the local state\n    setWebsites(prevWebsites =>\n      prevWebsites.map(site =>\n        site.id === updatedWebsite.id ? updatedWebsite : site\n      )\n    );\n  };\n\n  return (\n    <div className=\"h-full flex flex-col bg-gray-50 transition-colors duration-300\">\n      {/* Fixed header section */}\n      <div className=\"bg-white p-4 shadow-sm z-10 transition-colors duration-300\">\n        <div className=\"container mx-auto\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">Website Management</h1>\n            <button\n              onClick={() => setIsCategoryManagementOpen(true)}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\"\n            >\n              <Settings className=\"h-4 w-4 mr-2\" />\n              Manage Categories\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Scrollable content section */}\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <div className=\"container mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {/* Left side: Extract form */}\n            <div className=\"lg:col-span-1\">\n              <WebsiteExtractForm />\n            </div>\n\n            {/* Right side: Websites table */}\n            <div className=\"lg:col-span-2\">\n              <WebsitesTable\n                websites={websites}\n                onView={handleViewWebsite}\n                onRetry={handleRetryWebsite}\n                onDelete={handleDeleteWebsite}\n                onCategoryUpdate={handleCategoryUpdate}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Website View Modal */}\n      {selectedWebsite && (\n        <WebsiteViewModal\n          website={selectedWebsite}\n          extractionDetails={extractionDetails}\n          isOpen={isViewModalOpen}\n          onClose={() => setIsViewModalOpen(false)}\n          onRetry={handleRetryWithParser}\n        />\n      )}\n\n      {/* Website Category Management Modal */}\n      <WebsiteCategoryManagement\n        isOpen={isCategoryManagementOpen}\n        onClose={() => setIsCategoryManagementOpen(false)}\n      />\n    </div>\n  );\n};\n\nexport default WebsitesPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAElD,MAAO,CAAAC,kBAAkB,KAAM,8CAA8C,CAC7E,MAAO,CAAAC,aAAa,KAAM,yCAAyC,CACnE,MAAO,CAAAC,gBAAgB,KAAM,4CAA4C,CACzE,MAAO,CAAAC,yBAAyB,KAAM,qDAAqD,CAC3F,OAASC,WAAW,KAAQ,oBAAoB,CAChD,OAASC,QAAQ,KAAQ,cAAc,CAEvC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,eAA0B,CAAG,CACjC,CACEC,EAAE,CAAE,GAAG,CACPC,GAAG,CAAE,6CAA6C,CAClDC,MAAM,CAAE,uBAAuB,CAC/BC,WAAW,CAAE,sBAAsB,CACnCC,MAAM,CAAE,SAAS,CACjBC,WAAW,CAAE,mBAAmB,CAChCC,cAAc,CAAE,mBAClB,CAAC,CACD,CACEN,EAAE,CAAE,GAAG,CACPC,GAAG,CAAE,uCAAuC,CAC5CC,MAAM,CAAE,aAAa,CACrBC,WAAW,CAAE,sBAAsB,CACnCC,MAAM,CAAE,SAAS,CACjBC,WAAW,CAAE,mBAAmB,CAChCC,cAAc,CAAE,mBAClB,CAAC,CACD,CACEN,EAAE,CAAE,GAAG,CACPC,GAAG,CAAE,0CAA0C,CAC/CC,MAAM,CAAE,aAAa,CACrBC,WAAW,CAAE,sBAAsB,CACnCC,MAAM,CAAE,SAAS,CACjBC,WAAW,CAAE,wBAAwB,CACrCC,cAAc,CAAE,eAClB,CAAC,CACD,CACEN,EAAE,CAAE,GAAG,CACPC,GAAG,CAAE,gDAAgD,CACrDC,MAAM,CAAE,iCAAiC,CACzCC,WAAW,CAAE,sBAAsB,CACnCC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,wBAAwB,CACrCC,cAAc,CAAE,mBAClB,CAAC,CACD,CACEN,EAAE,CAAE,GAAG,CACPC,GAAG,CAAE,4CAA4C,CACjDC,MAAM,CAAE,UAAU,CAClBC,WAAW,CAAE,sBAAsB,CACnCC,MAAM,CAAE,iBAAiB,CACzBC,WAAW,CAAE,qBAAqB,CAClCC,cAAc,CAAE,cAClB,CAAC,CACF,CAED;AACA,KAAM,CAAAC,yBAAmD,CAAG,CAC1DC,gBAAgB,CAAE,aAAa,CAC/BC,eAAe,CAAE,CAAC,aAAa,CAAE,eAAe,CAAC,CACjDC,cAAc,CAAE,EAAE,CAClBC,QAAQ,CAAE,CAAC,gCAAgC,CAAE,+CAA+C,CAAC,CAC7FC,gBAAgB,CAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CACDC,cAAc,CAAE,IAAI,CACpBC,MAAM,CAAE,CACV,CAAC,CAED,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAYY,eAAe,CAAC,CACpE,KAAM,CAACmB,eAAe,CAAEC,kBAAkB,CAAC,CAAGhC,QAAQ,CAAiB,IAAI,CAAC,CAC5E,KAAM,CAACiC,eAAe,CAAEC,kBAAkB,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACmC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpC,QAAQ,CAA2BoB,yBAAyB,CAAC,CAC/G,KAAM,CAACiB,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CAE/E;AACAC,SAAS,CAAC,IAAM,CACdsC,aAAa,CAAC,CAAC,CAEf;AACA,KAAM,CAAAC,sBAAsB,CAAIC,KAAkB,EAAK,CACrD,KAAM,CAAAC,UAAU,CAAGD,KAAK,CAACE,MAAM,CAC/Bb,WAAW,CAACc,IAAI,EAAI,CAACF,UAAU,CAAE,GAAGE,IAAI,CAAC,CAAC,CAC1C;AACAC,UAAU,CAAC,IAAMN,aAAa,CAAC,CAAC,CAAE,IAAI,CAAC,CAAE;AAC3C,CAAC,CAED;AACAO,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,CAAEP,sBAAuC,CAAC,CAEpF;AACA,MAAO,IAAM,CACXM,MAAM,CAACE,mBAAmB,CAAC,kBAAkB,CAAER,sBAAuC,CAAC,CACzF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAU,IAAI,CAAG,KAAM,CAAA3C,WAAW,CAAC,CAAC,CAChC,GAAI2C,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAG,CAAC,CAAE,CAC3B;AACApB,WAAW,CAACmB,IAAI,CAAC,CACjBE,OAAO,CAACC,GAAG,CAAC,UAAUH,IAAI,CAACC,MAAM,yBAAyB,CAAC,CAC7D,CAAC,IAAM,CACLC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAC5D;AACAtB,WAAW,CAAClB,eAAe,CAAC,CAC9B,CACF,CAAE,MAAOyC,KAAK,CAAE,CACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD;AACAvB,WAAW,CAAClB,eAAe,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAA0C,iBAAiB,CAAIC,OAAgB,EAAK,CAC9CvB,kBAAkB,CAACuB,OAAO,CAAC,CAE3B;AACA,cAAe,CAAAC,sBAAsBA,CAAA,CAAG,CACtC,GAAI,CACF;AACA,KAAM,CAAEC,2BAA2B,CAAEC,iBAAkB,CAAC,CAAG,KAAM,OAAM,CAAC,oBAAoB,CAAC,CAE7F;AACA,GAAI,CACF,KAAM,CAAAT,IAAI,CAAG,KAAM,CAAAQ,2BAA2B,CAACF,OAAO,CAAC1C,EAAE,CAAC,CAC1DsC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEH,IAAI,CAAC,CACzDb,oBAAoB,CAACa,IAAI,CAAC,CAC1B,OACF,CAAE,MAAOU,eAAe,CAAE,CACxBR,OAAO,CAACS,IAAI,CAAC,oEAAoE,CAAED,eAAe,CAAC,CACrG,CAEA;AACA,GAAI,CACF,KAAM,CAAAE,WAAW,CAAG,KAAM,CAAAH,iBAAiB,CAACH,OAAO,CAAC1C,EAAE,CAAC,CACvDsC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAES,WAAW,CAAC,CAErD;AACA,KAAM,CAAAC,OAAO,CAAG,CACdrC,gBAAgB,CAAEoC,WAAW,CAACE,OAAO,EAAIF,WAAW,CAACG,IAAI,EAAI,sBAAsB,CACnF3C,gBAAgB,CAAEwC,WAAW,CAACI,iBAAiB,EAAI,iBAAiB,CACpEvC,cAAc,CAAEmC,WAAW,CAACK,eAAe,EAAI,GAAG,CAClD1C,QAAQ,CAAEqC,WAAW,CAACrC,QAAQ,EAAI,EAAE,CACpC;AACAF,eAAe,CAAEuC,WAAW,CAACM,gBAAgB,EAAI,CAAC,cAAc,CAAC,CACjE5C,cAAc,CAAEsC,WAAW,CAACO,aAAa,EAAI,EAAE,CAC/CzC,MAAM,CAAEkC,WAAW,CAAClC,MAAM,EAAI,CAChC,CAAC,CAEDS,oBAAoB,CAAC0B,OAAO,CAAC,CAC7B,OACF,CAAE,MAAOO,YAAY,CAAE,CACrBlB,OAAO,CAACE,KAAK,CAAC,gCAAgC,CAAEgB,YAAY,CAAC,CAC7D,KAAM,CAAAA,YAAY,CAAE;AACtB,CACF,CAAE,MAAOhB,KAAK,CAAE,CACdF,OAAO,CAACE,KAAK,CAAC,+CAA+C,CAAEA,KAAK,CAAC,CAErE;AACA,KAAM,CAAAiB,oBAA8C,CAAG,CACrD7C,gBAAgB,CAAE,kCAAkC8B,OAAO,CAACzC,GAAG,qBAAqBuC,KAAK,WAAY,CAAAkB,KAAK,CAAGlB,KAAK,CAACmB,OAAO,CAAG,eAAe,2BAA2B,CACvKnD,gBAAgB,CAAE,gBAAgB,CAClCK,cAAc,CAAE,CAAC,CACjBF,QAAQ,CAAE,CAAC,0BAA0B,CAAE6B,KAAK,WAAY,CAAAkB,KAAK,CAAGlB,KAAK,CAACmB,OAAO,CAAG,eAAe,CAAC,CAChGlD,eAAe,CAAE,CAAC,WAAW,CAAE,eAAe,CAAC,CAC/CC,cAAc,CAAE,EAAE,CAClBI,MAAM,CAAE,CACV,CAAC,CAEDS,oBAAoB,CAACkC,oBAAoB,CAAC,CAC5C,CACF,CAEAd,sBAAsB,CAAC,CAAC,CACxBtB,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAuC,kBAAkB,CAAIlB,OAAgB,EAAK,CAC/C;AACAmB,KAAK,CAAC,yBAAyBnB,OAAO,CAACzC,GAAG,EAAE,CAAC,CAC/C,CAAC,CAED,KAAM,CAAA6D,mBAAmB,CAAIpB,OAAgB,EAAK,CAChD,GAAIT,MAAM,CAAC8B,OAAO,CAAC,oCAAoCrB,OAAO,CAACzC,GAAG,IAAI,CAAC,CAAE,CACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACAgB,WAAW,CAACD,QAAQ,CAACgD,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACjE,EAAE,GAAK0C,OAAO,CAAC1C,EAAE,CAAC,CAAC,CAC9D,CACF,CAAC,CAED,KAAM,CAAAkE,qBAAqB,CAAG,KAAAA,CAAOxB,OAAgB,CAAEyB,MAAwB,GAAK,CAClF;AACAN,KAAK,CAAC,0BAA0BnB,OAAO,CAACzC,GAAG,SAASkE,MAAM,EAAE,CAAC,CAE7D;AACA,KAAM,IAAI,CAAAC,OAAO,CAACC,OAAO,EAAIrC,UAAU,CAACqC,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAIC,cAAuB,EAAK,CACxD;AACAtD,WAAW,CAACuD,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACR,IAAI,EACnBA,IAAI,CAACjE,EAAE,GAAKuE,cAAc,CAACvE,EAAE,CAAGuE,cAAc,CAAGN,IACnD,CACF,CAAC,CACH,CAAC,CAED,mBACEnE,KAAA,QAAK4E,SAAS,CAAC,gEAAgE,CAAAC,QAAA,eAE7E/E,IAAA,QAAK8E,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACzE/E,IAAA,QAAK8E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChC7E,KAAA,QAAK4E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD/E,IAAA,OAAI8E,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cACxE7E,KAAA,WACE8E,OAAO,CAAEA,CAAA,GAAMnD,2BAA2B,CAAC,IAAI,CAAE,CACjDiD,SAAS,CAAC,0LAA0L,CAAAC,QAAA,eAEpM/E,IAAA,CAACF,QAAQ,EAACgF,SAAS,CAAC,cAAc,CAAE,CAAC,oBAEvC,EAAQ,CAAC,EACN,CAAC,CACH,CAAC,CACH,CAAC,cAGN9E,IAAA,QAAK8E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzC/E,IAAA,QAAK8E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChC7E,KAAA,QAAK4E,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpD/E,IAAA,QAAK8E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B/E,IAAA,CAACP,kBAAkB,GAAE,CAAC,CACnB,CAAC,cAGNO,IAAA,QAAK8E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B/E,IAAA,CAACN,aAAa,EACZ0B,QAAQ,CAAEA,QAAS,CACnB6D,MAAM,CAAEpC,iBAAkB,CAC1BqC,OAAO,CAAElB,kBAAmB,CAC5BmB,QAAQ,CAAEjB,mBAAoB,CAC9BkB,gBAAgB,CAAEV,oBAAqB,CACxC,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAGLpD,eAAe,eACdtB,IAAA,CAACL,gBAAgB,EACfmD,OAAO,CAAExB,eAAgB,CACzBI,iBAAiB,CAAEA,iBAAkB,CACrC2D,MAAM,CAAE7D,eAAgB,CACxB8D,OAAO,CAAEA,CAAA,GAAM7D,kBAAkB,CAAC,KAAK,CAAE,CACzCyD,OAAO,CAAEZ,qBAAsB,CAChC,CACF,cAGDtE,IAAA,CAACJ,yBAAyB,EACxByF,MAAM,CAAEzD,wBAAyB,CACjC0D,OAAO,CAAEA,CAAA,GAAMzD,2BAA2B,CAAC,KAAK,CAAE,CACnD,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}