import os
import sys
import time
import requests

def check_backend():
    """Check if the backend server is running and return any errors"""
    try:
        response = requests.get("http://localhost:8000")
        print(f"Backend status: {response.status_code}")
        print(f"Backend response: {response.text}")
        return True
    except Exception as e:
        print(f"Backend error: {str(e)}")
        return False

def check_frontend():
    """Check if the frontend server is running and return any errors"""
    try:
        response = requests.get("http://localhost:3000")
        print(f"Frontend status: {response.status_code}")
        print(f"Frontend response: {response.text[:100]}...")
        return True
    except Exception as e:
        print(f"Frontend error: {str(e)}")
        return False

def test_query():
    """Test a query to the backend"""
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json={"query": "Who is the Railway Minister in India?", "model": "gemini-2.0-flash"}
        )
        print(f"Query status: {response.status_code}")
        print(f"Query response: {response.text}")
        return True
    except Exception as e:
        print(f"Query error: {str(e)}")
        return False

def main():
    """Main function"""
    print("Checking backend server...")
    backend_ok = check_backend()
    
    print("\nChecking frontend server...")
    frontend_ok = check_frontend()
    
    if backend_ok:
        print("\nTesting query...")
        test_query()
    
    print("\nDone!")

if __name__ == "__main__":
    main()
