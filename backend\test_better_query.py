#!/usr/bin/env python3
"""
Test script with queries that should have content in documents/websites.
"""

import requests
import json

API_URL = "http://localhost:8000"

def test_relevant_queries():
    """Test with queries that should have relevant content."""
    
    # Test with queries that should match actual content
    test_cases = [
        {
            "query": "Indian Railways information",
            "description": "Should find document and website content"
        },
        {
            "query": "railway transportation", 
            "description": "Should find website content"
        },
        {
            "query": "LLM meaning",
            "description": "Should fallback to general knowledge"
        },
        {
            "query": "train booking",
            "description": "Railway-related query"
        }
    ]
    
    test_models = ["gemini-2.0-flash", "groq-llama3-70b"]
    
    print("🔍 Testing Relevant Queries with Different Models")
    print("=" * 60)
    
    for test_case in test_cases:
        query = test_case["query"]
        description = test_case["description"]
        
        print(f"\n📝 Query: '{query}'")
        print(f"📋 Expected: {description}")
        print("-" * 50)
        
        for model in test_models:
            print(f"\n🤖 Testing with {model}")
            print("-" * 25)
            
            try:
                response = requests.post(f"{API_URL}/api/query", 
                    json={
                        "query": query,
                        "model": model,
                        "fallback_enabled": True
                    }, 
                    timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    doc_answer = "✅" if data.get('document_answer') else "❌"
                    web_answer = "✅" if data.get('website_answer') else "❌"
                    llm_fallback = "✅" if data.get('llm_fallback') else "❌"
                    model_used = data.get('llm_model', 'Unknown')
                    
                    print(f"📄 Document: {doc_answer} | 🌐 Website: {web_answer} | 🧠 LLM: {llm_fallback}")
                    print(f"🤖 Model used: {model_used}")
                    print(f"📊 Sources: {len(data.get('document_sources', []))} doc, {len(data.get('website_sources', []))} web")
                    
                    # Show answer type based on what was found
                    if data.get('document_answer') and data.get('website_answer'):
                        print("🟡 Both document and website answers available")
                    elif data.get('document_answer'):
                        print("🔵 Document answer only")
                    elif data.get('website_answer'):
                        print("🟢 Website answer only")
                    elif data.get('llm_fallback'):
                        print("🟣 LLM fallback answer")
                    else:
                        print("❌ No answer generated")
                        
                    # Show answer preview
                    answer = data.get('answer', '')
                    if answer:
                        print(f"💬 Answer preview: {answer[:150]}...")
                        
                else:
                    print(f"❌ Request failed: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Better Query Testing Tool")
    print("=" * 60)
    
    # Test if API is available
    try:
        response = requests.get(f"{API_URL}/api/health", timeout=3)
        if response.status_code == 200:
            print("✅ API is healthy and responding")
        else:
            print(f"❌ API health check failed: {response.status_code}")
            exit(1)
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        exit(1)
    
    test_relevant_queries()
    
    print(f"\n✅ Testing complete!") 