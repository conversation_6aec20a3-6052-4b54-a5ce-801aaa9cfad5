{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\";\nimport React, { useState, useEffect, useRef } from 'react';\nimport { getChatSessions, updateChatTitle, deleteChatSession, clearAllChatSessions } from '../../services/supabase';\n\n// Custom event for new chat creation and updates\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ChatSidebar = ({\n  isOpen,\n  onToggle,\n  currentChatId,\n  onChatSelect,\n  onNewChat\n}) => {\n  const [chatSessions, setChatSessions] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [editingChatId, setEditingChatId] = useState(null);\n  const [editingTitle, setEditingTitle] = useState('');\n  const [hoveredChatId, setHoveredChatId] = useState(null);\n  const editInputRef = useRef(null);\n\n  // Load chat sessions on component mount\n  useEffect(() => {\n    loadChatSessions();\n\n    // Listen for new chat events\n    const handleChatCreated = event => {\n      const newChat = event.detail;\n      setChatSessions(prev => [newChat, ...prev]);\n    };\n\n    // Listen for chat update events (like title changes)\n    const handleChatUpdated = event => {\n      const updatedChat = event.detail;\n      setChatSessions(prev => prev.map(chat => chat.id === updatedChat.id ? {\n        ...chat,\n        ...updatedChat\n      } : chat));\n    };\n    window.addEventListener('chatCreated', handleChatCreated);\n    window.addEventListener('chatUpdated', handleChatUpdated);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('chatCreated', handleChatCreated);\n      window.removeEventListener('chatUpdated', handleChatUpdated);\n    };\n  }, []);\n\n  // Auto-focus edit input when editing starts\n  useEffect(() => {\n    if (editingChatId && editInputRef.current) {\n      editInputRef.current.focus();\n      editInputRef.current.select();\n    }\n  }, [editingChatId]);\n  const loadChatSessions = async () => {\n    try {\n      setIsLoading(true);\n      const sessions = await getChatSessions();\n      setChatSessions(sessions || []);\n    } catch (error) {\n      console.warn('Error loading chat sessions:', (error === null || error === void 0 ? void 0 : error.message) || error);\n\n      // Always set empty array on error to prevent crashes\n      setChatSessions([]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleNewChat = async () => {\n    await onNewChat();\n    setSearchQuery(''); // Clear search when creating new chat\n  };\n  const handleRenameChat = (chatId, currentTitle) => {\n    setEditingChatId(chatId);\n    setEditingTitle(currentTitle);\n  };\n  const handleSaveRename = async () => {\n    if (!editingChatId || !editingTitle.trim()) {\n      handleCancelRename();\n      return;\n    }\n    try {\n      await updateChatTitle(editingChatId, editingTitle.trim());\n      setChatSessions(prev => prev.map(chat => chat.id === editingChatId ? {\n        ...chat,\n        title: editingTitle.trim()\n      } : chat));\n    } catch (error) {\n      console.error('Error renaming chat:', error);\n    } finally {\n      handleCancelRename();\n    }\n  };\n  const handleCancelRename = () => {\n    setEditingChatId(null);\n    setEditingTitle('');\n  };\n  const handleDeleteChat = async chatId => {\n    if (!window.confirm('Are you sure you want to delete this chat?')) {\n      return;\n    }\n    try {\n      await deleteChatSession(chatId);\n      setChatSessions(prev => prev.filter(chat => chat.id !== chatId));\n\n      // If we deleted the current chat, trigger a new chat\n      if (chatId === currentChatId) {\n        await onNewChat();\n      }\n    } catch (error) {\n      console.error('Error deleting chat:', error);\n    }\n  };\n  const handleClearAllChats = async () => {\n    if (!window.confirm('Are you sure you want to clear all chat history? This action cannot be undone.')) {\n      return;\n    }\n    try {\n      await clearAllChatSessions();\n      setChatSessions([]);\n      await onNewChat(); // Start a new chat after clearing all\n    } catch (error) {\n      console.error('Error clearing chats:', error);\n    }\n  };\n  const getTimeGroup = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);\n    const chatDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n    if (chatDate.getTime() === today.getTime()) {\n      return 'Today';\n    } else if (chatDate.getTime() === yesterday.getTime()) {\n      return 'Yesterday';\n    } else if (chatDate >= new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7)) {\n      return 'Last 7 days';\n    } else if (chatDate >= new Date(now.getFullYear(), now.getMonth(), 1)) {\n      return 'This month';\n    } else {\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long'\n      });\n    }\n  };\n  const groupChatsByTime = chats => {\n    const groups = {};\n    chats.forEach(chat => {\n      const group = getTimeGroup(chat.updated_at);\n      if (!groups[group]) {\n        groups[group] = [];\n      }\n      groups[group].push(chat);\n    });\n\n    // Sort groups by priority: Today, Yesterday, Last 7 days, This month, then by date\n    const sortedGroups = [];\n    const priorityOrder = ['Today', 'Yesterday', 'Last 7 days', 'This month'];\n    priorityOrder.forEach(label => {\n      if (groups[label]) {\n        sortedGroups.push({\n          label,\n          chats: groups[label]\n        });\n        delete groups[label];\n      }\n    });\n\n    // Add remaining groups (months and years) sorted\n    const remainingGroups = Object.entries(groups).sort(([a], [b]) => {\n      // Try to parse as dates for month names\n      const aDate = new Date(a + ' 1, 2024');\n      const bDate = new Date(b + ' 1, 2024');\n      if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {\n        return bDate.getTime() - aDate.getTime(); // Most recent first\n      }\n\n      // For years, sort numerically\n      const aYear = parseInt(a);\n      const bYear = parseInt(b);\n      if (!isNaN(aYear) && !isNaN(bYear)) {\n        return bYear - aYear; // Most recent first\n      }\n      return b.localeCompare(a); // Fallback alphabetical\n    });\n    remainingGroups.forEach(([label, chats]) => {\n      sortedGroups.push({\n        label,\n        chats\n      });\n    });\n    return sortedGroups;\n  };\n  const getModelIcon = modelUsed => {\n    if (modelUsed !== null && modelUsed !== void 0 && modelUsed.includes('gemini')) return '🧠';\n    if (modelUsed !== null && modelUsed !== void 0 && modelUsed.includes('chatgpt')) return '🤖';\n    if (modelUsed !== null && modelUsed !== void 0 && modelUsed.includes('groq')) return '⚡';\n    if (modelUsed !== null && modelUsed !== void 0 && modelUsed.includes('deepseek')) return '🔍';\n    if (modelUsed !== null && modelUsed !== void 0 && modelUsed.includes('qwen')) return '🌐';\n    if (modelUsed !== null && modelUsed !== void 0 && modelUsed.includes('ollama')) return '🏠';\n    if (modelUsed !== null && modelUsed !== void 0 && modelUsed.includes('huggingface')) return '🤗';\n    return '🧠';\n  };\n\n  // Filter chats based on search query\n  const filteredChats = chatSessions.filter(chat => chat.title.toLowerCase().includes(searchQuery.toLowerCase()));\n  const chatGroups = groupChatsByTime(filteredChats);\n  if (!isOpen) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 z-35 lg:hidden\",\n      onClick: onToggle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed left-0 top-0 h-full w-80 bg-white border-r border-gray-200 shadow-lg z-40 flex flex-col transition-colors duration-300\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800\",\n            children: \"Chat History\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onToggle,\n            className: \"p-1 text-gray-500 hover:text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded\",\n            title: \"Close sidebar\",\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNewChat,\n          className: \"w-full flex items-center justify-center gap-2 p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\",\n          children: \"\\u2795 New Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search chats...\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            className: \"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-colors duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto\",\n        children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center text-gray-500\",\n          children: \"Loading chats...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this) : chatGroups.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center text-gray-500\",\n          children: searchQuery ? 'No chats found' : 'No chat history yet'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this) : chatGroups.map(group => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide\",\n            children: group.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1\",\n            children: group.chats.map(chat => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `mx-2 p-3 rounded-lg cursor-pointer transition-all duration-200 group ${currentChatId === chat.id ? 'bg-blue-100 border border-blue-200' : 'hover:bg-gray-50'}`,\n              onClick: () => !editingChatId && onChatSelect(chat),\n              onMouseEnter: () => setHoveredChatId(chat.id),\n              onMouseLeave: () => setHoveredChatId(null),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2 mb-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs\",\n                      children: getModelIcon(chat.model_used)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 27\n                  }, this), editingChatId === chat.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    ref: editInputRef,\n                    type: \"text\",\n                    value: editingTitle,\n                    onChange: e => setEditingTitle(e.target.value),\n                    onKeyDown: e => {\n                      if (e.key === 'Enter') handleSaveRename();\n                      if (e.key === 'Escape') handleCancelRename();\n                    },\n                    onBlur: handleSaveRename,\n                    className: \"w-full p-1 text-sm border border-blue-300 bg-white text-gray-900 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-800 truncate\",\n                    children: chat.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: new Date(chat.updated_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 25\n                }, this), hoveredChatId === chat.id && !editingChatId && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1 ml-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleRenameChat(chat.id, chat.title);\n                    },\n                    className: \"p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded\",\n                    title: \"Rename chat\",\n                    children: \"\\uD83D\\uDCDD\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleDeleteChat(chat.id);\n                    },\n                    className: \"p-1 text-gray-400 hover:text-red-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 rounded\",\n                    title: \"Delete chat\",\n                    children: \"\\uD83D\\uDDD1\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 23\n              }, this)\n            }, chat.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 17\n          }, this)]\n        }, group.label, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClearAllChats,\n          className: \"w-full p-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50\",\n          children: \"Clear All Chats\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\nexport default ChatSidebar;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getChatSessions", "updateChatTitle", "deleteChatSession", "clearAllChatSessions", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ChatSidebar", "isOpen", "onToggle", "currentChatId", "onChatSelect", "onNewChat", "chatSessions", "setChatSessions", "isLoading", "setIsLoading", "searchQuery", "setSearch<PERSON>uery", "editingChatId", "setEditingChatId", "editingTitle", "setEditingTitle", "hoveredChatId", "setHoveredChatId", "editInputRef", "loadChatSessions", "handleChatCreated", "event", "newChat", "detail", "prev", "handleChatUpdated", "updatedChat", "map", "chat", "id", "window", "addEventListener", "removeEventListener", "current", "focus", "select", "sessions", "error", "console", "warn", "message", "handleNewChat", "handleRenameChat", "chatId", "currentTitle", "handleSaveRename", "trim", "handleCancelRename", "title", "handleDeleteChat", "confirm", "filter", "handleClearAllChats", "getTimeGroup", "dateString", "date", "Date", "now", "today", "getFullYear", "getMonth", "getDate", "yesterday", "getTime", "chatDate", "toLocaleDateString", "year", "month", "groupChatsByTime", "chats", "groups", "for<PERSON>ach", "group", "updated_at", "push", "sortedGroups", "priorityOrder", "label", "remainingGroups", "Object", "entries", "sort", "a", "b", "aDate", "bDate", "isNaN", "aYear", "parseInt", "bYear", "localeCompare", "getModelIcon", "modelUsed", "includes", "filteredChats", "toLowerCase", "chatGroups", "children", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "e", "target", "length", "onMouseEnter", "onMouseLeave", "model_used", "ref", "onKeyDown", "key", "onBlur", "stopPropagation"], "sources": ["C:/IR App/frontend/src/components/chat/ChatSidebar.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { ChatSession, getChatSessions, updateChatTitle, deleteChatSession, clearAllChatSessions } from '../../services/supabase';\n\n// Custom event for new chat creation and updates\ndeclare global {\n  interface WindowEventMap {\n    'chatCreated': CustomEvent<ChatSession>;\n    'chatUpdated': CustomEvent<ChatSession>;\n  }\n}\n\ninterface ChatSidebarProps {\n  isOpen: boolean;\n  onToggle: () => void;\n  currentChatId: string;\n  onChatSelect: (chatSession: ChatSession) => void;\n  onNewChat: () => void;\n}\n\ninterface ChatGroup {\n  label: string;\n  chats: ChatSession[];\n}\n\nconst ChatSidebar: React.FC<ChatSidebarProps> = ({\n  isOpen,\n  onToggle,\n  currentChatId,\n  onChatSelect,\n  onNewChat\n}) => {\n  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [editingChatId, setEditingChatId] = useState<string | null>(null);\n  const [editingTitle, setEditingTitle] = useState('');\n  const [hoveredChatId, setHoveredChatId] = useState<string | null>(null);\n  const editInputRef = useRef<HTMLInputElement>(null);\n\n  // Load chat sessions on component mount\n  useEffect(() => {\n    loadChatSessions();\n\n    // Listen for new chat events\n    const handleChatCreated = (event: CustomEvent<ChatSession>) => {\n      const newChat = event.detail;\n      setChatSessions(prev => [newChat, ...prev]);\n    };\n\n    // Listen for chat update events (like title changes)\n    const handleChatUpdated = (event: CustomEvent<ChatSession>) => {\n      const updatedChat = event.detail;\n      setChatSessions(prev => \n        prev.map(chat => \n          chat.id === updatedChat.id ? { ...chat, ...updatedChat } : chat\n        )\n      );\n    };\n\n    window.addEventListener('chatCreated', handleChatCreated);\n    window.addEventListener('chatUpdated', handleChatUpdated);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('chatCreated', handleChatCreated);\n      window.removeEventListener('chatUpdated', handleChatUpdated);\n    };\n  }, []);\n\n  // Auto-focus edit input when editing starts\n  useEffect(() => {\n    if (editingChatId && editInputRef.current) {\n      editInputRef.current.focus();\n      editInputRef.current.select();\n    }\n  }, [editingChatId]);\n\n  const loadChatSessions = async () => {\n    try {\n      setIsLoading(true);\n      const sessions = await getChatSessions();\n      setChatSessions(sessions || []);\n    } catch (error: any) {\n      console.warn('Error loading chat sessions:', error?.message || error);\n\n      // Always set empty array on error to prevent crashes\n      setChatSessions([]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleNewChat = async () => {\n    await onNewChat();\n    setSearchQuery(''); // Clear search when creating new chat\n  };\n\n  const handleRenameChat = (chatId: string, currentTitle: string) => {\n    setEditingChatId(chatId);\n    setEditingTitle(currentTitle);\n  };\n\n  const handleSaveRename = async () => {\n    if (!editingChatId || !editingTitle.trim()) {\n      handleCancelRename();\n      return;\n    }\n\n      try {\n      await updateChatTitle(editingChatId, editingTitle.trim());\n      setChatSessions(prev =>\n        prev.map(chat =>\n          chat.id === editingChatId ? { ...chat, title: editingTitle.trim() } : chat\n        )\n      );\n      } catch (error) {\n      console.error('Error renaming chat:', error);\n    } finally {\n      handleCancelRename();\n    }\n  };\n\n  const handleCancelRename = () => {\n    setEditingChatId(null);\n    setEditingTitle('');\n  };\n\n  const handleDeleteChat = async (chatId: string) => {\n    if (!window.confirm('Are you sure you want to delete this chat?')) {\n      return;\n    }\n\n      try {\n      await deleteChatSession(chatId);\n      setChatSessions(prev => prev.filter(chat => chat.id !== chatId));\n      \n      // If we deleted the current chat, trigger a new chat\n      if (chatId === currentChatId) {\n        await onNewChat();\n        }\n      } catch (error) {\n        console.error('Error deleting chat:', error);\n    }\n  };\n\n  const handleClearAllChats = async () => {\n    if (!window.confirm('Are you sure you want to clear all chat history? This action cannot be undone.')) {\n      return;\n    }\n\n      try {\n      await clearAllChatSessions();\n      setChatSessions([]);\n      await onNewChat(); // Start a new chat after clearing all\n      } catch (error) {\n      console.error('Error clearing chats:', error);\n    }\n  };\n\n  const getTimeGroup = (dateString: string): string => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);\n    const chatDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n\n    if (chatDate.getTime() === today.getTime()) {\n      return 'Today';\n    } else if (chatDate.getTime() === yesterday.getTime()) {\n      return 'Yesterday';\n    } else if (chatDate >= new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7)) {\n      return 'Last 7 days';\n    } else if (chatDate >= new Date(now.getFullYear(), now.getMonth(), 1)) {\n      return 'This month';\n    } else {\n      return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });\n    }\n  };\n\n  const groupChatsByTime = (chats: ChatSession[]): ChatGroup[] => {\n    const groups: { [key: string]: ChatSession[] } = {};\n\n    chats.forEach(chat => {\n      const group = getTimeGroup(chat.updated_at);\n      if (!groups[group]) {\n        groups[group] = [];\n      }\n      groups[group].push(chat);\n    });\n\n    // Sort groups by priority: Today, Yesterday, Last 7 days, This month, then by date\n    const sortedGroups: ChatGroup[] = [];\n    const priorityOrder = ['Today', 'Yesterday', 'Last 7 days', 'This month'];\n\n    priorityOrder.forEach(label => {\n      if (groups[label]) {\n        sortedGroups.push({ label, chats: groups[label] });\n        delete groups[label];\n      }\n    });\n\n    // Add remaining groups (months and years) sorted\n    const remainingGroups = Object.entries(groups).sort(([a], [b]) => {\n      // Try to parse as dates for month names\n      const aDate = new Date(a + ' 1, 2024');\n      const bDate = new Date(b + ' 1, 2024');\n\n      if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {\n        return bDate.getTime() - aDate.getTime(); // Most recent first\n      }\n\n      // For years, sort numerically\n      const aYear = parseInt(a);\n      const bYear = parseInt(b);\n      if (!isNaN(aYear) && !isNaN(bYear)) {\n        return bYear - aYear; // Most recent first\n      }\n\n      return b.localeCompare(a); // Fallback alphabetical\n    });\n\n    remainingGroups.forEach(([label, chats]) => {\n      sortedGroups.push({ label, chats });\n    });\n\n    return sortedGroups;\n  };\n\n  const getModelIcon = (modelUsed: string) => {\n    if (modelUsed?.includes('gemini')) return '🧠';\n    if (modelUsed?.includes('chatgpt')) return '🤖';\n    if (modelUsed?.includes('groq')) return '⚡';\n    if (modelUsed?.includes('deepseek')) return '🔍';\n    if (modelUsed?.includes('qwen')) return '🌐';\n    if (modelUsed?.includes('ollama')) return '🏠';\n    if (modelUsed?.includes('huggingface')) return '🤗';\n    return '🧠';\n  };\n\n  // Filter chats based on search query\n  const filteredChats = chatSessions.filter(chat =>\n    chat.title.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  const chatGroups = groupChatsByTime(filteredChats);\n\n  if (!isOpen) {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Overlay for mobile */}\n      <div\n        className=\"fixed inset-0 bg-black bg-opacity-50 z-35 lg:hidden\"\n        onClick={onToggle}\n      />\n\n      {/* Sidebar */}\n      <div className=\"fixed left-0 top-0 h-full w-80 bg-white border-r border-gray-200 shadow-lg z-40 flex flex-col transition-colors duration-300\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-lg font-semibold text-gray-800\">Chat History</h2>\n            <button\n              onClick={onToggle}\n              className=\"p-1 text-gray-500 hover:text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded\"\n              title=\"Close sidebar\"\n            >\n              ✕\n            </button>\n          </div>\n\n          {/* New Chat Button */}\n          <button\n            onClick={handleNewChat}\n            className=\"w-full flex items-center justify-center gap-2 p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\"\n          >\n            ➕ New Chat\n          </button>\n\n          {/* Search Bar */}\n          <div className=\"mt-3\">\n            <input\n              type=\"text\"\n              placeholder=\"Search chats...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-colors duration-300\"\n            />\n          </div>\n        </div>\n\n        {/* Chat List */}\n        <div className=\"flex-1 overflow-y-auto\">\n          {isLoading ? (\n            <div className=\"p-4 text-center text-gray-500\">Loading chats...</div>\n          ) : chatGroups.length === 0 ? (\n            <div className=\"p-4 text-center text-gray-500\">\n              {searchQuery ? 'No chats found' : 'No chat history yet'}\n            </div>\n          ) : (\n            chatGroups.map((group) => (\n              <div key={group.label} className=\"mb-4\">\n                <h3 className=\"px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide\">\n                  {group.label}\n                </h3>\n                <div className=\"space-y-1\">\n                  {group.chats.map((chat) => (\n                    <div\n                      key={chat.id}\n                      className={`mx-2 p-3 rounded-lg cursor-pointer transition-all duration-200 group ${\n                        currentChatId === chat.id\n                          ? 'bg-blue-100 border border-blue-200'\n                          : 'hover:bg-gray-50'\n                      }`}\n                      onClick={() => !editingChatId && onChatSelect(chat)}\n                      onMouseEnter={() => setHoveredChatId(chat.id)}\n                      onMouseLeave={() => setHoveredChatId(null)}\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center gap-2 mb-1\">\n                            <span className=\"text-xs\">\n                              {getModelIcon(chat.model_used)}\n                            </span>\n                          </div>\n\n                          {editingChatId === chat.id ? (\n                            <input\n                              ref={editInputRef}\n                              type=\"text\"\n                              value={editingTitle}\n                              onChange={(e) => setEditingTitle(e.target.value)}\n                              onKeyDown={(e) => {\n                                if (e.key === 'Enter') handleSaveRename();\n                                if (e.key === 'Escape') handleCancelRename();\n                              }}\n                              onBlur={handleSaveRename}\n                              className=\"w-full p-1 text-sm border border-blue-300 bg-white text-gray-900 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300\"\n                            />\n                          ) : (\n                            <h4 className=\"text-sm font-medium text-gray-800 truncate\">\n                              {chat.title}\n                            </h4>\n                          )}\n\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            {new Date(chat.updated_at).toLocaleDateString()}\n                          </p>\n                        </div>\n\n                        {/* Action Buttons */}\n                        {hoveredChatId === chat.id && !editingChatId && (\n                          <div className=\"flex items-center gap-1 ml-2\">\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                handleRenameChat(chat.id, chat.title);\n                              }}\n                              className=\"p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded\"\n                              title=\"Rename chat\"\n                            >\n                              📝\n                            </button>\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                handleDeleteChat(chat.id);\n                              }}\n                              className=\"p-1 text-gray-400 hover:text-red-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 rounded\"\n                              title=\"Delete chat\"\n                            >\n                              🗑️\n                            </button>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n\n        {/* Footer */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <button\n            onClick={handleClearAllChats}\n            className=\"w-full p-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50\"\n          >\n            Clear All Chats\n          </button>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default ChatSidebar;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAAsBC,eAAe,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,yBAAyB;;AAEhI;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqBA,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,MAAM;EACNC,QAAQ;EACRC,aAAa;EACbC,YAAY;EACZC;AACF,CAAC,KAAK;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM6B,YAAY,GAAG3B,MAAM,CAAmB,IAAI,CAAC;;EAEnD;EACAD,SAAS,CAAC,MAAM;IACd6B,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMC,iBAAiB,GAAIC,KAA+B,IAAK;MAC7D,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM;MAC5BhB,eAAe,CAACiB,IAAI,IAAI,CAACF,OAAO,EAAE,GAAGE,IAAI,CAAC,CAAC;IAC7C,CAAC;;IAED;IACA,MAAMC,iBAAiB,GAAIJ,KAA+B,IAAK;MAC7D,MAAMK,WAAW,GAAGL,KAAK,CAACE,MAAM;MAChChB,eAAe,CAACiB,IAAI,IAClBA,IAAI,CAACG,GAAG,CAACC,IAAI,IACXA,IAAI,CAACC,EAAE,KAAKH,WAAW,CAACG,EAAE,GAAG;QAAE,GAAGD,IAAI;QAAE,GAAGF;MAAY,CAAC,GAAGE,IAC7D,CACF,CAAC;IACH,CAAC;IAEDE,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEX,iBAAiB,CAAC;IACzDU,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEN,iBAAiB,CAAC;;IAEzD;IACA,OAAO,MAAM;MACXK,MAAM,CAACE,mBAAmB,CAAC,aAAa,EAAEZ,iBAAiB,CAAC;MAC5DU,MAAM,CAACE,mBAAmB,CAAC,aAAa,EAAEP,iBAAiB,CAAC;IAC9D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnC,SAAS,CAAC,MAAM;IACd,IAAIsB,aAAa,IAAIM,YAAY,CAACe,OAAO,EAAE;MACzCf,YAAY,CAACe,OAAO,CAACC,KAAK,CAAC,CAAC;MAC5BhB,YAAY,CAACe,OAAO,CAACE,MAAM,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACvB,aAAa,CAAC,CAAC;EAEnB,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFV,YAAY,CAAC,IAAI,CAAC;MAClB,MAAM2B,QAAQ,GAAG,MAAM5C,eAAe,CAAC,CAAC;MACxCe,eAAe,CAAC6B,QAAQ,IAAI,EAAE,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAE,CAAAF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,OAAO,KAAIH,KAAK,CAAC;;MAErE;MACA9B,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACRE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMgC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMpC,SAAS,CAAC,CAAC;IACjBM,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;EACtB,CAAC;EAED,MAAM+B,gBAAgB,GAAGA,CAACC,MAAc,EAAEC,YAAoB,KAAK;IACjE/B,gBAAgB,CAAC8B,MAAM,CAAC;IACxB5B,eAAe,CAAC6B,YAAY,CAAC;EAC/B,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACjC,aAAa,IAAI,CAACE,YAAY,CAACgC,IAAI,CAAC,CAAC,EAAE;MAC1CC,kBAAkB,CAAC,CAAC;MACpB;IACF;IAEE,IAAI;MACJ,MAAMtD,eAAe,CAACmB,aAAa,EAAEE,YAAY,CAACgC,IAAI,CAAC,CAAC,CAAC;MACzDvC,eAAe,CAACiB,IAAI,IAClBA,IAAI,CAACG,GAAG,CAACC,IAAI,IACXA,IAAI,CAACC,EAAE,KAAKjB,aAAa,GAAG;QAAE,GAAGgB,IAAI;QAAEoB,KAAK,EAAElC,YAAY,CAACgC,IAAI,CAAC;MAAE,CAAC,GAAGlB,IACxE,CACF,CAAC;IACD,CAAC,CAAC,OAAOS,KAAK,EAAE;MAChBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRU,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC;EAED,MAAMA,kBAAkB,GAAGA,CAAA,KAAM;IAC/BlC,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAMkC,gBAAgB,GAAG,MAAON,MAAc,IAAK;IACjD,IAAI,CAACb,MAAM,CAACoB,OAAO,CAAC,4CAA4C,CAAC,EAAE;MACjE;IACF;IAEE,IAAI;MACJ,MAAMxD,iBAAiB,CAACiD,MAAM,CAAC;MAC/BpC,eAAe,CAACiB,IAAI,IAAIA,IAAI,CAAC2B,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKc,MAAM,CAAC,CAAC;;MAEhE;MACA,IAAIA,MAAM,KAAKxC,aAAa,EAAE;QAC5B,MAAME,SAAS,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMe,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACtB,MAAM,CAACoB,OAAO,CAAC,gFAAgF,CAAC,EAAE;MACrG;IACF;IAEE,IAAI;MACJ,MAAMvD,oBAAoB,CAAC,CAAC;MAC5BY,eAAe,CAAC,EAAE,CAAC;MACnB,MAAMF,SAAS,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAChBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMgB,YAAY,GAAIC,UAAkB,IAAa;IACnD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,KAAK,GAAG,IAAIF,IAAI,CAACC,GAAG,CAACE,WAAW,CAAC,CAAC,EAAEF,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAEH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC;IACxE,MAAMC,SAAS,GAAG,IAAIN,IAAI,CAACE,KAAK,CAACK,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACjE,MAAMC,QAAQ,GAAG,IAAIR,IAAI,CAACD,IAAI,CAACI,WAAW,CAAC,CAAC,EAAEJ,IAAI,CAACK,QAAQ,CAAC,CAAC,EAAEL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC;IAE9E,IAAIG,QAAQ,CAACD,OAAO,CAAC,CAAC,KAAKL,KAAK,CAACK,OAAO,CAAC,CAAC,EAAE;MAC1C,OAAO,OAAO;IAChB,CAAC,MAAM,IAAIC,QAAQ,CAACD,OAAO,CAAC,CAAC,KAAKD,SAAS,CAACC,OAAO,CAAC,CAAC,EAAE;MACrD,OAAO,WAAW;IACpB,CAAC,MAAM,IAAIC,QAAQ,IAAI,IAAIR,IAAI,CAACC,GAAG,CAACE,WAAW,CAAC,CAAC,EAAEF,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAEH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;MACrF,OAAO,aAAa;IACtB,CAAC,MAAM,IAAIG,QAAQ,IAAI,IAAIR,IAAI,CAACC,GAAG,CAACE,WAAW,CAAC,CAAC,EAAEF,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACrE,OAAO,YAAY;IACrB,CAAC,MAAM;MACL,OAAOL,IAAI,CAACU,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC,CAAC;IAC7E;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIC,KAAoB,IAAkB;IAC9D,MAAMC,MAAwC,GAAG,CAAC,CAAC;IAEnDD,KAAK,CAACE,OAAO,CAAC3C,IAAI,IAAI;MACpB,MAAM4C,KAAK,GAAGnB,YAAY,CAACzB,IAAI,CAAC6C,UAAU,CAAC;MAC3C,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,EAAE;QAClBF,MAAM,CAACE,KAAK,CAAC,GAAG,EAAE;MACpB;MACAF,MAAM,CAACE,KAAK,CAAC,CAACE,IAAI,CAAC9C,IAAI,CAAC;IAC1B,CAAC,CAAC;;IAEF;IACA,MAAM+C,YAAyB,GAAG,EAAE;IACpC,MAAMC,aAAa,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC;IAEzEA,aAAa,CAACL,OAAO,CAACM,KAAK,IAAI;MAC7B,IAAIP,MAAM,CAACO,KAAK,CAAC,EAAE;QACjBF,YAAY,CAACD,IAAI,CAAC;UAAEG,KAAK;UAAER,KAAK,EAAEC,MAAM,CAACO,KAAK;QAAE,CAAC,CAAC;QAClD,OAAOP,MAAM,CAACO,KAAK,CAAC;MACtB;IACF,CAAC,CAAC;;IAEF;IACA,MAAMC,eAAe,GAAGC,MAAM,CAACC,OAAO,CAACV,MAAM,CAAC,CAACW,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAK;MAChE;MACA,MAAMC,KAAK,GAAG,IAAI5B,IAAI,CAAC0B,CAAC,GAAG,UAAU,CAAC;MACtC,MAAMG,KAAK,GAAG,IAAI7B,IAAI,CAAC2B,CAAC,GAAG,UAAU,CAAC;MAEtC,IAAI,CAACG,KAAK,CAACF,KAAK,CAACrB,OAAO,CAAC,CAAC,CAAC,IAAI,CAACuB,KAAK,CAACD,KAAK,CAACtB,OAAO,CAAC,CAAC,CAAC,EAAE;QACtD,OAAOsB,KAAK,CAACtB,OAAO,CAAC,CAAC,GAAGqB,KAAK,CAACrB,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5C;;MAEA;MACA,MAAMwB,KAAK,GAAGC,QAAQ,CAACN,CAAC,CAAC;MACzB,MAAMO,KAAK,GAAGD,QAAQ,CAACL,CAAC,CAAC;MACzB,IAAI,CAACG,KAAK,CAACC,KAAK,CAAC,IAAI,CAACD,KAAK,CAACG,KAAK,CAAC,EAAE;QAClC,OAAOA,KAAK,GAAGF,KAAK,CAAC,CAAC;MACxB;MAEA,OAAOJ,CAAC,CAACO,aAAa,CAACR,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC;IAEFJ,eAAe,CAACP,OAAO,CAAC,CAAC,CAACM,KAAK,EAAER,KAAK,CAAC,KAAK;MAC1CM,YAAY,CAACD,IAAI,CAAC;QAAEG,KAAK;QAAER;MAAM,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,OAAOM,YAAY;EACrB,CAAC;EAED,MAAMgB,YAAY,GAAIC,SAAiB,IAAK;IAC1C,IAAIA,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,IAAI;IAC9C,IAAID,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,IAAI;IAC/C,IAAID,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,GAAG;IAC3C,IAAID,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,IAAI;IAChD,IAAID,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,IAAI;IAC5C,IAAID,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,IAAI;IAC9C,IAAID,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,IAAI;IACnD,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGxF,YAAY,CAAC6C,MAAM,CAACvB,IAAI,IAC5CA,IAAI,CAACoB,KAAK,CAAC+C,WAAW,CAAC,CAAC,CAACF,QAAQ,CAACnF,WAAW,CAACqF,WAAW,CAAC,CAAC,CAC7D,CAAC;EAED,MAAMC,UAAU,GAAG5B,gBAAgB,CAAC0B,aAAa,CAAC;EAElD,IAAI,CAAC7F,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,oBACEJ,OAAA,CAAAE,SAAA;IAAAkG,QAAA,gBAEEpG,OAAA;MACEqG,SAAS,EAAC,qDAAqD;MAC/DC,OAAO,EAAEjG;IAAS;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAGF1G,OAAA;MAAKqG,SAAS,EAAC,8HAA8H;MAAAD,QAAA,gBAE3IpG,OAAA;QAAKqG,SAAS,EAAC,8BAA8B;QAAAD,QAAA,gBAC3CpG,OAAA;UAAKqG,SAAS,EAAC,wCAAwC;UAAAD,QAAA,gBACrDpG,OAAA;YAAIqG,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrE1G,OAAA;YACEsG,OAAO,EAAEjG,QAAS;YAClBgG,SAAS,EAAC,wJAAwJ;YAClKlD,KAAK,EAAC,eAAe;YAAAiD,QAAA,EACtB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN1G,OAAA;UACEsG,OAAO,EAAE1D,aAAc;UACvByD,SAAS,EAAC,gNAAgN;UAAAD,QAAA,EAC3N;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAGT1G,OAAA;UAAKqG,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBpG,OAAA;YACE2G,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,iBAAiB;YAC7BC,KAAK,EAAEhG,WAAY;YACnBiG,QAAQ,EAAGC,CAAC,IAAKjG,cAAc,CAACiG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDR,SAAS,EAAC;UAAgK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3K;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1G,OAAA;QAAKqG,SAAS,EAAC,wBAAwB;QAAAD,QAAA,EACpCzF,SAAS,gBACRX,OAAA;UAAKqG,SAAS,EAAC,+BAA+B;UAAAD,QAAA,EAAC;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GACnEP,UAAU,CAACc,MAAM,KAAK,CAAC,gBACzBjH,OAAA;UAAKqG,SAAS,EAAC,+BAA+B;UAAAD,QAAA,EAC3CvF,WAAW,GAAG,gBAAgB,GAAG;QAAqB;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,GAENP,UAAU,CAACrE,GAAG,CAAE6C,KAAK,iBACnB3E,OAAA;UAAuBqG,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACrCpG,OAAA;YAAIqG,SAAS,EAAC,uEAAuE;YAAAD,QAAA,EAClFzB,KAAK,CAACK;UAAK;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACL1G,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAD,QAAA,EACvBzB,KAAK,CAACH,KAAK,CAAC1C,GAAG,CAAEC,IAAI,iBACpB/B,OAAA;cAEEqG,SAAS,EAAE,wEACT/F,aAAa,KAAKyB,IAAI,CAACC,EAAE,GACrB,oCAAoC,GACpC,kBAAkB,EACrB;cACHsE,OAAO,EAAEA,CAAA,KAAM,CAACvF,aAAa,IAAIR,YAAY,CAACwB,IAAI,CAAE;cACpDmF,YAAY,EAAEA,CAAA,KAAM9F,gBAAgB,CAACW,IAAI,CAACC,EAAE,CAAE;cAC9CmF,YAAY,EAAEA,CAAA,KAAM/F,gBAAgB,CAAC,IAAI,CAAE;cAAAgF,QAAA,eAE3CpG,OAAA;gBAAKqG,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,gBAC/CpG,OAAA;kBAAKqG,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,gBAC7BpG,OAAA;oBAAKqG,SAAS,EAAC,8BAA8B;oBAAAD,QAAA,eAC3CpG,OAAA;sBAAMqG,SAAS,EAAC,SAAS;sBAAAD,QAAA,EACtBN,YAAY,CAAC/D,IAAI,CAACqF,UAAU;oBAAC;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAEL3F,aAAa,KAAKgB,IAAI,CAACC,EAAE,gBACxBhC,OAAA;oBACEqH,GAAG,EAAEhG,YAAa;oBAClBsF,IAAI,EAAC,MAAM;oBACXE,KAAK,EAAE5F,YAAa;oBACpB6F,QAAQ,EAAGC,CAAC,IAAK7F,eAAe,CAAC6F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDS,SAAS,EAAGP,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACQ,GAAG,KAAK,OAAO,EAAEvE,gBAAgB,CAAC,CAAC;sBACzC,IAAI+D,CAAC,CAACQ,GAAG,KAAK,QAAQ,EAAErE,kBAAkB,CAAC,CAAC;oBAC9C,CAAE;oBACFsE,MAAM,EAAExE,gBAAiB;oBACzBqD,SAAS,EAAC;kBAA6J;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxK,CAAC,gBAEF1G,OAAA;oBAAIqG,SAAS,EAAC,4CAA4C;oBAAAD,QAAA,EACvDrE,IAAI,CAACoB;kBAAK;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CACL,eAED1G,OAAA;oBAAGqG,SAAS,EAAC,4BAA4B;oBAAAD,QAAA,EACtC,IAAIzC,IAAI,CAAC5B,IAAI,CAAC6C,UAAU,CAAC,CAACR,kBAAkB,CAAC;kBAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EAGLvF,aAAa,KAAKY,IAAI,CAACC,EAAE,IAAI,CAACjB,aAAa,iBAC1Cf,OAAA;kBAAKqG,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,gBAC3CpG,OAAA;oBACEsG,OAAO,EAAGS,CAAC,IAAK;sBACdA,CAAC,CAACU,eAAe,CAAC,CAAC;sBACnB5E,gBAAgB,CAACd,IAAI,CAACC,EAAE,EAAED,IAAI,CAACoB,KAAK,CAAC;oBACvC,CAAE;oBACFkD,SAAS,EAAC,wJAAwJ;oBAClKlD,KAAK,EAAC,aAAa;oBAAAiD,QAAA,EACpB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT1G,OAAA;oBACEsG,OAAO,EAAGS,CAAC,IAAK;sBACdA,CAAC,CAACU,eAAe,CAAC,CAAC;sBACnBrE,gBAAgB,CAACrB,IAAI,CAACC,EAAE,CAAC;oBAC3B,CAAE;oBACFqE,SAAS,EAAC,sJAAsJ;oBAChKlD,KAAK,EAAC,aAAa;oBAAAiD,QAAA,EACpB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAnED3E,IAAI,CAACC,EAAE;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoET,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GA7EE/B,KAAK,CAACK,KAAK;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8EhB,CACN;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN1G,OAAA;QAAKqG,SAAS,EAAC,8BAA8B;QAAAD,QAAA,eAC3CpG,OAAA;UACEsG,OAAO,EAAE/C,mBAAoB;UAC7B8C,SAAS,EAAC,oKAAoK;UAAAD,QAAA,EAC/K;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAED,eAAevG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}