import React, { useState, useEffect, useRef } from 'react';
import { ChatSession, getChatSessions, updateChatTitle, deleteChatSession, clearAllChatSessions } from '../../services/supabase';

// Custom event for new chat creation and updates
declare global {
  interface WindowEventMap {
    'chatCreated': CustomEvent<ChatSession>;
    'chatUpdated': CustomEvent<ChatSession>;
  }
}

interface ChatSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  currentChatId: string;
  onChatSelect: (chatSession: ChatSession) => void;
  onNewChat: () => void;
}

interface ChatGroup {
  label: string;
  chats: ChatSession[];
}

const ChatSidebar: React.FC<ChatSidebarProps> = ({
  isOpen,
  onToggle,
  currentChatId,
  onChatSelect,
  onNewChat
}) => {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [editingChatId, setEditingChatId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [hoveredChatId, setHoveredChatId] = useState<string | null>(null);
  const editInputRef = useRef<HTMLInputElement>(null);

  // Load chat sessions on component mount
  useEffect(() => {
    loadChatSessions();

    // Listen for new chat events
    const handleChatCreated = (event: CustomEvent<ChatSession>) => {
      const newChat = event.detail;
      setChatSessions(prev => [newChat, ...prev]);
    };

    // Listen for chat update events (like title changes)
    const handleChatUpdated = (event: CustomEvent<ChatSession>) => {
      const updatedChat = event.detail;
      setChatSessions(prev => 
        prev.map(chat => 
          chat.id === updatedChat.id ? { ...chat, ...updatedChat } : chat
        )
      );
    };

    window.addEventListener('chatCreated', handleChatCreated);
    window.addEventListener('chatUpdated', handleChatUpdated);

    // Cleanup
    return () => {
      window.removeEventListener('chatCreated', handleChatCreated);
      window.removeEventListener('chatUpdated', handleChatUpdated);
    };
  }, []);

  // Auto-focus edit input when editing starts
  useEffect(() => {
    if (editingChatId && editInputRef.current) {
      editInputRef.current.focus();
      editInputRef.current.select();
    }
  }, [editingChatId]);

  const loadChatSessions = async () => {
    try {
      setIsLoading(true);
      const sessions = await getChatSessions();
      setChatSessions(sessions || []);
    } catch (error: any) {
      console.error('Error loading chat sessions:', error);

      // Handle timeout errors gracefully
      if (error?.code === '57014' || error?.message?.includes('timeout') || error?.name === 'AbortError') {
        console.warn('Chat sessions loading timed out, using empty state');
      }

      setChatSessions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewChat = async () => {
    await onNewChat();
    setSearchQuery(''); // Clear search when creating new chat
  };

  const handleRenameChat = (chatId: string, currentTitle: string) => {
    setEditingChatId(chatId);
    setEditingTitle(currentTitle);
  };

  const handleSaveRename = async () => {
    if (!editingChatId || !editingTitle.trim()) {
      handleCancelRename();
      return;
    }

      try {
      await updateChatTitle(editingChatId, editingTitle.trim());
      setChatSessions(prev =>
        prev.map(chat =>
          chat.id === editingChatId ? { ...chat, title: editingTitle.trim() } : chat
        )
      );
      } catch (error) {
      console.error('Error renaming chat:', error);
    } finally {
      handleCancelRename();
    }
  };

  const handleCancelRename = () => {
    setEditingChatId(null);
    setEditingTitle('');
  };

  const handleDeleteChat = async (chatId: string) => {
    if (!window.confirm('Are you sure you want to delete this chat?')) {
      return;
    }

      try {
      await deleteChatSession(chatId);
      setChatSessions(prev => prev.filter(chat => chat.id !== chatId));
      
      // If we deleted the current chat, trigger a new chat
      if (chatId === currentChatId) {
        await onNewChat();
        }
      } catch (error) {
        console.error('Error deleting chat:', error);
    }
  };

  const handleClearAllChats = async () => {
    if (!window.confirm('Are you sure you want to clear all chat history? This action cannot be undone.')) {
      return;
    }

      try {
      await clearAllChatSessions();
      setChatSessions([]);
      await onNewChat(); // Start a new chat after clearing all
      } catch (error) {
      console.error('Error clearing chats:', error);
    }
  };

  const getTimeGroup = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const chatDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (chatDate.getTime() === today.getTime()) {
      return 'Today';
    } else if (chatDate.getTime() === yesterday.getTime()) {
      return 'Yesterday';
    } else if (chatDate >= new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7)) {
      return 'Last 7 days';
    } else if (chatDate >= new Date(now.getFullYear(), now.getMonth(), 1)) {
      return 'This month';
    } else {
      return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
    }
  };

  const groupChatsByTime = (chats: ChatSession[]): ChatGroup[] => {
    const groups: { [key: string]: ChatSession[] } = {};

    chats.forEach(chat => {
      const group = getTimeGroup(chat.updated_at);
      if (!groups[group]) {
        groups[group] = [];
      }
      groups[group].push(chat);
    });

    // Sort groups by priority: Today, Yesterday, Last 7 days, This month, then by date
    const sortedGroups: ChatGroup[] = [];
    const priorityOrder = ['Today', 'Yesterday', 'Last 7 days', 'This month'];

    priorityOrder.forEach(label => {
      if (groups[label]) {
        sortedGroups.push({ label, chats: groups[label] });
        delete groups[label];
      }
    });

    // Add remaining groups (months and years) sorted
    const remainingGroups = Object.entries(groups).sort(([a], [b]) => {
      // Try to parse as dates for month names
      const aDate = new Date(a + ' 1, 2024');
      const bDate = new Date(b + ' 1, 2024');

      if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
        return bDate.getTime() - aDate.getTime(); // Most recent first
      }

      // For years, sort numerically
      const aYear = parseInt(a);
      const bYear = parseInt(b);
      if (!isNaN(aYear) && !isNaN(bYear)) {
        return bYear - aYear; // Most recent first
      }

      return b.localeCompare(a); // Fallback alphabetical
    });

    remainingGroups.forEach(([label, chats]) => {
      sortedGroups.push({ label, chats });
    });

    return sortedGroups;
  };

  const getModelIcon = (modelUsed: string) => {
    if (modelUsed?.includes('gemini')) return '🧠';
    if (modelUsed?.includes('chatgpt')) return '🤖';
    if (modelUsed?.includes('groq')) return '⚡';
    if (modelUsed?.includes('deepseek')) return '🔍';
    if (modelUsed?.includes('qwen')) return '🌐';
    if (modelUsed?.includes('ollama')) return '🏠';
    if (modelUsed?.includes('huggingface')) return '🤗';
    return '🧠';
  };

  // Filter chats based on search query
  const filteredChats = chatSessions.filter(chat =>
    chat.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const chatGroups = groupChatsByTime(filteredChats);

  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* Overlay for mobile */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-35 lg:hidden"
        onClick={onToggle}
      />

      {/* Sidebar */}
      <div className="fixed left-0 top-0 h-full w-80 bg-white border-r border-gray-200 shadow-lg z-40 flex flex-col transition-colors duration-300">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Chat History</h2>
            <button
              onClick={onToggle}
              className="p-1 text-gray-500 hover:text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded"
              title="Close sidebar"
            >
              ✕
            </button>
          </div>

          {/* New Chat Button */}
          <button
            onClick={handleNewChat}
            className="w-full flex items-center justify-center gap-2 p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            ➕ New Chat
          </button>

          {/* Search Bar */}
          <div className="mt-3">
            <input
              type="text"
              placeholder="Search chats..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-colors duration-300"
            />
          </div>
        </div>

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center text-gray-500">Loading chats...</div>
          ) : chatGroups.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {searchQuery ? 'No chats found' : 'No chat history yet'}
            </div>
          ) : (
            chatGroups.map((group) => (
              <div key={group.label} className="mb-4">
                <h3 className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                  {group.label}
                </h3>
                <div className="space-y-1">
                  {group.chats.map((chat) => (
                    <div
                      key={chat.id}
                      className={`mx-2 p-3 rounded-lg cursor-pointer transition-all duration-200 group ${
                        currentChatId === chat.id
                          ? 'bg-blue-100 border border-blue-200'
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={() => !editingChatId && onChatSelect(chat)}
                      onMouseEnter={() => setHoveredChatId(chat.id)}
                      onMouseLeave={() => setHoveredChatId(null)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-xs">
                              {getModelIcon(chat.model_used)}
                            </span>
                          </div>

                          {editingChatId === chat.id ? (
                            <input
                              ref={editInputRef}
                              type="text"
                              value={editingTitle}
                              onChange={(e) => setEditingTitle(e.target.value)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') handleSaveRename();
                                if (e.key === 'Escape') handleCancelRename();
                              }}
                              onBlur={handleSaveRename}
                              className="w-full p-1 text-sm border border-blue-300 bg-white text-gray-900 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300"
                            />
                          ) : (
                            <h4 className="text-sm font-medium text-gray-800 truncate">
                              {chat.title}
                            </h4>
                          )}

                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(chat.updated_at).toLocaleDateString()}
                          </p>
                        </div>

                        {/* Action Buttons */}
                        {hoveredChatId === chat.id && !editingChatId && (
                          <div className="flex items-center gap-1 ml-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRenameChat(chat.id, chat.title);
                              }}
                              className="p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded"
                              title="Rename chat"
                            >
                              📝
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteChat(chat.id);
                              }}
                              className="p-1 text-gray-400 hover:text-red-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 rounded"
                              title="Delete chat"
                            >
                              🗑️
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={handleClearAllChats}
            className="w-full p-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
          >
            Clear All Chats
          </button>
        </div>
      </div>
    </>
  );
};

export default ChatSidebar;