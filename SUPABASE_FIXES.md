# Supabase Integration Fixes

This document provides instructions for fixing the Supabase integration issues in the RailGPT application.

## Issues Identified

Based on the logs and code analysis, the following issues were identified:

1. **Missing Hybrid Search Functions**: The `hybrid_search_document_chunks` and `hybrid_search_website_chunks` functions are referenced in the code but not defined in the Supabase database.

2. **Embedding Format Issues**: The embeddings are not being properly formatted or stored, leading to errors like `'similarity'` and empty embeddings.

3. **Vector Database Stats**: The vector database stats show 0 chunks, even though chunks are being successfully stored.

## Fix Instructions

### 1. Add Hybrid Search Functions to Supabase

1. Log in to the Supabase dashboard for the "RailGPT" project.
2. Go to the SQL Editor.
3. Copy the contents of the `supabase_hybrid_search.sql` file.
4. Paste the SQL into the editor and run it.
5. This will create the missing hybrid search functions.

### 2. Fix Embedding Format Issues

1. Run the following script to fix the embedding format issues:

```powershell
cd backend
python fix_embeddings.py
```

This script will:
- Check for empty or invalid embeddings in the document_chunks and website_chunks tables
- Update them with properly formatted mock embeddings

### 3. Restart the Backend Server

After applying the fixes, restart the backend server:

```powershell
cd backend
uvicorn server:app --reload
```

## Verification

To verify that the fixes worked:

1. Check the logs for any errors related to `'similarity'` or `'text'`.
2. Try searching for "rapid response app" in the RailGPT interface.
3. Check if the vector database stats show the correct number of chunks.

## Additional Scripts

The following scripts were created to help fix the issues:

- `add_hybrid_search_functions.py`: Adds the hybrid search functions to Supabase.
- `fix_embeddings.py`: Fixes the embedding format issues.
- `run_all_fixes.py`: Runs all the fixes in sequence.

## Manual SQL Execution

If the scripts don't work, you can manually execute the SQL in the Supabase SQL Editor:

1. Go to the Supabase dashboard for the "RailGPT" project.
2. Go to the SQL Editor.
3. Copy the contents of the `supabase_hybrid_search.sql` file.
4. Paste the SQL into the editor and run it.

## Troubleshooting

If you still encounter issues:

1. Check the logs for specific error messages.
2. Verify that the Supabase URL and API key are correct.
3. Check if the required tables exist in the Supabase database.
4. Try running the test scripts to verify the functionality:

```powershell
cd backend
python test_vector_db.py
```

## Contact

If you need further assistance, please contact the development team.
