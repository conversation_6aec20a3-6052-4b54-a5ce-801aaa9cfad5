# RailGPT Visual Content User Guide

## 🎯 Overview

RailGPT now supports **comprehensive visual content extraction and display** from uploaded documents. This includes tables, images, charts, and diagrams from PDF, DOCX, and XLSX files.

## 📋 Visual Content Types Supported

### 1. **Tables** 📊
- **Extraction**: Automatically detects and extracts tables from documents
- **Display**: Renders as interactive HTML tables with proper formatting
- **Search**: Query table data using natural language
- **Formats**: Supports complex tables with headers, merged cells, and formatting

### 2. **Images** 🖼️
- **Extraction**: Extracts embedded images from documents
- **Display**: Shows images inline with proper scaling and alt text
- **Storage**: Images stored securely in Supabase Storage
- **Metadata**: Includes dimensions, format, and file size information

### 3. **Charts & Diagrams** 📈
- **Detection**: Identifies technical drawings, flowcharts, and diagrams
- **Analysis**: Extracts metadata about chart elements and structure
- **Display**: Shows chart information with confidence scores

## 🔍 How to Query Visual Content

### Table Queries
Use these keywords to find table data:
```
"Show me the specifications table"
"What are the part numbers in the table?"
"List all components from the table"
"Display the maintenance schedule table"
"What is the weight of component X?"
```

### Image Queries
Use these keywords to find images:
```
"Show me the diagram"
"Display the technical drawing"
"What images are in the document?"
"Show me the schematic"
"Display the photo of the component"
```

### Chart Queries
Use these keywords to find charts:
```
"Show me the flowchart"
"Display the process diagram"
"What charts are available?"
"Show me the technical drawing"
"Display the blueprint"
```

## 📤 Uploading Documents with Visual Content

### Step 1: Upload Document
1. Click the **Upload** button in RailGPT
2. Select your PDF, DOCX, or XLSX file
3. **Enable visual extraction options**:
   - ✅ Extract Tables
   - ✅ Extract Images  
   - ✅ Extract Charts

### Step 2: Wait for Processing
- Documents are processed to extract both text and visual content
- Processing time depends on document size and complexity
- You'll see a confirmation when extraction is complete

### Step 3: Query Visual Content
- Use natural language to ask about tables, images, or charts
- RailGPT will prioritize visual content when relevant
- Visual content displays alongside text answers

## 🎨 Visual Content Display Features

### Interactive Tables
- **Responsive Design**: Tables adapt to screen size
- **Scrollable**: Large tables can be scrolled horizontally
- **Formatted Headers**: Clear distinction between headers and data
- **Hover Effects**: Rows highlight on hover for better readability

### Image Display
- **High Quality**: Images display at full resolution
- **Loading States**: Smooth loading with progress indicators
- **Error Handling**: Graceful fallback if images can't be loaded
- **Metadata**: Shows image information when available

### Chart Information
- **Structured Data**: Displays chart metadata and confidence scores
- **Visual Indicators**: Shows what types of elements were detected
- **Context**: Links charts to surrounding text content

## 🚀 Best Practices

### Document Preparation
1. **High Quality**: Use high-resolution PDFs for better extraction
2. **Clear Tables**: Ensure tables have clear borders and headers
3. **Readable Text**: Avoid heavily compressed or low-quality scans
4. **Standard Formats**: Use standard table layouts when possible

### Query Optimization
1. **Be Specific**: Use specific terms like "specifications table" vs "table"
2. **Include Context**: Mention page numbers or section names if known
3. **Use Keywords**: Include relevant technical terms from your domain
4. **Multiple Queries**: Try different phrasings if first query doesn't work

### Performance Tips
1. **Document Size**: Large documents (>50MB) may take longer to process
2. **Complex Tables**: Very complex tables may need manual verification
3. **Image Quality**: Higher quality images extract better metadata
4. **Network**: Ensure stable internet for image loading

## 🔧 Troubleshooting

### Visual Content Not Found
**Problem**: Query returns no visual content
**Solutions**:
- Check if document was uploaded with visual extraction enabled
- Try more specific keywords (e.g., "table" instead of "data")
- Verify the document actually contains the visual content
- Try rephrasing your query

### Tables Not Displaying Properly
**Problem**: Table appears malformed or incomplete
**Solutions**:
- Check if original table has clear structure
- Try querying specific parts of the table
- Verify table has proper headers and borders
- Consider re-uploading with higher quality document

### Images Not Loading
**Problem**: Images show loading spinner or error
**Solutions**:
- Check internet connection
- Refresh the page
- Verify document was processed successfully
- Contact support if issue persists

### Slow Performance
**Problem**: Queries take too long to return results
**Solutions**:
- Use more specific queries to reduce search scope
- Avoid very broad queries like "show me everything"
- Consider breaking large documents into smaller sections
- Check system resources and network speed

## 📊 Query Examples by Document Type

### Railway Maintenance Manuals
```
"Show me the maintenance schedule table"
"What are the torque specifications?"
"Display the component diagram"
"List all part numbers from the table"
"Show me the wiring schematic"
```

### Technical Specifications
```
"Display the specifications table"
"What are the dimensions of component X?"
"Show me the technical drawing"
"List all materials from the table"
"Display the performance chart"
```

### Safety Documents
```
"Show me the safety checklist table"
"Display the emergency procedure flowchart"
"What are the safety specifications?"
"Show me the hazard identification table"
"Display the safety equipment diagram"
```

### Training Materials
```
"Show me the training schedule table"
"Display the process flowchart"
"What are the learning objectives?"
"Show me the assessment criteria table"
"Display the training diagram"
```

## 🎯 Advanced Features

### Multi-Visual Responses
- RailGPT can display multiple visual elements in a single response
- Tables and images can appear together when relevant
- Tabbed interface for complex visual content

### Source Attribution
- Each visual element shows its source document and page number
- Click source links to view the original document
- Visual content is properly attributed in citations

### Search Integration
- Visual content is indexed for semantic search
- Keywords from tables and image metadata are searchable
- Hybrid search combines text and visual content

### Performance Monitoring
- System tracks visual content extraction success rates
- Performance metrics help optimize processing
- User feedback improves visual content detection

## 📞 Support & Feedback

### Getting Help
- Use the feedback button to report issues with visual content
- Include specific details about what visual content you expected
- Provide document names and query text for faster resolution

### Feature Requests
- Suggest improvements to visual content display
- Request support for additional file formats
- Propose new visual content types

### Best Practices Sharing
- Share successful query patterns with your team
- Document effective visual content preparation methods
- Contribute to the knowledge base with your experiences

---

## 🎉 Start Using Visual Content Today!

1. **Upload** a document with tables, images, or charts
2. **Enable** visual extraction options during upload
3. **Query** using natural language about visual content
4. **Explore** the interactive visual displays
5. **Share** your findings with your team

**Happy querying with RailGPT's enhanced visual capabilities!** 🚂✨ 