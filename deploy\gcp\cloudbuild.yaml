# Cloud Build configuration for RailGPT
# This file defines the CI/CD pipeline for automatic deployment

steps:
  # Step 1: Build and push backend Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-backend'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/railgpt-backend:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/railgpt-backend:latest',
      './backend'
    ]
    waitFor: ['-']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-backend'
    args: [
      'push', 'gcr.io/$PROJECT_ID/railgpt-backend:$COMMIT_SHA'
    ]
    waitFor: ['build-backend']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-backend-latest'
    args: [
      'push', 'gcr.io/$PROJECT_ID/railgpt-backend:latest'
    ]
    waitFor: ['build-backend']

  # Step 2: Deploy backend to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-backend'
    args: [
      'run', 'deploy', 'railgpt-backend',
      '--image', 'gcr.io/$PROJECT_ID/railgpt-backend:$COMMIT_SHA',
      '--region', '${_REGION}',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '2Gi',
      '--cpu', '2',
      '--max-instances', '10',
      '--min-instances', '0',
      '--concurrency', '80',
      '--timeout', '300',
      '--port', '8000',
      '--set-env-vars', 'ENVIRONMENT=production,API_HOST=0.0.0.0,API_PORT=8000,LOG_LEVEL=INFO',
      '--set-env-vars', 'SUPABASE_URL=${_SUPABASE_URL}',
      '--set-env-vars', 'SUPABASE_KEY=${_SUPABASE_KEY}',
      '--set-env-vars', 'SUPABASE_ANON_KEY=${_SUPABASE_ANON_KEY}',
      '--set-env-vars', 'GEMINI_API_KEY=${_GEMINI_API_KEY}'
    ]
    waitFor: ['push-backend']

  # Step 3: Get backend URL for frontend build
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'get-backend-url'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        BACKEND_URL=$(gcloud run services describe railgpt-backend --region=${_REGION} --format="value(status.url)")
        echo "BACKEND_URL=$BACKEND_URL" > /workspace/backend_url.env
        echo "Backend URL: $BACKEND_URL"
    waitFor: ['deploy-backend']

  # Step 4: Build frontend
  - name: 'node:18'
    id: 'build-frontend'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd frontend
        
        # Load backend URL
        source /workspace/backend_url.env
        
        # Set environment variables for build
        export REACT_APP_API_URL=$BACKEND_URL
        export REACT_APP_SUPABASE_URL=${_SUPABASE_URL}
        export REACT_APP_SUPABASE_ANON_KEY=${_SUPABASE_ANON_KEY}
        
        # Install dependencies and build
        npm ci
        npm run build
        
        echo "Frontend build completed"
        echo "API URL: $REACT_APP_API_URL"
    waitFor: ['get-backend-url']

  # Step 5: Deploy frontend to Cloud Storage
  - name: 'gcr.io/cloud-builders/gsutil'
    id: 'deploy-frontend'
    args: [
      '-m', 'rsync', '-r', '-d',
      'frontend/build/',
      'gs://${_FRONTEND_BUCKET}/'
    ]
    waitFor: ['build-frontend']

  # Step 6: Set cache headers for frontend assets
  - name: 'gcr.io/cloud-builders/gsutil'
    id: 'set-cache-headers'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Cache static assets for 1 year
        gsutil -m setmeta -h "Cache-Control:public, max-age=31536000, immutable" gs://${_FRONTEND_BUCKET}/static/**
        
        # Don't cache HTML files
        gsutil -m setmeta -h "Cache-Control:public, max-age=0, must-revalidate" gs://${_FRONTEND_BUCKET}/index.html
        gsutil -m setmeta -h "Cache-Control:public, max-age=0, must-revalidate" gs://${_FRONTEND_BUCKET}/manifest.json
        
        # Set content types
        gsutil -m setmeta -h "Content-Type:text/html" gs://${_FRONTEND_BUCKET}/index.html
        gsutil -m setmeta -h "Content-Type:application/json" gs://${_FRONTEND_BUCKET}/manifest.json
        
        echo "Cache headers set successfully"
    waitFor: ['deploy-frontend']

  # Step 7: Run health checks
  - name: 'gcr.io/cloud-builders/curl'
    id: 'health-check-backend'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        source /workspace/backend_url.env
        echo "Testing backend health at: $BACKEND_URL/health"
        
        # Wait for service to be ready
        sleep 30
        
        # Test health endpoint
        if curl -f "$BACKEND_URL/health"; then
          echo "✅ Backend health check passed"
        else
          echo "❌ Backend health check failed"
          exit 1
        fi
    waitFor: ['deploy-backend']

  # Step 8: Test frontend (if domain is configured)
  - name: 'gcr.io/cloud-builders/curl'
    id: 'health-check-frontend'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [ ! -z "${_DOMAIN}" ]; then
          echo "Testing frontend at: https://${_DOMAIN}"
          if curl -f "https://${_DOMAIN}"; then
            echo "✅ Frontend health check passed"
          else
            echo "⚠️ Frontend health check failed (DNS might still be propagating)"
          fi
        else
          echo "Testing frontend at: https://storage.googleapis.com/${_FRONTEND_BUCKET}/index.html"
          if curl -f "https://storage.googleapis.com/${_FRONTEND_BUCKET}/index.html"; then
            echo "✅ Frontend health check passed"
          else
            echo "❌ Frontend health check failed"
            exit 1
          fi
        fi
    waitFor: ['set-cache-headers']

  # Step 9: Notify deployment completion
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'notify-completion'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        source /workspace/backend_url.env
        
        echo "🎉 Deployment completed successfully!"
        echo "📋 Deployment Summary:"
        echo "  Commit: $COMMIT_SHA"
        echo "  Backend URL: $BACKEND_URL"
        echo "  API Documentation: $BACKEND_URL/docs"
        
        if [ ! -z "${_DOMAIN}" ]; then
          echo "  Frontend URL: https://${_DOMAIN}"
        else
          echo "  Frontend URL: https://storage.googleapis.com/${_FRONTEND_BUCKET}/index.html"
        fi
        
        echo "  Build ID: $BUILD_ID"
        echo "  Build Time: $(date)"
    waitFor: ['health-check-backend', 'health-check-frontend']

# Substitution variables (can be overridden)
substitutions:
  _REGION: 'us-central1'
  _FRONTEND_BUCKET: 'railgpt-frontend-${PROJECT_ID}'
  _SUPABASE_URL: 'https://rkllidjktazafeinezgo.supabase.co'
  _SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA'
  _SUPABASE_KEY: 'your_service_key_here'
  _GEMINI_API_KEY: 'your_gemini_api_key_here'
  _DOMAIN: ''  # Optional: set to your domain name

# Build options
options:
  # Use high-performance machine for faster builds
  machineType: 'E2_HIGHCPU_8'
  
  # Increase disk size for large builds
  diskSizeGb: 100
  
  # Use Cloud Logging for better log management
  logging: CLOUD_LOGGING_ONLY
  
  # Set build timeout (default is 10 minutes)
  timeout: '1200s'

# Build artifacts (optional)
artifacts:
  objects:
    location: 'gs://${PROJECT_ID}-build-artifacts'
    paths:
      - 'frontend/build/**/*'

# Build triggers (for automatic deployment)
# This section is used when setting up triggers via gcloud or Console
# trigger:
#   github:
#     owner: 'your-github-username'
#     name: 'railgpt'
#     push:
#       branch: '^main$'
