{"version": 3, "sources": ["webpack://src/index.css", "webpack://src/App.css", "webpack://src/components/ui/TrainLoader.css"], "names": [], "mappings": "AAAA,cAAc,CACd,oBAAoB,CACpB,mBAAmB,CAEnB,KACE,QAAS,CACT,mJAEY,CACZ,kCAAmC,CACnC,iCAAkC,CAClC,mDACF,CAEA,KACE,yEAEF,CAEA,gBACE,WACF,CAGA,oBACE,SAAU,CACV,UACF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,EACE,gGAAwG,CACxG,kDAAwD,CACxD,wBACF,CC/CA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YACE,wBAAyB,CACzB,gBAAiB,CACjB,YAAa,CACb,qBAAsB,CACtB,kBAAmB,CACnB,sBAAuB,CACvB,4BAA6B,CAC7B,UACF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CCpCA,wBACE,cAAe,CACf,OAAQ,CACR,MAAO,CACP,YAAa,CACb,kBAAmB,CACnB,YAAa,CACb,mBAAoB,CACpB,2CAA4C,CAC5C,kBAAmB,CACnB,qBAAsB,CACtB,+CAEF,CAGA,qCACE,sDACF,CAGA,kBACE,cAAe,CACf,OAAQ,CACR,MAAO,CACP,YAAa,CACb,mBAAoB,CACpB,8BAAgC,CAChC,eAAiB,CACjB,aAAc,CACd,cAAe,CACf,sCAA2C,CAC3C,uCAAwC,CACxC,kBAAmB,CACnB,0BAA2B,CAC3B,kBACF,CAGA,+BACE,kDAAmD,CACnD,kBACF,CAEA,qBACE,GACE,6CAA8C,CAC9C,SACF,CACA,GACE,6CAA8C,CAC9C,SACF,CACA,IACE,0DAA2D,CAC3D,SACF,CACA,GACE,0DAA2D,CAC3D,SACF,CACF,CAGA,gCACE,GACE,6CAA8C,CAC9C,SACF,CACA,IACE,6CAA8C,CAC9C,SACF,CACA,IACE,6CAA8C,CAC9C,SACF,CACA,IACE,0DAA2D,CAC3D,SACF,CACA,IACE,4CAA6C,CAC7C,SACF,CACA,IACE,6CAA8C,CAC9C,SACF,CACA,GACE,6CAA8C,CAC9C,SACF,CACF,CAEA,yBACE,GAAK,iEAAqE,CAC1E,GAAO,mEAAuE,CAChF,CAGA,oCACE,GAAK,iEAAqE,CAC1E,IAAM,+EAAmF,CACzF,IAAM,+EAAmF,CACzF,IAAM,iEAAqE,CAC3E,GAAO,mEAAuE,CAChF,CAGA,cACE,iBAAkB,CAClB,WAAY,CACZ,WAAY,CACZ,kBAAmB,CACnB,8BAA+B,CAC/B,gBAAiB,CACjB,WAAY,CACZ,eACF,CAEA,cAEE,QAAS,CACT,WAAY,CACZ,UAAW,CACX,WAAY,CACZ,iDAAoD,CACpD,yBAA0B,CAC1B,WAEF,CAGA,8BAZE,iBAAkB,CAQlB,eAcF,CAVA,gBAEE,SAAU,CACV,SAAU,CACV,UAAW,CACX,WAAY,CACZ,kBAAmB,CACnB,yBAA0B,CAC1B,WAEF,CAEA,OACE,iBAAkB,CAClB,SAAU,CACV,UAAW,CACX,4BAAiC,CACjC,iBAAkB,CAClB,wCAAyC,CACzC,WAAY,CACZ,eACF,CAEA,SACE,kBAAmB,CACnB,QACF,CACA,SACE,mBAAqB,CACrB,QACF,CACA,SACE,kBAAmB,CACnB,QACF,CACA,SACE,oBAAqB,CACrB,QACF,CAEA,qBACE,GACE,gCAAiC,CACjC,UACF,CACA,IACE,sCAAuC,CACvC,UACF,CACA,GACE,oCAAqC,CACrC,SACF,CACF,CAGA,gBACE,iBAAkB,CAClB,QAAS,CACT,SACF,CAEA,eACE,UAAW,CACX,WAAY,CACZ,iDAAoD,CACpD,wBAAyB,CACzB,iBAAkB,CAClB,eACF,CAEA,cACE,iBAAkB,CAClB,QAAS,CACT,QAAS,CACT,UAAW,CACX,WAAY,CACZ,+CAAqD,CACrD,iBAAkB,CAClB,eAAgB,CAChB,wDACF,CAEA,wBACE,GAAK,SAAY,CACjB,GAAO,UAAc,CACvB,CAGA,aACE,iBAAkB,CAClB,WAAY,CACZ,WAAY,CACZ,kBAAmB,CACnB,iBAAkB,CAClB,gBAAiB,CACjB,WAAY,CACZ,eACF,CAGA,SACE,iBAAkB,CAClB,QAAS,CACT,SACF,CAEA,WACE,UAAW,CACX,WAAY,CACZ,eAAmB,CACnB,qBAAyB,CACzB,iBAAkB,CAClB,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,eACF,CAEA,iBACE,UAAW,CACX,WACF,CAEA,SACE,eAAiB,CACjB,cAAe,CACf,UAAc,CACd,8BAAgC,CAChC,gBACF,CAEA,eACE,aACF,CAGA,eACE,iBAAkB,CAClB,QAAS,CACT,UAAW,CACX,YAAa,CACb,YAAQ,CAAR,OACF,CAEA,QACE,UAAW,CACX,WAAY,CACZ,iDAAoD,CACpD,wBAAyB,CACzB,iBAAkB,CAClB,eAAgB,CAChB,yDACF,CAEA,yBACE,GAAK,iDAAsD,CAC3D,IAAM,iDAAsD,CAC5D,GAAO,iDAAsD,CAC/D,CAGA,eACE,iBAAkB,CAClB,WAAY,CACZ,SACF,CAEA,YACE,UAAW,CACX,WAAY,CACZ,+CAAoD,CACpD,wBAAyB,CACzB,iBAAkB,CAClB,eACF,CAEA,cACE,iBAAkB,CAClB,SAAU,CACV,MAAO,CACP,cAAe,CACf,eAAiB,CACjB,UAAc,CACd,gBAAiB,CACjB,mCACF,CAGA,aACE,iBAAkB,CAClB,WAAY,CACZ,WAAY,CACZ,kDAAqD,CACrD,iBAAkB,CAClB,WAAY,CACZ,eACF,CAEA,YACE,iBAAkB,CAClB,OAAQ,CACR,UACF,CAEA,eACE,iBAAkB,CAClB,QAAS,CACT,SACF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,YAAQ,CAAR,OACF,CAEA,YACE,UAAW,CACX,UAAW,CACX,iDAAoD,CACpD,iBAAkB,CAClB,eACF,CAGA,cACE,iBAAkB,CAClB,YAAa,CACb,MAAO,CACP,OAAQ,CACR,YAAa,CACb,6BAA8B,CAC9B,cACF,CAEA,OACE,UAAW,CACX,WAAY,CACZ,uEAA2E,CAC3E,wBAAyB,CACzB,iBAAkB,CAClB,iBAAkB,CAClB,2CAA6C,CAC7C,eACF,CAEA,yBACE,GAAK,uBAA2B,CAChC,GAAO,sBAAyB,CAClC,CAEA,aACE,iBAAkB,CAClB,OAAQ,CACR,QAAS,CACT,SAAU,CACV,UAAW,CACX,kBAAmB,CACnB,2BAA4B,CAC5B,iBACF,CAEA,yBAA4B,2CAA+C,CAC3E,0BAA4B,4CAAgD,CAC5E,0BAA4B,6CAAiD,CAG7E,yBACE,wBACE,+CAAiD,CACjD,2CAA4C,CAC5C,kBACF,CAEA,qCACE,sDACF,CAEA,kBACE,cAAe,CACf,uCAAwC,CACxC,kBACF,CAEA,+BACE,kDAAmD,CACnD,kBACF,CAEA,yBACE,GAAK,iEAAqE,CAC1E,GAAO,mEAAuE,CAChF,CAEA,oCACE,GAAK,iEAAqE,CAC1E,IAAM,8EAAkF,CACxF,IAAM,+EAAmF,CACzF,IAAM,iEAAqE,CAC3E,GAAO,mEAAuE,CAChF,CAEA,qBACE,GACE,6CAA8C,CAC9C,SACF,CACA,GACE,6CAA8C,CAC9C,SACF,CACA,IACE,0DAA2D,CAC3D,SACF,CACA,GACE,0DAA2D,CAC3D,SACF,CACF,CAEA,gCACE,GACE,6CAA8C,CAC9C,SACF,CACA,IACE,6CAA8C,CAC9C,SACF,CACA,IACE,6CAA8C,CAC9C,SACF,CACA,IACE,0DAA2D,CAC3D,SACF,CACA,IACE,4CAA6C,CAC7C,SACF,CACA,IACE,6CAA8C,CAC9C,SACF,CACA,GACE,6CAA8C,CAC9C,SACF,CACF,CACF,CAEA,yBACE,wBACE,+CAAiD,CACjD,2CAA4C,CAC5C,kBACF,CAEA,qCACE,sDACF,CAEA,kBACE,cAAe,CACf,uCAAwC,CACxC,kBACF,CAEA,+BACE,kDAAmD,CACnD,kBACF,CAEA,yBACE,GAAK,iEAAqE,CAC1E,GAAO,mEAAuE,CAChF,CAEA,oCACE,GAAK,iEAAqE,CAC1E,IAAM,8EAAkF,CACxF,IAAM,+EAAmF,CACzF,IAAM,iEAAqE,CAC3E,GAAO,mEAAuE,CAChF,CAEA,qBACE,GACE,6CAA8C,CAC9C,SACF,CACA,GACE,6CAA8C,CAC9C,SACF,CACA,IACE,0DAA2D,CAC3D,SACF,CACA,GACE,0DAA2D,CAC3D,SACF,CACF,CAEA,gCACE,GACE,6CAA8C,CAC9C,SACF,CACA,IACE,6CAA8C,CAC9C,SACF,CACA,IACE,6CAA8C,CAC9C,SACF,CACA,IACE,0DAA2D,CAC3D,SACF,CACA,IACE,4CAA6C,CAC7C,SACF,CACA,IACE,6CAA8C,CAC9C,SACF,CACA,GACE,6CAA8C,CAC9C,SACF,CACF,CACF,CAGA,0BACE,qBACF,CAGA,uCACE,wDAIE,cACF,CAEA,kBACE,YACF,CAEA,QACE,iDACF,CACF,CAGA,+BASE,wCACE,qBACF,CACF", "file": "main.3b2d5304.chunk.css", "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  transition: background-color 0.3s ease, color 0.3s ease;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\nhtml, body, #root {\n  height: 100%;\n}\n\n/* Custom scrollbar styling */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* Smooth transitions */\n* {\n  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n", ".App {\n  text-align: center;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n", "/* Train Container - Invisible wrapper, no background */\n.train-container-direct {\n  position: fixed;\n  top: 50%;\n  left: 0;\n  display: flex;\n  align-items: center;\n  z-index: 9999;\n  pointer-events: none;\n  animation: trainMovement 15s linear infinite;\n  animation-delay: 0s;\n  will-change: transform;\n  transform: translateY(-50%) scale(0.6) scaleX(-1);\n  /* No background, no border, no shadow, no dimensions */\n}\n\n/* When sidebar is open, use different animation to avoid sidebar area */\n.train-container-direct.sidebar-open {\n  animation: trainMovementSidebarOpen 15s linear infinite;\n}\n\n/* Animated Text Trail */\n.train-text-trail {\n  position: fixed;\n  top: 50%;\n  left: 0;\n  z-index: 9998;\n  pointer-events: none;\n  font-family: 'Arial', sans-serif;\n  font-weight: bold;\n  color: #1E3A8A;\n  font-size: 18px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n  animation: textTrail 20s linear infinite;\n  animation-delay: 5s;\n  transform: translateY(-50%);\n  white-space: nowrap;\n}\n\n/* Text trail when sidebar is open */\n.train-text-trail.sidebar-open {\n  animation: textTrailSidebarOpen 20s linear infinite;\n  animation-delay: 4s;\n}\n\n@keyframes textTrail {\n  0% { \n    transform: translateY(-50%) translateX(-800px);\n    opacity: 0;\n  }\n  5% { \n    transform: translateY(-50%) translateX(-300px);\n    opacity: 1;\n  }\n  90% { \n    transform: translateY(-50%) translateX(calc(100vw - 200px));\n    opacity: 1;\n  }\n  100% { \n    transform: translateY(-50%) translateX(calc(100vw + 200px));\n    opacity: 0;\n  }\n}\n\n/* Text trail animation when sidebar is open */\n@keyframes textTrailSidebarOpen {\n  0% { \n    transform: translateY(-50%) translateX(-800px);\n    opacity: 0;\n  }\n  15% { \n    transform: translateY(-50%) translateX(-400px);\n    opacity: 0;\n  }\n  20% { \n    transform: translateY(-50%) translateX(-200px);\n    opacity: 1;\n  }\n  30% { \n    transform: translateY(-50%) translateX(calc(100vw - 300px));\n    opacity: 1;\n  }\n  75% { \n    transform: translateY(-50%) translateX(400px);\n    opacity: 1;\n  }\n  95% { \n    transform: translateY(-50%) translateX(-200px);\n    opacity: 1;\n  }\n  100% { \n    transform: translateY(-50%) translateX(-800px);\n    opacity: 0;\n  }\n}\n\n@keyframes trainMovement {\n  0% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(200vw); }\n  100% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(-1200px); }\n}\n\n/* Train animation when sidebar is open - avoid sidebar area */\n@keyframes trainMovementSidebarOpen {\n  0% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(200vw); }\n  10% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(calc(100vw - 100px)); }\n  25% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(calc(100vw - 300px)); }\n  75% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(400px); }\n  100% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(-1200px); }\n}\n\n/* Engine Styles */\n.train-engine {\n  position: relative;\n  width: 120px;\n  height: 80px;\n  background: #1E3A8A;\n  border-radius: 8px 15px 4px 4px;\n  margin-right: 5px;\n  border: none;\n  box-shadow: none;\n}\n\n.engine-front {\n  position: absolute;\n  top: 10px;\n  right: -10px;\n  width: 15px;\n  height: 60px;\n  background: linear-gradient(90deg, #2563EB, #1E40AF);\n  border-radius: 0 8px 8px 0;\n  border: none;\n  box-shadow: none;\n}\n\n/* Engine Chimney and Smoke */\n.engine-chimney {\n  position: absolute;\n  top: -15px;\n  left: 20px;\n  width: 12px;\n  height: 20px;\n  background: #374151;\n  border-radius: 2px 2px 0 0;\n  border: none;\n  box-shadow: none;\n}\n\n.smoke {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background: rgba(64, 64, 64, 0.8);\n  border-radius: 50%;\n  animation: smokeRise 2s ease-out infinite;\n  border: none;\n  box-shadow: none;\n}\n\n.smoke-1 { \n  animation-delay: 0s; \n  left: 2px;\n}\n.smoke-2 { \n  animation-delay: 0.5s; \n  left: 6px;\n}\n.smoke-3 { \n  animation-delay: 1s; \n  left: 4px;\n}\n.smoke-4 { \n  animation-delay: 1.5s; \n  left: 8px;\n}\n\n@keyframes smokeRise {\n  0% {\n    transform: translateY(0) scale(1);\n    opacity: 0.8;\n  }\n  50% {\n    transform: translateY(-20px) scale(1.5);\n    opacity: 0.4;\n  }\n  100% {\n    transform: translateY(-40px) scale(2);\n    opacity: 0;\n  }\n}\n\n/* Engine Details */\n.engine-details {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n}\n\n.engine-window {\n  width: 25px;\n  height: 20px;\n  background: linear-gradient(45deg, #87CEEB, #B0E0E6);\n  border: 2px solid #2D3748;\n  border-radius: 3px;\n  box-shadow: none;\n}\n\n.engine-light {\n  position: absolute;\n  top: 25px;\n  left: 5px;\n  width: 15px;\n  height: 15px;\n  background: radial-gradient(circle, #FFF700, #FFD700);\n  border-radius: 50%;\n  box-shadow: none;\n  animation: lightFlicker 2s ease-in-out infinite alternate;\n}\n\n@keyframes lightFlicker {\n  0% { opacity: 1; }\n  100% { opacity: 0.7; }\n}\n\n/* Coach Styles */\n.train-coach {\n  position: relative;\n  width: 140px;\n  height: 85px;\n  background: #DC143C;\n  border-radius: 4px;\n  margin-right: 5px;\n  border: none;\n  box-shadow: none;\n}\n\n/* Indian Railways Logo */\n.ir-logo {\n  position: absolute;\n  top: 10px;\n  left: 10px;\n}\n\n.ir-circle {\n  width: 30px;\n  height: 30px;\n  background: #FFFFFF;\n  border: 2px solid #FF6600;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: none;\n}\n\n.ir-circle.small {\n  width: 20px;\n  height: 20px;\n}\n\n.ir-text {\n  font-weight: bold;\n  font-size: 12px;\n  color: #FF6600;\n  font-family: 'Arial', sans-serif;\n  text-shadow: none;\n}\n\n.ir-text.small {\n  font-size: 8px;\n}\n\n/* Coach Windows */\n.coach-windows {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  display: flex;\n  gap: 8px;\n}\n\n.window {\n  width: 18px;\n  height: 15px;\n  background: linear-gradient(45deg, #87CEEB, #B0E0E6);\n  border: 1px solid #2D3748;\n  border-radius: 2px;\n  box-shadow: none;\n  animation: windowFlicker 3s ease-in-out infinite alternate;\n}\n\n@keyframes windowFlicker {\n  0% { background: linear-gradient(45deg, #87CEEB, #B0E0E6); }\n  50% { background: linear-gradient(45deg, #FFE4B5, #FFF8DC); }\n  100% { background: linear-gradient(45deg, #87CEEB, #B0E0E6); }\n}\n\n/* Coach Details */\n.coach-details {\n  position: absolute;\n  bottom: 35px;\n  left: 15px;\n}\n\n.coach-door {\n  width: 20px;\n  height: 35px;\n  background: linear-gradient(90deg, #8B0000, #A52A2A);\n  border: 1px solid #2D3748;\n  border-radius: 2px;\n  box-shadow: none;\n}\n\n.coach-number {\n  position: absolute;\n  top: -20px;\n  left: 0;\n  font-size: 10px;\n  font-weight: bold;\n  color: #FFFFFF;\n  text-shadow: none;\n  font-family: 'Courier New', monospace;\n}\n\n/* Wagon Styles */\n.train-wagon {\n  position: relative;\n  width: 120px;\n  height: 80px;\n  background: linear-gradient(180deg, #9CA3AF, #6B7280);\n  border-radius: 4px;\n  border: none;\n  box-shadow: none;\n}\n\n.wagon-logo {\n  position: absolute;\n  top: 8px;\n  right: 10px;\n}\n\n.wagon-details {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n}\n\n.cargo-lines {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.cargo-line {\n  width: 80px;\n  height: 3px;\n  background: linear-gradient(90deg, #4B5563, #6B7280);\n  border-radius: 1px;\n  box-shadow: none;\n}\n\n/* Train Wheels */\n.train-wheels {\n  position: absolute;\n  bottom: -20px;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: space-between;\n  padding: 0 15px;\n}\n\n.wheel {\n  width: 35px;\n  height: 35px;\n  background: radial-gradient(circle, #2D3748 30%, #4A5568 70%, #1A202C 100%);\n  border: 3px solid #E2E8F0;\n  border-radius: 50%;\n  position: relative;\n  animation: wheelRotation 0.5s linear infinite;\n  box-shadow: none;\n}\n\n@keyframes wheelRotation {\n  0% { transform: rotate(360deg); }\n  100% { transform: rotate(0deg); }\n}\n\n.wheel-spoke {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 2px;\n  height: 60%;\n  background: #E2E8F0;\n  transform-origin: center top;\n  border-radius: 1px;\n}\n\n.wheel-spoke:nth-child(1) { transform: translate(-50%, -50%) rotate(0deg); }\n.wheel-spoke:nth-child(2) { transform: translate(-50%, -50%) rotate(60deg); }\n.wheel-spoke:nth-child(3) { transform: translate(-50%, -50%) rotate(120deg); }\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .train-container-direct {\n    transform: translateY(-50%) scale(0.5) scaleX(-1);\n    animation: trainMovement 15s linear infinite;\n    animation-delay: 0s;\n  }\n  \n  .train-container-direct.sidebar-open {\n    animation: trainMovementSidebarOpen 15s linear infinite;\n  }\n  \n  .train-text-trail {\n    font-size: 16px;\n    animation: textTrail 20s linear infinite;\n    animation-delay: 5s;\n  }\n  \n  .train-text-trail.sidebar-open {\n    animation: textTrailSidebarOpen 20s linear infinite;\n    animation-delay: 4s;\n  }\n  \n  @keyframes trainMovement {\n    0% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(200vw); }\n    100% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(-1200px); }\n  }\n  \n  @keyframes trainMovementSidebarOpen {\n    0% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(200vw); }\n    10% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(calc(100vw - 80px)); }\n    25% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(calc(100vw - 250px)); }\n    75% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(320px); }\n    100% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(-1200px); }\n  }\n  \n  @keyframes textTrail {\n    0% { \n      transform: translateY(-50%) translateX(-700px);\n      opacity: 0;\n    }\n    5% { \n      transform: translateY(-50%) translateX(-250px);\n      opacity: 1;\n    }\n    90% { \n      transform: translateY(-50%) translateX(calc(100vw - 150px));\n      opacity: 1;\n    }\n    100% { \n      transform: translateY(-50%) translateX(calc(100vw + 150px));\n      opacity: 0;\n    }\n  }\n  \n  @keyframes textTrailSidebarOpen {\n    0% { \n      transform: translateY(-50%) translateX(-700px);\n      opacity: 0;\n    }\n    15% { \n      transform: translateY(-50%) translateX(-350px);\n      opacity: 0;\n    }\n    20% { \n      transform: translateY(-50%) translateX(-150px);\n      opacity: 1;\n    }\n    30% { \n      transform: translateY(-50%) translateX(calc(100vw - 250px));\n      opacity: 1;\n    }\n    75% { \n      transform: translateY(-50%) translateX(320px);\n      opacity: 1;\n    }\n    95% { \n      transform: translateY(-50%) translateX(-150px);\n      opacity: 1;\n    }\n    100% { \n      transform: translateY(-50%) translateX(-700px);\n      opacity: 0;\n    }\n  }\n}\n\n@media (max-width: 480px) {\n  .train-container-direct {\n    transform: translateY(-50%) scale(0.4) scaleX(-1);\n    animation: trainMovement 15s linear infinite;\n    animation-delay: 0s;\n  }\n  \n  .train-container-direct.sidebar-open {\n    animation: trainMovementSidebarOpen 15s linear infinite;\n  }\n  \n  .train-text-trail {\n    font-size: 14px;\n    animation: textTrail 20s linear infinite;\n    animation-delay: 5s;\n  }\n  \n  .train-text-trail.sidebar-open {\n    animation: textTrailSidebarOpen 20s linear infinite;\n    animation-delay: 4s;\n  }\n  \n  @keyframes trainMovement {\n    0% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(200vw); }\n    100% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(-1200px); }\n  }\n  \n  @keyframes trainMovementSidebarOpen {\n    0% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(200vw); }\n    10% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(calc(100vw - 60px)); }\n    25% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(calc(100vw - 200px)); }\n    75% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(280px); }\n    100% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(-1200px); }\n  }\n  \n  @keyframes textTrail {\n    0% { \n      transform: translateY(-50%) translateX(-600px);\n      opacity: 0;\n    }\n    5% { \n      transform: translateY(-50%) translateX(-200px);\n      opacity: 1;\n    }\n    90% { \n      transform: translateY(-50%) translateX(calc(100vw - 100px));\n      opacity: 1;\n    }\n    100% { \n      transform: translateY(-50%) translateX(calc(100vw + 100px));\n      opacity: 0;\n    }\n  }\n  \n  @keyframes textTrailSidebarOpen {\n    0% { \n      transform: translateY(-50%) translateX(-600px);\n      opacity: 0;\n    }\n    15% { \n      transform: translateY(-50%) translateX(-300px);\n      opacity: 0;\n    }\n    20% { \n      transform: translateY(-50%) translateX(-100px);\n      opacity: 1;\n    }\n    30% { \n      transform: translateY(-50%) translateX(calc(100vw - 200px));\n      opacity: 1;\n    }\n    75% { \n      transform: translateY(-50%) translateX(280px);\n      opacity: 1;\n    }\n    95% { \n      transform: translateY(-50%) translateX(-100px);\n      opacity: 1;\n    }\n    100% { \n      transform: translateY(-50%) translateX(-600px);\n      opacity: 0;\n    }\n  }\n}\n\n/* Performance Optimizations */\n.train-container-direct * {\n  will-change: transform;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .train-container-direct,\n  .wheel,\n  .smoke,\n  .train-text-trail {\n    animation: none;\n  }\n  \n  .train-text-trail {\n    display: none;\n  }\n  \n  .window {\n    background: linear-gradient(45deg, #87CEEB, #B0E0E6);\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .train-engine {\n    border: 2px solid #000;\n  }\n  \n  .train-coach {\n    border: 2px solid #000;\n  }\n  \n  .train-wagon {\n    border: 2px solid #000;\n  }\n} "]}