@echo off
echo === IR App Frontend Clean Installation ===
echo.

echo Cleaning npm cache...
call npm cache clean --force

echo.
echo Removing node_modules directory...
if exist node_modules rmdir /s /q node_modules

echo.
echo Removing package-lock.json...
if exist package-lock.json del package-lock.json

echo.
echo Installing dependencies with legacy peer deps...
call npm install --legacy-peer-deps

echo.
echo Installation completed!
echo.
echo To start the frontend server: npm start
echo.
pause
