import os
import logging
import re
import uuid
from typing import List, Dict, Any, Optional
import fitz  # PyMuPDF
import pdfplumber
import pytesseract
from pdf2image import convert_from_path
import docx
import openpyxl
import textract
from PIL import Image
from dotenv import load_dotenv

# Import Supabase client
from supabase_client import supabase

# Import visual content extractor
from visual_extractor import extract_visual_content_from_file, create_visual_content_chunks

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Check if we should use Supabase
USE_SUPABASE = os.getenv("USE_SUPABASE", "true").lower() == "true"

# Tesseract config - update path if needed for Windows
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Text cleanup function
def clean_text(text: str) -> str:
    """Clean and normalize text from document extraction."""
    if not text:
        return ""
    text = re.sub(r'\s+', ' ', text)  # Replace multiple whitespace with single space
    return text.strip()

def extract_from_pdf_fitz(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from PDF using PyMuPDF (fitz)."""
    try:
        logger.info(f"Extracting text from PDF using PyMuPDF: {file_path}")
        result = []

        # Open the PDF with PyMuPDF
        with fitz.open(file_path) as pdf:
            filename = os.path.basename(file_path)

            # Extract text from each page
            for page_num, page in enumerate(pdf):
                text = page.get_text()
                cleaned_text = clean_text(text)

                # Skip empty pages
                if not cleaned_text:
                    logger.warning(f"Empty page {page_num+1} in {filename} using PyMuPDF")
                    continue

                result.append({
                    "filename": filename,
                    "page": page_num + 1,
                    "text": cleaned_text,
                    "extraction_method": "pymupdf"
                })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using PyMuPDF: {str(e)}")
        return []

def extract_from_pdf_pdfplumber(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from PDF using pdfplumber (better layout handling)."""
    try:
        logger.info(f"Extracting text from PDF using pdfplumber: {file_path}")
        result = []

        # Open the PDF with pdfplumber
        with pdfplumber.open(file_path) as pdf:
            filename = os.path.basename(file_path)

            # Extract text from each page
            for page_num, page in enumerate(pdf.pages):
                text = page.extract_text()
                cleaned_text = clean_text(text)

                # Skip empty pages
                if not cleaned_text:
                    logger.warning(f"Empty page {page_num+1} in {filename} using pdfplumber")
                    continue

                result.append({
                    "filename": filename,
                    "page": page_num + 1,
                    "text": cleaned_text,
                    "extraction_method": "pdfplumber"
                })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using pdfplumber: {str(e)}")
        return []

def extract_from_pdf_ocr(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from PDF using OCR (for scanned PDFs)."""
    try:
        logger.info(f"Extracting text from PDF using OCR: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Convert PDF to images
        logger.info(f"Converting PDF to images: {file_path}")
        images = convert_from_path(file_path)

        # Process each page/image
        for page_num, image in enumerate(images):
            logger.info(f"OCR processing page {page_num+1} of {filename}")

            # Perform OCR
            text = pytesseract.image_to_string(image)
            cleaned_text = clean_text(text)

            # Skip empty pages
            if not cleaned_text:
                logger.warning(f"Empty page {page_num+1} in {filename} after OCR")
                continue

            result.append({
                "filename": filename,
                "page": page_num + 1,
                "text": cleaned_text,
                "extraction_method": "ocr"
            })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using OCR: {str(e)}")
        return []

def extract_from_docx_python_docx(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from DOCX using python-docx."""
    try:
        logger.info(f"Extracting text from DOCX using python-docx: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Open the document
        doc = docx.Document(file_path)

        # Extract all paragraphs
        all_paragraphs = [para.text for para in doc.paragraphs if para.text.strip()]

        # Combine paragraphs into a single text
        text = "\n".join(all_paragraphs)
        cleaned_text = clean_text(text)

        if cleaned_text:
            result.append({
                "filename": filename,
                "page": 1,  # DOCX doesn't have pages, so use 1
                "text": cleaned_text,
                "extraction_method": "python-docx"
            })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using python-docx: {str(e)}")
        return []

def extract_from_docx_textract(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from DOCX using textract (fallback)."""
    try:
        logger.info(f"Extracting text from DOCX using textract: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Extract text using textract
        text = textract.process(file_path).decode('utf-8')
        cleaned_text = clean_text(text)

        if cleaned_text:
            result.append({
                "filename": filename,
                "page": 1,  # DOCX doesn't have pages, so use 1
                "text": cleaned_text,
                "extraction_method": "textract"
            })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using textract: {str(e)}")
        return []

def extract_from_xlsx(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from Excel files."""
    try:
        logger.info(f"Extracting text from Excel: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Open the workbook
        workbook = openpyxl.load_workbook(file_path, data_only=True)

        # Process each sheet
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            sheet_text = []

            # Process each row
            for row in sheet.iter_rows(values_only=True):
                # Skip empty rows
                if any(cell is not None and str(cell).strip() for cell in row):
                    row_text = " | ".join([str(cell) if cell is not None else "" for cell in row])
                    sheet_text.append(row_text)

            # Combine all rows for this sheet
            if sheet_text:
                combined_text = f"[Sheet: {sheet_name}]\n" + "\n".join(sheet_text)

                result.append({
                    "filename": filename,
                    "page": sheet_name,  # Use sheet name as page
                    "text": combined_text,
                    "extraction_method": "openpyxl"
                })

        return result

    except Exception as e:
        logger.error(f"Error extracting text from Excel: {str(e)}")
        return []

def extract_from_image(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from image files using OCR."""
    try:
        logger.info(f"Extracting text from image using OCR: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Open the image and perform OCR
        image = Image.open(file_path)
        text = pytesseract.image_to_string(image)
        cleaned_text = clean_text(text)

        if cleaned_text:
            result.append({
                "filename": filename,
                "page": 1,  # Image has one page
                "text": cleaned_text,
                "extraction_method": "image-ocr"
            })

        return result

    except Exception as e:
        logger.error(f"Error extracting text from image: {str(e)}")
        return []

def create_chunks(extracted_items: List[Dict[str, Any]], target_size: int = 400, overlap: int = 100) -> List[Dict[str, Any]]:
    """
    Split extracted text into chunks with metadata, preserving context.
    """
    chunks = []

    for item in extracted_items:
        text = item.get("text", "")
        if not text:
            continue

        # Get metadata from extraction
        filename = item.get("filename", "unknown")
        page = item.get("page", 1)
        extraction_method = item.get("extraction_method", "unknown")

        # Add document title as context prefix
        doc_title = os.path.splitext(filename)[0]
        context_prefix = f"Document: {doc_title}, Page: {page}\n\n"

        # Split text into sentences first
        sentences = re.split(r'(?<=[.!?])\s+', text)

        # Group sentences into chunks
        current_chunk = []
        current_word_count = 0
        chunk_index = 0

        for sentence in sentences:
            # Count words in this sentence
            sentence_words = sentence.split()
            sentence_word_count = len(sentence_words)

            # If adding this sentence would exceed the target size and we already have content,
            # create a chunk and start a new one
            if current_word_count + sentence_word_count > target_size and current_word_count > 0:
                # Create chunk from current sentences
                chunk_text = " ".join(current_chunk)

                # Determine chunk position (start, middle, end)
                position = "start" if chunk_index == 0 else "middle"

                # Create unique chunk ID
                chunk_id = f"{os.path.splitext(filename)[0]}_{page}_{chunk_index}"

                # Add context prefix to the chunk text
                full_chunk_text = context_prefix + chunk_text

                chunks.append({
                    "filename": filename,
                    "page": page,
                    "chunk_id": chunk_id,
                    "text": full_chunk_text,
                    "source_type": "document",
                    "extraction_method": extraction_method,
                    "position": position,
                    "word_count": current_word_count
                })

                # Start a new chunk with overlap
                # Take the last few sentences to maintain context
                overlap_sentences = []
                overlap_word_count = 0

                # Work backwards through current_chunk to get overlap sentences
                for s in reversed(current_chunk):
                    s_words = s.split()
                    if overlap_word_count + len(s_words) <= overlap:
                        overlap_sentences.insert(0, s)
                        overlap_word_count += len(s_words)
                    else:
                        break

                # Reset current chunk with overlap sentences
                current_chunk = overlap_sentences
                current_word_count = overlap_word_count
                chunk_index += 1

            # Add the current sentence to the chunk
            current_chunk.append(sentence)
            current_word_count += sentence_word_count

        # Don't forget the last chunk if there's anything left
        if current_chunk:
            chunk_text = " ".join(current_chunk)

            # Mark as the end chunk
            position = "end" if chunk_index > 0 else "single"

            # Create unique chunk ID
            chunk_id = f"{os.path.splitext(filename)[0]}_{page}_{chunk_index}"

            # Add context prefix to the chunk text
            full_chunk_text = context_prefix + chunk_text

            chunks.append({
                "filename": filename,
                "page": page,
                "chunk_id": chunk_id,
                "text": full_chunk_text,
                "source_type": "document",
                "extraction_method": extraction_method,
                "position": position,
                "word_count": current_word_count
            })

    return chunks

def extract_document(file_path: str, supabase_file_path: str = None, uploaded_by: str = None) -> List[Dict[str, Any]]:
    """
    Extract text from various document formats with fallback mechanisms.
    """
    logger.info(f"Extracting document: {file_path}")

    if not os.path.exists(file_path):
        logger.error(f"File does not exist: {file_path}")
        return []

    # Get file extension and size
    _, ext = os.path.splitext(file_path)
    ext = ext.lower()
    file_size = os.path.getsize(file_path)
    filename = os.path.basename(file_path)

    extracted_items = []

    # Track extraction method for metadata
    extraction_method = "unknown"

    # PDF documents
    if ext == '.pdf':
        # Try PyMuPDF first (fastest and most accurate for text PDFs)
        extracted_items = extract_from_pdf_fitz(file_path)
        if extracted_items and any(item.get("text", "") for item in extracted_items):
            extraction_method = "pymupdf"

        # If PyMuPDF failed to extract text, try pdfplumber
        if not extracted_items or all(not item.get("text", "") for item in extracted_items):
            logger.info(f"PyMuPDF extraction failed or empty, trying pdfplumber for {file_path}")
            extracted_items = extract_from_pdf_pdfplumber(file_path)
            if extracted_items and any(item.get("text", "") for item in extracted_items):
                extraction_method = "pdfplumber"

        # If pdfplumber failed, try OCR
        if not extracted_items or all(not item.get("text", "") for item in extracted_items):
            logger.info(f"Pdfplumber extraction failed or empty, trying OCR for {file_path}")
            extracted_items = extract_from_pdf_ocr(file_path)
            if extracted_items and any(item.get("text", "") for item in extracted_items):
                extraction_method = "ocr"

    # Word documents
    elif ext == '.docx':
        # Try python-docx first
        extracted_items = extract_from_docx_python_docx(file_path)
        if extracted_items and any(item.get("text", "") for item in extracted_items):
            extraction_method = "python-docx"

        # If python-docx failed, try textract
        if not extracted_items or all(not item.get("text", "") for item in extracted_items):
            logger.info(f"python-docx extraction failed or empty, trying textract for {file_path}")
            extracted_items = extract_from_docx_textract(file_path)
            if extracted_items and any(item.get("text", "") for item in extracted_items):
                extraction_method = "textract"

    # Excel files
    elif ext == '.xlsx':
        extracted_items = extract_from_xlsx(file_path)
        if extracted_items and any(item.get("text", "") for item in extracted_items):
            extraction_method = "openpyxl"

    # Image files
    elif ext in ['.jpg', '.jpeg', '.png', '.tiff', '.tif']:
        extracted_items = extract_from_image(file_path)
        if extracted_items and any(item.get("text", "") for item in extracted_items):
            extraction_method = "image-ocr"

    # Unsupported file type
    else:
        logger.warning(f"Unsupported file type: {ext}")
        return []

    if not extracted_items:
        logger.warning(f"Could not extract text from {file_path} using any method")
        return []

    # Create chunks from extracted items
    chunks = create_chunks(extracted_items)

    # Store document in Supabase if enabled
    document_id = None
    if USE_SUPABASE:
        try:
            # Upload file to Supabase Storage if we have a local file path
            supabase_storage_path = None
            if os.path.exists(file_path):
                # Create a storage path based on the file type and name
                storage_path = f"{ext.replace('.', '')}/{filename}"

                # Upload to Supabase Storage
                logger.info(f"Uploading file to Supabase Storage: {storage_path}")
                upload_result = supabase.upload_file("documents", file_path, storage_path)

                if "error" not in upload_result:
                    supabase_storage_path = storage_path
                    logger.info(f"File uploaded to Supabase Storage: {supabase_storage_path}")
                else:
                    logger.warning(f"Error uploading file to Supabase Storage: {upload_result.get('error')}")

            # Create document entry in Supabase
            document_data = {
                "filename": filename,
                "display_name": filename,
                "file_path": supabase_file_path or supabase_storage_path or file_path,
                "file_type": ext.replace('.', ''),
                "file_size": file_size,
                "uploaded_by": uploaded_by,
                "status": "processed",
                "extraction_method": extraction_method
            }

            # Store document metadata in Supabase
            result = supabase.store_document(
                filename=document_data["filename"],
                display_name=document_data["display_name"],
                file_path=document_data["file_path"],
                file_type=document_data["file_type"],
                file_size=document_data["file_size"],
                uploaded_by=document_data["uploaded_by"]
            )

            if "error" in result:
                logger.error(f"Error storing document in Supabase: {result['error']}")
            else:
                document_id = result.get("id")
                logger.info(f"Stored document in Supabase with ID: {document_id}")

                # Add document_id to all chunks
                for chunk in chunks:
                    chunk["document_id"] = document_id
                    chunk["chunk_index"] = chunks.index(chunk)
                    chunk["page_number"] = chunk.get("page", 1)
                    chunk["metadata"] = {
                        "extraction_method": extraction_method,
                        "filename": filename,
                        "file_type": ext.replace('.', ''),
                        "file_size": file_size
                    }

                # Store chunks in Supabase
                for chunk in chunks:
                    chunk_result = supabase.store_document_chunk(
                        document_id=document_id,
                        chunk_index=chunk["chunk_index"],
                        page_number=chunk["page_number"],
                        text=chunk["text"],
                        embedding=chunk.get("embedding", []),
                        metadata=chunk["metadata"],
                        source_type=chunk["source_type"]
                    )
                    if "error" in chunk_result:
                        logger.error(f"Error storing document chunk: {chunk_result['error']}")
                    else:
                        logger.info(f"Stored document chunk with ID: {chunk_result.get('id')}")

        except Exception as e:
            logger.error(f"Error storing document in Supabase: {str(e)}")

    logger.info(f"Extracted {len(chunks)} chunks from {file_path}")
    return chunks

def extract_document_with_visual_content(file_path: str, supabase_file_path: str = None, 
                                       uploaded_by: str = None, extract_tables: bool = True,
                                       extract_images: bool = True, extract_charts: bool = True) -> List[Dict[str, Any]]:
    """
    Extract both text and visual content from documents.
    
    Args:
        file_path: Path to the document file
        supabase_file_path: Path to the file in Supabase Storage (if applicable)
        uploaded_by: User ID of the uploader (if applicable)
        extract_tables: Whether to extract tables
        extract_images: Whether to extract images
        extract_charts: Whether to detect charts/diagrams
    
    Returns:
        List of document chunks including both text and visual content
    """
    logger.info(f"Extracting document with visual content: {file_path}")
    
    # First extract regular text content
    text_chunks = extract_document(file_path, supabase_file_path, uploaded_by)
    
    # Then extract visual content for PDFs
    _, ext = os.path.splitext(file_path)
    if ext.lower() == '.pdf' and (extract_tables or extract_images or extract_charts):
        try:
            logger.info(f"Extracting visual content from PDF: {file_path}")
            visual_content = extract_visual_content_from_file(
                file_path, 
                extract_tables=extract_tables,
                extract_images=extract_images, 
                extract_charts=extract_charts
            )
            
            # Convert visual content to chunks
            visual_chunks = create_visual_content_chunks(visual_content)
            
            # Add document metadata to visual chunks if we have it
            if text_chunks and len(text_chunks) > 0:
                document_id = text_chunks[0].get("document_id")
                if document_id:
                    for chunk in visual_chunks:
                        chunk["document_id"] = document_id
                        chunk["chunk_index"] = len(text_chunks) + visual_chunks.index(chunk)
                        chunk["page_number"] = chunk.get("page", 1)
                        
                        # Store visual chunks in Supabase if enabled
                        if USE_SUPABASE:
                            try:
                                chunk_result = supabase.store_document_chunk(
                                    document_id=document_id,
                                    chunk_index=chunk["chunk_index"],
                                    page_number=chunk["page_number"],
                                    text=chunk["text"],
                                    embedding=chunk.get("embedding", []),
                                    metadata=chunk.get("metadata", {}),
                                    source_type=chunk["source_type"]
                                )
                                if "error" in chunk_result:
                                    logger.error(f"Error storing visual chunk: {chunk_result['error']}")
                                else:
                                    logger.info(f"Stored visual chunk with ID: {chunk_result.get('id')}")
                            except Exception as e:
                                logger.error(f"Error storing visual chunk in Supabase: {str(e)}")
            
            # Combine text and visual chunks
            all_chunks = text_chunks + visual_chunks
            
            logger.info(f"Extracted {len(text_chunks)} text chunks and {len(visual_chunks)} visual chunks")
            return all_chunks
            
        except Exception as e:
            logger.error(f"Error extracting visual content: {str(e)}")
            # Return just text chunks if visual extraction fails
            return text_chunks
    
    # For non-PDF files or when visual extraction is disabled, return just text chunks
    return text_chunks 