#!/usr/bin/env python3
"""
Test file access from current directory
"""

import os
import requests

def test_file_access():
    """Test if we can access files from current directory"""
    print("=== Testing File Access ===")
    
    # Get current working directory
    cwd = os.getcwd()
    print(f"Current working directory: {cwd}")
    
    # Test files
    test_files = [
        "SampleRailwayDoc.pdf",
        "ACP 110V.docx"
    ]
    
    for filename in test_files:
        print(f"\nTesting {filename}:")
        
        # Check if file exists in data/uploads
        file_path = os.path.join("data", "uploads", filename)
        if os.path.exists(file_path):
            print(f"  ✅ File exists at: {file_path}")
            
            # Get file size
            file_size = os.path.getsize(file_path)
            print(f"  File size: {file_size} bytes")
            
            # Test if we can read it
            try:
                with open(file_path, 'rb') as f:
                    first_bytes = f.read(100)
                print(f"  ✅ File is readable (first 100 bytes read)")
            except Exception as e:
                print(f"  ❌ Cannot read file: {str(e)}")
        else:
            print(f"  ❌ File not found at: {file_path}")

def test_server_endpoint():
    """Test the server endpoint"""
    print("\n=== Testing Server Endpoint ===")
    
    try:
        response = requests.get("http://localhost:8000/api/health")
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print(f"❌ Server health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Cannot connect to server: {str(e)}")

if __name__ == "__main__":
    test_file_access()
    test_server_endpoint() 