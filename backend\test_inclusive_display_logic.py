#!/usr/bin/env python3
"""
Test script to verify the inclusive answer display logic implementation.

This script tests that the system correctly implements the new inclusive logic:
1. Document chunks (high priority) - show document card
2. Website chunks (medium priority) - show website card  
3. Both available - show BOTH cards
4. Neither available - show LLM fallback

Run this script to verify the inclusive implementation works correctly.
"""

import requests
import json
import time
import sys

# Configuration
API_URL = "http://localhost:8000"

def test_api_health():
    """Test if the API is available."""
    try:
        response = requests.get(f"{API_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API is healthy and responding")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def send_test_query(query, model="gemini-2.0-flash"):
    """Send a test query to the API."""
    try:
        response = requests.post(
            f"{API_URL}/api/query",
            json={
                "query": query,
                "model": model,
                "fallback_enabled": True,
                "extract_format": "paragraph",
                "use_hybrid_search": True
            },
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Query failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Query error: {e}")
        return None

def analyze_inclusive_response(response, test_name):
    """Analyze the response to check if it follows inclusive display logic."""
    print(f"\n🧪 {test_name}")
    print("=" * 60)
    
    if not response:
        print("❌ No response received")
        return False
    
    # Check response structure
    has_document_answer = bool(response.get('document_answer'))
    has_website_answer = bool(response.get('website_answer'))
    has_document_sources = bool(response.get('document_sources'))
    has_website_sources = bool(response.get('website_sources'))
    llm_fallback = response.get('llm_fallback', False)
    
    print(f"📄 Document answer: {'✅ Available' if has_document_answer else '❌ Not available'}")
    print(f"🌐 Website answer: {'✅ Available' if has_website_answer else '❌ Not available'}")
    print(f"📄 Document sources: {len(response.get('document_sources', []))} sources")
    print(f"🌐 Website sources: {len(response.get('website_sources', []))} sources")
    print(f"🧠 LLM fallback: {'✅ Used' if llm_fallback else '❌ Not used'}")
    
    # Verify inclusive logic
    inclusive_correct = True
    
    # Check if both sources are available (this is the main new feature)
    if has_document_answer and has_website_answer:
        print("🎯 INCLUSIVE DISPLAY: Both document and website answers available")
        if llm_fallback:
            print("❌ ERROR: LLM fallback should be false when sources are available")
            inclusive_correct = False
        else:
            print("✅ CORRECT: Both cards should be displayed in frontend")
    
    # Check document-only scenario
    elif has_document_answer and not has_website_answer:
        print("📄 DOCUMENT ONLY: Document answer available")
        if llm_fallback:
            print("❌ ERROR: LLM fallback should be false when document sources exist")
            inclusive_correct = False
        else:
            print("✅ CORRECT: Only document card should be displayed")
    
    # Check website-only scenario
    elif has_website_answer and not has_document_answer:
        print("🌐 WEBSITE ONLY: Website answer available")
        if llm_fallback:
            print("❌ ERROR: LLM fallback should be false when website sources exist")
            inclusive_correct = False
        else:
            print("✅ CORRECT: Only website card should be displayed")
    
    # Check LLM fallback scenario
    elif llm_fallback and not has_document_answer and not has_website_answer:
        print("🧠 LLM FALLBACK: No relevant chunks found")
        print("✅ CORRECT: Only LLM fallback card should be displayed")
    
    # Check error scenario
    else:
        print("❌ ERROR: Inconsistent response state")
        print(f"   Document answer: {has_document_answer}")
        print(f"   Website answer: {has_website_answer}")
        print(f"   LLM fallback: {llm_fallback}")
        inclusive_correct = False
    
    # Show answer previews
    if has_document_answer:
        doc_preview = response.get('document_answer', '')[:150] + "..." if len(response.get('document_answer', '')) > 150 else response.get('document_answer', '')
        print(f"📄 Document answer preview: {doc_preview}")
    
    if has_website_answer:
        web_preview = response.get('website_answer', '')[:150] + "..." if len(response.get('website_answer', '')) > 150 else response.get('website_answer', '')
        print(f"🌐 Website answer preview: {web_preview}")
    
    if llm_fallback:
        llm_preview = response.get('answer', '')[:150] + "..." if len(response.get('answer', '')) > 150 else response.get('answer', '')
        print(f"🧠 LLM answer preview: {llm_preview}")
    
    return inclusive_correct

def main():
    """Run all inclusive display tests."""
    print("🚀 Testing INCLUSIVE Answer Display Logic Implementation")
    print("=" * 70)
    print("New Requirement: Show BOTH cards when both sources are available")
    print("=" * 70)
    
    # Test 1: Check API health
    if not test_api_health():
        print("❌ Cannot proceed - API is not available")
        sys.exit(1)
    
    # Test 2: Railway operations query (likely to find both documents and websites)
    print("\n🧪 Test 1: Railway operations query (expecting both sources)")
    both_response = send_test_query("What are the safety protocols for Indian Railways?")
    both_test_passed = analyze_inclusive_response(both_response, "Both Sources Available Test")
    
    # Test 3: Document-specific query (should find only documents)
    print("\n🧪 Test 2: Document-specific query (expecting documents only)")
    doc_response = send_test_query("What are the maintenance procedures for railway equipment?")
    doc_test_passed = analyze_inclusive_response(doc_response, "Document Only Test")
    
    # Test 4: Website/news query (should find only websites)
    print("\n🧪 Test 3: Website/news query (expecting websites only)")
    web_response = send_test_query("What are the latest railway announcements and news?")
    web_test_passed = analyze_inclusive_response(web_response, "Website Only Test")
    
    # Test 5: General knowledge query (should use LLM fallback)
    print("\n🧪 Test 4: General knowledge query (expecting LLM fallback)")
    llm_response = send_test_query("What is the capital of France?")
    llm_test_passed = analyze_inclusive_response(llm_response, "LLM Fallback Test")
    
    # Test 6: Railway technical query (could go any way)
    print("\n🧪 Test 5: Railway technical query (testing system behavior)")
    tech_response = send_test_query("How do railway signaling systems work?")
    tech_test_passed = analyze_inclusive_response(tech_response, "Technical Query Test")
    
    # Summary
    print("\n📊 INCLUSIVE DISPLAY TEST SUMMARY")
    print("=" * 50)
    print(f"Both sources test: {'✅ PASSED' if both_test_passed else '❌ FAILED'}")
    print(f"Document only test: {'✅ PASSED' if doc_test_passed else '❌ FAILED'}")
    print(f"Website only test: {'✅ PASSED' if web_test_passed else '❌ FAILED'}")
    print(f"LLM fallback test: {'✅ PASSED' if llm_test_passed else '❌ FAILED'}")
    print(f"Technical query test: {'✅ PASSED' if tech_test_passed else '❌ FAILED'}")
    
    all_tests_passed = all([both_test_passed, doc_test_passed, web_test_passed, llm_test_passed, tech_test_passed])
    
    if all_tests_passed:
        print("\n🎉 ALL TESTS PASSED - Inclusive display logic is working correctly!")
        print("✅ Frontend should now show both document AND website cards when both are available")
        return 0
    else:
        print("\n💥 SOME TESTS FAILED - Inclusive display logic needs attention")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 
"""
Test script to verify the inclusive answer display logic implementation.

This script tests that the system correctly implements the new inclusive logic:
1. Document chunks (high priority) - show document card
2. Website chunks (medium priority) - show website card  
3. Both available - show BOTH cards
4. Neither available - show LLM fallback

Run this script to verify the inclusive implementation works correctly.
"""

import requests
import json
import time
import sys

# Configuration
API_URL = "http://localhost:8000"

def test_api_health():
    """Test if the API is available."""
    try:
        response = requests.get(f"{API_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API is healthy and responding")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def send_test_query(query, model="gemini-2.0-flash"):
    """Send a test query to the API."""
    try:
        response = requests.post(
            f"{API_URL}/api/query",
            json={
                "query": query,
                "model": model,
                "fallback_enabled": True,
                "extract_format": "paragraph",
                "use_hybrid_search": True
            },
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Query failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Query error: {e}")
        return None

def analyze_inclusive_response(response, test_name):
    """Analyze the response to check if it follows inclusive display logic."""
    print(f"\n🧪 {test_name}")
    print("=" * 60)
    
    if not response:
        print("❌ No response received")
        return False
    
    # Check response structure
    has_document_answer = bool(response.get('document_answer'))
    has_website_answer = bool(response.get('website_answer'))
    has_document_sources = bool(response.get('document_sources'))
    has_website_sources = bool(response.get('website_sources'))
    llm_fallback = response.get('llm_fallback', False)
    
    print(f"📄 Document answer: {'✅ Available' if has_document_answer else '❌ Not available'}")
    print(f"🌐 Website answer: {'✅ Available' if has_website_answer else '❌ Not available'}")
    print(f"📄 Document sources: {len(response.get('document_sources', []))} sources")
    print(f"🌐 Website sources: {len(response.get('website_sources', []))} sources")
    print(f"🧠 LLM fallback: {'✅ Used' if llm_fallback else '❌ Not used'}")
    
    # Verify inclusive logic
    inclusive_correct = True
    
    # Check if both sources are available (this is the main new feature)
    if has_document_answer and has_website_answer:
        print("🎯 INCLUSIVE DISPLAY: Both document and website answers available")
        if llm_fallback:
            print("❌ ERROR: LLM fallback should be false when sources are available")
            inclusive_correct = False
        else:
            print("✅ CORRECT: Both cards should be displayed in frontend")
    
    # Check document-only scenario
    elif has_document_answer and not has_website_answer:
        print("📄 DOCUMENT ONLY: Document answer available")
        if llm_fallback:
            print("❌ ERROR: LLM fallback should be false when document sources exist")
            inclusive_correct = False
        else:
            print("✅ CORRECT: Only document card should be displayed")
    
    # Check website-only scenario
    elif has_website_answer and not has_document_answer:
        print("🌐 WEBSITE ONLY: Website answer available")
        if llm_fallback:
            print("❌ ERROR: LLM fallback should be false when website sources exist")
            inclusive_correct = False
        else:
            print("✅ CORRECT: Only website card should be displayed")
    
    # Check LLM fallback scenario
    elif llm_fallback and not has_document_answer and not has_website_answer:
        print("🧠 LLM FALLBACK: No relevant chunks found")
        print("✅ CORRECT: Only LLM fallback card should be displayed")
    
    # Check error scenario
    else:
        print("❌ ERROR: Inconsistent response state")
        print(f"   Document answer: {has_document_answer}")
        print(f"   Website answer: {has_website_answer}")
        print(f"   LLM fallback: {llm_fallback}")
        inclusive_correct = False
    
    # Show answer previews
    if has_document_answer:
        doc_preview = response.get('document_answer', '')[:150] + "..." if len(response.get('document_answer', '')) > 150 else response.get('document_answer', '')
        print(f"📄 Document answer preview: {doc_preview}")
    
    if has_website_answer:
        web_preview = response.get('website_answer', '')[:150] + "..." if len(response.get('website_answer', '')) > 150 else response.get('website_answer', '')
        print(f"🌐 Website answer preview: {web_preview}")
    
    if llm_fallback:
        llm_preview = response.get('answer', '')[:150] + "..." if len(response.get('answer', '')) > 150 else response.get('answer', '')
        print(f"🧠 LLM answer preview: {llm_preview}")
    
    return inclusive_correct

def main():
    """Run all inclusive display tests."""
    print("🚀 Testing INCLUSIVE Answer Display Logic Implementation")
    print("=" * 70)
    print("New Requirement: Show BOTH cards when both sources are available")
    print("=" * 70)
    
    # Test 1: Check API health
    if not test_api_health():
        print("❌ Cannot proceed - API is not available")
        sys.exit(1)
    
    # Test 2: Railway operations query (likely to find both documents and websites)
    print("\n🧪 Test 1: Railway operations query (expecting both sources)")
    both_response = send_test_query("What are the safety protocols for Indian Railways?")
    both_test_passed = analyze_inclusive_response(both_response, "Both Sources Available Test")
    
    # Test 3: Document-specific query (should find only documents)
    print("\n🧪 Test 2: Document-specific query (expecting documents only)")
    doc_response = send_test_query("What are the maintenance procedures for railway equipment?")
    doc_test_passed = analyze_inclusive_response(doc_response, "Document Only Test")
    
    # Test 4: Website/news query (should find only websites)
    print("\n🧪 Test 3: Website/news query (expecting websites only)")
    web_response = send_test_query("What are the latest railway announcements and news?")
    web_test_passed = analyze_inclusive_response(web_response, "Website Only Test")
    
    # Test 5: General knowledge query (should use LLM fallback)
    print("\n🧪 Test 4: General knowledge query (expecting LLM fallback)")
    llm_response = send_test_query("What is the capital of France?")
    llm_test_passed = analyze_inclusive_response(llm_response, "LLM Fallback Test")
    
    # Test 6: Railway technical query (could go any way)
    print("\n🧪 Test 5: Railway technical query (testing system behavior)")
    tech_response = send_test_query("How do railway signaling systems work?")
    tech_test_passed = analyze_inclusive_response(tech_response, "Technical Query Test")
    
    # Summary
    print("\n📊 INCLUSIVE DISPLAY TEST SUMMARY")
    print("=" * 50)
    print(f"Both sources test: {'✅ PASSED' if both_test_passed else '❌ FAILED'}")
    print(f"Document only test: {'✅ PASSED' if doc_test_passed else '❌ FAILED'}")
    print(f"Website only test: {'✅ PASSED' if web_test_passed else '❌ FAILED'}")
    print(f"LLM fallback test: {'✅ PASSED' if llm_test_passed else '❌ FAILED'}")
    print(f"Technical query test: {'✅ PASSED' if tech_test_passed else '❌ FAILED'}")
    
    all_tests_passed = all([both_test_passed, doc_test_passed, web_test_passed, llm_test_passed, tech_test_passed])
    
    if all_tests_passed:
        print("\n🎉 ALL TESTS PASSED - Inclusive display logic is working correctly!")
        print("✅ Frontend should now show both document AND website cards when both are available")
        return 0
    else:
        print("\n💥 SOME TESTS FAILED - Inclusive display logic needs attention")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 