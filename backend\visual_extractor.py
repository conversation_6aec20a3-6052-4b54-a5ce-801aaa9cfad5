"""
Visual Content Extractor for RailGPT
Extracts tables, images, charts, and diagrams from documents
"""

import os
import logging
import base64
import json
from typing import List, Dict, Any, Optional, Tuple
import fitz  # PyMuPDF
import pdfplumber
from PIL import Image
import io

# Configure logging
logger = logging.getLogger(__name__)

def extract_tables_from_pdf_pdfplumber(file_path: str) -> List[Dict[str, Any]]:
    """Extract tables from PDF using pdfplumber."""
    tables = []
    
    try:
        with pdfplumber.open(file_path) as pdf:
            filename = os.path.basename(file_path)
            
            for page_num, page in enumerate(pdf.pages):
                # Extract tables from this page
                page_tables = page.extract_tables()
                
                if page_tables:
                    for table_idx, table in enumerate(page_tables):
                        if table and len(table) > 0:
                            # Convert table to structured format
                            table_data = {
                                "filename": filename,
                                "page": page_num + 1,
                                "table_index": table_idx,
                                "type": "table",
                                "extraction_method": "pdfplumber",
                                "data": table,
                                "text_representation": format_table_as_text(table),
                                "markdown_representation": format_table_as_markdown(table)
                            }
                            tables.append(table_data)
                            
                            logger.info(f"Extracted table {table_idx} from page {page_num + 1} of {filename}")
    
    except Exception as e:
        logger.error(f"Error extracting tables with pdfplumber: {str(e)}")
    
    return tables

def extract_tables_from_pdf_pymupdf(file_path: str) -> List[Dict[str, Any]]:
    """Extract tables from PDF using PyMuPDF."""
    tables = []
    
    try:
        with fitz.open(file_path) as pdf:
            filename = os.path.basename(file_path)
            
            for page_num, page in enumerate(pdf):
                # Find tables on this page
                try:
                    table_finder = page.find_tables()
                    page_tables = table_finder.tables if hasattr(table_finder, 'tables') else []
                    
                    if page_tables:
                        for table_idx, table in enumerate(page_tables):
                            try:
                                # Extract table data
                                table_data_raw = table.extract()
                                
                                if table_data_raw and len(table_data_raw) > 0:
                                    table_data = {
                                        "filename": filename,
                                        "page": page_num + 1,
                                        "table_index": table_idx,
                                        "type": "table",
                                        "extraction_method": "pymupdf",
                                        "data": table_data_raw,
                                        "text_representation": format_table_as_text(table_data_raw),
                                        "markdown_representation": format_table_as_markdown(table_data_raw),
                                        "bbox": table.bbox  # Bounding box coordinates
                                    }
                                    tables.append(table_data)
                                    
                                    logger.info(f"Extracted table {table_idx} from page {page_num + 1} of {filename}")
                            
                            except Exception as e:
                                logger.warning(f"Failed to extract table {table_idx} from page {page_num + 1}: {str(e)}")
                except AttributeError:
                    # find_tables method not available in this version of PyMuPDF
                    logger.warning("PyMuPDF find_tables method not available")
                    break
    
    except Exception as e:
        logger.error(f"Error extracting tables with PyMuPDF: {str(e)}")
    
    return tables

def extract_images_from_pdf(file_path: str, save_images: bool = False) -> List[Dict[str, Any]]:
    """Extract images from PDF using PyMuPDF."""
    images = []
    
    try:
        with fitz.open(file_path) as pdf:
            filename = os.path.basename(file_path)
            
            for page_num, page in enumerate(pdf):
                # Get list of images on this page
                image_list = page.get_images()
                
                for img_idx, img in enumerate(image_list):
                    try:
                        # Get image data
                        xref = img[0]
                        pix = fitz.Pixmap(pdf, xref)
                        
                        # Skip if image is too small (likely decorative)
                        if pix.width < 50 or pix.height < 50:
                            pix = None
                            continue
                        
                        # Convert to PIL Image
                        if pix.n - pix.alpha < 4:  # GRAY or RGB
                            img_data = pix.tobytes("png")
                            pil_image = Image.open(io.BytesIO(img_data))
                            
                            # Create image metadata
                            image_data = {
                                "filename": filename,
                                "page": page_num + 1,
                                "image_index": img_idx,
                                "type": "image",
                                "extraction_method": "pymupdf",
                                "width": pix.width,
                                "height": pix.height,
                                "format": "PNG",
                                "size_bytes": len(img_data)
                            }
                            
                            # Optionally save image file
                            if save_images:
                                image_filename = f"{os.path.splitext(filename)[0]}_page{page_num + 1}_img{img_idx}.png"
                                image_path = os.path.join("extracted_images", image_filename)
                                os.makedirs("extracted_images", exist_ok=True)
                                pil_image.save(image_path)
                                image_data["saved_path"] = image_path
                            
                            # Convert to base64 for storage/display
                            image_data["base64_data"] = base64.b64encode(img_data).decode('utf-8')
                            
                            images.append(image_data)
                            logger.info(f"Extracted image {img_idx} from page {page_num + 1} of {filename}")
                        
                        pix = None  # Clean up
                        
                    except Exception as e:
                        logger.warning(f"Failed to extract image {img_idx} from page {page_num + 1}: {str(e)}")
    
    except Exception as e:
        logger.error(f"Error extracting images: {str(e)}")
    
    return images

def detect_charts_and_diagrams(file_path: str) -> List[Dict[str, Any]]:
    """Detect potential charts and diagrams in PDF."""
    charts = []
    
    try:
        with fitz.open(file_path) as pdf:
            filename = os.path.basename(file_path)
            
            for page_num, page in enumerate(pdf):
                # Get page drawings (vector graphics)
                drawings = page.get_drawings()
                
                if drawings and len(drawings) > 10:  # Likely contains charts/diagrams
                    # Analyze drawing complexity
                    total_paths = len(drawings)
                    
                    # Look for chart-like patterns
                    has_lines = any(d.get('type') == 'l' for d in drawings)
                    has_curves = any(d.get('type') == 'c' for d in drawings)
                    has_rectangles = any(d.get('type') == 're' for d in drawings)
                    
                    if total_paths > 20 and (has_lines or has_curves):
                        chart_data = {
                            "filename": filename,
                            "page": page_num + 1,
                            "type": "chart_diagram",
                            "extraction_method": "pymupdf_drawings",
                            "drawing_count": total_paths,
                            "has_lines": has_lines,
                            "has_curves": has_curves,
                            "has_rectangles": has_rectangles,
                            "confidence": "medium",  # Basic heuristic
                            "description": f"Potential chart/diagram with {total_paths} drawing elements"
                        }
                        charts.append(chart_data)
                        logger.info(f"Detected potential chart/diagram on page {page_num + 1} of {filename}")
    
    except Exception as e:
        logger.error(f"Error detecting charts/diagrams: {str(e)}")
    
    return charts

def format_table_as_text(table_data: List[List[str]]) -> str:
    """Format table data as plain text."""
    if not table_data:
        return ""
    
    text_lines = []
    for row in table_data:
        if row:  # Skip empty rows
            # Clean and join cells
            cleaned_row = [str(cell).strip() if cell else "" for cell in row]
            text_lines.append(" | ".join(cleaned_row))
    
    return "\n".join(text_lines)

def format_table_as_markdown(table_data: List[List[str]]) -> str:
    """Format table data as markdown table."""
    if not table_data or len(table_data) < 2:
        return ""
    
    markdown_lines = []
    
    # Header row
    header = table_data[0]
    if header:
        cleaned_header = [str(cell).strip() if cell else "" for cell in header]
        markdown_lines.append("| " + " | ".join(cleaned_header) + " |")
        
        # Separator row
        separator = ["---"] * len(cleaned_header)
        markdown_lines.append("| " + " | ".join(separator) + " |")
        
        # Data rows
        for row in table_data[1:]:
            if row:
                cleaned_row = [str(cell).strip() if cell else "" for cell in row]
                # Pad row to match header length
                while len(cleaned_row) < len(cleaned_header):
                    cleaned_row.append("")
                markdown_lines.append("| " + " | ".join(cleaned_row[:len(cleaned_header)]) + " |")
    
    return "\n".join(markdown_lines)

def extract_visual_content_from_file(file_path: str, extract_tables: bool = True, 
                                   extract_images: bool = True, 
                                   extract_charts: bool = True,
                                   save_images: bool = False) -> Dict[str, Any]:
    """
    Extract all visual content from a PDF file.
    """
    logger.info(f"Extracting visual content from: {file_path}")
    
    visual_content = {
        "filename": os.path.basename(file_path),
        "file_path": file_path,
        "tables": [],
        "images": [],
        "charts_diagrams": [],
        "extraction_summary": {}
    }
    
    # Extract tables
    if extract_tables:
        logger.info("Extracting tables...")
        
        # Try pdfplumber first (better for tables)
        tables_pdfplumber = extract_tables_from_pdf_pdfplumber(file_path)
        visual_content["tables"].extend(tables_pdfplumber)
        
        # Try PyMuPDF as fallback/supplement
        tables_pymupdf = extract_tables_from_pdf_pymupdf(file_path)
        
        # Merge tables, avoiding duplicates
        for table in tables_pymupdf:
            # Simple duplicate check based on page and position
            is_duplicate = any(
                existing["page"] == table["page"] and 
                existing.get("table_index", 0) == table.get("table_index", 0)
                for existing in visual_content["tables"]
            )
            if not is_duplicate:
                visual_content["tables"].append(table)
    
    # Extract images
    if extract_images:
        logger.info("Extracting images...")
        images = extract_images_from_pdf(file_path, save_images=save_images)
        visual_content["images"].extend(images)
    
    # Detect charts and diagrams
    if extract_charts:
        logger.info("Detecting charts and diagrams...")
        charts = detect_charts_and_diagrams(file_path)
        visual_content["charts_diagrams"].extend(charts)
    
    # Create summary
    visual_content["extraction_summary"] = {
        "total_tables": len(visual_content["tables"]),
        "total_images": len(visual_content["images"]),
        "total_charts_diagrams": len(visual_content["charts_diagrams"]),
        "pages_with_tables": len(set(t["page"] for t in visual_content["tables"])),
        "pages_with_images": len(set(i["page"] for i in visual_content["images"])),
        "pages_with_charts": len(set(c["page"] for c in visual_content["charts_diagrams"]))
    }
    
    logger.info(f"Visual content extraction complete: {visual_content['extraction_summary']}")
    
    return visual_content

# Note: create_visual_content_chunks function moved to document_extractor.py for better integration 