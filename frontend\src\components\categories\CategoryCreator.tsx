import React, { useState, useEffect } from 'react';
import { Plus, X, Trash2, FolderPlus } from 'lucide-react';
import { getCategories, createCategory, deleteCategory } from '../../services/categoryApi';
import { CategoryHierarchy, CategoryCreate } from '../../types/documents';

interface CategoryCreatorProps {
  isOpen: boolean;
  onClose: () => void;
  onCategoryCreated?: (category: any) => void; // Use any to avoid type conflicts
}

const CategoryCreator: React.FC<CategoryCreatorProps> = ({
  isOpen,
  onClose,
  onCategoryCreated
}) => {
  const [categories, setCategories] = useState<CategoryHierarchy[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state for 4-row layout
  const [selectedMainCategory, setSelectedMainCategory] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [selectedMinorCategory, setSelectedMinorCategory] = useState<string>('');

  // New category creation states
  const [newMainCategory, setNewMainCategory] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [newSubCategory, setNewSubCategory] = useState('');
  const [newMinorCategory, setNewMinorCategory] = useState('');

  // Show/hide new category inputs
  const [showNewMainCategory, setShowNewMainCategory] = useState(false);
  const [showNewCategory, setShowNewCategory] = useState(false);
  const [showNewSubCategory, setShowNewSubCategory] = useState(false);
  const [showNewMinorCategory, setShowNewMinorCategory] = useState(false);

  // Description for new categories
  const [description, setDescription] = useState('');

  const categoryTypes: Array<{
    value: 'main_category' | 'category' | 'sub_category' | 'minor_category';
    label: string;
    description: string;
    level: number;
  }> = [
    {
      value: 'main_category',
      label: 'Main Category',
      description: 'Top-level category (e.g., Safety, Operations)',
      level: 0
    },
    {
      value: 'category',
      label: 'Category',
      description: 'Second-level category under Main Category',
      level: 1
    },
    {
      value: 'sub_category',
      label: 'Sub Category',
      description: 'Third-level category under Category',
      level: 2
    },
    {
      value: 'minor_category',
      label: 'Minor Category',
      description: 'Fourth-level category under Sub Category',
      level: 3
    }
  ];

  useEffect(() => {
    if (isOpen) {
      loadCategories();
    }
  }, [isOpen]);

  // Helper functions to get categories by type
  const getMainCategoriesList = () => categories.filter(cat => cat.type === 'main_category');
  const getCategoriesList = (mainCategoryId: string) =>
    categories.filter(cat => cat.type === 'category' && cat.parent_id === mainCategoryId);
  const getSubCategoriesList = (categoryId: string) =>
    categories.filter(cat => cat.type === 'sub_category' && cat.parent_id === categoryId);
  const getMinorCategoriesList = (subCategoryId: string) =>
    categories.filter(cat => cat.type === 'minor_category' && cat.parent_id === subCategoryId);

  // Reset dependent selections when parent changes
  useEffect(() => {
    setSelectedCategory('');
    setSelectedSubCategory('');
    setSelectedMinorCategory('');
    setShowNewCategory(false);
    setShowNewSubCategory(false);
    setShowNewMinorCategory(false);
  }, [selectedMainCategory]);

  useEffect(() => {
    setSelectedSubCategory('');
    setSelectedMinorCategory('');
    setShowNewSubCategory(false);
    setShowNewMinorCategory(false);
  }, [selectedCategory]);

  useEffect(() => {
    setSelectedMinorCategory('');
    setShowNewMinorCategory(false);
  }, [selectedSubCategory]);

  // Get current category path
  const getCurrentPath = () => {
    const parts = [];

    if (selectedMainCategory) {
      const mainCat = categories.find(cat => cat.id === selectedMainCategory);
      if (mainCat) parts.push(mainCat.name);
    }

    if (selectedCategory) {
      const cat = categories.find(cat => cat.id === selectedCategory);
      if (cat) parts.push(cat.name);
    }

    if (selectedSubCategory) {
      const subCat = categories.find(cat => cat.id === selectedSubCategory);
      if (subCat) parts.push(subCat.name);
    }

    if (selectedMinorCategory) {
      const minorCat = categories.find(cat => cat.id === selectedMinorCategory);
      if (minorCat) parts.push(minorCat.name);
    }

    return parts.join(' > ');
  };

  const loadCategories = async () => {
    try {
      setLoading(true);
      const data = await getCategories(); // This is the API call from categoryApi
      setCategories(data);
    } catch (err) {
      setError('Failed to load categories');
      console.error('Error loading categories:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCategory = async (type: 'main_category' | 'category' | 'sub_category' | 'minor_category', name: string, parentId?: string) => {
    if (!name.trim()) {
      setError('Category name is required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const newCategory: CategoryCreate = {
        name: name.trim(),
        type: type,
        parent_id: parentId,
        description: description.trim() || undefined,
        sort_order: categories.length + 1
      };

      const response = await createCategory(newCategory);

      if (response.success) {
        setSuccess(`${type.replace('_', ' ')} "${name}" created successfully!`);

        // Clear the input for the created category
        switch (type) {
          case 'main_category':
            setNewMainCategory('');
            setShowNewMainCategory(false);
            break;
          case 'category':
            setNewCategory('');
            setShowNewCategory(false);
            break;
          case 'sub_category':
            setNewSubCategory('');
            setShowNewSubCategory(false);
            break;
          case 'minor_category':
            setNewMinorCategory('');
            setShowNewMinorCategory(false);
            break;
        }

        setDescription('');

        // Reload categories to show the new one
        await loadCategories();

        // Notify parent component
        if (onCategoryCreated && response.category) {
          onCategoryCreated(response.category);
        }
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create category');
      console.error('Error creating category:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId: string, categoryName: string) => {
    if (!window.confirm(`Are you sure you want to delete the category "${categoryName}"?`)) {
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      await deleteCategory(categoryId);
      setSuccess(`Category "${categoryName}" deleted successfully!`);
      
      // Reload categories
      await loadCategories();
    } catch (err: any) {
      setError(err.message || 'Failed to delete category');
      console.error('Error deleting category:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryList = () => {
    if (categories.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <FolderPlus className="mx-auto h-12 w-12 mb-4 text-gray-300" />
          <p>No categories yet. Create your first category below!</p>
        </div>
      );
    }

    // Group categories by type for better organization
    const groupedCategories = categoryTypes.reduce((acc, type) => {
      acc[type.value] = categories.filter(cat => cat.type === type.value);
      return acc;
    }, {} as Record<string, CategoryHierarchy[]>);

    return (
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 mb-3">Existing Categories:</h4>

        {categoryTypes.map((type) => {
          const categoriesOfType = groupedCategories[type.value] || [];
          if (categoriesOfType.length === 0) return null;

          return (
            <div key={type.value} className="space-y-2">
              <h5 className="text-sm font-medium text-gray-700 flex items-center">
                <span className="bg-gray-100 px-2 py-1 rounded text-xs mr-2">
                  Level {type.level + 1}
                </span>
                {type.label} ({categoriesOfType.length})
              </h5>

              {categoriesOfType.map((category) => (
                <div
                  key={category.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border ml-4"
                  style={{ marginLeft: `${type.level * 16 + 16}px` }}
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{category.name}</span>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">
                        {type.label}
                      </span>
                    </div>
                    {category.description && (
                      <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">Path: {category.full_path}</p>
                  </div>
                  <button
                    onClick={() => handleDeleteCategory(category.id, category.name)}
                    className="ml-3 p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Delete category"
                    disabled={loading}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          );
        })}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Manage Categories
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Category Hierarchy Guide */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-900 mb-2">Category Hierarchy Guide</h3>
            <div className="text-xs text-blue-700 space-y-1">
              <div className="flex items-center">
                <span className="bg-blue-100 px-2 py-1 rounded mr-2">Level 1</span>
                <span><strong>Main Category</strong> - Top level (e.g., Safety, Operations)</span>
              </div>
              <div className="flex items-center">
                <span className="bg-blue-100 px-2 py-1 rounded mr-2">Level 2</span>
                <span><strong>Category</strong> - Under Main Category (e.g., Guidelines, Procedures)</span>
              </div>
              <div className="flex items-center">
                <span className="bg-blue-100 px-2 py-1 rounded mr-2">Level 3</span>
                <span><strong>Sub Category</strong> - Under Category (e.g., Emergency, Routine)</span>
              </div>
              <div className="flex items-center">
                <span className="bg-blue-100 px-2 py-1 rounded mr-2">Level 4</span>
                <span><strong>Minor Category</strong> - Under Sub Category (e.g., Fire Safety, Equipment)</span>
              </div>
            </div>
            <p className="text-xs text-blue-600 mt-2">
              💡 <strong>Tip:</strong> Start with Main Categories, then build your hierarchy step by step.
            </p>
          </div>

          {/* Success/Error Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {success}
            </div>
          )}

          {/* Category List */}
          {renderCategoryList()}

          {/* Current Category Path Display */}
          {getCurrentPath() && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-sm text-green-700">
                <strong>Current Path:</strong> {getCurrentPath()}
              </p>
            </div>
          )}

          {/* 4-Row Category Creation Form */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Create Categories</h3>

            <div className="space-y-6">
              {/* Row 1: Main Category */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-blue-900">
                    <span className="bg-blue-100 px-2 py-1 rounded text-xs mr-2">Level 1</span>
                    Main Category
                  </h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Select Existing Main Category
                    </label>
                    <select
                      value={selectedMainCategory}
                      onChange={(e) => setSelectedMainCategory(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      disabled={loading}
                    >
                      <option value="">Select a main category</option>
                      {getMainCategoriesList().map((cat) => (
                        <option key={cat.id} value={cat.id}>{cat.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Or Create New Main Category
                    </label>
                    <div className="flex space-x-2">
                      {!showNewMainCategory ? (
                        <button
                          type="button"
                          onClick={() => setShowNewMainCategory(true)}
                          className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                          disabled={loading}
                        >
                          <Plus className="h-4 w-4 inline mr-1" />
                          New
                        </button>
                      ) : (
                        <>
                          <input
                            type="text"
                            value={newMainCategory}
                            onChange={(e) => setNewMainCategory(e.target.value)}
                            placeholder="Main category name"
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            disabled={loading}
                          />
                          <button
                            type="button"
                            onClick={() => handleCreateCategory('main_category', newMainCategory)}
                            className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                            disabled={loading || !newMainCategory.trim()}
                          >
                            Create
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setShowNewMainCategory(false);
                              setNewMainCategory('');
                            }}
                            className="px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm"
                            disabled={loading}
                          >
                            Cancel
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Row 2: Category */}
              <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${!selectedMainCategory ? 'opacity-50' : ''}`}>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-green-900">
                    <span className="bg-green-100 px-2 py-1 rounded text-xs mr-2">Level 2</span>
                    Category
                  </h4>
                  {!selectedMainCategory && (
                    <span className="text-xs text-gray-500">Select a Main Category first</span>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Select Existing Category
                    </label>
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      disabled={loading || !selectedMainCategory}
                    >
                      <option value="">Select a category</option>
                      {selectedMainCategory && getCategoriesList(selectedMainCategory).map((cat) => (
                        <option key={cat.id} value={cat.id}>{cat.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Or Create New Category
                    </label>
                    <div className="flex space-x-2">
                      {!showNewCategory ? (
                        <button
                          type="button"
                          onClick={() => setShowNewCategory(true)}
                          className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                          disabled={loading || !selectedMainCategory}
                        >
                          <Plus className="h-4 w-4 inline mr-1" />
                          New
                        </button>
                      ) : (
                        <>
                          <input
                            type="text"
                            value={newCategory}
                            onChange={(e) => setNewCategory(e.target.value)}
                            placeholder="Category name"
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            disabled={loading}
                          />
                          <button
                            type="button"
                            onClick={() => handleCreateCategory('category', newCategory, selectedMainCategory)}
                            className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                            disabled={loading || !newCategory.trim() || !selectedMainCategory}
                          >
                            Create
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setShowNewCategory(false);
                              setNewCategory('');
                            }}
                            className="px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm"
                            disabled={loading}
                          >
                            Cancel
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Row 3: Sub Category */}
              <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${!selectedCategory ? 'opacity-50' : ''}`}>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-yellow-900">
                    <span className="bg-yellow-100 px-2 py-1 rounded text-xs mr-2">Level 3</span>
                    Sub Category
                  </h4>
                  {!selectedCategory && (
                    <span className="text-xs text-gray-500">Select a Category first</span>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Select Existing Sub Category
                    </label>
                    <select
                      value={selectedSubCategory}
                      onChange={(e) => setSelectedSubCategory(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                      disabled={loading || !selectedCategory}
                    >
                      <option value="">Select a sub category</option>
                      {selectedCategory && getSubCategoriesList(selectedCategory).map((cat) => (
                        <option key={cat.id} value={cat.id}>{cat.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Or Create New Sub Category
                    </label>
                    <div className="flex space-x-2">
                      {!showNewSubCategory ? (
                        <button
                          type="button"
                          onClick={() => setShowNewSubCategory(true)}
                          className="px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm"
                          disabled={loading || !selectedCategory}
                        >
                          <Plus className="h-4 w-4 inline mr-1" />
                          New
                        </button>
                      ) : (
                        <>
                          <input
                            type="text"
                            value={newSubCategory}
                            onChange={(e) => setNewSubCategory(e.target.value)}
                            placeholder="Sub category name"
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                            disabled={loading}
                          />
                          <button
                            type="button"
                            onClick={() => handleCreateCategory('sub_category', newSubCategory, selectedCategory)}
                            className="px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm"
                            disabled={loading || !newSubCategory.trim() || !selectedCategory}
                          >
                            Create
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setShowNewSubCategory(false);
                              setNewSubCategory('');
                            }}
                            className="px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm"
                            disabled={loading}
                          >
                            Cancel
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Row 4: Minor Category */}
              <div className={`bg-purple-50 border border-purple-200 rounded-lg p-4 ${!selectedSubCategory ? 'opacity-50' : ''}`}>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-purple-900">
                    <span className="bg-purple-100 px-2 py-1 rounded text-xs mr-2">Level 4</span>
                    Minor Category
                  </h4>
                  {!selectedSubCategory && (
                    <span className="text-xs text-gray-500">Select a Sub Category first</span>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Select Existing Minor Category
                    </label>
                    <select
                      value={selectedMinorCategory}
                      onChange={(e) => setSelectedMinorCategory(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      disabled={loading || !selectedSubCategory}
                    >
                      <option value="">Select a minor category</option>
                      {selectedSubCategory && getMinorCategoriesList(selectedSubCategory).map((cat) => (
                        <option key={cat.id} value={cat.id}>{cat.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Or Create New Minor Category
                    </label>
                    <div className="flex space-x-2">
                      {!showNewMinorCategory ? (
                        <button
                          type="button"
                          onClick={() => setShowNewMinorCategory(true)}
                          className="px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                          disabled={loading || !selectedSubCategory}
                        >
                          <Plus className="h-4 w-4 inline mr-1" />
                          New
                        </button>
                      ) : (
                        <>
                          <input
                            type="text"
                            value={newMinorCategory}
                            onChange={(e) => setNewMinorCategory(e.target.value)}
                            placeholder="Minor category name"
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                            disabled={loading}
                          />
                          <button
                            type="button"
                            onClick={() => handleCreateCategory('minor_category', newMinorCategory, selectedSubCategory)}
                            className="px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                            disabled={loading || !newMinorCategory.trim() || !selectedSubCategory}
                          >
                            Create
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setShowNewMinorCategory(false);
                              setNewMinorCategory('');
                            }}
                            className="px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm"
                            disabled={loading}
                          >
                            Cancel
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Description Field */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description (Optional)
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter description for new categories"
                  disabled={loading}
                />
              </div>

              {/* Close Button */}
              <div className="flex justify-end pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                  disabled={loading}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryCreator;
