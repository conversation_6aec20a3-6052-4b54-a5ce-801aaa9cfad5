import React, { useState, useEffect } from 'react';
import { Plus, X, Trash2, FolderPlus } from 'lucide-react';
import { getCategories, createCategory, deleteCategory } from '../../services/categoryApi';
import { CategoryHierarchy, CategoryCreate } from '../../types/documents';

interface CategoryCreatorProps {
  isOpen: boolean;
  onClose: () => void;
  onCategoryCreated?: (category: any) => void; // Use any to avoid type conflicts
}

const CategoryCreator: React.FC<CategoryCreatorProps> = ({
  isOpen,
  onClose,
  onCategoryCreated
}) => {
  const [categories, setCategories] = useState<CategoryHierarchy[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [categoryName, setCategoryName] = useState('');
  const [categoryType, setCategoryType] = useState<'main_category' | 'category' | 'sub_category' | 'minor_category'>('main_category');
  const [parentId, setParentId] = useState<string>('');
  const [description, setDescription] = useState('');

  const categoryTypes: Array<{ value: 'main_category' | 'category' | 'sub_category' | 'minor_category'; label: string }> = [
    { value: 'main_category', label: 'Main Category' },
    { value: 'category', label: 'Category' },
    { value: 'sub_category', label: 'Sub Category' },
    { value: 'minor_category', label: 'Minor Category' }
  ];

  useEffect(() => {
    if (isOpen) {
      loadCategories();
    }
  }, [isOpen]);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const data = await getCategories();
      setCategories(data);
    } catch (err) {
      setError('Failed to load categories');
      console.error('Error loading categories:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCategory = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!categoryName.trim()) {
      setError('Category name is required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const newCategory: CategoryCreate = {
        name: categoryName.trim(),
        type: categoryType,
        parent_id: parentId || undefined,
        description: description.trim() || undefined,
        sort_order: categories.length + 1
      };

      const response = await createCategory(newCategory);
      
      if (response.success) {
        setSuccess(`Category "${categoryName}" created successfully!`);
        setCategoryName('');
        setDescription('');
        setParentId('');
        
        // Reload categories to show the new one
        await loadCategories();
        
        // Notify parent component
        if (onCategoryCreated && response.category) {
          onCategoryCreated(response.category);
        }
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create category');
      console.error('Error creating category:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId: string, categoryName: string) => {
    if (!window.confirm(`Are you sure you want to delete the category "${categoryName}"?`)) {
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      await deleteCategory(categoryId);
      setSuccess(`Category "${categoryName}" deleted successfully!`);
      
      // Reload categories
      await loadCategories();
    } catch (err: any) {
      setError(err.message || 'Failed to delete category');
      console.error('Error deleting category:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryList = () => {
    if (categories.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <FolderPlus className="mx-auto h-12 w-12 mb-4 text-gray-300" />
          <p>No categories yet. Create your first category below!</p>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        <h4 className="font-medium text-gray-900 mb-3">Existing Categories:</h4>
        {categories.map((category) => (
          <div
            key={category.id}
            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
          >
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-900">{category.name}</span>
                <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">
                  {category.type.replace('_', ' ')}
                </span>
              </div>
              {category.description && (
                <p className="text-sm text-gray-600 mt-1">{category.description}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">Path: {category.full_path}</p>
            </div>
            <button
              onClick={() => handleDeleteCategory(category.id, category.name)}
              className="ml-3 p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              title="Delete category"
              disabled={loading}
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        ))}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Manage Categories
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Success/Error Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}
          
          {success && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {success}
            </div>
          )}

          {/* Category List */}
          {renderCategoryList()}

          {/* Create New Category Form */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Category</h3>
            
            <form onSubmit={handleCreateCategory} className="space-y-4">
              <div>
                <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700 mb-1">
                  Category Name *
                </label>
                <input
                  type="text"
                  id="categoryName"
                  value={categoryName}
                  onChange={(e) => setCategoryName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter category name"
                  required
                  disabled={loading}
                />
              </div>

              <div>
                <label htmlFor="categoryType" className="block text-sm font-medium text-gray-700 mb-1">
                  Category Type
                </label>
                <select
                  id="categoryType"
                  value={categoryType}
                  onChange={(e) => setCategoryType(e.target.value as 'main_category' | 'category' | 'sub_category' | 'minor_category')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={loading}
                >
                  {categoryTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {categories.length > 0 && (
                <div>
                  <label htmlFor="parentId" className="block text-sm font-medium text-gray-700 mb-1">
                    Parent Category (Optional)
                  </label>
                  <select
                    id="parentId"
                    value={parentId}
                    onChange={(e) => setParentId(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={loading}
                  >
                    <option value="">No parent (Top level)</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.full_path}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description (Optional)
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter category description"
                  disabled={loading}
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                  disabled={loading}
                >
                  Close
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors flex items-center space-x-2 disabled:opacity-50"
                  disabled={loading || !categoryName.trim()}
                >
                  <Plus className="h-4 w-4" />
                  <span>{loading ? 'Creating...' : 'Create Category'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryCreator;
