"""
<PERSON><PERSON><PERSON> to create text search functions in the database for RailGPT.
This script will create SQL functions for text search.
"""
import os
import logging
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def create_text_search_functions():
    """Create SQL functions for text search."""
    logger.info("Creating SQL functions for text search...")
    
    # Text search for document chunks
    text_search_doc_function = """
    CREATE OR REPLACE FUNCTION text_search_document_chunks(
        query_text text,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        document_id uuid,
        chunk_index int,
        page_number int,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.embedding,
            dc.metadata,
            dc.created_at,
            dc.updated_at,
            ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text)) AS similarity
        FROM
            document_chunks dc
        WHERE
            to_tsvector('english', dc.text) @@ plainto_tsquery('english', query_text)
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Text search for website chunks
    text_search_web_function = """
    CREATE OR REPLACE FUNCTION text_search_website_chunks(
        query_text text,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        website_id uuid,
        url text,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            wc.id,
            wc.website_id,
            wc.url,
            wc.text,
            wc.embedding,
            wc.metadata,
            wc.created_at,
            wc.updated_at,
            ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', query_text)) AS similarity
        FROM
            website_chunks wc
        WHERE
            to_tsvector('english', wc.text) @@ plainto_tsquery('english', query_text)
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Execute the function creation queries
    functions = [
        ("text_search_document_chunks", text_search_doc_function),
        ("text_search_website_chunks", text_search_web_function)
    ]
    
    for name, query in functions:
        try:
            result = supabase.execute_query(query)
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error creating function {name}: {result['error']}")
            else:
                logger.info(f"Successfully created function {name}")
        except Exception as e:
            logger.error(f"Error creating function {name}: {str(e)}")

def main():
    """Main function to create text search functions."""
    create_text_search_functions()
    
    logger.info("Text search functions created successfully!")

if __name__ == "__main__":
    main()
