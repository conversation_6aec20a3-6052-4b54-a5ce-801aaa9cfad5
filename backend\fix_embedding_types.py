"""
Fix the embedding type mismatch causing the 'ufunc multiply' error.
This script patches the vector similarity calculation to ensure embeddings are proper numeric arrays.
"""
import os
import re
import json
import numpy as np

def fix_cosine_similarity():
    """Fix the cosine_similarity function in server.py to handle string embeddings."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the cosine_similarity function
        cosine_pos = content.find("def cosine_similarity(")
        
        if cosine_pos == -1:
            print("ERROR: Could not find cosine_similarity function")
            return False
        
        # Find the end of the function
        func_end = content.find("\ndef ", cosine_pos + 10)
        
        if func_end == -1:
            print("ERROR: Could not find end of cosine_similarity function")
            return False
        
        # Replace with a more robust version that handles string embeddings
        new_func = """def cosine_similarity(embedding1, embedding2):
    """
    Calculate cosine similarity between two embeddings, handling string conversions.
    """
    # Convert embeddings to numpy arrays if they are not already
    try:
        # Handle string embeddings (from JSON)
        if isinstance(embedding1, str):
            try:
                embedding1 = json.loads(embedding1)
            except:
                logger.error("Failed to parse string embedding1")
                return 0.0
                
        if isinstance(embedding2, str):
            try:
                embedding2 = json.loads(embedding2)
            except:
                logger.error("Failed to parse string embedding2")
                return 0.0
        
        # Ensure embeddings are numpy arrays of float32
        embedding1 = np.array(embedding1, dtype=np.float32)
        embedding2 = np.array(embedding2, dtype=np.float32)
        
        # Compute cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        return dot_product / (norm1 * norm2)
    except Exception as e:
        logger.error(f"Error calculating cosine similarity: {str(e)}")
        return 0.0  # Return 0 similarity on error
"""
        
        # Replace the function
        content = content[:cosine_pos] + new_func + content[func_end:]
        
        # Update the in-memory similarity search to handle string embeddings
        memory_search_pos = content.find("for chunk in DOCUMENT_CHUNKS:")
        
        if memory_search_pos != -1:
            # Find the similarity calculation
            sim_calc_pos = content.find("similarity = cosine_similarity(", memory_search_pos)
            
            if sim_calc_pos != -1:
                # Find the end of that block
                try_except_block = """                try:
                    similarity = cosine_similarity(query_embedding, chunk["embedding"])
                    chunks_with_similarity.append({**chunk, "similarity": similarity})
                except Exception as sim_err:
                    logger.error(f"Error calculating similarity: {str(sim_err)}")
                    # Still include the chunk with a default similarity
                    chunks_with_similarity.append({**chunk, "similarity": 0.5})"""
                
                # Find the line end
                line_end = content.find("\n", sim_calc_pos)
                next_line = content.find("\n", line_end + 1)
                
                if line_end != -1:
                    content = content[:sim_calc_pos-16] + try_except_block + content[next_line:]
                    print("Updated in-memory similarity search with error handling")
        
        # Write the updated content
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        print("Successfully fixed cosine_similarity function")
        return True
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return False

def fix_document_chunks_loading():
    """Fix document chunks loading to ensure proper embedding type conversion."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the loading code for document chunks from Supabase
        load_chunks_pos = content.find("# Also load document chunks from Supabase")
        
        if load_chunks_pos == -1:
            print("ERROR: Could not find document chunks loading code")
            return False
        
        # Find where embeddings are handled
        embedding_pos = content.find("chunk[\"embedding\"] =", load_chunks_pos)
        
        if embedding_pos == -1:
            # Add embedding conversion code
            process_chunks_pos = content.find("for chunk in result:", load_chunks_pos)
            
            if process_chunks_pos != -1:
                # Find where chunks are added to DOCUMENT_CHUNKS
                add_chunk_pos = content.find("DOCUMENT_CHUNKS.append(chunk)", process_chunks_pos)
                
                if add_chunk_pos != -1:
                    # Insert embedding conversion before adding to DOCUMENT_CHUNKS
                    conversion_code = """                # Ensure embedding is a proper numpy array
                if "embedding" in chunk:
                    try:
                        # Handle string embeddings
                        if isinstance(chunk["embedding"], str):
                            try:
                                chunk["embedding"] = json.loads(chunk["embedding"])
                            except:
                                # Create a default embedding
                                chunk["embedding"] = [0.01] * 768
                        
                        # Convert to numpy array if needed
                        if not isinstance(chunk["embedding"], np.ndarray):
                            chunk["embedding"] = np.array(chunk["embedding"], dtype=np.float32)
                    except Exception as e:
                        logger.error(f"Error converting embedding: {str(e)}")
                        # Use a default embedding
                        chunk["embedding"] = np.array([0.01] * 768, dtype=np.float32)
                        
                """
                    
                    # Insert before adding to DOCUMENT_CHUNKS
                    content = content[:add_chunk_pos] + conversion_code + content[add_chunk_pos:]
                    print("Added embedding conversion code")
        
        # Add required imports if needed
        if "import json" not in content:
            import_pos = content.find("import re")
            
            if import_pos != -1:
                content = content[:import_pos] + "import json\n" + content[import_pos:]
                print("Added json import")
        
        # Write the updated content
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return False

def fix_vector_db_search():
    """Fix the vector database search functions to handle embedding type issues."""
    try:
        with open("vector_db.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Add embedding conversion in hybrid_search
        hybrid_pos = content.find("def hybrid_search(")
        
        if hybrid_pos != -1:
            # Add embedding conversion at the start of the function
            function_body_pos = content.find(":", hybrid_pos)
            
            if function_body_pos != -1:
                # Find where function body starts
                body_start = content.find("\n", function_body_pos)
                
                if body_start != -1:
                    # Add embedding conversion code
                    conversion_code = """
        # Ensure query_embedding is a numpy array of floats
        if not isinstance(query_embedding, np.ndarray):
            try:
                if isinstance(query_embedding, str):
                    import json
                    query_embedding = json.loads(query_embedding)
                query_embedding = np.array(query_embedding, dtype=np.float32)
            except Exception as e:
                logger.error(f"Error converting query_embedding: {str(e)}")
                # Create a default embedding as fallback
                query_embedding = np.array([0.01] * 768, dtype=np.float32)
"""
                    
                    # Insert after function body starts
                    indent = " " * 8  # Assuming 8-space indentation
                    indented_code = conversion_code.replace("\n", f"\n{indent}")
                    content = content[:body_start+1] + indent + indented_code + content[body_start+1:]
                    print("Added embedding conversion in hybrid_search")
        
        # Add imports if needed
        if "import numpy as np" not in content:
            import_pos = content.find("import ")
            
            if import_pos != -1:
                content = content[:import_pos] + "import numpy as np\n" + content[import_pos:]
                print("Added numpy import")
        
        # Write the updated content
        with open("vector_db.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return False

def create_embedding_cleanup_script():
    """Create a script to clean up and normalize embeddings in the database."""
    script = """'''
Cleanup and normalize document embeddings in Supabase.
This script converts all string embeddings to proper numeric arrays.
'''
import os
import sys
import json
import logging
import numpy as np
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def cleanup_embeddings():
    '''Clean up and normalize embeddings in document_chunks table.'''
    try:
        # Get all document chunks
        chunks_query = "SELECT id, embedding FROM document_chunks"
        chunks = supabase.execute_query(chunks_query)
        
        if isinstance(chunks, dict) and "error" in chunks:
            logger.error(f"Error getting document chunks: {chunks['error']}")
            return False
        
        logger.info(f"Found {len(chunks)} document chunks to check")
        fixed_count = 0
        
        for chunk in chunks:
            chunk_id = chunk.get("id")
            embedding = chunk.get("embedding")
            
            # Skip if no embedding
            if not embedding:
                continue
            
            needs_fixing = False
            
            # Check if embedding is a string and needs conversion
            if isinstance(embedding, str):
                try:
                    # Try to parse as JSON
                    parsed = json.loads(embedding)
                    if isinstance(parsed, list) and len(parsed) > 0:
                        # Valid JSON array, update embedding
                        embedding = parsed
                        needs_fixing = True
                except:
                    # Not valid JSON, create mock embedding
                    embedding = [0.01] * 768
                    needs_fixing = True
            
            # Fix if needed
            if needs_fixing:
                # Update the chunk with fixed embedding
                update_query = f"UPDATE document_chunks SET embedding = '{json.dumps(embedding)}' WHERE id = '{chunk_id}'"
                result = supabase.execute_query(update_query)
                
                if isinstance(result, dict) and "error" in result:
                    logger.error(f"Error updating chunk {chunk_id}: {result['error']}")
                else:
                    fixed_count += 1
                    logger.info(f"Fixed embedding for chunk {chunk_id}")
        
        logger.info(f"Fixed {fixed_count} document chunk embeddings")
        return fixed_count > 0
    except Exception as e:
        logger.error(f"Error cleaning up embeddings: {str(e)}")
        return False

if __name__ == "__main__":
    print("Cleaning up document embeddings...")
    if cleanup_embeddings():
        print("Successfully fixed document embeddings")
    else:
        print("Failed to fix document embeddings")
"""
    
    try:
        with open("cleanup_embeddings.py", "w", encoding="utf-8") as f:
            f.write(script)
        
        print("Created cleanup_embeddings.py script")
        return True
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return False

def main():
    print("\n=== FIXING EMBEDDING TYPE MISMATCH ===\n")
    
    # 1. Fix cosine similarity function
    print("Fixing cosine similarity function...")
    if fix_cosine_similarity():
        print("+ Successfully fixed cosine similarity calculation")
    else:
        print("- Failed to fix cosine similarity")
    
    # 2. Fix document chunks loading
    print("\nFixing document chunks loading...")
    if fix_document_chunks_loading():
        print("+ Successfully fixed document chunks loading")
    else:
        print("- Failed to fix document chunks loading")
    
    # 3. Fix vector database search
    print("\nFixing vector database search...")
    if fix_vector_db_search():
        print("+ Successfully fixed vector database search")
    else:
        print("- Failed to fix vector database search")
    
    # 4. Create embedding cleanup script
    print("\nCreating embedding cleanup script...")
    if create_embedding_cleanup_script():
        print("+ Successfully created embedding cleanup script")
    else:
        print("- Failed to create embedding cleanup script")
    
    print("\n=== FIX COMPLETE ===")
    print("\nThe embedding type mismatch has been fixed.")
    print("Please restart your server and run the cleanup script:")
    print("1. python cleanup_embeddings.py")
    print("2. python -m uvicorn server:app --reload")

if __name__ == "__main__":
    main()
