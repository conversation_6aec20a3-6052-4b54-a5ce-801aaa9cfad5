#!/usr/bin/env python3
"""
Test script for enhanced logo detection functionality
Tests queries like "VASP Enterprises logo" to ensure proper image retrieval
"""

import requests
import json
import time

def test_logo_detection():
    """Test the enhanced logo detection functionality"""
    print("🔍 Testing Enhanced Logo Detection")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test queries for logo detection
    test_queries = [
        "VASP Enterprises logo",
        "show me VASP Enterprises logo", 
        "VASP logo",
        "logo of VASP Enterprises",
        "display VASP company logo",
        "VASP Enterprises symbol",
        "show me image of VASP logo"
    ]
    
    for query in test_queries:
        print(f"\n📝 Testing query: '{query}'")
        print("-" * 40)
        
        try:
            # Test visual query detection first
            response = requests.post(
                f"{base_url}/api/query",
                json={'query': query, 'model': 'gemini-2.0-flash'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Query successful!")
                print(f"📊 Visual content found: {result.get('visual_content_found', False)}")
                print(f"🎨 Visual content types: {result.get('visual_content_types', [])}")
                print(f"📄 Document sources: {len(result.get('document_sources', []))}")
                print(f"🔄 LLM fallback used: {result.get('llm_fallback', False)}")
                
                # Analyze document sources for logo content
                doc_sources = result.get('document_sources', [])
                logo_sources = []
                
                for i, source in enumerate(doc_sources):
                    print(f"\n--- Document Source {i+1} ---")
                    print(f"Filename: {source.get('filename', 'NOT SET')}")
                    print(f"Content Type: {source.get('content_type', 'text')}")
                    print(f"Page: {source.get('page', 'NOT SET')}")
                    print(f"Storage URL: {'Available' if source.get('storage_url') else 'Not available'}")
                    
                    visual_content = source.get('visual_content', {})
                    if visual_content:
                        print(f"Visual content keys: {list(visual_content.keys())}")
                        
                        # Check for logo-specific metadata
                        detected_companies = visual_content.get('detected_companies', [])
                        ocr_text = visual_content.get('ocr_text', '')
                        is_logo = visual_content.get('is_logo', False)
                        
                        print(f"Detected companies: {detected_companies}")
                        print(f"OCR text: {ocr_text[:100]}..." if ocr_text else "No OCR text")
                        print(f"Marked as logo: {is_logo}")
                        
                        # Check if this could be a VASP logo
                        has_vasp = any('vasp' in company.lower() for company in detected_companies)
                        has_vasp_ocr = 'vasp' in ocr_text.lower()
                        
                        if has_vasp or has_vasp_ocr or is_logo:
                            logo_sources.append(source)
                            print("🎯 POTENTIAL VASP LOGO DETECTED!")
                
                if logo_sources:
                    print(f"\n🏢 Found {len(logo_sources)} potential VASP logo sources!")
                else:
                    print(f"\n❌ No VASP logo sources detected in response")
                    
            else:
                print(f"❌ Query failed with status {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing query: {e}")
        
        print()
        time.sleep(1)  # Small delay between tests

def test_visual_query_detection():
    """Test the visual query detection logic specifically"""
    print("\n🧠 Testing Visual Query Detection Logic")
    print("=" * 40)
    
    # Import the function to test directly
    try:
        import sys
        sys.path.append('backend')
        from server import detect_visual_query
        
        test_queries = [
            "VASP Enterprises logo",
            "show me VASP logo", 
            "logo of VASP Enterprises",
            "what is the price table",  # Non-logo query
            "Project 1 image"  # Different visual query
        ]
        
        for query in test_queries:
            result = detect_visual_query(query)
            print(f"\nQuery: '{query}'")
            print(f"Detection result: {json.dumps(result, indent=2)}")
            
    except ImportError as e:
        print(f"Could not import visual query detection function: {e}")

def test_document_chunks():
    """Check what document chunks are available in the system"""
    print("\n📚 Checking Available Document Chunks")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    try:
        response = requests.get(f"{base_url}/api/debug/document-chunks")
        
        if response.status_code == 200:
            chunks = response.json()
            print(f"Total document chunks: {len(chunks)}")
            
            # Look for image chunks
            image_chunks = [chunk for chunk in chunks if chunk.get('content_type') == 'image']
            print(f"Image chunks: {len(image_chunks)}")
            
            # Look for potential VASP content
            vasp_chunks = []
            for chunk in chunks:
                text = chunk.get('text', '').lower()
                filename = chunk.get('filename', '').lower()
                
                if 'vasp' in text or 'vasp' in filename:
                    vasp_chunks.append(chunk)
            
            print(f"Chunks containing 'VASP': {len(vasp_chunks)}")
            
            if vasp_chunks:
                print("\nVASP-related chunks found:")
                for i, chunk in enumerate(vasp_chunks[:3]):  # Show first 3
                    print(f"{i+1}. File: {chunk.get('filename', 'Unknown')}")
                    print(f"   Type: {chunk.get('content_type', 'text')}")
                    print(f"   Text preview: {chunk.get('text', '')[:100]}...")
                    print()
                    
        else:
            print(f"Failed to get document chunks: {response.status_code}")
            
    except Exception as e:
        print(f"Error checking document chunks: {e}")

def main():
    """Run all logo detection tests"""
    print("🚀 RailGPT Logo Detection Testing")
    print("=" * 50)
    
    # Test visual query detection
    test_visual_query_detection()
    
    # Check available document chunks
    test_document_chunks()
    
    # Test actual queries
    test_logo_detection()
    
    print("\n✅ Logo detection testing completed!")

if __name__ == "__main__":
    main() 