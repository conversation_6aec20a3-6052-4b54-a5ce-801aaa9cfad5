// API endpoints and methods for interacting with the backend

export const API_URL = 'http://localhost:8000';

export interface QueryRequest {
  query: string;
}

export interface Source {
  source_type: string;
  filename?: string;
  name?: string;  // For display name of the document
  page?: number;
  url?: string;
  link?: string;  // For document viewer links
}

export interface QueryResponse {
  answer: string;  // Combined answer
  document_answer?: string;  // Answer from document sources only
  website_answer?: string;  // Answer from website sources only
  sources: Array<Source>;  // All sources
  document_sources?: Array<Source>;  // Document sources only
  website_sources?: Array<Source>;  // Website sources only
}

export interface WebsiteAddRequest {
  url: string;
  submitted_by?: string;
  role?: string;
  follow_links?: boolean;
  extraction_depth?: number;
  extract_images?: boolean;
  extract_tables?: boolean;
  max_pages?: number;
  extractor_type?: string;
  domain_category?: string;
  [key: string]: any; // To allow for future extension options
}

export interface UploadResponse {
  success: boolean;
  message: string;
  chunks_extracted?: number;
  chunks?: Array<any>;
  data?: {
    id: string;
    path: string;
    [key: string]: any;
  };
}

/**
 * Attempts to use the actual backend API but gracefully falls back to a mock response
 * if the backend is not available
 * 
 * @param query - The user's question
 * @returns Promise with the response containing answer and sources
 */
export const sendQuery = async (query: string): Promise<QueryResponse> => {
  console.log('Processing query:', query);
  
  // Define our mock/fallback response
  const mockResponse: QueryResponse = {
    answer: "⚠️ MOCK RESPONSE: The backend server could not be reached. This is a fallback response.",
    document_answer: "⚠️ MOCK: This is a fallback response for document sources. The actual backend is not available.",
    website_answer: "⚠️ MOCK: This is a fallback response for website sources. The actual backend is not available.",
    sources: [
      { source_type: "document", filename: "SampleDoc.pdf", page: 1 },
      { source_type: "document", filename: "SampleDoc.pdf", page: 2 },
      { source_type: "website", url: "https://example.com" }
    ],
    document_sources: [
      { source_type: "document", filename: "SampleDoc.pdf", page: 1, name: "Sample Document (MOCK)" },
      { source_type: "document", filename: "SampleDoc.pdf", page: 2, name: "Sample Document (MOCK)" }
    ],
    website_sources: [
      { source_type: "website", url: "https://example.com", name: "Example Website (MOCK)" }
    ]
  };
  
  // First, try to use the real backend
  try {
    // Check basic connectivity to the server - skip the root check as it may not return 200 OK
    console.log('Attempting to connect to backend at:', API_URL);
    
    // Try different endpoint variations that might work
    const endpoints = [
      '/api/query'
    ];
    
    let lastError: Error | null = null;
    
    try {
      // Just directly try the /api/query endpoint with a longer timeout
      console.log(`Trying endpoint: ${API_URL}/api/query`);
      
      const response = await fetch(`${API_URL}/api/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
        signal: AbortSignal.timeout(10000) // 10 second timeout for slower responses
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('Backend response received:', data);
        return data;
      } else {
        console.error(`API query failed with status: ${response.status} ${response.statusText}`);
        // Print the error response body if possible
        try {
          const errorText = await response.text();
          console.error('Error response:', errorText);
        } catch {}
        throw new Error(`API request failed with status ${response.status}`);
      }
    } catch (error) {
      console.error('Error querying backend:', error);
      
      // Only fall back to mock response as a last resort
      console.warn('Using mock response as fallback');
      return mockResponse;
    }
    
  } catch (error) {
    console.error('Fatal error in API service:', error);
    return mockResponse;
  }
};

/**
 * Upload a document file to the backend
 * 
 * @param file - The file to upload
 * @param uploadedBy - Name of the uploader (optional) 
 * @returns Promise with the normalized upload response
 */
export const uploadDocument = async (file: File, uploadedBy: string = 'default'): Promise<UploadResponse> => {
  console.log('Uploading document:', file.name);
  
  try {
    // Create form data for file upload
    const formData = new FormData();
    formData.append('file', file);
    formData.append('uploaded_by', uploadedBy);
    
    // Send the request to the backend
    const response = await fetch(`${API_URL}/api/upload-document`, {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header - browser will set it with boundary for FormData
    });
    
    if (!response.ok) {
      // Format error response
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      return {
        success: false,
        message: errorData.detail || `Upload failed: ${response.status} ${response.statusText}`,
      };
    }
    
    // Process successful response
    const data = await response.json();
    console.log('Upload response:', data);
    
    // Normalize the response to match our interface
    return {
      success: true,
      message: data.message,
      chunks_extracted: data.chunks_extracted,
      chunks: data.chunks,
      data: {
        id: `doc-${Date.now()}`,
        path: `/documents/${file.name}`,
        originalResponse: data
      }
    };
  } catch (error) {
    console.error('Error uploading document:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred during upload',
    };
  }
};

/**
 * Add a website URL to be scraped and indexed
 * 
 * @param url - The website URL to add
 * @param submittedBy - Name of the submitter (optional)
 * @param extractionOptions - Advanced options for website extraction
 * @returns Promise with the normalized website add response
 */
export const addWebsite = async (
  url: string, 
  submittedBy: string = 'default', 
  extractionOptions?: Record<string, any>
): Promise<UploadResponse> => {
  console.log('Adding website:', url, 'with options:', extractionOptions);
  
  try {
    const request: WebsiteAddRequest = {
      url,
      submitted_by: submittedBy,
      ...extractionOptions
    };
    
    // Send the request to the backend
    const response = await fetch(`${API_URL}/api/add-website`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      // Format error response
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      return {
        success: false,
        message: errorData.detail || `Website add failed: ${response.status} ${response.statusText}`,
      };
    }
    
    // Process successful response
    const data = await response.json();
    console.log('Website add response:', data);
    
    // Normalize the response to match our interface
    return {
      success: true,
      message: data.message,
      chunks_extracted: data.chunks_extracted,
      chunks: data.chunks,
      data: {
        id: `web-${Date.now()}`,
        path: url,
        originalResponse: data
      }
    };
  } catch (error) {
    console.error('Error adding website:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred while adding website',
    };
  }
};

/**
 * Get document extraction details
 * 
 * @param documentId - The ID of the document
 * @returns Promise with the extraction details
 */
export const getDocumentExtractionDetails = async (documentId: string): Promise<any> => {
  console.log('Getting extraction details for document:', documentId);
  
  try {
    const response = await fetch(`${API_URL}/api/documents/${documentId}/extraction-details`);
    
    if (!response.ok) {
      console.error(`Failed to get document extraction details: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get document extraction details: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting document extraction details:', error);
    
    // Return fallback data
    return {
      extractedContent: `Sample extracted content for ${documentId}. This is placeholder text because the actual content could not be retrieved from the server.`,
      extractionMethod: 'Unknown (error occurred)',
      qualityScore: 0,
      processingTime: 0,
      chunks: 0,
      warnings: ['Failed to retrieve extraction details from server'],
      fallbackReason: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Get document content
 * 
 * @param documentId - The ID of the document
 * @returns Promise with the document content
 */
export const getDocumentContent = async (documentId: string): Promise<any> => {
  console.log('Getting content for document:', documentId);
  
  try {
    const response = await fetch(`${API_URL}/api/documents/${documentId}/content`);
    
    if (!response.ok) {
      console.error(`Failed to get document content: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get document content: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting document content:', error);
    
    // Return fallback data
    return {
      content: `# Document Content Unavailable\n\nThe content for document ${documentId} could not be retrieved from the server.\n\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\n\nPlease try again later or contact support if the problem persists.`,
      extraction_method: 'Unknown (error occurred)',
      quality_score: 0,
      processing_time: 0,
      chunks_count: 0
    };
  }
};

/**
 * Get website extraction details
 * 
 * @param websiteId - The ID of the website
 * @returns Promise with the extraction details
 */
export const getWebsiteExtractionDetails = async (websiteId: string): Promise<any> => {
  console.log('Getting extraction details for website:', websiteId);
  
  try {
    const response = await fetch(`${API_URL}/api/websites/${websiteId}/extraction-details`);
    
    if (!response.ok) {
      console.error(`Failed to get website extraction details: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get website extraction details: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting website extraction details:', error);
    
    // Return fallback data
    return {
      extractedContent: `Sample extracted content for ${websiteId}. This is placeholder text because the actual content could not be retrieved from the server.`,
      extractionMethod: 'Unknown (error occurred)',
      fallbackHistory: [],
      contentQuality: 0,
      warnings: ['Failed to retrieve extraction details from server'],
      processingTime: 0,
      chunks: 0
    };
  }
};

/**
 * Get website content
 * 
 * @param websiteId - The ID of the website
 * @returns Promise with the website content
 */
export const getWebsiteContent = async (websiteId: string): Promise<any> => {
  console.log('Getting content for website:', websiteId);
  
  try {
    const response = await fetch(`${API_URL}/api/websites/${websiteId}/content`);
    
    if (!response.ok) {
      console.error(`Failed to get website content: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get website content: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting website content:', error);
    
    // Return fallback data
    return {
      content: `# Website Content Unavailable\n\nThe content for website ${websiteId} could not be retrieved from the server.\n\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\n\nPlease try again later or contact support if the problem persists.`,
      extraction_method: 'Unknown (error occurred)',
      quality_score: 0,
      processing_time: 0,
      pages_processed: 0,
      total_links: 0
    };
  }
};
