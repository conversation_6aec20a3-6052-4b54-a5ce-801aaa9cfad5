import React, { useState, useEffect } from 'react';
import { getFeedbackEmails, updateFeedbackEmails } from '../../services/api';

const FeedbackEmailConfig: React.FC = () => {
  const [emails, setEmails] = useState<string[]>([]);
  const [newEmail, setNewEmail] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [updateStatus, setUpdateStatus] = useState<{success: boolean; message: string} | null>(null);

  // Fetch configured emails on component mount
  useEffect(() => {
    const loadEmails = async () => {
      try {
        const response = await getFeedbackEmails();
        setEmails(response.emails || []);
      } catch (error) {
        console.error('Error loading feedback emails:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadEmails();
  }, []);

  const handleAddEmail = () => {
    if (!newEmail || !newEmail.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      setUpdateStatus({
        success: false,
        message: 'Please enter a valid email address.'
      });
      return;
    }

    if (emails.includes(newEmail)) {
      setUpdateStatus({
        success: false,
        message: 'This email is already in the list.'
      });
      return;
    }

    if (emails.length >= 5) {
      setUpdateStatus({
        success: false,
        message: 'Maximum 5 email addresses allowed.'
      });
      return;
    }

    const updatedEmails = [...emails, newEmail];
    handleUpdateEmails(updatedEmails);
    setNewEmail('');
  };

  const handleRemoveEmail = (email: string) => {
    const updatedEmails = emails.filter(e => e !== email);
    handleUpdateEmails(updatedEmails);
  };

  const handleUpdateEmails = async (updatedEmails: string[]) => {
    setIsLoading(true);
    try {
      const response = await updateFeedbackEmails(updatedEmails);
      if (response.success) {
        setEmails(updatedEmails);
        setUpdateStatus({
          success: true,
          message: 'Feedback notification emails updated successfully.'
        });
      } else {
        setUpdateStatus({
          success: false,
          message: response.message || 'Failed to update emails.'
        });
      }
    } catch (error) {
      setUpdateStatus({
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsLoading(false);
      // Clear status message after 3 seconds
      setTimeout(() => setUpdateStatus(null), 3000);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 mb-6 transition-colors duration-300">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Feedback Notification Settings</h3>

      <p className="text-gray-600 mb-4">
        Configure email addresses to receive notifications when users provide negative feedback.
      </p>

      {updateStatus && (
        <div className={`p-3 mb-4 rounded-md transition-colors duration-300 ${
          updateStatus.success
            ? 'bg-green-50 text-green-700 border border-green-200'
            : 'bg-red-50 text-red-700 border border-red-200'
        }`}>
          {updateStatus.message}
        </div>
      )}

      <div className="mb-4">
        <h4 className="font-medium text-gray-700 mb-2">Current Notification Emails</h4>
        {isLoading ? (
          <div className="text-gray-500">Loading...</div>
        ) : emails.length === 0 ? (
          <div className="text-gray-500">No notification emails configured.</div>
        ) : (
          <ul className="space-y-2">
            {emails.map((email, index) => (
              <li key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded transition-colors duration-300">
                <span className="text-gray-900">{email}</span>
                <button
                  onClick={() => handleRemoveEmail(email)}
                  className="text-red-600 hover:text-red-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 rounded"
                  title="Remove email"
                  disabled={isLoading}
                >
                  ✕
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className="mt-4">
        <h4 className="font-medium text-gray-700 mb-2">Add New Email</h4>
        <div className="flex">
          <input
            type="email"
            value={newEmail}
            onChange={(e) => setNewEmail(e.target.value)}
            placeholder="<EMAIL>"
            className="flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300"
            disabled={isLoading || emails.length >= 5}
          />
          <button
            onClick={handleAddEmail}
            className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            disabled={isLoading || !newEmail || emails.length >= 5}
          >
            Add
          </button>
        </div>
        {emails.length >= 5 && (
          <p className="text-xs text-amber-600 mt-1">Maximum 5 email addresses allowed.</p>
        )}
        <p className="text-xs text-gray-500 mt-2">
          These emails will receive notifications when users provide negative feedback on AI answers.
        </p>
      </div>
    </div>
  );
};

export default FeedbackEmailConfig;
