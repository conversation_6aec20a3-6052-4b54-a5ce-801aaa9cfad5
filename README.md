# RailGPT Application

A web application for retrieving and displaying railway information.

## Image Display Fix

If you're experiencing issues with images always showing the same logo/image:

1. **Clear browser cache:** Press Ctrl+Shift+Delete in your browser and clear cache
2. **Use incognito mode:** Open the application in an incognito/private window
3. **Check browser console:** Press F12 to open developer tools and check for errors

The application has been updated to provide better error handling and debug information for image loading issues.

## Running the Application

### Windows (PowerShell)

1. Run the start script:
   ```
   .\start.ps1
   ```

2. If you get a security error, you may need to change the execution policy:
   ```
   Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
   .\start.ps1
   ```

3. Alternatively, use the following commands:
   ```
   cd frontend
   $env:NODE_OPTIONS="--openssl-legacy-provider"
   npm start
   ```

### Manual Setup

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies (if not already installed):
   ```
   npm install
   ```

3. Start the application:
   ```
   npm start
   ```

4. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

## Troubleshooting

- **Same image always appears:** This is usually a caching issue. Clear your browser cache or use incognito mode.
- **No images appear:** Check browser console (F12) for errors. Make sure the backend is running.
- **"&&" error in PowerShell:** PowerShell uses semicolons (;) not && for command chaining. Use `cd frontend; npm start` 