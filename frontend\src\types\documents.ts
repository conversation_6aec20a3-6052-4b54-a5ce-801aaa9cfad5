// Document types for RailGPT
export interface Document {
  id: string;
  name: string;
  uploadedAt: string; // ISO date string
  mainCategory: string;
  category: string;
  subCategory?: string;
  minorCategory?: string;
  uploadedBy: string;
  qualityScore?: number; // 0-100
  status: 'Pending' | 'Extracted' | 'Manual Review';
  filePath: string;
  fileType: string; // pdf, docx, xlsx, etc.
  fileSize: number; // bytes
  extractedContent?: string; // Extracted text content
  chunks?: any[]; // Array of text chunks with metadata
  chunks_extracted?: number; // Number of chunks extracted
}

export interface DocumentExtractionDetails {
  extractionMethod: string; // pymupdf, pdfminer, OCR, etc.
  qualityScore: number;
  warnings: string[];
  fallbackReason?: string;
  extractedContent: string;
  processingTime: number; // ms
  chunks: number; // Number of chunks created
}

export interface DocumentCategory {
  id: string;
  name: string;
  type: 'Main' | 'Category' | 'Sub' | 'Minor';
  parentId?: string; // For hierarchical categories
}

// Enhanced category interfaces for the new category management system
export interface Category {
  id: string;
  name: string;
  type: 'main_category' | 'category' | 'sub_category' | 'minor_category';
  parent_id?: string;
  description?: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface CategoryHierarchy extends Category {
  full_path: string;
  level: number;
  children?: CategoryHierarchy[];
}

export interface CategoryCreate {
  name: string;
  type: 'main_category' | 'category' | 'sub_category' | 'minor_category';
  parent_id?: string;
  description?: string;
  sort_order?: number;
}

export interface CategoryUpdate {
  name?: string;
  description?: string;
  sort_order?: number;
  is_active?: boolean;
}

export interface DocumentCategoryUpdate {
  main_category?: string;
  category?: string;
  sub_category?: string;
  minor_category?: string;
}

export type ExtractionTool = 'PyMuPDF' | 'PDFPlumber' | 'Tesseract OCR' | 'Textract' | 'DocX Parser';
