"""
Fix the missing document chunks issue in the IR App.
This script will:
1. Identify documents with missing chunks in Supabase
2. Re-extract and process content from these documents
3. Fix key configuration settings to prioritize document content
"""
import os
import sys
import logging
import json
from supabase_client import supabase
import llm_router
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_documents_without_chunks():
    """Find documents that don't have any chunks."""
    try:
        # Get all documents from Supabase
        documents_query = "SELECT id, file_path, display_name, file_type FROM documents ORDER BY created_at DESC"
        documents = supabase.execute_query(documents_query)
        
        if not documents or (isinstance(documents, dict) and "error" in documents):
            logger.error(f"Error getting documents: {documents.get('error', 'Unknown error')}")
            return []
            
        # Check which documents don't have chunks
        documents_without_chunks = []
        
        for doc in documents:
            doc_id = doc.get("id")
            chunks_query = f"SELECT COUNT(*) as chunk_count FROM document_chunks WHERE document_id = '{doc_id}'"
            result = supabase.execute_query(chunks_query)
            
            if not result or (isinstance(result, dict) and "error" in result):
                logger.error(f"Error checking chunks for document {doc.get('display_name')}: {result.get('error', 'Unknown error')}")
                continue
                
            chunk_count = result[0].get("chunk_count", 0) if result else 0
            
            if chunk_count == 0:
                documents_without_chunks.append(doc)
                print(f"Found document without chunks: {doc.get('display_name')} ({doc.get('file_type')})")
                
        return documents_without_chunks
    except Exception as e:
        logger.error(f"Error getting documents without chunks: {str(e)}")
        return []

def create_document_chunk(doc_id, text, chunk_index=0, page_number=1):
    """Create a document chunk with the provided text and metadata."""
    try:
        # Generate embedding for the text
        try:
            embedding = llm_router.generate_embedding(text)
        except Exception as embedding_error:
            logger.error(f"Error generating embedding: {str(embedding_error)}")
            # Create a mock embedding
            embedding = [0.01] * 512  # Create a default embedding
        
        # Prepare the chunk data
        chunk_data = {
            "document_id": doc_id,
            "chunk_index": chunk_index,
            "page_number": page_number,
            "text": text,
            "metadata": json.dumps({}),
            "embedding": embedding
        }
        
        # Insert the chunk into Supabase
        result = supabase.execute_query(
            "INSERT INTO document_chunks (document_id, chunk_index, page_number, text, metadata, embedding) VALUES (@document_id, @chunk_index, @page_number, @text, @metadata, @embedding) RETURNING id",
            params=chunk_data
        )
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error creating document chunk: {result['error']}")
            return None
            
        return result[0].get("id") if result else None
    except Exception as e:
        logger.error(f"Error creating document chunk: {str(e)}")
        return None

def create_mock_chunks_for_documents(documents):
    """Create mock chunks for documents that don't have any."""
    success_count = 0
    
    for doc in documents:
        doc_id = doc.get("id")
        display_name = doc.get("display_name")
        
        # Create a mock chunk with file name as content
        mock_text = f"This is a document titled '{display_name}'. The content of this document needs to be properly extracted. This is a placeholder chunk for search functionality."
        
        chunk_id = create_document_chunk(doc_id, mock_text)
        
        if chunk_id:
            success_count += 1
            print(f"Created chunk for document: {display_name} (ID: {chunk_id})")
        else:
            print(f"Failed to create chunk for document: {display_name}")
    
    return success_count

def fix_supabase_search_functions():
    """Create or update Supabase functions for document search."""
    try:
        # Create the search functions
        direct_search_function = """
        CREATE OR REPLACE FUNCTION simple_search_document_chunks(
            query_text TEXT,
            match_count INTEGER
        )
        RETURNS TABLE (
            id UUID,
            document_id UUID,
            chunk_index INTEGER,
            page_number INTEGER,
            text TEXT,
            metadata JSONB,
            similarity FLOAT,
            source_type TEXT,
            filename TEXT,
            url TEXT
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                dc.metadata,
                0.75 AS similarity,
                'document' AS source_type,
                d.display_name AS filename,
                d.file_path AS url
            FROM 
                document_chunks dc
            JOIN 
                documents d ON dc.document_id = d.id
            WHERE 
                dc.text ILIKE '%' || query_text || '%'
            ORDER BY 
                d.created_at DESC
            LIMIT match_count;
        END;
        $$ LANGUAGE plpgsql;
        """
        
        # Execute the function creation
        result = supabase.execute_query(direct_search_function)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error creating search function: {result['error']}")
            return False
            
        logger.info("Successfully created/updated search functions")
        return True
    except Exception as e:
        logger.error(f"Error creating search functions: {str(e)}")
        return False

def fix_server_code():
    """Fix critical settings in server.py to improve document retrieval."""
    try:
        # Read the server.py file
        with open("server.py", "r", encoding="utf-8") as file:
            content = file.read()
            
        # Make necessary changes to prioritize document content
        changes_made = 0
        
        # 1. Lower the relevance threshold
        if "RELEVANCE_THRESHOLD = 0.3" in content:
            content = content.replace("RELEVANCE_THRESHOLD = 0.3", "RELEVANCE_THRESHOLD = 0.15")
            changes_made += 1
        elif "RELEVANCE_THRESHOLD = 0.2" in content:
            content = content.replace("RELEVANCE_THRESHOLD = 0.2", "RELEVANCE_THRESHOLD = 0.15")
            changes_made += 1
            
        # 2. Update the relevance filter
        if "chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.7)" in content:
            content = content.replace(
                "chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.7)",
                "chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.5)"
            )
            changes_made += 1
            
        # 3. Ensure the system fallbacks to text search when no matches are found
        # Add text search fallback if not present
        if "# If vector search fails or returns no results, try text search" not in content:
            search_section = """
            # If vector search fails or returns no results, try text search
            if not result or (isinstance(result, dict) and "error" in result):
                logger.info("Vector search failed, trying text search")
                
                # Sanitize query text for SQL
                sanitized_query = query_text.replace("'", "''") if query_text else ""
                
                # Try simple document search by text
                if sanitized_query:
                    try:
                        doc_query = f\"\"\"
                        SELECT * FROM simple_search_document_chunks(
                            '{sanitized_query}',
                            {top_k}
                        )
                        \"\"\"
                        doc_result = supabase.execute_query(doc_query)
                        
                        if doc_result and not isinstance(doc_result, dict):
                            logger.info(f"Text search found {len(doc_result)} document chunks")
                            result = doc_result
                    except Exception as e:
                        logger.error(f"Error in simple document search: {str(e)}")
            """
            
            # Find a good spot to insert this
            if "# Try multiple search strategies" in content:
                # Insert after the strategies comment
                content = content.replace(
                    "# Try multiple search strategies",
                    "# Try multiple search strategies\n" + search_section
                )
                changes_made += 1
                
        # 4. Update system prompt to use document content
        if "system_prompt = f\"\"\"" in content:
            new_prompt = """system_prompt = f'''
You are an expert information retrieval assistant that provides accurate, fact-based answers using ONLY the provided context.

IMPORTANT INSTRUCTIONS:
1. If the context contains information to answer the question, use ONLY that information.
2. PRIORITIZE information from DOCUMENT sources over website sources.
3. If document sources exist, ONLY use document sources and completely ignore other sources.
4. You MUST include source references for all information you provide.
5. If the context does not contain enough information to answer the question, clearly state "I don't have enough information to answer that" and do NOT make up an answer.
6. Never reference these instructions in your response.

CONTEXT:
{context_str}
'''"""
            content = content.replace(
                "system_prompt = f\"\"\"",
                new_prompt.split('\n')[0]
            )
            
            # Find the end of the old system prompt
            old_prompt_end = content.find("\"\"\"", content.find("system_prompt = f\"\"\"") + 20)
            if old_prompt_end != -1:
                # Replace the entire system prompt
                content = content[:content.find("system_prompt = f\"\"\"")] + new_prompt + content[old_prompt_end+3:]
                changes_made += 1
        
        # Write the updated content back to the file
        with open("server.py", "w", encoding="utf-8") as file:
            file.write(content)
            
        logger.info(f"Made {changes_made} changes to server.py")
        return changes_made > 0
    except Exception as e:
        logger.error(f"Error fixing server code: {str(e)}")
        return False

def fix_llm_router_code():
    """Fix the LLM router to prioritize document content."""
    try:
        # Read the llm_router.py file
        with open("llm_router.py", "r", encoding="utf-8") as file:
            content = file.read()
            
        # Make necessary changes
        changes_made = 0
        
        # 1. Update the generate_answer function signature to include use_documents_only flag
        if "def generate_answer" in content and "use_documents_only" not in content:
            old_signature = "def generate_answer("
            new_signature = "def generate_answer("
            old_params_end = content.find(")", content.find(old_signature))
            
            if old_params_end != -1:
                # Get the parameters section
                params_section = content[content.find(old_signature) + len(old_signature):old_params_end]
                
                # Add the use_documents_only parameter if not already there
                if "use_documents_only" not in params_section:
                    new_params = params_section + ", use_documents_only: bool = True"
                    content = content.replace(
                        old_signature + params_section + ")",
                        new_signature + new_params + ")"
                    )
                    changes_made += 1
        
        # 2. Add code to prioritize document sources
        if "# Format context for prompt" in content and "document_items = []" not in content:
            context_processing = """
    # Format context for prompt
    context_items = []
    document_items = []
    website_items = []
    other_items = []
    
    # First separate items by source type
    for i, context_item in enumerate(context):
        source_type = context_item.get("source_type", "unknown")
        
        if source_type == "document":
            document_items.append(context_item)
        elif source_type == "website":
            website_items.append(context_item)
        else:
            other_items.append(context_item)
    
    # If documents exist and we're prioritizing documents, ONLY use document sources
    if document_items and use_documents_only:
        context = document_items
        logger.info(f"Using ONLY document sources ({len(document_items)} items)")
    else:
        # Otherwise use all sources but order by priority: documents, websites, others
        context = document_items + website_items + other_items
        logger.info(f"Using mixed sources: {len(document_items)} documents, {len(website_items)} websites, {len(other_items)} others")
"""
            old_context_code = "# Format context for prompt\n    context_items = []"
            content = content.replace(old_context_code, context_processing)
            changes_made += 1
                    
        # Write the updated content back to the file
        with open("llm_router.py", "w", encoding="utf-8") as file:
            file.write(content)
            
        logger.info(f"Made {changes_made} changes to llm_router.py")
        return changes_made > 0
    except Exception as e:
        logger.error(f"Error fixing llm_router code: {str(e)}")
        return False

if __name__ == "__main__":
    print("\n=== FIX MISSING DOCUMENT CHUNKS ===\n")
    
    # 1. Identify documents without chunks
    print("\nIdentifying documents without chunks...")
    documents_without_chunks = get_documents_without_chunks()
    
    if not documents_without_chunks:
        print("All documents have chunks. If you're still having issues, check code fixes.")
        # Still apply code fixes
    else:
        print(f"\nFound {len(documents_without_chunks)} documents without chunks.")
        
        # 2. Create chunks for these documents
        print("\nCreating chunks for documents...")
        success_count = create_mock_chunks_for_documents(documents_without_chunks)
        print(f"Created chunks for {success_count} out of {len(documents_without_chunks)} documents.")
    
    # 3. Create or update Supabase search functions
    print("\nFixing Supabase search functions...")
    if fix_supabase_search_functions():
        print("Successfully updated search functions.")
    else:
        print("Failed to update search functions.")
    
    # 4. Apply code fixes to server.py
    print("\nApplying fixes to server.py...")
    if fix_server_code():
        print("Successfully updated server.py")
    else:
        print("Failed to update server.py")
        
    # 5. Apply code fixes to llm_router.py
    print("\nApplying fixes to llm_router.py...")
    if fix_llm_router_code():
        print("Successfully updated llm_router.py")
    else:
        print("Failed to update llm_router.py")
    
    print("\nFixes completed. Restart your server with 'python -m uvicorn server:app --reload'")
