"""
Fix for website chunks not appearing in Supabase despite successful addition.

This script:
1. Checks the website_chunks table structure
2. Ensures the source_type column exists
3. Fixes the add_website endpoint to properly store chunks in Supabase
4. Provides a utility to manually add website chunks for existing websites
"""

import os
import sys
import logging
import json
import uuid
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import required modules
try:
    from supabase_client import supabase
    from website_scraper import extract_website_text
    from embeddings import get_embedding
except ImportError as e:
    logger.error(f"Error importing required modules: {str(e)}")
    logger.info("Make sure you're running this script from the backend directory")
    sys.exit(1)

def check_website_chunks_table():
    """Check if the website_chunks table has the correct structure."""
    try:
        # Check if the source_type column exists
        query = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'website_chunks' 
        AND column_name = 'source_type'
        """
        
        result = supabase.execute_query(query)
        
        if not result or len(result) == 0:
            logger.warning("source_type column missing from website_chunks table")
            
            # Add the source_type column
            alter_query = """
            ALTER TABLE website_chunks 
            ADD COLUMN IF NOT EXISTS source_type TEXT DEFAULT 'website'
            """
            
            alter_result = supabase.execute_sql(alter_query)
            logger.info("Added source_type column to website_chunks table")
        else:
            logger.info("website_chunks table structure is correct")
            
        return True
    except Exception as e:
        logger.error(f"Error checking website_chunks table: {str(e)}")
        return False

def fix_website_chunks_for_website(url: str) -> bool:
    """
    Fix website chunks for a specific website URL.
    
    Args:
        url: The website URL to fix
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Parse URL to get domain
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        
        logger.info(f"Fixing website chunks for URL: {url}")
        
        # Check if website exists in Supabase
        query = f"""
        SELECT id, url, domain, title 
        FROM websites 
        WHERE url = '{url}'
        """
        
        website_result = supabase.execute_query(query)
        
        if not website_result or len(website_result) == 0:
            logger.error(f"Website not found in database: {url}")
            return False
            
        website_id = website_result[0].get("id")
        logger.info(f"Found website with ID: {website_id}")
        
        # Check if website has chunks
        chunks_query = f"""
        SELECT COUNT(*) as count
        FROM website_chunks
        WHERE website_id = '{website_id}'
        """
        
        chunks_result = supabase.execute_query(chunks_query)
        chunks_count = chunks_result[0].get("count", 0) if chunks_result else 0
        
        logger.info(f"Website has {chunks_count} chunks in database")
        
        # If chunks exist, delete them first
        if chunks_count > 0:
            delete_query = f"""
            DELETE FROM website_chunks
            WHERE website_id = '{website_id}'
            """
            
            supabase.execute_query(delete_query)
            logger.info(f"Deleted {chunks_count} existing chunks for website")
        
        # Extract website content
        website_chunks = extract_website_text(url)
        
        if not website_chunks:
            logger.error(f"Failed to extract content from website: {url}")
            return False
            
        logger.info(f"Extracted {len(website_chunks)} chunks from website")
        
        # Store each chunk in Supabase
        successful_chunks = 0
        
        for i, chunk in enumerate(website_chunks):
            # Ensure chunk has text
            if not chunk.get("text"):
                logger.warning(f"Chunk {i} has no text, skipping")
                continue
                
            # Get or generate embedding
            embedding = chunk.get("embedding")
            if not embedding:
                logger.info(f"Generating embedding for chunk {i}")
                embedding = get_embedding(chunk["text"])
                
            # Store chunk in Supabase
            chunk_data = {
                "id": str(uuid.uuid4()),
                "website_id": website_id,
                "chunk_index": i,
                "text": chunk["text"],
                "embedding": json.dumps(embedding) if isinstance(embedding, list) else embedding,
                "metadata": json.dumps(chunk.get("metadata", {})),
                "source_type": "website"
            }
            
            # Use direct REST API call for better control
            response = supabase.supabase.table("website_chunks").insert(chunk_data).execute()
            
            if hasattr(response, 'data') and response.data:
                successful_chunks += 1
                logger.info(f"Successfully stored chunk {i}")
            else:
                logger.error(f"Failed to store chunk {i}: {response}")
                
        logger.info(f"Successfully stored {successful_chunks} out of {len(website_chunks)} chunks")
        
        return successful_chunks > 0
        
    except Exception as e:
        logger.error(f"Error fixing website chunks: {str(e)}")
        return False

def fix_all_websites():
    """Fix chunks for all websites in the database."""
    try:
        # Get all websites
        query = "SELECT id, url, domain, title FROM websites"
        websites = supabase.execute_query(query)
        
        if not websites:
            logger.info("No websites found in database")
            return
            
        logger.info(f"Found {len(websites)} websites in database")
        
        # Process each website
        successful = 0
        
        for website in websites:
            url = website.get("url")
            if url:
                result = fix_website_chunks_for_website(url)
                if result:
                    successful += 1
                    
        logger.info(f"Successfully fixed {successful} out of {len(websites)} websites")
        
    except Exception as e:
        logger.error(f"Error fixing all websites: {str(e)}")

def main():
    """Main function to run the fix."""
    logger.info("Starting website chunks fix")
    
    # Check table structure
    check_website_chunks_table()
    
    # Process command line arguments
    if len(sys.argv) > 1:
        url = sys.argv[1]
        logger.info(f"Fixing website chunks for URL: {url}")
        fix_website_chunks_for_website(url)
    else:
        logger.info("Fixing chunks for all websites")
        fix_all_websites()
        
    logger.info("Website chunks fix completed")

if __name__ == "__main__":
    main()
