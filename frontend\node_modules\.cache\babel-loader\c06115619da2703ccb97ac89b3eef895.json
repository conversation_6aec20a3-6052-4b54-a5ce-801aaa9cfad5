{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import'./App.css';import{sendQuery}from'./services/api';import LLMSelector,{DEFAULT_LLM_MODELS}from'./components/ui/LLMSelector';import InteractiveAnswer from'./components/ui/InteractiveAnswer';import ChatSidebar from'./components/chat/ChatSidebar';import TrainLoader from'./components/ui/TrainLoader';import VisualContent from'./components/ui/VisualContent';import{useChatContext}from'./contexts/ChatContext';// Using ChatMessage interface from services/supabase.ts\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ChatInterface(_ref){var _DEFAULT_LLM_MODELS$f;let{sidebarOpen,setSidebarOpen}=_ref;const{currentSession,messages,createNewChat,loadChatSession,addMessage,updateMessage,clearCurrentChat}=useChatContext();const[input,setInput]=useState('');const[isSubmitting,setIsSubmitting]=useState(false);const[activeLLMModel,setActiveLLMModel]=useState('gemini-2.0-flash');// Default LLM model\nconst[showTrainLoader,setShowTrainLoader]=useState(false);const[currentSearchStage,setCurrentSearchStage]=useState('initializing');const messagesEndRef=useRef(null);// Scroll to bottom of chat whenever messages change\nuseEffect(()=>{if(messagesEndRef.current){messagesEndRef.current.scrollIntoView({behavior:'smooth'});}},[messages]);// Helper function to determine if visual content should be shown\nconst shouldShowVisualContent=(userQuestion,documentAnswer)=>{console.log('🔍 DEBUG shouldShowVisualContent called with:',{userQuestion,documentAnswerLength:documentAnswer===null||documentAnswer===void 0?void 0:documentAnswer.length});const userQuery=userQuestion.toLowerCase();// TEMPORARY FIX: Always show visual content when user asks for images\nif(userQuery.includes('image')){console.log('🔍 DEBUG: ALWAYS showing visual content because query contains \"image\"');return true;}// Detect different types of requests\nconst askedForImages=userQuery.includes('image')||userQuery.includes('picture')||userQuery.includes('diagram')||userQuery.includes('chart');const askedForTableData=(userQuery.includes('table')||userQuery.includes('quotation'))&&(userQuery.includes('give me')||userQuery.includes('show me table')||userQuery.includes('get')||userQuery.includes('provide'));const askedToShowImages=userQuery.includes('show me image')||userQuery.includes('display image')||userQuery.includes('see image');const answerLower=(documentAnswer===null||documentAnswer===void 0?void 0:documentAnswer.toLowerCase())||'';const hasTableInAnswer=answerLower.includes('<table>')||answerLower.includes('|')||answerLower.includes('table')&&answerLower.length>200;const hasImageDescription=answerLower.includes('image')||answerLower.includes('figure')||answerLower.includes('diagram');const answerHasContent=answerLower.length>50;// ALWAYS show visual content if user specifically asks to see images/visuals\nif(askedToShowImages||askedForImages&&(userQuery.includes('show')||userQuery.includes('display'))){console.log('🔍 DEBUG: Visual content shown because user explicitly asked for images');return true;}// NEVER show if user asked for table data (they want the actual data, not images)\nif(askedForTableData&&!askedForImages){console.log('🔍 DEBUG: Visual content hidden because user asked for table data');return false;}// For other cases, show only if answer doesn't already provide adequate information\nconst shouldShow=!hasTableInAnswer&&// Don't show if answer already has table data\n!hasImageDescription&&(// Don't show if answer already describes images well\naskedForImages&&!answerHasContent||// Show images if user asked and answer is short\n!answerHasContent&&answerLower.length<50// Show for very short answers\n);console.log('🔍 DEBUG: Visual content decision for regular query:',shouldShow);return shouldShow;};// Helper function to filter visual content types based on user query\nconst filterVisualContent=(source,userQuestion)=>{if(typeof source!=='object'||!source.visual_content){console.log('🔍 DEBUG: Filtering out - not object or no visual_content:',typeof source,!!(source!==null&&source!==void 0&&source.visual_content));return false;}const userQueryLower=userQuestion.toLowerCase();const sourceObj=source;console.log('🔍 DEBUG: Checking visual content filter for:',{content_type:sourceObj.content_type,has_visual_content:!!sourceObj.visual_content,user_query:userQueryLower,filename:sourceObj.filename,page:sourceObj.page,visual_content_sample:sourceObj.visual_content?JSON.stringify(sourceObj.visual_content).substring(0,200):'none'});// Extract specific project/quotation numbers from user query\nconst projectMatch=userQueryLower.match(/project\\s*(\\d+)/);const quotationMatch=userQueryLower.match(/quotation\\s*(\\d+)/);// Extract company names for logo queries\nconst logoQueryPatterns=[/(?:logo\\s+(?:of\\s+)?|show\\s+me\\s+(?:the\\s+)?logo\\s+(?:of\\s+)?)([A-Z][A-Za-z\\s&]+?)(?:\\s+logo|\\s+enterprises|\\s+company|\\s+corp|\\s+ltd|\\s+inc|\\s*$)/i,/([A-Z][A-Za-z\\s&]+?)\\s+(?:enterprises|company|corp|ltd|inc)\\s+logo/i,/([A-Z][A-Za-z\\s&]+?)\\s+logo/i];let companyMatch=null;for(const pattern of logoQueryPatterns){const match=userQuestion.match(pattern);if(match){companyMatch=match[1].trim();break;}}// Check if the visual content contains relevant information for the specific request\nconst visualContentStr=JSON.stringify(sourceObj.visual_content||{}).toLowerCase();// If user asked for a specific company logo\nif(companyMatch&&userQueryLower.includes('logo')){var _sourceObj$visual_con,_sourceObj$visual_con2,_sourceObj$visual_con3,_sourceObj$visual_con4;const companyNameLower=companyMatch.toLowerCase();const isImage=sourceObj.content_type==='image';console.log('🔍 DEBUG: Company logo search:',{company:companyMatch,isImage,has_detected_companies:!!((_sourceObj$visual_con=sourceObj.visual_content)!==null&&_sourceObj$visual_con!==void 0&&_sourceObj$visual_con.detected_companies),detected_companies:((_sourceObj$visual_con2=sourceObj.visual_content)===null||_sourceObj$visual_con2===void 0?void 0:_sourceObj$visual_con2.detected_companies)||[],ocr_text:((_sourceObj$visual_con3=sourceObj.visual_content)===null||_sourceObj$visual_con3===void 0?void 0:_sourceObj$visual_con3.ocr_text)||'',is_logo:((_sourceObj$visual_con4=sourceObj.visual_content)===null||_sourceObj$visual_con4===void 0?void 0:_sourceObj$visual_con4.is_logo)||false});if(isImage){var _sourceObj$visual_con5,_sourceObj$visual_con6,_sourceObj$visual_con7;// Check if this image contains the requested company\nconst hasCompanyInContent=visualContentStr.includes(companyNameLower);const hasCompanyInFilename=sourceObj.filename&&sourceObj.filename.toLowerCase().includes(companyNameLower);// Check detected companies from OCR\nconst detectedCompanies=((_sourceObj$visual_con5=sourceObj.visual_content)===null||_sourceObj$visual_con5===void 0?void 0:_sourceObj$visual_con5.detected_companies)||[];const hasDetectedCompany=detectedCompanies.some(company=>company.toLowerCase().includes(companyNameLower)||companyNameLower.includes(company.toLowerCase()));// Check OCR text\nconst ocrText=((_sourceObj$visual_con6=sourceObj.visual_content)===null||_sourceObj$visual_con6===void 0?void 0:_sourceObj$visual_con6.ocr_text)||'';const hasOcrMatch=ocrText.toLowerCase().includes(companyNameLower);// Check if marked as logo\nconst isLogo=((_sourceObj$visual_con7=sourceObj.visual_content)===null||_sourceObj$visual_con7===void 0?void 0:_sourceObj$visual_con7.is_logo)||false;const isRelevantLogo=hasCompanyInContent||hasCompanyInFilename||hasDetectedCompany||hasOcrMatch||isLogo;console.log('🔍 DEBUG: Company logo filter result:',{company:companyMatch,hasCompanyInContent,hasCompanyInFilename,hasDetectedCompany,hasOcrMatch,isLogo,isRelevantLogo,should_show:isImage&&isRelevantLogo});return isRelevantLogo;}return false;// Not an image for logo query\n}// If user asked for a specific project image\nif(projectMatch&&userQueryLower.includes('image')){var _sourceObj$visual_con8,_sourceObj$visual_con9;const projectNumber=projectMatch[1];const isImage=sourceObj.content_type==='image';// Check if this image is related to the specific project\nconst hasProjectInContent=visualContentStr.includes(`project ${projectNumber}`)||visualContentStr.includes(`project${projectNumber}`);const hasProjectInFilename=sourceObj.filename&&sourceObj.filename.toLowerCase().includes(`project ${projectNumber}`);const isOnProjectPage=sourceObj.page&&Math.abs(sourceObj.page-parseInt(projectNumber))<=1;// Allow adjacent pages\nconst hasProjectContext=((_sourceObj$visual_con8=sourceObj.visual_content)===null||_sourceObj$visual_con8===void 0?void 0:_sourceObj$visual_con8.project_context)&&sourceObj.visual_content.project_context.includes(`project ${projectNumber}`);const isRelevantProject=hasProjectInContent||hasProjectInFilename||isOnProjectPage||hasProjectContext;console.log('🔍 DEBUG: Project',projectNumber,'image filter:',{isImage,hasProjectInContent,hasProjectInFilename,isOnProjectPage,hasProjectContext,isRelevantProject,visualContentStr_sample:visualContentStr.substring(0,150),project_context:(_sourceObj$visual_con9=sourceObj.visual_content)===null||_sourceObj$visual_con9===void 0?void 0:_sourceObj$visual_con9.project_context,page:sourceObj.page,should_show:isImage&&isRelevantProject});// If this is an image and it's relevant to the project, show it\n// If no images are relevant, we'll fall back to showing all images\nreturn isImage&&isRelevantProject;}// If user asked for a specific quotation table\nif(quotationMatch&&userQueryLower.includes('table')){const quotationNumber=quotationMatch[1];const isTable=sourceObj.content_type==='table';// Check if this table is related to the specific quotation\nconst isRelevantQuotation=visualContentStr.includes(`quotation ${quotationNumber}`)||visualContentStr.includes(`quotation${quotationNumber}`)||visualContentStr.includes(`quote ${quotationNumber}`);console.log('🔍 DEBUG: Quotation',quotationNumber,'table filter:',{isTable,isRelevantQuotation,should_show:isTable&&isRelevantQuotation});return isTable&&isRelevantQuotation;}// If user specifically asked for images (but no specific project), only show images\nif(userQueryLower.includes('image')&&!userQueryLower.includes('table')){const isImage=sourceObj.content_type==='image';console.log('🔍 DEBUG: User asked for images only, source is image:',isImage);return isImage;}// If user specifically asked for tables (but no specific quotation), only show tables\nif(userQueryLower.includes('table')&&!userQueryLower.includes('image')){const isTable=sourceObj.content_type==='table';console.log('🔍 DEBUG: User asked for tables only, source is table:',isTable);return isTable;}// Default: show all visual content\nconsole.log('🔍 DEBUG: Showing all visual content by default');return true;};// Handle command shortcuts in the textbox\nconst handleCommandShortcut=input=>{// Check if the input is a command\nif(input.startsWith('/')){const command=input.split(' ')[0].toLowerCase();// Command: /model <model-name>\nif(command==='/model'){const modelArg=input.substring(7).trim();const matchedModel=DEFAULT_LLM_MODELS.find(m=>m.name.toLowerCase().includes(modelArg.toLowerCase())||m.id.toLowerCase().includes(modelArg.toLowerCase()));if(matchedModel&&matchedModel.enabled){setActiveLLMModel(matchedModel.id);setInput('');return'processed';}}// Command: /reset or /clear - clear chat history\nelse if(command==='/reset'||command==='/clear'){clearCurrentChat();setInput('');return'processed';}}return'not_processed';};const handleSendMessage=async e=>{e.preventDefault();if(!input.trim()||isSubmitting)return;// Handle command shortcuts like /reset, /model, etc.\nif(input.startsWith('/')){const result=handleCommandShortcut(input);if(result==='processed'){setInput('');return;}// If not processed as a command, continue as a regular message\n}return await sendUserMessage(input);};const sendUserMessage=async messageText=>{var _messagesEndRef$curre;// Create new chat session if none exists\nif(!currentSession){await createNewChat();}const userMessage={id:`user-${Date.now()}`,content:messageText,sender:'user',timestamp:new Date().toISOString(),chatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'temp'};addMessage(userMessage);setInput('');const messageId=Date.now();const tempAiMessage={id:`ai-${messageId}`,content:'',sender:'ai',loading:true,timestamp:new Date().toISOString(),llm_model:activeLLMModel,chatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'temp'};addMessage(tempAiMessage);(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:'smooth'});setIsSubmitting(true);setShowTrainLoader(true);setCurrentSearchStage('initializing');try{var _messagesEndRef$curre2,_response$document_so,_response$website_sou,_messagesEndRef$curre3;(_messagesEndRef$curre2=messagesEndRef.current)===null||_messagesEndRef$curre2===void 0?void 0:_messagesEndRef$curre2.scrollIntoView({behavior:'smooth'});// Simulate search progress updates with train loader\nsetTimeout(()=>setCurrentSearchStage('searching_documents'),500);setTimeout(()=>setCurrentSearchStage('searching_websites'),2000);setTimeout(()=>setCurrentSearchStage('generating_answer'),4000);const response=await sendQuery(messageText,activeLLMModel);// Create the AI message based on strict priority logic\nconst aiMessage={id:`ai-${messageId}`,content:response.answer,document_answer:response.document_answer,website_answer:response.website_answer,llm_model:response.llm_model||activeLLMModel,sender:'ai',sources:response.sources,document_sources:response.document_sources,website_sources:response.website_sources,chatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'temp',llm_fallback:response.llm_fallback};// Debug logging\nconsole.log('AI Message created:',{document_answer:response.document_answer?'EXISTS':'MISSING',website_answer:response.website_answer?'EXISTS':'MISSING',document_sources_count:((_response$document_so=response.document_sources)===null||_response$document_so===void 0?void 0:_response$document_so.length)||0,website_sources_count:((_response$website_sou=response.website_sources)===null||_response$website_sou===void 0?void 0:_response$website_sou.length)||0,llm_fallback:response.llm_fallback});// Debug visual content sources\nconsole.log('🔍 DEBUG: Document sources from API:',response.document_sources);if(response.document_sources){response.document_sources.forEach((source,index)=>{console.log(`🔍 DEBUG: Source ${index}:`,{content_type:source.content_type,has_visual_content:!!source.visual_content,storage_url:source.storage_url,display_type:source.display_type,visual_content_keys:source.visual_content?Object.keys(source.visual_content):[]});});}updateMessage(tempAiMessage.id,aiMessage);(_messagesEndRef$curre3=messagesEndRef.current)===null||_messagesEndRef$curre3===void 0?void 0:_messagesEndRef$curre3.scrollIntoView({behavior:'smooth'});}catch(error){console.error('Error sending message:',error);// Provide more helpful error message without the specific query\nconst errorMessage={id:`ai-${messageId}`,content:`I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,sender:'ai',chatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'temp',llm_fallback:true};updateMessage(tempAiMessage.id,errorMessage);}finally{setIsSubmitting(false);setShowTrainLoader(false);setCurrentSearchStage('complete');}};const processDocumentSources=sources=>{if(!sources||sources.length===0)return[];// Group by filename to avoid repetition\nconst groupedSources={};sources.forEach(source=>{let filename;let page;if(typeof source==='string'){// Parse string format like \"MaintenanceManual.pdf – Page 3\"\nconst match=source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);filename=match?match[1].trim():source;page=match&&match[2]?parseInt(match[2]):1;}else{filename=source.name||source.filename||\"Unknown Document\";page=source.page||1;}if(!groupedSources[filename]){groupedSources[filename]={filename,pages:[]};}if(!groupedSources[filename].pages.includes(page)){groupedSources[filename].pages.push(page);}});// Convert to display format with viewer links\nreturn Object.values(groupedSources).map(group=>{const sortedPages=group.pages.sort((a,b)=>a-b);const pageText=sortedPages.length===1?`Page ${sortedPages[0]}`:`Pages ${sortedPages.join(', ')}`;// Create link for document viewer that opens at exact page number\nreturn{text:`${group.filename} – ${pageText}`,link:`/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,isDocument:true};});};const processWebsiteSources=sources=>{if(!sources||sources.length===0)return[];// Remove duplicates and format\nconst uniqueUrls=new Set();const processed=[];sources.forEach(source=>{let url;let displayText;if(typeof source==='string'){url=source.startsWith('http')?source:`https://${source}`;try{const urlObj=new URL(url);displayText=urlObj.hostname.replace(/^www\\./,'');}catch{displayText=source;}}else{url=source.url||'https://railgpt.indianrailways.gov.in';try{const urlObj=new URL(url);displayText=urlObj.hostname.replace(/^www\\./,'');}catch{displayText=url;}}if(!uniqueUrls.has(url)){uniqueUrls.add(url);processed.push({text:displayText,link:url,isDocument:false// Mark as website source\n});}});return processed;// Return all website sources\n};// Component for expandable source list with appropriate click behaviors\nconst SourceList=_ref2=>{let{items,maxVisible=3}=_ref2;const[expanded,setExpanded]=useState(items.length<=maxVisible);if(items.length===0)return null;const visibleItems=expanded?items:items.slice(0,maxVisible);const hasMore=items.length>maxVisible;const handleDocumentClick=(e,link)=>{// If we want to handle document links in a special way, we can do so here\n// For example, we could open a modal or new tab with the document viewer\n// Currently, just allowing regular link behavior\n};return/*#__PURE__*/_jsxs(\"ul\",{className:\"text-xs list-disc pl-4 mt-1 space-y-1\",children:[visibleItems.map((item,index)=>/*#__PURE__*/_jsx(\"li\",{children:item.link?/*#__PURE__*/_jsx(\"a\",{href:item.link,target:\"_blank\",rel:\"noopener noreferrer\",className:\"hover:underline text-blue-600 transition-colors duration-200\",title:item.isDocument?\"Open document at this page\":\"Open website in new tab\",onClick:item.isDocument?e=>handleDocumentClick(e,item.link):undefined,children:item.text}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-700\",children:item.text})},index)),hasMore&&!expanded&&/*#__PURE__*/_jsx(\"li\",{className:\"list-none\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setExpanded(true),className:\"text-blue-500 hover:underline text-xs transition-colors duration-200\",children:[\"+ \",items.length-maxVisible,\" more sources\"]})}),hasMore&&expanded&&/*#__PURE__*/_jsx(\"li\",{className:\"list-none\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>setExpanded(false),className:\"text-blue-500 hover:underline text-xs transition-colors duration-200\",children:\"Show less\"})})]});};// Sidebar handlers\nconst handleChatSelect=async chatSession=>{// Load the selected chat session using context\nawait loadChatSession(chatSession.id);setActiveLLMModel(chatSession.model_used||'gemini-2.0-flash');setSidebarOpen(false);// Close sidebar on mobile after selection\n};const handleNewChat=async()=>{console.log('Creating new chat...');await createNewChat();setSidebarOpen(false);// Close sidebar on mobile after creating new chat\n};return/*#__PURE__*/_jsxs(\"div\",{className:\"flex h-screen bg-gray-100 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(TrainLoader,{isVisible:showTrainLoader,message:(()=>{switch(currentSearchStage){case'searching_documents':return\"RailGPT Searching in Documents...\";case'searching_websites':return\"RailGPT Searching in Websites...\";case'generating_answer':return\"RailGPT Generating Response...\";default:return\"RailGPT Processing Your Query...\";}})(),trainType:\"express\",currentStage:currentSearchStage,sidebarOpen:sidebarOpen}),/*#__PURE__*/_jsx(ChatSidebar,{isOpen:sidebarOpen,onToggle:()=>setSidebarOpen(!sidebarOpen),currentChatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'',onChatSelect:handleChatSelect,onNewChat:handleNewChat}),/*#__PURE__*/_jsxs(\"div\",{className:`flex flex-col flex-1 transition-all duration-300 ${sidebarOpen?'lg:ml-80':''}`,children:[/*#__PURE__*/_jsx(\"div\",{className:`flex-1 ${messages.length>0?'overflow-y-auto':'overflow-hidden'} p-4 pb-32`,children:messages.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-gray-500\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xl font-semibold mb-3\",children:\"Welcome to RailGPT!\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Ask questions about Indian Railways...\"})]})}):/*#__PURE__*/_jsxs(\"div\",{children:[messages.map(message=>/*#__PURE__*/_jsx(\"div\",{className:`mb-4 ${message.sender==='user'?'flex justify-end':'flex justify-start'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:`max-w-4xl rounded-lg p-4 transition-colors duration-300 ${message.sender==='user'?'bg-blue-500 text-white':'bg-white text-gray-800 shadow-md'}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-start mb-1\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:message.sender==='user'?'You':'RailGPT'}),message.timestamp&&/*#__PURE__*/_jsx(\"span\",{className:`text-xs ml-2 ${message.sender==='user'?'text-blue-100':'text-gray-500'}`,children:new Date(message.timestamp).toLocaleTimeString()})]}),message.sender==='user'&&message.content&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-2 whitespace-pre-wrap\",children:message.content}),message.sender==='ai'&&/*#__PURE__*/_jsx(\"div\",{children:((_message$document_ans,_message$website_answ,_message$document_sou,_message$website_sour)=>{// Only hide if this specific message is loading AND has no content yet\nif(message.loading&&showTrainLoader&&!message.content&&!message.document_answer&&!message.website_answer){return null;}// Process sources with improved deduplication\nconst documentSourceItems=processDocumentSources(message.document_sources);const websiteSourceItems=processWebsiteSources(message.website_sources);// Check what content is available for conditional display\nconst hasDocumentContent=!!(message.document_answer&&message.document_answer.trim()!==\"\");const hasWebsiteContent=!!(message.website_answer&&message.website_answer.trim()!==\"\");// LLM fallback happens if NEITHER document sources NOR website sources are found, or if explicitly set\nconst hasLLMFallback=!hasDocumentContent&&!hasWebsiteContent||message.llm_fallback;// Debug logging for rendering\nconsole.log(`🔍 Rendering message ${message.id}:`,{hasDocumentContent,hasWebsiteContent,hasLLMFallback,documentAnswerLength:((_message$document_ans=message.document_answer)===null||_message$document_ans===void 0?void 0:_message$document_ans.length)||0,websiteAnswerLength:((_message$website_answ=message.website_answer)===null||_message$website_answ===void 0?void 0:_message$website_answ.length)||0,documentSourcesCount:documentSourceItems.length,websiteSourcesCount:websiteSourceItems.length,rawDocumentAnswer:message.document_answer?'EXISTS':'MISSING',rawWebsiteAnswer:message.website_answer?'EXISTS':'MISSING',rawDocumentSources:((_message$document_sou=message.document_sources)===null||_message$document_sou===void 0?void 0:_message$document_sou.length)||0,rawWebsiteSources:((_message$website_sour=message.website_sources)===null||_message$website_sour===void 0?void 0:_message$website_sour.length)||0});// Get the user's question for context - find the most recent user message before this AI message\nconst currentMessageIndex=messages.findIndex(m=>m.id===message.id);let userQuestion='';// Look backwards from current AI message to find the most recent user message\nfor(let i=currentMessageIndex-1;i>=0;i--){if(messages[i].sender==='user'&&messages[i].content){userQuestion=messages[i].content;break;}}console.log('🔍 DEBUG: Found user question for AI message:',{aiMessageId:message.id,userQuestion});// Conditional display logic based on answer sources\nconst components=[];// eslint-disable-next-line @typescript-eslint/no-unused-vars\nlet answerSource='';// Case 1: Answer exists from both uploaded documents and websites\nif(hasDocumentContent&&hasWebsiteContent){answerSource='document_and_website';// Document answer card\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst documentName=documentSourceItems.length===1?documentSourceItems[0].text.split(' – ')[0]:'Uploaded Documents';components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-semibold text-blue-800 text-sm mb-3 flex items-center\",children:[\"\\uD83D\\uDCC4 Answer from \",documentName]}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.document_answer||\"\",query:userQuestion,model:message.llm_model,chatId:message.chatId}),(_message$document_sou2=>{const visualSources=((_message$document_sou2=message.document_sources)===null||_message$document_sou2===void 0?void 0:_message$document_sou2.filter(source=>typeof source==='object'&&source.visual_content))||[];// First try to find sources that match the user's specific request\nlet relevantSources=visualSources.filter(source=>filterVisualContent(source,userQuestion));// If no specific matches and user asked for images, show any available images\nif(relevantSources.length===0&&userQuestion.toLowerCase().includes('image')){relevantSources=visualSources.filter(source=>source.content_type==='image');console.log('🔍 DEBUG: No specific project matches, showing all available images:',relevantSources.length);}return relevantSources.length>0&&shouldShowVisualContent(userQuestion,message.document_answer||'')?/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"text-sm font-semibold text-blue-800 mb-3\",children:\"\\uD83D\\uDCCA Visual Content:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:relevantSources.map((source,index)=>/*#__PURE__*/_jsx(VisualContent,{source:source},index))})]}):null;})(),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 pt-3 border-t border-blue-200\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-blue-700 font-semibold mb-1\",children:\"Sources:\"}),/*#__PURE__*/_jsx(SourceList,{items:documentSourceItems,maxVisible:5})]})]},\"document-card\"));// Website answer card\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst websiteLabel=websiteSourceItems.length>1?'Extracted Websites':'Extracted Website';components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-semibold text-green-800 text-sm mb-3 flex items-center\",children:[\"\\uD83C\\uDF10 Answer from \",websiteLabel]}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.website_answer||\"\",query:userQuestion,model:message.llm_model,chatId:message.chatId}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 pt-3 border-t border-green-200\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-green-700 font-semibold mb-1\",children:\"Sources:\"}),/*#__PURE__*/_jsx(SourceList,{items:websiteSourceItems,maxVisible:3})]})]},\"website-card\"));// Case 2: Answer exists only from documents\n}else if(hasDocumentContent){answerSource='document_only';// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst documentName=documentSourceItems.length===1?documentSourceItems[0].text.split(' – ')[0]:'Uploaded Documents';components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold text-blue-800 text-sm mb-3 flex items-center\",children:\"\\uD83D\\uDCC4 Answer from Uploaded Documents\"}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.document_answer||\"\",query:userQuestion,model:message.llm_model,chatId:message.chatId}),(_message$document_sou3=>{const visualSources=((_message$document_sou3=message.document_sources)===null||_message$document_sou3===void 0?void 0:_message$document_sou3.filter(source=>typeof source==='object'&&source.visual_content))||[];// First try to find sources that match the user's specific request\nlet relevantSources=visualSources.filter(source=>filterVisualContent(source,userQuestion));// If no specific matches and user asked for images, show any available images\nif(relevantSources.length===0&&userQuestion.toLowerCase().includes('image')){relevantSources=visualSources.filter(source=>source.content_type==='image');console.log('🔍 DEBUG: No specific project matches for document-only case, showing all available images:',relevantSources.length);}return relevantSources.length>0&&shouldShowVisualContent(userQuestion,message.document_answer||'')?/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"text-sm font-semibold text-blue-800 mb-3\",children:\"\\uD83D\\uDCCA Visual Content:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:relevantSources.map((source,index)=>/*#__PURE__*/_jsx(VisualContent,{source:source},index))})]}):null;})(),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 pt-3 border-t border-blue-200\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-blue-700 font-semibold mb-1\",children:\"Sources:\"}),/*#__PURE__*/_jsx(SourceList,{items:documentSourceItems,maxVisible:5})]})]},\"document-priority\"));// Case 3: Answer exists only from websites\n}else if(hasWebsiteContent){answerSource='website_only';// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst websiteLabel=websiteSourceItems.length>1?'Extracted Websites':'Extracted Website';components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold text-green-800 text-sm mb-3 flex items-center\",children:\"\\uD83C\\uDF10 Answer from Extracted Websites\"}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.website_answer||\"\",query:userQuestion,model:message.llm_model,chatId:message.chatId}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 pt-3 border-t border-green-200\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-green-700 font-semibold mb-1\",children:\"Sources:\"}),/*#__PURE__*/_jsx(SourceList,{items:websiteSourceItems,maxVisible:3})]})]},\"website-priority\"));// Case 4: No answer from either - fallback to LLM\n}else if(hasLLMFallback){var _message$llm_model,_message$llm_model2,_message$llm_model3,_message$llm_model4,_message$llm_model5,_message$llm_model6;answerSource='llm';const modelName=message.llm_model||'Gemini';const modelLogo=(_message$llm_model=message.llm_model)!==null&&_message$llm_model!==void 0&&_message$llm_model.includes('chatgpt')?'🤖':(_message$llm_model2=message.llm_model)!==null&&_message$llm_model2!==void 0&&_message$llm_model2.includes('groq')?'⚡':(_message$llm_model3=message.llm_model)!==null&&_message$llm_model3!==void 0&&_message$llm_model3.includes('deepseek')?'🔍':(_message$llm_model4=message.llm_model)!==null&&_message$llm_model4!==void 0&&_message$llm_model4.includes('qwen')?'🌐':(_message$llm_model5=message.llm_model)!==null&&_message$llm_model5!==void 0&&_message$llm_model5.includes('ollama')?'🏠':(_message$llm_model6=message.llm_model)!==null&&_message$llm_model6!==void 0&&_message$llm_model6.includes('huggingface')?'🤗':'🧠';// Only show the LLM fallback card if not in loading state\nif(!showTrainLoader||!message.loading){components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-semibold text-purple-800 text-sm mb-3 flex items-center\",children:[modelLogo,\" Answer generated by \",modelName]}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.content||\"I couldn't find any relevant information to answer your question.\",query:userQuestion,model:message.llm_model||'Gemini',chatId:message.chatId}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-3 pt-3 border-t border-purple-200\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-purple-600 italic\",children:\"This answer was generated by an AI model as no relevant information was found in your documents or websites.\"})})]},\"llm-fallback\"));}// Case 5: No sources found and fallback disabled (or similar edge case)\n}else{// eslint-disable-next-line @typescript-eslint/no-unused-vars\nanswerSource='no_results';// Only show the \"no results\" card if not in loading state\nif(!showTrainLoader||!message.loading){components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-2\",children:\"No sources found\"}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.content||\"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\",query:userQuestion,model:message.llm_model||'Gemini',chatId:message.chatId})]},\"no-results\"));}}// If we have components to display, render them\nif(components.length>0){return/*#__PURE__*/_jsx(\"div\",{className:\"mt-3\",children:components});}// Fallback for any unhandled edge cases (should rarely happen)\nconsole.warn(\"Frontend: Unhandled rendering case\");return/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-2\",children:\"Rendering Error\"}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.content||\"An error occurred while rendering the response.\",query:userQuestion,model:message.llm_model||'Gemini',chatId:message.chatId})]});})()})]})},message.id)),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef,style:{float:'left',clear:'both'}})]})}),/*#__PURE__*/_jsxs(\"div\",{className:`border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen?'lg:left-80 left-0':'left-0'}`,children:[/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSendMessage,className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 relative flex\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:input,onChange:e=>{const newValue=e.target.value;setInput(newValue);// Don't handle command shortcuts as you type, only on submit\n},placeholder:\"Type your message... (/model, /reset, /clear)\",className:\"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\",disabled:isSubmitting,\"aria-label\":\"Message input\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(LLMSelector,{currentModel:activeLLMModel,onModelChange:modelId=>setActiveLLMModel(modelId),isLoading:isSubmitting}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isSubmitting||!input.trim(),className:\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\",title:\"Send message\",children:/*#__PURE__*/_jsx(\"span\",{children:isSubmitting?\"Sending...\":\"Send\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-400 mt-1 text-center\",children:[\"Current model: \",((_DEFAULT_LLM_MODELS$f=DEFAULT_LLM_MODELS.find(m=>m.id===activeLLMModel))===null||_DEFAULT_LLM_MODELS$f===void 0?void 0:_DEFAULT_LLM_MODELS$f.name)||activeLLMModel]})]}),messages.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"h-36\"})]})]});}function App(_ref3){let{sidebarOpen,setSidebarOpen}=_ref3;return/*#__PURE__*/_jsx(ChatInterface,{sidebarOpen:sidebarOpen,setSidebarOpen:setSidebarOpen});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "LLMSelector", "DEFAULT_LLM_MODELS", "InteractiveAnswer", "ChatSidebar", "TrainLoader", "VisualContent", "useChatContext", "jsx", "_jsx", "jsxs", "_jsxs", "ChatInterface", "_ref", "_DEFAULT_LLM_MODELS$f", "sidebarOpen", "setSidebarOpen", "currentSession", "messages", "createNewChat", "loadChatSession", "addMessage", "updateMessage", "clearCurrentChat", "input", "setInput", "isSubmitting", "setIsSubmitting", "activeLLMModel", "setActiveLLMModel", "showTrainLoader", "setShowTrainLoader", "currentSearchStage", "setCurrentSearchStage", "messagesEndRef", "current", "scrollIntoView", "behavior", "shouldShowVisualContent", "userQuestion", "documentAnswer", "console", "log", "documentAnswerLength", "length", "userQuery", "toLowerCase", "includes", "askedForImages", "askedForTableData", "askedToShowImages", "answerLower", "hasTableInAnswer", "hasImageDescription", "answerHasContent", "shouldShow", "filterVisualContent", "source", "visual_content", "userQueryLower", "sourceObj", "content_type", "has_visual_content", "user_query", "filename", "page", "visual_content_sample", "JSON", "stringify", "substring", "projectMatch", "match", "quotationMatch", "logoQueryPatterns", "companyMatch", "pattern", "trim", "visualContentStr", "_sourceObj$visual_con", "_sourceObj$visual_con2", "_sourceObj$visual_con3", "_sourceObj$visual_con4", "companyNameLower", "isImage", "company", "has_detected_companies", "detected_companies", "ocr_text", "is_logo", "_sourceObj$visual_con5", "_sourceObj$visual_con6", "_sourceObj$visual_con7", "hasCompanyInContent", "hasCompanyInFilename", "detectedCompanies", "hasDetectedCompany", "some", "ocrText", "hasOcrMatch", "isLogo", "isRelevantLogo", "should_show", "_sourceObj$visual_con8", "_sourceObj$visual_con9", "projectNumber", "hasProjectInContent", "hasProjectInFilename", "isOnProjectPage", "Math", "abs", "parseInt", "hasProjectContext", "project_context", "isRelevantProject", "visualContentStr_sample", "quotationNumber", "isTable", "isRelevantQuotation", "handleCommandShortcut", "startsWith", "command", "split", "modelArg", "matchedModel", "find", "m", "name", "id", "enabled", "handleSendMessage", "e", "preventDefault", "result", "sendUserMessage", "messageText", "_messagesEndRef$curre", "userMessage", "Date", "now", "content", "sender", "timestamp", "toISOString", "chatId", "messageId", "tempAiMessage", "loading", "llm_model", "_messagesEndRef$curre2", "_response$document_so", "_response$website_sou", "_messagesEndRef$curre3", "setTimeout", "response", "aiMessage", "answer", "document_answer", "website_answer", "sources", "document_sources", "website_sources", "llm_fallback", "document_sources_count", "website_sources_count", "for<PERSON>ach", "index", "storage_url", "display_type", "visual_content_keys", "Object", "keys", "error", "errorMessage", "processDocumentSources", "groupedSources", "pages", "push", "values", "map", "group", "sortedPages", "sort", "a", "b", "pageText", "join", "text", "link", "encodeURIComponent", "isDocument", "processWebsiteSources", "uniqueUrls", "Set", "processed", "url", "displayText", "url<PERSON>bj", "URL", "hostname", "replace", "has", "add", "SourceList", "_ref2", "items", "maxVisible", "expanded", "setExpanded", "visibleItems", "slice", "hasMore", "handleDocumentClick", "className", "children", "item", "href", "target", "rel", "title", "onClick", "undefined", "handleChatSelect", "chatSession", "model_used", "handleNewChat", "isVisible", "message", "trainType", "currentStage", "isOpen", "onToggle", "currentChatId", "onChatSelect", "onNewChat", "toLocaleTimeString", "_message$document_ans", "_message$website_answ", "_message$document_sou", "_message$website_sour", "documentSourceItems", "websiteSourceItems", "hasDocumentContent", "has<PERSON>ebsite<PERSON><PERSON>nt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "websiteAnswerLength", "documentSourcesCount", "websiteSourcesCount", "rawDocumentAnswer", "rawWebsiteAnswer", "rawDocumentSources", "rawWebsiteSources", "currentMessageIndex", "findIndex", "i", "aiMessageId", "components", "answerSource", "documentName", "query", "model", "_message$document_sou2", "visualSources", "filter", "relevantSources", "websiteLabel", "_message$document_sou3", "_message$llm_model", "_message$llm_model2", "_message$llm_model3", "_message$llm_model4", "_message$llm_model5", "_message$llm_model6", "modelName", "modelLogo", "warn", "ref", "style", "float", "clear", "onSubmit", "type", "value", "onChange", "newValue", "placeholder", "disabled", "currentModel", "onModelChange", "modelId", "isLoading", "App", "_ref3"], "sources": ["C:/IR App/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './App.css';\nimport { sendQuery } from './services/api';\nimport LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';\nimport InteractiveAnswer from './components/ui/InteractiveAnswer';\nimport ChatSidebar from './components/chat/ChatSidebar';\nimport TrainLoader from './components/ui/TrainLoader';\nimport VisualContent from './components/ui/VisualContent';\nimport { useChatContext } from './contexts/ChatContext';\nimport { ChatSession, ChatMessage } from './services/supabase';\n\ninterface Source {\n  source_type: string;\n  filename?: string;\n  page?: number;\n  url?: string;\n  link?: string; // For document viewer links\n  name?: string; // For display name\n  // Visual content fields\n  content_type?: string;  // \"text\", \"table\", \"image\", \"chart_diagram\"\n  visual_content?: Record<string, any>;  // Visual content metadata\n  storage_url?: string;  // URL for stored visual content\n  display_type?: string;  // \"text\", \"html_table\", \"image\", \"base64_image\"\n}\n\n// Using ChatMessage interface from services/supabase.ts\n\ninterface ChatInterfaceProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction ChatInterface({ sidebarOpen, setSidebarOpen }: ChatInterfaceProps) {\n  const {\n    currentSession,\n    messages,\n    createNewChat,\n    loadChatSession,\n    addMessage,\n    updateMessage,\n    clearCurrentChat\n  } = useChatContext();\n\n  const [input, setInput] = useState<string>('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model\n  const [showTrainLoader, setShowTrainLoader] = useState(false);\n  const [currentSearchStage, setCurrentSearchStage] = useState<'initializing' | 'searching_documents' | 'searching_websites' | 'generating_answer' | 'complete'>('initializing');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Scroll to bottom of chat whenever messages change\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  // Helper function to determine if visual content should be shown\n  const shouldShowVisualContent = (userQuestion: string, documentAnswer: string): boolean => {\n    console.log('🔍 DEBUG shouldShowVisualContent called with:', { userQuestion, documentAnswerLength: documentAnswer?.length });\n    \n    const userQuery = userQuestion.toLowerCase();\n    \n    // TEMPORARY FIX: Always show visual content when user asks for images\n    if (userQuery.includes('image')) {\n      console.log('🔍 DEBUG: ALWAYS showing visual content because query contains \"image\"');\n      return true;\n    }\n    \n    // Detect different types of requests\n    const askedForImages = userQuery.includes('image') || userQuery.includes('picture') || userQuery.includes('diagram') || userQuery.includes('chart');\n    const askedForTableData = (userQuery.includes('table') || userQuery.includes('quotation')) && \n                             (userQuery.includes('give me') || userQuery.includes('show me table') || userQuery.includes('get') || userQuery.includes('provide'));\n    const askedToShowImages = userQuery.includes('show me image') || userQuery.includes('display image') || userQuery.includes('see image');\n    \n    const answerLower = documentAnswer?.toLowerCase() || '';\n    const hasTableInAnswer = answerLower.includes('<table>') || answerLower.includes('|') || \n                            (answerLower.includes('table') && answerLower.length > 200);\n    const hasImageDescription = answerLower.includes('image') || answerLower.includes('figure') || answerLower.includes('diagram');\n    const answerHasContent = answerLower.length > 50;\n    \n    // ALWAYS show visual content if user specifically asks to see images/visuals\n    if (askedToShowImages || (askedForImages && (userQuery.includes('show') || userQuery.includes('display')))) {\n      console.log('🔍 DEBUG: Visual content shown because user explicitly asked for images');\n      return true;\n    }\n    \n    // NEVER show if user asked for table data (they want the actual data, not images)\n    if (askedForTableData && !askedForImages) {\n      console.log('🔍 DEBUG: Visual content hidden because user asked for table data');\n      return false;\n    }\n    \n    // For other cases, show only if answer doesn't already provide adequate information\n    const shouldShow = !hasTableInAnswer && // Don't show if answer already has table data\n                      !hasImageDescription && // Don't show if answer already describes images well\n                      (\n                        (askedForImages && !answerHasContent) || // Show images if user asked and answer is short\n                        (!answerHasContent && answerLower.length < 50) // Show for very short answers\n                      );\n    \n    console.log('🔍 DEBUG: Visual content decision for regular query:', shouldShow);\n    return shouldShow;\n  };\n\n  // Helper function to filter visual content types based on user query\n  const filterVisualContent = (source: Source | string, userQuestion: string) => {\n    if (typeof source !== 'object' || !source.visual_content) {\n      console.log('🔍 DEBUG: Filtering out - not object or no visual_content:', typeof source, !!(source as Source)?.visual_content);\n      return false;\n    }\n    \n    const userQueryLower = userQuestion.toLowerCase();\n    const sourceObj = source as Source;\n    \n    console.log('🔍 DEBUG: Checking visual content filter for:', {\n      content_type: sourceObj.content_type,\n      has_visual_content: !!sourceObj.visual_content,\n      user_query: userQueryLower,\n      filename: sourceObj.filename,\n      page: sourceObj.page,\n      visual_content_sample: sourceObj.visual_content ? JSON.stringify(sourceObj.visual_content).substring(0, 200) : 'none'\n    });\n    \n    // Extract specific project/quotation numbers from user query\n    const projectMatch = userQueryLower.match(/project\\s*(\\d+)/);\n    const quotationMatch = userQueryLower.match(/quotation\\s*(\\d+)/);\n    \n    // Extract company names for logo queries\n    const logoQueryPatterns = [\n      /(?:logo\\s+(?:of\\s+)?|show\\s+me\\s+(?:the\\s+)?logo\\s+(?:of\\s+)?)([A-Z][A-Za-z\\s&]+?)(?:\\s+logo|\\s+enterprises|\\s+company|\\s+corp|\\s+ltd|\\s+inc|\\s*$)/i,\n      /([A-Z][A-Za-z\\s&]+?)\\s+(?:enterprises|company|corp|ltd|inc)\\s+logo/i,\n      /([A-Z][A-Za-z\\s&]+?)\\s+logo/i,\n    ];\n    \n    let companyMatch = null;\n    for (const pattern of logoQueryPatterns) {\n      const match = userQuestion.match(pattern);\n      if (match) {\n        companyMatch = match[1].trim();\n        break;\n      }\n    }\n    \n    // Check if the visual content contains relevant information for the specific request\n    const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();\n    \n    // If user asked for a specific company logo\n    if (companyMatch && userQueryLower.includes('logo')) {\n      const companyNameLower = companyMatch.toLowerCase();\n      const isImage = sourceObj.content_type === 'image';\n      \n      console.log('🔍 DEBUG: Company logo search:', {\n        company: companyMatch,\n        isImage,\n        has_detected_companies: !!(sourceObj.visual_content?.detected_companies),\n        detected_companies: sourceObj.visual_content?.detected_companies || [],\n        ocr_text: sourceObj.visual_content?.ocr_text || '',\n        is_logo: sourceObj.visual_content?.is_logo || false\n      });\n      \n      if (isImage) {\n        // Check if this image contains the requested company\n        const hasCompanyInContent = visualContentStr.includes(companyNameLower);\n        const hasCompanyInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(companyNameLower);\n        \n        // Check detected companies from OCR\n        const detectedCompanies: string[] = sourceObj.visual_content?.detected_companies || [];\n        const hasDetectedCompany = detectedCompanies.some((company: string) =>\n          company.toLowerCase().includes(companyNameLower) ||\n          companyNameLower.includes(company.toLowerCase())\n        );\n        \n        // Check OCR text\n        const ocrText = sourceObj.visual_content?.ocr_text || '';\n        const hasOcrMatch = ocrText.toLowerCase().includes(companyNameLower);\n        \n        // Check if marked as logo\n        const isLogo = sourceObj.visual_content?.is_logo || false;\n        \n        const isRelevantLogo = hasCompanyInContent || hasCompanyInFilename || hasDetectedCompany || hasOcrMatch || isLogo;\n        \n        console.log('🔍 DEBUG: Company logo filter result:', {\n          company: companyMatch,\n          hasCompanyInContent,\n          hasCompanyInFilename,\n          hasDetectedCompany,\n          hasOcrMatch,\n          isLogo,\n          isRelevantLogo,\n          should_show: isImage && isRelevantLogo\n        });\n        \n        return isRelevantLogo;\n      }\n      \n      return false; // Not an image for logo query\n    }\n    \n    // If user asked for a specific project image\n    if (projectMatch && userQueryLower.includes('image')) {\n      const projectNumber = projectMatch[1];\n      const isImage = sourceObj.content_type === 'image';\n      \n      // Check if this image is related to the specific project\n      const hasProjectInContent = visualContentStr.includes(`project ${projectNumber}`) || \n                                  visualContentStr.includes(`project${projectNumber}`);\n      const hasProjectInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(`project ${projectNumber}`);\n      const isOnProjectPage = sourceObj.page && Math.abs(sourceObj.page - parseInt(projectNumber)) <= 1; // Allow adjacent pages\n      const hasProjectContext = sourceObj.visual_content?.project_context && \n                               sourceObj.visual_content.project_context.includes(`project ${projectNumber}`);\n      \n      const isRelevantProject = hasProjectInContent || hasProjectInFilename || isOnProjectPage || hasProjectContext;\n      \n      console.log('🔍 DEBUG: Project', projectNumber, 'image filter:', {\n        isImage,\n        hasProjectInContent,\n        hasProjectInFilename,\n        isOnProjectPage,\n        hasProjectContext,\n        isRelevantProject,\n        visualContentStr_sample: visualContentStr.substring(0, 150),\n        project_context: sourceObj.visual_content?.project_context,\n        page: sourceObj.page,\n        should_show: isImage && isRelevantProject\n      });\n      \n      // If this is an image and it's relevant to the project, show it\n      // If no images are relevant, we'll fall back to showing all images\n      return isImage && isRelevantProject;\n    }\n    \n    // If user asked for a specific quotation table\n    if (quotationMatch && userQueryLower.includes('table')) {\n      const quotationNumber = quotationMatch[1];\n      const isTable = sourceObj.content_type === 'table';\n      \n      // Check if this table is related to the specific quotation\n      const isRelevantQuotation = visualContentStr.includes(`quotation ${quotationNumber}`) || \n                                  visualContentStr.includes(`quotation${quotationNumber}`) ||\n                                  visualContentStr.includes(`quote ${quotationNumber}`);\n      \n      console.log('🔍 DEBUG: Quotation', quotationNumber, 'table filter:', {\n        isTable,\n        isRelevantQuotation,\n        should_show: isTable && isRelevantQuotation\n      });\n      \n      return isTable && isRelevantQuotation;\n    }\n    \n    // If user specifically asked for images (but no specific project), only show images\n    if (userQueryLower.includes('image') && !userQueryLower.includes('table')) {\n      const isImage = sourceObj.content_type === 'image';\n      console.log('🔍 DEBUG: User asked for images only, source is image:', isImage);\n      return isImage;\n    }\n    \n    // If user specifically asked for tables (but no specific quotation), only show tables\n    if (userQueryLower.includes('table') && !userQueryLower.includes('image')) {\n      const isTable = sourceObj.content_type === 'table';\n      console.log('🔍 DEBUG: User asked for tables only, source is table:', isTable);\n      return isTable;\n    }\n    \n    // Default: show all visual content\n    console.log('🔍 DEBUG: Showing all visual content by default');\n    return true;\n  };\n\n  // Handle command shortcuts in the textbox\n  const handleCommandShortcut = (input: string) => {\n    // Check if the input is a command\n    if (input.startsWith('/')) {\n      const command = input.split(' ')[0].toLowerCase();\n\n      // Command: /model <model-name>\n      if (command === '/model') {\n        const modelArg = input.substring(7).trim();\n        const matchedModel = DEFAULT_LLM_MODELS.find(m =>\n          m.name.toLowerCase().includes(modelArg.toLowerCase()) ||\n          m.id.toLowerCase().includes(modelArg.toLowerCase())\n        );\n\n        if (matchedModel && matchedModel.enabled) {\n          setActiveLLMModel(matchedModel.id);\n          setInput('');\n          return 'processed';\n        }\n      }\n\n      // Command: /reset or /clear - clear chat history\n      else if (command === '/reset' || command === '/clear') {\n        clearCurrentChat();\n        setInput('');\n        return 'processed';\n      }\n    }\n\n    return 'not_processed';\n  };\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isSubmitting) return;\n\n    // Handle command shortcuts like /reset, /model, etc.\n    if (input.startsWith('/')) {\n      const result = handleCommandShortcut(input);\n      if (result === 'processed') {\n        setInput('');\n        return;\n      }\n      // If not processed as a command, continue as a regular message\n    }\n\n    return await sendUserMessage(input);\n  };\n\n  const sendUserMessage = async (messageText: string) => {\n    // Create new chat session if none exists\n    if (!currentSession) {\n      await createNewChat();\n    }\n\n    const userMessage: ChatMessage = {\n      id: `user-${Date.now()}`,\n      content: messageText,\n      sender: 'user',\n      timestamp: new Date().toISOString(),\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(userMessage);\n    setInput('');\n\n    const messageId = Date.now();\n    const tempAiMessage: ChatMessage = {\n      id: `ai-${messageId}`,\n      content: '',\n      sender: 'ai',\n      loading: true,\n      timestamp: new Date().toISOString(),\n      llm_model: activeLLMModel,\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(tempAiMessage);\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n    setIsSubmitting(true);\n    setShowTrainLoader(true);\n    setCurrentSearchStage('initializing');\n\n    try {\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n      // Simulate search progress updates with train loader\n      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);\n      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);\n      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);\n\n      const response = await sendQuery(messageText, activeLLMModel);\n\n      // Create the AI message based on strict priority logic\n      const aiMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: response.answer,\n        document_answer: response.document_answer,\n        website_answer: response.website_answer,\n        llm_model: response.llm_model || activeLLMModel,\n        sender: 'ai',\n        sources: response.sources,\n        document_sources: response.document_sources,\n        website_sources: response.website_sources,\n        chatId: currentSession?.id || 'temp',\n        llm_fallback: response.llm_fallback,\n      };\n\n      // Debug logging\n      console.log('AI Message created:', {\n        document_answer: response.document_answer ? 'EXISTS' : 'MISSING',\n        website_answer: response.website_answer ? 'EXISTS' : 'MISSING',\n        document_sources_count: response.document_sources?.length || 0,\n        website_sources_count: response.website_sources?.length || 0,\n        llm_fallback: response.llm_fallback\n      });\n\n      // Debug visual content sources\n      console.log('🔍 DEBUG: Document sources from API:', response.document_sources);\n      if (response.document_sources) {\n        response.document_sources.forEach((source, index) => {\n          console.log(`🔍 DEBUG: Source ${index}:`, {\n            content_type: source.content_type,\n            has_visual_content: !!source.visual_content,\n            storage_url: source.storage_url,\n            display_type: source.display_type,\n            visual_content_keys: source.visual_content ? Object.keys(source.visual_content) : []\n          });\n        });\n      }\n\n      updateMessage(tempAiMessage.id, aiMessage);\n\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      // Provide more helpful error message without the specific query\n      const errorMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,\n        sender: 'ai',\n        chatId: currentSession?.id || 'temp',\n        llm_fallback: true,\n      };\n\n      updateMessage(tempAiMessage.id, errorMessage);\n    } finally {\n      setIsSubmitting(false);\n      setShowTrainLoader(false);\n      setCurrentSearchStage('complete');\n    }\n  };\n\n\n\n  const processDocumentSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Group by filename to avoid repetition\n    const groupedSources: { [key: string]: { filename: string; pages: number[] } } = {};\n\n    sources.forEach(source => {\n      let filename: string;\n      let page: number;\n\n      if (typeof source === 'string') {\n        // Parse string format like \"MaintenanceManual.pdf – Page 3\"\n        const match = source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);\n        filename = match ? match[1].trim() : source;\n        page = match && match[2] ? parseInt(match[2]) : 1;\n      } else {\n        filename = source.name || source.filename || \"Unknown Document\";\n        page = source.page || 1;\n        }\n\n      if (!groupedSources[filename]) {\n        groupedSources[filename] = { filename, pages: [] };\n      }\n\n      if (!groupedSources[filename].pages.includes(page)) {\n        groupedSources[filename].pages.push(page);\n      }\n    });\n\n    // Convert to display format with viewer links\n    return Object.values(groupedSources).map(group => {\n      const sortedPages = group.pages.sort((a, b) => a - b);\n      const pageText = sortedPages.length === 1\n        ? `Page ${sortedPages[0]}`\n        : `Pages ${sortedPages.join(', ')}`;\n\n      // Create link for document viewer that opens at exact page number\n      return {\n        text: `${group.filename} – ${pageText}`,\n        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,\n        isDocument: true\n      };\n    });\n  };\n\n  const processWebsiteSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Remove duplicates and format\n    const uniqueUrls = new Set<string>();\n    const processed: Array<{ text: string; link: string; isDocument?: boolean }> = [];\n\n    sources.forEach(source => {\n      let url: string;\n      let displayText: string;\n\n      if (typeof source === 'string') {\n        url = source.startsWith('http') ? source : `https://${source}`;\n          try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n          } catch {\n          displayText = source;\n        }\n      } else {\n        url = source.url || 'https://railgpt.indianrailways.gov.in';\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = url;\n        }\n      }\n\n      if (!uniqueUrls.has(url)) {\n        uniqueUrls.add(url);\n        processed.push({\n          text: displayText,\n          link: url,\n          isDocument: false  // Mark as website source\n        });\n        }\n    });\n\n    return processed; // Return all website sources\n  };\n\n  // Component for expandable source list with appropriate click behaviors\n  const SourceList = ({ items, maxVisible = 3 }: {\n    items: Array<{ text: string; link?: string; isDocument?: boolean }>;\n    maxVisible?: number\n  }) => {\n    const [expanded, setExpanded] = useState(items.length <= maxVisible);\n\n    if (items.length === 0) return null;\n\n    const visibleItems = expanded ? items : items.slice(0, maxVisible);\n    const hasMore = items.length > maxVisible;\n\n    const handleDocumentClick = (e: React.MouseEvent<HTMLAnchorElement>, link: string) => {\n      // If we want to handle document links in a special way, we can do so here\n      // For example, we could open a modal or new tab with the document viewer\n      // Currently, just allowing regular link behavior\n    };\n\n    return (\n      <ul className=\"text-xs list-disc pl-4 mt-1 space-y-1\">\n        {visibleItems.map((item, index) => (\n              <li key={index}>\n                {item.link ? (\n              <a\n                href={item.link}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"hover:underline text-blue-600 transition-colors duration-200\"\n                title={item.isDocument ? \"Open document at this page\" : \"Open website in new tab\"}\n                onClick={item.isDocument ? (e) => handleDocumentClick(e, item.link!) : undefined}\n              >\n                    {item.text}\n                  </a>\n                ) : (\n              <span className=\"text-gray-700\">{item.text}</span>\n                )}\n              </li>\n            ))}\n        {hasMore && !expanded && (\n          <li className=\"list-none\">\n            <button\n              onClick={() => setExpanded(true)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n            >\n              + {items.length - maxVisible} more sources\n            </button>\n          </li>\n        )}\n        {hasMore && expanded && (\n          <li className=\"list-none\">\n              <button\n                onClick={() => setExpanded(false)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n              >\n                Show less\n              </button>\n            </li>\n                )}\n      </ul>\n    );\n  };\n\n  // Sidebar handlers\n  const handleChatSelect = async (chatSession: ChatSession) => {\n    // Load the selected chat session using context\n    await loadChatSession(chatSession.id);\n    setActiveLLMModel(chatSession.model_used || 'gemini-2.0-flash');\n    setSidebarOpen(false); // Close sidebar on mobile after selection\n  };\n\n  const handleNewChat = async () => {\n    console.log('Creating new chat...');\n    await createNewChat();\n    setSidebarOpen(false); // Close sidebar on mobile after creating new chat\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-100 transition-colors duration-300\">\n      {/* Train Loader Overlay */}\n      <TrainLoader\n        isVisible={showTrainLoader}\n        message={(() => {\n          switch (currentSearchStage) {\n            case 'searching_documents':\n              return \"RailGPT Searching in Documents...\";\n            case 'searching_websites':\n              return \"RailGPT Searching in Websites...\";\n            case 'generating_answer':\n              return \"RailGPT Generating Response...\";\n            default:\n              return \"RailGPT Processing Your Query...\";\n          }\n        })()}\n        trainType=\"express\"\n        currentStage={currentSearchStage}\n        sidebarOpen={sidebarOpen}\n      />\n\n      {/* Chat Sidebar */}\n      <ChatSidebar\n        isOpen={sidebarOpen}\n        onToggle={() => setSidebarOpen(!sidebarOpen)}\n        currentChatId={currentSession?.id || ''}\n        onChatSelect={handleChatSelect}\n        onNewChat={handleNewChat}\n      />\n\n      {/* Main Chat Area */}\n      <div className={`flex flex-col flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-80' : ''}`}>\n        {/* Message Area - only scrollable when messages exist */}\n        <div className={`flex-1 ${messages.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-4 pb-32`}>\n          {messages.length === 0 ? (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center text-gray-500\">\n                <p className=\"text-xl font-semibold mb-3\">Welcome to RailGPT!</p>\n                <p>Ask questions about Indian Railways...</p>\n              </div>\n            </div>\n          ) : (\n            <div>\n              {messages.map((message) => (\n                <div\n                  key={message.id}\n                  className={`mb-4 ${\n                    message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'\n                  }`}\n                >\n                  <div\n                    className={`max-w-4xl rounded-lg p-4 transition-colors duration-300 ${\n                      message.sender === 'user'\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-white text-gray-800 shadow-md'\n                    }`}\n                  >\n                    <div className=\"flex justify-between items-start mb-1\">\n                      <span className=\"font-semibold\">\n                        {message.sender === 'user' ? 'You' : 'RailGPT'}\n                      </span>\n                      {message.timestamp && (\n                        <span className={`text-xs ml-2 ${\n                          message.sender === 'user'\n                            ? 'text-blue-100'\n                            : 'text-gray-500'\n                        }`}>\n                          {new Date(message.timestamp).toLocaleTimeString()}\n                        </span>\n                      )}\n                    </div>\n\n                    {/* Only show content directly for user messages */}\n                    {message.sender === 'user' && message.content && (\n                      <div className=\"mt-2 whitespace-pre-wrap\">{message.content}</div>\n                    )}\n\n                    {/* AI messages with strict priority display logic */}\n                    {message.sender === 'ai' && (\n                      <div>\n                        {(() => {\n                          // Only hide if this specific message is loading AND has no content yet\n                          if (message.loading && showTrainLoader && !message.content && !message.document_answer && !message.website_answer) {\n                            return null;\n                          }\n\n                          // Process sources with improved deduplication\n                          const documentSourceItems = processDocumentSources(message.document_sources);\n                          const websiteSourceItems = processWebsiteSources(message.website_sources);\n\n                          // Check what content is available for conditional display\n                          const hasDocumentContent = !!(message.document_answer && message.document_answer.trim() !== \"\");\n                          const hasWebsiteContent = !!(message.website_answer && message.website_answer.trim() !== \"\");\n                          // LLM fallback happens if NEITHER document sources NOR website sources are found, or if explicitly set\n                          const hasLLMFallback = (!hasDocumentContent && !hasWebsiteContent) || message.llm_fallback;\n\n                          // Debug logging for rendering\n                          console.log(`🔍 Rendering message ${message.id}:`, {\n                            hasDocumentContent,\n                            hasWebsiteContent,\n                            hasLLMFallback,\n                            documentAnswerLength: message.document_answer?.length || 0,\n                            websiteAnswerLength: message.website_answer?.length || 0,\n                            documentSourcesCount: documentSourceItems.length,\n                            websiteSourcesCount: websiteSourceItems.length,\n                            rawDocumentAnswer: message.document_answer ? 'EXISTS' : 'MISSING',\n                            rawWebsiteAnswer: message.website_answer ? 'EXISTS' : 'MISSING',\n                            rawDocumentSources: message.document_sources?.length || 0,\n                            rawWebsiteSources: message.website_sources?.length || 0\n                          });\n\n                          // Get the user's question for context - find the most recent user message before this AI message\n                          const currentMessageIndex = messages.findIndex(m => m.id === message.id);\n                          let userQuestion = '';\n                          \n                          // Look backwards from current AI message to find the most recent user message\n                          for (let i = currentMessageIndex - 1; i >= 0; i--) {\n                            if (messages[i].sender === 'user' && messages[i].content) {\n                              userQuestion = messages[i].content;\n                              break;\n                            }\n                          }\n                          \n                          console.log('🔍 DEBUG: Found user question for AI message:', { aiMessageId: message.id, userQuestion });\n\n                          // Conditional display logic based on answer sources\n                          const components = [];\n                          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                          let answerSource = '';\n\n                          // Case 1: Answer exists from both uploaded documents and websites\n                          if (hasDocumentContent && hasWebsiteContent) {\n                            answerSource = 'document_and_website';\n\n                            // Document answer card\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const documentName = documentSourceItems.length === 1\n                              ? documentSourceItems[0].text.split(' – ')[0]\n                              : 'Uploaded Documents';\n\n                            components.push(\n                              <div key=\"document-card\" className=\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-blue-800 text-sm mb-3 flex items-center\">\n                                  📄 Answer from {documentName}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.document_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                \n                                {/* Display Visual Content with Smart Fallback */}\n                                {(() => {\n                                  const visualSources = message.document_sources?.filter(source => \n                                    typeof source === 'object' && source.visual_content\n                                  ) || [];\n                                  \n                                  // First try to find sources that match the user's specific request\n                                  let relevantSources = visualSources.filter(source => \n                                    filterVisualContent(source as Source, userQuestion)\n                                  );\n                                  \n                                  // If no specific matches and user asked for images, show any available images\n                                  if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                                    relevantSources = visualSources.filter(source => \n                                      (source as Source).content_type === 'image'\n                                    );\n                                    console.log('🔍 DEBUG: No specific project matches, showing all available images:', relevantSources.length);\n                                  }\n                                  \n                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (\n                                    <div className=\"mt-4\">\n                                      <h5 className=\"text-sm font-semibold text-blue-800 mb-3\">📊 Visual Content:</h5>\n                                      <div className=\"space-y-3\">\n                                        {relevantSources.map((source, index) => (\n                                          <VisualContent key={index} source={source as Source} />\n                                        ))}\n                                      </div>\n                                    </div>\n                                  ) : null;\n                                })()}\n                                \n                                <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                                  <p className=\"text-xs text-blue-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={documentSourceItems} maxVisible={5} />\n                                </div>\n                              </div>\n                            );\n\n                            // Website answer card\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n\n                            components.push(\n                              <div key=\"website-card\" className=\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-green-800 text-sm mb-3 flex items-center\">\n                                  🌐 Answer from {websiteLabel}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.website_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-green-200\">\n                                  <p className=\"text-xs text-green-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={websiteSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 2: Answer exists only from documents\n                          } else if (hasDocumentContent) {\n                            answerSource = 'document_only';\n\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const documentName = documentSourceItems.length === 1\n                              ? documentSourceItems[0].text.split(' – ')[0]\n                              : 'Uploaded Documents';\n\n                            components.push(\n                              <div key=\"document-priority\" className=\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-blue-800 text-sm mb-3 flex items-center\">\n                                  📄 Answer from Uploaded Documents\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.document_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                \n                                {/* Display Visual Content with Smart Fallback */}\n                                {(() => {\n                                  const visualSources = message.document_sources?.filter(source => \n                                    typeof source === 'object' && source.visual_content\n                                  ) || [];\n                                  \n                                  // First try to find sources that match the user's specific request\n                                  let relevantSources = visualSources.filter(source => \n                                    filterVisualContent(source as Source, userQuestion)\n                                  );\n                                  \n                                  // If no specific matches and user asked for images, show any available images\n                                  if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                                    relevantSources = visualSources.filter(source => \n                                      (source as Source).content_type === 'image'\n                                    );\n                                    console.log('🔍 DEBUG: No specific project matches for document-only case, showing all available images:', relevantSources.length);\n                                  }\n                                  \n                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (\n                                    <div className=\"mt-4\">\n                                      <h5 className=\"text-sm font-semibold text-blue-800 mb-3\">📊 Visual Content:</h5>\n                                      <div className=\"space-y-3\">\n                                        {relevantSources.map((source, index) => (\n                                          <VisualContent key={index} source={source as Source} />\n                                        ))}\n                                      </div>\n                                    </div>\n                                  ) : null;\n                                })()}\n                                \n                                <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                                  <p className=\"text-xs text-blue-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={documentSourceItems} maxVisible={5} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 3: Answer exists only from websites\n                          } else if (hasWebsiteContent) {\n                            answerSource = 'website_only';\n\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n\n                            components.push(\n                              <div key=\"website-priority\" className=\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-green-800 text-sm mb-3 flex items-center\">\n                                  🌐 Answer from Extracted Websites\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.website_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-green-200\">\n                                  <p className=\"text-xs text-green-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={websiteSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 4: No answer from either - fallback to LLM\n                          } else if (hasLLMFallback) {\n                            answerSource = 'llm';\n\n                            const modelName = message.llm_model || 'Gemini';\n                            const modelLogo = message.llm_model?.includes('chatgpt') ? '🤖' :\n                                            message.llm_model?.includes('groq') ? '⚡' :\n                                            message.llm_model?.includes('deepseek') ? '🔍' :\n                                            message.llm_model?.includes('qwen') ? '🌐' :\n                                            message.llm_model?.includes('ollama') ? '🏠' :\n                                            message.llm_model?.includes('huggingface') ? '🤗' : '🧠';\n\n                            // Only show the LLM fallback card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"llm-fallback\" className=\"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-purple-800 text-sm mb-3 flex items-center\">\n                                  {modelLogo} Answer generated by {modelName}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't find any relevant information to answer your question.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Gemini'}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-purple-200\">\n                                  <p className=\"text-xs text-purple-600 italic\">\n                                    This answer was generated by an AI model as no relevant information was found in your documents or websites.\n                                  </p>\n                                </div>\n                              </div>\n                            );\n                            }\n\n                          // Case 5: No sources found and fallback disabled (or similar edge case)\n                          } else {\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            answerSource = 'no_results';\n\n                            // Only show the \"no results\" card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"no-results\" className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                                <p className=\"text-sm text-gray-600 mb-2\">No sources found</p>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Gemini'}\n                                  chatId={message.chatId}\n                                />\n                              </div>\n                            );\n                            }\n                          }\n\n                          // If we have components to display, render them\n                          if (components.length > 0) {\n                            return (\n                              <div className=\"mt-3\">\n                                {components}\n                              </div>\n                            );\n                          }\n\n                          // Fallback for any unhandled edge cases (should rarely happen)\n                          console.warn(\"Frontend: Unhandled rendering case\");\n                          return (\n                            <div className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                              <p className=\"text-sm text-gray-600 mb-2\">Rendering Error</p>\n                              <InteractiveAnswer\n                                content={message.content || \"An error occurred while rendering the response.\"}\n                                query={userQuestion}\n                                model={message.llm_model || 'Gemini'}\n                                chatId={message.chatId}\n                              />\n                            </div>\n                          );\n                        })()}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n              <div ref={messagesEndRef} style={{ float: 'left', clear: 'both' }} />\n            </div>\n          )}\n        </div>\n\n        {/* Fixed Chat Input Box at Bottom - using fixed positioning */}\n        <div className={`border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen ? 'lg:left-80 left-0' : 'left-0'}`}>\n          <form onSubmit={handleSendMessage} className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 relative flex\">\n              <input\n                type=\"text\"\n                value={input}\n                onChange={(e) => {\n                  const newValue = e.target.value;\n                  setInput(newValue);\n                  // Don't handle command shortcuts as you type, only on submit\n                }}\n                placeholder=\"Type your message... (/model, /reset, /clear)\"\n                className=\"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\n                disabled={isSubmitting}\n                aria-label=\"Message input\"\n              />\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <LLMSelector\n                currentModel={activeLLMModel}\n                onModelChange={(modelId) => setActiveLLMModel(modelId)}\n                isLoading={isSubmitting}\n              />\n              <button\n                type=\"submit\"\n                disabled={isSubmitting || !input.trim()}\n                className=\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\"\n                title=\"Send message\"\n              >\n                <span>{isSubmitting ? \"Sending...\" : \"Send\"}</span>\n              </button>\n            </div>\n          </form>\n                      <div className=\"text-xs text-gray-400 mt-1 text-center\">\n            Current model: {DEFAULT_LLM_MODELS.find(m => m.id === activeLLMModel)?.name || activeLLMModel}\n          </div>\n        </div>\n        {/* Spacer div to push content up above the fixed input box - only needed when there are messages */}\n        {messages.length > 0 && <div className=\"h-36\"></div>}\n      </div>\n    </div>\n  );\n}\n\ninterface AppProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction App({ sidebarOpen, setSidebarOpen }: AppProps) {\n  return (\n    <ChatInterface sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,WAAW,CAClB,OAASC,SAAS,KAAQ,gBAAgB,CAC1C,MAAO,CAAAC,WAAW,EAAIC,kBAAkB,KAAQ,6BAA6B,CAC7E,MAAO,CAAAC,iBAAiB,KAAM,mCAAmC,CACjE,MAAO,CAAAC,WAAW,KAAM,+BAA+B,CACvD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,OAASC,cAAc,KAAQ,wBAAwB,CAiBvD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOA,QAAS,CAAAC,aAAaA,CAAAC,IAAA,CAAsD,KAAAC,qBAAA,IAArD,CAAEC,WAAW,CAAEC,cAAmC,CAAC,CAAAH,IAAA,CACxE,KAAM,CACJI,cAAc,CACdC,QAAQ,CACRC,aAAa,CACbC,eAAe,CACfC,UAAU,CACVC,aAAa,CACbC,gBACF,CAAC,CAAGhB,cAAc,CAAC,CAAC,CAEpB,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAAC6B,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC+B,cAAc,CAAEC,iBAAiB,CAAC,CAAGhC,QAAQ,CAAC,kBAAkB,CAAC,CAAE;AAC1E,KAAM,CAACiC,eAAe,CAAEC,kBAAkB,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACmC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpC,QAAQ,CAAmG,cAAc,CAAC,CAC9K,KAAM,CAAAqC,cAAc,CAAGpC,MAAM,CAAiB,IAAI,CAAC,CAEnD;AACAC,SAAS,CAAC,IAAM,CACd,GAAImC,cAAc,CAACC,OAAO,CAAE,CAC1BD,cAAc,CAACC,OAAO,CAACC,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAC/D,CACF,CAAC,CAAE,CAACnB,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAoB,uBAAuB,CAAGA,CAACC,YAAoB,CAAEC,cAAsB,GAAc,CACzFC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAE,CAAEH,YAAY,CAAEI,oBAAoB,CAAEH,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEI,MAAO,CAAC,CAAC,CAE5H,KAAM,CAAAC,SAAS,CAAGN,YAAY,CAACO,WAAW,CAAC,CAAC,CAE5C;AACA,GAAID,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,CAAE,CAC/BN,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC,CACrF,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAM,cAAc,CAAGH,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,CACnJ,KAAM,CAAAE,iBAAiB,CAAG,CAACJ,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC,IAC/DF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAC7J,KAAM,CAAAG,iBAAiB,CAAGL,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC,CAEvI,KAAM,CAAAI,WAAW,CAAG,CAAAX,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEM,WAAW,CAAC,CAAC,GAAI,EAAE,CACvD,KAAM,CAAAM,gBAAgB,CAAGD,WAAW,CAACJ,QAAQ,CAAC,SAAS,CAAC,EAAII,WAAW,CAACJ,QAAQ,CAAC,GAAG,CAAC,EAC5DI,WAAW,CAACJ,QAAQ,CAAC,OAAO,CAAC,EAAII,WAAW,CAACP,MAAM,CAAG,GAAI,CACnF,KAAM,CAAAS,mBAAmB,CAAGF,WAAW,CAACJ,QAAQ,CAAC,OAAO,CAAC,EAAII,WAAW,CAACJ,QAAQ,CAAC,QAAQ,CAAC,EAAII,WAAW,CAACJ,QAAQ,CAAC,SAAS,CAAC,CAC9H,KAAM,CAAAO,gBAAgB,CAAGH,WAAW,CAACP,MAAM,CAAG,EAAE,CAEhD;AACA,GAAIM,iBAAiB,EAAKF,cAAc,GAAKH,SAAS,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAE,CAAE,CAC1GN,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC,CACtF,MAAO,KAAI,CACb,CAEA;AACA,GAAIO,iBAAiB,EAAI,CAACD,cAAc,CAAE,CACxCP,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC,CAChF,MAAO,MAAK,CACd,CAEA;AACA,KAAM,CAAAa,UAAU,CAAG,CAACH,gBAAgB,EAAI;AACtB,CAACC,mBAAmB,GAAI;AAErBL,cAAc,EAAI,CAACM,gBAAgB,EAAK;AACxC,CAACA,gBAAgB,EAAIH,WAAW,CAACP,MAAM,CAAG,EAAI;AAAA,CAChD,CAEnBH,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAEa,UAAU,CAAC,CAC/E,MAAO,CAAAA,UAAU,CACnB,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAGA,CAACC,MAAuB,CAAElB,YAAoB,GAAK,CAC7E,GAAI,MAAO,CAAAkB,MAAM,GAAK,QAAQ,EAAI,CAACA,MAAM,CAACC,cAAc,CAAE,CACxDjB,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAE,MAAO,CAAAe,MAAM,CAAE,CAAC,EAAEA,MAAM,SAANA,MAAM,WAANA,MAAM,CAAaC,cAAc,EAAC,CAC9H,MAAO,MAAK,CACd,CAEA,KAAM,CAAAC,cAAc,CAAGpB,YAAY,CAACO,WAAW,CAAC,CAAC,CACjD,KAAM,CAAAc,SAAS,CAAGH,MAAgB,CAElChB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAE,CAC3DmB,YAAY,CAAED,SAAS,CAACC,YAAY,CACpCC,kBAAkB,CAAE,CAAC,CAACF,SAAS,CAACF,cAAc,CAC9CK,UAAU,CAAEJ,cAAc,CAC1BK,QAAQ,CAAEJ,SAAS,CAACI,QAAQ,CAC5BC,IAAI,CAAEL,SAAS,CAACK,IAAI,CACpBC,qBAAqB,CAAEN,SAAS,CAACF,cAAc,CAAGS,IAAI,CAACC,SAAS,CAACR,SAAS,CAACF,cAAc,CAAC,CAACW,SAAS,CAAC,CAAC,CAAE,GAAG,CAAC,CAAG,MACjH,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,YAAY,CAAGX,cAAc,CAACY,KAAK,CAAC,iBAAiB,CAAC,CAC5D,KAAM,CAAAC,cAAc,CAAGb,cAAc,CAACY,KAAK,CAAC,mBAAmB,CAAC,CAEhE;AACA,KAAM,CAAAE,iBAAiB,CAAG,CACxB,qJAAqJ,CACrJ,qEAAqE,CACrE,8BAA8B,CAC/B,CAED,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,IAAK,KAAM,CAAAC,OAAO,GAAI,CAAAF,iBAAiB,CAAE,CACvC,KAAM,CAAAF,KAAK,CAAGhC,YAAY,CAACgC,KAAK,CAACI,OAAO,CAAC,CACzC,GAAIJ,KAAK,CAAE,CACTG,YAAY,CAAGH,KAAK,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,CAC9B,MACF,CACF,CAEA;AACA,KAAM,CAAAC,gBAAgB,CAAGV,IAAI,CAACC,SAAS,CAACR,SAAS,CAACF,cAAc,EAAI,CAAC,CAAC,CAAC,CAACZ,WAAW,CAAC,CAAC,CAErF;AACA,GAAI4B,YAAY,EAAIf,cAAc,CAACZ,QAAQ,CAAC,MAAM,CAAC,CAAE,KAAA+B,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACnD,KAAM,CAAAC,gBAAgB,CAAGR,YAAY,CAAC5B,WAAW,CAAC,CAAC,CACnD,KAAM,CAAAqC,OAAO,CAAGvB,SAAS,CAACC,YAAY,GAAK,OAAO,CAElDpB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAE,CAC5C0C,OAAO,CAAEV,YAAY,CACrBS,OAAO,CACPE,sBAAsB,CAAE,CAAC,GAAAP,qBAAA,CAAElB,SAAS,CAACF,cAAc,UAAAoB,qBAAA,WAAxBA,qBAAA,CAA0BQ,kBAAkB,CAAC,CACxEA,kBAAkB,CAAE,EAAAP,sBAAA,CAAAnB,SAAS,CAACF,cAAc,UAAAqB,sBAAA,iBAAxBA,sBAAA,CAA0BO,kBAAkB,GAAI,EAAE,CACtEC,QAAQ,CAAE,EAAAP,sBAAA,CAAApB,SAAS,CAACF,cAAc,UAAAsB,sBAAA,iBAAxBA,sBAAA,CAA0BO,QAAQ,GAAI,EAAE,CAClDC,OAAO,CAAE,EAAAP,sBAAA,CAAArB,SAAS,CAACF,cAAc,UAAAuB,sBAAA,iBAAxBA,sBAAA,CAA0BO,OAAO,GAAI,KAChD,CAAC,CAAC,CAEF,GAAIL,OAAO,CAAE,KAAAM,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACX;AACA,KAAM,CAAAC,mBAAmB,CAAGf,gBAAgB,CAAC9B,QAAQ,CAACmC,gBAAgB,CAAC,CACvE,KAAM,CAAAW,oBAAoB,CAAGjC,SAAS,CAACI,QAAQ,EAAIJ,SAAS,CAACI,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACmC,gBAAgB,CAAC,CAE9G;AACA,KAAM,CAAAY,iBAA2B,CAAG,EAAAL,sBAAA,CAAA7B,SAAS,CAACF,cAAc,UAAA+B,sBAAA,iBAAxBA,sBAAA,CAA0BH,kBAAkB,GAAI,EAAE,CACtF,KAAM,CAAAS,kBAAkB,CAAGD,iBAAiB,CAACE,IAAI,CAAEZ,OAAe,EAChEA,OAAO,CAACtC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACmC,gBAAgB,CAAC,EAChDA,gBAAgB,CAACnC,QAAQ,CAACqC,OAAO,CAACtC,WAAW,CAAC,CAAC,CACjD,CAAC,CAED;AACA,KAAM,CAAAmD,OAAO,CAAG,EAAAP,sBAAA,CAAA9B,SAAS,CAACF,cAAc,UAAAgC,sBAAA,iBAAxBA,sBAAA,CAA0BH,QAAQ,GAAI,EAAE,CACxD,KAAM,CAAAW,WAAW,CAAGD,OAAO,CAACnD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACmC,gBAAgB,CAAC,CAEpE;AACA,KAAM,CAAAiB,MAAM,CAAG,EAAAR,sBAAA,CAAA/B,SAAS,CAACF,cAAc,UAAAiC,sBAAA,iBAAxBA,sBAAA,CAA0BH,OAAO,GAAI,KAAK,CAEzD,KAAM,CAAAY,cAAc,CAAGR,mBAAmB,EAAIC,oBAAoB,EAAIE,kBAAkB,EAAIG,WAAW,EAAIC,MAAM,CAEjH1D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAE,CACnD0C,OAAO,CAAEV,YAAY,CACrBkB,mBAAmB,CACnBC,oBAAoB,CACpBE,kBAAkB,CAClBG,WAAW,CACXC,MAAM,CACNC,cAAc,CACdC,WAAW,CAAElB,OAAO,EAAIiB,cAC1B,CAAC,CAAC,CAEF,MAAO,CAAAA,cAAc,CACvB,CAEA,MAAO,MAAK,CAAE;AAChB,CAEA;AACA,GAAI9B,YAAY,EAAIX,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,CAAE,KAAAuD,sBAAA,CAAAC,sBAAA,CACpD,KAAM,CAAAC,aAAa,CAAGlC,YAAY,CAAC,CAAC,CAAC,CACrC,KAAM,CAAAa,OAAO,CAAGvB,SAAS,CAACC,YAAY,GAAK,OAAO,CAElD;AACA,KAAM,CAAA4C,mBAAmB,CAAG5B,gBAAgB,CAAC9B,QAAQ,CAAC,WAAWyD,aAAa,EAAE,CAAC,EACrD3B,gBAAgB,CAAC9B,QAAQ,CAAC,UAAUyD,aAAa,EAAE,CAAC,CAChF,KAAM,CAAAE,oBAAoB,CAAG9C,SAAS,CAACI,QAAQ,EAAIJ,SAAS,CAACI,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAWyD,aAAa,EAAE,CAAC,CACxH,KAAM,CAAAG,eAAe,CAAG/C,SAAS,CAACK,IAAI,EAAI2C,IAAI,CAACC,GAAG,CAACjD,SAAS,CAACK,IAAI,CAAG6C,QAAQ,CAACN,aAAa,CAAC,CAAC,EAAI,CAAC,CAAE;AACnG,KAAM,CAAAO,iBAAiB,CAAG,EAAAT,sBAAA,CAAA1C,SAAS,CAACF,cAAc,UAAA4C,sBAAA,iBAAxBA,sBAAA,CAA0BU,eAAe,GAC1CpD,SAAS,CAACF,cAAc,CAACsD,eAAe,CAACjE,QAAQ,CAAC,WAAWyD,aAAa,EAAE,CAAC,CAEtG,KAAM,CAAAS,iBAAiB,CAAGR,mBAAmB,EAAIC,oBAAoB,EAAIC,eAAe,EAAII,iBAAiB,CAE7GtE,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE8D,aAAa,CAAE,eAAe,CAAE,CAC/DrB,OAAO,CACPsB,mBAAmB,CACnBC,oBAAoB,CACpBC,eAAe,CACfI,iBAAiB,CACjBE,iBAAiB,CACjBC,uBAAuB,CAAErC,gBAAgB,CAACR,SAAS,CAAC,CAAC,CAAE,GAAG,CAAC,CAC3D2C,eAAe,EAAAT,sBAAA,CAAE3C,SAAS,CAACF,cAAc,UAAA6C,sBAAA,iBAAxBA,sBAAA,CAA0BS,eAAe,CAC1D/C,IAAI,CAAEL,SAAS,CAACK,IAAI,CACpBoC,WAAW,CAAElB,OAAO,EAAI8B,iBAC1B,CAAC,CAAC,CAEF;AACA;AACA,MAAO,CAAA9B,OAAO,EAAI8B,iBAAiB,CACrC,CAEA;AACA,GAAIzC,cAAc,EAAIb,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,CAAE,CACtD,KAAM,CAAAoE,eAAe,CAAG3C,cAAc,CAAC,CAAC,CAAC,CACzC,KAAM,CAAA4C,OAAO,CAAGxD,SAAS,CAACC,YAAY,GAAK,OAAO,CAElD;AACA,KAAM,CAAAwD,mBAAmB,CAAGxC,gBAAgB,CAAC9B,QAAQ,CAAC,aAAaoE,eAAe,EAAE,CAAC,EACzDtC,gBAAgB,CAAC9B,QAAQ,CAAC,YAAYoE,eAAe,EAAE,CAAC,EACxDtC,gBAAgB,CAAC9B,QAAQ,CAAC,SAASoE,eAAe,EAAE,CAAC,CAEjF1E,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEyE,eAAe,CAAE,eAAe,CAAE,CACnEC,OAAO,CACPC,mBAAmB,CACnBhB,WAAW,CAAEe,OAAO,EAAIC,mBAC1B,CAAC,CAAC,CAEF,MAAO,CAAAD,OAAO,EAAIC,mBAAmB,CACvC,CAEA;AACA,GAAI1D,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,EAAI,CAACY,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,CAAE,CACzE,KAAM,CAAAoC,OAAO,CAAGvB,SAAS,CAACC,YAAY,GAAK,OAAO,CAClDpB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAEyC,OAAO,CAAC,CAC9E,MAAO,CAAAA,OAAO,CAChB,CAEA;AACA,GAAIxB,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,EAAI,CAACY,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,CAAE,CACzE,KAAM,CAAAqE,OAAO,CAAGxD,SAAS,CAACC,YAAY,GAAK,OAAO,CAClDpB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAE0E,OAAO,CAAC,CAC9E,MAAO,CAAAA,OAAO,CAChB,CAEA;AACA3E,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC,CAC9D,MAAO,KAAI,CACb,CAAC,CAED;AACA,KAAM,CAAA4E,qBAAqB,CAAI9F,KAAa,EAAK,CAC/C;AACA,GAAIA,KAAK,CAAC+F,UAAU,CAAC,GAAG,CAAC,CAAE,CACzB,KAAM,CAAAC,OAAO,CAAGhG,KAAK,CAACiG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC3E,WAAW,CAAC,CAAC,CAEjD;AACA,GAAI0E,OAAO,GAAK,QAAQ,CAAE,CACxB,KAAM,CAAAE,QAAQ,CAAGlG,KAAK,CAAC6C,SAAS,CAAC,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC,CAC1C,KAAM,CAAA+C,YAAY,CAAGzH,kBAAkB,CAAC0H,IAAI,CAACC,CAAC,EAC5CA,CAAC,CAACC,IAAI,CAAChF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC2E,QAAQ,CAAC5E,WAAW,CAAC,CAAC,CAAC,EACrD+E,CAAC,CAACE,EAAE,CAACjF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC2E,QAAQ,CAAC5E,WAAW,CAAC,CAAC,CACpD,CAAC,CAED,GAAI6E,YAAY,EAAIA,YAAY,CAACK,OAAO,CAAE,CACxCnG,iBAAiB,CAAC8F,YAAY,CAACI,EAAE,CAAC,CAClCtG,QAAQ,CAAC,EAAE,CAAC,CACZ,MAAO,WAAW,CACpB,CACF,CAEA;AAAA,IACK,IAAI+F,OAAO,GAAK,QAAQ,EAAIA,OAAO,GAAK,QAAQ,CAAE,CACrDjG,gBAAgB,CAAC,CAAC,CAClBE,QAAQ,CAAC,EAAE,CAAC,CACZ,MAAO,WAAW,CACpB,CACF,CAEA,MAAO,eAAe,CACxB,CAAC,CAED,KAAM,CAAAwG,iBAAiB,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACtDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAAC3G,KAAK,CAACoD,IAAI,CAAC,CAAC,EAAIlD,YAAY,CAAE,OAEnC;AACA,GAAIF,KAAK,CAAC+F,UAAU,CAAC,GAAG,CAAC,CAAE,CACzB,KAAM,CAAAa,MAAM,CAAGd,qBAAqB,CAAC9F,KAAK,CAAC,CAC3C,GAAI4G,MAAM,GAAK,WAAW,CAAE,CAC1B3G,QAAQ,CAAC,EAAE,CAAC,CACZ,OACF,CACA;AACF,CAEA,MAAO,MAAM,CAAA4G,eAAe,CAAC7G,KAAK,CAAC,CACrC,CAAC,CAED,KAAM,CAAA6G,eAAe,CAAG,KAAO,CAAAC,WAAmB,EAAK,KAAAC,qBAAA,CACrD;AACA,GAAI,CAACtH,cAAc,CAAE,CACnB,KAAM,CAAAE,aAAa,CAAC,CAAC,CACvB,CAEA,KAAM,CAAAqH,WAAwB,CAAG,CAC/BT,EAAE,CAAE,QAAQU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CACxBC,OAAO,CAAEL,WAAW,CACpBM,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CACnCC,MAAM,CAAE,CAAA9H,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE8G,EAAE,GAAI,MAChC,CAAC,CAED1G,UAAU,CAACmH,WAAW,CAAC,CACvB/G,QAAQ,CAAC,EAAE,CAAC,CAEZ,KAAM,CAAAuH,SAAS,CAAGP,IAAI,CAACC,GAAG,CAAC,CAAC,CAC5B,KAAM,CAAAO,aAA0B,CAAG,CACjClB,EAAE,CAAE,MAAMiB,SAAS,EAAE,CACrBL,OAAO,CAAE,EAAE,CACXC,MAAM,CAAE,IAAI,CACZM,OAAO,CAAE,IAAI,CACbL,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CACnCK,SAAS,CAAEvH,cAAc,CACzBmH,MAAM,CAAE,CAAA9H,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE8G,EAAE,GAAI,MAChC,CAAC,CAED1G,UAAU,CAAC4H,aAAa,CAAC,CACzB,CAAAV,qBAAA,CAAArG,cAAc,CAACC,OAAO,UAAAoG,qBAAA,iBAAtBA,qBAAA,CAAwBnG,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAE9DV,eAAe,CAAC,IAAI,CAAC,CACrBI,kBAAkB,CAAC,IAAI,CAAC,CACxBE,qBAAqB,CAAC,cAAc,CAAC,CAErC,GAAI,KAAAmH,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CACF,CAAAH,sBAAA,CAAAlH,cAAc,CAACC,OAAO,UAAAiH,sBAAA,iBAAtBA,sBAAA,CAAwBhH,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAE9D;AACAmH,UAAU,CAAC,IAAMvH,qBAAqB,CAAC,qBAAqB,CAAC,CAAE,GAAG,CAAC,CACnEuH,UAAU,CAAC,IAAMvH,qBAAqB,CAAC,oBAAoB,CAAC,CAAE,IAAI,CAAC,CACnEuH,UAAU,CAAC,IAAMvH,qBAAqB,CAAC,mBAAmB,CAAC,CAAE,IAAI,CAAC,CAElE,KAAM,CAAAwH,QAAQ,CAAG,KAAM,CAAAzJ,SAAS,CAACsI,WAAW,CAAE1G,cAAc,CAAC,CAE7D;AACA,KAAM,CAAA8H,SAAsB,CAAG,CAC7B3B,EAAE,CAAE,MAAMiB,SAAS,EAAE,CACrBL,OAAO,CAAEc,QAAQ,CAACE,MAAM,CACxBC,eAAe,CAAEH,QAAQ,CAACG,eAAe,CACzCC,cAAc,CAAEJ,QAAQ,CAACI,cAAc,CACvCV,SAAS,CAAEM,QAAQ,CAACN,SAAS,EAAIvH,cAAc,CAC/CgH,MAAM,CAAE,IAAI,CACZkB,OAAO,CAAEL,QAAQ,CAACK,OAAO,CACzBC,gBAAgB,CAAEN,QAAQ,CAACM,gBAAgB,CAC3CC,eAAe,CAAEP,QAAQ,CAACO,eAAe,CACzCjB,MAAM,CAAE,CAAA9H,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE8G,EAAE,GAAI,MAAM,CACpCkC,YAAY,CAAER,QAAQ,CAACQ,YACzB,CAAC,CAED;AACAxH,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CACjCkH,eAAe,CAAEH,QAAQ,CAACG,eAAe,CAAG,QAAQ,CAAG,SAAS,CAChEC,cAAc,CAAEJ,QAAQ,CAACI,cAAc,CAAG,QAAQ,CAAG,SAAS,CAC9DK,sBAAsB,CAAE,EAAAb,qBAAA,CAAAI,QAAQ,CAACM,gBAAgB,UAAAV,qBAAA,iBAAzBA,qBAAA,CAA2BzG,MAAM,GAAI,CAAC,CAC9DuH,qBAAqB,CAAE,EAAAb,qBAAA,CAAAG,QAAQ,CAACO,eAAe,UAAAV,qBAAA,iBAAxBA,qBAAA,CAA0B1G,MAAM,GAAI,CAAC,CAC5DqH,YAAY,CAAER,QAAQ,CAACQ,YACzB,CAAC,CAAC,CAEF;AACAxH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAE+G,QAAQ,CAACM,gBAAgB,CAAC,CAC9E,GAAIN,QAAQ,CAACM,gBAAgB,CAAE,CAC7BN,QAAQ,CAACM,gBAAgB,CAACK,OAAO,CAAC,CAAC3G,MAAM,CAAE4G,KAAK,GAAK,CACnD5H,OAAO,CAACC,GAAG,CAAC,oBAAoB2H,KAAK,GAAG,CAAE,CACxCxG,YAAY,CAAEJ,MAAM,CAACI,YAAY,CACjCC,kBAAkB,CAAE,CAAC,CAACL,MAAM,CAACC,cAAc,CAC3C4G,WAAW,CAAE7G,MAAM,CAAC6G,WAAW,CAC/BC,YAAY,CAAE9G,MAAM,CAAC8G,YAAY,CACjCC,mBAAmB,CAAE/G,MAAM,CAACC,cAAc,CAAG+G,MAAM,CAACC,IAAI,CAACjH,MAAM,CAACC,cAAc,CAAC,CAAG,EACpF,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEApC,aAAa,CAAC2H,aAAa,CAAClB,EAAE,CAAE2B,SAAS,CAAC,CAE1C,CAAAH,sBAAA,CAAArH,cAAc,CAACC,OAAO,UAAAoH,sBAAA,iBAAtBA,sBAAA,CAAwBnH,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAE,MAAOsI,KAAK,CAAE,CACdlI,OAAO,CAACkI,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAE9C;AACA,KAAM,CAAAC,YAAyB,CAAG,CAChC7C,EAAE,CAAE,MAAMiB,SAAS,EAAE,CACrBL,OAAO,CAAE,iNAAiN,CAC1NC,MAAM,CAAE,IAAI,CACZG,MAAM,CAAE,CAAA9H,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE8G,EAAE,GAAI,MAAM,CACpCkC,YAAY,CAAE,IAChB,CAAC,CAED3I,aAAa,CAAC2H,aAAa,CAAClB,EAAE,CAAE6C,YAAY,CAAC,CAC/C,CAAC,OAAS,CACRjJ,eAAe,CAAC,KAAK,CAAC,CACtBI,kBAAkB,CAAC,KAAK,CAAC,CACzBE,qBAAqB,CAAC,UAAU,CAAC,CACnC,CACF,CAAC,CAID,KAAM,CAAA4I,sBAAsB,CAAIf,OAAgC,EAAK,CACnE,GAAI,CAACA,OAAO,EAAIA,OAAO,CAAClH,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAE/C;AACA,KAAM,CAAAkI,cAAwE,CAAG,CAAC,CAAC,CAEnFhB,OAAO,CAACM,OAAO,CAAC3G,MAAM,EAAI,CACxB,GAAI,CAAAO,QAAgB,CACpB,GAAI,CAAAC,IAAY,CAEhB,GAAI,MAAO,CAAAR,MAAM,GAAK,QAAQ,CAAE,CAC9B;AACA,KAAM,CAAAc,KAAK,CAAGd,MAAM,CAACc,KAAK,CAAC,kCAAkC,CAAC,CAC9DP,QAAQ,CAAGO,KAAK,CAAGA,KAAK,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,CAAGnB,MAAM,CAC3CQ,IAAI,CAAGM,KAAK,EAAIA,KAAK,CAAC,CAAC,CAAC,CAAGuC,QAAQ,CAACvC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAG,CAAC,CACnD,CAAC,IAAM,CACLP,QAAQ,CAAGP,MAAM,CAACqE,IAAI,EAAIrE,MAAM,CAACO,QAAQ,EAAI,kBAAkB,CAC/DC,IAAI,CAAGR,MAAM,CAACQ,IAAI,EAAI,CAAC,CACvB,CAEF,GAAI,CAAC6G,cAAc,CAAC9G,QAAQ,CAAC,CAAE,CAC7B8G,cAAc,CAAC9G,QAAQ,CAAC,CAAG,CAAEA,QAAQ,CAAE+G,KAAK,CAAE,EAAG,CAAC,CACpD,CAEA,GAAI,CAACD,cAAc,CAAC9G,QAAQ,CAAC,CAAC+G,KAAK,CAAChI,QAAQ,CAACkB,IAAI,CAAC,CAAE,CAClD6G,cAAc,CAAC9G,QAAQ,CAAC,CAAC+G,KAAK,CAACC,IAAI,CAAC/G,IAAI,CAAC,CAC3C,CACF,CAAC,CAAC,CAEF;AACA,MAAO,CAAAwG,MAAM,CAACQ,MAAM,CAACH,cAAc,CAAC,CAACI,GAAG,CAACC,KAAK,EAAI,CAChD,KAAM,CAAAC,WAAW,CAAGD,KAAK,CAACJ,KAAK,CAACM,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAGC,CAAC,CAAC,CACrD,KAAM,CAAAC,QAAQ,CAAGJ,WAAW,CAACxI,MAAM,GAAK,CAAC,CACrC,QAAQwI,WAAW,CAAC,CAAC,CAAC,EAAE,CACxB,SAASA,WAAW,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAErC;AACA,MAAO,CACLC,IAAI,CAAE,GAAGP,KAAK,CAACnH,QAAQ,MAAMwH,QAAQ,EAAE,CACvCG,IAAI,CAAE,gBAAgBC,kBAAkB,CAACT,KAAK,CAACnH,QAAQ,CAAC,SAASoH,WAAW,CAAC,CAAC,CAAC,EAAE,CACjFS,UAAU,CAAE,IACd,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAIhC,OAAgC,EAAK,CAClE,GAAI,CAACA,OAAO,EAAIA,OAAO,CAAClH,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAE/C;AACA,KAAM,CAAAmJ,UAAU,CAAG,GAAI,CAAAC,GAAG,CAAS,CAAC,CACpC,KAAM,CAAAC,SAAsE,CAAG,EAAE,CAEjFnC,OAAO,CAACM,OAAO,CAAC3G,MAAM,EAAI,CACxB,GAAI,CAAAyI,GAAW,CACf,GAAI,CAAAC,WAAmB,CAEvB,GAAI,MAAO,CAAA1I,MAAM,GAAK,QAAQ,CAAE,CAC9ByI,GAAG,CAAGzI,MAAM,CAAC8D,UAAU,CAAC,MAAM,CAAC,CAAG9D,MAAM,CAAG,WAAWA,MAAM,EAAE,CAC5D,GAAI,CACJ,KAAM,CAAA2I,MAAM,CAAG,GAAI,CAAAC,GAAG,CAACH,GAAG,CAAC,CAC3BC,WAAW,CAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAC,CACnD,CAAE,KAAM,CACRJ,WAAW,CAAG1I,MAAM,CACtB,CACF,CAAC,IAAM,CACLyI,GAAG,CAAGzI,MAAM,CAACyI,GAAG,EAAI,uCAAuC,CAC3D,GAAI,CACF,KAAM,CAAAE,MAAM,CAAG,GAAI,CAAAC,GAAG,CAACH,GAAG,CAAC,CAC3BC,WAAW,CAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAC,CACrD,CAAE,KAAM,CACNJ,WAAW,CAAGD,GAAG,CACnB,CACF,CAEA,GAAI,CAACH,UAAU,CAACS,GAAG,CAACN,GAAG,CAAC,CAAE,CACxBH,UAAU,CAACU,GAAG,CAACP,GAAG,CAAC,CACnBD,SAAS,CAACjB,IAAI,CAAC,CACbU,IAAI,CAAES,WAAW,CACjBR,IAAI,CAAEO,GAAG,CACTL,UAAU,CAAE,KAAO;AACrB,CAAC,CAAC,CACF,CACJ,CAAC,CAAC,CAEF,MAAO,CAAAI,SAAS,CAAE;AACpB,CAAC,CAED;AACA,KAAM,CAAAS,UAAU,CAAGC,KAAA,EAGb,IAHc,CAAEC,KAAK,CAAEC,UAAU,CAAG,CAG1C,CAAC,CAAAF,KAAA,CACC,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAGlN,QAAQ,CAAC+M,KAAK,CAAChK,MAAM,EAAIiK,UAAU,CAAC,CAEpE,GAAID,KAAK,CAAChK,MAAM,GAAK,CAAC,CAAE,MAAO,KAAI,CAEnC,KAAM,CAAAoK,YAAY,CAAGF,QAAQ,CAAGF,KAAK,CAAGA,KAAK,CAACK,KAAK,CAAC,CAAC,CAAEJ,UAAU,CAAC,CAClE,KAAM,CAAAK,OAAO,CAAGN,KAAK,CAAChK,MAAM,CAAGiK,UAAU,CAEzC,KAAM,CAAAM,mBAAmB,CAAGA,CAACjF,CAAsC,CAAEyD,IAAY,GAAK,CACpF;AACA;AACA;AAAA,CACD,CAED,mBACEhL,KAAA,OAAIyM,SAAS,CAAC,uCAAuC,CAAAC,QAAA,EAClDL,YAAY,CAAC9B,GAAG,CAAC,CAACoC,IAAI,CAAEjD,KAAK,gBACxB5J,IAAA,OAAA4M,QAAA,CACGC,IAAI,CAAC3B,IAAI,cACZlL,IAAA,MACE8M,IAAI,CAAED,IAAI,CAAC3B,IAAK,CAChB6B,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBL,SAAS,CAAC,8DAA8D,CACxEM,KAAK,CAAEJ,IAAI,CAACzB,UAAU,CAAG,4BAA4B,CAAG,yBAA0B,CAClF8B,OAAO,CAAEL,IAAI,CAACzB,UAAU,CAAI3D,CAAC,EAAKiF,mBAAmB,CAACjF,CAAC,CAAEoF,IAAI,CAAC3B,IAAK,CAAC,CAAGiC,SAAU,CAAAP,QAAA,CAE5EC,IAAI,CAAC5B,IAAI,CACT,CAAC,cAERjL,IAAA,SAAM2M,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEC,IAAI,CAAC5B,IAAI,CAAO,CAC9C,EAdMrB,KAeL,CACL,CAAC,CACL6C,OAAO,EAAI,CAACJ,QAAQ,eACnBrM,IAAA,OAAI2M,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvB1M,KAAA,WACEgN,OAAO,CAAEA,CAAA,GAAMZ,WAAW,CAAC,IAAI,CAAE,CACjCK,SAAS,CAAC,sEAAsE,CAAAC,QAAA,EACjF,IACG,CAACT,KAAK,CAAChK,MAAM,CAAGiK,UAAU,CAAC,eAC/B,EAAQ,CAAC,CACP,CACL,CACAK,OAAO,EAAIJ,QAAQ,eAClBrM,IAAA,OAAI2M,SAAS,CAAC,WAAW,CAAAC,QAAA,cACrB5M,IAAA,WACEkN,OAAO,CAAEA,CAAA,GAAMZ,WAAW,CAAC,KAAK,CAAE,CACpCK,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAC/E,WAED,CAAQ,CAAC,CACP,CACC,EACP,CAAC,CAET,CAAC,CAED;AACA,KAAM,CAAAQ,gBAAgB,CAAG,KAAO,CAAAC,WAAwB,EAAK,CAC3D;AACA,KAAM,CAAA1M,eAAe,CAAC0M,WAAW,CAAC/F,EAAE,CAAC,CACrClG,iBAAiB,CAACiM,WAAW,CAACC,UAAU,EAAI,kBAAkB,CAAC,CAC/D/M,cAAc,CAAC,KAAK,CAAC,CAAE;AACzB,CAAC,CAED,KAAM,CAAAgN,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCvL,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CACnC,KAAM,CAAAvB,aAAa,CAAC,CAAC,CACrBH,cAAc,CAAC,KAAK,CAAC,CAAE;AACzB,CAAC,CAED,mBACEL,KAAA,QAAKyM,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAEvE5M,IAAA,CAACJ,WAAW,EACV4N,SAAS,CAAEnM,eAAgB,CAC3BoM,OAAO,CAAE,CAAC,IAAM,CACd,OAAQlM,kBAAkB,EACxB,IAAK,qBAAqB,CACxB,MAAO,mCAAmC,CAC5C,IAAK,oBAAoB,CACvB,MAAO,kCAAkC,CAC3C,IAAK,mBAAmB,CACtB,MAAO,gCAAgC,CACzC,QACE,MAAO,kCAAkC,CAC7C,CACF,CAAC,EAAE,CAAE,CACLmM,SAAS,CAAC,SAAS,CACnBC,YAAY,CAAEpM,kBAAmB,CACjCjB,WAAW,CAAEA,WAAY,CAC1B,CAAC,cAGFN,IAAA,CAACL,WAAW,EACViO,MAAM,CAAEtN,WAAY,CACpBuN,QAAQ,CAAEA,CAAA,GAAMtN,cAAc,CAAC,CAACD,WAAW,CAAE,CAC7CwN,aAAa,CAAE,CAAAtN,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE8G,EAAE,GAAI,EAAG,CACxCyG,YAAY,CAAEX,gBAAiB,CAC/BY,SAAS,CAAET,aAAc,CAC1B,CAAC,cAGFrN,KAAA,QAAKyM,SAAS,CAAE,oDAAoDrM,WAAW,CAAG,UAAU,CAAG,EAAE,EAAG,CAAAsM,QAAA,eAElG5M,IAAA,QAAK2M,SAAS,CAAE,UAAUlM,QAAQ,CAAC0B,MAAM,CAAG,CAAC,CAAG,iBAAiB,CAAG,iBAAiB,YAAa,CAAAyK,QAAA,CAC/FnM,QAAQ,CAAC0B,MAAM,GAAK,CAAC,cACpBnC,IAAA,QAAK2M,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtD1M,KAAA,QAAKyM,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC5M,IAAA,MAAG2M,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,qBAAmB,CAAG,CAAC,cACjE5M,IAAA,MAAA4M,QAAA,CAAG,wCAAsC,CAAG,CAAC,EAC1C,CAAC,CACH,CAAC,cAEN1M,KAAA,QAAA0M,QAAA,EACGnM,QAAQ,CAACgK,GAAG,CAAEgD,OAAO,eACpBzN,IAAA,QAEE2M,SAAS,CAAE,QACTc,OAAO,CAACtF,MAAM,GAAK,MAAM,CAAG,kBAAkB,CAAG,oBAAoB,EACpE,CAAAyE,QAAA,cAEH1M,KAAA,QACEyM,SAAS,CAAE,2DACTc,OAAO,CAACtF,MAAM,GAAK,MAAM,CACrB,wBAAwB,CACxB,kCAAkC,EACrC,CAAAyE,QAAA,eAEH1M,KAAA,QAAKyM,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD5M,IAAA,SAAM2M,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC5Ba,OAAO,CAACtF,MAAM,GAAK,MAAM,CAAG,KAAK,CAAG,SAAS,CAC1C,CAAC,CACNsF,OAAO,CAACrF,SAAS,eAChBpI,IAAA,SAAM2M,SAAS,CAAE,gBACfc,OAAO,CAACtF,MAAM,GAAK,MAAM,CACrB,eAAe,CACf,eAAe,EAClB,CAAAyE,QAAA,CACA,GAAI,CAAA5E,IAAI,CAACyF,OAAO,CAACrF,SAAS,CAAC,CAAC6F,kBAAkB,CAAC,CAAC,CAC7C,CACP,EACE,CAAC,CAGLR,OAAO,CAACtF,MAAM,GAAK,MAAM,EAAIsF,OAAO,CAACvF,OAAO,eAC3ClI,IAAA,QAAK2M,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAEa,OAAO,CAACvF,OAAO,CAAM,CACjE,CAGAuF,OAAO,CAACtF,MAAM,GAAK,IAAI,eACtBnI,IAAA,QAAA4M,QAAA,CACG,CAAC,CAAAsB,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,GAAM,CACN;AACA,GAAIZ,OAAO,CAAChF,OAAO,EAAIpH,eAAe,EAAI,CAACoM,OAAO,CAACvF,OAAO,EAAI,CAACuF,OAAO,CAACtE,eAAe,EAAI,CAACsE,OAAO,CAACrE,cAAc,CAAE,CACjH,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAkF,mBAAmB,CAAGlE,sBAAsB,CAACqD,OAAO,CAACnE,gBAAgB,CAAC,CAC5E,KAAM,CAAAiF,kBAAkB,CAAGlD,qBAAqB,CAACoC,OAAO,CAAClE,eAAe,CAAC,CAEzE;AACA,KAAM,CAAAiF,kBAAkB,CAAG,CAAC,EAAEf,OAAO,CAACtE,eAAe,EAAIsE,OAAO,CAACtE,eAAe,CAAChF,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC/F,KAAM,CAAAsK,iBAAiB,CAAG,CAAC,EAAEhB,OAAO,CAACrE,cAAc,EAAIqE,OAAO,CAACrE,cAAc,CAACjF,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC5F;AACA,KAAM,CAAAuK,cAAc,CAAI,CAACF,kBAAkB,EAAI,CAACC,iBAAiB,EAAKhB,OAAO,CAACjE,YAAY,CAE1F;AACAxH,OAAO,CAACC,GAAG,CAAC,wBAAwBwL,OAAO,CAACnG,EAAE,GAAG,CAAE,CACjDkH,kBAAkB,CAClBC,iBAAiB,CACjBC,cAAc,CACdxM,oBAAoB,CAAE,EAAAgM,qBAAA,CAAAT,OAAO,CAACtE,eAAe,UAAA+E,qBAAA,iBAAvBA,qBAAA,CAAyB/L,MAAM,GAAI,CAAC,CAC1DwM,mBAAmB,CAAE,EAAAR,qBAAA,CAAAV,OAAO,CAACrE,cAAc,UAAA+E,qBAAA,iBAAtBA,qBAAA,CAAwBhM,MAAM,GAAI,CAAC,CACxDyM,oBAAoB,CAAEN,mBAAmB,CAACnM,MAAM,CAChD0M,mBAAmB,CAAEN,kBAAkB,CAACpM,MAAM,CAC9C2M,iBAAiB,CAAErB,OAAO,CAACtE,eAAe,CAAG,QAAQ,CAAG,SAAS,CACjE4F,gBAAgB,CAAEtB,OAAO,CAACrE,cAAc,CAAG,QAAQ,CAAG,SAAS,CAC/D4F,kBAAkB,CAAE,EAAAZ,qBAAA,CAAAX,OAAO,CAACnE,gBAAgB,UAAA8E,qBAAA,iBAAxBA,qBAAA,CAA0BjM,MAAM,GAAI,CAAC,CACzD8M,iBAAiB,CAAE,EAAAZ,qBAAA,CAAAZ,OAAO,CAAClE,eAAe,UAAA8E,qBAAA,iBAAvBA,qBAAA,CAAyBlM,MAAM,GAAI,CACxD,CAAC,CAAC,CAEF;AACA,KAAM,CAAA+M,mBAAmB,CAAGzO,QAAQ,CAAC0O,SAAS,CAAC/H,CAAC,EAAIA,CAAC,CAACE,EAAE,GAAKmG,OAAO,CAACnG,EAAE,CAAC,CACxE,GAAI,CAAAxF,YAAY,CAAG,EAAE,CAErB;AACA,IAAK,GAAI,CAAAsN,CAAC,CAAGF,mBAAmB,CAAG,CAAC,CAAEE,CAAC,EAAI,CAAC,CAAEA,CAAC,EAAE,CAAE,CACjD,GAAI3O,QAAQ,CAAC2O,CAAC,CAAC,CAACjH,MAAM,GAAK,MAAM,EAAI1H,QAAQ,CAAC2O,CAAC,CAAC,CAAClH,OAAO,CAAE,CACxDpG,YAAY,CAAGrB,QAAQ,CAAC2O,CAAC,CAAC,CAAClH,OAAO,CAClC,MACF,CACF,CAEAlG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAE,CAAEoN,WAAW,CAAE5B,OAAO,CAACnG,EAAE,CAAExF,YAAa,CAAC,CAAC,CAEvG;AACA,KAAM,CAAAwN,UAAU,CAAG,EAAE,CACrB;AACA,GAAI,CAAAC,YAAY,CAAG,EAAE,CAErB;AACA,GAAIf,kBAAkB,EAAIC,iBAAiB,CAAE,CAC3Cc,YAAY,CAAG,sBAAsB,CAErC;AACA;AACA,KAAM,CAAAC,YAAY,CAAGlB,mBAAmB,CAACnM,MAAM,GAAK,CAAC,CACjDmM,mBAAmB,CAAC,CAAC,CAAC,CAACrD,IAAI,CAACjE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3C,oBAAoB,CAExBsI,UAAU,CAAC/E,IAAI,cACbrK,KAAA,QAAyByM,SAAS,CAAC,gGAAgG,CAAAC,QAAA,eACjI1M,KAAA,OAAIyM,SAAS,CAAC,4DAA4D,CAAAC,QAAA,EAAC,2BAC1D,CAAC4C,YAAY,EAC1B,CAAC,cACLxP,IAAA,CAACN,iBAAiB,EAChBwI,OAAO,CAAEuF,OAAO,CAACtE,eAAe,EAAI,EAAG,CACvCsG,KAAK,CAAE3N,YAAa,CACpB4N,KAAK,CAAEjC,OAAO,CAAC/E,SAAU,CACzBJ,MAAM,CAAEmF,OAAO,CAACnF,MAAO,CACxB,CAAC,CAGD,CAACqH,sBAAA,EAAM,CACN,KAAM,CAAAC,aAAa,CAAG,EAAAD,sBAAA,CAAAlC,OAAO,CAACnE,gBAAgB,UAAAqG,sBAAA,iBAAxBA,sBAAA,CAA0BE,MAAM,CAAC7M,MAAM,EAC3D,MAAO,CAAAA,MAAM,GAAK,QAAQ,EAAIA,MAAM,CAACC,cACvC,CAAC,GAAI,EAAE,CAEP;AACA,GAAI,CAAA6M,eAAe,CAAGF,aAAa,CAACC,MAAM,CAAC7M,MAAM,EAC/CD,mBAAmB,CAACC,MAAM,CAAYlB,YAAY,CACpD,CAAC,CAED;AACA,GAAIgO,eAAe,CAAC3N,MAAM,GAAK,CAAC,EAAIL,YAAY,CAACO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAE,CAChFwN,eAAe,CAAGF,aAAa,CAACC,MAAM,CAAC7M,MAAM,EAC1CA,MAAM,CAAYI,YAAY,GAAK,OACtC,CAAC,CACDpB,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAE6N,eAAe,CAAC3N,MAAM,CAAC,CAC7G,CAEA,MAAO,CAAA2N,eAAe,CAAC3N,MAAM,CAAG,CAAC,EAAIN,uBAAuB,CAACC,YAAY,CAAE2L,OAAO,CAACtE,eAAe,EAAI,EAAE,CAAC,cACvGjJ,KAAA,QAAKyM,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5M,IAAA,OAAI2M,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,8BAAkB,CAAI,CAAC,cAChF5M,IAAA,QAAK2M,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBkD,eAAe,CAACrF,GAAG,CAAC,CAACzH,MAAM,CAAE4G,KAAK,gBACjC5J,IAAA,CAACH,aAAa,EAAamD,MAAM,CAAEA,MAAiB,EAAhC4G,KAAkC,CACvD,CAAC,CACC,CAAC,EACH,CAAC,CACJ,IAAI,CACV,CAAC,EAAE,CAAC,cAEJ1J,KAAA,QAAKyM,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD5M,IAAA,MAAG2M,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,UAAQ,CAAG,CAAC,cACpE5M,IAAA,CAACiM,UAAU,EAACE,KAAK,CAAEmC,mBAAoB,CAAClC,UAAU,CAAE,CAAE,CAAE,CAAC,EACtD,CAAC,GA7CC,eA8CJ,CACP,CAAC,CAED;AACA;AACA,KAAM,CAAA2D,YAAY,CAAGxB,kBAAkB,CAACpM,MAAM,CAAG,CAAC,CAAG,oBAAoB,CAAG,mBAAmB,CAE/FmN,UAAU,CAAC/E,IAAI,cACbrK,KAAA,QAAwByM,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAClI1M,KAAA,OAAIyM,SAAS,CAAC,6DAA6D,CAAAC,QAAA,EAAC,2BAC3D,CAACmD,YAAY,EAC1B,CAAC,cACL/P,IAAA,CAACN,iBAAiB,EAChBwI,OAAO,CAAEuF,OAAO,CAACrE,cAAc,EAAI,EAAG,CACtCqG,KAAK,CAAE3N,YAAa,CACpB4N,KAAK,CAAEjC,OAAO,CAAC/E,SAAU,CACzBJ,MAAM,CAAEmF,OAAO,CAACnF,MAAO,CACxB,CAAC,cACFpI,KAAA,QAAKyM,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClD5M,IAAA,MAAG2M,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,UAAQ,CAAG,CAAC,cACrE5M,IAAA,CAACiM,UAAU,EAACE,KAAK,CAAEoC,kBAAmB,CAACnC,UAAU,CAAE,CAAE,CAAE,CAAC,EACrD,CAAC,GAbC,cAcJ,CACP,CAAC,CAEH;AACA,CAAC,IAAM,IAAIoC,kBAAkB,CAAE,CAC7Be,YAAY,CAAG,eAAe,CAE9B;AACA,KAAM,CAAAC,YAAY,CAAGlB,mBAAmB,CAACnM,MAAM,GAAK,CAAC,CACjDmM,mBAAmB,CAAC,CAAC,CAAC,CAACrD,IAAI,CAACjE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3C,oBAAoB,CAExBsI,UAAU,CAAC/E,IAAI,cACbrK,KAAA,QAA6ByM,SAAS,CAAC,gGAAgG,CAAAC,QAAA,eACrI5M,IAAA,OAAI2M,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CAAC,6CAE3E,CAAI,CAAC,cACL5M,IAAA,CAACN,iBAAiB,EAChBwI,OAAO,CAAEuF,OAAO,CAACtE,eAAe,EAAI,EAAG,CACvCsG,KAAK,CAAE3N,YAAa,CACpB4N,KAAK,CAAEjC,OAAO,CAAC/E,SAAU,CACzBJ,MAAM,CAAEmF,OAAO,CAACnF,MAAO,CACxB,CAAC,CAGD,CAAC0H,sBAAA,EAAM,CACN,KAAM,CAAAJ,aAAa,CAAG,EAAAI,sBAAA,CAAAvC,OAAO,CAACnE,gBAAgB,UAAA0G,sBAAA,iBAAxBA,sBAAA,CAA0BH,MAAM,CAAC7M,MAAM,EAC3D,MAAO,CAAAA,MAAM,GAAK,QAAQ,EAAIA,MAAM,CAACC,cACvC,CAAC,GAAI,EAAE,CAEP;AACA,GAAI,CAAA6M,eAAe,CAAGF,aAAa,CAACC,MAAM,CAAC7M,MAAM,EAC/CD,mBAAmB,CAACC,MAAM,CAAYlB,YAAY,CACpD,CAAC,CAED;AACA,GAAIgO,eAAe,CAAC3N,MAAM,GAAK,CAAC,EAAIL,YAAY,CAACO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAE,CAChFwN,eAAe,CAAGF,aAAa,CAACC,MAAM,CAAC7M,MAAM,EAC1CA,MAAM,CAAYI,YAAY,GAAK,OACtC,CAAC,CACDpB,OAAO,CAACC,GAAG,CAAC,6FAA6F,CAAE6N,eAAe,CAAC3N,MAAM,CAAC,CACpI,CAEA,MAAO,CAAA2N,eAAe,CAAC3N,MAAM,CAAG,CAAC,EAAIN,uBAAuB,CAACC,YAAY,CAAE2L,OAAO,CAACtE,eAAe,EAAI,EAAE,CAAC,cACvGjJ,KAAA,QAAKyM,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5M,IAAA,OAAI2M,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,8BAAkB,CAAI,CAAC,cAChF5M,IAAA,QAAK2M,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBkD,eAAe,CAACrF,GAAG,CAAC,CAACzH,MAAM,CAAE4G,KAAK,gBACjC5J,IAAA,CAACH,aAAa,EAAamD,MAAM,CAAEA,MAAiB,EAAhC4G,KAAkC,CACvD,CAAC,CACC,CAAC,EACH,CAAC,CACJ,IAAI,CACV,CAAC,EAAE,CAAC,cAEJ1J,KAAA,QAAKyM,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD5M,IAAA,MAAG2M,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,UAAQ,CAAG,CAAC,cACpE5M,IAAA,CAACiM,UAAU,EAACE,KAAK,CAAEmC,mBAAoB,CAAClC,UAAU,CAAE,CAAE,CAAE,CAAC,EACtD,CAAC,GA7CC,mBA8CJ,CACP,CAAC,CAEH;AACA,CAAC,IAAM,IAAIqC,iBAAiB,CAAE,CAC5Bc,YAAY,CAAG,cAAc,CAE7B;AACA,KAAM,CAAAQ,YAAY,CAAGxB,kBAAkB,CAACpM,MAAM,CAAG,CAAC,CAAG,oBAAoB,CAAG,mBAAmB,CAE/FmN,UAAU,CAAC/E,IAAI,cACbrK,KAAA,QAA4ByM,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eACtI5M,IAAA,OAAI2M,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,6CAE5E,CAAI,CAAC,cACL5M,IAAA,CAACN,iBAAiB,EAChBwI,OAAO,CAAEuF,OAAO,CAACrE,cAAc,EAAI,EAAG,CACtCqG,KAAK,CAAE3N,YAAa,CACpB4N,KAAK,CAAEjC,OAAO,CAAC/E,SAAU,CACzBJ,MAAM,CAAEmF,OAAO,CAACnF,MAAO,CACxB,CAAC,cACFpI,KAAA,QAAKyM,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClD5M,IAAA,MAAG2M,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,UAAQ,CAAG,CAAC,cACrE5M,IAAA,CAACiM,UAAU,EAACE,KAAK,CAAEoC,kBAAmB,CAACnC,UAAU,CAAE,CAAE,CAAE,CAAC,EACrD,CAAC,GAbC,kBAcJ,CACP,CAAC,CAEH;AACA,CAAC,IAAM,IAAIsC,cAAc,CAAE,KAAAuB,kBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CACzBf,YAAY,CAAG,KAAK,CAEpB,KAAM,CAAAgB,SAAS,CAAG9C,OAAO,CAAC/E,SAAS,EAAI,QAAQ,CAC/C,KAAM,CAAA8H,SAAS,CAAG,CAAAP,kBAAA,CAAAxC,OAAO,CAAC/E,SAAS,UAAAuH,kBAAA,WAAjBA,kBAAA,CAAmB3N,QAAQ,CAAC,SAAS,CAAC,CAAG,IAAI,CAC/C,CAAA4N,mBAAA,CAAAzC,OAAO,CAAC/E,SAAS,UAAAwH,mBAAA,WAAjBA,mBAAA,CAAmB5N,QAAQ,CAAC,MAAM,CAAC,CAAG,GAAG,CACzC,CAAA6N,mBAAA,CAAA1C,OAAO,CAAC/E,SAAS,UAAAyH,mBAAA,WAAjBA,mBAAA,CAAmB7N,QAAQ,CAAC,UAAU,CAAC,CAAG,IAAI,CAC9C,CAAA8N,mBAAA,CAAA3C,OAAO,CAAC/E,SAAS,UAAA0H,mBAAA,WAAjBA,mBAAA,CAAmB9N,QAAQ,CAAC,MAAM,CAAC,CAAG,IAAI,CAC1C,CAAA+N,mBAAA,CAAA5C,OAAO,CAAC/E,SAAS,UAAA2H,mBAAA,WAAjBA,mBAAA,CAAmB/N,QAAQ,CAAC,QAAQ,CAAC,CAAG,IAAI,CAC5C,CAAAgO,mBAAA,CAAA7C,OAAO,CAAC/E,SAAS,UAAA4H,mBAAA,WAAjBA,mBAAA,CAAmBhO,QAAQ,CAAC,aAAa,CAAC,CAAG,IAAI,CAAG,IAAI,CAExE;AACA,GAAI,CAACjB,eAAe,EAAI,CAACoM,OAAO,CAAChF,OAAO,CAAE,CAC1C6G,UAAU,CAAC/E,IAAI,cACbrK,KAAA,QAAwByM,SAAS,CAAC,oGAAoG,CAAAC,QAAA,eACpI1M,KAAA,OAAIyM,SAAS,CAAC,8DAA8D,CAAAC,QAAA,EACzE4D,SAAS,CAAC,uBAAqB,CAACD,SAAS,EACxC,CAAC,cACLvQ,IAAA,CAACN,iBAAiB,EAChBwI,OAAO,CAAEuF,OAAO,CAACvF,OAAO,EAAI,mEAAoE,CAChGuH,KAAK,CAAE3N,YAAa,CACpB4N,KAAK,CAAEjC,OAAO,CAAC/E,SAAS,EAAI,QAAS,CACrCJ,MAAM,CAAEmF,OAAO,CAACnF,MAAO,CACxB,CAAC,cACFtI,IAAA,QAAK2M,SAAS,CAAC,sCAAsC,CAAAC,QAAA,cACnD5M,IAAA,MAAG2M,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,8GAE9C,CAAG,CAAC,CACD,CAAC,GAdC,cAeJ,CACP,CAAC,CACD,CAEF;AACA,CAAC,IAAM,CACL;AACA2C,YAAY,CAAG,YAAY,CAE3B;AACA,GAAI,CAAClO,eAAe,EAAI,CAACoM,OAAO,CAAChF,OAAO,CAAE,CAC1C6G,UAAU,CAAC/E,IAAI,cACbrK,KAAA,QAAsByM,SAAS,CAAC,sFAAsF,CAAAC,QAAA,eACpH5M,IAAA,MAAG2M,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAAC,cAC9D5M,IAAA,CAACN,iBAAiB,EAChBwI,OAAO,CAAEuF,OAAO,CAACvF,OAAO,EAAI,sGAAuG,CACnIuH,KAAK,CAAE3N,YAAa,CACpB4N,KAAK,CAAEjC,OAAO,CAAC/E,SAAS,EAAI,QAAS,CACrCJ,MAAM,CAAEmF,OAAO,CAACnF,MAAO,CACxB,CAAC,GAPK,YAQJ,CACP,CAAC,CACD,CACF,CAEA;AACA,GAAIgH,UAAU,CAACnN,MAAM,CAAG,CAAC,CAAE,CACzB,mBACEnC,IAAA,QAAK2M,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB0C,UAAU,CACR,CAAC,CAEV,CAEA;AACAtN,OAAO,CAACyO,IAAI,CAAC,oCAAoC,CAAC,CAClD,mBACEvQ,KAAA,QAAKyM,SAAS,CAAC,sFAAsF,CAAAC,QAAA,eACnG5M,IAAA,MAAG2M,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAAC,cAC7D5M,IAAA,CAACN,iBAAiB,EAChBwI,OAAO,CAAEuF,OAAO,CAACvF,OAAO,EAAI,iDAAkD,CAC9EuH,KAAK,CAAE3N,YAAa,CACpB4N,KAAK,CAAEjC,OAAO,CAAC/E,SAAS,EAAI,QAAS,CACrCJ,MAAM,CAAEmF,OAAO,CAACnF,MAAO,CACxB,CAAC,EACC,CAAC,CAEV,CAAC,EAAE,CAAC,CACD,CACN,EACE,CAAC,EA3UDmF,OAAO,CAACnG,EA4UV,CACN,CAAC,cACFtH,IAAA,QAAK0Q,GAAG,CAAEjP,cAAe,CAACkP,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,EAClE,CACN,CACE,CAAC,cAGN3Q,KAAA,QAAKyM,SAAS,CAAE,2GAA2GrM,WAAW,CAAG,mBAAmB,CAAG,QAAQ,EAAG,CAAAsM,QAAA,eACxK1M,KAAA,SAAM4Q,QAAQ,CAAEtJ,iBAAkB,CAACmF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACxE5M,IAAA,QAAK2M,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC5M,IAAA,UACE+Q,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEjQ,KAAM,CACbkQ,QAAQ,CAAGxJ,CAAC,EAAK,CACf,KAAM,CAAAyJ,QAAQ,CAAGzJ,CAAC,CAACsF,MAAM,CAACiE,KAAK,CAC/BhQ,QAAQ,CAACkQ,QAAQ,CAAC,CAClB;AACF,CAAE,CACFC,WAAW,CAAC,+CAA+C,CAC3DxE,SAAS,CAAC,wJAAwJ,CAClKyE,QAAQ,CAAEnQ,YAAa,CACvB,aAAW,eAAe,CAC3B,CAAC,CACC,CAAC,cAENf,KAAA,QAAKyM,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5M,IAAA,CAACR,WAAW,EACV6R,YAAY,CAAElQ,cAAe,CAC7BmQ,aAAa,CAAGC,OAAO,EAAKnQ,iBAAiB,CAACmQ,OAAO,CAAE,CACvDC,SAAS,CAAEvQ,YAAa,CACzB,CAAC,cACFjB,IAAA,WACE+Q,IAAI,CAAC,QAAQ,CACbK,QAAQ,CAAEnQ,YAAY,EAAI,CAACF,KAAK,CAACoD,IAAI,CAAC,CAAE,CACxCwI,SAAS,CAAC,uMAAuM,CACjNM,KAAK,CAAC,cAAc,CAAAL,QAAA,cAEpB5M,IAAA,SAAA4M,QAAA,CAAO3L,YAAY,CAAG,YAAY,CAAG,MAAM,CAAO,CAAC,CAC7C,CAAC,EACN,CAAC,EACF,CAAC,cACKf,KAAA,QAAKyM,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,iBACnD,CAAC,EAAAvM,qBAAA,CAAAZ,kBAAkB,CAAC0H,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACE,EAAE,GAAKnG,cAAc,CAAC,UAAAd,qBAAA,iBAArDA,qBAAA,CAAuDgH,IAAI,GAAIlG,cAAc,EAC1F,CAAC,EACH,CAAC,CAELV,QAAQ,CAAC0B,MAAM,CAAG,CAAC,eAAInC,IAAA,QAAK2M,SAAS,CAAC,MAAM,CAAM,CAAC,EACjD,CAAC,EACH,CAAC,CAEV,CAOA,QAAS,CAAA8E,GAAGA,CAAAC,KAAA,CAA4C,IAA3C,CAAEpR,WAAW,CAAEC,cAAyB,CAAC,CAAAmR,KAAA,CACpD,mBACE1R,IAAA,CAACG,aAAa,EAACG,WAAW,CAAEA,WAAY,CAACC,cAAc,CAAEA,cAAe,CAAE,CAAC,CAE/E,CAEA,cAAe,CAAAkR,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}