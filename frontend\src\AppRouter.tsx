import React, { useState } from 'react';
import { BrowserRouter as Router, Switch, Route } from 'react-router-dom';
import App from './App';
import DocumentsPage from './pages/documents/DocumentsPage';
import WebsitesPage from './pages/websites/WebsitesPage';
import SettingsPage from './pages/settings/SettingsPage';
import DocumentViewer from './pages/DocumentViewer';
import Header from './components/layout/Header';
import { ChatProvider } from './contexts/ChatContext';

const AppRouter: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <ChatProvider>
      <Router>
        <div className="flex flex-col h-screen overflow-hidden bg-white transition-colors duration-300">
          {/* Fixed header */}
          <Header
            onSidebarToggle={() => setSidebarOpen(!sidebarOpen)}
            sidebarOpen={sidebarOpen}
          />
          {/* Main content area with padding-top to account for fixed header */}
          <main className="flex-grow bg-gray-50 h-full pt-16 sm:pt-20 overflow-hidden transition-colors duration-300">
            <Switch>
              <Route exact path="/" render={() => <App sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />} />
              <Route path="/documents" component={DocumentsPage} />
              <Route path="/websites" component={WebsitesPage} />
              <Route path="/settings" component={SettingsPage} />
              <Route path="/viewer" component={DocumentViewer} />
            </Switch>
          </main>
        </div>
      </Router>
    </ChatProvider>
  );
};

export default AppRouter;
