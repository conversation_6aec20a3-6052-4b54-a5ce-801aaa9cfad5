{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@supabase/supabase-js": "^2.49.4", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@types/jest": "^26.0.24", "@types/node": "^12.20.55", "@types/react": "^16.14.43", "@types/react-dom": "^16.9.18", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "lucide-react": "^0.16.0", "pdfjs-dist": "^2.16.105", "react": "^16.14.0", "react-dom": "^16.14.0", "react-markdown": "^8.0.7", "react-pdf": "^5.7.2", "react-router-dom": "^5.3.4", "react-scripts": "4.0.3", "remark-gfm": "^3.0.1", "typescript": "^4.1.6", "web-vitals": "^1.1.2"}, "scripts": {"prestart": "echo Setting NODE_OPTIONS for OpenSSL legacy provider", "start": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-scripts start", "start-win": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "start-unix": "export NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "postcss": "^8.5.3", "tailwindcss": "^3.4.1"}}