{"version": 3, "sources": ["../node_modules/web-vitals/dist/web-vitals.js"], "names": ["e", "t", "n", "i", "a", "name", "value", "delta", "entries", "id", "concat", "Date", "now", "Math", "floor", "random", "r", "PerformanceObserver", "supportedEntryTypes", "includes", "self", "getEntries", "map", "observe", "type", "buffered", "o", "document", "visibilityState", "removeEventListener", "addEventListener", "c", "persisted", "u", "WeakSet", "Set", "f", "has", "s", "hadRecentInput", "push", "takeRecords", "m", "p", "v", "timeStamp", "d", "setTimeout", "l", "disconnect", "startTime", "add", "performance", "getEntriesByName", "requestAnimationFrame", "h", "passive", "capture", "S", "y", "w", "g", "entryType", "target", "cancelable", "processingStart", "for<PERSON>ach", "E", "L", "T", "once", "b", "getEntriesByType", "timing", "max", "navigationStart", "responseStart", "readyState"], "mappings": "sGAAA,+MAAIA,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,SAASJ,EAAEC,GAAG,MAAM,CAACI,KAAKL,EAAEM,WAAM,IAASL,GAAG,EAAEA,EAAEM,MAAM,EAAEC,QAAQ,GAAGC,GAAG,MAAMC,OAAOC,KAAKC,MAAM,KAAKF,OAAOG,KAAKC,MAAM,cAAcD,KAAKE,UAAU,MAAM,EAAEC,EAAE,SAAShB,EAAEC,GAAG,IAAI,GAAGgB,oBAAoBC,oBAAoBC,SAASnB,GAAG,CAAC,GAAG,gBAAgBA,KAAK,2BAA2BoB,MAAM,OAAO,IAAIlB,EAAE,IAAIe,qBAAqB,SAASjB,GAAG,OAAOA,EAAEqB,aAAaC,IAAIrB,EAAE,IAAI,OAAOC,EAAEqB,QAAQ,CAACC,KAAKxB,EAAEyB,UAAS,IAAKvB,CAAC,CAAC,CAAC,MAAMF,GAAG,CAAC,EAAE0B,EAAE,SAAS1B,EAAEC,GAAG,IAAIC,EAAE,SAASA,EAAEC,GAAG,aAAaA,EAAEqB,MAAM,WAAWG,SAASC,kBAAkB5B,EAAEG,GAAGF,IAAI4B,oBAAoB,mBAAmB3B,GAAE,GAAI2B,oBAAoB,WAAW3B,GAAE,IAAK,EAAE4B,iBAAiB,mBAAmB5B,GAAE,GAAI4B,iBAAiB,WAAW5B,GAAE,EAAG,EAAE6B,EAAE,SAAS/B,GAAG8B,iBAAiB,YAAY,SAAS7B,GAAGA,EAAE+B,WAAWhC,EAAEC,EAAE,IAAG,EAAG,EAAEgC,EAAE,mBAAmBC,QAAQ,IAAIA,QAAQ,IAAIC,IAAIC,EAAE,SAASpC,EAAEC,EAAEC,GAAG,IAAIC,EAAE,OAAO,WAAWF,EAAEK,OAAO,IAAIJ,GAAG+B,EAAEI,IAAIpC,IAAI,WAAW0B,SAASC,mBAAmB3B,EAAEM,MAAMN,EAAEK,OAAOH,GAAG,IAAIF,EAAEM,YAAO,IAASJ,KAAKA,EAAEF,EAAEK,MAAMN,EAAEC,IAAI,CAAC,EAAEqC,EAAE,SAAStC,EAAEC,GAAG,IAAIC,EAAEC,EAAEC,EAAE,MAAM,GAAG6B,EAAE,SAASjC,GAAGA,EAAEuC,iBAAiBpC,EAAEG,OAAON,EAAEM,MAAMH,EAAEK,QAAQgC,KAAKxC,GAAGE,IAAI,EAAEoC,EAAEtB,EAAE,eAAeiB,GAAGK,IAAIpC,EAAEkC,EAAEpC,EAAEG,EAAEF,GAAGyB,GAAG,WAAWY,EAAEG,cAAcnB,IAAIW,GAAG/B,GAAG,IAAI6B,GAAG,WAAW5B,EAAEC,EAAE,MAAM,GAAGF,EAAEkC,EAAEpC,EAAEG,EAAEF,EAAE,IAAI,EAAEyC,GAAG,EAAEC,EAAE,WAAW,MAAM,WAAWhB,SAASC,gBAAgB,EAAE,GAAG,EAAEgB,EAAE,WAAWlB,GAAG,SAAS1B,GAAG,IAAIC,EAAED,EAAE6C,UAAUH,EAAEzC,CAAC,IAAG,EAAG,EAAE6C,EAAE,WAAW,OAAOJ,EAAE,IAAIA,EAAEC,IAAIC,IAAIb,GAAG,WAAWgB,YAAY,WAAWL,EAAEC,IAAIC,GAAG,GAAG,EAAE,KAAK,CAAC,aAAIC,GAAY,OAAOH,CAAC,EAAE,EAAEM,EAAE,SAAShD,EAAEC,GAAG,IAAIC,EAAEC,EAAE2C,IAAIpB,EAAEtB,EAAE,OAAOkC,EAAE,SAAStC,GAAG,2BAA2BA,EAAEK,OAAOsC,GAAGA,EAAEM,aAAajD,EAAEkD,UAAU/C,EAAE0C,YAAYnB,EAAEpB,MAAMN,EAAEkD,UAAUxB,EAAElB,QAAQgC,KAAKxC,GAAGiC,EAAEkB,IAAIzB,GAAGxB,KAAK,EAAEwC,EAAEU,YAAYC,iBAAiB,0BAA0B,GAAGV,EAAED,EAAE,KAAK1B,EAAE,QAAQsB,IAAII,GAAGC,KAAKzC,EAAEkC,EAAEpC,EAAE0B,EAAEzB,GAAGyC,GAAGJ,EAAEI,GAAGX,GAAG,SAAS5B,GAAGuB,EAAEtB,EAAE,OAAOF,EAAEkC,EAAEpC,EAAE0B,EAAEzB,GAAGqD,uBAAuB,WAAWA,uBAAuB,WAAW5B,EAAEpB,MAAM8C,YAAYxC,MAAMT,EAAE0C,UAAUZ,EAAEkB,IAAIzB,GAAGxB,GAAG,GAAG,GAAG,IAAI,EAAEqD,EAAE,CAACC,SAAQ,EAAGC,SAAQ,GAAIC,EAAE,IAAI/C,KAAKgD,EAAE,SAASxD,EAAEC,GAAGJ,IAAIA,EAAEI,EAAEH,EAAEE,EAAED,EAAE,IAAIS,KAAKiD,EAAE/B,qBAAqBgC,IAAI,EAAEA,EAAE,WAAW,GAAG5D,GAAG,GAAGA,EAAEC,EAAEwD,EAAE,CAAC,IAAItD,EAAE,CAAC0D,UAAU,cAAczD,KAAKL,EAAEwB,KAAKuC,OAAO/D,EAAE+D,OAAOC,WAAWhE,EAAEgE,WAAWd,UAAUlD,EAAE6C,UAAUoB,gBAAgBjE,EAAE6C,UAAU5C,GAAGE,EAAE+D,SAAS,SAASlE,GAAGA,EAAEI,EAAE,IAAID,EAAE,EAAE,CAAC,EAAEgE,EAAE,SAASnE,GAAG,GAAGA,EAAEgE,WAAW,CAAC,IAAI/D,GAAGD,EAAE6C,UAAU,KAAK,IAAIlC,KAAKyC,YAAYxC,OAAOZ,EAAE6C,UAAU,eAAe7C,EAAEwB,KAAK,SAASxB,EAAEC,GAAG,IAAIC,EAAE,WAAWyD,EAAE3D,EAAEC,GAAGG,GAAG,EAAED,EAAE,WAAWC,GAAG,EAAEA,EAAE,WAAWyB,oBAAoB,YAAY3B,EAAEqD,GAAG1B,oBAAoB,gBAAgB1B,EAAEoD,EAAE,EAAEzB,iBAAiB,YAAY5B,EAAEqD,GAAGzB,iBAAiB,gBAAgB3B,EAAEoD,EAAE,CAAhO,CAAkOtD,EAAED,GAAG2D,EAAE1D,EAAED,EAAE,CAAC,EAAE4D,EAAE,SAAS5D,GAAG,CAAC,YAAY,UAAU,aAAa,eAAekE,SAAS,SAASjE,GAAG,OAAOD,EAAEC,EAAEkE,EAAEZ,EAAE,GAAG,EAAEa,EAAE,SAASlE,EAAEoC,GAAG,IAAII,EAAEC,EAAEG,IAAIF,EAAExC,EAAE,OAAO4C,EAAE,SAAShD,GAAGA,EAAEkD,UAAUP,EAAEE,YAAYD,EAAEtC,MAAMN,EAAEiE,gBAAgBjE,EAAEkD,UAAUN,EAAEpC,QAAQgC,KAAKxC,GAAGiC,EAAEkB,IAAIP,GAAGF,IAAI,EAAEa,EAAEvC,EAAE,cAAcgC,GAAGN,EAAEN,EAAElC,EAAE0C,EAAEN,GAAGiB,GAAG7B,GAAG,WAAW6B,EAAEd,cAAcnB,IAAI0B,GAAGO,EAAEN,YAAY,IAAG,GAAIM,GAAGxB,GAAG,WAAW,IAAIf,EAAE4B,EAAExC,EAAE,OAAOsC,EAAEN,EAAElC,EAAE0C,EAAEN,GAAGnC,EAAE,GAAGF,GAAG,EAAED,EAAE,KAAK4D,EAAE9B,kBAAkBd,EAAEgC,EAAE7C,EAAEqC,KAAKxB,GAAG6C,GAAG,GAAG,EAAEQ,EAAE,SAASrE,EAAEC,GAAG,IAAIC,EAAEC,EAAE2C,IAAIR,EAAElC,EAAE,OAAOsC,EAAE,SAAS1C,GAAG,IAAIC,EAAED,EAAEkD,UAAUjD,EAAEE,EAAE0C,YAAYP,EAAEhC,MAAML,EAAEqC,EAAE9B,QAAQgC,KAAKxC,IAAIE,GAAG,EAAEyC,EAAE3B,EAAE,2BAA2B0B,GAAG,GAAGC,EAAE,CAACzC,EAAEkC,EAAEpC,EAAEsC,EAAErC,GAAG,IAAI2C,EAAE,WAAWX,EAAEI,IAAIC,KAAKK,EAAEF,cAAcnB,IAAIoB,GAAGC,EAAEM,aAAahB,EAAEkB,IAAIb,GAAGpC,IAAI,EAAE,CAAC,UAAU,SAASgE,SAAS,SAASlE,GAAG8B,iBAAiB9B,EAAE4C,EAAE,CAAC0B,MAAK,EAAGb,SAAQ,GAAI,IAAI/B,EAAEkB,GAAE,GAAIb,GAAG,SAAS5B,GAAGmC,EAAElC,EAAE,OAAOF,EAAEkC,EAAEpC,EAAEsC,EAAErC,GAAGqD,uBAAuB,WAAWA,uBAAuB,WAAWhB,EAAEhC,MAAM8C,YAAYxC,MAAMT,EAAE0C,UAAUZ,EAAEkB,IAAIb,GAAGpC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAEqE,EAAE,SAASvE,GAAG,IAAIC,EAAEC,EAAEE,EAAE,QAAQH,EAAE,WAAW,IAAI,IAAIA,EAAEmD,YAAYoB,iBAAiB,cAAc,IAAI,WAAW,IAAIxE,EAAEoD,YAAYqB,OAAOxE,EAAE,CAAC6D,UAAU,aAAaZ,UAAU,GAAG,IAAI,IAAIhD,KAAKF,EAAE,oBAAoBE,GAAG,WAAWA,IAAID,EAAEC,GAAGW,KAAK6D,IAAI1E,EAAEE,GAAGF,EAAE2E,gBAAgB,IAAI,OAAO1E,CAAC,CAAjL,GAAqL,GAAGC,EAAEI,MAAMJ,EAAEK,MAAMN,EAAE2E,cAAc1E,EAAEI,MAAM,EAAE,OAAOJ,EAAEM,QAAQ,CAACP,GAAGD,EAAEE,EAAE,CAAC,MAAMF,GAAG,CAAC,EAAE,aAAa2B,SAASkD,WAAW9B,WAAW9C,EAAE,GAAG6B,iBAAiB,WAAW7B,EAAE,C", "file": "static/js/4.1634db42.chunk.js", "sourcesContent": ["var e,t,n,i,a=function(e,t){return{name:e,value:void 0===t?-1:t,delta:0,entries:[],id:\"v1-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12)}},r=function(e,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){if(\"first-input\"===e&&!(\"PerformanceEventTiming\"in self))return;var n=new PerformanceObserver((function(e){return e.getEntries().map(t)}));return n.observe({type:e,buffered:!0}),n}}catch(e){}},o=function(e,t){var n=function n(i){\"pagehide\"!==i.type&&\"hidden\"!==document.visibilityState||(e(i),t&&(removeEventListener(\"visibilitychange\",n,!0),removeEventListener(\"pagehide\",n,!0)))};addEventListener(\"visibilitychange\",n,!0),addEventListener(\"pagehide\",n,!0)},c=function(e){addEventListener(\"pageshow\",(function(t){t.persisted&&e(t)}),!0)},u=\"function\"==typeof WeakSet?new WeakSet:new Set,f=function(e,t,n){var i;return function(){t.value>=0&&(n||u.has(t)||\"hidden\"===document.visibilityState)&&(t.delta=t.value-(i||0),(t.delta||void 0===i)&&(i=t.value,e(t)))}},s=function(e,t){var n,i=a(\"CLS\",0),u=function(e){e.hadRecentInput||(i.value+=e.value,i.entries.push(e),n())},s=r(\"layout-shift\",u);s&&(n=f(e,i,t),o((function(){s.takeRecords().map(u),n()})),c((function(){i=a(\"CLS\",0),n=f(e,i,t)})))},m=-1,p=function(){return\"hidden\"===document.visibilityState?0:1/0},v=function(){o((function(e){var t=e.timeStamp;m=t}),!0)},d=function(){return m<0&&(m=p(),v(),c((function(){setTimeout((function(){m=p(),v()}),0)}))),{get timeStamp(){return m}}},l=function(e,t){var n,i=d(),o=a(\"FCP\"),s=function(e){\"first-contentful-paint\"===e.name&&(p&&p.disconnect(),e.startTime<i.timeStamp&&(o.value=e.startTime,o.entries.push(e),u.add(o),n()))},m=performance.getEntriesByName(\"first-contentful-paint\")[0],p=m?null:r(\"paint\",s);(m||p)&&(n=f(e,o,t),m&&s(m),c((function(i){o=a(\"FCP\"),n=f(e,o,t),requestAnimationFrame((function(){requestAnimationFrame((function(){o.value=performance.now()-i.timeStamp,u.add(o),n()}))}))})))},h={passive:!0,capture:!0},S=new Date,y=function(i,a){e||(e=a,t=i,n=new Date,w(removeEventListener),g())},g=function(){if(t>=0&&t<n-S){var a={entryType:\"first-input\",name:e.type,target:e.target,cancelable:e.cancelable,startTime:e.timeStamp,processingStart:e.timeStamp+t};i.forEach((function(e){e(a)})),i=[]}},E=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,t){var n=function(){y(e,t),a()},i=function(){a()},a=function(){removeEventListener(\"pointerup\",n,h),removeEventListener(\"pointercancel\",i,h)};addEventListener(\"pointerup\",n,h),addEventListener(\"pointercancel\",i,h)}(t,e):y(t,e)}},w=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(t){return e(t,E,h)}))},L=function(n,s){var m,p=d(),v=a(\"FID\"),l=function(e){e.startTime<p.timeStamp&&(v.value=e.processingStart-e.startTime,v.entries.push(e),u.add(v),m())},h=r(\"first-input\",l);m=f(n,v,s),h&&o((function(){h.takeRecords().map(l),h.disconnect()}),!0),h&&c((function(){var r;v=a(\"FID\"),m=f(n,v,s),i=[],t=-1,e=null,w(addEventListener),r=l,i.push(r),g()}))},T=function(e,t){var n,i=d(),s=a(\"LCP\"),m=function(e){var t=e.startTime;t<i.timeStamp&&(s.value=t,s.entries.push(e)),n()},p=r(\"largest-contentful-paint\",m);if(p){n=f(e,s,t);var v=function(){u.has(s)||(p.takeRecords().map(m),p.disconnect(),u.add(s),n())};[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,v,{once:!0,capture:!0})})),o(v,!0),c((function(i){s=a(\"LCP\"),n=f(e,s,t),requestAnimationFrame((function(){requestAnimationFrame((function(){s.value=performance.now()-i.timeStamp,u.add(s),n()}))}))}))}},b=function(e){var t,n=a(\"TTFB\");t=function(){try{var t=performance.getEntriesByType(\"navigation\")[0]||function(){var e=performance.timing,t={entryType:\"navigation\",startTime:0};for(var n in e)\"navigationStart\"!==n&&\"toJSON\"!==n&&(t[n]=Math.max(e[n]-e.navigationStart,0));return t}();if(n.value=n.delta=t.responseStart,n.value<0)return;n.entries=[t],e(n)}catch(e){}},\"complete\"===document.readyState?setTimeout(t,0):addEventListener(\"pageshow\",t)};export{s as getCLS,l as getFCP,L as getFID,T as getLCP,b as getTTFB};\n"], "sourceRoot": ""}