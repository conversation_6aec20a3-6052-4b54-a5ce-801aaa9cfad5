from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import os
import logging
from document_loader import load_documents, DOCUMENT_CHUNKS

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI()

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # This allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # This allows all methods
    allow_headers=["*"],  # This allows all headers
)

# Define our data models
class QueryRequest(BaseModel):
    query: str

class ChunkResponse(BaseModel):
    filename: str
    page: int
    chunk_id: str
    text: str

class QueryResponse(BaseModel):
    answer: str
    sources: List[str]

# Load documents at startup
@app.on_event("startup")
async def startup_event():
    logger.info("Loading documents on startup")
    try:
        # Get the absolute path for the data directory
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
        chunks = load_documents(data_dir)
        logger.info(f"Loaded {len(chunks)} document chunks on startup")
    except Exception as e:
        logger.error(f"Error loading documents: {str(e)}")

@app.get("/")
async def root():
    return {"message": "RailGPT API is running"}

@app.get("/api/chunks")
async def get_chunks():
    # Return the first 5 chunks for testing
    logger.info(f"Returning first 5 chunks out of {len(DOCUMENT_CHUNKS)}")
    return DOCUMENT_CHUNKS[:5] if DOCUMENT_CHUNKS else []

@app.post("/query")
async def process_query(request: QueryRequest):
    logger.info(f"Received query: {request.query}")
    
    # Create a response that references our actual documents
    sources = []
    if DOCUMENT_CHUNKS:
        # Use actual document references for sources
        sources = [f"{chunk['filename']} Page {chunk['page']}" for chunk in DOCUMENT_CHUNKS[:2]]
    else:
        sources = ["SampleDoc.pdf Page 1", "SampleDoc.pdf Page 2"]
    
    response = QueryResponse(
        answer="This is a mocked response from RailGPT backend.",
        sources=sources
    )
    
    return response
