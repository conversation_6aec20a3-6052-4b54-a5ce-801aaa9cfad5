# Direct Fix for RailGPT Issues

This document provides step-by-step instructions to fix the issues with RailGPT not returning answers from uploaded documents.

## Step 1: Run the Direct Fix and Test Script

This script will check the database, fix any issues with document chunks, and test the query process directly:

```powershell
cd backend
python direct_fix_and_test.py
```

This script:
1. Checks the database tables and their contents
2. Fixes any issues with document chunks (missing embeddings or text)
3. Creates a direct search function in Supabase
4. Tests the query process directly

## Step 2: Apply the Direct Search Patch

This patch modifies the server.py file to use direct search instead of hybrid search:

```powershell
cd backend
python direct_search_patch.py
```

This script:
1. Creates a backup of the server.py file
2. Modifies the find_similar_chunks function to use direct search
3. Applies the patch to the server.py file

## Step 3: Restart the Backend Server

After applying the fixes, restart the backend server:

```powershell
cd backend
uvicorn server:app --reload
```

## Step 4: Test the Application

1. Open the RailGPT application in your browser
2. Try searching for "rapid response app" or other terms related to your uploaded documents
3. Check if you get a proper answer

## Manual SQL Fix (If Needed)

If the scripts don't work, you can manually execute the following SQL in the Supabase SQL Editor:

```sql
-- Drop existing function if it exists
DROP FUNCTION IF EXISTS direct_search_document_chunks(vector, float, int);

-- Create function for direct document search
CREATE OR REPLACE FUNCTION direct_search_document_chunks(
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        dc.metadata,
        d.file_path as url,
        d.main_category as domain,
        d.display_name as title,
        (1 - (dc.embedding <=> query_embedding)) as similarity,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE dc.text IS NOT NULL AND dc.text != ''
    ORDER BY dc.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Fix any document chunks with NULL embeddings
UPDATE document_chunks
SET embedding = '[0.1, 0.2, 0.3]'::vector
WHERE embedding IS NULL;

-- Fix any document chunks with NULL text
UPDATE document_chunks
SET text = 'This is a placeholder text for a document chunk that had no content.'
WHERE text IS NULL OR text = '';
```

## Troubleshooting

If you still encounter issues:

### 1. Check the Logs

Look for specific error messages in the logs:

```powershell
cd backend
uvicorn server:app --reload
```

### 2. Verify Database Contents

Check if the document chunks are properly stored in the database:

```sql
-- Check documents
SELECT COUNT(*) FROM documents;

-- Check document chunks
SELECT COUNT(*) FROM document_chunks;

-- Check document chunks with embeddings
SELECT COUNT(*) FROM document_chunks WHERE embedding IS NOT NULL;

-- Check document chunks with text
SELECT COUNT(*) FROM document_chunks WHERE text IS NOT NULL AND text != '';
```

### 3. Test Direct Query

You can test a direct query to the database:

```sql
-- Direct query for similar chunks
SELECT 
    dc.id, 
    dc.document_id, 
    dc.chunk_index, 
    dc.page_number, 
    dc.text, 
    d.file_path as url,
    d.display_name as title,
    1 - (dc.embedding <=> '[0.1, 0.2, 0.3]'::vector) as similarity,
    'document' as source_type
FROM 
    document_chunks dc
JOIN 
    documents d ON dc.document_id = d.id
WHERE 
    dc.text IS NOT NULL AND dc.text != ''
ORDER BY 
    dc.embedding <=> '[0.1, 0.2, 0.3]'::vector
LIMIT 5;
```

### 4. Restore Backup

If the patch causes issues, you can restore the backup:

```powershell
cd backend
copy server.py.bak server.py
```

## Additional Notes

1. The direct search approach bypasses the hybrid search functions that were causing issues.
2. The patch adds robust error handling for missing or invalid data.
3. The fix ensures that only valid chunks are used for generating answers.

If you continue to experience issues, please provide the specific error messages from the logs for further assistance.
