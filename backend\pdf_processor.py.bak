import os
import re
import logging
import numpy as np
import shutil
from typing import List, Dict, Any, Optional, Tuple
import fitz  # PyMuPDF
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import google.generativeai as genai
from dotenv import load_dotenv
from website_scraper import extract_website_text
from document_extractor import extract_document
from vector_db import vector_db  # Import the vector database

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Configure Gemini API
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    logger.warning("GEMINI_API_KEY not found in environment variables. Using mock embeddings.")
else:
    logger.info("Configuring Gemini API with provided key")
    genai.configure(api_key=api_key)

# Global variable to store document chunks with embeddings
DOCUMENT_CHUNKS = []

# Create FastAPI app
app = FastAPI(title="RailGPT Document Processor")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define API request and response models
class QueryRequest(BaseModel):
    query: str

class WebsiteScrapeRequest(BaseModel):
    url: str
    
class WebsiteAddRequest(BaseModel):
    url: str
    submitted_by: Optional[str] = None
    role: Optional[str] = None

class Source(BaseModel):
    source_type: str  # "document" or "website"
    # For documents
    filename: Optional[str] = None
    page: Optional[int] = None
    # For websites
    url: Optional[str] = None

class QueryResponse(BaseModel):
    answer: str  # Combined answer from all sources
    document_answer: Optional[str] = None  # Answer only from document sources
    website_answer: Optional[str] = None  # Answer only from website sources
    sources: List[Source]  # All sources
    document_sources: Optional[List[Source]] = None  # Only document sources
    website_sources: Optional[List[Source]] = None  # Only website sources

class ChunkData(BaseModel):
    filename: str
    page: int
    chunk_id: str
    text: str
    embedding: Optional[List[float]] = None

# Document processing functions
def clean_text(text: str) -> str:
    """Clean and normalize text from PDF extraction."""
    text = re.sub(r'\s+', ' ', text)  # Replace multiple whitespace with single space
    return text.strip()

def generate_embedding(text: str) -> List[float]:
    """Generate embedding vector for text using Gemini API."""
    try:
        if not api_key:
            # If no API key, return mock embedding (random vector)
            return list(np.random.rand(768))
        
        # Use Gemini API to generate embedding
        # Using the text embedding model directly
        response = genai.embed_content(
            model="models/embedding-001",
            content=text,
            task_type="retrieval_document"
        )
        
        # Return the embedding as a list of floats
        return list(response['embedding'])
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Fallback to random embedding if API call fails
        return list(np.random.rand(768))

def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """Calculate cosine similarity between two vectors."""
    # Convert lists to numpy arrays
    vec1 = np.array(vec1)
    vec2 = np.array(vec2)
    
    # Compute cosine similarity
    dot_product = np.dot(vec1, vec2)
    norm_vec1 = np.linalg.norm(vec1)
    norm_vec2 = np.linalg.norm(vec2)
    
    # Avoid division by zero
    if norm_vec1 == 0 or norm_vec2 == 0:
        return 0.0
    
    return dot_product / (norm_vec1 * norm_vec2)

# Define a relevance threshold to filter out irrelevant results
# 0.4 is a moderate threshold that will filter extreme irrelevance but still return useful results
RELEVANCE_THRESHOLD = 0.4

def group_chunks_by_source(chunks):
    """
    Group chunks by source type into document and website categories.
    
    Args:
        chunks: List of chunks with source_type information
        
    Returns:
        Tuple of (document_chunks, website_chunks)
    """
    document_chunks = [c for c in chunks if c.get("source_type", "") == "document"]
    website_chunks = [c for c in chunks if c.get("source_type", "") == "website"]
    
    return document_chunks, website_chunks


def find_similar_chunks(query_embedding, top_k=30):  # Increased to 30 for better coverage across multiple sources
    """Find chunks with embeddings most similar to the query embedding.
    
    Uses vector database for efficient search when available, with fallback to in-memory search.
    Only returns chunks where the cosine similarity is at least RELEVANCE_THRESHOLD.
    
    Args:
        query_embedding: The query embedding vector
        top_k: Number of most similar chunks to return
        
    Returns:
        List of chunks with highest cosine similarity to query
    """
    # Check if vector database has content
    if vector_db.get_stats()["total_chunks"] > 0:
        # Use vector database for efficient search
        logger.info(f"Using vector database for search with threshold {RELEVANCE_THRESHOLD}")
        return vector_db.search(query_embedding, top_k=top_k, threshold=RELEVANCE_THRESHOLD)
    
    # Fallback to in-memory search if vector database is empty
    logger.info(f"Falling back to in-memory search with threshold {RELEVANCE_THRESHOLD}")
    
    if not DOCUMENT_CHUNKS:
        return []
    
    similarity_scores = []
    
    for chunk in DOCUMENT_CHUNKS:
        if "embedding" not in chunk:
            continue
        
        chunk_embedding = chunk["embedding"]
        similarity = cosine_similarity(query_embedding, chunk_embedding)
        
        # Only include chunks that meet the relevance threshold
        if similarity >= RELEVANCE_THRESHOLD:
            # Create a copy of the chunk without the embedding to match vector_db format
            chunk_copy = {k: v for k, v in chunk.items() if k != "embedding"}
            chunk_copy["similarity"] = similarity
            similarity_scores.append({
                "chunk": chunk_copy,
                "similarity": similarity
            })
    
    # Log the threshold and number of results
    logger.info(f"Using relevance threshold: {RELEVANCE_THRESHOLD}. Found {len(similarity_scores)} relevant chunks.")
    
    # Sort by similarity (highest first)
    similarity_scores = sorted(similarity_scores, key=lambda x: x["similarity"], reverse=True)
    
    # Return top_k most similar chunks
    return [item["chunk"] for item in similarity_scores[:top_k]]

def generate_llm_answer(query: str, similar_chunks: List[Dict[str, Any]], system_prompt: str = None) -> Tuple[str, List[Dict[str, Any]]]:
    """Generate a coherent answer using Gemini LLM based on similar chunks.
    
    Args:
        query: The user's question
        similar_chunks: List of context chunks to use for generating the answer
        system_prompt: Optional system prompt to guide the LLM's response
    
    Returns:
        Tuple of (answer_text, source_data)
    """
    if not similar_chunks:
        return "I don't have enough information to answer that question.", []
    
    if not api_key:
        logger.warning("No Gemini API key available for LLM answer generation. Using chunk concatenation.")
        # Fallback to concatenating chunks if no API key
        combined_text = "\n\n".join([chunk["text"] for chunk in similar_chunks])
        
        # Create source metadata based on chunk type
        sources = []
        for chunk in similar_chunks:
            if "source_type" in chunk and chunk["source_type"] == "website":
                # Website source
                source = {"source_type": "website", "url": chunk.get("url", "")}
            else:
                # Document source (default)
                source = {"source_type": "document", "filename": chunk.get("filename", ""), "page": chunk.get("page", 1)}
            
            # Only add unique sources
            if source not in sources:
                sources.append(source)
                
        return f"Based on the available information:\n\n{combined_text}", sources
    
    try:
        # Prepare context with chunk texts and sources
        context_parts = []
        sources = []
        
        for chunk in similar_chunks:
            # Format the source reference based on chunk type
            if "source_type" in chunk and chunk["source_type"] == "website":
                # Website source
                source_ref = f"from website: {chunk.get('url', '')}"
                source = {"source_type": "website", "url": chunk.get("url", "")}
            else:
                # Document source (default)
                source_ref = f"from {chunk.get('filename', '')}, page {chunk.get('page', 1)}"
                source = {"source_type": "document", "filename": chunk.get("filename", ""), "page": chunk.get("page", 1)}
            
            # Add to context
            context_parts.append(f"- {chunk['text']} ({source_ref})")
            
            # Only add unique sources
            if source not in sources:
                sources.append(source)
        
        context = "\n".join(context_parts)
        
        # Construct prompt for Gemini
        system_instruction = system_prompt if system_prompt else "You are RailGPT, an assistant for Indian Railways. Based on the following document and website excerpts, answer the user's question accurately and concisely."
        
        prompt = f"""
        {system_instruction}
        
        Question: {query}
        Context:
        {context}
        
        Provide a clear answer. Do not include "Sources:" in your response as this will be added separately.
        """
        
        # Call Gemini model to generate response
        model = genai.GenerativeModel('gemini-2.0-flash')
        response = model.generate_content(prompt)
        
        # Extract and return the generated answer
        answer = response.text.strip()
        logger.info(f"Generated LLM answer: {answer[:100]}...")
        
        return answer, sources
        
    except Exception as e:
        logger.error(f"Error generating LLM answer: {str(e)}")
        # Fallback to concatenating chunks if LLM fails
        combined_text = "\n\n".join([chunk["text"] for chunk in similar_chunks])
        return f"Based on the available information:\n\n{combined_text}", sources

def create_chunks(text: str, filename: str, page_num: int, target_size: int = 400, overlap: int = 50) -> List[Dict[str, Any]]:
    """Split text into chunks of approximately target_size words with overlap."""
    words = text.split()
    total_words = len(words)
    chunks = []
    
    if total_words == 0:
        return chunks
    
    # If text is smaller than target size, return as a single chunk
    if total_words <= target_size:
        chunk_id = f"{os.path.splitext(filename)[0]}_{page_num}_0"
        
        # Generate embedding for the chunk
        embedding = generate_embedding(text)
        
        chunks.append({
            "filename": filename,
            "page": page_num,
            "chunk_id": chunk_id,
            "text": text,
            "embedding": embedding
        })
        return chunks
    
    # Create overlapping chunks
    chunk_index = 0
    start_idx = 0
    
    while start_idx < total_words:
        end_idx = min(start_idx + target_size, total_words)
        
        # Create chunk
        chunk_text = " ".join(words[start_idx:end_idx])
        chunk_id = f"{os.path.splitext(filename)[0]}_{page_num}_{chunk_index}"
        
        # Generate embedding for the chunk
        embedding = generate_embedding(chunk_text)
        
        chunks.append({
            "filename": filename,
            "page": page_num,
            "chunk_id": chunk_id,
            "text": chunk_text,
            "embedding": embedding
        })
        
        # If this is the last chunk, break
        if end_idx >= total_words:
            break
            
        # Move to next chunk start, accounting for overlap
        start_idx = end_idx - overlap
        chunk_index += 1
    
    return chunks

def load_documents(data_dir: str = './data') -> None:
    global DOCUMENT_CHUNKS
    
    logger.info("Loading documents on startup")
    
    # Import the document extractor module
    from document_extractor import extract_document
    
    # Get all supported files in data directory
    supported_extensions = ('.pdf', '.docx', '.xlsx', '.jpg', '.jpeg', '.png', '.tiff', '.tif')
    document_files = []
    
    for file in os.listdir(data_dir):
        if file.lower().endswith(supported_extensions):
            document_files.append(os.path.join(data_dir, file))
    
    logger.info(f"Found {len(document_files)} supported document files in {data_dir}")
    
    # Process each document
    all_chunks = []
    for doc_path in document_files:
        filename = os.path.basename(doc_path)
        logger.info(f"Processing {doc_path}")
        
        try:
            # Extract text and create chunks using the document extractor
            document_chunks = extract_document(doc_path)
            
            # Generate embeddings for each chunk
            for chunk in document_chunks:
                # Add embedding to each chunk
                chunk_text = chunk.get("text", "")
                embedding = generate_embedding(chunk_text)
                chunk["embedding"] = embedding
            
            all_chunks.extend(document_chunks)
            logger.info(f"Processed {filename}: extracted {len(document_chunks)} chunks")
            
        except Exception as e:
            logger.error(f"Error processing {filename}: {str(e)}")
    
    DOCUMENT_CHUNKS = all_chunks
    logger.info(f"Finished processing. Total chunks created: {len(DOCUMENT_CHUNKS)}")
    
    logger.info(f"Loaded {len(DOCUMENT_CHUNKS)} document chunks on startup")

@app.on_event("startup")
async def startup_event():
    logger.info("Loading documents on startup")
    try:
        # Get the absolute path for the data directory
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
        load_documents(data_dir)
    except Exception as e:
        logger.error(f"Error loading documents: {str(e)}")

# Root endpoint
@app.get("/")
def read_root():
    return {"message": "RailGPT Document Processor API", "status": "online"}

# Chunks endpoint
@app.get("/api/chunks")
def get_chunks():
    logger.info(f"Requested chunks - returning first 5 of {len(DOCUMENT_CHUNKS)}")
    # Return chunks without embedding for efficiency
    chunks_without_embedding = []
    for chunk in DOCUMENT_CHUNKS[:5]:
        chunk_copy = dict(chunk)
        if "embedding" in chunk_copy:
            del chunk_copy["embedding"]
        chunks_without_embedding.append(chunk_copy)
    return chunks_without_embedding if DOCUMENT_CHUNKS else []

# Website scraping endpoint
@app.post("/api/scrape-website")
async def scrape_website(request: WebsiteScrapeRequest):
    url = request.url
    logger.info(f"Received request to scrape website: {url}")
    
    if not url or not url.strip():
        raise HTTPException(status_code=400, detail="URL cannot be empty")
    
    try:
        # Extract text from website using fallback extraction methods
        website_chunks = extract_website_text(url)
        
        if not website_chunks:
            raise HTTPException(status_code=422, detail=f"Failed to extract content from {url}")
        
        # Generate embeddings for each chunk
        for chunk in website_chunks:
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding
        
        # Add to global document chunks
        global DOCUMENT_CHUNKS
        DOCUMENT_CHUNKS.extend(website_chunks)
        
        logger.info(f"Added {len(website_chunks)} website chunks to knowledge base")
        
        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in website_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)
        
        return {
            "message": f"Successfully scraped {url}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding
        }
        
    except Exception as e:
        logger.error(f"Error scraping website {url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error scraping website: {str(e)}")

# Query endpoint for semantic search and LLM answer generation
@app.post("/api/query")
async def query(request: QueryRequest):
    query_text = request.query
    logger.info(f"Received query: {query_text}")
    
    if not query_text.strip():
        raise HTTPException(status_code=400, detail="Query cannot be empty")
    
    # Generate embedding for query
    logger.info("Generating embedding for query")
    query_embedding = generate_embedding(query_text)
    
    # Find similar chunks
    logger.info("Finding similar chunks")
    similar_chunks = find_similar_chunks(query_embedding, top_k=5)  # Increased to 5 for better context
    
    if not similar_chunks:
        logger.warning("No relevant information found for query (similarity below threshold)")
        return QueryResponse(
            answer="No relevant information found in the uploaded documents or extracted websites.",
            sources=[]
        )
    
    # Group chunks by source type using our helper function
    document_chunks, website_chunks = group_chunks_by_source(similar_chunks)
    
    logger.info(f"Found {len(document_chunks)} document chunks and {len(website_chunks)} website chunks")
    
    # Generate coherent answer using Gemini LLM for all chunks
    logger.info("Generating combined answer using Gemini LLM")
    combined_answer, all_sources_data = generate_llm_answer(query_text, similar_chunks)
    
    # Initialize document and website specific answers and sources
    document_answer = None
    website_answer = None
    document_sources_data = []
    website_sources_data = []
    
    # Generate separate answers for document chunks if available
    if document_chunks:
        logger.info("Generating answer from document sources")
        document_answer, document_sources_data = generate_llm_answer(
            query_text, 
            document_chunks,
            "You are answering based ONLY on the provided document excerpts. Only reference information from these documents. If the documents don't contain information relevant to the question, respond with 'No relevant information found in the documents.'"
        )
        # Check if there was no relevant information found
        if document_answer and ("no relevant information" in document_answer.lower() or 
                             "don't have enough information" in document_answer.lower()):
            document_answer = None  # Set to None to hide card in frontend
    
    # Generate separate answers for website chunks if available
    if website_chunks:
        logger.info("Generating answer from website sources")
        website_answer, website_sources_data = generate_llm_answer(
            query_text, 
            website_chunks,
            "You are answering based ONLY on the provided website excerpts. Only reference information from these websites. If the websites don't contain information relevant to the question, respond with 'No relevant information found in the websites.'"
        )
        # Check if there was no relevant information found
        if website_answer and ("no relevant information" in website_answer.lower() or 
                            "don't have enough information" in website_answer.lower()):
            website_answer = None  # Set to None to hide card in frontend
    
    # Convert source dictionaries to Source objects
    all_sources = [Source(**source) for source in all_sources_data]
    document_sources = [Source(**source) for source in document_sources_data] if document_sources_data else None
    website_sources = [Source(**source) for source in website_sources_data] if website_sources_data else None
    
    return QueryResponse(
        answer=combined_answer,
        document_answer=document_answer,
        website_answer=website_answer,
        sources=all_sources,
        document_sources=document_sources,
        website_sources=website_sources
    )

# Document upload endpoint
@app.post("/api/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    uploaded_by: Optional[str] = Form(None),
    role: Optional[str] = Form(None)
):
    logger.info(f"Received document upload request: {file.filename}")
    
    # Validate file extension
    supported_extensions = ('.pdf', '.docx', '.xlsx', '.jpg', '.jpeg', '.png', '.tiff', '.tif')
    file_ext = os.path.splitext(file.filename)[1].lower()
    
    if file_ext not in supported_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type: {file_ext}. Supported types: {', '.join(supported_extensions)}"
        )
    
    try:
        # Create uploads directory if it doesn't exist
        upload_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'uploads')
        os.makedirs(upload_dir, exist_ok=True)
        
        # Save the uploaded file
        file_path = os.path.join(upload_dir, file.filename)
        
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        logger.info(f"File saved to {file_path}")
        
        # Extract text and create chunks using the document extractor
        document_chunks = extract_document(file_path)
        
        if not document_chunks:
            raise HTTPException(
                status_code=422,
                detail=f"Failed to extract content from {file.filename}"
            )
        
        # Generate embeddings for each chunk
        for chunk in document_chunks:
            # Add metadata
            chunk["source_type"] = "document"
            if uploaded_by:
                chunk["uploaded_by"] = uploaded_by
            if role:
                chunk["role"] = role
            
            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding
        
        # Add to global document chunks (legacy in-memory storage)
        global DOCUMENT_CHUNKS
        DOCUMENT_CHUNKS.extend(document_chunks)
        
        # Add to vector database for efficient search
        vector_db.add_chunks(document_chunks)
        
        logger.info(f"Added {len(document_chunks)} document chunks to knowledge base and vector database")
        
        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in document_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)
        
        return {
            "message": f"Successfully processed {file.filename}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding
        }
        
    except Exception as e:
        logger.error(f"Error processing uploaded document {file.filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")
    finally:
        file.file.close()

# Website add endpoint
@app.post("/api/add-website")
async def add_website(request: WebsiteAddRequest):
    url = request.url
    submitted_by = request.submitted_by
    role = request.role
    
    logger.info(f"Received request to add website: {url}")
    
    if not url or not url.strip():
        raise HTTPException(status_code=400, detail="URL cannot be empty")
    
    try:
        # Extract text from website using fallback extraction methods
        website_chunks = extract_website_text(url)
        
        if not website_chunks:
            raise HTTPException(status_code=422, detail=f"Failed to extract content from {url}")
        
        # Generate embeddings for each chunk
        for chunk in website_chunks:
            # Add additional metadata
            if submitted_by:
                chunk["submitted_by"] = submitted_by
            if role:
                chunk["role"] = role
            
            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding
        
        # Add to global document chunks (legacy in-memory storage)
        global DOCUMENT_CHUNKS
        DOCUMENT_CHUNKS.extend(website_chunks)
        
        # Add to vector database for efficient search
        vector_db.add_chunks(website_chunks)
        
        logger.info(f"Added {len(website_chunks)} website chunks to knowledge base and vector database")
        
        # Log vector database stats
        logger.info(f"Vector database stats: {vector_db.get_stats()}")
        
        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in website_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)
        
        return {
            "message": f"Successfully added website {url}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding
        }
        
    except Exception as e:
        logger.error(f"Error adding website {url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding website: {str(e)}")

# For direct execution
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
