#!/usr/bin/env python3
"""
Test script to verify markdown formatting fix
"""
import requests
import json

def test_markdown_formatting():
    print("🧪 Testing Markdown Formatting Fix")
    print("=" * 50)
    
    try:
        # Test the backend query
        response = requests.post('http://localhost:8000/api/query', 
                               json={'query': 'What is ACP', 'model': 'gemini-2.0-flash'},
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print('✅ Backend query successful')
            
            # Check document answer
            doc_answer = data.get('document_answer', '')
            if doc_answer:
                print(f'\n📄 Document Answer Preview:')
                print(f'{doc_answer[:300]}...')
                
                # Check for asterisks that should be formatted
                if '*' in doc_answer:
                    print('\n⚠️  Answer contains asterisks - these should be formatted as bold by frontend')
                    
                    # Extract examples of asterisk usage
                    import re
                    asterisk_matches = re.findall(r'\*([^*]+)\*', doc_answer)
                    if asterisk_matches:
                        print('\n🎯 Examples that should become bold:')
                        for i, match in enumerate(asterisk_matches[:3]):
                            print(f'   {i+1}. "*{match}*" → **{match}**')
                else:
                    print('✅ Answer does not contain raw asterisks')
            
            # Check sources
            doc_sources = data.get('document_sources', [])
            web_sources = data.get('website_sources', [])
            print(f'\n📊 Sources Found:')
            print(f'   📄 Document sources: {len(doc_sources)}')
            print(f'   🌐 Website sources: {len(web_sources)}')
            
            if len(doc_sources) <= 2:
                print('✅ Source filtering working correctly (≤2 sources)')
            else:
                print(f'⚠️  Too many sources shown: {len(doc_sources)}')
                
        else:
            print(f'❌ Backend query failed: {response.status_code}')
            print(f'Response: {response.text}')
            
    except Exception as e:
        print(f'❌ Error testing backend: {e}')

    print("\n" + "=" * 50)
    print("🎯 Frontend Testing Instructions:")
    print("1. Open: http://localhost:3000")
    print("2. Query: 'What is ACP'")
    print("3. Check that asterisks (*text*) appear as bold text")
    print("4. Try the Format/Plain toggle button")
    print("5. Test PDF viewer with multiple viewer options")

if __name__ == "__main__":
    test_markdown_formatting() 