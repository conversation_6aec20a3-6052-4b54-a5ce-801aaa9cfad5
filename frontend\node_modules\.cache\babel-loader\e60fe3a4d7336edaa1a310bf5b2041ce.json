{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\AppRouter.tsx\";\nimport React, { useState } from 'react';\nimport { BrowserRouter as Router, Switch, Route } from 'react-router-dom';\nimport App from './App';\nimport DocumentsPage from './pages/documents/DocumentsPage';\nimport WebsitesPage from './pages/websites/WebsitesPage';\nimport SettingsPage from './pages/settings/SettingsPage';\nimport DocumentViewer from './pages/DocumentViewer';\nimport Header from './components/layout/Header';\nimport { ChatProvider } from './contexts/ChatContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppRouter = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(ChatProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col h-screen overflow-hidden bg-white transition-colors duration-300\",\n        children: [/*#__PURE__*/_jsxDEV(Header, {\n          onSidebarToggle: () => setSidebarOpen(!sidebarOpen),\n          sidebarOpen: sidebarOpen\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-grow bg-gray-50 h-full pt-16 sm:pt-20 overflow-hidden transition-colors duration-300\",\n          children: /*#__PURE__*/_jsxDEV(Switch, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              exact: true,\n              path: \"/\",\n              render: () => /*#__PURE__*/_jsxDEV(App, {\n                sidebarOpen: sidebarOpen,\n                setSidebarOpen: setSidebarOpen\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/documents\",\n              component: DocumentsPage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/websites\",\n              component: WebsitesPage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/categories\",\n              component: ManageCategoriesPage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/settings\",\n              component: SettingsPage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/viewer\",\n              component: DocumentViewer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\nexport default AppRouter;", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Switch", "Route", "App", "DocumentsPage", "WebsitesPage", "SettingsPage", "DocumentViewer", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "AppRouter", "sidebarOpen", "setSidebarOpen", "children", "className", "onSidebarToggle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "exact", "path", "render", "component", "ManageCategoriesPage"], "sources": ["C:/IR App/frontend/src/AppRouter.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { BrowserRouter as Router, Switch, Route } from 'react-router-dom';\nimport App from './App';\nimport DocumentsPage from './pages/documents/DocumentsPage';\nimport WebsitesPage from './pages/websites/WebsitesPage';\nimport SettingsPage from './pages/settings/SettingsPage';\nimport DocumentViewer from './pages/DocumentViewer';\nimport Header from './components/layout/Header';\nimport { ChatProvider } from './contexts/ChatContext';\n\nconst AppRouter: React.FC = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <ChatProvider>\n      <Router>\n        <div className=\"flex flex-col h-screen overflow-hidden bg-white transition-colors duration-300\">\n          {/* Fixed header */}\n          <Header\n            onSidebarToggle={() => setSidebarOpen(!sidebarOpen)}\n            sidebarOpen={sidebarOpen}\n          />\n          {/* Main content area with padding-top to account for fixed header */}\n          <main className=\"flex-grow bg-gray-50 h-full pt-16 sm:pt-20 overflow-hidden transition-colors duration-300\">\n            <Switch>\n              <Route exact path=\"/\" render={() => <App sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />} />\n              <Route path=\"/documents\" component={DocumentsPage} />\n              <Route path=\"/websites\" component={WebsitesPage} />\n              <Route path=\"/categories\" component={ManageCategoriesPage} />\n              <Route path=\"/settings\" component={SettingsPage} />\n              <Route path=\"/viewer\" component={DocumentViewer} />\n            </Switch>\n          </main>\n        </div>\n      </Router>\n    </ChatProvider>\n  );\n};\n\nexport default AppRouter;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,YAAY,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAErD,oBACEa,OAAA,CAACF,YAAY;IAAAM,QAAA,eACXJ,OAAA,CAACX,MAAM;MAAAe,QAAA,eACLJ,OAAA;QAAKK,SAAS,EAAC,gFAAgF;QAAAD,QAAA,gBAE7FJ,OAAA,CAACH,MAAM;UACLS,eAAe,EAAEA,CAAA,KAAMH,cAAc,CAAC,CAACD,WAAW,CAAE;UACpDA,WAAW,EAAEA;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEFV,OAAA;UAAMK,SAAS,EAAC,2FAA2F;UAAAD,QAAA,eACzGJ,OAAA,CAACV,MAAM;YAAAc,QAAA,gBACLJ,OAAA,CAACT,KAAK;cAACoB,KAAK;cAACC,IAAI,EAAC,GAAG;cAACC,MAAM,EAAEA,CAAA,kBAAMb,OAAA,CAACR,GAAG;gBAACU,WAAW,EAAEA,WAAY;gBAACC,cAAc,EAAEA;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzGV,OAAA,CAACT,KAAK;cAACqB,IAAI,EAAC,YAAY;cAACE,SAAS,EAAErB;YAAc;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDV,OAAA,CAACT,KAAK;cAACqB,IAAI,EAAC,WAAW;cAACE,SAAS,EAAEpB;YAAa;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDV,OAAA,CAACT,KAAK;cAACqB,IAAI,EAAC,aAAa;cAACE,SAAS,EAAEC;YAAqB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DV,OAAA,CAACT,KAAK;cAACqB,IAAI,EAAC,WAAW;cAACE,SAAS,EAAEnB;YAAa;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDV,OAAA,CAACT,KAAK;cAACqB,IAAI,EAAC,SAAS;cAACE,SAAS,EAAElB;YAAe;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB,CAAC;AAED,eAAeT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}