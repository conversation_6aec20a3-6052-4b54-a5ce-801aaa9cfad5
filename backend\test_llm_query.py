#!/usr/bin/env python3
"""
Test script to debug LLM meaning query.
"""

import requests
import json

API_URL = "http://localhost:8000"

def test_llm_query():
    """Test the LLM meaning query specifically."""
    
    print("🔍 Testing 'LLM meaning' Query Debug")
    print("=" * 50)
    
    # Test the debug endpoints
    try:
        print("\n📊 Checking website search for 'LLM meaning':")
        response = requests.get(f"{API_URL}/api/debug/website-search", 
                              params={"query": "LLM meaning"}, 
                              timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Website search successful")
            print(f"📊 Total website chunks: {data.get('total_website_chunks', 0)}")
            print(f"🔎 Matching chunks found: {data.get('matching_chunks_found', 0)}")
            
            if data.get('matching_chunks'):
                print(f"📄 Matching chunks:")
                for i, chunk in enumerate(data['matching_chunks'][:3]):
                    similarity = chunk.get('similarity', 0)
                    url = chunk.get('url', 'Unknown')
                    text_preview = chunk.get('text', '')[:100] + '...'
                    print(f"   {i+1}. Similarity: {similarity:.3f} | URL: {url}")
                    print(f"      Text: {text_preview}")
            else:
                print(f"❌ No matching chunks found")
        else:
            print(f"❌ Website search failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing website search: {str(e)}")
    
    # Test the regular query
    try:
        print(f"\n🧪 Testing regular query with Gemini:")
        response = requests.post(f"{API_URL}/api/query", 
            json={
                "query": "LLM meaning",
                "model": "gemini-2.0-flash",
                "fallback_enabled": True
            }, 
            timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Query successful")
            print(f"📄 Document answer: {'Yes' if data.get('document_answer') else 'No'}")
            print(f"🌐 Website answer: {'Yes' if data.get('website_answer') else 'No'}")
            print(f"🧠 LLM fallback: {'Yes' if data.get('llm_fallback') else 'No'}")
            print(f"🤖 Model used: {data.get('llm_model', 'Unknown')}")
            print(f"📊 Document sources: {len(data.get('document_sources', []))}")
            print(f"📊 Website sources: {len(data.get('website_sources', []))}")
            
            # Show the actual answer
            answer = data.get('answer', '')
            print(f"\n💬 Full answer:")
            print(f"{answer}")
            
            # Show sources
            if data.get('document_sources'):
                print(f"\n📄 Document sources:")
                for source in data['document_sources']:
                    print(f"   - {source.get('filename', 'Unknown')} (page {source.get('page', 'Unknown')})")
                    
            if data.get('website_sources'):
                print(f"\n🌐 Website sources:")
                for source in data['website_sources']:
                    print(f"   - {source.get('url', 'Unknown')}")
                    
        else:
            print(f"❌ Query failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing query: {str(e)}")

if __name__ == "__main__":
    print("🚀 LLM Query Debug Tool")
    print("=" * 60)
    
    # Test if API is available
    try:
        response = requests.get(f"{API_URL}/api/health", timeout=3)
        if response.status_code == 200:
            print("✅ API is healthy and responding")
        else:
            print(f"❌ API health check failed: {response.status_code}")
            exit(1)
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        exit(1)
    
    test_llm_query()
    
    print(f"\n✅ Debug complete!") 