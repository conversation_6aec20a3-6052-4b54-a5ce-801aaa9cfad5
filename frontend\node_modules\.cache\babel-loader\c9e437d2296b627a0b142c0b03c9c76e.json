{"ast": null, "code": "import React,{useState,useEffect}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const VisualContent=_ref=>{var _source$visual_conten;let{source}=_ref;const[selectedTab,setSelectedTab]=useState('content');const[imageLoading,setImageLoading]=useState(true);const[imageError,setImageError]=useState(false);const[imageTries,setImageTries]=useState(0);const[cacheBuster,setCacheBuster]=useState(`cb-${Date.now()}`);const[visibleDebug,setVisibleDebug]=useState(false);// Refresh image on source change\nuseEffect(()=>{// Reset state when source changes\nsetImageLoading(true);setImageError(false);setImageTries(0);setCacheBuster(`cb-${Date.now()}-${Math.random().toString(36).substring(2,9)}`);console.log('🔄 Visual content source changed:',{content_type:source.content_type,filename:source.filename,page:source.page,has_visual_content:!!source.visual_content,storage_url:source.storage_url?`${source.storage_url.substring(0,30)}...`:'None'});},[source.filename,source.page,source.storage_url]);if(!source.content_type||source.content_type==='text'){return null;// No visual content to display\n}const renderTableContent=()=>{const visualContent=source.visual_content;if(!visualContent)return null;// If we have table data, render it as a proper table\nif(visualContent.table_data&&Array.isArray(visualContent.table_data)){const tableData=visualContent.table_data;if(tableData.length===0)return null;const headers=tableData[0]||[];const rows=tableData.slice(1);return/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto w-full\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full bg-white border border-gray-200 rounded-lg table-fixed\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsx(\"tr\",{children:headers.map((header,idx)=>/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\",style:{minWidth:'100px',maxWidth:'200px'},children:String(header||'').trim()||`Column ${idx+1}`},idx))})}),/*#__PURE__*/_jsx(\"tbody\",{children:rows.map((row,rowIdx)=>/*#__PURE__*/_jsx(\"tr\",{className:\"hover:bg-gray-50 transition-colors\",children:headers.map((_,cellIdx)=>/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100 truncate\",title:String(row[cellIdx]||''),children:String(row[cellIdx]||'').trim()||'-'},cellIdx))},rowIdx))})]})});}// If we have markdown table, render it as HTML\nif(visualContent.markdown_table){return/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto w-full\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg border border-gray-200\",children:/*#__PURE__*/_jsx(\"div\",{className:\"prose prose-sm max-w-none p-0\",dangerouslySetInnerHTML:{__html:markdownTableToHtml(visualContent.markdown_table)}})})});}// Fallback: Try to display as text table if available\nif(visualContent.text_table){return/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg border border-gray-200 p-4\",children:/*#__PURE__*/_jsx(\"pre\",{className:\"text-sm text-gray-700 whitespace-pre-wrap font-mono\",children:visualContent.text_table})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 rounded-lg p-4 text-sm text-gray-600\",children:[\"Table data is not available in a displayable format.\",visualContent&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2 text-xs\",children:[\"Available data: \",Object.keys(visualContent).join(', ')]})]});};const getImageSources=()=>{const visualContent=source.visual_content;const sources=[];// 1. Try storage URL if available (with cache buster)\nif(source.storage_url){const hasQueryParams=source.storage_url.includes('?');const cacheParam=hasQueryParams?`&_cb=${cacheBuster}`:`?_cb=${cacheBuster}`;sources.push({url:`${source.storage_url}${cacheParam}`,type:'Storage URL'});}// 2. Try base64 data if available\nif(visualContent!==null&&visualContent!==void 0&&visualContent.base64_data){sources.push({url:`data:image/png;base64,${visualContent.base64_data}`,type:'Base64 Data'});}// 3. Generate path based on filename and page\nif(source.filename&&source.page!==undefined){const safeName=source.filename.replace(/[^a-zA-Z0-9]/g,'_').toLowerCase();sources.push({url:`/images/${safeName}_page${source.page}.png?_cb=${cacheBuster}`,type:'Generated Path'});}// 4. Logo as absolute fallback\nsources.push({url:`/logo512.png?_cb=${cacheBuster}`,type:'Default Logo'});return sources;};const renderImageContent=()=>{const visualContent=source.visual_content;console.log('DEBUG: Rendering image for',source.filename,'page',source.page);console.log('DEBUG: storage_url =',source.storage_url);console.log('DEBUG: base64_data available =',!!(visualContent!==null&&visualContent!==void 0&&visualContent.base64_data));// Show debug info with image\nconst debugInfo=/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-50 p-2 mb-2 rounded text-xs\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"File:\"}),\" \",source.filename||'Unknown']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Page:\"}),\" \",source.page||'Unknown']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"URL:\"}),\" \",source.storage_url?'Available':'Not available']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Time:\"}),\" \",new Date().toISOString()]})]});// Try to find actual image matching Project 1, Project 2, etc. in query response\nif(source.content_type==='image'&&source.filename&&source.filename.includes('Project')){return/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[debugInfo,imageLoading&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"})}),/*#__PURE__*/_jsx(\"img\",{src:source.storage_url||`/logo512.png?t=${Date.now()}`,alt:`Image from page ${source.page} of ${source.filename}`,className:\"max-w-full h-auto rounded-lg border border-gray-200 shadow-md\",onLoad:()=>{console.log('DEBUG: Image loaded successfully');setImageLoading(false);},onError:e=>{console.error('DEBUG: Image failed to load:',e);setImageLoading(false);setImageError(true);}}),imageError&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-red-50 rounded-lg p-4 text-center text-gray-600 mt-2\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Image could not be loaded\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs mt-1\",children:\"Please check the document and try again\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{console.log('DEBUG: Retrying image load');setImageLoading(true);setImageError(false);},className:\"mt-2 px-2 py-1 bg-blue-100 rounded text-sm\",children:\"Retry\"})]})]});}// Try storage URL first\nif(source.storage_url){var _source$storage_url;return/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[debugInfo,imageLoading&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"})}),/*#__PURE__*/_jsx(\"img\",{src:`${source.storage_url}?t=${Date.now()}`,alt:`Image from page ${source.page} of ${source.filename}`,className:`max-w-full h-auto rounded-lg border border-gray-200 ${imageLoading?'opacity-0':'opacity-100'}`,onLoad:()=>setImageLoading(false),onError:()=>{console.error('DEBUG: Storage URL image failed to load:',source.storage_url);setImageLoading(false);setImageError(true);}}),imageError&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-100 rounded-lg p-4 text-center text-gray-600\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Image could not be loaded\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs mt-1\",children:[\"URL: \",(_source$storage_url=source.storage_url)===null||_source$storage_url===void 0?void 0:_source$storage_url.substring(0,30),\"...\"]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{console.log('DEBUG: Retrying image load');setImageLoading(true);setImageError(false);},className:\"mt-2 px-2 py-1 bg-blue-100 rounded text-sm\",children:\"Retry\"})]})]});}// Try base64 data if available\nif(visualContent!==null&&visualContent!==void 0&&visualContent.base64_data){return/*#__PURE__*/_jsxs(\"div\",{children:[debugInfo,/*#__PURE__*/_jsx(\"img\",{src:`data:image/png;base64,${visualContent.base64_data}`,alt:`Image from page ${source.page} of ${source.filename}`,className:\"max-w-full h-auto rounded-lg border border-gray-200\",onError:()=>{console.error('DEBUG: Base64 image failed to load');setImageError(true);}})]});}// Default to logo if no other image is available\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 rounded-lg p-4 text-center\",children:[debugInfo,/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-2\",children:\"No image content available from the document\"}),/*#__PURE__*/_jsx(\"img\",{src:`/logo512.png?t=${Date.now()}`,alt:\"Default logo\",className:\"max-w-full h-auto rounded-lg border border-gray-200 mx-auto\",style:{maxHeight:'200px'}})]});};const renderChartContent=()=>{const visualContent=source.visual_content;if(!visualContent)return null;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold text-purple-800 mb-3 flex items-center\",children:\"\\uD83D\\uDCCA Chart/Diagram Detected\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[visualContent.description&&/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-700\",children:visualContent.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-4 text-xs text-gray-600\",children:[visualContent.drawing_count&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"Drawing Elements:\"}),\" \",visualContent.drawing_count]}),visualContent.confidence&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"Confidence:\"}),\" \",visualContent.confidence]}),visualContent.has_lines&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"w-2 h-2 bg-green-400 rounded-full mr-2\"}),\"Contains lines\"]}),visualContent.has_curves&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"w-2 h-2 bg-blue-400 rounded-full mr-2\"}),\"Contains curves\"]})]})]})]});};const getContentTypeLabel=()=>{switch(source.content_type){case'table':return'📋 Table';case'image':return'🖼️ Image';case'chart_diagram':return'📊 Chart/Diagram';default:return'📄 Content';}};const getContentTypeColor=()=>{switch(source.content_type){case'table':return'from-green-50 to-emerald-50 border-green-200';case'image':return'from-blue-50 to-cyan-50 border-blue-200';case'chart_diagram':return'from-purple-50 to-indigo-50 border-purple-200';default:return'from-gray-50 to-slate-50 border-gray-200';}};return/*#__PURE__*/_jsxs(\"div\",{className:`bg-gradient-to-br ${getContentTypeColor()} rounded-lg border p-4 mt-3`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-3\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-semibold text-gray-800 text-sm flex items-center\",children:[getContentTypeLabel(),source.page&&/*#__PURE__*/_jsxs(\"span\",{className:\"ml-2 text-xs bg-white bg-opacity-70 px-2 py-1 rounded\",children:[\"Page \",source.page]})]}),source.content_type==='table'&&((_source$visual_conten=source.visual_content)===null||_source$visual_conten===void 0?void 0:_source$visual_conten.metadata)&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex text-xs bg-white bg-opacity-50 rounded\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedTab('content'),className:`px-2 py-1 rounded-l ${selectedTab==='content'?'bg-white text-gray-800':'text-gray-600'}`,children:\"Table\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedTab('metadata'),className:`px-2 py-1 rounded-r ${selectedTab==='metadata'?'bg-white text-gray-800':'text-gray-600'}`,children:\"Info\"})]})]}),selectedTab==='content'&&/*#__PURE__*/_jsxs(\"div\",{children:[source.content_type==='table'&&renderTableContent(),source.content_type==='image'&&renderImageContent(),source.content_type==='chart_diagram'&&renderChartContent()]}),selectedTab==='metadata'&&source.visual_content&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-white bg-opacity-50 rounded p-3 text-xs text-gray-600\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-2\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"Extraction Method:\"}),\" \",source.visual_content.extraction_method]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"Page:\"}),\" \",source.page]}),source.visual_content.table_index!==undefined&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"Table Index:\"}),\" \",source.visual_content.table_index]}),source.visual_content.image_index!==undefined&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"Image Index:\"}),\" \",source.visual_content.image_index]})]})})]});};// Helper function to convert markdown table to HTML\nconst markdownTableToHtml=markdown=>{if(!markdown||typeof markdown!=='string'){return`<div class=\"p-4 text-gray-600\">Invalid table data</div>`;}const lines=markdown.split('\\n').filter(line=>line.trim());if(lines.length<2)return`<div class=\"p-4 text-gray-600\">Invalid table format</div>`;let html='<table class=\"min-w-full border border-gray-200 rounded-lg\">';try{// Process header\nconst headerLine=lines[0];const headerCells=headerLine.split('|').map(cell=>cell.trim()).filter(cell=>cell!=='');// Remove empty cells from start/end\nif(headerCells.length===0){// Try alternate format where cells are separated by multiple spaces\nconst spaceSeparatedCells=headerLine.split(/\\s{2,}/).filter(cell=>cell.trim());if(spaceSeparatedCells.length>0){html+='<thead class=\"bg-gray-50\"><tr>';spaceSeparatedCells.forEach((cell,index)=>{const cleanCell=cell.replace(/\\*\\*/g,'').trim();html+=`<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell||`Column ${index+1}`}</th>`;});html+='</tr></thead>';// Process data rows (skip separator row)\nhtml+='<tbody>';for(let i=2;i<lines.length;i++){const rowCells=lines[i].split(/\\s{2,}/).filter(cell=>cell.trim());if(rowCells.length===0)continue;html+='<tr class=\"hover:bg-gray-50 transition-colors\">';// Make sure we have the right number of cells\nwhile(rowCells.length<spaceSeparatedCells.length){rowCells.push('');}spaceSeparatedCells.forEach((_,index)=>{const cellContent=rowCells[index]||'';html+=`<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cellContent.trim()||'-'}</td>`;});html+='</tr>';}html+='</tbody></table>';return html;}return`<div class=\"p-4 text-gray-600\">No table headers found</div>`;}html+='<thead class=\"bg-gray-50\"><tr>';headerCells.forEach((cell,index)=>{const cleanCell=cell.replace(/\\*\\*/g,'').trim();// Remove markdown bold\nhtml+=`<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell||`Column ${index+1}`}</th>`;});html+='</tr></thead>';// Check if we have a separator row at index 1 (standard markdown table)\nconst hasSeparator=lines.length>1&&lines[1].includes('|')&&lines[1].includes('-');const startRowIndex=hasSeparator?2:1;// Process data rows\nhtml+='<tbody>';for(let i=startRowIndex;i<lines.length;i++){const rowLine=lines[i];if(!rowLine.includes('|'))continue;// Skip non-table lines\nconst cells=rowLine.split('|').map(cell=>cell.trim()).filter((cell,index,array)=>{// Keep all cells except first and last if they're empty (markdown format)\nif(index===0||index===array.length-1){return cell!=='';}return true;});// Ensure we have the right number of cells\nwhile(cells.length<headerCells.length){cells.push('');}html+='<tr class=\"hover:bg-gray-50 transition-colors\">';headerCells.forEach((_,cellIndex)=>{const cellContent=cells[cellIndex]||'';const cleanCell=cellContent.replace(/\\*\\*/g,'').trim();// Remove markdown bold\nhtml+=`<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cleanCell||'-'}</td>`;});html+='</tr>';}html+='</tbody></table>';}catch(error){console.error('Error parsing markdown table:',error);const errorMessage=error instanceof Error?error.message:'Unknown error';return`<div class=\"p-4 text-gray-600 bg-red-50 border border-red-200 rounded\">Error parsing table: ${errorMessage}</div>`;}return html;};export default VisualContent;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "VisualContent", "_ref", "_source$visual_conten", "source", "selectedTab", "setSelectedTab", "imageLoading", "setImageLoading", "imageError", "setImageError", "imageTries", "setImageTries", "cacheBuster", "setCacheBuster", "Date", "now", "visibleDebug", "setVisibleDebug", "Math", "random", "toString", "substring", "console", "log", "content_type", "filename", "page", "has_visual_content", "visual_content", "storage_url", "renderTableContent", "visualContent", "table_data", "Array", "isArray", "tableData", "length", "headers", "rows", "slice", "className", "children", "map", "header", "idx", "style", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "String", "trim", "row", "rowIdx", "_", "cellIdx", "title", "markdown_table", "dangerouslySetInnerHTML", "__html", "markdownTableToHtml", "text_table", "Object", "keys", "join", "getImageSources", "sources", "hasQueryParams", "includes", "cacheParam", "push", "url", "type", "base64_data", "undefined", "safeName", "replace", "toLowerCase", "renderImageContent", "debugInfo", "toISOString", "src", "alt", "onLoad", "onError", "e", "error", "onClick", "_source$storage_url", "maxHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description", "drawing_count", "confidence", "has_lines", "has_curves", "getContentTypeLabel", "getContentTypeColor", "metadata", "extraction_method", "table_index", "image_index", "markdown", "lines", "split", "filter", "line", "html", "headerLine", "headerCells", "cell", "spaceSeparatedCells", "for<PERSON>ach", "index", "cleanCell", "i", "row<PERSON>ells", "cellContent", "hasSeparator", "startRowIndex", "rowLine", "cells", "array", "cellIndex", "errorMessage", "Error", "message"], "sources": ["C:/IR App/frontend/src/components/ui/VisualContent.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\ninterface VisualContentProps {\n  source: {\n    content_type?: string;\n    visual_content?: Record<string, any>;\n    storage_url?: string;\n    display_type?: string;\n    filename?: string;\n    page?: number;\n  };\n}\n\nconst VisualContent: React.FC<VisualContentProps> = ({ source }) => {\n  const [selectedTab, setSelectedTab] = useState<string>('content');\n  const [imageLoading, setImageLoading] = useState<boolean>(true);\n  const [imageError, setImageError] = useState<boolean>(false);\n  const [imageTries, setImageTries] = useState<number>(0);\n  const [cacheBuster, setCacheBuster] = useState<string>(`cb-${Date.now()}`);\n  const [visibleDebug, setVisibleDebug] = useState<boolean>(false);\n  \n  // Refresh image on source change\n  useEffect(() => {\n    // Reset state when source changes\n    setImageLoading(true);\n    setImageError(false);\n    setImageTries(0);\n    setCacheBuster(`cb-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`);\n    \n    console.log('🔄 Visual content source changed:', {\n      content_type: source.content_type,\n      filename: source.filename,\n      page: source.page,\n      has_visual_content: !!source.visual_content,\n      storage_url: source.storage_url ? `${source.storage_url.substring(0, 30)}...` : 'None',\n    });\n  }, [source.filename, source.page, source.storage_url]);\n\n  if (!source.content_type || source.content_type === 'text') {\n    return null; // No visual content to display\n  }\n\n  const renderTableContent = () => {\n    const visualContent = source.visual_content;\n    \n    if (!visualContent) return null;\n\n    // If we have table data, render it as a proper table\n    if (visualContent.table_data && Array.isArray(visualContent.table_data)) {\n      const tableData = visualContent.table_data;\n      if (tableData.length === 0) return null;\n\n      const headers = tableData[0] || [];\n      const rows = tableData.slice(1);\n\n      return (\n        <div className=\"overflow-x-auto w-full\">\n          <table className=\"min-w-full bg-white border border-gray-200 rounded-lg table-fixed\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                {headers.map((header: any, idx: number) => (\n                  <th \n                    key={idx}\n                    className=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\"\n                    style={{ minWidth: '100px', maxWidth: '200px' }}\n                  >\n                    {String(header || '').trim() || `Column ${idx + 1}`}\n                  </th>\n                ))}\n              </tr>\n            </thead>\n            <tbody>\n              {rows.map((row: any[], rowIdx: number) => (\n                <tr key={rowIdx} className=\"hover:bg-gray-50 transition-colors\">\n                  {headers.map((_: any, cellIdx: number) => (\n                    <td \n                      key={cellIdx}\n                      className=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100 truncate\"\n                      title={String(row[cellIdx] || '')}\n                    >\n                      {String(row[cellIdx] || '').trim() || '-'}\n                    </td>\n                  ))}\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      );\n    }\n    \n    // If we have markdown table, render it as HTML\n    if (visualContent.markdown_table) {\n      return (\n        <div className=\"overflow-x-auto w-full\">\n          <div className=\"bg-white rounded-lg border border-gray-200\">\n            <div \n              className=\"prose prose-sm max-w-none p-0\"\n              dangerouslySetInnerHTML={{\n                __html: markdownTableToHtml(visualContent.markdown_table)\n              }}\n            />\n          </div>\n        </div>\n      );\n    }\n\n    // Fallback: Try to display as text table if available\n    if (visualContent.text_table) {\n      return (\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n          <pre className=\"text-sm text-gray-700 whitespace-pre-wrap font-mono\">\n            {visualContent.text_table}\n          </pre>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"bg-gray-50 rounded-lg p-4 text-sm text-gray-600\">\n        Table data is not available in a displayable format.\n        {visualContent && (\n          <div className=\"mt-2 text-xs\">\n            Available data: {Object.keys(visualContent).join(', ')}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  const getImageSources = () => {\n    const visualContent = source.visual_content;\n    const sources = [];\n    \n    // 1. Try storage URL if available (with cache buster)\n    if (source.storage_url) {\n      const hasQueryParams = source.storage_url.includes('?');\n      const cacheParam = hasQueryParams ? `&_cb=${cacheBuster}` : `?_cb=${cacheBuster}`;\n      sources.push({\n        url: `${source.storage_url}${cacheParam}`,\n        type: 'Storage URL'\n      });\n    }\n    \n    // 2. Try base64 data if available\n    if (visualContent?.base64_data) {\n      sources.push({\n        url: `data:image/png;base64,${visualContent.base64_data}`,\n        type: 'Base64 Data'\n      });\n    }\n    \n    // 3. Generate path based on filename and page\n    if (source.filename && source.page !== undefined) {\n      const safeName = source.filename.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();\n      sources.push({\n        url: `/images/${safeName}_page${source.page}.png?_cb=${cacheBuster}`,\n        type: 'Generated Path'\n      });\n    }\n    \n    // 4. Logo as absolute fallback\n    sources.push({\n      url: `/logo512.png?_cb=${cacheBuster}`,\n      type: 'Default Logo'\n    });\n    \n    return sources;\n  };\n\n  const renderImageContent = () => {\n    const visualContent = source.visual_content;\n    \n    console.log('DEBUG: Rendering image for', source.filename, 'page', source.page);\n    console.log('DEBUG: storage_url =', source.storage_url);\n    console.log('DEBUG: base64_data available =', !!visualContent?.base64_data);\n    \n    // Show debug info with image\n    const debugInfo = (\n      <div className=\"bg-blue-50 p-2 mb-2 rounded text-xs\">\n        <p><strong>File:</strong> {source.filename || 'Unknown'}</p>\n        <p><strong>Page:</strong> {source.page || 'Unknown'}</p>\n        <p><strong>URL:</strong> {source.storage_url ? 'Available' : 'Not available'}</p>\n        <p><strong>Time:</strong> {new Date().toISOString()}</p>\n      </div>\n    );\n\n    // Try to find actual image matching Project 1, Project 2, etc. in query response\n    if (source.content_type === 'image' && source.filename && source.filename.includes('Project')) {\n      return (\n        <div className=\"relative\">\n          {debugInfo}\n          {imageLoading && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            </div>\n          )}\n          <img\n            src={source.storage_url || `/logo512.png?t=${Date.now()}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className=\"max-w-full h-auto rounded-lg border border-gray-200 shadow-md\"\n            onLoad={() => {\n              console.log('DEBUG: Image loaded successfully');\n              setImageLoading(false);\n            }}\n            onError={(e) => {\n              console.error('DEBUG: Image failed to load:', e);\n              setImageLoading(false);\n              setImageError(true);\n            }}\n          />\n          {imageError && (\n            <div className=\"bg-red-50 rounded-lg p-4 text-center text-gray-600 mt-2\">\n              <p>Image could not be loaded</p>\n              <p className=\"text-xs mt-1\">Please check the document and try again</p>\n              <button \n                onClick={() => {\n                  console.log('DEBUG: Retrying image load');\n                  setImageLoading(true);\n                  setImageError(false);\n                }}\n                className=\"mt-2 px-2 py-1 bg-blue-100 rounded text-sm\"\n              >\n                Retry\n              </button>\n            </div>\n          )}\n        </div>\n      );\n    }\n    \n    // Try storage URL first\n    if (source.storage_url) {\n      return (\n        <div className=\"relative\">\n          {debugInfo}\n          {imageLoading && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            </div>\n          )}\n          <img\n            src={`${source.storage_url}?t=${Date.now()}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className={`max-w-full h-auto rounded-lg border border-gray-200 ${imageLoading ? 'opacity-0' : 'opacity-100'}`}\n            onLoad={() => setImageLoading(false)}\n            onError={() => {\n              console.error('DEBUG: Storage URL image failed to load:', source.storage_url);\n              setImageLoading(false);\n              setImageError(true);\n            }}\n          />\n          {imageError && (\n            <div className=\"bg-gray-100 rounded-lg p-4 text-center text-gray-600\">\n              <p>Image could not be loaded</p>\n              <p className=\"text-xs mt-1\">URL: {source.storage_url?.substring(0, 30)}...</p>\n              <button \n                onClick={() => {\n                  console.log('DEBUG: Retrying image load');\n                  setImageLoading(true);\n                  setImageError(false);\n                }}\n                className=\"mt-2 px-2 py-1 bg-blue-100 rounded text-sm\"\n              >\n                Retry\n              </button>\n            </div>\n          )}\n        </div>\n      );\n    }\n\n    // Try base64 data if available\n    if (visualContent?.base64_data) {\n      return (\n        <div>\n          {debugInfo}\n          <img\n            src={`data:image/png;base64,${visualContent.base64_data}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className=\"max-w-full h-auto rounded-lg border border-gray-200\"\n            onError={() => {\n              console.error('DEBUG: Base64 image failed to load');\n              setImageError(true);\n            }}\n          />\n        </div>\n      );\n    }\n\n    // Default to logo if no other image is available\n    return (\n      <div className=\"bg-gray-50 rounded-lg p-4 text-center\">\n        {debugInfo}\n        <p className=\"text-sm text-gray-600 mb-2\">No image content available from the document</p>\n        <img \n          src={`/logo512.png?t=${Date.now()}`}\n          alt=\"Default logo\"\n          className=\"max-w-full h-auto rounded-lg border border-gray-200 mx-auto\"\n          style={{ maxHeight: '200px' }}\n        />\n      </div>\n    );\n  };\n\n  const renderChartContent = () => {\n    const visualContent = source.visual_content;\n    \n    if (!visualContent) return null;\n\n    return (\n      <div className=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200\">\n        <h4 className=\"font-semibold text-purple-800 mb-3 flex items-center\">\n          📊 Chart/Diagram Detected\n        </h4>\n        <div className=\"space-y-3\">\n          {visualContent.description && (\n            <p className=\"text-sm text-gray-700\">{visualContent.description}</p>\n          )}\n          <div className=\"grid grid-cols-2 gap-4 text-xs text-gray-600\">\n            {visualContent.drawing_count && (\n              <div>\n                <span className=\"font-medium\">Drawing Elements:</span> {visualContent.drawing_count}\n              </div>\n            )}\n            {visualContent.confidence && (\n              <div>\n                <span className=\"font-medium\">Confidence:</span> {visualContent.confidence}\n              </div>\n            )}\n            {visualContent.has_lines && (\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2\"></span>\n                Contains lines\n              </div>\n            )}\n            {visualContent.has_curves && (\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-blue-400 rounded-full mr-2\"></span>\n                Contains curves\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const getContentTypeLabel = () => {\n    switch (source.content_type) {\n      case 'table': return '📋 Table';\n      case 'image': return '🖼️ Image';\n      case 'chart_diagram': return '📊 Chart/Diagram';\n      default: return '📄 Content';\n    }\n  };\n\n  const getContentTypeColor = () => {\n    switch (source.content_type) {\n      case 'table': return 'from-green-50 to-emerald-50 border-green-200';\n      case 'image': return 'from-blue-50 to-cyan-50 border-blue-200';\n      case 'chart_diagram': return 'from-purple-50 to-indigo-50 border-purple-200';\n      default: return 'from-gray-50 to-slate-50 border-gray-200';\n    }\n  };\n\n  return (\n    <div className={`bg-gradient-to-br ${getContentTypeColor()} rounded-lg border p-4 mt-3`}>\n      <div className=\"flex items-center justify-between mb-3\">\n        <h4 className=\"font-semibold text-gray-800 text-sm flex items-center\">\n          {getContentTypeLabel()}\n          {source.page && (\n            <span className=\"ml-2 text-xs bg-white bg-opacity-70 px-2 py-1 rounded\">\n              Page {source.page}\n            </span>\n          )}\n        </h4>\n        \n        {/* Tab selector for complex content */}\n        {source.content_type === 'table' && source.visual_content?.metadata && (\n          <div className=\"flex text-xs bg-white bg-opacity-50 rounded\">\n            <button\n              onClick={() => setSelectedTab('content')}\n              className={`px-2 py-1 rounded-l ${\n                selectedTab === 'content' ? 'bg-white text-gray-800' : 'text-gray-600'\n              }`}\n            >\n              Table\n            </button>\n            <button\n              onClick={() => setSelectedTab('metadata')}\n              className={`px-2 py-1 rounded-r ${\n                selectedTab === 'metadata' ? 'bg-white text-gray-800' : 'text-gray-600'\n              }`}\n            >\n              Info\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Content display */}\n      {selectedTab === 'content' && (\n        <div>\n          {source.content_type === 'table' && renderTableContent()}\n          {source.content_type === 'image' && renderImageContent()}\n          {source.content_type === 'chart_diagram' && renderChartContent()}\n        </div>\n      )}\n\n      {/* Metadata tab */}\n      {selectedTab === 'metadata' && source.visual_content && (\n        <div className=\"bg-white bg-opacity-50 rounded p-3 text-xs text-gray-600\">\n          <div className=\"grid grid-cols-2 gap-2\">\n            <div><span className=\"font-medium\">Extraction Method:</span> {source.visual_content.extraction_method}</div>\n            <div><span className=\"font-medium\">Page:</span> {source.page}</div>\n            {source.visual_content.table_index !== undefined && (\n              <div><span className=\"font-medium\">Table Index:</span> {source.visual_content.table_index}</div>\n            )}\n            {source.visual_content.image_index !== undefined && (\n              <div><span className=\"font-medium\">Image Index:</span> {source.visual_content.image_index}</div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Helper function to convert markdown table to HTML\nconst markdownTableToHtml = (markdown: string): string => {\n  if (!markdown || typeof markdown !== 'string') {\n    return `<div class=\"p-4 text-gray-600\">Invalid table data</div>`;\n  }\n  \n  const lines = markdown.split('\\n').filter(line => line.trim());\n  if (lines.length < 2) return `<div class=\"p-4 text-gray-600\">Invalid table format</div>`;\n\n  let html = '<table class=\"min-w-full border border-gray-200 rounded-lg\">';\n  \n  try {\n    // Process header\n    const headerLine = lines[0];\n    const headerCells = headerLine.split('|')\n      .map(cell => cell.trim())\n      .filter(cell => cell !== ''); // Remove empty cells from start/end\n    \n    if (headerCells.length === 0) {\n      // Try alternate format where cells are separated by multiple spaces\n      const spaceSeparatedCells = headerLine.split(/\\s{2,}/).filter(cell => cell.trim());\n      if (spaceSeparatedCells.length > 0) {\n        html += '<thead class=\"bg-gray-50\"><tr>';\n        spaceSeparatedCells.forEach((cell, index) => {\n          const cleanCell = cell.replace(/\\*\\*/g, '').trim();\n          html += `<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell || `Column ${index + 1}`}</th>`;\n        });\n        html += '</tr></thead>';\n        \n        // Process data rows (skip separator row)\n        html += '<tbody>';\n        for (let i = 2; i < lines.length; i++) {\n          const rowCells = lines[i].split(/\\s{2,}/).filter(cell => cell.trim());\n          if (rowCells.length === 0) continue;\n          \n          html += '<tr class=\"hover:bg-gray-50 transition-colors\">';\n          // Make sure we have the right number of cells\n          while (rowCells.length < spaceSeparatedCells.length) {\n            rowCells.push('');\n          }\n          \n          spaceSeparatedCells.forEach((_, index) => {\n            const cellContent = rowCells[index] || '';\n            html += `<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cellContent.trim() || '-'}</td>`;\n          });\n          html += '</tr>';\n        }\n        html += '</tbody></table>';\n        return html;\n      }\n      \n      return `<div class=\"p-4 text-gray-600\">No table headers found</div>`;\n    }\n    \n    html += '<thead class=\"bg-gray-50\"><tr>';\n    headerCells.forEach((cell, index) => {\n      const cleanCell = cell.replace(/\\*\\*/g, '').trim(); // Remove markdown bold\n      html += `<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell || `Column ${index + 1}`}</th>`;\n    });\n    html += '</tr></thead>';\n\n    // Check if we have a separator row at index 1 (standard markdown table)\n    const hasSeparator = lines.length > 1 && lines[1].includes('|') && lines[1].includes('-');\n    const startRowIndex = hasSeparator ? 2 : 1;\n\n    // Process data rows\n    html += '<tbody>';\n    for (let i = startRowIndex; i < lines.length; i++) {\n      const rowLine = lines[i];\n      if (!rowLine.includes('|')) continue; // Skip non-table lines\n      \n      const cells = rowLine.split('|')\n        .map(cell => cell.trim())\n        .filter((cell, index, array) => {\n          // Keep all cells except first and last if they're empty (markdown format)\n          if (index === 0 || index === array.length - 1) {\n            return cell !== '';\n          }\n          return true;\n        });\n      \n      // Ensure we have the right number of cells\n      while (cells.length < headerCells.length) {\n        cells.push('');\n      }\n      \n      html += '<tr class=\"hover:bg-gray-50 transition-colors\">';\n      headerCells.forEach((_, cellIndex) => {\n        const cellContent = cells[cellIndex] || '';\n        const cleanCell = cellContent.replace(/\\*\\*/g, '').trim(); // Remove markdown bold\n        html += `<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cleanCell || '-'}</td>`;\n      });\n      html += '</tr>';\n    }\n    html += '</tbody></table>';\n  } catch (error) {\n    console.error('Error parsing markdown table:', error);\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n    return `<div class=\"p-4 text-gray-600 bg-red-50 border border-red-200 rounded\">Error parsing table: ${errorMessage}</div>`;\n  }\n\n  return html;\n};\n\nexport default VisualContent; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAanD,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAAgB,KAAAC,qBAAA,IAAf,CAAEC,MAAO,CAAC,CAAAF,IAAA,CAC7D,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAGX,QAAQ,CAAS,SAAS,CAAC,CACjE,KAAM,CAACY,YAAY,CAAEC,eAAe,CAAC,CAAGb,QAAQ,CAAU,IAAI,CAAC,CAC/D,KAAM,CAACc,UAAU,CAAEC,aAAa,CAAC,CAAGf,QAAQ,CAAU,KAAK,CAAC,CAC5D,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAS,CAAC,CAAC,CACvD,KAAM,CAACkB,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAS,MAAMoB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAC1E,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGvB,QAAQ,CAAU,KAAK,CAAC,CAEhE;AACAC,SAAS,CAAC,IAAM,CACd;AACAY,eAAe,CAAC,IAAI,CAAC,CACrBE,aAAa,CAAC,KAAK,CAAC,CACpBE,aAAa,CAAC,CAAC,CAAC,CAChBE,cAAc,CAAC,MAAMC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIG,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,EAAE,CAAC,CAEhFC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAE,CAC/CC,YAAY,CAAErB,MAAM,CAACqB,YAAY,CACjCC,QAAQ,CAAEtB,MAAM,CAACsB,QAAQ,CACzBC,IAAI,CAAEvB,MAAM,CAACuB,IAAI,CACjBC,kBAAkB,CAAE,CAAC,CAACxB,MAAM,CAACyB,cAAc,CAC3CC,WAAW,CAAE1B,MAAM,CAAC0B,WAAW,CAAG,GAAG1B,MAAM,CAAC0B,WAAW,CAACR,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,KAAK,CAAG,MAClF,CAAC,CAAC,CACJ,CAAC,CAAE,CAAClB,MAAM,CAACsB,QAAQ,CAAEtB,MAAM,CAACuB,IAAI,CAAEvB,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAEtD,GAAI,CAAC1B,MAAM,CAACqB,YAAY,EAAIrB,MAAM,CAACqB,YAAY,GAAK,MAAM,CAAE,CAC1D,MAAO,KAAI,CAAE;AACf,CAEA,KAAM,CAAAM,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,aAAa,CAAG5B,MAAM,CAACyB,cAAc,CAE3C,GAAI,CAACG,aAAa,CAAE,MAAO,KAAI,CAE/B;AACA,GAAIA,aAAa,CAACC,UAAU,EAAIC,KAAK,CAACC,OAAO,CAACH,aAAa,CAACC,UAAU,CAAC,CAAE,CACvE,KAAM,CAAAG,SAAS,CAAGJ,aAAa,CAACC,UAAU,CAC1C,GAAIG,SAAS,CAACC,MAAM,GAAK,CAAC,CAAE,MAAO,KAAI,CAEvC,KAAM,CAAAC,OAAO,CAAGF,SAAS,CAAC,CAAC,CAAC,EAAI,EAAE,CAClC,KAAM,CAAAG,IAAI,CAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC,CAE/B,mBACE1C,IAAA,QAAK2C,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC1C,KAAA,UAAOyC,SAAS,CAAC,mEAAmE,CAAAC,QAAA,eAClF5C,IAAA,UAAO2C,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B5C,IAAA,OAAA4C,QAAA,CACGJ,OAAO,CAACK,GAAG,CAAC,CAACC,MAAW,CAAEC,GAAW,gBACpC/C,IAAA,OAEE2C,SAAS,CAAC,kFAAkF,CAC5FK,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAO,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAN,QAAA,CAE/CO,MAAM,CAACL,MAAM,EAAI,EAAE,CAAC,CAACM,IAAI,CAAC,CAAC,EAAI,UAAUL,GAAG,CAAG,CAAC,EAAE,EAJ9CA,GAKH,CACL,CAAC,CACA,CAAC,CACA,CAAC,cACR/C,IAAA,UAAA4C,QAAA,CACGH,IAAI,CAACI,GAAG,CAAC,CAACQ,GAAU,CAAEC,MAAc,gBACnCtD,IAAA,OAAiB2C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAC5DJ,OAAO,CAACK,GAAG,CAAC,CAACU,CAAM,CAAEC,OAAe,gBACnCxD,IAAA,OAEE2C,SAAS,CAAC,mEAAmE,CAC7Ec,KAAK,CAAEN,MAAM,CAACE,GAAG,CAACG,OAAO,CAAC,EAAI,EAAE,CAAE,CAAAZ,QAAA,CAEjCO,MAAM,CAACE,GAAG,CAACG,OAAO,CAAC,EAAI,EAAE,CAAC,CAACJ,IAAI,CAAC,CAAC,EAAI,GAAG,EAJpCI,OAKH,CACL,CAAC,EATKF,MAUL,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAEV,CAEA;AACA,GAAIpB,aAAa,CAACwB,cAAc,CAAE,CAChC,mBACE1D,IAAA,QAAK2C,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC5C,IAAA,QAAK2C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,cACzD5C,IAAA,QACE2C,SAAS,CAAC,+BAA+B,CACzCgB,uBAAuB,CAAE,CACvBC,MAAM,CAAEC,mBAAmB,CAAC3B,aAAa,CAACwB,cAAc,CAC1D,CAAE,CACH,CAAC,CACC,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAIxB,aAAa,CAAC4B,UAAU,CAAE,CAC5B,mBACE9D,IAAA,QAAK2C,SAAS,CAAC,gDAAgD,CAAAC,QAAA,cAC7D5C,IAAA,QAAK2C,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CACjEV,aAAa,CAAC4B,UAAU,CACtB,CAAC,CACH,CAAC,CAEV,CAEA,mBACE5D,KAAA,QAAKyC,SAAS,CAAC,iDAAiD,CAAAC,QAAA,EAAC,sDAE/D,CAACV,aAAa,eACZhC,KAAA,QAAKyC,SAAS,CAAC,cAAc,CAAAC,QAAA,EAAC,kBACZ,CAACmB,MAAM,CAACC,IAAI,CAAC9B,aAAa,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAAC,EACnD,CACN,EACE,CAAC,CAEV,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAhC,aAAa,CAAG5B,MAAM,CAACyB,cAAc,CAC3C,KAAM,CAAAoC,OAAO,CAAG,EAAE,CAElB;AACA,GAAI7D,MAAM,CAAC0B,WAAW,CAAE,CACtB,KAAM,CAAAoC,cAAc,CAAG9D,MAAM,CAAC0B,WAAW,CAACqC,QAAQ,CAAC,GAAG,CAAC,CACvD,KAAM,CAAAC,UAAU,CAAGF,cAAc,CAAG,QAAQrD,WAAW,EAAE,CAAG,QAAQA,WAAW,EAAE,CACjFoD,OAAO,CAACI,IAAI,CAAC,CACXC,GAAG,CAAE,GAAGlE,MAAM,CAAC0B,WAAW,GAAGsC,UAAU,EAAE,CACzCG,IAAI,CAAE,aACR,CAAC,CAAC,CACJ,CAEA;AACA,GAAIvC,aAAa,SAAbA,aAAa,WAAbA,aAAa,CAAEwC,WAAW,CAAE,CAC9BP,OAAO,CAACI,IAAI,CAAC,CACXC,GAAG,CAAE,yBAAyBtC,aAAa,CAACwC,WAAW,EAAE,CACzDD,IAAI,CAAE,aACR,CAAC,CAAC,CACJ,CAEA;AACA,GAAInE,MAAM,CAACsB,QAAQ,EAAItB,MAAM,CAACuB,IAAI,GAAK8C,SAAS,CAAE,CAChD,KAAM,CAAAC,QAAQ,CAAGtE,MAAM,CAACsB,QAAQ,CAACiD,OAAO,CAAC,eAAe,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CAC5EX,OAAO,CAACI,IAAI,CAAC,CACXC,GAAG,CAAE,WAAWI,QAAQ,QAAQtE,MAAM,CAACuB,IAAI,YAAYd,WAAW,EAAE,CACpE0D,IAAI,CAAE,gBACR,CAAC,CAAC,CACJ,CAEA;AACAN,OAAO,CAACI,IAAI,CAAC,CACXC,GAAG,CAAE,oBAAoBzD,WAAW,EAAE,CACtC0D,IAAI,CAAE,cACR,CAAC,CAAC,CAEF,MAAO,CAAAN,OAAO,CAChB,CAAC,CAED,KAAM,CAAAY,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAA7C,aAAa,CAAG5B,MAAM,CAACyB,cAAc,CAE3CN,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEpB,MAAM,CAACsB,QAAQ,CAAE,MAAM,CAAEtB,MAAM,CAACuB,IAAI,CAAC,CAC/EJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAEpB,MAAM,CAAC0B,WAAW,CAAC,CACvDP,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAE,CAAC,EAACQ,aAAa,SAAbA,aAAa,WAAbA,aAAa,CAAEwC,WAAW,EAAC,CAE3E;AACA,KAAM,CAAAM,SAAS,cACb9E,KAAA,QAAKyC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClD1C,KAAA,MAAA0C,QAAA,eAAG5C,IAAA,WAAA4C,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACtC,MAAM,CAACsB,QAAQ,EAAI,SAAS,EAAI,CAAC,cAC5D1B,KAAA,MAAA0C,QAAA,eAAG5C,IAAA,WAAA4C,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACtC,MAAM,CAACuB,IAAI,EAAI,SAAS,EAAI,CAAC,cACxD3B,KAAA,MAAA0C,QAAA,eAAG5C,IAAA,WAAA4C,QAAA,CAAQ,MAAI,CAAQ,CAAC,IAAC,CAACtC,MAAM,CAAC0B,WAAW,CAAG,WAAW,CAAG,eAAe,EAAI,CAAC,cACjF9B,KAAA,MAAA0C,QAAA,eAAG5C,IAAA,WAAA4C,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAAC,GAAI,CAAA3B,IAAI,CAAC,CAAC,CAACgE,WAAW,CAAC,CAAC,EAAI,CAAC,EACrD,CACN,CAED;AACA,GAAI3E,MAAM,CAACqB,YAAY,GAAK,OAAO,EAAIrB,MAAM,CAACsB,QAAQ,EAAItB,MAAM,CAACsB,QAAQ,CAACyC,QAAQ,CAAC,SAAS,CAAC,CAAE,CAC7F,mBACEnE,KAAA,QAAKyC,SAAS,CAAC,UAAU,CAAAC,QAAA,EACtBoC,SAAS,CACTvE,YAAY,eACXT,IAAA,QAAK2C,SAAS,CAAC,0EAA0E,CAAAC,QAAA,cACvF5C,IAAA,QAAK2C,SAAS,CAAC,8DAA8D,CAAM,CAAC,CACjF,CACN,cACD3C,IAAA,QACEkF,GAAG,CAAE5E,MAAM,CAAC0B,WAAW,EAAI,kBAAkBf,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG,CAC1DiE,GAAG,CAAE,mBAAmB7E,MAAM,CAACuB,IAAI,OAAOvB,MAAM,CAACsB,QAAQ,EAAG,CAC5De,SAAS,CAAC,+DAA+D,CACzEyC,MAAM,CAAEA,CAAA,GAAM,CACZ3D,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/ChB,eAAe,CAAC,KAAK,CAAC,CACxB,CAAE,CACF2E,OAAO,CAAGC,CAAC,EAAK,CACd7D,OAAO,CAAC8D,KAAK,CAAC,8BAA8B,CAAED,CAAC,CAAC,CAChD5E,eAAe,CAAC,KAAK,CAAC,CACtBE,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACH,CAAC,CACDD,UAAU,eACTT,KAAA,QAAKyC,SAAS,CAAC,yDAAyD,CAAAC,QAAA,eACtE5C,IAAA,MAAA4C,QAAA,CAAG,2BAAyB,CAAG,CAAC,cAChC5C,IAAA,MAAG2C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,yCAAuC,CAAG,CAAC,cACvE5C,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAM,CACb/D,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CACzChB,eAAe,CAAC,IAAI,CAAC,CACrBE,aAAa,CAAC,KAAK,CAAC,CACtB,CAAE,CACF+B,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACvD,OAED,CAAQ,CAAC,EACN,CACN,EACE,CAAC,CAEV,CAEA;AACA,GAAItC,MAAM,CAAC0B,WAAW,CAAE,KAAAyD,mBAAA,CACtB,mBACEvF,KAAA,QAAKyC,SAAS,CAAC,UAAU,CAAAC,QAAA,EACtBoC,SAAS,CACTvE,YAAY,eACXT,IAAA,QAAK2C,SAAS,CAAC,0EAA0E,CAAAC,QAAA,cACvF5C,IAAA,QAAK2C,SAAS,CAAC,8DAA8D,CAAM,CAAC,CACjF,CACN,cACD3C,IAAA,QACEkF,GAAG,CAAE,GAAG5E,MAAM,CAAC0B,WAAW,MAAMf,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG,CAC7CiE,GAAG,CAAE,mBAAmB7E,MAAM,CAACuB,IAAI,OAAOvB,MAAM,CAACsB,QAAQ,EAAG,CAC5De,SAAS,CAAE,uDAAuDlC,YAAY,CAAG,WAAW,CAAG,aAAa,EAAG,CAC/G2E,MAAM,CAAEA,CAAA,GAAM1E,eAAe,CAAC,KAAK,CAAE,CACrC2E,OAAO,CAAEA,CAAA,GAAM,CACb5D,OAAO,CAAC8D,KAAK,CAAC,0CAA0C,CAAEjF,MAAM,CAAC0B,WAAW,CAAC,CAC7EtB,eAAe,CAAC,KAAK,CAAC,CACtBE,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACH,CAAC,CACDD,UAAU,eACTT,KAAA,QAAKyC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE5C,IAAA,MAAA4C,QAAA,CAAG,2BAAyB,CAAG,CAAC,cAChC1C,KAAA,MAAGyC,SAAS,CAAC,cAAc,CAAAC,QAAA,EAAC,OAAK,EAAA6C,mBAAA,CAACnF,MAAM,CAAC0B,WAAW,UAAAyD,mBAAA,iBAAlBA,mBAAA,CAAoBjE,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC,KAAG,EAAG,CAAC,cAC9ExB,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAM,CACb/D,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CACzChB,eAAe,CAAC,IAAI,CAAC,CACrBE,aAAa,CAAC,KAAK,CAAC,CACtB,CAAE,CACF+B,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACvD,OAED,CAAQ,CAAC,EACN,CACN,EACE,CAAC,CAEV,CAEA;AACA,GAAIV,aAAa,SAAbA,aAAa,WAAbA,aAAa,CAAEwC,WAAW,CAAE,CAC9B,mBACExE,KAAA,QAAA0C,QAAA,EACGoC,SAAS,cACVhF,IAAA,QACEkF,GAAG,CAAE,yBAAyBhD,aAAa,CAACwC,WAAW,EAAG,CAC1DS,GAAG,CAAE,mBAAmB7E,MAAM,CAACuB,IAAI,OAAOvB,MAAM,CAACsB,QAAQ,EAAG,CAC5De,SAAS,CAAC,qDAAqD,CAC/D0C,OAAO,CAAEA,CAAA,GAAM,CACb5D,OAAO,CAAC8D,KAAK,CAAC,oCAAoC,CAAC,CACnD3E,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACH,CAAC,EACC,CAAC,CAEV,CAEA;AACA,mBACEV,KAAA,QAAKyC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,EACnDoC,SAAS,cACVhF,IAAA,MAAG2C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,8CAA4C,CAAG,CAAC,cAC1F5C,IAAA,QACEkF,GAAG,CAAE,kBAAkBjE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG,CACpCiE,GAAG,CAAC,cAAc,CAClBxC,SAAS,CAAC,6DAA6D,CACvEK,KAAK,CAAE,CAAE0C,SAAS,CAAE,OAAQ,CAAE,CAC/B,CAAC,EACC,CAAC,CAEV,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAzD,aAAa,CAAG5B,MAAM,CAACyB,cAAc,CAE3C,GAAI,CAACG,aAAa,CAAE,MAAO,KAAI,CAE/B,mBACEhC,KAAA,QAAKyC,SAAS,CAAC,uFAAuF,CAAAC,QAAA,eACpG5C,IAAA,OAAI2C,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAAC,qCAErE,CAAI,CAAC,cACL1C,KAAA,QAAKyC,SAAS,CAAC,WAAW,CAAAC,QAAA,EACvBV,aAAa,CAAC0D,WAAW,eACxB5F,IAAA,MAAG2C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEV,aAAa,CAAC0D,WAAW,CAAI,CACpE,cACD1F,KAAA,QAAKyC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,EAC1DV,aAAa,CAAC2D,aAAa,eAC1B3F,KAAA,QAAA0C,QAAA,eACE5C,IAAA,SAAM2C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,mBAAiB,CAAM,CAAC,IAAC,CAACV,aAAa,CAAC2D,aAAa,EAChF,CACN,CACA3D,aAAa,CAAC4D,UAAU,eACvB5F,KAAA,QAAA0C,QAAA,eACE5C,IAAA,SAAM2C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,IAAC,CAACV,aAAa,CAAC4D,UAAU,EACvE,CACN,CACA5D,aAAa,CAAC6D,SAAS,eACtB7F,KAAA,QAAKyC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5C,IAAA,SAAM2C,SAAS,CAAC,wCAAwC,CAAO,CAAC,iBAElE,EAAK,CACN,CACAT,aAAa,CAAC8D,UAAU,eACvB9F,KAAA,QAAKyC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5C,IAAA,SAAM2C,SAAS,CAAC,uCAAuC,CAAO,CAAC,kBAEjE,EAAK,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,KAAM,CAAAsD,mBAAmB,CAAGA,CAAA,GAAM,CAChC,OAAQ3F,MAAM,CAACqB,YAAY,EACzB,IAAK,OAAO,CAAE,MAAO,UAAU,CAC/B,IAAK,OAAO,CAAE,MAAO,WAAW,CAChC,IAAK,eAAe,CAAE,MAAO,kBAAkB,CAC/C,QAAS,MAAO,YAAY,CAC9B,CACF,CAAC,CAED,KAAM,CAAAuE,mBAAmB,CAAGA,CAAA,GAAM,CAChC,OAAQ5F,MAAM,CAACqB,YAAY,EACzB,IAAK,OAAO,CAAE,MAAO,8CAA8C,CACnE,IAAK,OAAO,CAAE,MAAO,yCAAyC,CAC9D,IAAK,eAAe,CAAE,MAAO,+CAA+C,CAC5E,QAAS,MAAO,0CAA0C,CAC5D,CACF,CAAC,CAED,mBACEzB,KAAA,QAAKyC,SAAS,CAAE,qBAAqBuD,mBAAmB,CAAC,CAAC,6BAA8B,CAAAtD,QAAA,eACtF1C,KAAA,QAAKyC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD1C,KAAA,OAAIyC,SAAS,CAAC,uDAAuD,CAAAC,QAAA,EAClEqD,mBAAmB,CAAC,CAAC,CACrB3F,MAAM,CAACuB,IAAI,eACV3B,KAAA,SAAMyC,SAAS,CAAC,uDAAuD,CAAAC,QAAA,EAAC,OACjE,CAACtC,MAAM,CAACuB,IAAI,EACb,CACP,EACC,CAAC,CAGJvB,MAAM,CAACqB,YAAY,GAAK,OAAO,IAAAtB,qBAAA,CAAIC,MAAM,CAACyB,cAAc,UAAA1B,qBAAA,iBAArBA,qBAAA,CAAuB8F,QAAQ,gBACjEjG,KAAA,QAAKyC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D5C,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAMhF,cAAc,CAAC,SAAS,CAAE,CACzCmC,SAAS,CAAE,uBACTpC,WAAW,GAAK,SAAS,CAAG,wBAAwB,CAAG,eAAe,EACrE,CAAAqC,QAAA,CACJ,OAED,CAAQ,CAAC,cACT5C,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAMhF,cAAc,CAAC,UAAU,CAAE,CAC1CmC,SAAS,CAAE,uBACTpC,WAAW,GAAK,UAAU,CAAG,wBAAwB,CAAG,eAAe,EACtE,CAAAqC,QAAA,CACJ,MAED,CAAQ,CAAC,EACN,CACN,EACE,CAAC,CAGLrC,WAAW,GAAK,SAAS,eACxBL,KAAA,QAAA0C,QAAA,EACGtC,MAAM,CAACqB,YAAY,GAAK,OAAO,EAAIM,kBAAkB,CAAC,CAAC,CACvD3B,MAAM,CAACqB,YAAY,GAAK,OAAO,EAAIoD,kBAAkB,CAAC,CAAC,CACvDzE,MAAM,CAACqB,YAAY,GAAK,eAAe,EAAIgE,kBAAkB,CAAC,CAAC,EAC7D,CACN,CAGApF,WAAW,GAAK,UAAU,EAAID,MAAM,CAACyB,cAAc,eAClD/B,IAAA,QAAK2C,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cACvE1C,KAAA,QAAKyC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC1C,KAAA,QAAA0C,QAAA,eAAK5C,IAAA,SAAM2C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,oBAAkB,CAAM,CAAC,IAAC,CAACtC,MAAM,CAACyB,cAAc,CAACqE,iBAAiB,EAAM,CAAC,cAC5GlG,KAAA,QAAA0C,QAAA,eAAK5C,IAAA,SAAM2C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,IAAC,CAACtC,MAAM,CAACuB,IAAI,EAAM,CAAC,CAClEvB,MAAM,CAACyB,cAAc,CAACsE,WAAW,GAAK1B,SAAS,eAC9CzE,KAAA,QAAA0C,QAAA,eAAK5C,IAAA,SAAM2C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,IAAC,CAACtC,MAAM,CAACyB,cAAc,CAACsE,WAAW,EAAM,CAChG,CACA/F,MAAM,CAACyB,cAAc,CAACuE,WAAW,GAAK3B,SAAS,eAC9CzE,KAAA,QAAA0C,QAAA,eAAK5C,IAAA,SAAM2C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,IAAC,CAACtC,MAAM,CAACyB,cAAc,CAACuE,WAAW,EAAM,CAChG,EACE,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAzC,mBAAmB,CAAI0C,QAAgB,EAAa,CACxD,GAAI,CAACA,QAAQ,EAAI,MAAO,CAAAA,QAAQ,GAAK,QAAQ,CAAE,CAC7C,MAAO,yDAAyD,CAClE,CAEA,KAAM,CAAAC,KAAK,CAAGD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACvD,IAAI,CAAC,CAAC,CAAC,CAC9D,GAAIoD,KAAK,CAACjE,MAAM,CAAG,CAAC,CAAE,MAAO,2DAA2D,CAExF,GAAI,CAAAqE,IAAI,CAAG,8DAA8D,CAEzE,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAGL,KAAK,CAAC,CAAC,CAAC,CAC3B,KAAM,CAAAM,WAAW,CAAGD,UAAU,CAACJ,KAAK,CAAC,GAAG,CAAC,CACtC5D,GAAG,CAACkE,IAAI,EAAIA,IAAI,CAAC3D,IAAI,CAAC,CAAC,CAAC,CACxBsD,MAAM,CAACK,IAAI,EAAIA,IAAI,GAAK,EAAE,CAAC,CAAE;AAEhC,GAAID,WAAW,CAACvE,MAAM,GAAK,CAAC,CAAE,CAC5B;AACA,KAAM,CAAAyE,mBAAmB,CAAGH,UAAU,CAACJ,KAAK,CAAC,QAAQ,CAAC,CAACC,MAAM,CAACK,IAAI,EAAIA,IAAI,CAAC3D,IAAI,CAAC,CAAC,CAAC,CAClF,GAAI4D,mBAAmB,CAACzE,MAAM,CAAG,CAAC,CAAE,CAClCqE,IAAI,EAAI,gCAAgC,CACxCI,mBAAmB,CAACC,OAAO,CAAC,CAACF,IAAI,CAAEG,KAAK,GAAK,CAC3C,KAAM,CAAAC,SAAS,CAAGJ,IAAI,CAAClC,OAAO,CAAC,OAAO,CAAE,EAAE,CAAC,CAACzB,IAAI,CAAC,CAAC,CAClDwD,IAAI,EAAI,gGAAgGO,SAAS,EAAI,UAAUD,KAAK,CAAG,CAAC,EAAE,OAAO,CACnJ,CAAC,CAAC,CACFN,IAAI,EAAI,eAAe,CAEvB;AACAA,IAAI,EAAI,SAAS,CACjB,IAAK,GAAI,CAAAQ,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGZ,KAAK,CAACjE,MAAM,CAAE6E,CAAC,EAAE,CAAE,CACrC,KAAM,CAAAC,QAAQ,CAAGb,KAAK,CAACY,CAAC,CAAC,CAACX,KAAK,CAAC,QAAQ,CAAC,CAACC,MAAM,CAACK,IAAI,EAAIA,IAAI,CAAC3D,IAAI,CAAC,CAAC,CAAC,CACrE,GAAIiE,QAAQ,CAAC9E,MAAM,GAAK,CAAC,CAAE,SAE3BqE,IAAI,EAAI,iDAAiD,CACzD;AACA,MAAOS,QAAQ,CAAC9E,MAAM,CAAGyE,mBAAmB,CAACzE,MAAM,CAAE,CACnD8E,QAAQ,CAAC9C,IAAI,CAAC,EAAE,CAAC,CACnB,CAEAyC,mBAAmB,CAACC,OAAO,CAAC,CAAC1D,CAAC,CAAE2D,KAAK,GAAK,CACxC,KAAM,CAAAI,WAAW,CAAGD,QAAQ,CAACH,KAAK,CAAC,EAAI,EAAE,CACzCN,IAAI,EAAI,wEAAwEU,WAAW,CAAClE,IAAI,CAAC,CAAC,EAAI,GAAG,OAAO,CAClH,CAAC,CAAC,CACFwD,IAAI,EAAI,OAAO,CACjB,CACAA,IAAI,EAAI,kBAAkB,CAC1B,MAAO,CAAAA,IAAI,CACb,CAEA,MAAO,6DAA6D,CACtE,CAEAA,IAAI,EAAI,gCAAgC,CACxCE,WAAW,CAACG,OAAO,CAAC,CAACF,IAAI,CAAEG,KAAK,GAAK,CACnC,KAAM,CAAAC,SAAS,CAAGJ,IAAI,CAAClC,OAAO,CAAC,OAAO,CAAE,EAAE,CAAC,CAACzB,IAAI,CAAC,CAAC,CAAE;AACpDwD,IAAI,EAAI,gGAAgGO,SAAS,EAAI,UAAUD,KAAK,CAAG,CAAC,EAAE,OAAO,CACnJ,CAAC,CAAC,CACFN,IAAI,EAAI,eAAe,CAEvB;AACA,KAAM,CAAAW,YAAY,CAAGf,KAAK,CAACjE,MAAM,CAAG,CAAC,EAAIiE,KAAK,CAAC,CAAC,CAAC,CAACnC,QAAQ,CAAC,GAAG,CAAC,EAAImC,KAAK,CAAC,CAAC,CAAC,CAACnC,QAAQ,CAAC,GAAG,CAAC,CACzF,KAAM,CAAAmD,aAAa,CAAGD,YAAY,CAAG,CAAC,CAAG,CAAC,CAE1C;AACAX,IAAI,EAAI,SAAS,CACjB,IAAK,GAAI,CAAAQ,CAAC,CAAGI,aAAa,CAAEJ,CAAC,CAAGZ,KAAK,CAACjE,MAAM,CAAE6E,CAAC,EAAE,CAAE,CACjD,KAAM,CAAAK,OAAO,CAAGjB,KAAK,CAACY,CAAC,CAAC,CACxB,GAAI,CAACK,OAAO,CAACpD,QAAQ,CAAC,GAAG,CAAC,CAAE,SAAU;AAEtC,KAAM,CAAAqD,KAAK,CAAGD,OAAO,CAAChB,KAAK,CAAC,GAAG,CAAC,CAC7B5D,GAAG,CAACkE,IAAI,EAAIA,IAAI,CAAC3D,IAAI,CAAC,CAAC,CAAC,CACxBsD,MAAM,CAAC,CAACK,IAAI,CAAEG,KAAK,CAAES,KAAK,GAAK,CAC9B;AACA,GAAIT,KAAK,GAAK,CAAC,EAAIA,KAAK,GAAKS,KAAK,CAACpF,MAAM,CAAG,CAAC,CAAE,CAC7C,MAAO,CAAAwE,IAAI,GAAK,EAAE,CACpB,CACA,MAAO,KAAI,CACb,CAAC,CAAC,CAEJ;AACA,MAAOW,KAAK,CAACnF,MAAM,CAAGuE,WAAW,CAACvE,MAAM,CAAE,CACxCmF,KAAK,CAACnD,IAAI,CAAC,EAAE,CAAC,CAChB,CAEAqC,IAAI,EAAI,iDAAiD,CACzDE,WAAW,CAACG,OAAO,CAAC,CAAC1D,CAAC,CAAEqE,SAAS,GAAK,CACpC,KAAM,CAAAN,WAAW,CAAGI,KAAK,CAACE,SAAS,CAAC,EAAI,EAAE,CAC1C,KAAM,CAAAT,SAAS,CAAGG,WAAW,CAACzC,OAAO,CAAC,OAAO,CAAE,EAAE,CAAC,CAACzB,IAAI,CAAC,CAAC,CAAE;AAC3DwD,IAAI,EAAI,wEAAwEO,SAAS,EAAI,GAAG,OAAO,CACzG,CAAC,CAAC,CACFP,IAAI,EAAI,OAAO,CACjB,CACAA,IAAI,EAAI,kBAAkB,CAC5B,CAAE,MAAOrB,KAAK,CAAE,CACd9D,OAAO,CAAC8D,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,KAAM,CAAAsC,YAAY,CAAGtC,KAAK,WAAY,CAAAuC,KAAK,CAAGvC,KAAK,CAACwC,OAAO,CAAG,eAAe,CAC7E,MAAO,+FAA+FF,YAAY,QAAQ,CAC5H,CAEA,MAAO,CAAAjB,IAAI,CACb,CAAC,CAED,cAAe,CAAAzG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}