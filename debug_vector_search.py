#!/usr/bin/env python3
"""
Debug vector search issues
"""

import requests
import json

def test_vector_search_issue():
    """Test why vector search is failing"""
    print("=== Debugging Vector Search Issue ===")
    
    # 1. Check if we have document chunks
    try:
        response = requests.get("http://localhost:8000/api/debug/document-chunks")
        if response.status_code == 200:
            data = response.json()
            chunks = data.get('document_chunks', [])
            print(f"✅ Found {len(chunks)} document chunks in database")
            
            if chunks:
                # Show first chunk details
                first_chunk = chunks[0]
                print(f"   First chunk ID: {first_chunk.get('id', 'Unknown')}")
                print(f"   First chunk filename: {first_chunk.get('filename', 'Unknown')}")
                print(f"   First chunk text length: {len(first_chunk.get('text', ''))}")
                print(f"   First chunk source_type: {first_chunk.get('source_type', 'Unknown')}")
        else:
            print(f"❌ Failed to get document chunks: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting document chunks: {str(e)}")
        return False
    
    # 2. Test various queries that should find content
    test_queries = [
        "railway",
        "ACP", 
        "document",
        "sample",
        "authority transfer",
        "train"
    ]
    
    print(f"\n=== Testing Queries ===")
    for query in test_queries:
        try:
            headers = {"Content-Type": "application/json"}
            body = {"query": query}
            response = requests.post("http://localhost:8000/api/query", headers=headers, json=body)
            
            if response.status_code == 200:
                data = response.json()
                doc_sources = len(data.get('document_sources', []))
                web_sources = len(data.get('website_sources', []))
                llm_fallback = data.get('llm_fallback', False)
                
                status = "✅" if (doc_sources > 0 or web_sources > 0) else "❌"
                print(f"   {status} '{query}': doc={doc_sources}, web={web_sources}, fallback={llm_fallback}")
                
                # If we found sources, show the links
                if doc_sources > 0:
                    for i, source in enumerate(data['document_sources'][:2]):
                        link = source.get('link', 'No link')
                        name = source.get('name', 'Unknown')
                        print(f"      Source {i+1}: {name} -> {link}")
            else:
                print(f"   ❌ '{query}': HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ '{query}': Exception {str(e)}")
    
    # 3. Test debug search endpoint
    print(f"\n=== Testing Debug Search ===")
    try:
        response = requests.get("http://localhost:8000/api/debug/search?query=railway")
        if response.status_code == 200:
            data = response.json()
            direct_doc_count = data.get('direct_document_search', {}).get('direct_document_count', 0)
            vector_doc_count = data.get('vector_document_search', {}).get('document_count', 0)
            print(f"   Direct document search: {direct_doc_count} chunks")
            print(f"   Vector document search: {vector_doc_count} chunks")
            
            if direct_doc_count > 0 and vector_doc_count == 0:
                print("   🔍 ISSUE: Direct search works but vector search fails!")
                print("   This suggests an embedding/similarity calculation problem.")
        else:
            print(f"   ❌ Debug search failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Debug search exception: {str(e)}")
    
    # 4. Test specific working query from logs
    print(f"\n=== Testing Known Working Query ===")
    try:
        headers = {"Content-Type": "application/json"}
        body = {"query": "authority transfer"}
        response = requests.post("http://localhost:8000/api/query", headers=headers, json=body)
        
        if response.status_code == 200:
            data = response.json()
            doc_sources = data.get('document_sources', [])
            print(f"   'authority transfer': {len(doc_sources)} document sources")
            
            if doc_sources:
                print("   ✅ This query works! Testing source links:")
                for source in doc_sources:
                    link = source.get('link', 'No link')
                    print(f"      Link: {link}")
                    
                    # Test if the link is properly formatted
                    if link.startswith('/viewer?file=') and '&page=' in link:
                        print(f"         ✅ Link format is correct")
                    else:
                        print(f"         ❌ Link format is incorrect")
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
    
    print(f"\n=== Summary ===")
    print("If direct search works but vector search fails, the issue is with embeddings/similarity.")
    print("If queries return sources, test the links manually in the browser.")
    print("Expected working URLs:")
    print("  http://localhost:3000/viewer?file=Authority%20Transfer%20Declaration.pdf&page=1")
    print("  http://localhost:3000/viewer?file=SampleRailwayDoc.pdf&page=1")

if __name__ == "__main__":
    test_vector_search_issue() 