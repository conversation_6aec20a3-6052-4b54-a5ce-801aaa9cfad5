#!/usr/bin/env python3
"""
Test script to debug model selection issues.
"""

import requests
import json

API_URL = "http://localhost:8000"

def test_model_selection():
    """Test different model selections."""
    
    # Test models that user reported issues with
    test_models = [
        "gemini-2.0-flash",
        "groq-llama3-70b", 
        "groq-llama3-8b",
        "deepseek-coder"
    ]
    
    query = "LLM meaning"
    
    print("🔍 Testing Model Selection Issues")
    print("=" * 50)
    
    for model in test_models:
        print(f"\n🧪 Testing model: '{model}'")
        print("-" * 30)
        
        try:
            # Test the query endpoint with different models
            response = requests.post(f"{API_URL}/api/query", 
                json={
                    "query": query,
                    "model": model,
                    "fallback_enabled": True
                }, 
                timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ Query successful")
                print(f"📄 Document answer: {'Yes' if data.get('document_answer') else 'No'}")
                print(f"🌐 Website answer: {'Yes' if data.get('website_answer') else 'No'}")
                print(f"🧠 LLM fallback: {'Yes' if data.get('llm_fallback') else 'No'}")
                print(f"🤖 Model used: {data.get('llm_model', 'Unknown')}")
                print(f"📊 Document sources: {len(data.get('document_sources', []))}")
                print(f"📊 Website sources: {len(data.get('website_sources', []))}")
                
                # Show answer preview
                answer = data.get('answer', '')
                if answer:
                    print(f"💬 Answer preview: {answer[:100]}...")
                
                # Check if answer contains specific content from sources
                doc_answer = data.get('document_answer', '')
                web_answer = data.get('website_answer', '')
                
                if doc_answer:
                    print(f"📄 Document answer preview: {doc_answer[:100]}...")
                if web_answer:
                    print(f"🌐 Website answer preview: {web_answer[:100]}...")
                    
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing model '{model}': {str(e)}")

def test_model_availability():
    """Test which models are available."""
    print(f"\n🔍 Testing Model Availability")
    print("-" * 40)
    
    # Check the LLM router configuration
    try:
        # Import llm_router to check available models
        import sys
        sys.path.append('.')
        import llm_router
        
        available_models = llm_router.get_available_models()
        
        print(f"✅ Available models: {len(available_models)}")
        for model in available_models:
            print(f"   - {model['id']}: {model['name']} ({model['provider']})")
            
        # Test API key validity
        print(f"\n🔑 Testing API Key Validity")
        providers = ["google", "groq", "deepseek"]
        
        for provider in providers:
            key_status = llm_router.check_api_key_validity(provider)
            status = "✅" if key_status.get("valid", False) else "❌"
            print(f"   {status} {provider}: {key_status.get('message', 'Unknown')}")
            
    except Exception as e:
        print(f"❌ Error checking model availability: {str(e)}")

if __name__ == "__main__":
    print("🚀 Model Selection Debug Tool")
    print("=" * 60)
    
    # Test if API is available
    try:
        response = requests.get(f"{API_URL}/api/health", timeout=3)
        if response.status_code == 200:
            print("✅ API is healthy and responding")
        else:
            print(f"❌ API health check failed: {response.status_code}")
            exit(1)
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        print(f"   Make sure the server is running on {API_URL}")
        exit(1)
    
    # Run tests
    test_model_availability()
    test_model_selection()
    
    print(f"\n✅ Model testing complete!") 