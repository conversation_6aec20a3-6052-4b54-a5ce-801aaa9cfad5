{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\App.tsx\";\nimport React, { useState, useRef, useEffect } from 'react';\nimport './App.css';\nimport { sendQuery } from './services/api';\nimport LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';\nimport InteractiveAnswer from './components/ui/InteractiveAnswer';\nimport ChatSidebar from './components/chat/ChatSidebar';\nimport TrainLoader from './components/ui/TrainLoader';\nimport VisualContent from './components/ui/VisualContent';\nimport { useChatContext } from './contexts/ChatContext';\n\n// Using ChatMessage interface from services/supabase.ts\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ChatInterface({\n  sidebarOpen,\n  setSidebarOpen\n}) {\n  var _DEFAULT_LLM_MODELS$f;\n  const {\n    currentSession,\n    messages,\n    createNewChat,\n    loadChatSession,\n    addMessage,\n    updateMessage,\n    clearCurrentChat\n  } = useChatContext();\n  const [input, setInput] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model\n  const [showTrainLoader, setShowTrainLoader] = useState(false);\n  const [currentSearchStage, setCurrentSearchStage] = useState('initializing');\n  const messagesEndRef = useRef(null);\n\n  // Scroll to bottom of chat whenever messages change\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n\n  // Helper function to determine if visual content should be shown\n  const shouldShowVisualContent = (userQuestion, documentAnswer) => {\n    const userQuery = userQuestion.toLowerCase();\n\n    // Always show visual content when user asks for images\n    if (userQuery.includes('image')) {\n      return true;\n    }\n\n    // Detect different types of requests\n    const askedForImages = userQuery.includes('image') || userQuery.includes('picture') || userQuery.includes('diagram') || userQuery.includes('chart');\n    const askedForTableData = (userQuery.includes('table') || userQuery.includes('quotation')) && (userQuery.includes('give me') || userQuery.includes('show me table') || userQuery.includes('get') || userQuery.includes('provide'));\n    const askedToShowImages = userQuery.includes('show me image') || userQuery.includes('display image') || userQuery.includes('see image');\n    const answerLower = (documentAnswer === null || documentAnswer === void 0 ? void 0 : documentAnswer.toLowerCase()) || '';\n    const hasTableInAnswer = answerLower.includes('<table>') || answerLower.includes('|') || answerLower.includes('table') && answerLower.length > 200;\n    const hasImageDescription = answerLower.includes('image') || answerLower.includes('figure') || answerLower.includes('diagram');\n    const answerHasContent = answerLower.length > 50;\n\n    // ALWAYS show visual content if user specifically asks to see images/visuals\n    if (askedToShowImages || askedForImages && (userQuery.includes('show') || userQuery.includes('display'))) {\n      console.log('🔍 DEBUG: Visual content shown because user explicitly asked for images');\n      return true;\n    }\n\n    // NEVER show if user asked for table data (they want the actual data, not images)\n    if (askedForTableData && !askedForImages) {\n      console.log('🔍 DEBUG: Visual content hidden because user asked for table data');\n      return false;\n    }\n\n    // For other cases, show only if answer doesn't already provide adequate information\n    const shouldShow = !hasTableInAnswer &&\n    // Don't show if answer already has table data\n    !hasImageDescription && (\n    // Don't show if answer already describes images well\n\n    askedForImages && !answerHasContent ||\n    // Show images if user asked and answer is short\n    !answerHasContent && answerLower.length < 50 // Show for very short answers\n    );\n    console.log('🔍 DEBUG: Visual content decision for regular query:', shouldShow);\n    return shouldShow;\n  };\n\n  // Helper function to filter visual content types based on user query\n  const filterVisualContent = (source, userQuestion) => {\n    if (typeof source !== 'object' || !source.visual_content) {\n      console.log('🔍 DEBUG: Filtering out - not object or no visual_content:', typeof source, !!(source !== null && source !== void 0 && source.visual_content));\n      return false;\n    }\n    const userQueryLower = userQuestion.toLowerCase();\n    const sourceObj = source;\n    console.log('🔍 DEBUG: Checking visual content filter for:', {\n      content_type: sourceObj.content_type,\n      has_visual_content: !!sourceObj.visual_content,\n      user_query: userQueryLower,\n      filename: sourceObj.filename,\n      page: sourceObj.page,\n      visual_content_sample: sourceObj.visual_content ? JSON.stringify(sourceObj.visual_content).substring(0, 200) : 'none'\n    });\n\n    // Extract specific project/quotation numbers from user query\n    const projectMatch = userQueryLower.match(/project\\s*(\\d+)/);\n    const quotationMatch = userQueryLower.match(/quotation\\s*(\\d+)/);\n\n    // Extract company names for logo queries\n    const logoQueryPatterns = [/(?:logo\\s+(?:of\\s+)?|show\\s+me\\s+(?:the\\s+)?logo\\s+(?:of\\s+)?)([A-Z][A-Za-z\\s&]+?)(?:\\s+logo|\\s+enterprises|\\s+company|\\s+corp|\\s+ltd|\\s+inc|\\s*$)/i, /([A-Z][A-Za-z\\s&]+?)\\s+(?:enterprises|company|corp|ltd|inc)\\s+logo/i, /([A-Z][A-Za-z\\s&]+?)\\s+logo/i];\n    let companyMatch = null;\n    for (const pattern of logoQueryPatterns) {\n      const match = userQuestion.match(pattern);\n      if (match) {\n        companyMatch = match[1].trim();\n        break;\n      }\n    }\n\n    // Check if the visual content contains relevant information for the specific request\n    const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();\n\n    // If user asked for a specific company logo\n    if (companyMatch && userQueryLower.includes('logo')) {\n      var _sourceObj$visual_con, _sourceObj$visual_con2, _sourceObj$visual_con3, _sourceObj$visual_con4;\n      const companyNameLower = companyMatch.toLowerCase();\n      const isImage = sourceObj.content_type === 'image';\n      console.log('🔍 DEBUG: Company logo search:', {\n        company: companyMatch,\n        isImage,\n        has_detected_companies: !!((_sourceObj$visual_con = sourceObj.visual_content) !== null && _sourceObj$visual_con !== void 0 && _sourceObj$visual_con.detected_companies),\n        detected_companies: ((_sourceObj$visual_con2 = sourceObj.visual_content) === null || _sourceObj$visual_con2 === void 0 ? void 0 : _sourceObj$visual_con2.detected_companies) || [],\n        ocr_text: ((_sourceObj$visual_con3 = sourceObj.visual_content) === null || _sourceObj$visual_con3 === void 0 ? void 0 : _sourceObj$visual_con3.ocr_text) || '',\n        is_logo: ((_sourceObj$visual_con4 = sourceObj.visual_content) === null || _sourceObj$visual_con4 === void 0 ? void 0 : _sourceObj$visual_con4.is_logo) || false\n      });\n      if (isImage) {\n        var _sourceObj$visual_con5, _sourceObj$visual_con6, _sourceObj$visual_con7;\n        // Check if this image contains the requested company\n        const hasCompanyInContent = visualContentStr.includes(companyNameLower);\n        const hasCompanyInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(companyNameLower);\n\n        // Check detected companies from OCR\n        const detectedCompanies = ((_sourceObj$visual_con5 = sourceObj.visual_content) === null || _sourceObj$visual_con5 === void 0 ? void 0 : _sourceObj$visual_con5.detected_companies) || [];\n        const hasDetectedCompany = detectedCompanies.some(company => company.toLowerCase().includes(companyNameLower) || companyNameLower.includes(company.toLowerCase()));\n\n        // Check OCR text\n        const ocrText = ((_sourceObj$visual_con6 = sourceObj.visual_content) === null || _sourceObj$visual_con6 === void 0 ? void 0 : _sourceObj$visual_con6.ocr_text) || '';\n        const hasOcrMatch = ocrText.toLowerCase().includes(companyNameLower);\n\n        // Check if marked as logo\n        const isLogo = ((_sourceObj$visual_con7 = sourceObj.visual_content) === null || _sourceObj$visual_con7 === void 0 ? void 0 : _sourceObj$visual_con7.is_logo) || false;\n        const isRelevantLogo = hasCompanyInContent || hasCompanyInFilename || hasDetectedCompany || hasOcrMatch || isLogo;\n        console.log('🔍 DEBUG: Company logo filter result:', {\n          company: companyMatch,\n          hasCompanyInContent,\n          hasCompanyInFilename,\n          hasDetectedCompany,\n          hasOcrMatch,\n          isLogo,\n          isRelevantLogo,\n          should_show: isImage && isRelevantLogo\n        });\n        return isRelevantLogo;\n      }\n      return false; // Not an image for logo query\n    }\n\n    // If user asked for a specific project image\n    if (projectMatch && userQueryLower.includes('image')) {\n      var _sourceObj$visual_con8, _sourceObj$visual_con9;\n      const projectNumber = projectMatch[1];\n      const isImage = sourceObj.content_type === 'image';\n\n      // Check if this image is related to the specific project\n      const hasProjectInContent = visualContentStr.includes(`project ${projectNumber}`) || visualContentStr.includes(`project${projectNumber}`);\n      const hasProjectInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(`project ${projectNumber}`);\n      const isOnProjectPage = sourceObj.page && Math.abs(sourceObj.page - parseInt(projectNumber)) <= 1; // Allow adjacent pages\n      const hasProjectContext = ((_sourceObj$visual_con8 = sourceObj.visual_content) === null || _sourceObj$visual_con8 === void 0 ? void 0 : _sourceObj$visual_con8.project_context) && sourceObj.visual_content.project_context.includes(`project ${projectNumber}`);\n      const isRelevantProject = hasProjectInContent || hasProjectInFilename || isOnProjectPage || hasProjectContext;\n      console.log('🔍 DEBUG: Project', projectNumber, 'image filter:', {\n        isImage,\n        hasProjectInContent,\n        hasProjectInFilename,\n        isOnProjectPage,\n        hasProjectContext,\n        isRelevantProject,\n        visualContentStr_sample: visualContentStr.substring(0, 150),\n        project_context: (_sourceObj$visual_con9 = sourceObj.visual_content) === null || _sourceObj$visual_con9 === void 0 ? void 0 : _sourceObj$visual_con9.project_context,\n        page: sourceObj.page,\n        should_show: isImage && isRelevantProject\n      });\n\n      // If this is an image and it's relevant to the project, show it\n      // If no images are relevant, we'll fall back to showing all images\n      return isImage && isRelevantProject;\n    }\n\n    // If user asked for a specific quotation table\n    if (quotationMatch && userQueryLower.includes('table')) {\n      const quotationNumber = quotationMatch[1];\n      const isTable = sourceObj.content_type === 'table';\n\n      // Check if this table is related to the specific quotation\n      const isRelevantQuotation = visualContentStr.includes(`quotation ${quotationNumber}`) || visualContentStr.includes(`quotation${quotationNumber}`) || visualContentStr.includes(`quote ${quotationNumber}`);\n      console.log('🔍 DEBUG: Quotation', quotationNumber, 'table filter:', {\n        isTable,\n        isRelevantQuotation,\n        should_show: isTable && isRelevantQuotation\n      });\n      return isTable && isRelevantQuotation;\n    }\n\n    // If user specifically asked for images (but no specific project), only show images\n    if (userQueryLower.includes('image') && !userQueryLower.includes('table')) {\n      const isImage = sourceObj.content_type === 'image';\n      console.log('🔍 DEBUG: User asked for images only, source is image:', isImage);\n      return isImage;\n    }\n\n    // If user specifically asked for tables (but no specific quotation), only show tables\n    if (userQueryLower.includes('table') && !userQueryLower.includes('image')) {\n      const isTable = sourceObj.content_type === 'table';\n      console.log('🔍 DEBUG: User asked for tables only, source is table:', isTable);\n      return isTable;\n    }\n\n    // Default: show all visual content\n    console.log('🔍 DEBUG: Showing all visual content by default');\n    return true;\n  };\n\n  // Handle command shortcuts in the textbox\n  const handleCommandShortcut = input => {\n    // Check if the input is a command\n    if (input.startsWith('/')) {\n      const command = input.split(' ')[0].toLowerCase();\n\n      // Command: /model <model-name>\n      if (command === '/model') {\n        const modelArg = input.substring(7).trim();\n        const matchedModel = DEFAULT_LLM_MODELS.find(m => m.name.toLowerCase().includes(modelArg.toLowerCase()) || m.id.toLowerCase().includes(modelArg.toLowerCase()));\n        if (matchedModel && matchedModel.enabled) {\n          setActiveLLMModel(matchedModel.id);\n          setInput('');\n          return 'processed';\n        }\n      }\n\n      // Command: /reset or /clear - clear chat history\n      else if (command === '/reset' || command === '/clear') {\n        clearCurrentChat();\n        setInput('');\n        return 'processed';\n      }\n    }\n    return 'not_processed';\n  };\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (!input.trim() || isSubmitting) return;\n\n    // Handle command shortcuts like /reset, /model, etc.\n    if (input.startsWith('/')) {\n      const result = handleCommandShortcut(input);\n      if (result === 'processed') {\n        setInput('');\n        return;\n      }\n      // If not processed as a command, continue as a regular message\n    }\n    return await sendUserMessage(input);\n  };\n  const sendUserMessage = async messageText => {\n    var _messagesEndRef$curre;\n    // Create new chat session if none exists\n    if (!currentSession) {\n      await createNewChat();\n    }\n    const userMessage = {\n      id: `user-${Date.now()}`,\n      content: messageText,\n      sender: 'user',\n      timestamp: new Date().toISOString(),\n      chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp'\n    };\n    addMessage(userMessage);\n    setInput('');\n    const messageId = Date.now();\n    const tempAiMessage = {\n      id: `ai-${messageId}`,\n      content: '',\n      sender: 'ai',\n      loading: true,\n      timestamp: new Date().toISOString(),\n      llm_model: activeLLMModel,\n      chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp'\n    };\n    addMessage(tempAiMessage);\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n    setIsSubmitting(true);\n    setShowTrainLoader(true);\n    setCurrentSearchStage('initializing');\n    try {\n      var _messagesEndRef$curre2, _response$document_so, _response$website_sou, _messagesEndRef$curre3;\n      (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n        behavior: 'smooth'\n      });\n\n      // Simulate search progress updates with train loader\n      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);\n      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);\n      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);\n      const response = await sendQuery(messageText, activeLLMModel);\n\n      // Create the AI message based on strict priority logic\n      const aiMessage = {\n        id: `ai-${messageId}`,\n        content: response.answer,\n        document_answer: response.document_answer,\n        website_answer: response.website_answer,\n        llm_model: response.llm_model || activeLLMModel,\n        sender: 'ai',\n        sources: response.sources,\n        document_sources: response.document_sources,\n        website_sources: response.website_sources,\n        chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp',\n        llm_fallback: response.llm_fallback\n      };\n\n      // Debug logging\n      console.log('AI Message created:', {\n        document_answer: response.document_answer ? 'EXISTS' : 'MISSING',\n        website_answer: response.website_answer ? 'EXISTS' : 'MISSING',\n        document_sources_count: ((_response$document_so = response.document_sources) === null || _response$document_so === void 0 ? void 0 : _response$document_so.length) || 0,\n        website_sources_count: ((_response$website_sou = response.website_sources) === null || _response$website_sou === void 0 ? void 0 : _response$website_sou.length) || 0,\n        llm_fallback: response.llm_fallback\n      });\n\n      // Debug visual content sources\n      console.log('🔍 DEBUG: Document sources from API:', response.document_sources);\n      if (response.document_sources) {\n        response.document_sources.forEach((source, index) => {\n          console.log(`🔍 DEBUG: Source ${index}:`, {\n            content_type: source.content_type,\n            has_visual_content: !!source.visual_content,\n            storage_url: source.storage_url,\n            display_type: source.display_type,\n            visual_content_keys: source.visual_content ? Object.keys(source.visual_content) : []\n          });\n        });\n      }\n      updateMessage(tempAiMessage.id, aiMessage);\n      (_messagesEndRef$curre3 = messagesEndRef.current) === null || _messagesEndRef$curre3 === void 0 ? void 0 : _messagesEndRef$curre3.scrollIntoView({\n        behavior: 'smooth'\n      });\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      // Provide more helpful error message without the specific query\n      const errorMessage = {\n        id: `ai-${messageId}`,\n        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,\n        sender: 'ai',\n        chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp',\n        llm_fallback: true\n      };\n      updateMessage(tempAiMessage.id, errorMessage);\n    } finally {\n      setIsSubmitting(false);\n      setShowTrainLoader(false);\n      setCurrentSearchStage('complete');\n    }\n  };\n  const processDocumentSources = sources => {\n    if (!sources || sources.length === 0) return [];\n\n    // Group by filename to avoid repetition\n    const groupedSources = {};\n    sources.forEach(source => {\n      let filename;\n      let page;\n      if (typeof source === 'string') {\n        // Parse string format like \"MaintenanceManual.pdf – Page 3\"\n        const match = source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);\n        filename = match ? match[1].trim() : source;\n        page = match && match[2] ? parseInt(match[2]) : 1;\n      } else {\n        filename = source.name || source.filename || \"Unknown Document\";\n        page = source.page || 1;\n      }\n      if (!groupedSources[filename]) {\n        groupedSources[filename] = {\n          filename,\n          pages: []\n        };\n      }\n      if (!groupedSources[filename].pages.includes(page)) {\n        groupedSources[filename].pages.push(page);\n      }\n    });\n\n    // Convert to display format with viewer links\n    return Object.values(groupedSources).map(group => {\n      const sortedPages = group.pages.sort((a, b) => a - b);\n      const pageText = sortedPages.length === 1 ? `Page ${sortedPages[0]}` : `Pages ${sortedPages.join(', ')}`;\n\n      // Create link for document viewer that opens at exact page number\n      return {\n        text: `${group.filename} – ${pageText}`,\n        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,\n        isDocument: true\n      };\n    });\n  };\n  const processWebsiteSources = sources => {\n    if (!sources || sources.length === 0) return [];\n\n    // Remove duplicates and format\n    const uniqueUrls = new Set();\n    const processed = [];\n    sources.forEach(source => {\n      let url;\n      let displayText;\n      if (typeof source === 'string') {\n        url = source.startsWith('http') ? source : `https://${source}`;\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = source;\n        }\n      } else {\n        url = source.url || 'https://railgpt.indianrailways.gov.in';\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = url;\n        }\n      }\n      if (!uniqueUrls.has(url)) {\n        uniqueUrls.add(url);\n        processed.push({\n          text: displayText,\n          link: url,\n          isDocument: false // Mark as website source\n        });\n      }\n    });\n    return processed; // Return all website sources\n  };\n\n  // Component for expandable source list with appropriate click behaviors\n  const SourceList = ({\n    items,\n    maxVisible = 3\n  }) => {\n    const [expanded, setExpanded] = useState(items.length <= maxVisible);\n    if (items.length === 0) return null;\n    const visibleItems = expanded ? items : items.slice(0, maxVisible);\n    const hasMore = items.length > maxVisible;\n    const handleDocumentClick = (e, link) => {\n      // If we want to handle document links in a special way, we can do so here\n      // For example, we could open a modal or new tab with the document viewer\n      // Currently, just allowing regular link behavior\n    };\n    return /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"text-xs list-disc pl-4 mt-1 space-y-1\",\n      children: [visibleItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: item.link ? /*#__PURE__*/_jsxDEV(\"a\", {\n          href: item.link,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"hover:underline text-blue-600 transition-colors duration-200\",\n          title: item.isDocument ? \"Open document at this page\" : \"Open website in new tab\",\n          onClick: item.isDocument ? e => handleDocumentClick(e, item.link) : undefined,\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 15\n      }, this)), hasMore && !expanded && /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"list-none\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setExpanded(true),\n          className: \"text-blue-500 hover:underline text-xs transition-colors duration-200\",\n          children: [\"+ \", items.length - maxVisible, \" more sources\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 11\n      }, this), hasMore && expanded && /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"list-none\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setExpanded(false),\n          className: \"text-blue-500 hover:underline text-xs transition-colors duration-200\",\n          children: \"Show less\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Sidebar handlers\n  const handleChatSelect = async chatSession => {\n    // Load the selected chat session using context\n    await loadChatSession(chatSession.id);\n    setActiveLLMModel(chatSession.model_used || 'gemini-2.0-flash');\n    setSidebarOpen(false); // Close sidebar on mobile after selection\n  };\n  const handleNewChat = async () => {\n    console.log('Creating new chat...');\n    await createNewChat();\n    setSidebarOpen(false); // Close sidebar on mobile after creating new chat\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-100 transition-colors duration-300\",\n    children: [/*#__PURE__*/_jsxDEV(TrainLoader, {\n      isVisible: showTrainLoader,\n      message: (() => {\n        switch (currentSearchStage) {\n          case 'searching_documents':\n            return \"RailGPT Searching in Documents...\";\n          case 'searching_websites':\n            return \"RailGPT Searching in Websites...\";\n          case 'generating_answer':\n            return \"RailGPT Generating Response...\";\n          default:\n            return \"RailGPT Processing Your Query...\";\n        }\n      })(),\n      trainType: \"express\",\n      currentStage: currentSearchStage,\n      sidebarOpen: sidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChatSidebar, {\n      isOpen: sidebarOpen,\n      onToggle: () => setSidebarOpen(!sidebarOpen),\n      currentChatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || '',\n      onChatSelect: handleChatSelect,\n      onNewChat: handleNewChat\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex flex-col flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-80' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex-1 ${messages.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-4 pb-32`,\n        children: messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl font-semibold mb-3\",\n              children: \"Welcome to RailGPT!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Ask questions about Indian Railways...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `mb-4 ${message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `max-w-4xl rounded-lg p-4 transition-colors duration-300 ${message.sender === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-800 shadow-md'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: message.sender === 'user' ? 'You' : 'RailGPT'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 23\n                }, this), message.timestamp && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs ml-2 ${message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'}`,\n                  children: new Date(message.timestamp).toLocaleTimeString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 21\n              }, this), message.sender === 'user' && message.content && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 whitespace-pre-wrap\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 23\n              }, this), message.sender === 'ai' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: ((_message$document_ans, _message$website_answ, _message$document_sou, _message$website_sour) => {\n                  // Only hide if this specific message is loading AND has no content yet\n                  if (message.loading && showTrainLoader && !message.content && !message.document_answer && !message.website_answer) {\n                    return null;\n                  }\n\n                  // Process sources with improved deduplication\n                  const documentSourceItems = processDocumentSources(message.document_sources);\n                  const websiteSourceItems = processWebsiteSources(message.website_sources);\n\n                  // Check what content is available for conditional display\n                  const hasDocumentContent = !!(message.document_answer && message.document_answer.trim() !== \"\");\n                  const hasWebsiteContent = !!(message.website_answer && message.website_answer.trim() !== \"\");\n                  // LLM fallback happens if NEITHER document sources NOR website sources are found, or if explicitly set\n                  const hasLLMFallback = !hasDocumentContent && !hasWebsiteContent || message.llm_fallback;\n\n                  // Debug logging for rendering\n                  console.log(`🔍 Rendering message ${message.id}:`, {\n                    hasDocumentContent,\n                    hasWebsiteContent,\n                    hasLLMFallback,\n                    documentAnswerLength: ((_message$document_ans = message.document_answer) === null || _message$document_ans === void 0 ? void 0 : _message$document_ans.length) || 0,\n                    websiteAnswerLength: ((_message$website_answ = message.website_answer) === null || _message$website_answ === void 0 ? void 0 : _message$website_answ.length) || 0,\n                    documentSourcesCount: documentSourceItems.length,\n                    websiteSourcesCount: websiteSourceItems.length,\n                    rawDocumentAnswer: message.document_answer ? 'EXISTS' : 'MISSING',\n                    rawWebsiteAnswer: message.website_answer ? 'EXISTS' : 'MISSING',\n                    rawDocumentSources: ((_message$document_sou = message.document_sources) === null || _message$document_sou === void 0 ? void 0 : _message$document_sou.length) || 0,\n                    rawWebsiteSources: ((_message$website_sour = message.website_sources) === null || _message$website_sour === void 0 ? void 0 : _message$website_sour.length) || 0\n                  });\n\n                  // Get the user's question for context - find the most recent user message before this AI message\n                  const currentMessageIndex = messages.findIndex(m => m.id === message.id);\n                  let userQuestion = '';\n\n                  // Look backwards from current AI message to find the most recent user message\n                  for (let i = currentMessageIndex - 1; i >= 0; i--) {\n                    if (messages[i].sender === 'user' && messages[i].content) {\n                      userQuestion = messages[i].content;\n                      break;\n                    }\n                  }\n                  console.log('🔍 DEBUG: Found user question for AI message:', {\n                    aiMessageId: message.id,\n                    userQuestion\n                  });\n\n                  // Conditional display logic based on answer sources\n                  const components = [];\n                  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                  let answerSource = '';\n\n                  // Case 1: Answer exists from both uploaded documents and websites\n                  if (hasDocumentContent && hasWebsiteContent) {\n                    answerSource = 'document_and_website';\n\n                    // Document answer card\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    const documentName = documentSourceItems.length === 1 ? documentSourceItems[0].text.split(' – ')[0] : 'Uploaded Documents';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-blue-800 text-sm mb-3 flex items-center\",\n                        children: [\"\\uD83D\\uDCC4 Answer from \", documentName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.document_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 33\n                      }, this), (_message$document_sou2 => {\n                        const visualSources = ((_message$document_sou2 = message.document_sources) === null || _message$document_sou2 === void 0 ? void 0 : _message$document_sou2.filter(source => typeof source === 'object' && source.visual_content)) || [];\n\n                        // First try to find sources that match the user's specific request\n                        let relevantSources = visualSources.filter(source => filterVisualContent(source, userQuestion));\n\n                        // If no specific matches and user asked for images, show any available images\n                        if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                          relevantSources = visualSources.filter(source => source.content_type === 'image');\n                          console.log('🔍 DEBUG: No specific project matches, showing all available images:', relevantSources.length);\n                        }\n                        return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                            className: \"text-sm font-semibold text-blue-800 mb-3\",\n                            children: \"\\uD83D\\uDCCA Visual Content:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 762,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"space-y-3\",\n                            children: relevantSources.map((source, index) => /*#__PURE__*/_jsxDEV(VisualContent, {\n                              source: source\n                            }, index, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 765,\n                              columnNumber: 43\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 763,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 761,\n                          columnNumber: 37\n                        }, this) : null;\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-blue-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-blue-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 773,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: documentSourceItems,\n                          maxVisible: 5\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 774,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 772,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"document-card\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 31\n                    }, this));\n\n                    // Website answer card\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-green-800 text-sm mb-3 flex items-center\",\n                        children: [\"\\uD83C\\uDF10 Answer from \", websiteLabel]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 785,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.website_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 788,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-green-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-green-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 795,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: websiteSourceItems,\n                          maxVisible: 3\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 796,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 794,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"website-card\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 784,\n                      columnNumber: 31\n                    }, this));\n\n                    // Case 2: Answer exists only from documents\n                  } else if (hasDocumentContent) {\n                    answerSource = 'document_only';\n\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    const documentName = documentSourceItems.length === 1 ? documentSourceItems[0].text.split(' – ')[0] : 'Uploaded Documents';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-blue-800 text-sm mb-3 flex items-center\",\n                        children: \"\\uD83D\\uDCC4 Answer from Uploaded Documents\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 812,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.document_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 815,\n                        columnNumber: 33\n                      }, this), (_message$document_sou3 => {\n                        const visualSources = ((_message$document_sou3 = message.document_sources) === null || _message$document_sou3 === void 0 ? void 0 : _message$document_sou3.filter(source => typeof source === 'object' && source.visual_content)) || [];\n\n                        // First try to find sources that match the user's specific request\n                        let relevantSources = visualSources.filter(source => filterVisualContent(source, userQuestion));\n\n                        // If no specific matches and user asked for images, show any available images\n                        if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                          relevantSources = visualSources.filter(source => source.content_type === 'image');\n                          console.log('🔍 DEBUG: No specific project matches for document-only case, showing all available images:', relevantSources.length);\n                        }\n                        return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                            className: \"text-sm font-semibold text-blue-800 mb-3\",\n                            children: \"\\uD83D\\uDCCA Visual Content:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 843,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"space-y-3\",\n                            children: relevantSources.map((source, index) => /*#__PURE__*/_jsxDEV(VisualContent, {\n                              source: source\n                            }, index, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 846,\n                              columnNumber: 43\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 844,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 842,\n                          columnNumber: 37\n                        }, this) : null;\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-blue-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-blue-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 854,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: documentSourceItems,\n                          maxVisible: 5\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 855,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 853,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"document-priority\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 811,\n                      columnNumber: 31\n                    }, this));\n\n                    // Case 3: Answer exists only from websites\n                  } else if (hasWebsiteContent) {\n                    answerSource = 'website_only';\n\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-green-800 text-sm mb-3 flex items-center\",\n                        children: \"\\uD83C\\uDF10 Answer from Extracted Websites\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 869,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.website_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 872,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-green-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-green-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 879,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: websiteSourceItems,\n                          maxVisible: 3\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 880,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 878,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"website-priority\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 31\n                    }, this));\n\n                    // Case 4: No answer from either - fallback to LLM\n                  } else if (hasLLMFallback) {\n                    var _message$llm_model, _message$llm_model2, _message$llm_model3, _message$llm_model4, _message$llm_model5, _message$llm_model6;\n                    answerSource = 'llm';\n                    const modelName = message.llm_model || 'Gemini';\n                    const modelLogo = (_message$llm_model = message.llm_model) !== null && _message$llm_model !== void 0 && _message$llm_model.includes('chatgpt') ? '🤖' : (_message$llm_model2 = message.llm_model) !== null && _message$llm_model2 !== void 0 && _message$llm_model2.includes('groq') ? '⚡' : (_message$llm_model3 = message.llm_model) !== null && _message$llm_model3 !== void 0 && _message$llm_model3.includes('deepseek') ? '🔍' : (_message$llm_model4 = message.llm_model) !== null && _message$llm_model4 !== void 0 && _message$llm_model4.includes('qwen') ? '🌐' : (_message$llm_model5 = message.llm_model) !== null && _message$llm_model5 !== void 0 && _message$llm_model5.includes('ollama') ? '🏠' : (_message$llm_model6 = message.llm_model) !== null && _message$llm_model6 !== void 0 && _message$llm_model6.includes('huggingface') ? '🤗' : '🧠';\n\n                    // Only show the LLM fallback card if not in loading state\n                    if (!showTrainLoader || !message.loading) {\n                      components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"font-semibold text-purple-800 text-sm mb-3 flex items-center\",\n                          children: [modelLogo, \" Answer generated by \", modelName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 901,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                          content: message.content || \"I couldn't find any relevant information to answer your question.\",\n                          query: userQuestion,\n                          model: message.llm_model || 'Gemini',\n                          chatId: message.chatId\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 904,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-3 pt-3 border-t border-purple-200\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-purple-600 italic\",\n                            children: \"This answer was generated by an AI model as no relevant information was found in your documents or websites.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 911,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 910,\n                          columnNumber: 33\n                        }, this)]\n                      }, \"llm-fallback\", true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 900,\n                        columnNumber: 31\n                      }, this));\n                    }\n\n                    // Case 5: No sources found and fallback disabled (or similar edge case)\n                  } else {\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    answerSource = 'no_results';\n\n                    // Only show the \"no results\" card if not in loading state\n                    if (!showTrainLoader || !message.loading) {\n                      components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-600 mb-2\",\n                          children: \"No sources found\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 928,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                          content: message.content || \"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\",\n                          query: userQuestion,\n                          model: message.llm_model || 'Gemini',\n                          chatId: message.chatId\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 929,\n                          columnNumber: 33\n                        }, this)]\n                      }, \"no-results\", true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 927,\n                        columnNumber: 31\n                      }, this));\n                    }\n                  }\n\n                  // If we have components to display, render them\n                  if (components.length > 0) {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3\",\n                      children: components\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 943,\n                      columnNumber: 31\n                    }, this);\n                  }\n\n                  // Fallback for any unhandled edge cases (should rarely happen)\n                  console.warn(\"Frontend: Unhandled rendering case\");\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 mb-2\",\n                      children: \"Rendering Error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 953,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                      content: message.content || \"An error occurred while rendering the response.\",\n                      query: userQuestion,\n                      model: message.llm_model || 'Gemini',\n                      chatId: message.chatId\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 954,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 29\n                  }, this);\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 19\n            }, this)\n          }, message.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef,\n            style: {\n              float: 'left',\n              clear: 'both'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 968,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen ? 'lg:left-80 left-0' : 'left-0'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSendMessage,\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative flex\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: input,\n              onChange: e => {\n                const newValue = e.target.value;\n                setInput(newValue);\n                // Don't handle command shortcuts as you type, only on submit\n              },\n              placeholder: \"Type your message... (/model, /reset, /clear)\",\n              className: \"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\",\n              disabled: isSubmitting,\n              \"aria-label\": \"Message input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 976,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(LLMSelector, {\n              currentModel: activeLLMModel,\n              onModelChange: modelId => setActiveLLMModel(modelId),\n              isLoading: isSubmitting\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 993,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isSubmitting || !input.trim(),\n              className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\",\n              title: \"Send message\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isSubmitting ? \"Sending...\" : \"Send\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-400 mt-1 text-center\",\n          children: [\"Current model: \", ((_DEFAULT_LLM_MODELS$f = DEFAULT_LLM_MODELS.find(m => m.id === activeLLMModel)) === null || _DEFAULT_LLM_MODELS$f === void 0 ? void 0 : _DEFAULT_LLM_MODELS$f.name) || activeLLMModel]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1008,\n          columnNumber: 23\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 974,\n        columnNumber: 9\n      }, this), messages.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-36\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1013,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 589,\n    columnNumber: 5\n  }, this);\n}\nfunction App({\n  sidebarOpen,\n  setSidebarOpen\n}) {\n  return /*#__PURE__*/_jsxDEV(ChatInterface, {\n    sidebarOpen: sidebarOpen,\n    setSidebarOpen: setSidebarOpen\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1026,\n    columnNumber: 5\n  }, this);\n}\nexport default App;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "LLMSelector", "DEFAULT_LLM_MODELS", "InteractiveAnswer", "ChatSidebar", "TrainLoader", "VisualContent", "useChatContext", "jsxDEV", "_jsxDEV", "ChatInterface", "sidebarOpen", "setSidebarOpen", "_DEFAULT_LLM_MODELS$f", "currentSession", "messages", "createNewChat", "loadChatSession", "addMessage", "updateMessage", "clearCurrentChat", "input", "setInput", "isSubmitting", "setIsSubmitting", "activeLLMModel", "setActiveLLMModel", "showTrainLoader", "setShowTrainLoader", "currentSearchStage", "setCurrentSearchStage", "messagesEndRef", "current", "scrollIntoView", "behavior", "shouldShowVisualContent", "userQuestion", "documentAnswer", "userQuery", "toLowerCase", "includes", "askedForImages", "askedForTableData", "askedToShowImages", "answerLower", "hasTableInAnswer", "length", "hasImageDescription", "answerHasContent", "console", "log", "shouldShow", "filterVisualContent", "source", "visual_content", "userQueryLower", "sourceObj", "content_type", "has_visual_content", "user_query", "filename", "page", "visual_content_sample", "JSON", "stringify", "substring", "projectMatch", "match", "quotationMatch", "logoQueryPatterns", "companyMatch", "pattern", "trim", "visualContentStr", "_sourceObj$visual_con", "_sourceObj$visual_con2", "_sourceObj$visual_con3", "_sourceObj$visual_con4", "companyNameLower", "isImage", "company", "has_detected_companies", "detected_companies", "ocr_text", "is_logo", "_sourceObj$visual_con5", "_sourceObj$visual_con6", "_sourceObj$visual_con7", "hasCompanyInContent", "hasCompanyInFilename", "detectedCompanies", "hasDetectedCompany", "some", "ocrText", "hasOcrMatch", "isLogo", "isRelevantLogo", "should_show", "_sourceObj$visual_con8", "_sourceObj$visual_con9", "projectNumber", "hasProjectInContent", "hasProjectInFilename", "isOnProjectPage", "Math", "abs", "parseInt", "hasProjectContext", "project_context", "isRelevantProject", "visualContentStr_sample", "quotationNumber", "isTable", "isRelevantQuotation", "handleCommandShortcut", "startsWith", "command", "split", "modelArg", "matchedModel", "find", "m", "name", "id", "enabled", "handleSendMessage", "e", "preventDefault", "result", "sendUserMessage", "messageText", "_messagesEndRef$curre", "userMessage", "Date", "now", "content", "sender", "timestamp", "toISOString", "chatId", "messageId", "tempAiMessage", "loading", "llm_model", "_messagesEndRef$curre2", "_response$document_so", "_response$website_sou", "_messagesEndRef$curre3", "setTimeout", "response", "aiMessage", "answer", "document_answer", "website_answer", "sources", "document_sources", "website_sources", "llm_fallback", "document_sources_count", "website_sources_count", "for<PERSON>ach", "index", "storage_url", "display_type", "visual_content_keys", "Object", "keys", "error", "errorMessage", "processDocumentSources", "groupedSources", "pages", "push", "values", "map", "group", "sortedPages", "sort", "a", "b", "pageText", "join", "text", "link", "encodeURIComponent", "isDocument", "processWebsiteSources", "uniqueUrls", "Set", "processed", "url", "displayText", "url<PERSON>bj", "URL", "hostname", "replace", "has", "add", "SourceList", "items", "maxVisible", "expanded", "setExpanded", "visibleItems", "slice", "hasMore", "handleDocumentClick", "className", "children", "item", "href", "target", "rel", "title", "onClick", "undefined", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleChatSelect", "chatSession", "model_used", "handleNewChat", "isVisible", "message", "trainType", "currentStage", "isOpen", "onToggle", "currentChatId", "onChatSelect", "onNewChat", "toLocaleTimeString", "_message$document_ans", "_message$website_answ", "_message$document_sou", "_message$website_sour", "documentSourceItems", "websiteSourceItems", "hasDocumentContent", "has<PERSON>ebsite<PERSON><PERSON>nt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentAnswerLength", "websiteAnswerLength", "documentSourcesCount", "websiteSourcesCount", "rawDocumentAnswer", "rawWebsiteAnswer", "rawDocumentSources", "rawWebsiteSources", "currentMessageIndex", "findIndex", "i", "aiMessageId", "components", "answerSource", "documentName", "query", "model", "_message$document_sou2", "visualSources", "filter", "relevantSources", "websiteLabel", "_message$document_sou3", "_message$llm_model", "_message$llm_model2", "_message$llm_model3", "_message$llm_model4", "_message$llm_model5", "_message$llm_model6", "modelName", "modelLogo", "warn", "ref", "style", "float", "clear", "onSubmit", "type", "value", "onChange", "newValue", "placeholder", "disabled", "currentModel", "onModelChange", "modelId", "isLoading", "App"], "sources": ["C:/IR App/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './App.css';\nimport { sendQuery } from './services/api';\nimport LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';\nimport InteractiveAnswer from './components/ui/InteractiveAnswer';\nimport ChatSidebar from './components/chat/ChatSidebar';\nimport TrainLoader from './components/ui/TrainLoader';\nimport VisualContent from './components/ui/VisualContent';\nimport { useChatContext } from './contexts/ChatContext';\nimport { ChatSession, ChatMessage } from './services/supabase';\n\ninterface Source {\n  source_type: string;\n  filename?: string;\n  page?: number;\n  url?: string;\n  link?: string; // For document viewer links\n  name?: string; // For display name\n  // Visual content fields\n  content_type?: string;  // \"text\", \"table\", \"image\", \"chart_diagram\"\n  visual_content?: Record<string, any>;  // Visual content metadata\n  storage_url?: string;  // URL for stored visual content\n  display_type?: string;  // \"text\", \"html_table\", \"image\", \"base64_image\"\n}\n\n// Using ChatMessage interface from services/supabase.ts\n\ninterface ChatInterfaceProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction ChatInterface({ sidebarOpen, setSidebarOpen }: ChatInterfaceProps) {\n  const {\n    currentSession,\n    messages,\n    createNewChat,\n    loadChatSession,\n    addMessage,\n    updateMessage,\n    clearCurrentChat\n  } = useChatContext();\n\n  const [input, setInput] = useState<string>('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model\n  const [showTrainLoader, setShowTrainLoader] = useState(false);\n  const [currentSearchStage, setCurrentSearchStage] = useState<'initializing' | 'searching_documents' | 'searching_websites' | 'generating_answer' | 'complete'>('initializing');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Scroll to bottom of chat whenever messages change\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  // Helper function to determine if visual content should be shown\n  const shouldShowVisualContent = (userQuestion: string, documentAnswer: string): boolean => {\n    const userQuery = userQuestion.toLowerCase();\n\n    // Always show visual content when user asks for images\n    if (userQuery.includes('image')) {\n      return true;\n    }\n    \n    // Detect different types of requests\n    const askedForImages = userQuery.includes('image') || userQuery.includes('picture') || userQuery.includes('diagram') || userQuery.includes('chart');\n    const askedForTableData = (userQuery.includes('table') || userQuery.includes('quotation')) && \n                             (userQuery.includes('give me') || userQuery.includes('show me table') || userQuery.includes('get') || userQuery.includes('provide'));\n    const askedToShowImages = userQuery.includes('show me image') || userQuery.includes('display image') || userQuery.includes('see image');\n    \n    const answerLower = documentAnswer?.toLowerCase() || '';\n    const hasTableInAnswer = answerLower.includes('<table>') || answerLower.includes('|') || \n                            (answerLower.includes('table') && answerLower.length > 200);\n    const hasImageDescription = answerLower.includes('image') || answerLower.includes('figure') || answerLower.includes('diagram');\n    const answerHasContent = answerLower.length > 50;\n    \n    // ALWAYS show visual content if user specifically asks to see images/visuals\n    if (askedToShowImages || (askedForImages && (userQuery.includes('show') || userQuery.includes('display')))) {\n      console.log('🔍 DEBUG: Visual content shown because user explicitly asked for images');\n      return true;\n    }\n    \n    // NEVER show if user asked for table data (they want the actual data, not images)\n    if (askedForTableData && !askedForImages) {\n      console.log('🔍 DEBUG: Visual content hidden because user asked for table data');\n      return false;\n    }\n    \n    // For other cases, show only if answer doesn't already provide adequate information\n    const shouldShow = !hasTableInAnswer && // Don't show if answer already has table data\n                      !hasImageDescription && // Don't show if answer already describes images well\n                      (\n                        (askedForImages && !answerHasContent) || // Show images if user asked and answer is short\n                        (!answerHasContent && answerLower.length < 50) // Show for very short answers\n                      );\n    \n    console.log('🔍 DEBUG: Visual content decision for regular query:', shouldShow);\n    return shouldShow;\n  };\n\n  // Helper function to filter visual content types based on user query\n  const filterVisualContent = (source: Source | string, userQuestion: string) => {\n    if (typeof source !== 'object' || !source.visual_content) {\n      console.log('🔍 DEBUG: Filtering out - not object or no visual_content:', typeof source, !!(source as Source)?.visual_content);\n      return false;\n    }\n    \n    const userQueryLower = userQuestion.toLowerCase();\n    const sourceObj = source as Source;\n    \n    console.log('🔍 DEBUG: Checking visual content filter for:', {\n      content_type: sourceObj.content_type,\n      has_visual_content: !!sourceObj.visual_content,\n      user_query: userQueryLower,\n      filename: sourceObj.filename,\n      page: sourceObj.page,\n      visual_content_sample: sourceObj.visual_content ? JSON.stringify(sourceObj.visual_content).substring(0, 200) : 'none'\n    });\n    \n    // Extract specific project/quotation numbers from user query\n    const projectMatch = userQueryLower.match(/project\\s*(\\d+)/);\n    const quotationMatch = userQueryLower.match(/quotation\\s*(\\d+)/);\n    \n    // Extract company names for logo queries\n    const logoQueryPatterns = [\n      /(?:logo\\s+(?:of\\s+)?|show\\s+me\\s+(?:the\\s+)?logo\\s+(?:of\\s+)?)([A-Z][A-Za-z\\s&]+?)(?:\\s+logo|\\s+enterprises|\\s+company|\\s+corp|\\s+ltd|\\s+inc|\\s*$)/i,\n      /([A-Z][A-Za-z\\s&]+?)\\s+(?:enterprises|company|corp|ltd|inc)\\s+logo/i,\n      /([A-Z][A-Za-z\\s&]+?)\\s+logo/i,\n    ];\n    \n    let companyMatch = null;\n    for (const pattern of logoQueryPatterns) {\n      const match = userQuestion.match(pattern);\n      if (match) {\n        companyMatch = match[1].trim();\n        break;\n      }\n    }\n    \n    // Check if the visual content contains relevant information for the specific request\n    const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();\n    \n    // If user asked for a specific company logo\n    if (companyMatch && userQueryLower.includes('logo')) {\n      const companyNameLower = companyMatch.toLowerCase();\n      const isImage = sourceObj.content_type === 'image';\n      \n      console.log('🔍 DEBUG: Company logo search:', {\n        company: companyMatch,\n        isImage,\n        has_detected_companies: !!(sourceObj.visual_content?.detected_companies),\n        detected_companies: sourceObj.visual_content?.detected_companies || [],\n        ocr_text: sourceObj.visual_content?.ocr_text || '',\n        is_logo: sourceObj.visual_content?.is_logo || false\n      });\n      \n      if (isImage) {\n        // Check if this image contains the requested company\n        const hasCompanyInContent = visualContentStr.includes(companyNameLower);\n        const hasCompanyInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(companyNameLower);\n        \n        // Check detected companies from OCR\n        const detectedCompanies: string[] = sourceObj.visual_content?.detected_companies || [];\n        const hasDetectedCompany = detectedCompanies.some((company: string) =>\n          company.toLowerCase().includes(companyNameLower) ||\n          companyNameLower.includes(company.toLowerCase())\n        );\n        \n        // Check OCR text\n        const ocrText = sourceObj.visual_content?.ocr_text || '';\n        const hasOcrMatch = ocrText.toLowerCase().includes(companyNameLower);\n        \n        // Check if marked as logo\n        const isLogo = sourceObj.visual_content?.is_logo || false;\n        \n        const isRelevantLogo = hasCompanyInContent || hasCompanyInFilename || hasDetectedCompany || hasOcrMatch || isLogo;\n        \n        console.log('🔍 DEBUG: Company logo filter result:', {\n          company: companyMatch,\n          hasCompanyInContent,\n          hasCompanyInFilename,\n          hasDetectedCompany,\n          hasOcrMatch,\n          isLogo,\n          isRelevantLogo,\n          should_show: isImage && isRelevantLogo\n        });\n        \n        return isRelevantLogo;\n      }\n      \n      return false; // Not an image for logo query\n    }\n    \n    // If user asked for a specific project image\n    if (projectMatch && userQueryLower.includes('image')) {\n      const projectNumber = projectMatch[1];\n      const isImage = sourceObj.content_type === 'image';\n      \n      // Check if this image is related to the specific project\n      const hasProjectInContent = visualContentStr.includes(`project ${projectNumber}`) || \n                                  visualContentStr.includes(`project${projectNumber}`);\n      const hasProjectInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(`project ${projectNumber}`);\n      const isOnProjectPage = sourceObj.page && Math.abs(sourceObj.page - parseInt(projectNumber)) <= 1; // Allow adjacent pages\n      const hasProjectContext = sourceObj.visual_content?.project_context && \n                               sourceObj.visual_content.project_context.includes(`project ${projectNumber}`);\n      \n      const isRelevantProject = hasProjectInContent || hasProjectInFilename || isOnProjectPage || hasProjectContext;\n      \n      console.log('🔍 DEBUG: Project', projectNumber, 'image filter:', {\n        isImage,\n        hasProjectInContent,\n        hasProjectInFilename,\n        isOnProjectPage,\n        hasProjectContext,\n        isRelevantProject,\n        visualContentStr_sample: visualContentStr.substring(0, 150),\n        project_context: sourceObj.visual_content?.project_context,\n        page: sourceObj.page,\n        should_show: isImage && isRelevantProject\n      });\n      \n      // If this is an image and it's relevant to the project, show it\n      // If no images are relevant, we'll fall back to showing all images\n      return isImage && isRelevantProject;\n    }\n    \n    // If user asked for a specific quotation table\n    if (quotationMatch && userQueryLower.includes('table')) {\n      const quotationNumber = quotationMatch[1];\n      const isTable = sourceObj.content_type === 'table';\n      \n      // Check if this table is related to the specific quotation\n      const isRelevantQuotation = visualContentStr.includes(`quotation ${quotationNumber}`) || \n                                  visualContentStr.includes(`quotation${quotationNumber}`) ||\n                                  visualContentStr.includes(`quote ${quotationNumber}`);\n      \n      console.log('🔍 DEBUG: Quotation', quotationNumber, 'table filter:', {\n        isTable,\n        isRelevantQuotation,\n        should_show: isTable && isRelevantQuotation\n      });\n      \n      return isTable && isRelevantQuotation;\n    }\n    \n    // If user specifically asked for images (but no specific project), only show images\n    if (userQueryLower.includes('image') && !userQueryLower.includes('table')) {\n      const isImage = sourceObj.content_type === 'image';\n      console.log('🔍 DEBUG: User asked for images only, source is image:', isImage);\n      return isImage;\n    }\n    \n    // If user specifically asked for tables (but no specific quotation), only show tables\n    if (userQueryLower.includes('table') && !userQueryLower.includes('image')) {\n      const isTable = sourceObj.content_type === 'table';\n      console.log('🔍 DEBUG: User asked for tables only, source is table:', isTable);\n      return isTable;\n    }\n    \n    // Default: show all visual content\n    console.log('🔍 DEBUG: Showing all visual content by default');\n    return true;\n  };\n\n  // Handle command shortcuts in the textbox\n  const handleCommandShortcut = (input: string) => {\n    // Check if the input is a command\n    if (input.startsWith('/')) {\n      const command = input.split(' ')[0].toLowerCase();\n\n      // Command: /model <model-name>\n      if (command === '/model') {\n        const modelArg = input.substring(7).trim();\n        const matchedModel = DEFAULT_LLM_MODELS.find(m =>\n          m.name.toLowerCase().includes(modelArg.toLowerCase()) ||\n          m.id.toLowerCase().includes(modelArg.toLowerCase())\n        );\n\n        if (matchedModel && matchedModel.enabled) {\n          setActiveLLMModel(matchedModel.id);\n          setInput('');\n          return 'processed';\n        }\n      }\n\n      // Command: /reset or /clear - clear chat history\n      else if (command === '/reset' || command === '/clear') {\n        clearCurrentChat();\n        setInput('');\n        return 'processed';\n      }\n    }\n\n    return 'not_processed';\n  };\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isSubmitting) return;\n\n    // Handle command shortcuts like /reset, /model, etc.\n    if (input.startsWith('/')) {\n      const result = handleCommandShortcut(input);\n      if (result === 'processed') {\n        setInput('');\n        return;\n      }\n      // If not processed as a command, continue as a regular message\n    }\n\n    return await sendUserMessage(input);\n  };\n\n  const sendUserMessage = async (messageText: string) => {\n    // Create new chat session if none exists\n    if (!currentSession) {\n      await createNewChat();\n    }\n\n    const userMessage: ChatMessage = {\n      id: `user-${Date.now()}`,\n      content: messageText,\n      sender: 'user',\n      timestamp: new Date().toISOString(),\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(userMessage);\n    setInput('');\n\n    const messageId = Date.now();\n    const tempAiMessage: ChatMessage = {\n      id: `ai-${messageId}`,\n      content: '',\n      sender: 'ai',\n      loading: true,\n      timestamp: new Date().toISOString(),\n      llm_model: activeLLMModel,\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(tempAiMessage);\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n    setIsSubmitting(true);\n    setShowTrainLoader(true);\n    setCurrentSearchStage('initializing');\n\n    try {\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n      // Simulate search progress updates with train loader\n      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);\n      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);\n      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);\n\n      const response = await sendQuery(messageText, activeLLMModel);\n\n      // Create the AI message based on strict priority logic\n      const aiMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: response.answer,\n        document_answer: response.document_answer,\n        website_answer: response.website_answer,\n        llm_model: response.llm_model || activeLLMModel,\n        sender: 'ai',\n        sources: response.sources,\n        document_sources: response.document_sources,\n        website_sources: response.website_sources,\n        chatId: currentSession?.id || 'temp',\n        llm_fallback: response.llm_fallback,\n      };\n\n      // Debug logging\n      console.log('AI Message created:', {\n        document_answer: response.document_answer ? 'EXISTS' : 'MISSING',\n        website_answer: response.website_answer ? 'EXISTS' : 'MISSING',\n        document_sources_count: response.document_sources?.length || 0,\n        website_sources_count: response.website_sources?.length || 0,\n        llm_fallback: response.llm_fallback\n      });\n\n      // Debug visual content sources\n      console.log('🔍 DEBUG: Document sources from API:', response.document_sources);\n      if (response.document_sources) {\n        response.document_sources.forEach((source, index) => {\n          console.log(`🔍 DEBUG: Source ${index}:`, {\n            content_type: source.content_type,\n            has_visual_content: !!source.visual_content,\n            storage_url: source.storage_url,\n            display_type: source.display_type,\n            visual_content_keys: source.visual_content ? Object.keys(source.visual_content) : []\n          });\n        });\n      }\n\n      updateMessage(tempAiMessage.id, aiMessage);\n\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      // Provide more helpful error message without the specific query\n      const errorMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,\n        sender: 'ai',\n        chatId: currentSession?.id || 'temp',\n        llm_fallback: true,\n      };\n\n      updateMessage(tempAiMessage.id, errorMessage);\n    } finally {\n      setIsSubmitting(false);\n      setShowTrainLoader(false);\n      setCurrentSearchStage('complete');\n    }\n  };\n\n\n\n  const processDocumentSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Group by filename to avoid repetition\n    const groupedSources: { [key: string]: { filename: string; pages: number[] } } = {};\n\n    sources.forEach(source => {\n      let filename: string;\n      let page: number;\n\n      if (typeof source === 'string') {\n        // Parse string format like \"MaintenanceManual.pdf – Page 3\"\n        const match = source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);\n        filename = match ? match[1].trim() : source;\n        page = match && match[2] ? parseInt(match[2]) : 1;\n      } else {\n        filename = source.name || source.filename || \"Unknown Document\";\n        page = source.page || 1;\n        }\n\n      if (!groupedSources[filename]) {\n        groupedSources[filename] = { filename, pages: [] };\n      }\n\n      if (!groupedSources[filename].pages.includes(page)) {\n        groupedSources[filename].pages.push(page);\n      }\n    });\n\n    // Convert to display format with viewer links\n    return Object.values(groupedSources).map(group => {\n      const sortedPages = group.pages.sort((a, b) => a - b);\n      const pageText = sortedPages.length === 1\n        ? `Page ${sortedPages[0]}`\n        : `Pages ${sortedPages.join(', ')}`;\n\n      // Create link for document viewer that opens at exact page number\n      return {\n        text: `${group.filename} – ${pageText}`,\n        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,\n        isDocument: true\n      };\n    });\n  };\n\n  const processWebsiteSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Remove duplicates and format\n    const uniqueUrls = new Set<string>();\n    const processed: Array<{ text: string; link: string; isDocument?: boolean }> = [];\n\n    sources.forEach(source => {\n      let url: string;\n      let displayText: string;\n\n      if (typeof source === 'string') {\n        url = source.startsWith('http') ? source : `https://${source}`;\n          try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n          } catch {\n          displayText = source;\n        }\n      } else {\n        url = source.url || 'https://railgpt.indianrailways.gov.in';\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = url;\n        }\n      }\n\n      if (!uniqueUrls.has(url)) {\n        uniqueUrls.add(url);\n        processed.push({\n          text: displayText,\n          link: url,\n          isDocument: false  // Mark as website source\n        });\n        }\n    });\n\n    return processed; // Return all website sources\n  };\n\n  // Component for expandable source list with appropriate click behaviors\n  const SourceList = ({ items, maxVisible = 3 }: {\n    items: Array<{ text: string; link?: string; isDocument?: boolean }>;\n    maxVisible?: number\n  }) => {\n    const [expanded, setExpanded] = useState(items.length <= maxVisible);\n\n    if (items.length === 0) return null;\n\n    const visibleItems = expanded ? items : items.slice(0, maxVisible);\n    const hasMore = items.length > maxVisible;\n\n    const handleDocumentClick = (e: React.MouseEvent<HTMLAnchorElement>, link: string) => {\n      // If we want to handle document links in a special way, we can do so here\n      // For example, we could open a modal or new tab with the document viewer\n      // Currently, just allowing regular link behavior\n    };\n\n    return (\n      <ul className=\"text-xs list-disc pl-4 mt-1 space-y-1\">\n        {visibleItems.map((item, index) => (\n              <li key={index}>\n                {item.link ? (\n              <a\n                href={item.link}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"hover:underline text-blue-600 transition-colors duration-200\"\n                title={item.isDocument ? \"Open document at this page\" : \"Open website in new tab\"}\n                onClick={item.isDocument ? (e) => handleDocumentClick(e, item.link!) : undefined}\n              >\n                    {item.text}\n                  </a>\n                ) : (\n              <span className=\"text-gray-700\">{item.text}</span>\n                )}\n              </li>\n            ))}\n        {hasMore && !expanded && (\n          <li className=\"list-none\">\n            <button\n              onClick={() => setExpanded(true)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n            >\n              + {items.length - maxVisible} more sources\n            </button>\n          </li>\n        )}\n        {hasMore && expanded && (\n          <li className=\"list-none\">\n              <button\n                onClick={() => setExpanded(false)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n              >\n                Show less\n              </button>\n            </li>\n                )}\n      </ul>\n    );\n  };\n\n  // Sidebar handlers\n  const handleChatSelect = async (chatSession: ChatSession) => {\n    // Load the selected chat session using context\n    await loadChatSession(chatSession.id);\n    setActiveLLMModel(chatSession.model_used || 'gemini-2.0-flash');\n    setSidebarOpen(false); // Close sidebar on mobile after selection\n  };\n\n  const handleNewChat = async () => {\n    console.log('Creating new chat...');\n    await createNewChat();\n    setSidebarOpen(false); // Close sidebar on mobile after creating new chat\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-100 transition-colors duration-300\">\n      {/* Train Loader Overlay */}\n      <TrainLoader\n        isVisible={showTrainLoader}\n        message={(() => {\n          switch (currentSearchStage) {\n            case 'searching_documents':\n              return \"RailGPT Searching in Documents...\";\n            case 'searching_websites':\n              return \"RailGPT Searching in Websites...\";\n            case 'generating_answer':\n              return \"RailGPT Generating Response...\";\n            default:\n              return \"RailGPT Processing Your Query...\";\n          }\n        })()}\n        trainType=\"express\"\n        currentStage={currentSearchStage}\n        sidebarOpen={sidebarOpen}\n      />\n\n      {/* Chat Sidebar */}\n      <ChatSidebar\n        isOpen={sidebarOpen}\n        onToggle={() => setSidebarOpen(!sidebarOpen)}\n        currentChatId={currentSession?.id || ''}\n        onChatSelect={handleChatSelect}\n        onNewChat={handleNewChat}\n      />\n\n      {/* Main Chat Area */}\n      <div className={`flex flex-col flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-80' : ''}`}>\n        {/* Message Area - only scrollable when messages exist */}\n        <div className={`flex-1 ${messages.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-4 pb-32`}>\n          {messages.length === 0 ? (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center text-gray-500\">\n                <p className=\"text-xl font-semibold mb-3\">Welcome to RailGPT!</p>\n                <p>Ask questions about Indian Railways...</p>\n              </div>\n            </div>\n          ) : (\n            <div>\n              {messages.map((message) => (\n                <div\n                  key={message.id}\n                  className={`mb-4 ${\n                    message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'\n                  }`}\n                >\n                  <div\n                    className={`max-w-4xl rounded-lg p-4 transition-colors duration-300 ${\n                      message.sender === 'user'\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-white text-gray-800 shadow-md'\n                    }`}\n                  >\n                    <div className=\"flex justify-between items-start mb-1\">\n                      <span className=\"font-semibold\">\n                        {message.sender === 'user' ? 'You' : 'RailGPT'}\n                      </span>\n                      {message.timestamp && (\n                        <span className={`text-xs ml-2 ${\n                          message.sender === 'user'\n                            ? 'text-blue-100'\n                            : 'text-gray-500'\n                        }`}>\n                          {new Date(message.timestamp).toLocaleTimeString()}\n                        </span>\n                      )}\n                    </div>\n\n                    {/* Only show content directly for user messages */}\n                    {message.sender === 'user' && message.content && (\n                      <div className=\"mt-2 whitespace-pre-wrap\">{message.content}</div>\n                    )}\n\n                    {/* AI messages with strict priority display logic */}\n                    {message.sender === 'ai' && (\n                      <div>\n                        {(() => {\n                          // Only hide if this specific message is loading AND has no content yet\n                          if (message.loading && showTrainLoader && !message.content && !message.document_answer && !message.website_answer) {\n                            return null;\n                          }\n\n                          // Process sources with improved deduplication\n                          const documentSourceItems = processDocumentSources(message.document_sources);\n                          const websiteSourceItems = processWebsiteSources(message.website_sources);\n\n                          // Check what content is available for conditional display\n                          const hasDocumentContent = !!(message.document_answer && message.document_answer.trim() !== \"\");\n                          const hasWebsiteContent = !!(message.website_answer && message.website_answer.trim() !== \"\");\n                          // LLM fallback happens if NEITHER document sources NOR website sources are found, or if explicitly set\n                          const hasLLMFallback = (!hasDocumentContent && !hasWebsiteContent) || message.llm_fallback;\n\n                          // Debug logging for rendering\n                          console.log(`🔍 Rendering message ${message.id}:`, {\n                            hasDocumentContent,\n                            hasWebsiteContent,\n                            hasLLMFallback,\n                            documentAnswerLength: message.document_answer?.length || 0,\n                            websiteAnswerLength: message.website_answer?.length || 0,\n                            documentSourcesCount: documentSourceItems.length,\n                            websiteSourcesCount: websiteSourceItems.length,\n                            rawDocumentAnswer: message.document_answer ? 'EXISTS' : 'MISSING',\n                            rawWebsiteAnswer: message.website_answer ? 'EXISTS' : 'MISSING',\n                            rawDocumentSources: message.document_sources?.length || 0,\n                            rawWebsiteSources: message.website_sources?.length || 0\n                          });\n\n                          // Get the user's question for context - find the most recent user message before this AI message\n                          const currentMessageIndex = messages.findIndex(m => m.id === message.id);\n                          let userQuestion = '';\n                          \n                          // Look backwards from current AI message to find the most recent user message\n                          for (let i = currentMessageIndex - 1; i >= 0; i--) {\n                            if (messages[i].sender === 'user' && messages[i].content) {\n                              userQuestion = messages[i].content;\n                              break;\n                            }\n                          }\n                          \n                          console.log('🔍 DEBUG: Found user question for AI message:', { aiMessageId: message.id, userQuestion });\n\n                          // Conditional display logic based on answer sources\n                          const components = [];\n                          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                          let answerSource = '';\n\n                          // Case 1: Answer exists from both uploaded documents and websites\n                          if (hasDocumentContent && hasWebsiteContent) {\n                            answerSource = 'document_and_website';\n\n                            // Document answer card\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const documentName = documentSourceItems.length === 1\n                              ? documentSourceItems[0].text.split(' – ')[0]\n                              : 'Uploaded Documents';\n\n                            components.push(\n                              <div key=\"document-card\" className=\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-blue-800 text-sm mb-3 flex items-center\">\n                                  📄 Answer from {documentName}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.document_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                \n                                {/* Display Visual Content with Smart Fallback */}\n                                {(() => {\n                                  const visualSources = message.document_sources?.filter(source => \n                                    typeof source === 'object' && source.visual_content\n                                  ) || [];\n                                  \n                                  // First try to find sources that match the user's specific request\n                                  let relevantSources = visualSources.filter(source => \n                                    filterVisualContent(source as Source, userQuestion)\n                                  );\n                                  \n                                  // If no specific matches and user asked for images, show any available images\n                                  if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                                    relevantSources = visualSources.filter(source => \n                                      (source as Source).content_type === 'image'\n                                    );\n                                    console.log('🔍 DEBUG: No specific project matches, showing all available images:', relevantSources.length);\n                                  }\n                                  \n                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (\n                                    <div className=\"mt-4\">\n                                      <h5 className=\"text-sm font-semibold text-blue-800 mb-3\">📊 Visual Content:</h5>\n                                      <div className=\"space-y-3\">\n                                        {relevantSources.map((source, index) => (\n                                          <VisualContent key={index} source={source as Source} />\n                                        ))}\n                                      </div>\n                                    </div>\n                                  ) : null;\n                                })()}\n                                \n                                <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                                  <p className=\"text-xs text-blue-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={documentSourceItems} maxVisible={5} />\n                                </div>\n                              </div>\n                            );\n\n                            // Website answer card\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n\n                            components.push(\n                              <div key=\"website-card\" className=\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-green-800 text-sm mb-3 flex items-center\">\n                                  🌐 Answer from {websiteLabel}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.website_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-green-200\">\n                                  <p className=\"text-xs text-green-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={websiteSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 2: Answer exists only from documents\n                          } else if (hasDocumentContent) {\n                            answerSource = 'document_only';\n\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const documentName = documentSourceItems.length === 1\n                              ? documentSourceItems[0].text.split(' – ')[0]\n                              : 'Uploaded Documents';\n\n                            components.push(\n                              <div key=\"document-priority\" className=\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-blue-800 text-sm mb-3 flex items-center\">\n                                  📄 Answer from Uploaded Documents\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.document_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                \n                                {/* Display Visual Content with Smart Fallback */}\n                                {(() => {\n                                  const visualSources = message.document_sources?.filter(source => \n                                    typeof source === 'object' && source.visual_content\n                                  ) || [];\n                                  \n                                  // First try to find sources that match the user's specific request\n                                  let relevantSources = visualSources.filter(source => \n                                    filterVisualContent(source as Source, userQuestion)\n                                  );\n                                  \n                                  // If no specific matches and user asked for images, show any available images\n                                  if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                                    relevantSources = visualSources.filter(source => \n                                      (source as Source).content_type === 'image'\n                                    );\n                                    console.log('🔍 DEBUG: No specific project matches for document-only case, showing all available images:', relevantSources.length);\n                                  }\n                                  \n                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (\n                                    <div className=\"mt-4\">\n                                      <h5 className=\"text-sm font-semibold text-blue-800 mb-3\">📊 Visual Content:</h5>\n                                      <div className=\"space-y-3\">\n                                        {relevantSources.map((source, index) => (\n                                          <VisualContent key={index} source={source as Source} />\n                                        ))}\n                                      </div>\n                                    </div>\n                                  ) : null;\n                                })()}\n                                \n                                <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                                  <p className=\"text-xs text-blue-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={documentSourceItems} maxVisible={5} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 3: Answer exists only from websites\n                          } else if (hasWebsiteContent) {\n                            answerSource = 'website_only';\n\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n\n                            components.push(\n                              <div key=\"website-priority\" className=\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-green-800 text-sm mb-3 flex items-center\">\n                                  🌐 Answer from Extracted Websites\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.website_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-green-200\">\n                                  <p className=\"text-xs text-green-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={websiteSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 4: No answer from either - fallback to LLM\n                          } else if (hasLLMFallback) {\n                            answerSource = 'llm';\n\n                            const modelName = message.llm_model || 'Gemini';\n                            const modelLogo = message.llm_model?.includes('chatgpt') ? '🤖' :\n                                            message.llm_model?.includes('groq') ? '⚡' :\n                                            message.llm_model?.includes('deepseek') ? '🔍' :\n                                            message.llm_model?.includes('qwen') ? '🌐' :\n                                            message.llm_model?.includes('ollama') ? '🏠' :\n                                            message.llm_model?.includes('huggingface') ? '🤗' : '🧠';\n\n                            // Only show the LLM fallback card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"llm-fallback\" className=\"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-purple-800 text-sm mb-3 flex items-center\">\n                                  {modelLogo} Answer generated by {modelName}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't find any relevant information to answer your question.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Gemini'}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-purple-200\">\n                                  <p className=\"text-xs text-purple-600 italic\">\n                                    This answer was generated by an AI model as no relevant information was found in your documents or websites.\n                                  </p>\n                                </div>\n                              </div>\n                            );\n                            }\n\n                          // Case 5: No sources found and fallback disabled (or similar edge case)\n                          } else {\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            answerSource = 'no_results';\n\n                            // Only show the \"no results\" card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"no-results\" className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                                <p className=\"text-sm text-gray-600 mb-2\">No sources found</p>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Gemini'}\n                                  chatId={message.chatId}\n                                />\n                              </div>\n                            );\n                            }\n                          }\n\n                          // If we have components to display, render them\n                          if (components.length > 0) {\n                            return (\n                              <div className=\"mt-3\">\n                                {components}\n                              </div>\n                            );\n                          }\n\n                          // Fallback for any unhandled edge cases (should rarely happen)\n                          console.warn(\"Frontend: Unhandled rendering case\");\n                          return (\n                            <div className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                              <p className=\"text-sm text-gray-600 mb-2\">Rendering Error</p>\n                              <InteractiveAnswer\n                                content={message.content || \"An error occurred while rendering the response.\"}\n                                query={userQuestion}\n                                model={message.llm_model || 'Gemini'}\n                                chatId={message.chatId}\n                              />\n                            </div>\n                          );\n                        })()}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n              <div ref={messagesEndRef} style={{ float: 'left', clear: 'both' }} />\n            </div>\n          )}\n        </div>\n\n        {/* Fixed Chat Input Box at Bottom - using fixed positioning */}\n        <div className={`border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen ? 'lg:left-80 left-0' : 'left-0'}`}>\n          <form onSubmit={handleSendMessage} className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 relative flex\">\n              <input\n                type=\"text\"\n                value={input}\n                onChange={(e) => {\n                  const newValue = e.target.value;\n                  setInput(newValue);\n                  // Don't handle command shortcuts as you type, only on submit\n                }}\n                placeholder=\"Type your message... (/model, /reset, /clear)\"\n                className=\"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\n                disabled={isSubmitting}\n                aria-label=\"Message input\"\n              />\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <LLMSelector\n                currentModel={activeLLMModel}\n                onModelChange={(modelId) => setActiveLLMModel(modelId)}\n                isLoading={isSubmitting}\n              />\n              <button\n                type=\"submit\"\n                disabled={isSubmitting || !input.trim()}\n                className=\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\"\n                title=\"Send message\"\n              >\n                <span>{isSubmitting ? \"Sending...\" : \"Send\"}</span>\n              </button>\n            </div>\n          </form>\n                      <div className=\"text-xs text-gray-400 mt-1 text-center\">\n            Current model: {DEFAULT_LLM_MODELS.find(m => m.id === activeLLMModel)?.name || activeLLMModel}\n          </div>\n        </div>\n        {/* Spacer div to push content up above the fixed input box - only needed when there are messages */}\n        {messages.length > 0 && <div className=\"h-36\"></div>}\n      </div>\n    </div>\n  );\n}\n\ninterface AppProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction App({ sidebarOpen, setSidebarOpen }: AppProps) {\n  return (\n    <ChatInterface sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,WAAW;AAClB,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,WAAW,IAAIC,kBAAkB,QAAQ,6BAA6B;AAC7E,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,cAAc,QAAQ,wBAAwB;;AAiBvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,SAASC,aAAaA,CAAC;EAAEC,WAAW;EAAEC;AAAmC,CAAC,EAAE;EAAA,IAAAC,qBAAA;EAC1E,MAAM;IACJC,cAAc;IACdC,QAAQ;IACRC,aAAa;IACbC,eAAe;IACfC,UAAU;IACVC,aAAa;IACbC;EACF,CAAC,GAAGb,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC;EAC1E,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAmG,cAAc,CAAC;EAC9K,MAAMkC,cAAc,GAAGjC,MAAM,CAAiB,IAAI,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgC,cAAc,CAACC,OAAO,EAAE;MAC1BD,cAAc,CAACC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAACnB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMoB,uBAAuB,GAAGA,CAACC,YAAoB,EAAEC,cAAsB,KAAc;IACzF,MAAMC,SAAS,GAAGF,YAAY,CAACG,WAAW,CAAC,CAAC;;IAE5C;IACA,IAAID,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC/B,OAAO,IAAI;IACb;;IAEA;IACA,MAAMC,cAAc,GAAGH,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC;IACnJ,MAAME,iBAAiB,GAAG,CAACJ,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC,MAC/DF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC7J,MAAMG,iBAAiB,GAAGL,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC;IAEvI,MAAMI,WAAW,GAAG,CAAAP,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEE,WAAW,CAAC,CAAC,KAAI,EAAE;IACvD,MAAMM,gBAAgB,GAAGD,WAAW,CAACJ,QAAQ,CAAC,SAAS,CAAC,IAAII,WAAW,CAACJ,QAAQ,CAAC,GAAG,CAAC,IAC5DI,WAAW,CAACJ,QAAQ,CAAC,OAAO,CAAC,IAAII,WAAW,CAACE,MAAM,GAAG,GAAI;IACnF,MAAMC,mBAAmB,GAAGH,WAAW,CAACJ,QAAQ,CAAC,OAAO,CAAC,IAAII,WAAW,CAACJ,QAAQ,CAAC,QAAQ,CAAC,IAAII,WAAW,CAACJ,QAAQ,CAAC,SAAS,CAAC;IAC9H,MAAMQ,gBAAgB,GAAGJ,WAAW,CAACE,MAAM,GAAG,EAAE;;IAEhD;IACA,IAAIH,iBAAiB,IAAKF,cAAc,KAAKH,SAAS,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAE,EAAE;MAC1GS,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;MACtF,OAAO,IAAI;IACb;;IAEA;IACA,IAAIR,iBAAiB,IAAI,CAACD,cAAc,EAAE;MACxCQ,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;MAChF,OAAO,KAAK;IACd;;IAEA;IACA,MAAMC,UAAU,GAAG,CAACN,gBAAgB;IAAI;IACtB,CAACE,mBAAmB;IAAI;;IAErBN,cAAc,IAAI,CAACO,gBAAgB;IAAK;IACxC,CAACA,gBAAgB,IAAIJ,WAAW,CAACE,MAAM,GAAG,EAAG,CAAC;IAAA,CAChD;IAEnBG,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEC,UAAU,CAAC;IAC/E,OAAOA,UAAU;EACnB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAACC,MAAuB,EAAEjB,YAAoB,KAAK;IAC7E,IAAI,OAAOiB,MAAM,KAAK,QAAQ,IAAI,CAACA,MAAM,CAACC,cAAc,EAAE;MACxDL,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE,OAAOG,MAAM,EAAE,CAAC,EAAEA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAaC,cAAc,EAAC;MAC9H,OAAO,KAAK;IACd;IAEA,MAAMC,cAAc,GAAGnB,YAAY,CAACG,WAAW,CAAC,CAAC;IACjD,MAAMiB,SAAS,GAAGH,MAAgB;IAElCJ,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;MAC3DO,YAAY,EAAED,SAAS,CAACC,YAAY;MACpCC,kBAAkB,EAAE,CAAC,CAACF,SAAS,CAACF,cAAc;MAC9CK,UAAU,EAAEJ,cAAc;MAC1BK,QAAQ,EAAEJ,SAAS,CAACI,QAAQ;MAC5BC,IAAI,EAAEL,SAAS,CAACK,IAAI;MACpBC,qBAAqB,EAAEN,SAAS,CAACF,cAAc,GAAGS,IAAI,CAACC,SAAS,CAACR,SAAS,CAACF,cAAc,CAAC,CAACW,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG;IACjH,CAAC,CAAC;;IAEF;IACA,MAAMC,YAAY,GAAGX,cAAc,CAACY,KAAK,CAAC,iBAAiB,CAAC;IAC5D,MAAMC,cAAc,GAAGb,cAAc,CAACY,KAAK,CAAC,mBAAmB,CAAC;;IAEhE;IACA,MAAME,iBAAiB,GAAG,CACxB,qJAAqJ,EACrJ,qEAAqE,EACrE,8BAA8B,CAC/B;IAED,IAAIC,YAAY,GAAG,IAAI;IACvB,KAAK,MAAMC,OAAO,IAAIF,iBAAiB,EAAE;MACvC,MAAMF,KAAK,GAAG/B,YAAY,CAAC+B,KAAK,CAACI,OAAO,CAAC;MACzC,IAAIJ,KAAK,EAAE;QACTG,YAAY,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;QAC9B;MACF;IACF;;IAEA;IACA,MAAMC,gBAAgB,GAAGV,IAAI,CAACC,SAAS,CAACR,SAAS,CAACF,cAAc,IAAI,CAAC,CAAC,CAAC,CAACf,WAAW,CAAC,CAAC;;IAErF;IACA,IAAI+B,YAAY,IAAIf,cAAc,CAACf,QAAQ,CAAC,MAAM,CAAC,EAAE;MAAA,IAAAkC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACnD,MAAMC,gBAAgB,GAAGR,YAAY,CAAC/B,WAAW,CAAC,CAAC;MACnD,MAAMwC,OAAO,GAAGvB,SAAS,CAACC,YAAY,KAAK,OAAO;MAElDR,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5C8B,OAAO,EAAEV,YAAY;QACrBS,OAAO;QACPE,sBAAsB,EAAE,CAAC,GAAAP,qBAAA,GAAElB,SAAS,CAACF,cAAc,cAAAoB,qBAAA,eAAxBA,qBAAA,CAA0BQ,kBAAkB,CAAC;QACxEA,kBAAkB,EAAE,EAAAP,sBAAA,GAAAnB,SAAS,CAACF,cAAc,cAAAqB,sBAAA,uBAAxBA,sBAAA,CAA0BO,kBAAkB,KAAI,EAAE;QACtEC,QAAQ,EAAE,EAAAP,sBAAA,GAAApB,SAAS,CAACF,cAAc,cAAAsB,sBAAA,uBAAxBA,sBAAA,CAA0BO,QAAQ,KAAI,EAAE;QAClDC,OAAO,EAAE,EAAAP,sBAAA,GAAArB,SAAS,CAACF,cAAc,cAAAuB,sBAAA,uBAAxBA,sBAAA,CAA0BO,OAAO,KAAI;MAChD,CAAC,CAAC;MAEF,IAAIL,OAAO,EAAE;QAAA,IAAAM,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACX;QACA,MAAMC,mBAAmB,GAAGf,gBAAgB,CAACjC,QAAQ,CAACsC,gBAAgB,CAAC;QACvE,MAAMW,oBAAoB,GAAGjC,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACsC,gBAAgB,CAAC;;QAE9G;QACA,MAAMY,iBAA2B,GAAG,EAAAL,sBAAA,GAAA7B,SAAS,CAACF,cAAc,cAAA+B,sBAAA,uBAAxBA,sBAAA,CAA0BH,kBAAkB,KAAI,EAAE;QACtF,MAAMS,kBAAkB,GAAGD,iBAAiB,CAACE,IAAI,CAAEZ,OAAe,IAChEA,OAAO,CAACzC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACsC,gBAAgB,CAAC,IAChDA,gBAAgB,CAACtC,QAAQ,CAACwC,OAAO,CAACzC,WAAW,CAAC,CAAC,CACjD,CAAC;;QAED;QACA,MAAMsD,OAAO,GAAG,EAAAP,sBAAA,GAAA9B,SAAS,CAACF,cAAc,cAAAgC,sBAAA,uBAAxBA,sBAAA,CAA0BH,QAAQ,KAAI,EAAE;QACxD,MAAMW,WAAW,GAAGD,OAAO,CAACtD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACsC,gBAAgB,CAAC;;QAEpE;QACA,MAAMiB,MAAM,GAAG,EAAAR,sBAAA,GAAA/B,SAAS,CAACF,cAAc,cAAAiC,sBAAA,uBAAxBA,sBAAA,CAA0BH,OAAO,KAAI,KAAK;QAEzD,MAAMY,cAAc,GAAGR,mBAAmB,IAAIC,oBAAoB,IAAIE,kBAAkB,IAAIG,WAAW,IAAIC,MAAM;QAEjH9C,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;UACnD8B,OAAO,EAAEV,YAAY;UACrBkB,mBAAmB;UACnBC,oBAAoB;UACpBE,kBAAkB;UAClBG,WAAW;UACXC,MAAM;UACNC,cAAc;UACdC,WAAW,EAAElB,OAAO,IAAIiB;QAC1B,CAAC,CAAC;QAEF,OAAOA,cAAc;MACvB;MAEA,OAAO,KAAK,CAAC,CAAC;IAChB;;IAEA;IACA,IAAI9B,YAAY,IAAIX,cAAc,CAACf,QAAQ,CAAC,OAAO,CAAC,EAAE;MAAA,IAAA0D,sBAAA,EAAAC,sBAAA;MACpD,MAAMC,aAAa,GAAGlC,YAAY,CAAC,CAAC,CAAC;MACrC,MAAMa,OAAO,GAAGvB,SAAS,CAACC,YAAY,KAAK,OAAO;;MAElD;MACA,MAAM4C,mBAAmB,GAAG5B,gBAAgB,CAACjC,QAAQ,CAAC,WAAW4D,aAAa,EAAE,CAAC,IACrD3B,gBAAgB,CAACjC,QAAQ,CAAC,UAAU4D,aAAa,EAAE,CAAC;MAChF,MAAME,oBAAoB,GAAG9C,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW4D,aAAa,EAAE,CAAC;MACxH,MAAMG,eAAe,GAAG/C,SAAS,CAACK,IAAI,IAAI2C,IAAI,CAACC,GAAG,CAACjD,SAAS,CAACK,IAAI,GAAG6C,QAAQ,CAACN,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;MACnG,MAAMO,iBAAiB,GAAG,EAAAT,sBAAA,GAAA1C,SAAS,CAACF,cAAc,cAAA4C,sBAAA,uBAAxBA,sBAAA,CAA0BU,eAAe,KAC1CpD,SAAS,CAACF,cAAc,CAACsD,eAAe,CAACpE,QAAQ,CAAC,WAAW4D,aAAa,EAAE,CAAC;MAEtG,MAAMS,iBAAiB,GAAGR,mBAAmB,IAAIC,oBAAoB,IAAIC,eAAe,IAAII,iBAAiB;MAE7G1D,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkD,aAAa,EAAE,eAAe,EAAE;QAC/DrB,OAAO;QACPsB,mBAAmB;QACnBC,oBAAoB;QACpBC,eAAe;QACfI,iBAAiB;QACjBE,iBAAiB;QACjBC,uBAAuB,EAAErC,gBAAgB,CAACR,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;QAC3D2C,eAAe,GAAAT,sBAAA,GAAE3C,SAAS,CAACF,cAAc,cAAA6C,sBAAA,uBAAxBA,sBAAA,CAA0BS,eAAe;QAC1D/C,IAAI,EAAEL,SAAS,CAACK,IAAI;QACpBoC,WAAW,EAAElB,OAAO,IAAI8B;MAC1B,CAAC,CAAC;;MAEF;MACA;MACA,OAAO9B,OAAO,IAAI8B,iBAAiB;IACrC;;IAEA;IACA,IAAIzC,cAAc,IAAIb,cAAc,CAACf,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,MAAMuE,eAAe,GAAG3C,cAAc,CAAC,CAAC,CAAC;MACzC,MAAM4C,OAAO,GAAGxD,SAAS,CAACC,YAAY,KAAK,OAAO;;MAElD;MACA,MAAMwD,mBAAmB,GAAGxC,gBAAgB,CAACjC,QAAQ,CAAC,aAAauE,eAAe,EAAE,CAAC,IACzDtC,gBAAgB,CAACjC,QAAQ,CAAC,YAAYuE,eAAe,EAAE,CAAC,IACxDtC,gBAAgB,CAACjC,QAAQ,CAAC,SAASuE,eAAe,EAAE,CAAC;MAEjF9D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6D,eAAe,EAAE,eAAe,EAAE;QACnEC,OAAO;QACPC,mBAAmB;QACnBhB,WAAW,EAAEe,OAAO,IAAIC;MAC1B,CAAC,CAAC;MAEF,OAAOD,OAAO,IAAIC,mBAAmB;IACvC;;IAEA;IACA,IAAI1D,cAAc,CAACf,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACe,cAAc,CAACf,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzE,MAAMuC,OAAO,GAAGvB,SAAS,CAACC,YAAY,KAAK,OAAO;MAClDR,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE6B,OAAO,CAAC;MAC9E,OAAOA,OAAO;IAChB;;IAEA;IACA,IAAIxB,cAAc,CAACf,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACe,cAAc,CAACf,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzE,MAAMwE,OAAO,GAAGxD,SAAS,CAACC,YAAY,KAAK,OAAO;MAClDR,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE8D,OAAO,CAAC;MAC9E,OAAOA,OAAO;IAChB;;IAEA;IACA/D,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9D,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMgE,qBAAqB,GAAI7F,KAAa,IAAK;IAC/C;IACA,IAAIA,KAAK,CAAC8F,UAAU,CAAC,GAAG,CAAC,EAAE;MACzB,MAAMC,OAAO,GAAG/F,KAAK,CAACgG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC9E,WAAW,CAAC,CAAC;;MAEjD;MACA,IAAI6E,OAAO,KAAK,QAAQ,EAAE;QACxB,MAAME,QAAQ,GAAGjG,KAAK,CAAC4C,SAAS,CAAC,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC;QAC1C,MAAM+C,YAAY,GAAGrH,kBAAkB,CAACsH,IAAI,CAACC,CAAC,IAC5CA,CAAC,CAACC,IAAI,CAACnF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC8E,QAAQ,CAAC/E,WAAW,CAAC,CAAC,CAAC,IACrDkF,CAAC,CAACE,EAAE,CAACpF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC8E,QAAQ,CAAC/E,WAAW,CAAC,CAAC,CACpD,CAAC;QAED,IAAIgF,YAAY,IAAIA,YAAY,CAACK,OAAO,EAAE;UACxClG,iBAAiB,CAAC6F,YAAY,CAACI,EAAE,CAAC;UAClCrG,QAAQ,CAAC,EAAE,CAAC;UACZ,OAAO,WAAW;QACpB;MACF;;MAEA;MAAA,KACK,IAAI8F,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;QACrDhG,gBAAgB,CAAC,CAAC;QAClBE,QAAQ,CAAC,EAAE,CAAC;QACZ,OAAO,WAAW;MACpB;IACF;IAEA,OAAO,eAAe;EACxB,CAAC;EAED,MAAMuG,iBAAiB,GAAG,MAAOC,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC1G,KAAK,CAACmD,IAAI,CAAC,CAAC,IAAIjD,YAAY,EAAE;;IAEnC;IACA,IAAIF,KAAK,CAAC8F,UAAU,CAAC,GAAG,CAAC,EAAE;MACzB,MAAMa,MAAM,GAAGd,qBAAqB,CAAC7F,KAAK,CAAC;MAC3C,IAAI2G,MAAM,KAAK,WAAW,EAAE;QAC1B1G,QAAQ,CAAC,EAAE,CAAC;QACZ;MACF;MACA;IACF;IAEA,OAAO,MAAM2G,eAAe,CAAC5G,KAAK,CAAC;EACrC,CAAC;EAED,MAAM4G,eAAe,GAAG,MAAOC,WAAmB,IAAK;IAAA,IAAAC,qBAAA;IACrD;IACA,IAAI,CAACrH,cAAc,EAAE;MACnB,MAAME,aAAa,CAAC,CAAC;IACvB;IAEA,MAAMoH,WAAwB,GAAG;MAC/BT,EAAE,EAAE,QAAQU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACxBC,OAAO,EAAEL,WAAW;MACpBM,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MACnCC,MAAM,EAAE,CAAA7H,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6G,EAAE,KAAI;IAChC,CAAC;IAEDzG,UAAU,CAACkH,WAAW,CAAC;IACvB9G,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMsH,SAAS,GAAGP,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMO,aAA0B,GAAG;MACjClB,EAAE,EAAE,MAAMiB,SAAS,EAAE;MACrBL,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,IAAI;MACZM,OAAO,EAAE,IAAI;MACbL,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MACnCK,SAAS,EAAEtH,cAAc;MACzBkH,MAAM,EAAE,CAAA7H,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6G,EAAE,KAAI;IAChC,CAAC;IAEDzG,UAAU,CAAC2H,aAAa,CAAC;IACzB,CAAAV,qBAAA,GAAApG,cAAc,CAACC,OAAO,cAAAmG,qBAAA,uBAAtBA,qBAAA,CAAwBlG,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;IAE9DV,eAAe,CAAC,IAAI,CAAC;IACrBI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,cAAc,CAAC;IAErC,IAAI;MAAA,IAAAkH,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF,CAAAH,sBAAA,GAAAjH,cAAc,CAACC,OAAO,cAAAgH,sBAAA,uBAAtBA,sBAAA,CAAwB/G,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;;MAE9D;MACAkH,UAAU,CAAC,MAAMtH,qBAAqB,CAAC,qBAAqB,CAAC,EAAE,GAAG,CAAC;MACnEsH,UAAU,CAAC,MAAMtH,qBAAqB,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC;MACnEsH,UAAU,CAAC,MAAMtH,qBAAqB,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC;MAElE,MAAMuH,QAAQ,GAAG,MAAMrJ,SAAS,CAACkI,WAAW,EAAEzG,cAAc,CAAC;;MAE7D;MACA,MAAM6H,SAAsB,GAAG;QAC7B3B,EAAE,EAAE,MAAMiB,SAAS,EAAE;QACrBL,OAAO,EAAEc,QAAQ,CAACE,MAAM;QACxBC,eAAe,EAAEH,QAAQ,CAACG,eAAe;QACzCC,cAAc,EAAEJ,QAAQ,CAACI,cAAc;QACvCV,SAAS,EAAEM,QAAQ,CAACN,SAAS,IAAItH,cAAc;QAC/C+G,MAAM,EAAE,IAAI;QACZkB,OAAO,EAAEL,QAAQ,CAACK,OAAO;QACzBC,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB;QAC3CC,eAAe,EAAEP,QAAQ,CAACO,eAAe;QACzCjB,MAAM,EAAE,CAAA7H,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6G,EAAE,KAAI,MAAM;QACpCkC,YAAY,EAAER,QAAQ,CAACQ;MACzB,CAAC;;MAED;MACA5G,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjCsG,eAAe,EAAEH,QAAQ,CAACG,eAAe,GAAG,QAAQ,GAAG,SAAS;QAChEC,cAAc,EAAEJ,QAAQ,CAACI,cAAc,GAAG,QAAQ,GAAG,SAAS;QAC9DK,sBAAsB,EAAE,EAAAb,qBAAA,GAAAI,QAAQ,CAACM,gBAAgB,cAAAV,qBAAA,uBAAzBA,qBAAA,CAA2BnG,MAAM,KAAI,CAAC;QAC9DiH,qBAAqB,EAAE,EAAAb,qBAAA,GAAAG,QAAQ,CAACO,eAAe,cAAAV,qBAAA,uBAAxBA,qBAAA,CAA0BpG,MAAM,KAAI,CAAC;QAC5D+G,YAAY,EAAER,QAAQ,CAACQ;MACzB,CAAC,CAAC;;MAEF;MACA5G,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEmG,QAAQ,CAACM,gBAAgB,CAAC;MAC9E,IAAIN,QAAQ,CAACM,gBAAgB,EAAE;QAC7BN,QAAQ,CAACM,gBAAgB,CAACK,OAAO,CAAC,CAAC3G,MAAM,EAAE4G,KAAK,KAAK;UACnDhH,OAAO,CAACC,GAAG,CAAC,oBAAoB+G,KAAK,GAAG,EAAE;YACxCxG,YAAY,EAAEJ,MAAM,CAACI,YAAY;YACjCC,kBAAkB,EAAE,CAAC,CAACL,MAAM,CAACC,cAAc;YAC3C4G,WAAW,EAAE7G,MAAM,CAAC6G,WAAW;YAC/BC,YAAY,EAAE9G,MAAM,CAAC8G,YAAY;YACjCC,mBAAmB,EAAE/G,MAAM,CAACC,cAAc,GAAG+G,MAAM,CAACC,IAAI,CAACjH,MAAM,CAACC,cAAc,CAAC,GAAG;UACpF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEAnC,aAAa,CAAC0H,aAAa,CAAClB,EAAE,EAAE2B,SAAS,CAAC;MAE1C,CAAAH,sBAAA,GAAApH,cAAc,CAACC,OAAO,cAAAmH,sBAAA,uBAAtBA,sBAAA,CAAwBlH,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAChE,CAAC,CAAC,OAAOqI,KAAK,EAAE;MACdtH,OAAO,CAACsH,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,MAAMC,YAAyB,GAAG;QAChC7C,EAAE,EAAE,MAAMiB,SAAS,EAAE;QACrBL,OAAO,EAAE,iNAAiN;QAC1NC,MAAM,EAAE,IAAI;QACZG,MAAM,EAAE,CAAA7H,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6G,EAAE,KAAI,MAAM;QACpCkC,YAAY,EAAE;MAChB,CAAC;MAED1I,aAAa,CAAC0H,aAAa,CAAClB,EAAE,EAAE6C,YAAY,CAAC;IAC/C,CAAC,SAAS;MACRhJ,eAAe,CAAC,KAAK,CAAC;MACtBI,kBAAkB,CAAC,KAAK,CAAC;MACzBE,qBAAqB,CAAC,UAAU,CAAC;IACnC;EACF,CAAC;EAID,MAAM2I,sBAAsB,GAAIf,OAAgC,IAAK;IACnE,IAAI,CAACA,OAAO,IAAIA,OAAO,CAAC5G,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;IAE/C;IACA,MAAM4H,cAAwE,GAAG,CAAC,CAAC;IAEnFhB,OAAO,CAACM,OAAO,CAAC3G,MAAM,IAAI;MACxB,IAAIO,QAAgB;MACpB,IAAIC,IAAY;MAEhB,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;QAC9B;QACA,MAAMc,KAAK,GAAGd,MAAM,CAACc,KAAK,CAAC,kCAAkC,CAAC;QAC9DP,QAAQ,GAAGO,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,GAAGnB,MAAM;QAC3CQ,IAAI,GAAGM,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGuC,QAAQ,CAACvC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACnD,CAAC,MAAM;QACLP,QAAQ,GAAGP,MAAM,CAACqE,IAAI,IAAIrE,MAAM,CAACO,QAAQ,IAAI,kBAAkB;QAC/DC,IAAI,GAAGR,MAAM,CAACQ,IAAI,IAAI,CAAC;MACvB;MAEF,IAAI,CAAC6G,cAAc,CAAC9G,QAAQ,CAAC,EAAE;QAC7B8G,cAAc,CAAC9G,QAAQ,CAAC,GAAG;UAAEA,QAAQ;UAAE+G,KAAK,EAAE;QAAG,CAAC;MACpD;MAEA,IAAI,CAACD,cAAc,CAAC9G,QAAQ,CAAC,CAAC+G,KAAK,CAACnI,QAAQ,CAACqB,IAAI,CAAC,EAAE;QAClD6G,cAAc,CAAC9G,QAAQ,CAAC,CAAC+G,KAAK,CAACC,IAAI,CAAC/G,IAAI,CAAC;MAC3C;IACF,CAAC,CAAC;;IAEF;IACA,OAAOwG,MAAM,CAACQ,MAAM,CAACH,cAAc,CAAC,CAACI,GAAG,CAACC,KAAK,IAAI;MAChD,MAAMC,WAAW,GAAGD,KAAK,CAACJ,KAAK,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;MACrD,MAAMC,QAAQ,GAAGJ,WAAW,CAAClI,MAAM,KAAK,CAAC,GACrC,QAAQkI,WAAW,CAAC,CAAC,CAAC,EAAE,GACxB,SAASA,WAAW,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE;;MAErC;MACA,OAAO;QACLC,IAAI,EAAE,GAAGP,KAAK,CAACnH,QAAQ,MAAMwH,QAAQ,EAAE;QACvCG,IAAI,EAAE,gBAAgBC,kBAAkB,CAACT,KAAK,CAACnH,QAAQ,CAAC,SAASoH,WAAW,CAAC,CAAC,CAAC,EAAE;QACjFS,UAAU,EAAE;MACd,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,qBAAqB,GAAIhC,OAAgC,IAAK;IAClE,IAAI,CAACA,OAAO,IAAIA,OAAO,CAAC5G,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;IAE/C;IACA,MAAM6I,UAAU,GAAG,IAAIC,GAAG,CAAS,CAAC;IACpC,MAAMC,SAAsE,GAAG,EAAE;IAEjFnC,OAAO,CAACM,OAAO,CAAC3G,MAAM,IAAI;MACxB,IAAIyI,GAAW;MACf,IAAIC,WAAmB;MAEvB,IAAI,OAAO1I,MAAM,KAAK,QAAQ,EAAE;QAC9ByI,GAAG,GAAGzI,MAAM,CAAC8D,UAAU,CAAC,MAAM,CAAC,GAAG9D,MAAM,GAAG,WAAWA,MAAM,EAAE;QAC5D,IAAI;UACJ,MAAM2I,MAAM,GAAG,IAAIC,GAAG,CAACH,GAAG,CAAC;UAC3BC,WAAW,GAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACnD,CAAC,CAAC,MAAM;UACRJ,WAAW,GAAG1I,MAAM;QACtB;MACF,CAAC,MAAM;QACLyI,GAAG,GAAGzI,MAAM,CAACyI,GAAG,IAAI,uCAAuC;QAC3D,IAAI;UACF,MAAME,MAAM,GAAG,IAAIC,GAAG,CAACH,GAAG,CAAC;UAC3BC,WAAW,GAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACrD,CAAC,CAAC,MAAM;UACNJ,WAAW,GAAGD,GAAG;QACnB;MACF;MAEA,IAAI,CAACH,UAAU,CAACS,GAAG,CAACN,GAAG,CAAC,EAAE;QACxBH,UAAU,CAACU,GAAG,CAACP,GAAG,CAAC;QACnBD,SAAS,CAACjB,IAAI,CAAC;UACbU,IAAI,EAAES,WAAW;UACjBR,IAAI,EAAEO,GAAG;UACTL,UAAU,EAAE,KAAK,CAAE;QACrB,CAAC,CAAC;MACF;IACJ,CAAC,CAAC;IAEF,OAAOI,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMS,UAAU,GAAGA,CAAC;IAAEC,KAAK;IAAEC,UAAU,GAAG;EAG1C,CAAC,KAAK;IACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7M,QAAQ,CAAC0M,KAAK,CAACzJ,MAAM,IAAI0J,UAAU,CAAC;IAEpE,IAAID,KAAK,CAACzJ,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEnC,MAAM6J,YAAY,GAAGF,QAAQ,GAAGF,KAAK,GAAGA,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEJ,UAAU,CAAC;IAClE,MAAMK,OAAO,GAAGN,KAAK,CAACzJ,MAAM,GAAG0J,UAAU;IAEzC,MAAMM,mBAAmB,GAAGA,CAAChF,CAAsC,EAAEyD,IAAY,KAAK;MACpF;MACA;MACA;IAAA,CACD;IAED,oBACE9K,OAAA;MAAIsM,SAAS,EAAC,uCAAuC;MAAAC,QAAA,GAClDL,YAAY,CAAC7B,GAAG,CAAC,CAACmC,IAAI,EAAEhD,KAAK,kBACxBxJ,OAAA;QAAAuM,QAAA,EACGC,IAAI,CAAC1B,IAAI,gBACZ9K,OAAA;UACEyM,IAAI,EAAED,IAAI,CAAC1B,IAAK;UAChB4B,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzBL,SAAS,EAAC,8DAA8D;UACxEM,KAAK,EAAEJ,IAAI,CAACxB,UAAU,GAAG,4BAA4B,GAAG,yBAA0B;UAClF6B,OAAO,EAAEL,IAAI,CAACxB,UAAU,GAAI3D,CAAC,IAAKgF,mBAAmB,CAAChF,CAAC,EAAEmF,IAAI,CAAC1B,IAAK,CAAC,GAAGgC,SAAU;UAAAP,QAAA,EAE5EC,IAAI,CAAC3B;QAAI;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,gBAERlN,OAAA;UAAMsM,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEC,IAAI,CAAC3B;QAAI;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAC9C,GAdM1D,KAAK;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeV,CACL,CAAC,EACLd,OAAO,IAAI,CAACJ,QAAQ,iBACnBhM,OAAA;QAAIsM,SAAS,EAAC,WAAW;QAAAC,QAAA,eACvBvM,OAAA;UACE6M,OAAO,EAAEA,CAAA,KAAMZ,WAAW,CAAC,IAAI,CAAE;UACjCK,SAAS,EAAC,sEAAsE;UAAAC,QAAA,GACjF,IACG,EAACT,KAAK,CAACzJ,MAAM,GAAG0J,UAAU,EAAC,eAC/B;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACL,EACAd,OAAO,IAAIJ,QAAQ,iBAClBhM,OAAA;QAAIsM,SAAS,EAAC,WAAW;QAAAC,QAAA,eACrBvM,OAAA;UACE6M,OAAO,EAAEA,CAAA,KAAMZ,WAAW,CAAC,KAAK,CAAE;UACpCK,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EAC/E;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAET,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAOC,WAAwB,IAAK;IAC3D;IACA,MAAM5M,eAAe,CAAC4M,WAAW,CAAClG,EAAE,CAAC;IACrCjG,iBAAiB,CAACmM,WAAW,CAACC,UAAU,IAAI,kBAAkB,CAAC;IAC/DlN,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAMmN,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC9K,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC,MAAMlC,aAAa,CAAC,CAAC;IACrBJ,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,oBACEH,OAAA;IAAKsM,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBAEvEvM,OAAA,CAACJ,WAAW;MACV2N,SAAS,EAAErM,eAAgB;MAC3BsM,OAAO,EAAE,CAAC,MAAM;QACd,QAAQpM,kBAAkB;UACxB,KAAK,qBAAqB;YACxB,OAAO,mCAAmC;UAC5C,KAAK,oBAAoB;YACvB,OAAO,kCAAkC;UAC3C,KAAK,mBAAmB;YACtB,OAAO,gCAAgC;UACzC;YACE,OAAO,kCAAkC;QAC7C;MACF,CAAC,EAAE,CAAE;MACLqM,SAAS,EAAC,SAAS;MACnBC,YAAY,EAAEtM,kBAAmB;MACjClB,WAAW,EAAEA;IAAY;MAAA6M,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGFlN,OAAA,CAACL,WAAW;MACVgO,MAAM,EAAEzN,WAAY;MACpB0N,QAAQ,EAAEA,CAAA,KAAMzN,cAAc,CAAC,CAACD,WAAW,CAAE;MAC7C2N,aAAa,EAAE,CAAAxN,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6G,EAAE,KAAI,EAAG;MACxC4G,YAAY,EAAEX,gBAAiB;MAC/BY,SAAS,EAAET;IAAc;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGFlN,OAAA;MAAKsM,SAAS,EAAE,oDAAoDpM,WAAW,GAAG,UAAU,GAAG,EAAE,EAAG;MAAAqM,QAAA,gBAElGvM,OAAA;QAAKsM,SAAS,EAAE,UAAUhM,QAAQ,CAAC+B,MAAM,GAAG,CAAC,GAAG,iBAAiB,GAAG,iBAAiB,YAAa;QAAAkK,QAAA,EAC/FjM,QAAQ,CAAC+B,MAAM,KAAK,CAAC,gBACpBrC,OAAA;UAAKsM,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDvM,OAAA;YAAKsM,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCvM,OAAA;cAAGsM,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjElN,OAAA;cAAAuM,QAAA,EAAG;YAAsC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENlN,OAAA;UAAAuM,QAAA,GACGjM,QAAQ,CAAC+J,GAAG,CAAEmD,OAAO,iBACpBxN,OAAA;YAEEsM,SAAS,EAAE,QACTkB,OAAO,CAACzF,MAAM,KAAK,MAAM,GAAG,kBAAkB,GAAG,oBAAoB,EACpE;YAAAwE,QAAA,eAEHvM,OAAA;cACEsM,SAAS,EAAE,2DACTkB,OAAO,CAACzF,MAAM,KAAK,MAAM,GACrB,wBAAwB,GACxB,kCAAkC,EACrC;cAAAwE,QAAA,gBAEHvM,OAAA;gBAAKsM,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDvM,OAAA;kBAAMsM,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAC5BiB,OAAO,CAACzF,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG;gBAAS;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,EACNM,OAAO,CAACxF,SAAS,iBAChBhI,OAAA;kBAAMsM,SAAS,EAAE,gBACfkB,OAAO,CAACzF,MAAM,KAAK,MAAM,GACrB,eAAe,GACf,eAAe,EAClB;kBAAAwE,QAAA,EACA,IAAI3E,IAAI,CAAC4F,OAAO,CAACxF,SAAS,CAAC,CAACgG,kBAAkB,CAAC;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGLM,OAAO,CAACzF,MAAM,KAAK,MAAM,IAAIyF,OAAO,CAAC1F,OAAO,iBAC3C9H,OAAA;gBAAKsM,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEiB,OAAO,CAAC1F;cAAO;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACjE,EAGAM,OAAO,CAACzF,MAAM,KAAK,IAAI,iBACtB/H,OAAA;gBAAAuM,QAAA,EACG,CAAC,CAAA0B,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,KAAM;kBACN;kBACA,IAAIZ,OAAO,CAACnF,OAAO,IAAInH,eAAe,IAAI,CAACsM,OAAO,CAAC1F,OAAO,IAAI,CAAC0F,OAAO,CAACzE,eAAe,IAAI,CAACyE,OAAO,CAACxE,cAAc,EAAE;oBACjH,OAAO,IAAI;kBACb;;kBAEA;kBACA,MAAMqF,mBAAmB,GAAGrE,sBAAsB,CAACwD,OAAO,CAACtE,gBAAgB,CAAC;kBAC5E,MAAMoF,kBAAkB,GAAGrD,qBAAqB,CAACuC,OAAO,CAACrE,eAAe,CAAC;;kBAEzE;kBACA,MAAMoF,kBAAkB,GAAG,CAAC,EAAEf,OAAO,CAACzE,eAAe,IAAIyE,OAAO,CAACzE,eAAe,CAAChF,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;kBAC/F,MAAMyK,iBAAiB,GAAG,CAAC,EAAEhB,OAAO,CAACxE,cAAc,IAAIwE,OAAO,CAACxE,cAAc,CAACjF,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;kBAC5F;kBACA,MAAM0K,cAAc,GAAI,CAACF,kBAAkB,IAAI,CAACC,iBAAiB,IAAKhB,OAAO,CAACpE,YAAY;;kBAE1F;kBACA5G,OAAO,CAACC,GAAG,CAAC,wBAAwB+K,OAAO,CAACtG,EAAE,GAAG,EAAE;oBACjDqH,kBAAkB;oBAClBC,iBAAiB;oBACjBC,cAAc;oBACdC,oBAAoB,EAAE,EAAAT,qBAAA,GAAAT,OAAO,CAACzE,eAAe,cAAAkF,qBAAA,uBAAvBA,qBAAA,CAAyB5L,MAAM,KAAI,CAAC;oBAC1DsM,mBAAmB,EAAE,EAAAT,qBAAA,GAAAV,OAAO,CAACxE,cAAc,cAAAkF,qBAAA,uBAAtBA,qBAAA,CAAwB7L,MAAM,KAAI,CAAC;oBACxDuM,oBAAoB,EAAEP,mBAAmB,CAAChM,MAAM;oBAChDwM,mBAAmB,EAAEP,kBAAkB,CAACjM,MAAM;oBAC9CyM,iBAAiB,EAAEtB,OAAO,CAACzE,eAAe,GAAG,QAAQ,GAAG,SAAS;oBACjEgG,gBAAgB,EAAEvB,OAAO,CAACxE,cAAc,GAAG,QAAQ,GAAG,SAAS;oBAC/DgG,kBAAkB,EAAE,EAAAb,qBAAA,GAAAX,OAAO,CAACtE,gBAAgB,cAAAiF,qBAAA,uBAAxBA,qBAAA,CAA0B9L,MAAM,KAAI,CAAC;oBACzD4M,iBAAiB,EAAE,EAAAb,qBAAA,GAAAZ,OAAO,CAACrE,eAAe,cAAAiF,qBAAA,uBAAvBA,qBAAA,CAAyB/L,MAAM,KAAI;kBACxD,CAAC,CAAC;;kBAEF;kBACA,MAAM6M,mBAAmB,GAAG5O,QAAQ,CAAC6O,SAAS,CAACnI,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKsG,OAAO,CAACtG,EAAE,CAAC;kBACxE,IAAIvF,YAAY,GAAG,EAAE;;kBAErB;kBACA,KAAK,IAAIyN,CAAC,GAAGF,mBAAmB,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;oBACjD,IAAI9O,QAAQ,CAAC8O,CAAC,CAAC,CAACrH,MAAM,KAAK,MAAM,IAAIzH,QAAQ,CAAC8O,CAAC,CAAC,CAACtH,OAAO,EAAE;sBACxDnG,YAAY,GAAGrB,QAAQ,CAAC8O,CAAC,CAAC,CAACtH,OAAO;sBAClC;oBACF;kBACF;kBAEAtF,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;oBAAE4M,WAAW,EAAE7B,OAAO,CAACtG,EAAE;oBAAEvF;kBAAa,CAAC,CAAC;;kBAEvG;kBACA,MAAM2N,UAAU,GAAG,EAAE;kBACrB;kBACA,IAAIC,YAAY,GAAG,EAAE;;kBAErB;kBACA,IAAIhB,kBAAkB,IAAIC,iBAAiB,EAAE;oBAC3Ce,YAAY,GAAG,sBAAsB;;oBAErC;oBACA;oBACA,MAAMC,YAAY,GAAGnB,mBAAmB,CAAChM,MAAM,KAAK,CAAC,GACjDgM,mBAAmB,CAAC,CAAC,CAAC,CAACxD,IAAI,CAACjE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAC3C,oBAAoB;oBAExB0I,UAAU,CAACnF,IAAI,cACbnK,OAAA;sBAAyBsM,SAAS,EAAC,gGAAgG;sBAAAC,QAAA,gBACjIvM,OAAA;wBAAIsM,SAAS,EAAC,4DAA4D;wBAAAC,QAAA,GAAC,2BAC1D,EAACiD,YAAY;sBAAA;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC,eACLlN,OAAA,CAACN,iBAAiB;wBAChBoI,OAAO,EAAE0F,OAAO,CAACzE,eAAe,IAAI,EAAG;wBACvC0G,KAAK,EAAE9N,YAAa;wBACpB+N,KAAK,EAAElC,OAAO,CAAClF,SAAU;wBACzBJ,MAAM,EAAEsF,OAAO,CAACtF;sBAAO;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,EAGD,CAACyC,sBAAA,IAAM;wBACN,MAAMC,aAAa,GAAG,EAAAD,sBAAA,GAAAnC,OAAO,CAACtE,gBAAgB,cAAAyG,sBAAA,uBAAxBA,sBAAA,CAA0BE,MAAM,CAACjN,MAAM,IAC3D,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,cACvC,CAAC,KAAI,EAAE;;wBAEP;wBACA,IAAIiN,eAAe,GAAGF,aAAa,CAACC,MAAM,CAACjN,MAAM,IAC/CD,mBAAmB,CAACC,MAAM,EAAYjB,YAAY,CACpD,CAAC;;wBAED;wBACA,IAAImO,eAAe,CAACzN,MAAM,KAAK,CAAC,IAAIV,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;0BAChF+N,eAAe,GAAGF,aAAa,CAACC,MAAM,CAACjN,MAAM,IAC1CA,MAAM,CAAYI,YAAY,KAAK,OACtC,CAAC;0BACDR,OAAO,CAACC,GAAG,CAAC,sEAAsE,EAAEqN,eAAe,CAACzN,MAAM,CAAC;wBAC7G;wBAEA,OAAOyN,eAAe,CAACzN,MAAM,GAAG,CAAC,IAAIX,uBAAuB,CAACC,YAAY,EAAE6L,OAAO,CAACzE,eAAe,IAAI,EAAE,CAAC,gBACvG/I,OAAA;0BAAKsM,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBvM,OAAA;4BAAIsM,SAAS,EAAC,0CAA0C;4BAAAC,QAAA,EAAC;0BAAkB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAChFlN,OAAA;4BAAKsM,SAAS,EAAC,WAAW;4BAAAC,QAAA,EACvBuD,eAAe,CAACzF,GAAG,CAAC,CAACzH,MAAM,EAAE4G,KAAK,kBACjCxJ,OAAA,CAACH,aAAa;8BAAa+C,MAAM,EAAEA;4BAAiB,GAAhC4G,KAAK;8BAAAuD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAA6B,CACvD;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,GACJ,IAAI;sBACV,CAAC,EAAE,CAAC,eAEJlN,OAAA;wBAAKsM,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDvM,OAAA;0BAAGsM,SAAS,EAAC,0CAA0C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACpElN,OAAA,CAAC6L,UAAU;0BAACC,KAAK,EAAEuC,mBAAoB;0BAACtC,UAAU,EAAE;wBAAE;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC;oBAAA,GA7CC,eAAe;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA8CnB,CACP,CAAC;;oBAED;oBACA;oBACA,MAAM6C,YAAY,GAAGzB,kBAAkB,CAACjM,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,mBAAmB;oBAE/FiN,UAAU,CAACnF,IAAI,cACbnK,OAAA;sBAAwBsM,SAAS,EAAC,kGAAkG;sBAAAC,QAAA,gBAClIvM,OAAA;wBAAIsM,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,GAAC,2BAC3D,EAACwD,YAAY;sBAAA;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC,eACLlN,OAAA,CAACN,iBAAiB;wBAChBoI,OAAO,EAAE0F,OAAO,CAACxE,cAAc,IAAI,EAAG;wBACtCyG,KAAK,EAAE9N,YAAa;wBACpB+N,KAAK,EAAElC,OAAO,CAAClF,SAAU;wBACzBJ,MAAM,EAAEsF,OAAO,CAACtF;sBAAO;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACFlN,OAAA;wBAAKsM,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,gBAClDvM,OAAA;0BAAGsM,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACrElN,OAAA,CAAC6L,UAAU;0BAACC,KAAK,EAAEwC,kBAAmB;0BAACvC,UAAU,EAAE;wBAAE;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA,GAbC,cAAc;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAclB,CACP,CAAC;;oBAEH;kBACA,CAAC,MAAM,IAAIqB,kBAAkB,EAAE;oBAC7BgB,YAAY,GAAG,eAAe;;oBAE9B;oBACA,MAAMC,YAAY,GAAGnB,mBAAmB,CAAChM,MAAM,KAAK,CAAC,GACjDgM,mBAAmB,CAAC,CAAC,CAAC,CAACxD,IAAI,CAACjE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAC3C,oBAAoB;oBAExB0I,UAAU,CAACnF,IAAI,cACbnK,OAAA;sBAA6BsM,SAAS,EAAC,gGAAgG;sBAAAC,QAAA,gBACrIvM,OAAA;wBAAIsM,SAAS,EAAC,4DAA4D;wBAAAC,QAAA,EAAC;sBAE3E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlN,OAAA,CAACN,iBAAiB;wBAChBoI,OAAO,EAAE0F,OAAO,CAACzE,eAAe,IAAI,EAAG;wBACvC0G,KAAK,EAAE9N,YAAa;wBACpB+N,KAAK,EAAElC,OAAO,CAAClF,SAAU;wBACzBJ,MAAM,EAAEsF,OAAO,CAACtF;sBAAO;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,EAGD,CAAC8C,sBAAA,IAAM;wBACN,MAAMJ,aAAa,GAAG,EAAAI,sBAAA,GAAAxC,OAAO,CAACtE,gBAAgB,cAAA8G,sBAAA,uBAAxBA,sBAAA,CAA0BH,MAAM,CAACjN,MAAM,IAC3D,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,cACvC,CAAC,KAAI,EAAE;;wBAEP;wBACA,IAAIiN,eAAe,GAAGF,aAAa,CAACC,MAAM,CAACjN,MAAM,IAC/CD,mBAAmB,CAACC,MAAM,EAAYjB,YAAY,CACpD,CAAC;;wBAED;wBACA,IAAImO,eAAe,CAACzN,MAAM,KAAK,CAAC,IAAIV,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;0BAChF+N,eAAe,GAAGF,aAAa,CAACC,MAAM,CAACjN,MAAM,IAC1CA,MAAM,CAAYI,YAAY,KAAK,OACtC,CAAC;0BACDR,OAAO,CAACC,GAAG,CAAC,6FAA6F,EAAEqN,eAAe,CAACzN,MAAM,CAAC;wBACpI;wBAEA,OAAOyN,eAAe,CAACzN,MAAM,GAAG,CAAC,IAAIX,uBAAuB,CAACC,YAAY,EAAE6L,OAAO,CAACzE,eAAe,IAAI,EAAE,CAAC,gBACvG/I,OAAA;0BAAKsM,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBvM,OAAA;4BAAIsM,SAAS,EAAC,0CAA0C;4BAAAC,QAAA,EAAC;0BAAkB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAChFlN,OAAA;4BAAKsM,SAAS,EAAC,WAAW;4BAAAC,QAAA,EACvBuD,eAAe,CAACzF,GAAG,CAAC,CAACzH,MAAM,EAAE4G,KAAK,kBACjCxJ,OAAA,CAACH,aAAa;8BAAa+C,MAAM,EAAEA;4BAAiB,GAAhC4G,KAAK;8BAAAuD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAA6B,CACvD;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,GACJ,IAAI;sBACV,CAAC,EAAE,CAAC,eAEJlN,OAAA;wBAAKsM,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDvM,OAAA;0BAAGsM,SAAS,EAAC,0CAA0C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACpElN,OAAA,CAAC6L,UAAU;0BAACC,KAAK,EAAEuC,mBAAoB;0BAACtC,UAAU,EAAE;wBAAE;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC;oBAAA,GA7CC,mBAAmB;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA8CvB,CACP,CAAC;;oBAEH;kBACA,CAAC,MAAM,IAAIsB,iBAAiB,EAAE;oBAC5Be,YAAY,GAAG,cAAc;;oBAE7B;oBACA,MAAMQ,YAAY,GAAGzB,kBAAkB,CAACjM,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,mBAAmB;oBAE/FiN,UAAU,CAACnF,IAAI,cACbnK,OAAA;sBAA4BsM,SAAS,EAAC,kGAAkG;sBAAAC,QAAA,gBACtIvM,OAAA;wBAAIsM,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,EAAC;sBAE5E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlN,OAAA,CAACN,iBAAiB;wBAChBoI,OAAO,EAAE0F,OAAO,CAACxE,cAAc,IAAI,EAAG;wBACtCyG,KAAK,EAAE9N,YAAa;wBACpB+N,KAAK,EAAElC,OAAO,CAAClF,SAAU;wBACzBJ,MAAM,EAAEsF,OAAO,CAACtF;sBAAO;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACFlN,OAAA;wBAAKsM,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,gBAClDvM,OAAA;0BAAGsM,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACrElN,OAAA,CAAC6L,UAAU;0BAACC,KAAK,EAAEwC,kBAAmB;0BAACvC,UAAU,EAAE;wBAAE;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA,GAbC,kBAAkB;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OActB,CACP,CAAC;;oBAEH;kBACA,CAAC,MAAM,IAAIuB,cAAc,EAAE;oBAAA,IAAAwB,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;oBACzBf,YAAY,GAAG,KAAK;oBAEpB,MAAMgB,SAAS,GAAG/C,OAAO,CAAClF,SAAS,IAAI,QAAQ;oBAC/C,MAAMkI,SAAS,GAAG,CAAAP,kBAAA,GAAAzC,OAAO,CAAClF,SAAS,cAAA2H,kBAAA,eAAjBA,kBAAA,CAAmBlO,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,GAC/C,CAAAmO,mBAAA,GAAA1C,OAAO,CAAClF,SAAS,cAAA4H,mBAAA,eAAjBA,mBAAA,CAAmBnO,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,GACzC,CAAAoO,mBAAA,GAAA3C,OAAO,CAAClF,SAAS,cAAA6H,mBAAA,eAAjBA,mBAAA,CAAmBpO,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAC9C,CAAAqO,mBAAA,GAAA5C,OAAO,CAAClF,SAAS,cAAA8H,mBAAA,eAAjBA,mBAAA,CAAmBrO,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,GAC1C,CAAAsO,mBAAA,GAAA7C,OAAO,CAAClF,SAAS,cAAA+H,mBAAA,eAAjBA,mBAAA,CAAmBtO,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,GAC5C,CAAAuO,mBAAA,GAAA9C,OAAO,CAAClF,SAAS,cAAAgI,mBAAA,eAAjBA,mBAAA,CAAmBvO,QAAQ,CAAC,aAAa,CAAC,GAAG,IAAI,GAAG,IAAI;;oBAExE;oBACA,IAAI,CAACb,eAAe,IAAI,CAACsM,OAAO,CAACnF,OAAO,EAAE;sBAC1CiH,UAAU,CAACnF,IAAI,cACbnK,OAAA;wBAAwBsM,SAAS,EAAC,oGAAoG;wBAAAC,QAAA,gBACpIvM,OAAA;0BAAIsM,SAAS,EAAC,8DAA8D;0BAAAC,QAAA,GACzEiE,SAAS,EAAC,uBAAqB,EAACD,SAAS;wBAAA;0BAAAxD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC,eACLlN,OAAA,CAACN,iBAAiB;0BAChBoI,OAAO,EAAE0F,OAAO,CAAC1F,OAAO,IAAI,mEAAoE;0BAChG2H,KAAK,EAAE9N,YAAa;0BACpB+N,KAAK,EAAElC,OAAO,CAAClF,SAAS,IAAI,QAAS;0BACrCJ,MAAM,EAAEsF,OAAO,CAACtF;wBAAO;0BAAA6E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC,eACFlN,OAAA;0BAAKsM,SAAS,EAAC,sCAAsC;0BAAAC,QAAA,eACnDvM,OAAA;4BAAGsM,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,EAAC;0BAE9C;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA,GAdC,cAAc;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAelB,CACP,CAAC;oBACD;;oBAEF;kBACA,CAAC,MAAM;oBACL;oBACAqC,YAAY,GAAG,YAAY;;oBAE3B;oBACA,IAAI,CAACrO,eAAe,IAAI,CAACsM,OAAO,CAACnF,OAAO,EAAE;sBAC1CiH,UAAU,CAACnF,IAAI,cACbnK,OAAA;wBAAsBsM,SAAS,EAAC,sFAAsF;wBAAAC,QAAA,gBACpHvM,OAAA;0BAAGsM,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAC;wBAAgB;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAC9DlN,OAAA,CAACN,iBAAiB;0BAChBoI,OAAO,EAAE0F,OAAO,CAAC1F,OAAO,IAAI,sGAAuG;0BACnI2H,KAAK,EAAE9N,YAAa;0BACpB+N,KAAK,EAAElC,OAAO,CAAClF,SAAS,IAAI,QAAS;0BACrCJ,MAAM,EAAEsF,OAAO,CAACtF;wBAAO;0BAAA6E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA,GAPK,YAAY;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAQhB,CACP,CAAC;oBACD;kBACF;;kBAEA;kBACA,IAAIoC,UAAU,CAACjN,MAAM,GAAG,CAAC,EAAE;oBACzB,oBACErC,OAAA;sBAAKsM,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClB+C;oBAAU;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAEV;;kBAEA;kBACA1K,OAAO,CAACiO,IAAI,CAAC,oCAAoC,CAAC;kBAClD,oBACEzQ,OAAA;oBAAKsM,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,gBACnGvM,OAAA;sBAAGsM,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAe;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC7DlN,OAAA,CAACN,iBAAiB;sBAChBoI,OAAO,EAAE0F,OAAO,CAAC1F,OAAO,IAAI,iDAAkD;sBAC9E2H,KAAK,EAAE9N,YAAa;sBACpB+N,KAAK,EAAElC,OAAO,CAAClF,SAAS,IAAI,QAAS;sBACrCJ,MAAM,EAAEsF,OAAO,CAACtF;oBAAO;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAEV,CAAC,EAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GA3UDM,OAAO,CAACtG,EAAE;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4UZ,CACN,CAAC,eACFlN,OAAA;YAAK0Q,GAAG,EAAEpP,cAAe;YAACqP,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAO;UAAE;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNlN,OAAA;QAAKsM,SAAS,EAAE,2GAA2GpM,WAAW,GAAG,mBAAmB,GAAG,QAAQ,EAAG;QAAAqM,QAAA,gBACxKvM,OAAA;UAAM8Q,QAAQ,EAAE1J,iBAAkB;UAACkF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBACxEvM,OAAA;YAAKsM,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCvM,OAAA;cACE+Q,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEpQ,KAAM;cACbqQ,QAAQ,EAAG5J,CAAC,IAAK;gBACf,MAAM6J,QAAQ,GAAG7J,CAAC,CAACqF,MAAM,CAACsE,KAAK;gBAC/BnQ,QAAQ,CAACqQ,QAAQ,CAAC;gBAClB;cACF,CAAE;cACFC,WAAW,EAAC,+CAA+C;cAC3D7E,SAAS,EAAC,wJAAwJ;cAClK8E,QAAQ,EAAEtQ,YAAa;cACvB,cAAW;YAAe;cAAAiM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlN,OAAA;YAAKsM,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CvM,OAAA,CAACR,WAAW;cACV6R,YAAY,EAAErQ,cAAe;cAC7BsQ,aAAa,EAAGC,OAAO,IAAKtQ,iBAAiB,CAACsQ,OAAO,CAAE;cACvDC,SAAS,EAAE1Q;YAAa;cAAAiM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACFlN,OAAA;cACE+Q,IAAI,EAAC,QAAQ;cACbK,QAAQ,EAAEtQ,YAAY,IAAI,CAACF,KAAK,CAACmD,IAAI,CAAC,CAAE;cACxCuI,SAAS,EAAC,uMAAuM;cACjNM,KAAK,EAAC,cAAc;cAAAL,QAAA,eAEpBvM,OAAA;gBAAAuM,QAAA,EAAOzL,YAAY,GAAG,YAAY,GAAG;cAAM;gBAAAiM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACKlN,OAAA;UAAKsM,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GAAC,iBACnD,EAAC,EAAAnM,qBAAA,GAAAX,kBAAkB,CAACsH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKlG,cAAc,CAAC,cAAAZ,qBAAA,uBAArDA,qBAAA,CAAuD6G,IAAI,KAAIjG,cAAc;QAAA;UAAA+L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL5M,QAAQ,CAAC+B,MAAM,GAAG,CAAC,iBAAIrC,OAAA;QAAKsM,SAAS,EAAC;MAAM;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAOA,SAASuE,GAAGA,CAAC;EAAEvR,WAAW;EAAEC;AAAyB,CAAC,EAAE;EACtD,oBACEH,OAAA,CAACC,aAAa;IAACC,WAAW,EAAEA,WAAY;IAACC,cAAc,EAAEA;EAAe;IAAA4M,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAE/E;AAEA,eAAeuE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}