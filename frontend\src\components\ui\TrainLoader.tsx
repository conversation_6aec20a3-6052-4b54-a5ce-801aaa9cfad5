import React, { useEffect } from 'react';
import './TrainLoader.css';

interface TrainLoaderProps {
  isVisible: boolean;
  message?: string;
  trainType?: 'rajdhani' | 'shatabdi' | 'express' | 'passenger';
  currentStage?: 'initializing' | 'searching_documents' | 'searching_websites' | 'generating_answer' | 'complete';
  onComplete?: () => void;
  sidebarOpen?: boolean; // New prop to handle sidebar state
}

const TrainLoader: React.FC<TrainLoaderProps> = ({ 
  isVisible, 
  message = "RailGPT Generating Answer...", 
  trainType = 'express',
  currentStage = 'initializing',
  onComplete,
  sidebarOpen = false
}) => {


  useEffect(() => {
    if (isVisible) {
      // Try to play train sound if available
      const audio = document.getElementById('train-audio') as HTMLAudioElement;
      if (audio) {
        audio.play().catch(() => {
          // Audio playback failed (user interaction required)
          console.log('Audio autoplay blocked - user interaction required');
        });
      }
    } else {
      // Stop train sound
      const audio = document.getElementById('train-audio') as HTMLAudioElement;
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
    }

    return () => {
      // Cleanup audio on component unmount
      const audio = document.getElementById('train-audio') as HTMLAudioElement;
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
    };
  }, [isVisible]);

  if (!isVisible) return null;

  const getTrainColors = () => {
    switch (trainType) {
      case 'rajdhani':
        return { coach: '#8B0000', engine: '#FFD700' }; // Dark red + Gold
      case 'shatabdi':
        return { coach: '#0066CC', engine: '#FFFFFF' }; // Blue + White
      case 'express':
        return { coach: '#DC143C', engine: '#1E3A8A' }; // Red + Blue
      default:
        return { coach: '#DC143C', engine: '#1E3A8A' }; // Default colors
    }
  };

  const colors = getTrainColors();

  return (
    <>
      {/* Audio element for train sound */}
      <audio 
        id="train-audio" 
        loop 
        preload="auto"
        style={{ display: 'none' }}
      >
        {/* Add your train sound file here */}
        <source src="/sounds/train-sound.mp3" type="audio/mpeg" />
        <source src="https://www.soundjay.com/misc/sounds/train-whistle-1.mp3" type="audio/mpeg" />
      </audio>

      {/* Animated Text Trail */}
      <div className={`train-text-trail ${sidebarOpen ? 'sidebar-open' : ''}`}>
        {message}
      </div>

      {/* Train Container - Direct without wrapper */}
      <div className={`train-container-direct ${sidebarOpen ? 'sidebar-open' : ''}`}>
        {/* Engine */}
        <div className="train-engine" style={{ backgroundColor: colors.engine }}>
          {/* Engine Front */}
          <div className="engine-front"></div>
          
          {/* Engine Chimney with Smoke */}
          <div className="engine-chimney">
            <div className="smoke smoke-1"></div>
            <div className="smoke smoke-2"></div>
            <div className="smoke smoke-3"></div>
            <div className="smoke smoke-4"></div>
          </div>
          
          {/* Engine Details */}
          <div className="engine-details">
            <div className="engine-window"></div>
            <div className="engine-light"></div>
          </div>

          {/* Engine Wheels */}
          <div className="train-wheels">
            <div className="wheel wheel-1">
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
            </div>
            <div className="wheel wheel-2">
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
            </div>
          </div>
        </div>

        {/* Coach */}
        <div className="train-coach" style={{ backgroundColor: colors.coach }}>
          {/* Indian Railways Logo */}
          <div className="ir-logo">
            <div className="ir-circle">
              <span className="ir-text">IR</span>
            </div>
          </div>

          {/* Coach Windows */}
          <div className="coach-windows">
            <div className="window"></div>
            <div className="window"></div>
            <div className="window"></div>
            <div className="window"></div>
          </div>

          {/* Coach Details */}
          <div className="coach-details">
            <div className="coach-door"></div>
            <div className="coach-number">12345</div>
          </div>

          {/* Coach Wheels */}
          <div className="train-wheels">
            <div className="wheel wheel-3">
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
            </div>
            <div className="wheel wheel-4">
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
            </div>
          </div>
        </div>

        {/* Wagon */}
        <div className="train-wagon">
          {/* Wagon Logo */}
          <div className="wagon-logo">
            <div className="ir-circle small">
              <span className="ir-text small">IR</span>
            </div>
          </div>

          {/* Wagon Details */}
          <div className="wagon-details">
            <div className="cargo-lines">
              <div className="cargo-line"></div>
              <div className="cargo-line"></div>
              <div className="cargo-line"></div>
            </div>
          </div>

          {/* Wagon Wheels */}
          <div className="train-wheels">
            <div className="wheel wheel-5">
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
            </div>
            <div className="wheel wheel-6">
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
              <div className="wheel-spoke"></div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TrainLoader; 