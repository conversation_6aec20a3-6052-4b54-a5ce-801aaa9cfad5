#!/usr/bin/env python3
"""
Test script for Category Management API
This script tests the category management functionality without requiring a running server.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from category_management import (
    get_categories, 
    get_categories_by_type, 
    create_category, 
    update_category,
    CategoryCreate,
    CategoryUpdate
)
import asyncio

async def test_category_management():
    """Test the category management functionality"""
    print("🧪 Testing Category Management API")
    print("=" * 50)
    
    try:
        # Test 1: Get all categories
        print("\n1. Testing get_categories()...")
        categories = await get_categories()
        print(f"✅ Found {len(categories)} categories")
        
        # Display first few categories
        for i, cat in enumerate(categories[:5]):
            print(f"   - {cat.get('name', 'Unknown')} ({cat.get('type', 'Unknown')})")
        
        # Test 2: Get categories by type
        print("\n2. Testing get_categories_by_type('main_category')...")
        main_categories = await get_categories_by_type('main_category')
        print(f"✅ Found {len(main_categories)} main categories")
        
        for cat in main_categories[:3]:
            print(f"   - {cat.get('name', 'Unknown')}")
        
        # Test 3: Create a new category
        print("\n3. Testing create_category()...")
        new_category = CategoryCreate(
            name="Test Category",
            type="main_category",
            description="A test category for demonstration",
            sort_order=999
        )
        
        try:
            result = await create_category(new_category)
            if result.get('success'):
                print("✅ Successfully created test category")
                created_id = result.get('category', {}).get('id')
                print(f"   - Created category ID: {created_id}")
                
                # Test 4: Update the category
                if created_id:
                    print("\n4. Testing update_category()...")
                    update_data = CategoryUpdate(
                        name="Updated Test Category",
                        description="Updated description"
                    )
                    
                    update_result = await update_category(created_id, update_data)
                    if update_result.get('success'):
                        print("✅ Successfully updated test category")
                    else:
                        print("❌ Failed to update category")
                
            else:
                print("❌ Failed to create test category")
                print(f"   Error: {result}")
                
        except Exception as e:
            print(f"❌ Error creating category: {str(e)}")
        
        print("\n" + "=" * 50)
        print("🎉 Category Management API Test Complete!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

def test_category_structure():
    """Test the category data structure and hierarchy"""
    print("\n🏗️  Testing Category Structure")
    print("=" * 50)
    
    # Test category creation
    category = CategoryCreate(
        name="Safety Protocols",
        type="category",
        parent_id="some-parent-id",
        description="Safety protocol documents",
        sort_order=1
    )
    
    print("✅ CategoryCreate model works correctly")
    print(f"   - Name: {category.name}")
    print(f"   - Type: {category.type}")
    print(f"   - Parent ID: {category.parent_id}")
    print(f"   - Description: {category.description}")
    print(f"   - Sort Order: {category.sort_order}")
    
    # Test category update
    update = CategoryUpdate(
        name="Updated Safety Protocols",
        is_active=True
    )
    
    print("\n✅ CategoryUpdate model works correctly")
    print(f"   - Name: {update.name}")
    print(f"   - Is Active: {update.is_active}")

if __name__ == "__main__":
    print("🚀 Starting Category Management Tests")
    
    # Test 1: Data structures
    test_category_structure()
    
    # Test 2: API functions (requires database connection)
    print("\n" + "=" * 70)
    try:
        asyncio.run(test_category_management())
    except Exception as e:
        print(f"❌ API tests failed: {str(e)}")
        print("   This is expected if the database is not accessible")
    
    print("\n✨ All tests completed!")
