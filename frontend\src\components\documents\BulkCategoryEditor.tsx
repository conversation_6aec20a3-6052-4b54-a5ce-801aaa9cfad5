import React, { useState, useEffect } from 'react';
import { Document } from '../../types/documents';
import { 
  getCategories, 
  bulkUpdateDocumentCategories,
  CategoryHierarchy,
  DocumentCategoryUpdate 
} from '../../services/api';

interface BulkCategoryEditorProps {
  documents: Document[];
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedDocuments: Document[]) => void;
}

const BulkCategoryEditor: React.FC<BulkCategoryEditorProps> = ({
  documents,
  isOpen,
  onClose,
  onSave,
}) => {
  const [categories, setCategories] = useState<CategoryHierarchy[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [categoryUpdate, setCategoryUpdate] = useState<DocumentCategoryUpdate>({
    main_category: '',
    category: '',
    sub_category: '',
    minor_category: '',
  });

  // Load categories when modal opens
  useEffect(() => {
    if (isOpen) {
      loadCategories();
    }
  }, [isOpen]);

  const loadCategories = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getCategories();
      setCategories(data);
    } catch (error) {
      console.error('Error loading categories:', error);
      setError('Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof DocumentCategoryUpdate, value: string) => {
    setCategoryUpdate(prev => ({
      ...prev,
      [field]: value || undefined
    }));
  };

  const handleSave = async () => {
    if (documents.length === 0) return;

    // Check if at least one field is filled
    const hasChanges = Object.values(categoryUpdate).some(value => value && value.trim() !== '');
    if (!hasChanges) {
      setError('Please select at least one category to update');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const documentIds = documents.map(doc => doc.id);
      
      // Clean up the category update object - remove empty strings
      const cleanedUpdate: DocumentCategoryUpdate = {};
      Object.entries(categoryUpdate).forEach(([key, value]) => {
        if (value && value.trim() !== '') {
          cleanedUpdate[key as keyof DocumentCategoryUpdate] = value.trim();
        }
      });

      await bulkUpdateDocumentCategories(documentIds, cleanedUpdate);

      // Create updated documents for the callback
      const updatedDocuments = documents.map(doc => ({
        ...doc,
        mainCategory: cleanedUpdate.main_category || doc.mainCategory,
        category: cleanedUpdate.category || doc.category,
        subCategory: cleanedUpdate.sub_category || doc.subCategory,
        minorCategory: cleanedUpdate.minor_category || doc.minorCategory,
      }));

      onSave(updatedDocuments);
      handleClose();
    } catch (error) {
      console.error('Error updating categories:', error);
      setError(error instanceof Error ? error.message : 'Failed to update categories');
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    setCategoryUpdate({
      main_category: '',
      category: '',
      sub_category: '',
      minor_category: '',
    });
    setError(null);
    onClose();
  };

  // Get unique category values for dropdowns
  const getMainCategories = () => {
    const mainCats = new Set<string>();
    categories.forEach(cat => {
      if (cat.level === 0) {
        mainCats.add(cat.name);
      }
    });
    return Array.from(mainCats).sort();
  };

  const getSubCategories = () => {
    const subCats = new Set<string>();
    categories.forEach(cat => {
      if (cat.level === 1) {
        subCats.add(cat.name);
      }
    });
    return Array.from(subCats).sort();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Bulk Update Categories ({documents.length} documents)
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {loading ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="mt-2 text-gray-600">Loading categories...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> Only fill in the categories you want to update. 
                  Empty fields will leave the existing values unchanged for each document.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Main Category
                  </label>
                  <select
                    value={categoryUpdate.main_category || ''}
                    onChange={(e) => handleInputChange('main_category', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Keep existing values</option>
                    {getMainCategories().map((cat) => (
                      <option key={cat} value={cat}>
                        {cat}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={categoryUpdate.category || ''}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Keep existing values</option>
                    {getSubCategories().map((cat) => (
                      <option key={cat} value={cat}>
                        {cat}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sub Category
                  </label>
                  <input
                    type="text"
                    value={categoryUpdate.sub_category || ''}
                    onChange={(e) => handleInputChange('sub_category', e.target.value)}
                    placeholder="Keep existing values"
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minor Category
                  </label>
                  <input
                    type="text"
                    value={categoryUpdate.minor_category || ''}
                    onChange={(e) => handleInputChange('minor_category', e.target.value)}
                    placeholder="Keep existing values"
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Selected Documents ({documents.length}):
                </h3>
                <div className="max-h-32 overflow-y-auto">
                  {documents.map((doc) => (
                    <div key={doc.id} className="text-sm text-gray-600 py-1">
                      • {doc.name}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
            <button
              onClick={handleClose}
              disabled={saving}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={saving || loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              {saving && (
                <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              )}
              {saving ? 'Updating...' : 'Update Categories'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkCategoryEditor;
