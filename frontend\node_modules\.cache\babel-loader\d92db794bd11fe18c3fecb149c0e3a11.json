{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\components\\\\documents\\\\DocumentUploadForm.tsx\";\nimport React, { useState, useEffect } from 'react';\nimport { uploadDocument } from '../../services/api';\nimport { Alert, AlertDescription, AlertTitle } from \"../ui/alert\";\nimport { getCategories } from '../../services/categoryApi';\n\n// Component for uploading documents with category selection\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DocumentUploadForm = () => {\n  // Form state\n  const [documentName, setDocumentName] = useState('');\n  const [mainCategory, setMainCategory] = useState('');\n  const [category, setCategory] = useState('');\n  const [subCategory, setSubCategory] = useState('');\n  const [minorCategory, setMinorCategory] = useState('');\n  const [file, setFile] = useState(null);\n\n  // UI state\n  const [isUploading, setIsUploading] = useState(false);\n  const [newCategoryInput, setNewCategoryInput] = useState('');\n  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);\n  const [categoryType, setCategoryType] = useState('Main');\n\n  // Upload status tracking\n  const [uploadStatus, setUploadStatus] = useState('idle');\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [extractedChunks, setExtractedChunks] = useState([]);\n\n  // Form validation and feedback\n  const [errors, setErrors] = useState({});\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [touched, setTouched] = useState({});\n\n  // Categories from API\n  const [allCategories, setAllCategories] = useState([]);\n  const [loadingCategories, setLoadingCategories] = useState(false);\n\n  // Helper functions to filter categories by type\n  const getMainCategories = () => allCategories.filter(cat => cat.type === 'main_category');\n  const getCategoriesByParent = parentId => allCategories.filter(cat => cat.parent_id === parentId);\n  const getSubCategoriesByParent = parentId => allCategories.filter(cat => cat.parent_id === parentId);\n  const getMinorCategoriesByParent = parentId => allCategories.filter(cat => cat.parent_id === parentId);\n\n  // Load categories from API\n  useEffect(() => {\n    const loadCategories = async () => {\n      try {\n        setLoadingCategories(true);\n        const categories = await getCategories();\n        setAllCategories(categories);\n      } catch (error) {\n        console.warn('Failed to load categories:', error);\n        setErrorMessage('Failed to load categories. Category selection may be limited.');\n      } finally {\n        setLoadingCategories(false);\n      }\n    };\n    loadCategories();\n  }, []);\n\n  // Effect to validate form\n  useEffect(() => {\n    let isMounted = true;\n    const newErrors = {};\n    if (touched.documentName && !documentName.trim()) {\n      newErrors.documentName = 'Document name is required';\n    }\n    if (touched.mainCategory && !mainCategory) {\n      newErrors.mainCategory = 'Main category is required';\n    }\n    if (touched.category && !category) {\n      newErrors.category = 'Category is required';\n    }\n    if (touched.file && !file) {\n      newErrors.file = 'Document file is required';\n    } else if (file) {\n      // Validate file type\n      const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'image/jpeg', 'image/png', 'text/plain'];\n      if (!allowedTypes.includes(file.type)) {\n        newErrors.file = 'File type not supported. Please upload PDF, DOCX, XLSX, JPG, PNG, or TXT';\n      }\n\n      // Validate file size (200MB max)\n      const maxSize = 200 * 1024 * 1024; // 200MB\n      if (file.size > maxSize) {\n        newErrors.file = 'File too large. Maximum size is 200MB';\n      }\n    }\n\n    // Only update state if component is still mounted\n    if (isMounted) {\n      setErrors(newErrors);\n    }\n\n    // Cleanup function\n    return () => {\n      isMounted = false;\n    };\n  }, [documentName, mainCategory, category, file, touched]);\n\n  // Mark fields as touched when user interacts with them\n  const markAsTouched = field => {\n    setTouched(prev => ({\n      ...prev,\n      [field]: true\n    }));\n  };\n\n  // useEffect to clear success message after 5 seconds\n  useEffect(() => {\n    if (successMessage) {\n      const timer = setTimeout(() => {\n        setSuccessMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage]);\n\n  // useEffect to clear error message after 5 seconds\n  useEffect(() => {\n    if (errorMessage) {\n      const timer = setTimeout(() => {\n        setErrorMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [errorMessage]);\n  const handleFileChange = e => {\n    if (e.target.files && e.target.files[0]) {\n      const selectedFile = e.target.files[0];\n      setFile(selectedFile);\n      markAsTouched('file');\n\n      // Set document name from filename if empty\n      if (!documentName.trim()) {\n        // Remove extension and replace underscores/hyphens with spaces\n        const nameFromFile = selectedFile.name.replace(/\\.[^/.]+$/, '').replace(/[_-]/g, ' ');\n        setDocumentName(nameFromFile);\n        markAsTouched('documentName');\n      }\n    }\n  };\n  const handleNewCategorySubmit = async () => {\n    if (!newCategoryInput.trim()) return;\n    setIsUploading(true);\n    try {\n      // In a real app, you would make an API call to create the category\n      // Example API call:\n      // const response = await fetch(`${API_URL}/api/categories`, {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ name: newCategoryInput, type: categoryType })\n      // });\n      // if (!response.ok) throw new Error('Failed to create category');\n      // const data = await response.json();\n\n      // For now, simulate API call with a delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      // Update the appropriate category state\n      if (categoryType === 'Main') {\n        const newCategory = {\n          id: `new-${Date.now()}`,\n          name: newCategoryInput,\n          type: 'Main'\n        };\n        setMainCategories(prev => [...prev, newCategory]);\n        setMainCategory(newCategoryInput);\n      } else if (categoryType === 'Category') {\n        const newCategory = {\n          id: `new-${Date.now()}`,\n          name: newCategoryInput,\n          type: 'Category',\n          parentId: mainCategory\n        };\n        setCategories(prev => [...prev, newCategory]);\n        setCategory(newCategoryInput);\n      } else if (categoryType === 'Sub') {\n        const newCategory = {\n          id: `new-${Date.now()}`,\n          name: newCategoryInput,\n          type: 'Sub',\n          parentId: category\n        };\n        setSubCategories(prev => [...prev, newCategory]);\n        setSubCategory(newCategoryInput);\n      } else {\n        // Minor category handling\n        setMinorCategory(newCategoryInput);\n      }\n      setSuccessMessage(`Created new ${categoryType} category: ${newCategoryInput}`);\n    } catch (error) {\n      setErrorMessage(`Failed to create category: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    } finally {\n      setIsUploading(false);\n      setNewCategoryInput('');\n      setShowNewCategoryInput(false);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Mark all fields as touched to show any errors\n    markAsTouched('documentName');\n    markAsTouched('mainCategory');\n    markAsTouched('category');\n    markAsTouched('file');\n\n    // Check if there are any errors\n    if (Object.keys(errors).length > 0 || !file) {\n      setErrorMessage('Please correct the errors before submitting');\n      return;\n    }\n\n    // Track if component is still mounted\n    let isMounted = true;\n    let progressInterval = null;\n\n    // Update UI state\n    const safeSetState = updater => {\n      if (isMounted) {\n        updater();\n      }\n    };\n    safeSetState(() => {\n      setIsUploading(true);\n      setUploadStatus('uploading');\n      setUploadProgress(10);\n      setSuccessMessage('');\n      setErrorMessage('');\n      setExtractedChunks([]);\n    });\n    try {\n      // Simulate upload progress updates (in a real app, use XHR or fetch with progress events)\n      progressInterval = setInterval(() => {\n        if (isMounted) {\n          setUploadProgress(prev => {\n            const newProgress = prev + 5;\n            if (newProgress >= 90) {\n              if (progressInterval) clearInterval(progressInterval);\n              return 90; // Hold at 90% until processing is complete\n            }\n            return newProgress;\n          });\n        }\n      }, 300);\n\n      // Upload the document\n      safeSetState(() => setUploadStatus('uploading'));\n\n      // Upload the document using the API function\n      const response = await uploadDocument(file, \"<EMAIL>\");\n\n      // Clear the progress interval if it exists\n      if (progressInterval) {\n        clearInterval(progressInterval);\n        progressInterval = null;\n      }\n\n      // Only update state if component is still mounted\n      if (!isMounted) return;\n      if (response.success) {\n        var _response$data, _file$name$split$pop;\n        // Set progress to 100%\n        setUploadProgress(100);\n        setUploadStatus('success');\n\n        // Store extracted chunks if available\n        if (response.chunks && response.chunks.length > 0) {\n          setExtractedChunks(response.chunks);\n          console.log('Extracted chunks:', response.chunks);\n        }\n\n        // Create event data for custom event\n        const eventData = {\n          detail: {\n            documentName,\n            mainCategory,\n            category,\n            subCategory,\n            minorCategory,\n            file,\n            uploadedAt: new Date().toISOString(),\n            id: ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.id) || `doc-${Date.now()}`,\n            status: 'Extracted',\n            // Update to real status from backend\n            fileType: (_file$name$split$pop = file.name.split('.').pop()) === null || _file$name$split$pop === void 0 ? void 0 : _file$name$split$pop.toLowerCase(),\n            qualityScore: 90,\n            // This would come from backend in real app\n            chunks: response.chunks || [],\n            extractedContent: response.chunks ? response.chunks.map(chunk => chunk.text).join('\\n\\n') : '',\n            chunks_extracted: response.chunks_extracted || 0\n          }\n        };\n\n        // Dispatch event to notify the DocumentsPage\n        const event = new CustomEvent('documentUploaded', {\n          detail: eventData.detail\n        });\n        window.dispatchEvent(event);\n\n        // Set success message (ensure error is cleared)\n        setErrorMessage('');\n        setSuccessMessage(`Document \"${documentName}\" uploaded successfully!`);\n\n        // Auto-clear success message after 5 seconds\n        const clearSuccessMessage = () => {\n          if (isMounted) {\n            setSuccessMessage('');\n          }\n        };\n        setTimeout(clearSuccessMessage, 5000);\n\n        // Reset form after successful upload\n        const resetForm = () => {\n          if (isMounted) {\n            setDocumentName('');\n            setMainCategory('');\n            setCategory('');\n            setSubCategory('');\n            setMinorCategory('');\n            setFile(null);\n            setUploadStatus('idle');\n            setUploadProgress(0);\n            setExtractedChunks([]);\n            setIsUploading(false);\n            setTouched({});\n          }\n        };\n        setTimeout(resetForm, 3000);\n      } else {\n        // Handle upload error\n        setUploadStatus('error');\n        setErrorMessage(response.message || 'Failed to upload document');\n      }\n    } catch (error) {\n      console.error('Upload failed:', error);\n      if (isMounted) {\n        setUploadStatus('error');\n        setErrorMessage(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      }\n    } finally {\n      if (isMounted) {\n        setIsUploading(false);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-md p-6 transition-colors duration-300\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-lg font-semibold mb-4 text-gray-900\",\n      children: \"Upload Document\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"success\",\n        children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n          children: \"Success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AlertDescription, {\n          children: successMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 9\n    }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"destructive\",\n        children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AlertDescription, {\n          children: errorMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: [\"Document Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 27\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: documentName,\n          onChange: e => setDocumentName(e.target.value),\n          onBlur: () => markAsTouched('documentName'),\n          className: `w-full p-2 border ${errors.documentName ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), errors.documentName && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-red-500\",\n          children: errors.documentName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: [\"Main Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 27\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: mainCategory,\n            onChange: e => {\n              setMainCategory(e.target.value);\n              // Reset dependent categories when main category changes\n              setCategory('');\n              setSubCategory('');\n              setMinorCategory('');\n            },\n            onBlur: () => markAsTouched('mainCategory'),\n            className: `flex-1 p-2 border ${errors.mainCategory ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`,\n            required: true,\n            disabled: loadingCategories,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: loadingCategories ? 'Loading...' : 'Select Main Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), getMainCategories().map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: cat.id,\n              children: cat.name\n            }, cat.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => {\n              setCategoryType('Main');\n              setShowNewCategoryInput(true);\n            },\n            className: \"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\",\n            children: \"+ New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this), errors.mainCategory && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-red-500\",\n          children: errors.mainCategory\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 22\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: category,\n            onChange: e => {\n              setCategory(e.target.value);\n              // Reset dependent categories when category changes\n              setSubCategory('');\n              setMinorCategory('');\n            },\n            onBlur: () => markAsTouched('category'),\n            className: `flex-1 p-2 border ${errors.category ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`,\n            required: true,\n            disabled: !mainCategory || loadingCategories,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), mainCategory && getCategoriesByParent(mainCategory).filter(cat => cat.type === 'category').map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: cat.id,\n              children: cat.name\n            }, cat.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => {\n              setCategoryType('Category');\n              setShowNewCategoryInput(true);\n            },\n            className: \"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\",\n            disabled: !mainCategory,\n            children: \"+ New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), errors.category && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-red-500\",\n          children: errors.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: \"Sub Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: subCategory,\n            onChange: e => setSubCategory(e.target.value),\n            className: \"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\",\n            disabled: !category,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Sub Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this), subCategories.filter(sub => {\n              var _categories$find;\n              return !category || sub.parentId === ((_categories$find = categories.find(c => c.name === category)) === null || _categories$find === void 0 ? void 0 : _categories$find.id);\n            }).map(sub => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: sub.name,\n              children: sub.name\n            }, sub.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => {\n              setCategoryType('Sub');\n              setShowNewCategoryInput(true);\n            },\n            className: \"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\",\n            disabled: !category,\n            children: \"+ New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: \"Minor Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: minorCategory,\n            onChange: e => setMinorCategory(e.target.value),\n            className: \"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\",\n            placeholder: \"Enter Minor Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => {\n              setCategoryType('Minor');\n              setShowNewCategoryInput(true);\n            },\n            className: \"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\",\n            children: \"+ New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: [\"Upload Document \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: `flex flex-col w-full h-32 border-2 ${errors.file ? 'border-red-500' : 'border-blue-300'} border-dashed hover:bg-gray-50 hover:border-blue-500 rounded-lg cursor-pointer`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center justify-center pt-7\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-8 h-8 text-gray-400 group-hover:text-gray-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600\",\n                children: file ? file.name : 'Attach document (PDF, DOCX, XLSX, etc.)'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              className: \"opacity-0\",\n              accept: \".pdf,.docx,.xlsx,.txt,.png,.jpg,.jpeg\",\n              onChange: handleFileChange,\n              onBlur: () => markAsTouched('file'),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), errors.file && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-red-500\",\n          children: errors.file\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isUploading,\n          className: `w-full px-4 py-2 text-white font-medium rounded-md ${isUploading ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'} focus:outline-none focus:ring-2 focus:ring-blue-500`,\n          children: isUploading ? 'Uploading...' : 'Upload Document'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 9\n      }, this), uploadStatus !== 'idle' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-700\",\n            children: uploadStatus === 'uploading' ? 'Uploading...' : uploadStatus === 'processing' ? 'Processing...' : uploadStatus === 'success' ? 'Upload Complete' : 'Upload Failed'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-700\",\n            children: [uploadProgress, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-gray-200 rounded-full h-2.5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `h-2.5 rounded-full ${uploadStatus === 'error' ? 'bg-red-600' : uploadStatus === 'success' ? 'bg-green-600' : 'bg-blue-600'}`,\n            style: {\n              width: `${uploadProgress}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 11\n      }, this), uploadStatus === 'success' && extractedChunks.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-700 mb-2\",\n          children: \"Extracted Content Preview:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-h-40 overflow-y-auto text-sm text-gray-600\",\n          children: [extractedChunks.slice(0, 3).map((chunk, index) => {\n            var _chunk$text;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2 p-2 bg-white rounded border border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mb-1\",\n                children: chunk.source_type === 'document' ? `Page ${chunk.page || 'N/A'}` : `Source: ${chunk.source || 'Unknown'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [(_chunk$text = chunk.text) === null || _chunk$text === void 0 ? void 0 : _chunk$text.substring(0, 150), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 17\n            }, this);\n          }), extractedChunks.length > 3 && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 text-center mt-2\",\n            children: [\"+ \", extractedChunks.length - 3, \" more chunks not shown\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), showNewCategoryInput && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium mb-4\",\n          children: [\"Add New \", categoryType, \" Category\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: newCategoryInput,\n          onChange: e => setNewCategoryInput(e.target.value),\n          className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4\",\n          placeholder: `Enter ${categoryType} Category name`,\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNewCategoryInput(false),\n            className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleNewCategorySubmit,\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n            children: \"Add Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 663,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 365,\n    columnNumber: 5\n  }, this);\n};\nexport default DocumentUploadForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "uploadDocument", "<PERSON><PERSON>", "AlertDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCategories", "jsxDEV", "_jsxDEV", "DocumentUploadForm", "documentName", "setDocumentName", "mainCategory", "setMainCategory", "category", "setCategory", "subCategory", "setSubCategory", "minorCategory", "setMinorCategory", "file", "setFile", "isUploading", "setIsUploading", "newCategoryInput", "setNewCategoryInput", "showNewCategoryInput", "setShowNewCategoryInput", "categoryType", "setCategoryType", "uploadStatus", "setUploadStatus", "uploadProgress", "setUploadProgress", "extractedChunks", "setExtractedChunks", "errors", "setErrors", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "touched", "setTouched", "allCategories", "setAllCategories", "loadingCategories", "setLoadingCategories", "getMainCategories", "filter", "cat", "type", "getCategoriesByParent", "parentId", "parent_id", "getSubCategoriesByParent", "getMinorCategoriesByParent", "loadCategories", "categories", "error", "console", "warn", "isMounted", "newErrors", "trim", "allowedTypes", "includes", "maxSize", "size", "<PERSON><PERSON><PERSON><PERSON>ched", "field", "prev", "timer", "setTimeout", "clearTimeout", "handleFileChange", "e", "target", "files", "selectedFile", "nameFromFile", "name", "replace", "handleNewCategorySubmit", "Promise", "resolve", "newCategory", "id", "Date", "now", "setMainCategories", "setCategories", "setSubCategories", "Error", "message", "handleSubmit", "preventDefault", "Object", "keys", "length", "progressInterval", "safeSetState", "updater", "setInterval", "newProgress", "clearInterval", "response", "success", "_response$data", "_file$name$split$pop", "chunks", "log", "eventData", "detail", "uploadedAt", "toISOString", "data", "status", "fileType", "split", "pop", "toLowerCase", "qualityScore", "extractedContent", "map", "chunk", "text", "join", "chunks_extracted", "event", "CustomEvent", "window", "dispatchEvent", "clearSuccessMessage", "resetForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onSubmit", "value", "onChange", "onBlur", "required", "disabled", "onClick", "subCategories", "sub", "_categories$find", "find", "c", "placeholder", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "accept", "style", "width", "slice", "index", "_chunk$text", "source_type", "page", "source", "substring", "autoFocus"], "sources": ["C:/IR App/frontend/src/components/documents/DocumentUploadForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { uploadDocument } from '../../services/api';\r\nimport { Alert, AlertDescription, AlertTitle } from \"../ui/alert\";\r\nimport { DocumentCategory, CategoryHierarchy } from '../../types/documents';\r\nimport { getCategories } from '../../services/categoryApi';\r\n\r\n// Component for uploading documents with category selection\r\ninterface FormErrors {\r\n  documentName?: string;\r\n  mainCategory?: string;\r\n  category?: string;\r\n  file?: string;\r\n}\r\n\r\nconst DocumentUploadForm: React.FC = (): React.ReactElement => {\r\n  // Form state\r\n  const [documentName, setDocumentName] = useState('');\r\n  const [mainCategory, setMainCategory] = useState('');\r\n  const [category, setCategory] = useState('');\r\n  const [subCategory, setSubCategory] = useState('');\r\n  const [minorCategory, setMinorCategory] = useState('');\r\n  const [file, setFile] = useState<File | null>(null);\r\n\r\n  // UI state\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [newCategoryInput, setNewCategoryInput] = useState('');\r\n  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);\r\n  const [categoryType, setCategoryType] = useState<'Main' | 'Category' | 'Sub' | 'Minor'>('Main');\r\n\r\n  // Upload status tracking\r\n  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'processing' | 'success' | 'error'>('idle');\r\n  const [uploadProgress, setUploadProgress] = useState(0);\r\n  const [extractedChunks, setExtractedChunks] = useState<any[]>([]);\r\n\r\n  // Form validation and feedback\r\n  const [errors, setErrors] = useState<FormErrors>({});\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [touched, setTouched] = useState<Record<string, boolean>>({});\r\n\r\n  // Categories from API\r\n  const [allCategories, setAllCategories] = useState<CategoryHierarchy[]>([]);\r\n  const [loadingCategories, setLoadingCategories] = useState(false);\r\n\r\n  // Helper functions to filter categories by type\r\n  const getMainCategories = () => allCategories.filter(cat => cat.type === 'main_category');\r\n  const getCategoriesByParent = (parentId: string) => allCategories.filter(cat => cat.parent_id === parentId);\r\n  const getSubCategoriesByParent = (parentId: string) => allCategories.filter(cat => cat.parent_id === parentId);\r\n  const getMinorCategoriesByParent = (parentId: string) => allCategories.filter(cat => cat.parent_id === parentId);\r\n\r\n  // Load categories from API\r\n  useEffect(() => {\r\n    const loadCategories = async () => {\r\n      try {\r\n        setLoadingCategories(true);\r\n        const categories = await getCategories();\r\n        setAllCategories(categories);\r\n      } catch (error) {\r\n        console.warn('Failed to load categories:', error);\r\n        setErrorMessage('Failed to load categories. Category selection may be limited.');\r\n      } finally {\r\n        setLoadingCategories(false);\r\n      }\r\n    };\r\n\r\n    loadCategories();\r\n  }, []);\r\n\r\n  // Effect to validate form\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const newErrors: FormErrors = {};\r\n\r\n    if (touched.documentName && !documentName.trim()) {\r\n      newErrors.documentName = 'Document name is required';\r\n    }\r\n\r\n    if (touched.mainCategory && !mainCategory) {\r\n      newErrors.mainCategory = 'Main category is required';\r\n    }\r\n\r\n    if (touched.category && !category) {\r\n      newErrors.category = 'Category is required';\r\n    }\r\n\r\n    if (touched.file && !file) {\r\n      newErrors.file = 'Document file is required';\r\n    } else if (file) {\r\n      // Validate file type\r\n      const allowedTypes = [\r\n        'application/pdf',\r\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n        'image/jpeg',\r\n        'image/png',\r\n        'text/plain'\r\n      ];\r\n\r\n      if (!allowedTypes.includes(file.type)) {\r\n        newErrors.file = 'File type not supported. Please upload PDF, DOCX, XLSX, JPG, PNG, or TXT';\r\n      }\r\n\r\n      // Validate file size (200MB max)\r\n      const maxSize = 200 * 1024 * 1024; // 200MB\r\n      if (file.size > maxSize) {\r\n        newErrors.file = 'File too large. Maximum size is 200MB';\r\n      }\r\n    }\r\n\r\n    // Only update state if component is still mounted\r\n    if (isMounted) {\r\n      setErrors(newErrors);\r\n    }\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [documentName, mainCategory, category, file, touched]);\r\n\r\n  // Mark fields as touched when user interacts with them\r\n  const markAsTouched = (field: string) => {\r\n    setTouched(prev => ({ ...prev, [field]: true }));\r\n  };\r\n\r\n\r\n\r\n  // useEffect to clear success message after 5 seconds\r\n  useEffect(() => {\r\n    if (successMessage) {\r\n      const timer = setTimeout(() => {\r\n        setSuccessMessage('');\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [successMessage]);\r\n\r\n  // useEffect to clear error message after 5 seconds\r\n  useEffect(() => {\r\n    if (errorMessage) {\r\n      const timer = setTimeout(() => {\r\n        setErrorMessage('');\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [errorMessage]);\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files[0]) {\r\n      const selectedFile = e.target.files[0];\r\n      setFile(selectedFile);\r\n      markAsTouched('file');\r\n\r\n      // Set document name from filename if empty\r\n      if (!documentName.trim()) {\r\n        // Remove extension and replace underscores/hyphens with spaces\r\n        const nameFromFile = selectedFile.name.replace(/\\.[^/.]+$/, '').replace(/[_-]/g, ' ');\r\n        setDocumentName(nameFromFile);\r\n        markAsTouched('documentName');\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleNewCategorySubmit = async () => {\r\n    if (!newCategoryInput.trim()) return;\r\n\r\n    setIsUploading(true);\r\n    try {\r\n      // In a real app, you would make an API call to create the category\r\n      // Example API call:\r\n      // const response = await fetch(`${API_URL}/api/categories`, {\r\n      //   method: 'POST',\r\n      //   headers: { 'Content-Type': 'application/json' },\r\n      //   body: JSON.stringify({ name: newCategoryInput, type: categoryType })\r\n      // });\r\n      // if (!response.ok) throw new Error('Failed to create category');\r\n      // const data = await response.json();\r\n\r\n      // For now, simulate API call with a delay\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      // Update the appropriate category state\r\n      if (categoryType === 'Main') {\r\n        const newCategory: DocumentCategory = { id: `new-${Date.now()}`, name: newCategoryInput, type: 'Main' };\r\n        setMainCategories(prev => [...prev, newCategory]);\r\n        setMainCategory(newCategoryInput);\r\n      } else if (categoryType === 'Category') {\r\n        const newCategory: DocumentCategory = { id: `new-${Date.now()}`, name: newCategoryInput, type: 'Category', parentId: mainCategory };\r\n        setCategories(prev => [...prev, newCategory]);\r\n        setCategory(newCategoryInput);\r\n      } else if (categoryType === 'Sub') {\r\n        const newCategory: DocumentCategory = { id: `new-${Date.now()}`, name: newCategoryInput, type: 'Sub', parentId: category };\r\n        setSubCategories(prev => [...prev, newCategory]);\r\n        setSubCategory(newCategoryInput);\r\n      } else {\r\n        // Minor category handling\r\n        setMinorCategory(newCategoryInput);\r\n      }\r\n\r\n      setSuccessMessage(`Created new ${categoryType} category: ${newCategoryInput}`);\r\n    } catch (error) {\r\n      setErrorMessage(`Failed to create category: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    } finally {\r\n      setIsUploading(false);\r\n      setNewCategoryInput('');\r\n      setShowNewCategoryInput(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent): Promise<void> => {\r\n    e.preventDefault();\r\n\r\n    // Mark all fields as touched to show any errors\r\n    markAsTouched('documentName');\r\n    markAsTouched('mainCategory');\r\n    markAsTouched('category');\r\n    markAsTouched('file');\r\n\r\n    // Check if there are any errors\r\n    if (Object.keys(errors).length > 0 || !file) {\r\n      setErrorMessage('Please correct the errors before submitting');\r\n      return;\r\n    }\r\n\r\n    // Track if component is still mounted\r\n    let isMounted = true;\r\n    let progressInterval: NodeJS.Timeout | null = null;\r\n\r\n    // Update UI state\r\n    const safeSetState = (updater: () => void) => {\r\n      if (isMounted) {\r\n        updater();\r\n      }\r\n    };\r\n\r\n    safeSetState(() => {\r\n      setIsUploading(true);\r\n      setUploadStatus('uploading');\r\n      setUploadProgress(10);\r\n      setSuccessMessage('');\r\n      setErrorMessage('');\r\n      setExtractedChunks([]);\r\n    });\r\n\r\n\r\n\r\n    try {\r\n      // Simulate upload progress updates (in a real app, use XHR or fetch with progress events)\r\n      progressInterval = setInterval(() => {\r\n        if (isMounted) {\r\n          setUploadProgress(prev => {\r\n            const newProgress = prev + 5;\r\n            if (newProgress >= 90) {\r\n              if (progressInterval) clearInterval(progressInterval);\r\n              return 90; // Hold at 90% until processing is complete\r\n            }\r\n            return newProgress;\r\n          });\r\n        }\r\n      }, 300);\r\n\r\n\r\n\r\n      // Upload the document\r\n      safeSetState(() => setUploadStatus('uploading'));\r\n\r\n      // Upload the document using the API function\r\n      const response = await uploadDocument(file, \"<EMAIL>\");\r\n\r\n      // Clear the progress interval if it exists\r\n      if (progressInterval) {\r\n        clearInterval(progressInterval);\r\n        progressInterval = null;\r\n      }\r\n\r\n      // Only update state if component is still mounted\r\n      if (!isMounted) return;\r\n\r\n      if (response.success) {\r\n        // Set progress to 100%\r\n        setUploadProgress(100);\r\n        setUploadStatus('success');\r\n\r\n        // Store extracted chunks if available\r\n        if (response.chunks && response.chunks.length > 0) {\r\n          setExtractedChunks(response.chunks);\r\n          console.log('Extracted chunks:', response.chunks);\r\n        }\r\n\r\n        // Create event data for custom event\r\n        const eventData = {\r\n          detail: {\r\n            documentName,\r\n            mainCategory,\r\n            category,\r\n            subCategory,\r\n            minorCategory,\r\n            file,\r\n            uploadedAt: new Date().toISOString(),\r\n            id: response.data?.id || `doc-${Date.now()}`,\r\n            status: 'Extracted', // Update to real status from backend\r\n            fileType: file.name.split('.').pop()?.toLowerCase(),\r\n            qualityScore: 90, // This would come from backend in real app\r\n            chunks: response.chunks || [],\r\n            extractedContent: response.chunks ? response.chunks.map((chunk: any) => chunk.text).join('\\n\\n') : '',\r\n            chunks_extracted: response.chunks_extracted || 0\r\n          }\r\n        };\r\n\r\n        // Dispatch event to notify the DocumentsPage\r\n        const event = new CustomEvent('documentUploaded', { detail: eventData.detail });\r\n        window.dispatchEvent(event);\r\n\r\n        // Set success message (ensure error is cleared)\r\n        setErrorMessage('');\r\n        setSuccessMessage(`Document \"${documentName}\" uploaded successfully!`);\r\n\r\n        // Auto-clear success message after 5 seconds\r\n        const clearSuccessMessage = () => {\r\n          if (isMounted) {\r\n            setSuccessMessage('');\r\n          }\r\n        };\r\n\r\n        setTimeout(clearSuccessMessage, 5000);\r\n\r\n        // Reset form after successful upload\r\n        const resetForm = () => {\r\n          if (isMounted) {\r\n            setDocumentName('');\r\n            setMainCategory('');\r\n            setCategory('');\r\n            setSubCategory('');\r\n            setMinorCategory('');\r\n            setFile(null);\r\n            setUploadStatus('idle');\r\n            setUploadProgress(0);\r\n            setExtractedChunks([]);\r\n            setIsUploading(false);\r\n            setTouched({});\r\n          }\r\n        };\r\n\r\n        setTimeout(resetForm, 3000);\r\n      } else {\r\n        // Handle upload error\r\n        setUploadStatus('error');\r\n        setErrorMessage(response.message || 'Failed to upload document');\r\n      }\r\n    } catch (error) {\r\n      console.error('Upload failed:', error);\r\n      if (isMounted) {\r\n        setUploadStatus('error');\r\n        setErrorMessage(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n      }\r\n    } finally {\r\n      if (isMounted) {\r\n        setIsUploading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md p-6 transition-colors duration-300\">\r\n      <h2 className=\"text-lg font-semibold mb-4 text-gray-900\">Upload Document</h2>\r\n\r\n      {/* Success message */}\r\n      {successMessage && (\r\n        <div className=\"mb-4\">\r\n          <Alert variant=\"success\">\r\n            <AlertTitle>Success</AlertTitle>\r\n            <AlertDescription>{successMessage}</AlertDescription>\r\n          </Alert>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error message */}\r\n      {errorMessage && (\r\n        <div className=\"mb-4\">\r\n          <Alert variant=\"destructive\">\r\n            <AlertTitle>Error</AlertTitle>\r\n            <AlertDescription>{errorMessage}</AlertDescription>\r\n          </Alert>\r\n        </div>\r\n      )}\r\n\r\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n        {/* Document Name */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Document Name <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            value={documentName}\r\n            onChange={(e) => setDocumentName(e.target.value)}\r\n            onBlur={() => markAsTouched('documentName')}\r\n            className={`w-full p-2 border ${errors.documentName ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}\r\n            required\r\n          />\r\n          {errors.documentName && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.documentName}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Main Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Main Category <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={mainCategory}\r\n              onChange={(e) => {\r\n                setMainCategory(e.target.value);\r\n                // Reset dependent categories when main category changes\r\n                setCategory('');\r\n                setSubCategory('');\r\n                setMinorCategory('');\r\n              }}\r\n              onBlur={() => markAsTouched('mainCategory')}\r\n              className={`flex-1 p-2 border ${errors.mainCategory ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}\r\n              required\r\n              disabled={loadingCategories}\r\n            >\r\n              <option value=\"\">{loadingCategories ? 'Loading...' : 'Select Main Category'}</option>\r\n              {getMainCategories().map((cat) => (\r\n                <option key={cat.id} value={cat.id}>\r\n                  {cat.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Main');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n          {errors.mainCategory && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.mainCategory}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Category <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={category}\r\n              onChange={(e) => {\r\n                setCategory(e.target.value);\r\n                // Reset dependent categories when category changes\r\n                setSubCategory('');\r\n                setMinorCategory('');\r\n              }}\r\n              onBlur={() => markAsTouched('category')}\r\n              className={`flex-1 p-2 border ${errors.category ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}\r\n              required\r\n              disabled={!mainCategory || loadingCategories}\r\n            >\r\n              <option value=\"\">Select Category</option>\r\n              {mainCategory && getCategoriesByParent(mainCategory)\r\n                .filter(cat => cat.type === 'category')\r\n                .map((cat) => (\r\n                  <option key={cat.id} value={cat.id}>\r\n                    {cat.name}\r\n                  </option>\r\n                ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Category');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n              disabled={!mainCategory}\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n          {errors.category && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.category}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Sub Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Sub Category\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={subCategory}\r\n              onChange={(e) => setSubCategory(e.target.value)}\r\n              className=\"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\r\n              disabled={!category}\r\n            >\r\n              <option value=\"\">Select Sub Category</option>\r\n              {subCategories\r\n                .filter(sub => !category || sub.parentId === categories.find(c => c.name === category)?.id)\r\n                .map((sub) => (\r\n                  <option key={sub.id} value={sub.name}>\r\n                    {sub.name}\r\n                  </option>\r\n                ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Sub');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n              disabled={!category}\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Minor Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Minor Category\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <input\r\n              type=\"text\"\r\n              value={minorCategory}\r\n              onChange={(e) => setMinorCategory(e.target.value)}\r\n              className=\"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\r\n              placeholder=\"Enter Minor Category\"\r\n            />\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Minor');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* File Upload */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Upload Document <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div className=\"flex items-center justify-center w-full\">\r\n            <label\r\n              className={`flex flex-col w-full h-32 border-2 ${errors.file ? 'border-red-500' : 'border-blue-300'} border-dashed hover:bg-gray-50 hover:border-blue-500 rounded-lg cursor-pointer`}\r\n            >\r\n              <div className=\"flex flex-col items-center justify-center pt-7\">\r\n                <svg\r\n                  className=\"w-8 h-8 text-gray-400 group-hover:text-gray-600\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth=\"2\"\r\n                    d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\r\n                  ></path>\r\n                </svg>\r\n                <p className=\"pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600\">\r\n                  {file ? file.name : 'Attach document (PDF, DOCX, XLSX, etc.)'}\r\n                </p>\r\n              </div>\r\n              <input\r\n                type=\"file\"\r\n                className=\"opacity-0\"\r\n                accept=\".pdf,.docx,.xlsx,.txt,.png,.jpg,.jpeg\"\r\n                onChange={handleFileChange}\r\n                onBlur={() => markAsTouched('file')}\r\n                required\r\n              />\r\n            </label>\r\n          </div>\r\n          {errors.file && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.file}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Submit Button */}\r\n        <div>\r\n          <button\r\n            type=\"submit\"\r\n            disabled={isUploading}\r\n            className={`w-full px-4 py-2 text-white font-medium rounded-md ${\r\n              isUploading ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'\r\n            } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n          >\r\n            {isUploading ? 'Uploading...' : 'Upload Document'}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Upload Progress */}\r\n        {uploadStatus !== 'idle' && (\r\n          <div className=\"mt-4\">\r\n            <div className=\"flex items-center justify-between mb-1\">\r\n              <span className=\"text-sm font-medium text-gray-700\">\r\n                {uploadStatus === 'uploading' ? 'Uploading...' :\r\n                 uploadStatus === 'processing' ? 'Processing...' :\r\n                 uploadStatus === 'success' ? 'Upload Complete' :\r\n                 'Upload Failed'}\r\n              </span>\r\n              <span className=\"text-sm font-medium text-gray-700\">{uploadProgress}%</span>\r\n            </div>\r\n            <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n              <div\r\n                className={`h-2.5 rounded-full ${\r\n                  uploadStatus === 'error' ? 'bg-red-600' :\r\n                  uploadStatus === 'success' ? 'bg-green-600' : 'bg-blue-600'\r\n                }`}\r\n                style={{ width: `${uploadProgress}%` }}\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Extracted Chunks Preview (shown after successful upload) */}\r\n        {uploadStatus === 'success' && extractedChunks.length > 0 && (\r\n          <div className=\"mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50\">\r\n            <h3 className=\"text-sm font-medium text-gray-700 mb-2\">Extracted Content Preview:</h3>\r\n            <div className=\"max-h-40 overflow-y-auto text-sm text-gray-600\">\r\n              {extractedChunks.slice(0, 3).map((chunk, index) => (\r\n                <div key={index} className=\"mb-2 p-2 bg-white rounded border border-gray-200\">\r\n                  <p className=\"text-xs text-gray-500 mb-1\">\r\n                    {chunk.source_type === 'document' ?\r\n                      `Page ${chunk.page || 'N/A'}` :\r\n                      `Source: ${chunk.source || 'Unknown'}`}\r\n                  </p>\r\n                  <p>{chunk.text?.substring(0, 150)}...</p>\r\n                </div>\r\n              ))}\r\n              {extractedChunks.length > 3 && (\r\n                <p className=\"text-xs text-gray-500 text-center mt-2\">\r\n                  + {extractedChunks.length - 3} more chunks not shown\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </form>\r\n\r\n      {/* New Category Modal */}\r\n      {showNewCategoryInput && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\r\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\r\n            <h3 className=\"text-lg font-medium mb-4\">Add New {categoryType} Category</h3>\r\n            <input\r\n              type=\"text\"\r\n              value={newCategoryInput}\r\n              onChange={(e) => setNewCategoryInput(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4\"\r\n              placeholder={`Enter ${categoryType} Category name`}\r\n              autoFocus\r\n            />\r\n            <div className=\"flex justify-end gap-2\">\r\n              <button\r\n                onClick={() => setShowNewCategoryInput(false)}\r\n                className=\"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={handleNewCategorySubmit}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\r\n              >\r\n                Add Category\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DocumentUploadForm;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,KAAK,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,aAAa;AAEjE,SAASC,aAAa,QAAQ,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAQA,MAAMC,kBAA4B,GAAGA,CAAA,KAA0B;EAC7D;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGrB,QAAQ,CAAc,IAAI,CAAC;;EAEnD;EACA,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAwC,MAAM,CAAC;;EAE/F;EACA,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAA4D,MAAM,CAAC;EACnH,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAQ,EAAE,CAAC;;EAEjE;EACA,MAAM,CAACoC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAa,CAAC,CAAC,CAAC;EACpD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAA0B,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAsB,EAAE,CAAC;EAC3E,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAMgD,iBAAiB,GAAGA,CAAA,KAAMJ,aAAa,CAACK,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,eAAe,CAAC;EACzF,MAAMC,qBAAqB,GAAIC,QAAgB,IAAKT,aAAa,CAACK,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACI,SAAS,KAAKD,QAAQ,CAAC;EAC3G,MAAME,wBAAwB,GAAIF,QAAgB,IAAKT,aAAa,CAACK,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACI,SAAS,KAAKD,QAAQ,CAAC;EAC9G,MAAMG,0BAA0B,GAAIH,QAAgB,IAAKT,aAAa,CAACK,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACI,SAAS,KAAKD,QAAQ,CAAC;;EAEhH;EACApD,SAAS,CAAC,MAAM;IACd,MAAMwD,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFV,oBAAoB,CAAC,IAAI,CAAC;QAC1B,MAAMW,UAAU,GAAG,MAAMpD,aAAa,CAAC,CAAC;QACxCuC,gBAAgB,CAACa,UAAU,CAAC;MAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEF,KAAK,CAAC;QACjDlB,eAAe,CAAC,+DAA+D,CAAC;MAClF,CAAC,SAAS;QACRM,oBAAoB,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC;IAEDU,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxD,SAAS,CAAC,MAAM;IACd,IAAI6D,SAAS,GAAG,IAAI;IAEpB,MAAMC,SAAqB,GAAG,CAAC,CAAC;IAEhC,IAAIrB,OAAO,CAAChC,YAAY,IAAI,CAACA,YAAY,CAACsD,IAAI,CAAC,CAAC,EAAE;MAChDD,SAAS,CAACrD,YAAY,GAAG,2BAA2B;IACtD;IAEA,IAAIgC,OAAO,CAAC9B,YAAY,IAAI,CAACA,YAAY,EAAE;MACzCmD,SAAS,CAACnD,YAAY,GAAG,2BAA2B;IACtD;IAEA,IAAI8B,OAAO,CAAC5B,QAAQ,IAAI,CAACA,QAAQ,EAAE;MACjCiD,SAAS,CAACjD,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI4B,OAAO,CAACtB,IAAI,IAAI,CAACA,IAAI,EAAE;MACzB2C,SAAS,CAAC3C,IAAI,GAAG,2BAA2B;IAC9C,CAAC,MAAM,IAAIA,IAAI,EAAE;MACf;MACA,MAAM6C,YAAY,GAAG,CACnB,iBAAiB,EACjB,yEAAyE,EACzE,mEAAmE,EACnE,YAAY,EACZ,WAAW,EACX,YAAY,CACb;MAED,IAAI,CAACA,YAAY,CAACC,QAAQ,CAAC9C,IAAI,CAAC+B,IAAI,CAAC,EAAE;QACrCY,SAAS,CAAC3C,IAAI,GAAG,0EAA0E;MAC7F;;MAEA;MACA,MAAM+C,OAAO,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;MACnC,IAAI/C,IAAI,CAACgD,IAAI,GAAGD,OAAO,EAAE;QACvBJ,SAAS,CAAC3C,IAAI,GAAG,uCAAuC;MAC1D;IACF;;IAEA;IACA,IAAI0C,SAAS,EAAE;MACbzB,SAAS,CAAC0B,SAAS,CAAC;IACtB;;IAEA;IACA,OAAO,MAAM;MACXD,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,CAACpD,YAAY,EAAEE,YAAY,EAAEE,QAAQ,EAAEM,IAAI,EAAEsB,OAAO,CAAC,CAAC;;EAEzD;EACA,MAAM2B,aAAa,GAAIC,KAAa,IAAK;IACvC3B,UAAU,CAAC4B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,KAAK,GAAG;IAAK,CAAC,CAAC,CAAC;EAClD,CAAC;;EAID;EACArE,SAAS,CAAC,MAAM;IACd,IAAIqC,cAAc,EAAE;MAClB,MAAMkC,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BlC,iBAAiB,CAAC,EAAE,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMmC,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAClC,cAAc,CAAC,CAAC;;EAEpB;EACArC,SAAS,CAAC,MAAM;IACd,IAAIuC,YAAY,EAAE;MAChB,MAAMgC,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BhC,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMiC,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAChC,YAAY,CAAC,CAAC;EAElB,MAAMmC,gBAAgB,GAAIC,CAAsC,IAAK;IACnE,IAAIA,CAAC,CAACC,MAAM,CAACC,KAAK,IAAIF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;MACvC,MAAMC,YAAY,GAAGH,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MACtCzD,OAAO,CAAC0D,YAAY,CAAC;MACrBV,aAAa,CAAC,MAAM,CAAC;;MAErB;MACA,IAAI,CAAC3D,YAAY,CAACsD,IAAI,CAAC,CAAC,EAAE;QACxB;QACA,MAAMgB,YAAY,GAAGD,YAAY,CAACE,IAAI,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;QACrFvE,eAAe,CAACqE,YAAY,CAAC;QAC7BX,aAAa,CAAC,cAAc,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMc,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,CAAC3D,gBAAgB,CAACwC,IAAI,CAAC,CAAC,EAAE;IAE9BzC,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIZ,UAAU,CAACY,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA,IAAIzD,YAAY,KAAK,MAAM,EAAE;QAC3B,MAAM0D,WAA6B,GAAG;UAAEC,EAAE,EAAE,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAAER,IAAI,EAAEzD,gBAAgB;UAAE2B,IAAI,EAAE;QAAO,CAAC;QACvGuC,iBAAiB,CAACnB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEe,WAAW,CAAC,CAAC;QACjDzE,eAAe,CAACW,gBAAgB,CAAC;MACnC,CAAC,MAAM,IAAII,YAAY,KAAK,UAAU,EAAE;QACtC,MAAM0D,WAA6B,GAAG;UAAEC,EAAE,EAAE,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAAER,IAAI,EAAEzD,gBAAgB;UAAE2B,IAAI,EAAE,UAAU;UAAEE,QAAQ,EAAEzC;QAAa,CAAC;QACnI+E,aAAa,CAACpB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEe,WAAW,CAAC,CAAC;QAC7CvE,WAAW,CAACS,gBAAgB,CAAC;MAC/B,CAAC,MAAM,IAAII,YAAY,KAAK,KAAK,EAAE;QACjC,MAAM0D,WAA6B,GAAG;UAAEC,EAAE,EAAE,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAAER,IAAI,EAAEzD,gBAAgB;UAAE2B,IAAI,EAAE,KAAK;UAAEE,QAAQ,EAAEvC;QAAS,CAAC;QAC1H8E,gBAAgB,CAACrB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEe,WAAW,CAAC,CAAC;QAChDrE,cAAc,CAACO,gBAAgB,CAAC;MAClC,CAAC,MAAM;QACL;QACAL,gBAAgB,CAACK,gBAAgB,CAAC;MACpC;MAEAe,iBAAiB,CAAC,eAAeX,YAAY,cAAcJ,gBAAgB,EAAE,CAAC;IAChF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdlB,eAAe,CAAC,8BAA8BkB,KAAK,YAAYkC,KAAK,GAAGlC,KAAK,CAACmC,OAAO,GAAG,eAAe,EAAE,CAAC;IAC3G,CAAC,SAAS;MACRvE,cAAc,CAAC,KAAK,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBE,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAMoE,YAAY,GAAG,MAAOnB,CAAkB,IAAoB;IAChEA,CAAC,CAACoB,cAAc,CAAC,CAAC;;IAElB;IACA3B,aAAa,CAAC,cAAc,CAAC;IAC7BA,aAAa,CAAC,cAAc,CAAC;IAC7BA,aAAa,CAAC,UAAU,CAAC;IACzBA,aAAa,CAAC,MAAM,CAAC;;IAErB;IACA,IAAI4B,MAAM,CAACC,IAAI,CAAC9D,MAAM,CAAC,CAAC+D,MAAM,GAAG,CAAC,IAAI,CAAC/E,IAAI,EAAE;MAC3CqB,eAAe,CAAC,6CAA6C,CAAC;MAC9D;IACF;;IAEA;IACA,IAAIqB,SAAS,GAAG,IAAI;IACpB,IAAIsC,gBAAuC,GAAG,IAAI;;IAElD;IACA,MAAMC,YAAY,GAAIC,OAAmB,IAAK;MAC5C,IAAIxC,SAAS,EAAE;QACbwC,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAEDD,YAAY,CAAC,MAAM;MACjB9E,cAAc,CAAC,IAAI,CAAC;MACpBQ,eAAe,CAAC,WAAW,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBM,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,EAAE,CAAC;MACnBN,kBAAkB,CAAC,EAAE,CAAC;IACxB,CAAC,CAAC;IAIF,IAAI;MACF;MACAiE,gBAAgB,GAAGG,WAAW,CAAC,MAAM;QACnC,IAAIzC,SAAS,EAAE;UACb7B,iBAAiB,CAACsC,IAAI,IAAI;YACxB,MAAMiC,WAAW,GAAGjC,IAAI,GAAG,CAAC;YAC5B,IAAIiC,WAAW,IAAI,EAAE,EAAE;cACrB,IAAIJ,gBAAgB,EAAEK,aAAa,CAACL,gBAAgB,CAAC;cACrD,OAAO,EAAE,CAAC,CAAC;YACb;YACA,OAAOI,WAAW;UACpB,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,GAAG,CAAC;;MAIP;MACAH,YAAY,CAAC,MAAMtE,eAAe,CAAC,WAAW,CAAC,CAAC;;MAEhD;MACA,MAAM2E,QAAQ,GAAG,MAAMxG,cAAc,CAACkB,IAAI,EAAE,mBAAmB,CAAC;;MAEhE;MACA,IAAIgF,gBAAgB,EAAE;QACpBK,aAAa,CAACL,gBAAgB,CAAC;QAC/BA,gBAAgB,GAAG,IAAI;MACzB;;MAEA;MACA,IAAI,CAACtC,SAAS,EAAE;MAEhB,IAAI4C,QAAQ,CAACC,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,oBAAA;QACpB;QACA5E,iBAAiB,CAAC,GAAG,CAAC;QACtBF,eAAe,CAAC,SAAS,CAAC;;QAE1B;QACA,IAAI2E,QAAQ,CAACI,MAAM,IAAIJ,QAAQ,CAACI,MAAM,CAACX,MAAM,GAAG,CAAC,EAAE;UACjDhE,kBAAkB,CAACuE,QAAQ,CAACI,MAAM,CAAC;UACnClD,OAAO,CAACmD,GAAG,CAAC,mBAAmB,EAAEL,QAAQ,CAACI,MAAM,CAAC;QACnD;;QAEA;QACA,MAAME,SAAS,GAAG;UAChBC,MAAM,EAAE;YACNvG,YAAY;YACZE,YAAY;YACZE,QAAQ;YACRE,WAAW;YACXE,aAAa;YACbE,IAAI;YACJ8F,UAAU,EAAE,IAAI1B,IAAI,CAAC,CAAC,CAAC2B,WAAW,CAAC,CAAC;YACpC5B,EAAE,EAAE,EAAAqB,cAAA,GAAAF,QAAQ,CAACU,IAAI,cAAAR,cAAA,uBAAbA,cAAA,CAAerB,EAAE,KAAI,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;YAC5C4B,MAAM,EAAE,WAAW;YAAE;YACrBC,QAAQ,GAAAT,oBAAA,GAAEzF,IAAI,CAAC6D,IAAI,CAACsC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAX,oBAAA,uBAA1BA,oBAAA,CAA4BY,WAAW,CAAC,CAAC;YACnDC,YAAY,EAAE,EAAE;YAAE;YAClBZ,MAAM,EAAEJ,QAAQ,CAACI,MAAM,IAAI,EAAE;YAC7Ba,gBAAgB,EAAEjB,QAAQ,CAACI,MAAM,GAAGJ,QAAQ,CAACI,MAAM,CAACc,GAAG,CAAEC,KAAU,IAAKA,KAAK,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACrGC,gBAAgB,EAAEtB,QAAQ,CAACsB,gBAAgB,IAAI;UACjD;QACF,CAAC;;QAED;QACA,MAAMC,KAAK,GAAG,IAAIC,WAAW,CAAC,kBAAkB,EAAE;UAAEjB,MAAM,EAAED,SAAS,CAACC;QAAO,CAAC,CAAC;QAC/EkB,MAAM,CAACC,aAAa,CAACH,KAAK,CAAC;;QAE3B;QACAxF,eAAe,CAAC,EAAE,CAAC;QACnBF,iBAAiB,CAAC,aAAa7B,YAAY,0BAA0B,CAAC;;QAEtE;QACA,MAAM2H,mBAAmB,GAAGA,CAAA,KAAM;UAChC,IAAIvE,SAAS,EAAE;YACbvB,iBAAiB,CAAC,EAAE,CAAC;UACvB;QACF,CAAC;QAEDkC,UAAU,CAAC4D,mBAAmB,EAAE,IAAI,CAAC;;QAErC;QACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;UACtB,IAAIxE,SAAS,EAAE;YACbnD,eAAe,CAAC,EAAE,CAAC;YACnBE,eAAe,CAAC,EAAE,CAAC;YACnBE,WAAW,CAAC,EAAE,CAAC;YACfE,cAAc,CAAC,EAAE,CAAC;YAClBE,gBAAgB,CAAC,EAAE,CAAC;YACpBE,OAAO,CAAC,IAAI,CAAC;YACbU,eAAe,CAAC,MAAM,CAAC;YACvBE,iBAAiB,CAAC,CAAC,CAAC;YACpBE,kBAAkB,CAAC,EAAE,CAAC;YACtBZ,cAAc,CAAC,KAAK,CAAC;YACrBoB,UAAU,CAAC,CAAC,CAAC,CAAC;UAChB;QACF,CAAC;QAED8B,UAAU,CAAC6D,SAAS,EAAE,IAAI,CAAC;MAC7B,CAAC,MAAM;QACL;QACAvG,eAAe,CAAC,OAAO,CAAC;QACxBU,eAAe,CAACiE,QAAQ,CAACZ,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAIG,SAAS,EAAE;QACb/B,eAAe,CAAC,OAAO,CAAC;QACxBU,eAAe,CAAC,kBAAkBkB,KAAK,YAAYkC,KAAK,GAAGlC,KAAK,CAACmC,OAAO,GAAG,eAAe,EAAE,CAAC;MAC/F;IACF,CAAC,SAAS;MACR,IAAIhC,SAAS,EAAE;QACbvC,cAAc,CAAC,KAAK,CAAC;MACvB;IACF;EACF,CAAC;EAED,oBACEf,OAAA;IAAK+H,SAAS,EAAC,kEAAkE;IAAAC,QAAA,gBAC/EhI,OAAA;MAAI+H,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAG5EtG,cAAc,iBACb9B,OAAA;MAAK+H,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBhI,OAAA,CAACL,KAAK;QAAC0I,OAAO,EAAC,SAAS;QAAAL,QAAA,gBACtBhI,OAAA,CAACH,UAAU;UAAAmI,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChCpI,OAAA,CAACJ,gBAAgB;UAAAoI,QAAA,EAAElG;QAAc;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EAGApG,YAAY,iBACXhC,OAAA;MAAK+H,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBhI,OAAA,CAACL,KAAK;QAAC0I,OAAO,EAAC,aAAa;QAAAL,QAAA,gBAC1BhI,OAAA,CAACH,UAAU;UAAAmI,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC9BpI,OAAA,CAACJ,gBAAgB;UAAAoI,QAAA,EAAEhG;QAAY;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAEDpI,OAAA;MAAMsI,QAAQ,EAAE/C,YAAa;MAACwC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAEjDhI,OAAA;QAAAgI,QAAA,gBACEhI,OAAA;UAAO+H,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,gBAChD,eAAAhI,OAAA;YAAM+H,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACRpI,OAAA;UACE2C,IAAI,EAAC,MAAM;UACX4F,KAAK,EAAErI,YAAa;UACpBsI,QAAQ,EAAGpE,CAAC,IAAKjE,eAAe,CAACiE,CAAC,CAACC,MAAM,CAACkE,KAAK,CAAE;UACjDE,MAAM,EAAEA,CAAA,KAAM5E,aAAa,CAAC,cAAc,CAAE;UAC5CkE,SAAS,EAAE,qBAAqBnG,MAAM,CAAC1B,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,uHAAwH;UAClNwI,QAAQ;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACDxG,MAAM,CAAC1B,YAAY,iBAClBF,OAAA;UAAG+H,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEpG,MAAM,CAAC1B;QAAY;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpI,OAAA;QAAAgI,QAAA,gBACEhI,OAAA;UAAO+H,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,gBAChD,eAAAhI,OAAA;YAAM+H,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACRpI,OAAA;UAAK+H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhI,OAAA;YACEuI,KAAK,EAAEnI,YAAa;YACpBoI,QAAQ,EAAGpE,CAAC,IAAK;cACf/D,eAAe,CAAC+D,CAAC,CAACC,MAAM,CAACkE,KAAK,CAAC;cAC/B;cACAhI,WAAW,CAAC,EAAE,CAAC;cACfE,cAAc,CAAC,EAAE,CAAC;cAClBE,gBAAgB,CAAC,EAAE,CAAC;YACtB,CAAE;YACF8H,MAAM,EAAEA,CAAA,KAAM5E,aAAa,CAAC,cAAc,CAAE;YAC5CkE,SAAS,EAAE,qBAAqBnG,MAAM,CAACxB,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,uHAAwH;YAClNsI,QAAQ;YACRC,QAAQ,EAAErG,iBAAkB;YAAA0F,QAAA,gBAE5BhI,OAAA;cAAQuI,KAAK,EAAC,EAAE;cAAAP,QAAA,EAAE1F,iBAAiB,GAAG,YAAY,GAAG;YAAsB;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,EACpF5F,iBAAiB,CAAC,CAAC,CAAC4E,GAAG,CAAE1E,GAAG,iBAC3B1C,OAAA;cAAqBuI,KAAK,EAAE7F,GAAG,CAACqC,EAAG;cAAAiD,QAAA,EAChCtF,GAAG,CAAC+B;YAAI,GADE/B,GAAG,CAACqC,EAAE;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTpI,OAAA;YACE2C,IAAI,EAAC,QAAQ;YACbiG,OAAO,EAAEA,CAAA,KAAM;cACbvH,eAAe,CAAC,MAAM,CAAC;cACvBF,uBAAuB,CAAC,IAAI,CAAC;YAC/B,CAAE;YACF4G,SAAS,EAAC,2KAA2K;YAAAC,QAAA,EACtL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLxG,MAAM,CAACxB,YAAY,iBAClBJ,OAAA;UAAG+H,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEpG,MAAM,CAACxB;QAAY;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpI,OAAA;QAAAgI,QAAA,gBACEhI,OAAA;UAAO+H,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,WACrD,eAAAhI,OAAA;YAAM+H,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACRpI,OAAA;UAAK+H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhI,OAAA;YACEuI,KAAK,EAAEjI,QAAS;YAChBkI,QAAQ,EAAGpE,CAAC,IAAK;cACf7D,WAAW,CAAC6D,CAAC,CAACC,MAAM,CAACkE,KAAK,CAAC;cAC3B;cACA9H,cAAc,CAAC,EAAE,CAAC;cAClBE,gBAAgB,CAAC,EAAE,CAAC;YACtB,CAAE;YACF8H,MAAM,EAAEA,CAAA,KAAM5E,aAAa,CAAC,UAAU,CAAE;YACxCkE,SAAS,EAAE,qBAAqBnG,MAAM,CAACtB,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,uHAAwH;YAC9MoI,QAAQ;YACRC,QAAQ,EAAE,CAACvI,YAAY,IAAIkC,iBAAkB;YAAA0F,QAAA,gBAE7ChI,OAAA;cAAQuI,KAAK,EAAC,EAAE;cAAAP,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACxChI,YAAY,IAAIwC,qBAAqB,CAACxC,YAAY,CAAC,CACjDqC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,UAAU,CAAC,CACtCyE,GAAG,CAAE1E,GAAG,iBACP1C,OAAA;cAAqBuI,KAAK,EAAE7F,GAAG,CAACqC,EAAG;cAAAiD,QAAA,EAChCtF,GAAG,CAAC+B;YAAI,GADE/B,GAAG,CAACqC,EAAE;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACTpI,OAAA;YACE2C,IAAI,EAAC,QAAQ;YACbiG,OAAO,EAAEA,CAAA,KAAM;cACbvH,eAAe,CAAC,UAAU,CAAC;cAC3BF,uBAAuB,CAAC,IAAI,CAAC;YAC/B,CAAE;YACF4G,SAAS,EAAC,2KAA2K;YACrLY,QAAQ,EAAE,CAACvI,YAAa;YAAA4H,QAAA,EACzB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLxG,MAAM,CAACtB,QAAQ,iBACdN,OAAA;UAAG+H,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEpG,MAAM,CAACtB;QAAQ;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC9D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpI,OAAA;QAAAgI,QAAA,gBACEhI,OAAA;UAAO+H,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpI,OAAA;UAAK+H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhI,OAAA;YACEuI,KAAK,EAAE/H,WAAY;YACnBgI,QAAQ,EAAGpE,CAAC,IAAK3D,cAAc,CAAC2D,CAAC,CAACC,MAAM,CAACkE,KAAK,CAAE;YAChDR,SAAS,EAAC,wJAAwJ;YAClKY,QAAQ,EAAE,CAACrI,QAAS;YAAA0H,QAAA,gBAEpBhI,OAAA;cAAQuI,KAAK,EAAC,EAAE;cAAAP,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC5CS,aAAa,CACXpG,MAAM,CAACqG,GAAG;cAAA,IAAAC,gBAAA;cAAA,OAAI,CAACzI,QAAQ,IAAIwI,GAAG,CAACjG,QAAQ,OAAAkG,gBAAA,GAAK7F,UAAU,CAAC8F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxE,IAAI,KAAKnE,QAAQ,CAAC,cAAAyI,gBAAA,uBAAzCA,gBAAA,CAA2ChE,EAAE;YAAA,EAAC,CAC1FqC,GAAG,CAAE0B,GAAG,iBACP9I,OAAA;cAAqBuI,KAAK,EAAEO,GAAG,CAACrE,IAAK;cAAAuD,QAAA,EAClCc,GAAG,CAACrE;YAAI,GADEqE,GAAG,CAAC/D,EAAE;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACTpI,OAAA;YACE2C,IAAI,EAAC,QAAQ;YACbiG,OAAO,EAAEA,CAAA,KAAM;cACbvH,eAAe,CAAC,KAAK,CAAC;cACtBF,uBAAuB,CAAC,IAAI,CAAC;YAC/B,CAAE;YACF4G,SAAS,EAAC,2KAA2K;YACrLY,QAAQ,EAAE,CAACrI,QAAS;YAAA0H,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpI,OAAA;QAAAgI,QAAA,gBACEhI,OAAA;UAAO+H,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpI,OAAA;UAAK+H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhI,OAAA;YACE2C,IAAI,EAAC,MAAM;YACX4F,KAAK,EAAE7H,aAAc;YACrB8H,QAAQ,EAAGpE,CAAC,IAAKzD,gBAAgB,CAACyD,CAAC,CAACC,MAAM,CAACkE,KAAK,CAAE;YAClDR,SAAS,EAAC,wJAAwJ;YAClKmB,WAAW,EAAC;UAAsB;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFpI,OAAA;YACE2C,IAAI,EAAC,QAAQ;YACbiG,OAAO,EAAEA,CAAA,KAAM;cACbvH,eAAe,CAAC,OAAO,CAAC;cACxBF,uBAAuB,CAAC,IAAI,CAAC;YAC/B,CAAE;YACF4G,SAAS,EAAC,2KAA2K;YAAAC,QAAA,EACtL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpI,OAAA;QAAAgI,QAAA,gBACEhI,OAAA;UAAO+H,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,kBAC9C,eAAAhI,OAAA;YAAM+H,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACRpI,OAAA;UAAK+H,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDhI,OAAA;YACE+H,SAAS,EAAE,sCAAsCnG,MAAM,CAAChB,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,iFAAkF;YAAAoH,QAAA,gBAErLhI,OAAA;cAAK+H,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC7DhI,OAAA;gBACE+H,SAAS,EAAC,iDAAiD;gBAC3DoB,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBACnBC,KAAK,EAAC,4BAA4B;gBAAAtB,QAAA,eAElChI,OAAA;kBACEuJ,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAC,GAAG;kBACfC,CAAC,EAAC;gBAAuF;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNpI,OAAA;gBAAG+H,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAC/EpH,IAAI,GAAGA,IAAI,CAAC6D,IAAI,GAAG;cAAyC;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpI,OAAA;cACE2C,IAAI,EAAC,MAAM;cACXoF,SAAS,EAAC,WAAW;cACrB4B,MAAM,EAAC,uCAAuC;cAC9CnB,QAAQ,EAAErE,gBAAiB;cAC3BsE,MAAM,EAAEA,CAAA,KAAM5E,aAAa,CAAC,MAAM,CAAE;cACpC6E,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACLxG,MAAM,CAAChB,IAAI,iBACVZ,OAAA;UAAG+H,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEpG,MAAM,CAAChB;QAAI;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC1D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpI,OAAA;QAAAgI,QAAA,eACEhI,OAAA;UACE2C,IAAI,EAAC,QAAQ;UACbgG,QAAQ,EAAE7H,WAAY;UACtBiH,SAAS,EAAE,sDACTjH,WAAW,GAAG,aAAa,GAAG,+BAA+B,sDACR;UAAAkH,QAAA,EAEtDlH,WAAW,GAAG,cAAc,GAAG;QAAiB;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL9G,YAAY,KAAK,MAAM,iBACtBtB,OAAA;QAAK+H,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhI,OAAA;UAAK+H,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDhI,OAAA;YAAM+H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAChD1G,YAAY,KAAK,WAAW,GAAG,cAAc,GAC7CA,YAAY,KAAK,YAAY,GAAG,eAAe,GAC/CA,YAAY,KAAK,SAAS,GAAG,iBAAiB,GAC9C;UAAe;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACPpI,OAAA;YAAM+H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,GAAExG,cAAc,EAAC,GAAC;UAAA;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACNpI,OAAA;UAAK+H,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDhI,OAAA;YACE+H,SAAS,EAAE,sBACTzG,YAAY,KAAK,OAAO,GAAG,YAAY,GACvCA,YAAY,KAAK,SAAS,GAAG,cAAc,GAAG,aAAa,EAC1D;YACHsI,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAGrI,cAAc;YAAI;UAAE;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA9G,YAAY,KAAK,SAAS,IAAII,eAAe,CAACiE,MAAM,GAAG,CAAC,iBACvD3F,OAAA;QAAK+H,SAAS,EAAC,uDAAuD;QAAAC,QAAA,gBACpEhI,OAAA;UAAI+H,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFpI,OAAA;UAAK+H,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAC5DtG,eAAe,CAACoI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC1C,GAAG,CAAC,CAACC,KAAK,EAAE0C,KAAK;YAAA,IAAAC,WAAA;YAAA,oBAC5ChK,OAAA;cAAiB+H,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC3EhI,OAAA;gBAAG+H,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACtCX,KAAK,CAAC4C,WAAW,KAAK,UAAU,GAC/B,QAAQ5C,KAAK,CAAC6C,IAAI,IAAI,KAAK,EAAE,GAC7B,WAAW7C,KAAK,CAAC8C,MAAM,IAAI,SAAS;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACJpI,OAAA;gBAAAgI,QAAA,IAAAgC,WAAA,GAAI3C,KAAK,CAACC,IAAI,cAAA0C,WAAA,uBAAVA,WAAA,CAAYI,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,GANjC2B,KAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CAAC;UAAA,CACP,CAAC,EACD1G,eAAe,CAACiE,MAAM,GAAG,CAAC,iBACzB3F,OAAA;YAAG+H,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,IAClD,EAACtG,eAAe,CAACiE,MAAM,GAAG,CAAC,EAAC,wBAChC;UAAA;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGNlH,oBAAoB,iBACnBlB,OAAA;MAAK+H,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FhI,OAAA;QAAK+H,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDhI,OAAA;UAAI+H,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GAAC,UAAQ,EAAC5G,YAAY,EAAC,WAAS;QAAA;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EpI,OAAA;UACE2C,IAAI,EAAC,MAAM;UACX4F,KAAK,EAAEvH,gBAAiB;UACxBwH,QAAQ,EAAGpE,CAAC,IAAKnD,mBAAmB,CAACmD,CAAC,CAACC,MAAM,CAACkE,KAAK,CAAE;UACrDR,SAAS,EAAC,uGAAuG;UACjHmB,WAAW,EAAE,SAAS9H,YAAY,gBAAiB;UACnDiJ,SAAS;QAAA;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACFpI,OAAA;UAAK+H,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrChI,OAAA;YACE4I,OAAO,EAAEA,CAAA,KAAMzH,uBAAuB,CAAC,KAAK,CAAE;YAC9C4G,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAC1E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpI,OAAA;YACE4I,OAAO,EAAEjE,uBAAwB;YACjCoD,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAC1E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAED,eAAenI,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}