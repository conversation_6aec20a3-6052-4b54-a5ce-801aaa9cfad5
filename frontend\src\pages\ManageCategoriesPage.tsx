import React, { useState, useEffect } from 'react';
import { 
  getCategories, 
  getWebsiteCategories,
  createCategory,
  createWebsiteCategory,
  updateCategory,
  deleteCategory,
  CategoryHierarchy,
  WebsiteCategory,
  CategoryCreateRequest,
  WebsiteCategoryCreateRequest
} from '../services/api';
import { Plus, Edit2, Trash2, Save, X } from 'lucide-react';

const ManageCategoriesPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'documents' | 'websites'>('documents');
  const [documentCategories, setDocumentCategories] = useState<CategoryHierarchy[]>([]);
  const [websiteCategories, setWebsiteCategories] = useState<WebsiteCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form states
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [newCategory, setNewCategory] = useState<CategoryCreateRequest>({
    name: '',
    description: '',
    parent_id: '',
    sort_order: 0
  });
  const [newWebsiteCategory, setNewWebsiteCategory] = useState<WebsiteCategoryCreateRequest>({
    name: '',
    description: '',
    sort_order: 0
  });

  useEffect(() => {
    loadCategories();
  }, [activeTab]);

  const loadCategories = async () => {
    setLoading(true);
    setError(null);
    try {
      if (activeTab === 'documents') {
        const data = await getCategories();
        setDocumentCategories(data);
      } else {
        const data = await getWebsiteCategories();
        setWebsiteCategories(data);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      setError('Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCategory = async () => {
    try {
      setError(null);
      if (activeTab === 'documents') {
        await createCategory(newCategory);
        setNewCategory({ name: '', description: '', parent_id: '', sort_order: 0 });
      } else {
        await createWebsiteCategory(newWebsiteCategory);
        setNewWebsiteCategory({ name: '', description: '', sort_order: 0 });
      }
      setShowAddForm(false);
      setSuccess('Category created successfully');
      loadCategories();
    } catch (error) {
      console.error('Error creating category:', error);
      setError(error instanceof Error ? error.message : 'Failed to create category');
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (!window.confirm('Are you sure you want to delete this category?')) return;

    try {
      setError(null);
      await deleteCategory(categoryId);
      setSuccess('Category deleted successfully');
      loadCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete category');
    }
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  const renderDocumentCategories = () => {
    const renderCategoryTree = (categories: CategoryHierarchy[], level = 0) => {
      return categories.map((category) => (
        <div key={category.id} className={`ml-${level * 4}`}>
          <div className="flex items-center justify-between p-3 border-b border-gray-200 hover:bg-gray-50">
            <div className="flex-1">
              <div className="flex items-center">
                <span className="font-medium text-gray-900">{category.name}</span>
                <span className="ml-2 text-xs text-gray-500">Level {category.level}</span>
              </div>
              {category.description && (
                <p className="text-sm text-gray-600 mt-1">{category.description}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">{category.full_path}</p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setEditingCategory(category.id)}
                className="p-1 text-blue-600 hover:text-blue-800"
                title="Edit category"
              >
                <Edit2 className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleDeleteCategory(category.id)}
                className="p-1 text-red-600 hover:text-red-800"
                title="Delete category"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
          {category.children && category.children.length > 0 && (
            <div className="ml-4">
              {renderCategoryTree(category.children, level + 1)}
            </div>
          )}
        </div>
      ));
    };

    return (
      <div className="space-y-2">
        {documentCategories.length > 0 ? (
          renderCategoryTree(documentCategories)
        ) : (
          <p className="text-gray-500 text-center py-8">No document categories found</p>
        )}
      </div>
    );
  };

  const renderWebsiteCategories = () => {
    return (
      <div className="space-y-2">
        {websiteCategories.length > 0 ? (
          websiteCategories.map((category) => (
            <div key={category.id} className="flex items-center justify-between p-3 border-b border-gray-200 hover:bg-gray-50">
              <div className="flex-1">
                <div className="flex items-center">
                  <span className="font-medium text-gray-900">{category.name}</span>
                  <span className="ml-2 text-xs text-gray-500">Order: {category.sort_order}</span>
                </div>
                {category.description && (
                  <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                )}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setEditingCategory(category.id)}
                  className="p-1 text-blue-600 hover:text-blue-800"
                  title="Edit category"
                >
                  <Edit2 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeleteCategory(category.id)}
                  className="p-1 text-red-600 hover:text-red-800"
                  title="Delete category"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-500 text-center py-8">No website categories found</p>
        )}
      </div>
    );
  };

  const renderAddForm = () => {
    if (activeTab === 'documents') {
      return (
        <div className="bg-gray-50 p-4 rounded-lg space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Add Document Category</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
              <input
                type="text"
                value={newCategory.name}
                onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Category name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Parent Category</label>
              <select
                value={newCategory.parent_id || ''}
                onChange={(e) => setNewCategory(prev => ({ ...prev, parent_id: e.target.value || undefined }))}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">No parent (top level)</option>
                {documentCategories.map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.full_path}
                  </option>
                ))}
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={newCategory.description || ''}
                onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="Category description"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
              <input
                type="number"
                value={newCategory.sort_order || 0}
                onChange={(e) => setNewCategory(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleCreateCategory}
              disabled={!newCategory.name.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              <Save className="h-4 w-4 mr-2" />
              Create Category
            </button>
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </button>
          </div>
        </div>
      );
    } else {
      return (
        <div className="bg-gray-50 p-4 rounded-lg space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Add Website Category</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
              <input
                type="text"
                value={newWebsiteCategory.name}
                onChange={(e) => setNewWebsiteCategory(prev => ({ ...prev, name: e.target.value }))}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Category name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
              <input
                type="number"
                value={newWebsiteCategory.sort_order || 0}
                onChange={(e) => setNewWebsiteCategory(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={newWebsiteCategory.description || ''}
                onChange={(e) => setNewWebsiteCategory(prev => ({ ...prev, description: e.target.value }))}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="Category description"
              />
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleCreateCategory}
              disabled={!newWebsiteCategory.name.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              <Save className="h-4 w-4 mr-2" />
              Create Category
            </button>
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </button>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      <div className="bg-white p-4 shadow-sm">
        <div className="container mx-auto">
          <h1 className="text-2xl font-bold text-gray-900">Manage Categories</h1>
          <p className="text-gray-600 mt-1">
            Organize your documents and websites with custom categories
          </p>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="container mx-auto max-w-4xl">
          {/* Messages */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex justify-between items-center">
              <span>{error}</span>
              <button onClick={clearMessages} className="text-red-500 hover:text-red-700">
                <X className="h-4 w-4" />
              </button>
            </div>
          )}

          {success && (
            <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded flex justify-between items-center">
              <span>{success}</span>
              <button onClick={clearMessages} className="text-green-500 hover:text-green-700">
                <X className="h-4 w-4" />
              </button>
            </div>
          )}

          {/* Tabs */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="border-b border-gray-200">
              <nav className="flex">
                <button
                  onClick={() => setActiveTab('documents')}
                  className={`px-6 py-3 text-sm font-medium ${
                    activeTab === 'documents'
                      ? 'border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Document Categories
                </button>
                <button
                  onClick={() => setActiveTab('websites')}
                  className={`px-6 py-3 text-sm font-medium ${
                    activeTab === 'websites'
                      ? 'border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Website Categories
                </button>
              </nav>
            </div>

            <div className="p-6">
              {/* Add Category Button */}
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold text-gray-900">
                  {activeTab === 'documents' ? 'Document Categories' : 'Website Categories'}
                </h2>
                <button
                  onClick={() => setShowAddForm(!showAddForm)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Category
                </button>
              </div>

              {/* Add Form */}
              {showAddForm && renderAddForm()}

              {/* Categories List */}
              {loading ? (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <p className="mt-2 text-gray-600">Loading categories...</p>
                </div>
              ) : (
                <div className="mt-6">
                  {activeTab === 'documents' ? renderDocumentCategories() : renderWebsiteCategories()}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManageCategoriesPage;
