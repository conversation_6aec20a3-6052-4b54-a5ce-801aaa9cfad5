"""
Test script for Supabase storage functionality.
"""
import os
import logging
import json
from typing import List, Dict, Any
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# No need to add parent directory to path since we're in the same directory

# Import Supabase client
from supabase_client import supabase

def generate_mock_embedding(dimension=768) -> List[float]:
    """Generate a mock embedding vector for testing."""
    return list(np.random.rand(dimension))

def test_clear_database():
    """Test clearing the database."""
    logger.info("Testing clear_all_data...")
    result = supabase.clear_all_data()
    logger.info(f"Clear result: {result}")
    return result

def test_store_document():
    """Test storing a document."""
    logger.info("Testing store_document...")
    result = supabase.store_document(
        filename="test_document.pdf",
        display_name="Test Document",
        file_path="documents/test_document.pdf",
        file_type="pdf",
        file_size=1024,
        main_category="Test",
        category="Test",
        sub_category="Test",
        minor_category="Test",
        uploaded_by="test_user"
    )
    logger.info(f"Document result: {result}")
    return result

def test_store_document_chunk(document_id: str):
    """Test storing a document chunk."""
    logger.info("Testing store_document_chunk...")
    result = supabase.store_document_chunk(
        document_id=document_id,
        chunk_index=0,
        page_number=1,
        text="This is a test document chunk.",
        embedding=generate_mock_embedding(),
        metadata={"test": "metadata"}
    )
    logger.info(f"Document chunk result: {result}")
    return result

def test_store_website():
    """Test storing a website."""
    logger.info("Testing store_website...")
    result = supabase.store_website(
        url="https://example.com",
        domain="example.com",
        title="Example Website",
        description="This is an example website.",
        category="Test",
        submitted_by="test_user"
    )
    logger.info(f"Website result: {result}")
    return result

def test_store_website_chunk(website_id: str):
    """Test storing a website chunk."""
    logger.info("Testing store_website_chunk...")
    result = supabase.store_website_chunk(
        website_id=website_id,
        chunk_index=0,
        text="This is a test website chunk.",
        embedding=generate_mock_embedding(),
        metadata={"test": "metadata"}
    )
    logger.info(f"Website chunk result: {result}")
    return result

def test_search_documents():
    """Test searching for documents."""
    logger.info("Testing search_documents...")
    result = supabase.search_documents(
        query_embedding=generate_mock_embedding(),
        match_threshold=0.4,
        match_count=10
    )
    logger.info(f"Search documents result: {result}")
    return result

def test_search_websites():
    """Test searching for websites."""
    logger.info("Testing search_websites...")
    result = supabase.search_websites(
        query_embedding=generate_mock_embedding(),
        match_threshold=0.4,
        match_count=10
    )
    logger.info(f"Search websites result: {result}")
    return result

def run_all_tests():
    """Run all tests."""
    logger.info("Running all tests...")

    # Clear the database
    test_clear_database()

    # Store a document
    document_result = test_store_document()
    document_id = document_result.get("id")

    # Store a document chunk
    if document_id:
        test_store_document_chunk(document_id)

    # Store a website
    website_result = test_store_website()
    website_id = website_result.get("id")

    # Store a website chunk
    if website_id:
        test_store_website_chunk(website_id)

    # Search for documents and websites
    test_search_documents()
    test_search_websites()

    logger.info("All tests completed.")

if __name__ == "__main__":
    run_all_tests()
