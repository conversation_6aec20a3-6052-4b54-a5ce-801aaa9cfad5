"""
Simple script to test document search in RailGPT.
"""
import os
import logging
import json
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def test_document_search():
    """Test document search."""
    logger.info("Testing document search...")
    
    # Test query
    query = "ACP"
    
    # Search for documents by title
    logger.info(f"Searching for documents with title containing '{query}'...")
    
    # Prepare the query
    search_query = f"""
    SELECT
        d.id,
        d.display_name,
        d.file_path,
        d.file_type,
        d.created_at,
        d.updated_at
    FROM
        documents d
    WHERE
        LOWER(d.display_name) LIKE LOWER('%{query}%')
    LIMIT 5
    """
    
    try:
        result = supabase.execute_query(search_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by title: {result['error']}")
        else:
            logger.info(f"Found {len(result)} documents matching '{query}'")
            
            # Print document details
            for i, doc in enumerate(result):
                logger.info(f"Document {i+1}:")
                logger.info(f"  ID: {doc.get('id')}")
                logger.info(f"  Name: {doc.get('display_name')}")
                logger.info(f"  Path: {doc.get('file_path')}")
                logger.info(f"  Type: {doc.get('file_type')}")
                logger.info("")
    except Exception as e:
        logger.error(f"Error searching documents by title: {str(e)}")

def test_document_chunks():
    """Test document chunks."""
    logger.info("Testing document chunks...")
    
    # Test document ID
    document_id = "6b40bb6b-3c87-4a9f-beac-b2c164516b27"  # ACP document
    
    # Get chunks for the document
    logger.info(f"Getting chunks for document {document_id}...")
    
    # Prepare the query
    chunks_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.document_id = '{document_id}'
    ORDER BY
        dc.page_number, dc.chunk_index
    LIMIT 3
    """
    
    try:
        result = supabase.execute_query(chunks_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error getting document chunks: {result['error']}")
        else:
            logger.info(f"Found {len(result)} chunks for document {document_id}")
            
            # Print chunk details
            for i, chunk in enumerate(result):
                logger.info(f"Chunk {i+1}:")
                logger.info(f"  ID: {chunk.get('id')}")
                logger.info(f"  Document ID: {chunk.get('document_id')}")
                logger.info(f"  Chunk Index: {chunk.get('chunk_index')}")
                logger.info(f"  Page Number: {chunk.get('page_number')}")
                logger.info(f"  Filename: {chunk.get('filename')}")
                logger.info(f"  URL: {chunk.get('url')}")
                logger.info(f"  Text: {chunk.get('text')[:100]}..." if chunk.get('text') else "  Text: None")
                logger.info("")
    except Exception as e:
        logger.error(f"Error getting document chunks: {str(e)}")

def test_website_chunks():
    """Test website chunks."""
    logger.info("Testing website chunks...")
    
    # Test query
    query = "railway"
    
    # Search for website chunks
    logger.info(f"Searching for website chunks with content containing '{query}'...")
    
    # Prepare the query
    search_query = f"""
    SELECT
        wc.id,
        wc.website_id,
        wc.chunk_index,
        wc.text,
        wc.url,
        w.name as website_name
    FROM
        website_chunks wc
    JOIN
        websites w ON wc.website_id = w.id
    WHERE
        wc.text ILIKE '%{query}%'
    LIMIT 3
    """
    
    try:
        result = supabase.execute_query(search_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching website chunks: {result['error']}")
        else:
            logger.info(f"Found {len(result)} website chunks matching '{query}'")
            
            # Print chunk details
            for i, chunk in enumerate(result):
                logger.info(f"Chunk {i+1}:")
                logger.info(f"  ID: {chunk.get('id')}")
                logger.info(f"  Website ID: {chunk.get('website_id')}")
                logger.info(f"  Website Name: {chunk.get('website_name')}")
                logger.info(f"  URL: {chunk.get('url')}")
                logger.info(f"  Text: {chunk.get('text')[:100]}..." if chunk.get('text') else "  Text: None")
                logger.info("")
    except Exception as e:
        logger.error(f"Error searching website chunks: {str(e)}")

def main():
    """Main function to test search."""
    test_document_search()
    test_document_chunks()
    test_website_chunks()

if __name__ == "__main__":
    main()
