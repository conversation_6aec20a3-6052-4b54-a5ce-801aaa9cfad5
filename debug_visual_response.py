#!/usr/bin/env python3
"""
Debug script to check what visual content data is being returned
"""

import requests
import json

def debug_visual_response():
    """Debug what visual content data is being returned"""
    print("🔍 Debugging Visual Content Response")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # Test query that should return visual content
    query = "What is the price for Quotation 1 and show me Image of Project 1"
    
    print(f"📤 Testing query: {query}")
    
    try:
        response = requests.post(
            f"{base_url}/api/query",
            json={'query': query, 'model': 'gemini-2.0-flash'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Response received!")
            print(f"📊 Visual content found: {result.get('visual_content_found', False)}")
            print(f"🎨 Visual content types: {result.get('visual_content_types', [])}")
            
            # Check sources structure
            sources = result.get('sources', [])
            print(f"\n📄 Total sources: {len(sources)}")
            
            for i, source in enumerate(sources):
                print(f"\n--- Source {i+1} ---")
                print(f"Type: {type(source)}")
                
                if isinstance(source, dict):
                    print(f"Keys: {list(source.keys())}")
                    print(f"Source type: {source.get('source_type', 'unknown')}")
                    print(f"Content type: {source.get('content_type', 'text')}")
                    print(f"Has visual content: {'visual_content' in source}")
                    
                    if 'visual_content' in source:
                        visual_content = source['visual_content']
                        print(f"Visual content type: {type(visual_content)}")
                        if isinstance(visual_content, dict):
                            print(f"Visual content keys: {list(visual_content.keys())}")
                            
                            # Check for table data
                            if 'table_data' in visual_content:
                                table_data = visual_content['table_data']
                                print(f"Table data: {len(table_data) if isinstance(table_data, list) else 'Not a list'}")
                                if isinstance(table_data, list) and len(table_data) > 0:
                                    print(f"First row: {table_data[0]}")
                            
                            # Check for markdown table
                            if 'markdown_table' in visual_content:
                                md_table = visual_content['markdown_table']
                                print(f"Markdown table length: {len(md_table) if isinstance(md_table, str) else 'Not a string'}")
                                if isinstance(md_table, str):
                                    print(f"Markdown preview: {md_table[:100]}...")
                            
                            # Check for image data
                            if 'base64_data' in visual_content:
                                print(f"Has base64 image data: {len(visual_content['base64_data'])} chars")
                            
                            if 'width' in visual_content and 'height' in visual_content:
                                print(f"Image dimensions: {visual_content['width']}x{visual_content['height']}")
                    
                    if 'storage_url' in source:
                        print(f"Storage URL: {source['storage_url']}")
                    
                    if 'display_type' in source:
                        print(f"Display type: {source['display_type']}")
                else:
                    print(f"Source is string: {source}")
            
            # Save full response for analysis
            with open('debug_response.json', 'w') as f:
                json.dump(result, f, indent=2)
            print(f"\n💾 Full response saved to debug_response.json")
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_visual_response() 