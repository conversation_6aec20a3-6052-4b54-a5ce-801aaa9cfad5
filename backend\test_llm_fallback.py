#!/usr/bin/env python3
"""
Test script to verify LLM fallback functionality.

This tests that:
1. Relevant queries find document/website content (fallback=false)
2. Irrelevant queries trigger LLM fallback (fallback=true)
"""

import requests
import json
import time

# Configuration
API_URL = "http://localhost:8000"

def test_query(query, expected_fallback, description):
    """Test a single query and check the fallback behavior."""
    print(f"\n🧪 Testing: {description}")
    print(f"Query: '{query}'")
    print(f"Expected LLM fallback: {expected_fallback}")
    
    try:
        response = requests.post(
            f"{API_URL}/api/query",
            headers={"Content-Type": "application/json"},
            json={
                "query": query,
                "model": "gemini",
                "fallback_enabled": True
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            actual_fallback = data.get("llm_fallback", False)
            doc_answer = data.get("document_answer")
            web_answer = data.get("website_answer")
            
            print(f"✅ Actual LLM fallback: {actual_fallback}")
            print(f"✅ Document answer available: {doc_answer is not None}")
            print(f"✅ Website answer available: {web_answer is not None}")
            
            if actual_fallback == expected_fallback:
                print(f"✅ SUCCESS: Fallback behavior matches expectation")
                return True
            else:
                print(f"❌ FAILURE: Expected fallback={expected_fallback}, got {actual_fallback}")
                return False
        else:
            print(f"❌ API Error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def main():
    print("🚀 Testing LLM Fallback Functionality")
    print("="*50)
    
    # Test 1: Railway-related query (should find content, fallback=false)
    test_query(
        "FSDS monitoring system",
        expected_fallback=False,
        description="Railway-specific query - should find document content"
    )
    
    # Test 2: Completely irrelevant query (should trigger LLM fallback)
    test_query(
        "How to bake a chocolate cake recipe ingredients",
        expected_fallback=True,
        description="Cooking query - should trigger LLM fallback"
    )
    
    # Test 3: Another irrelevant query
    test_query(
        "Best programming languages for web development",
        expected_fallback=True,
        description="Programming query - should trigger LLM fallback"
    )
    
    # Test 4: Generic transport query (might find website content)
    test_query(
        "public transportation safety",
        expected_fallback=False,
        description="Transport query - might find content in websites"
    )
    
    print("\n" + "="*50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    main() 
"""
Test script to verify LLM fallback functionality.

This tests that:
1. Relevant queries find document/website content (fallback=false)
2. Irrelevant queries trigger LLM fallback (fallback=true)
"""

import requests
import json
import time

# Configuration
API_URL = "http://localhost:8000"

def test_query(query, expected_fallback, description):
    """Test a single query and check the fallback behavior."""
    print(f"\n🧪 Testing: {description}")
    print(f"Query: '{query}'")
    print(f"Expected LLM fallback: {expected_fallback}")
    
    try:
        response = requests.post(
            f"{API_URL}/api/query",
            headers={"Content-Type": "application/json"},
            json={
                "query": query,
                "model": "gemini",
                "fallback_enabled": True
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            actual_fallback = data.get("llm_fallback", False)
            doc_answer = data.get("document_answer")
            web_answer = data.get("website_answer")
            
            print(f"✅ Actual LLM fallback: {actual_fallback}")
            print(f"✅ Document answer available: {doc_answer is not None}")
            print(f"✅ Website answer available: {web_answer is not None}")
            
            if actual_fallback == expected_fallback:
                print(f"✅ SUCCESS: Fallback behavior matches expectation")
                return True
            else:
                print(f"❌ FAILURE: Expected fallback={expected_fallback}, got {actual_fallback}")
                return False
        else:
            print(f"❌ API Error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def main():
    print("🚀 Testing LLM Fallback Functionality")
    print("="*50)
    
    # Test 1: Railway-related query (should find content, fallback=false)
    test_query(
        "FSDS monitoring system",
        expected_fallback=False,
        description="Railway-specific query - should find document content"
    )
    
    # Test 2: Completely irrelevant query (should trigger LLM fallback)
    test_query(
        "How to bake a chocolate cake recipe ingredients",
        expected_fallback=True,
        description="Cooking query - should trigger LLM fallback"
    )
    
    # Test 3: Another irrelevant query
    test_query(
        "Best programming languages for web development",
        expected_fallback=True,
        description="Programming query - should trigger LLM fallback"
    )
    
    # Test 4: Generic transport query (might find website content)
    test_query(
        "public transportation safety",
        expected_fallback=False,
        description="Transport query - might find content in websites"
    )
    
    print("\n" + "="*50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    main() 