#!/bin/bash

# RailGPT Frontend Deployment to Google Cloud Storage + CDN
# This script deploys the React frontend to Cloud Storage with CDN

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
PROJECT_ID=${PROJECT_ID:-"railgpt-production"}
BUCKET_NAME=${BUCKET_NAME:-"railgpt-frontend-$(date +%s)"}
REGION=${REGION:-"us-central1"}
BACKEND_URL=${BACKEND_URL:-""}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v gcloud &> /dev/null; then
        print_error "Google Cloud SDK not found. Please install it first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "Node.js/npm not found. Please install it first."
        exit 1
    fi
    
    # Check if logged in to gcloud
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        print_error "Not logged in to Google Cloud. Run 'gcloud auth login' first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Setup Google Cloud project
setup_project() {
    print_status "Setting up Google Cloud project..."
    
    # Set project
    gcloud config set project $PROJECT_ID
    
    # Enable required APIs
    print_status "Enabling required APIs..."
    gcloud services enable storage.googleapis.com
    gcloud services enable compute.googleapis.com
    
    print_success "Project setup completed"
}

# Get backend URL if not provided
get_backend_url() {
    if [ -z "$BACKEND_URL" ]; then
        print_status "Getting backend URL from Cloud Run..."
        
        # Try to get the backend service URL
        BACKEND_URL=$(gcloud run services describe railgpt-backend --region $REGION --format="value(status.url)" 2>/dev/null || echo "")
        
        if [ -z "$BACKEND_URL" ]; then
            print_warning "Backend service not found. Using placeholder URL."
            BACKEND_URL="https://railgpt-backend-xxx.a.run.app"
            print_warning "Please update REACT_APP_API_URL after backend deployment"
        else
            print_success "Found backend URL: $BACKEND_URL"
        fi
    fi
}

# Create storage bucket
create_bucket() {
    print_status "Creating Cloud Storage bucket..."
    
    # Check if bucket already exists
    if gsutil ls -b gs://$BUCKET_NAME &> /dev/null; then
        print_warning "Bucket $BUCKET_NAME already exists"
    else
        # Create bucket
        gsutil mb -p $PROJECT_ID -c STANDARD -l $REGION gs://$BUCKET_NAME
        print_success "Bucket created: gs://$BUCKET_NAME"
    fi
    
    # Configure bucket for website hosting
    gsutil web set -m index.html -e index.html gs://$BUCKET_NAME
    
    # Make bucket publicly readable
    gsutil iam ch allUsers:objectViewer gs://$BUCKET_NAME
    
    print_success "Bucket configured for website hosting"
}

# Build frontend
build_frontend() {
    print_status "Building frontend..."
    
    # Navigate to frontend directory
    cd "$(dirname "$0")/../../frontend"
    
    # Install dependencies
    print_status "Installing dependencies..."
    npm install
    
    # Set environment variables for build
    export REACT_APP_API_URL=$BACKEND_URL
    export REACT_APP_SUPABASE_URL="https://rkllidjktazafeinezgo.supabase.co"
    export REACT_APP_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA"
    
    # Build the application
    print_status "Building React application..."
    npm run build
    
    print_success "Frontend build completed"
}

# Deploy to Cloud Storage
deploy_to_storage() {
    print_status "Deploying to Cloud Storage..."
    
    # Upload files to bucket
    gsutil -m rsync -r -d build/ gs://$BUCKET_NAME/
    
    # Set cache headers for static assets
    print_status "Setting cache headers..."
    
    # Cache static assets for 1 year
    gsutil -m setmeta -h "Cache-Control:public, max-age=31536000, immutable" gs://$BUCKET_NAME/static/**
    
    # Don't cache HTML files
    gsutil -m setmeta -h "Cache-Control:public, max-age=0, must-revalidate" gs://$BUCKET_NAME/index.html
    gsutil -m setmeta -h "Cache-Control:public, max-age=0, must-revalidate" gs://$BUCKET_NAME/manifest.json
    
    # Set content types
    gsutil -m setmeta -h "Content-Type:text/html" gs://$BUCKET_NAME/index.html
    gsutil -m setmeta -h "Content-Type:application/json" gs://$BUCKET_NAME/manifest.json
    
    print_success "Files deployed to Cloud Storage"
}

# Setup Cloud CDN
setup_cdn() {
    print_status "Setting up Cloud CDN..."
    
    # Create backend bucket
    BACKEND_BUCKET_NAME="railgpt-frontend-bucket"
    
    if gcloud compute backend-buckets describe $BACKEND_BUCKET_NAME --global &> /dev/null; then
        print_warning "Backend bucket $BACKEND_BUCKET_NAME already exists"
    else
        gcloud compute backend-buckets create $BACKEND_BUCKET_NAME \
            --bucket-name=$BUCKET_NAME \
            --enable-cdn \
            --cache-mode=CACHE_ALL_STATIC
        
        print_success "Backend bucket created with CDN enabled"
    fi
    
    # Create URL map if it doesn't exist
    URL_MAP_NAME="railgpt-url-map"
    
    if gcloud compute url-maps describe $URL_MAP_NAME --global &> /dev/null; then
        print_warning "URL map $URL_MAP_NAME already exists"
    else
        gcloud compute url-maps create $URL_MAP_NAME \
            --default-backend-bucket=$BACKEND_BUCKET_NAME
        
        print_success "URL map created"
    fi
}

# Setup HTTPS load balancer
setup_load_balancer() {
    if [ ! -z "$DOMAIN" ]; then
        print_status "Setting up HTTPS load balancer for domain: $DOMAIN"
        
        # Create SSL certificate
        SSL_CERT_NAME="railgpt-ssl-cert"
        
        if gcloud compute ssl-certificates describe $SSL_CERT_NAME --global &> /dev/null; then
            print_warning "SSL certificate $SSL_CERT_NAME already exists"
        else
            gcloud compute ssl-certificates create $SSL_CERT_NAME \
                --domains=$DOMAIN
            
            print_success "SSL certificate created (will be provisioned automatically)"
        fi
        
        # Create target HTTPS proxy
        HTTPS_PROXY_NAME="railgpt-https-proxy"
        
        if gcloud compute target-https-proxies describe $HTTPS_PROXY_NAME --global &> /dev/null; then
            print_warning "HTTPS proxy $HTTPS_PROXY_NAME already exists"
        else
            gcloud compute target-https-proxies create $HTTPS_PROXY_NAME \
                --url-map=railgpt-url-map \
                --ssl-certificates=$SSL_CERT_NAME
            
            print_success "HTTPS proxy created"
        fi
        
        # Create forwarding rule
        FORWARDING_RULE_NAME="railgpt-https-rule"
        
        if gcloud compute forwarding-rules describe $FORWARDING_RULE_NAME --global &> /dev/null; then
            print_warning "Forwarding rule $FORWARDING_RULE_NAME already exists"
        else
            gcloud compute forwarding-rules create $FORWARDING_RULE_NAME \
                --global \
                --target-https-proxy=$HTTPS_PROXY_NAME \
                --ports=443
            
            print_success "HTTPS forwarding rule created"
        fi
        
        # Get load balancer IP
        LB_IP=$(gcloud compute forwarding-rules describe $FORWARDING_RULE_NAME --global --format="value(IPAddress)")
        
        print_success "Load balancer IP: $LB_IP"
        print_status "Please configure your DNS:"
        print_status "  A record: $DOMAIN → $LB_IP"
        
    else
        print_status "No domain specified. Skipping HTTPS load balancer setup."
        print_status "Website available at: https://storage.googleapis.com/$BUCKET_NAME/index.html"
    fi
}

# Test deployment
test_deployment() {
    print_status "Testing deployment..."
    
    if [ ! -z "$DOMAIN" ]; then
        WEBSITE_URL="https://$DOMAIN"
    else
        WEBSITE_URL="https://storage.googleapis.com/$BUCKET_NAME/index.html"
    fi
    
    print_status "Website URL: $WEBSITE_URL"
    
    # Test if website is accessible
    if curl -f "$WEBSITE_URL" > /dev/null 2>&1; then
        print_success "Website is accessible"
    else
        print_warning "Website might not be accessible yet. DNS propagation can take time."
    fi
}

# Main execution
main() {
    echo "========================================="
    echo "   RailGPT Frontend GCP Deployment      "
    echo "========================================="
    echo
    
    print_status "Project ID: $PROJECT_ID"
    print_status "Bucket Name: $BUCKET_NAME"
    print_status "Region: $REGION"
    echo
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --project)
                PROJECT_ID="$2"
                shift 2
                ;;
            --bucket)
                BUCKET_NAME="$2"
                shift 2
                ;;
            --region)
                REGION="$2"
                shift 2
                ;;
            --backend-url)
                BACKEND_URL="$2"
                shift 2
                ;;
            --domain)
                DOMAIN="$2"
                shift 2
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --project PROJECT_ID    Google Cloud project ID"
                echo "  --bucket BUCKET_NAME    Cloud Storage bucket name"
                echo "  --region REGION         Deployment region"
                echo "  --backend-url URL       Backend API URL"
                echo "  --domain DOMAIN         Custom domain for HTTPS"
                echo "  --help                  Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run deployment steps
    check_prerequisites
    setup_project
    get_backend_url
    create_bucket
    build_frontend
    deploy_to_storage
    setup_cdn
    
    if [ ! -z "$DOMAIN" ]; then
        setup_load_balancer
    fi
    
    test_deployment
    
    echo
    print_success "Frontend deployment completed successfully!"
    echo
    
    if [ ! -z "$DOMAIN" ]; then
        print_status "Website URL: https://$DOMAIN"
        print_status "Load Balancer IP: $(gcloud compute forwarding-rules describe railgpt-https-rule --global --format="value(IPAddress)" 2>/dev/null || echo "Not created")"
    else
        print_status "Website URL: https://storage.googleapis.com/$BUCKET_NAME/index.html"
    fi
    
    print_status "Cloud Storage Bucket: gs://$BUCKET_NAME"
    print_status "Backend API URL: $BACKEND_URL"
    echo
    print_status "Next steps:"
    echo "  1. Configure DNS if using custom domain"
    echo "  2. Wait for SSL certificate provisioning (up to 24 hours)"
    echo "  3. Test all application functionality"
    echo "  4. Set up monitoring and alerts"
    echo
    print_status "Useful commands:"
    echo "  Update website: gsutil -m rsync -r -d build/ gs://$BUCKET_NAME/"
    echo "  View bucket: gsutil ls -la gs://$BUCKET_NAME/"
    echo "  Delete bucket: gsutil rm -r gs://$BUCKET_NAME/"
}

# Run main function with all arguments
main "$@"
