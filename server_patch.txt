# Patch for server.py to fix the 'similarity' and 'text' errors

# 1. Add error handling for similarity and text in find_similar_chunks function
# Find this code in the find_similar_chunks function:

            # Filter by relevance threshold
            similar_chunks = [chunk for chunk in similar_chunks if chunk["similarity"] >= RELEVANCE_THRESHOLD]

# Add this code before it:

            # Add default similarity value if missing
            for chunk in similar_chunks:
                if "similarity" not in chunk:
                    chunk["similarity"] = 0.5
                    logger.warning(f"Added default similarity value to chunk {chunk.get('id', 'unknown')}")
                
                # Ensure text field exists and is a string
                if "text" not in chunk or not isinstance(chunk["text"], str):
                    # Add a default text value if missing or not a string
                    chunk["text"] = "No text content available"
                    logger.warning(f"Added default text value to chunk {chunk.get('id', 'unknown')}")

# 2. Add error handling for text in generate_llm_answer function
# Find this code in the generate_llm_answer function:

    try:
        # Prepare context from similar chunks
        context_texts = []
        sources = []
        document_sources = []
        website_sources = []

# Add this code after it:

        # Ensure all chunks have valid text
        valid_chunks = []
        for chunk in similar_chunks:
            # Ensure text field exists and is a string
            if "text" not in chunk or not isinstance(chunk["text"], str):
                # Skip this chunk
                logger.warning(f"Skipping chunk with missing or invalid text: {chunk.get('id', 'unknown')}")
                continue
            valid_chunks.append(chunk)
        
        # Use only valid chunks
        similar_chunks = valid_chunks
        
        # If no valid chunks, return early
        if not similar_chunks:
            logger.warning("No valid chunks found for LLM answer generation")
            return "I couldn't find any valid information to answer your question.", [], [], []
