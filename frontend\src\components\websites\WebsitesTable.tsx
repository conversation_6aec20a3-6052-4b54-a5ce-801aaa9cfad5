import React, { useState } from 'react';
import { Website } from '../../types/websites';

interface WebsitesTableProps {
  websites: Website[];
  onView: (website: Website) => void;
  onRetry: (website: Website) => void;
  onDelete: (website: Website) => void;
  onCategoryUpdate?: (updatedWebsite: Website) => void;
}

const WebsitesTable: React.FC<WebsitesTableProps> = ({
  websites,
  onView,
  onRetry,
  onDelete,
  onCategoryUpdate,
}) => {
  const [sortField, setSortField] = useState<keyof Website>('extractedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [domainFilter, setDomainFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  // Handle sorting
  const handleSort = (field: keyof Website) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Filter websites based on search query and filters
  const filteredWebsites = websites.filter((site) => {
    // Search query filter (URLs or domains)
    if (
      searchQuery &&
      !site.url.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !site.domain.toLowerCase().includes(searchQuery.toLowerCase())
    ) {
      return false;
    }

    // Domain filter
    if (
      domainFilter &&
      site.domain !== domainFilter &&
      site.domainCategory !== domainFilter
    ) {
      return false;
    }

    // Status filter
    if (statusFilter && site.status !== statusFilter) {
      return false;
    }

    return true;
  });

  // Sort websites
  const sortedWebsites = [...filteredWebsites].sort((a, b) => {
    // Use optional chaining and nullish coalescing to handle undefined
    const aValue = a[sortField] ?? '';
    const bValue = b[sortField] ?? '';
    
    if (aValue < bValue) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });

  // Format the date for display
  const formatDate = (dateStr: string | null | undefined) => {
    if (!dateStr) return 'N/A';
    
    try {
      // Check if dateStr is a valid date
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      
      return new Intl.DateTimeFormat('en-IN', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  // Render status badge
  const renderStatusBadge = (status: Website['status']) => {
    let bgColor;
    switch (status) {
      case 'Success':
        bgColor = 'bg-green-100 text-green-800';
        break;
      case 'Failed':
        bgColor = 'bg-red-100 text-red-800';
        break;
      case 'Manual Required':
        bgColor = 'bg-yellow-100 text-yellow-800';
        break;
      default:
        bgColor = 'bg-gray-100 text-gray-800';
    }

    return (
      <span className={`${bgColor} px-2 py-1 rounded-full text-xs font-medium`}>
        {status}
      </span>
    );
  };

  // Get unique domains for filtering
  const uniqueDomains = Array.from(
    new Set(
      websites.flatMap((site) => [
        site.domain,
        site.domainCategory || '',
      ]).filter(Boolean)
    )
  );

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold">Manage Websites</h2>
        
        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row gap-4 mt-4">
          <div className="md:w-1/3">
            <input
              type="text"
              placeholder="Search URLs or domains..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="md:w-1/3">
            <select
              value={domainFilter}
              onChange={(e) => setDomainFilter(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Domains</option>
              {uniqueDomains.map((domain) => (
                <option key={domain as string} value={domain as string}>
                  {domain as string}
                </option>
              ))}
            </select>
          </div>
          
          <div className="md:w-1/3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="Success">Success</option>
              <option value="Failed">Failed</option>
              <option value="Manual Required">Manual Required</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('url')}
              >
                URL
                {sortField === 'url' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('domain')}
              >
                Domain
                {sortField === 'domain' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('extractedAt')}
              >
                Extracted At
                {sortField === 'extractedAt' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('status')}
              >
                Status
                {sortField === 'status' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedWebsites.length > 0 ? (
              sortedWebsites.map((website) => (
                <tr key={website.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-blue-600 hover:underline">
                      <a href={website.url} target="_blank" rel="noopener noreferrer">
                        {website.url.length > 50 ? `${website.url.substring(0, 50)}...` : website.url}
                      </a>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {website.domain}
                    </div>
                    {website.domainCategory && (
                      <div className="text-xs text-gray-500">
                        {website.domainCategory}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(website.extractedAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderStatusBadge(website.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-3">
                      <button
                        onClick={() => onView(website)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View
                      </button>
                      <button
                        onClick={() => onRetry(website)}
                        className="text-yellow-600 hover:text-yellow-900"
                      >
                        Retry
                      </button>
                      <button
                        onClick={() => onDelete(website)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                  No websites found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default WebsitesTable;
