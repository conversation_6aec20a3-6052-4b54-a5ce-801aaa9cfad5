{"ast": null, "code": "// Category Management API Service\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// API Response interfaces\n\n// Get all categories with hierarchy\nexport const getCategories = async () => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`);\n    if (!response.ok) {\n      if (response.status === 0 || !response.status) {\n        console.warn('Backend server not running. Categories will be empty until server starts.');\n        return [];\n      }\n      throw new Error(`Failed to fetch categories: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    var _error$message;\n    if ((_error$message = error.message) !== null && _error$message !== void 0 && _error$message.includes('fetch') || error.name === 'TypeError') {\n      console.warn('Backend server not available. Categories will be empty until server starts.');\n      return [];\n    }\n    console.error('Error fetching categories:', error);\n    throw error;\n  }\n};\n\n// Get categories by type\nexport const getCategoriesByType = async type => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/by-type/${type}`);\n    if (!response.ok) {\n      throw new Error(`Failed to fetch categories by type: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error fetching categories by type:', error);\n    throw error;\n  }\n};\n\n// Get categories by parent\nexport const getCategoriesByParent = async parentId => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/by-parent/${parentId}`);\n    if (!response.ok) {\n      throw new Error(`Failed to fetch categories by parent: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error fetching categories by parent:', error);\n    throw error;\n  }\n};\n\n// Create new category\nexport const createCategory = async category => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(category)\n    });\n    if (!response.ok) {\n      if (response.status === 0 || !response.status) {\n        throw new Error('Backend server not running. Please start the backend server to create categories.');\n      }\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to create category: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    var _error$message2;\n    if ((_error$message2 = error.message) !== null && _error$message2 !== void 0 && _error$message2.includes('fetch') || error.name === 'TypeError') {\n      throw new Error('Backend server not available. Please start the backend server to create categories.');\n    }\n    console.error('Error creating category:', error);\n    throw error;\n  }\n};\n\n// Update category\nexport const updateCategory = async (categoryId, categoryUpdate) => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(categoryUpdate)\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to update category: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating category:', error);\n    throw error;\n  }\n};\n\n// Delete category (soft delete)\nexport const deleteCategory = async categoryId => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'DELETE'\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to delete category: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    throw error;\n  }\n};\n\n// Update document categories\nexport const updateDocumentCategories = async (documentId, categoryUpdate) => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/documents/${documentId}/categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(categoryUpdate)\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to update document categories: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating document categories:', error);\n    throw error;\n  }\n};\n\n// Get category path\nexport const getCategoryPath = async categoryId => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}/path`);\n    if (!response.ok) {\n      throw new Error(`Failed to get category path: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data.path;\n  } catch (error) {\n    console.error('Error getting category path:', error);\n    throw error;\n  }\n};\n\n// Helper function to build category hierarchy tree from flat list\nexport const buildCategoryTree = categories => {\n  const categoryMap = new Map();\n  const rootCategories = [];\n\n  // First pass: create map of all categories\n  categories.forEach(category => {\n    categoryMap.set(category.id, {\n      ...category,\n      children: []\n    });\n  });\n\n  // Second pass: build tree structure\n  categories.forEach(category => {\n    const categoryNode = categoryMap.get(category.id);\n    if (category.parent_id) {\n      const parent = categoryMap.get(category.parent_id);\n      if (parent) {\n        parent.children = parent.children || [];\n        parent.children.push(categoryNode);\n      }\n    } else {\n      rootCategories.push(categoryNode);\n    }\n  });\n\n  // Sort categories by sort_order and name\n  const sortCategories = cats => {\n    cats.sort((a, b) => {\n      if (a.sort_order !== b.sort_order) {\n        return a.sort_order - b.sort_order;\n      }\n      return a.name.localeCompare(b.name);\n    });\n    cats.forEach(cat => {\n      if (cat.children && cat.children.length > 0) {\n        sortCategories(cat.children);\n      }\n    });\n  };\n  sortCategories(rootCategories);\n  return rootCategories;\n};\n\n// Helper function to get all categories of a specific type from hierarchy\nexport const getCategoriesOfType = (categories, type) => {\n  const result = [];\n  const traverse = cats => {\n    cats.forEach(cat => {\n      if (cat.type === type) {\n        result.push(cat);\n      }\n      if (cat.children) {\n        traverse(cat.children);\n      }\n    });\n  };\n  traverse(categories);\n  return result;\n};\n\n// Helper function to find category by ID in hierarchy\nexport const findCategoryById = (categories, id) => {\n  for (const category of categories) {\n    if (category.id === id) {\n      return category;\n    }\n    if (category.children) {\n      const found = findCategoryById(category.children, id);\n      if (found) {\n        return found;\n      }\n    }\n  }\n  return null;\n};\n\n// Helper function to get category options for dropdowns\nexport const getCategoryOptions = (categories, type, parentId) => {\n  const options = [];\n  const traverse = (cats, level = 0) => {\n    cats.forEach(cat => {\n      // Filter by type if specified\n      if (type && cat.type !== type) {\n        if (cat.children) {\n          traverse(cat.children, level);\n        }\n        return;\n      }\n\n      // Filter by parent if specified\n      if (parentId && cat.parent_id !== parentId) {\n        if (cat.children) {\n          traverse(cat.children, level);\n        }\n        return;\n      }\n      const indent = '  '.repeat(level);\n      options.push({\n        value: cat.id,\n        label: `${indent}${cat.name}`,\n        fullPath: cat.full_path\n      });\n      if (cat.children) {\n        traverse(cat.children, level + 1);\n      }\n    });\n  };\n  traverse(categories);\n  return options;\n};", "map": {"version": 3, "names": ["API_URL", "process", "env", "REACT_APP_API_URL", "getCategories", "response", "fetch", "ok", "status", "console", "warn", "Error", "statusText", "data", "json", "error", "_error$message", "message", "includes", "name", "getCategoriesByType", "type", "getCategoriesByParent", "parentId", "createCategory", "category", "method", "headers", "body", "JSON", "stringify", "errorData", "catch", "detail", "_error$message2", "updateCategory", "categoryId", "categoryUpdate", "deleteCategory", "updateDocumentCategories", "documentId", "getCategoryPath", "path", "buildCategoryTree", "categories", "categoryMap", "Map", "rootCategories", "for<PERSON>ach", "set", "id", "children", "categoryNode", "get", "parent_id", "parent", "push", "sortCategories", "cats", "sort", "a", "b", "sort_order", "localeCompare", "cat", "length", "getCategoriesOfType", "result", "traverse", "findCategoryById", "found", "getCategoryOptions", "options", "level", "indent", "repeat", "value", "label", "fullPath", "full_path"], "sources": ["C:/IR App/frontend/src/services/categoryApi.ts"], "sourcesContent": ["// Category Management API Service\nimport { Category, CategoryHierarchy, CategoryCreate, CategoryUpdate, DocumentCategoryUpdate } from '../types/documents';\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// API Response interfaces\ninterface ApiResponse<T> {\n  success: boolean;\n  message: string;\n  data?: T;\n}\n\ninterface CategoryResponse extends ApiResponse<Category> {\n  category?: Category;\n}\n\n// Get all categories with hierarchy\nexport const getCategories = async (): Promise<CategoryHierarchy[]> => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`);\n\n    if (!response.ok) {\n      if (response.status === 0 || !response.status) {\n        console.warn('Backend server not running. Categories will be empty until server starts.');\n        return [];\n      }\n      throw new Error(`Failed to fetch categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error: any) {\n    if (error.message?.includes('fetch') || error.name === 'TypeError') {\n      console.warn('Backend server not available. Categories will be empty until server starts.');\n      return [];\n    }\n    console.error('Error fetching categories:', error);\n    throw error;\n  }\n};\n\n// Get categories by type\nexport const getCategoriesByType = async (type: string): Promise<Category[]> => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/by-type/${type}`);\n    \n    if (!response.ok) {\n      throw new Error(`Failed to fetch categories by type: ${response.statusText}`);\n    }\n    \n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error fetching categories by type:', error);\n    throw error;\n  }\n};\n\n// Get categories by parent\nexport const getCategoriesByParent = async (parentId: string): Promise<Category[]> => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/by-parent/${parentId}`);\n    \n    if (!response.ok) {\n      throw new Error(`Failed to fetch categories by parent: ${response.statusText}`);\n    }\n    \n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error fetching categories by parent:', error);\n    throw error;\n  }\n};\n\n// Create new category\nexport const createCategory = async (category: CategoryCreate): Promise<CategoryResponse> => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(category),\n    });\n\n    if (!response.ok) {\n      if (response.status === 0 || !response.status) {\n        throw new Error('Backend server not running. Please start the backend server to create categories.');\n      }\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to create category: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error: any) {\n    if (error.message?.includes('fetch') || error.name === 'TypeError') {\n      throw new Error('Backend server not available. Please start the backend server to create categories.');\n    }\n    console.error('Error creating category:', error);\n    throw error;\n  }\n};\n\n// Update category\nexport const updateCategory = async (categoryId: string, categoryUpdate: CategoryUpdate): Promise<ApiResponse<void>> => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(categoryUpdate),\n    });\n    \n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to update category: ${response.statusText}`);\n    }\n    \n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating category:', error);\n    throw error;\n  }\n};\n\n// Delete category (soft delete)\nexport const deleteCategory = async (categoryId: string): Promise<ApiResponse<void>> => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'DELETE',\n    });\n    \n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to delete category: ${response.statusText}`);\n    }\n    \n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    throw error;\n  }\n};\n\n// Update document categories\nexport const updateDocumentCategories = async (\n  documentId: string, \n  categoryUpdate: DocumentCategoryUpdate\n): Promise<ApiResponse<void>> => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/documents/${documentId}/categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(categoryUpdate),\n    });\n    \n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to update document categories: ${response.statusText}`);\n    }\n    \n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating document categories:', error);\n    throw error;\n  }\n};\n\n// Get category path\nexport const getCategoryPath = async (categoryId: string): Promise<string> => {\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}/path`);\n    \n    if (!response.ok) {\n      throw new Error(`Failed to get category path: ${response.statusText}`);\n    }\n    \n    const data = await response.json();\n    return data.path;\n  } catch (error) {\n    console.error('Error getting category path:', error);\n    throw error;\n  }\n};\n\n// Helper function to build category hierarchy tree from flat list\nexport const buildCategoryTree = (categories: CategoryHierarchy[]): CategoryHierarchy[] => {\n  const categoryMap = new Map<string, CategoryHierarchy>();\n  const rootCategories: CategoryHierarchy[] = [];\n\n  // First pass: create map of all categories\n  categories.forEach(category => {\n    categoryMap.set(category.id, { ...category, children: [] });\n  });\n\n  // Second pass: build tree structure\n  categories.forEach(category => {\n    const categoryNode = categoryMap.get(category.id)!;\n    \n    if (category.parent_id) {\n      const parent = categoryMap.get(category.parent_id);\n      if (parent) {\n        parent.children = parent.children || [];\n        parent.children.push(categoryNode);\n      }\n    } else {\n      rootCategories.push(categoryNode);\n    }\n  });\n\n  // Sort categories by sort_order and name\n  const sortCategories = (cats: CategoryHierarchy[]) => {\n    cats.sort((a, b) => {\n      if (a.sort_order !== b.sort_order) {\n        return a.sort_order - b.sort_order;\n      }\n      return a.name.localeCompare(b.name);\n    });\n    \n    cats.forEach(cat => {\n      if (cat.children && cat.children.length > 0) {\n        sortCategories(cat.children);\n      }\n    });\n  };\n\n  sortCategories(rootCategories);\n  return rootCategories;\n};\n\n// Helper function to get all categories of a specific type from hierarchy\nexport const getCategoriesOfType = (\n  categories: CategoryHierarchy[], \n  type: string\n): CategoryHierarchy[] => {\n  const result: CategoryHierarchy[] = [];\n  \n  const traverse = (cats: CategoryHierarchy[]) => {\n    cats.forEach(cat => {\n      if (cat.type === type) {\n        result.push(cat);\n      }\n      if (cat.children) {\n        traverse(cat.children);\n      }\n    });\n  };\n  \n  traverse(categories);\n  return result;\n};\n\n// Helper function to find category by ID in hierarchy\nexport const findCategoryById = (\n  categories: CategoryHierarchy[], \n  id: string\n): CategoryHierarchy | null => {\n  for (const category of categories) {\n    if (category.id === id) {\n      return category;\n    }\n    if (category.children) {\n      const found = findCategoryById(category.children, id);\n      if (found) {\n        return found;\n      }\n    }\n  }\n  return null;\n};\n\n// Helper function to get category options for dropdowns\nexport const getCategoryOptions = (\n  categories: CategoryHierarchy[],\n  type?: string,\n  parentId?: string\n): Array<{ value: string; label: string; fullPath: string }> => {\n  const options: Array<{ value: string; label: string; fullPath: string }> = [];\n  \n  const traverse = (cats: CategoryHierarchy[], level = 0) => {\n    cats.forEach(cat => {\n      // Filter by type if specified\n      if (type && cat.type !== type) {\n        if (cat.children) {\n          traverse(cat.children, level);\n        }\n        return;\n      }\n      \n      // Filter by parent if specified\n      if (parentId && cat.parent_id !== parentId) {\n        if (cat.children) {\n          traverse(cat.children, level);\n        }\n        return;\n      }\n      \n      const indent = '  '.repeat(level);\n      options.push({\n        value: cat.id,\n        label: `${indent}${cat.name}`,\n        fullPath: cat.full_path\n      });\n      \n      if (cat.children) {\n        traverse(cat.children, level + 1);\n      }\n    });\n  };\n  \n  traverse(categories);\n  return options;\n};\n"], "mappings": "AAAA;;AAGA,MAAMA,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAExE;;AAWA;AACA,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAA0C;EACrE,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,kBAAkB,CAAC;IAE1D,IAAI,CAACK,QAAQ,CAACE,EAAE,EAAE;MAChB,IAAIF,QAAQ,CAACG,MAAM,KAAK,CAAC,IAAI,CAACH,QAAQ,CAACG,MAAM,EAAE;QAC7CC,OAAO,CAACC,IAAI,CAAC,2EAA2E,CAAC;QACzF,OAAO,EAAE;MACX;MACA,MAAM,IAAIC,KAAK,CAAC,+BAA+BN,QAAQ,CAACO,UAAU,EAAE,CAAC;IACvE;IAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOE,KAAU,EAAE;IAAA,IAAAC,cAAA;IACnB,IAAI,CAAAA,cAAA,GAAAD,KAAK,CAACE,OAAO,cAAAD,cAAA,eAAbA,cAAA,CAAeE,QAAQ,CAAC,OAAO,CAAC,IAAIH,KAAK,CAACI,IAAI,KAAK,WAAW,EAAE;MAClEV,OAAO,CAACC,IAAI,CAAC,6EAA6E,CAAC;MAC3F,OAAO,EAAE;IACX;IACAD,OAAO,CAACM,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,mBAAmB,GAAG,MAAOC,IAAY,IAA0B;EAC9E,IAAI;IACF,MAAMhB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,2BAA2BqB,IAAI,EAAE,CAAC;IAEzE,IAAI,CAAChB,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAM,IAAII,KAAK,CAAC,uCAAuCN,QAAQ,CAACO,UAAU,EAAE,CAAC;IAC/E;IAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,qBAAqB,GAAG,MAAOC,QAAgB,IAA0B;EACpF,IAAI;IACF,MAAMlB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,6BAA6BuB,QAAQ,EAAE,CAAC;IAE/E,IAAI,CAAClB,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAM,IAAII,KAAK,CAAC,yCAAyCN,QAAQ,CAACO,UAAU,EAAE,CAAC;IACjF;IAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC5D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,cAAc,GAAG,MAAOC,QAAwB,IAAgC;EAC3F,IAAI;IACF,MAAMpB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,kBAAkB,EAAE;MACzD0B,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,QAAQ;IAC/B,CAAC,CAAC;IAEF,IAAI,CAACpB,QAAQ,CAACE,EAAE,EAAE;MAChB,IAAIF,QAAQ,CAACG,MAAM,KAAK,CAAC,IAAI,CAACH,QAAQ,CAACG,MAAM,EAAE;QAC7C,MAAM,IAAIG,KAAK,CAAC,mFAAmF,CAAC;MACtG;MACA,MAAMoB,SAAS,GAAG,MAAM1B,QAAQ,CAACS,IAAI,CAAC,CAAC,CAACkB,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAE5B,QAAQ,CAACO;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAID,KAAK,CAACoB,SAAS,CAACE,MAAM,IAAI,8BAA8B5B,QAAQ,CAACO,UAAU,EAAE,CAAC;IAC1F;IAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOE,KAAU,EAAE;IAAA,IAAAmB,eAAA;IACnB,IAAI,CAAAA,eAAA,GAAAnB,KAAK,CAACE,OAAO,cAAAiB,eAAA,eAAbA,eAAA,CAAehB,QAAQ,CAAC,OAAO,CAAC,IAAIH,KAAK,CAACI,IAAI,KAAK,WAAW,EAAE;MAClE,MAAM,IAAIR,KAAK,CAAC,qFAAqF,CAAC;IACxG;IACAF,OAAO,CAACM,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMoB,cAAc,GAAG,MAAAA,CAAOC,UAAkB,EAAEC,cAA8B,KAAiC;EACtH,IAAI;IACF,MAAMhC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,mBAAmBoC,UAAU,EAAE,EAAE;MACtEV,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACO,cAAc;IACrC,CAAC,CAAC;IAEF,IAAI,CAAChC,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAMwB,SAAS,GAAG,MAAM1B,QAAQ,CAACS,IAAI,CAAC,CAAC,CAACkB,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAE5B,QAAQ,CAACO;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAID,KAAK,CAACoB,SAAS,CAACE,MAAM,IAAI,8BAA8B5B,QAAQ,CAACO,UAAU,EAAE,CAAC;IAC1F;IAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMuB,cAAc,GAAG,MAAOF,UAAkB,IAAiC;EACtF,IAAI;IACF,MAAM/B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,mBAAmBoC,UAAU,EAAE,EAAE;MACtEV,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI,CAACrB,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAMwB,SAAS,GAAG,MAAM1B,QAAQ,CAACS,IAAI,CAAC,CAAC,CAACkB,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAE5B,QAAQ,CAACO;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAID,KAAK,CAACoB,SAAS,CAACE,MAAM,IAAI,8BAA8B5B,QAAQ,CAACO,UAAU,EAAE,CAAC;IAC1F;IAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMwB,wBAAwB,GAAG,MAAAA,CACtCC,UAAkB,EAClBH,cAAsC,KACP;EAC/B,IAAI;IACF,MAAMhC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,6BAA6BwC,UAAU,aAAa,EAAE;MAC3Fd,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACO,cAAc;IACrC,CAAC,CAAC;IAEF,IAAI,CAAChC,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAMwB,SAAS,GAAG,MAAM1B,QAAQ,CAACS,IAAI,CAAC,CAAC,CAACkB,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAE5B,QAAQ,CAACO;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAID,KAAK,CAACoB,SAAS,CAACE,MAAM,IAAI,yCAAyC5B,QAAQ,CAACO,UAAU,EAAE,CAAC;IACrG;IAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC3D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM0B,eAAe,GAAG,MAAOL,UAAkB,IAAsB;EAC5E,IAAI;IACF,MAAM/B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,OAAO,mBAAmBoC,UAAU,OAAO,CAAC;IAE5E,IAAI,CAAC/B,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAM,IAAII,KAAK,CAAC,gCAAgCN,QAAQ,CAACO,UAAU,EAAE,CAAC;IACxE;IAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI,CAAC6B,IAAI;EAClB,CAAC,CAAC,OAAO3B,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM4B,iBAAiB,GAAIC,UAA+B,IAA0B;EACzF,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAA4B,CAAC;EACxD,MAAMC,cAAmC,GAAG,EAAE;;EAE9C;EACAH,UAAU,CAACI,OAAO,CAACvB,QAAQ,IAAI;IAC7BoB,WAAW,CAACI,GAAG,CAACxB,QAAQ,CAACyB,EAAE,EAAE;MAAE,GAAGzB,QAAQ;MAAE0B,QAAQ,EAAE;IAAG,CAAC,CAAC;EAC7D,CAAC,CAAC;;EAEF;EACAP,UAAU,CAACI,OAAO,CAACvB,QAAQ,IAAI;IAC7B,MAAM2B,YAAY,GAAGP,WAAW,CAACQ,GAAG,CAAC5B,QAAQ,CAACyB,EAAE,CAAE;IAElD,IAAIzB,QAAQ,CAAC6B,SAAS,EAAE;MACtB,MAAMC,MAAM,GAAGV,WAAW,CAACQ,GAAG,CAAC5B,QAAQ,CAAC6B,SAAS,CAAC;MAClD,IAAIC,MAAM,EAAE;QACVA,MAAM,CAACJ,QAAQ,GAAGI,MAAM,CAACJ,QAAQ,IAAI,EAAE;QACvCI,MAAM,CAACJ,QAAQ,CAACK,IAAI,CAACJ,YAAY,CAAC;MACpC;IACF,CAAC,MAAM;MACLL,cAAc,CAACS,IAAI,CAACJ,YAAY,CAAC;IACnC;EACF,CAAC,CAAC;;EAEF;EACA,MAAMK,cAAc,GAAIC,IAAyB,IAAK;IACpDA,IAAI,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAClB,IAAID,CAAC,CAACE,UAAU,KAAKD,CAAC,CAACC,UAAU,EAAE;QACjC,OAAOF,CAAC,CAACE,UAAU,GAAGD,CAAC,CAACC,UAAU;MACpC;MACA,OAAOF,CAAC,CAACzC,IAAI,CAAC4C,aAAa,CAACF,CAAC,CAAC1C,IAAI,CAAC;IACrC,CAAC,CAAC;IAEFuC,IAAI,CAACV,OAAO,CAACgB,GAAG,IAAI;MAClB,IAAIA,GAAG,CAACb,QAAQ,IAAIa,GAAG,CAACb,QAAQ,CAACc,MAAM,GAAG,CAAC,EAAE;QAC3CR,cAAc,CAACO,GAAG,CAACb,QAAQ,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ,CAAC;EAEDM,cAAc,CAACV,cAAc,CAAC;EAC9B,OAAOA,cAAc;AACvB,CAAC;;AAED;AACA,OAAO,MAAMmB,mBAAmB,GAAGA,CACjCtB,UAA+B,EAC/BvB,IAAY,KACY;EACxB,MAAM8C,MAA2B,GAAG,EAAE;EAEtC,MAAMC,QAAQ,GAAIV,IAAyB,IAAK;IAC9CA,IAAI,CAACV,OAAO,CAACgB,GAAG,IAAI;MAClB,IAAIA,GAAG,CAAC3C,IAAI,KAAKA,IAAI,EAAE;QACrB8C,MAAM,CAACX,IAAI,CAACQ,GAAG,CAAC;MAClB;MACA,IAAIA,GAAG,CAACb,QAAQ,EAAE;QAChBiB,QAAQ,CAACJ,GAAG,CAACb,QAAQ,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC;EAEDiB,QAAQ,CAACxB,UAAU,CAAC;EACpB,OAAOuB,MAAM;AACf,CAAC;;AAED;AACA,OAAO,MAAME,gBAAgB,GAAGA,CAC9BzB,UAA+B,EAC/BM,EAAU,KACmB;EAC7B,KAAK,MAAMzB,QAAQ,IAAImB,UAAU,EAAE;IACjC,IAAInB,QAAQ,CAACyB,EAAE,KAAKA,EAAE,EAAE;MACtB,OAAOzB,QAAQ;IACjB;IACA,IAAIA,QAAQ,CAAC0B,QAAQ,EAAE;MACrB,MAAMmB,KAAK,GAAGD,gBAAgB,CAAC5C,QAAQ,CAAC0B,QAAQ,EAAED,EAAE,CAAC;MACrD,IAAIoB,KAAK,EAAE;QACT,OAAOA,KAAK;MACd;IACF;EACF;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAChC3B,UAA+B,EAC/BvB,IAAa,EACbE,QAAiB,KAC6C;EAC9D,MAAMiD,OAAkE,GAAG,EAAE;EAE7E,MAAMJ,QAAQ,GAAGA,CAACV,IAAyB,EAAEe,KAAK,GAAG,CAAC,KAAK;IACzDf,IAAI,CAACV,OAAO,CAACgB,GAAG,IAAI;MAClB;MACA,IAAI3C,IAAI,IAAI2C,GAAG,CAAC3C,IAAI,KAAKA,IAAI,EAAE;QAC7B,IAAI2C,GAAG,CAACb,QAAQ,EAAE;UAChBiB,QAAQ,CAACJ,GAAG,CAACb,QAAQ,EAAEsB,KAAK,CAAC;QAC/B;QACA;MACF;;MAEA;MACA,IAAIlD,QAAQ,IAAIyC,GAAG,CAACV,SAAS,KAAK/B,QAAQ,EAAE;QAC1C,IAAIyC,GAAG,CAACb,QAAQ,EAAE;UAChBiB,QAAQ,CAACJ,GAAG,CAACb,QAAQ,EAAEsB,KAAK,CAAC;QAC/B;QACA;MACF;MAEA,MAAMC,MAAM,GAAG,IAAI,CAACC,MAAM,CAACF,KAAK,CAAC;MACjCD,OAAO,CAAChB,IAAI,CAAC;QACXoB,KAAK,EAAEZ,GAAG,CAACd,EAAE;QACb2B,KAAK,EAAE,GAAGH,MAAM,GAAGV,GAAG,CAAC7C,IAAI,EAAE;QAC7B2D,QAAQ,EAAEd,GAAG,CAACe;MAChB,CAAC,CAAC;MAEF,IAAIf,GAAG,CAACb,QAAQ,EAAE;QAChBiB,QAAQ,CAACJ,GAAG,CAACb,QAAQ,EAAEsB,KAAK,GAAG,CAAC,CAAC;MACnC;IACF,CAAC,CAAC;EACJ,CAAC;EAEDL,QAAQ,CAACxB,UAAU,CAAC;EACpB,OAAO4B,OAAO;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}