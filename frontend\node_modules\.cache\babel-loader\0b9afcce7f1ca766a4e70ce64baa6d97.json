{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\components\\\\categories\\\\CategoryCreator.tsx\";\nimport React, { useState, useEffect } from 'react';\nimport { Plus, X, Trash2, FolderPlus } from 'lucide-react';\nimport { getCategories, createCategory, deleteCategory } from '../../services/categoryApi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CategoryCreator = ({\n  isOpen,\n  onClose,\n  onCategoryCreated\n}) => {\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Form state for 4-row layout\n  const [selectedMainCategory, setSelectedMainCategory] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n  const [selectedMinorCategory, setSelectedMinorCategory] = useState('');\n\n  // New category creation states\n  const [newMainCategory, setNewMainCategory] = useState('');\n  const [newCategory, setNewCategory] = useState('');\n  const [newSubCategory, setNewSubCategory] = useState('');\n  const [newMinorCategory, setNewMinorCategory] = useState('');\n\n  // Show/hide new category inputs\n  const [showNewMainCategory, setShowNewMainCategory] = useState(false);\n  const [showNewCategory, setShowNewCategory] = useState(false);\n  const [showNewSubCategory, setShowNewSubCategory] = useState(false);\n  const [showNewMinorCategory, setShowNewMinorCategory] = useState(false);\n\n  // Description for new categories\n  const [description, setDescription] = useState('');\n  const categoryTypes = [{\n    value: 'main_category',\n    label: 'Main Category',\n    description: 'Top-level category (e.g., Safety, Operations)',\n    level: 0\n  }, {\n    value: 'category',\n    label: 'Category',\n    description: 'Second-level category under Main Category',\n    level: 1\n  }, {\n    value: 'sub_category',\n    label: 'Sub Category',\n    description: 'Third-level category under Category',\n    level: 2\n  }, {\n    value: 'minor_category',\n    label: 'Minor Category',\n    description: 'Fourth-level category under Sub Category',\n    level: 3\n  }];\n  useEffect(() => {\n    if (isOpen) {\n      loadCategories();\n    }\n  }, [isOpen]);\n\n  // Helper functions to get categories by type\n  const getMainCategoriesList = () => categories.filter(cat => cat.type === 'main_category');\n  const getCategoriesList = mainCategoryId => categories.filter(cat => cat.type === 'category' && cat.parent_id === mainCategoryId);\n  const getSubCategoriesList = categoryId => categories.filter(cat => cat.type === 'sub_category' && cat.parent_id === categoryId);\n  const getMinorCategoriesList = subCategoryId => categories.filter(cat => cat.type === 'minor_category' && cat.parent_id === subCategoryId);\n\n  // Reset dependent selections when parent changes\n  useEffect(() => {\n    setSelectedCategory('');\n    setSelectedSubCategory('');\n    setSelectedMinorCategory('');\n    setShowNewCategory(false);\n    setShowNewSubCategory(false);\n    setShowNewMinorCategory(false);\n  }, [selectedMainCategory]);\n  useEffect(() => {\n    setSelectedSubCategory('');\n    setSelectedMinorCategory('');\n    setShowNewSubCategory(false);\n    setShowNewMinorCategory(false);\n  }, [selectedCategory]);\n  useEffect(() => {\n    setSelectedMinorCategory('');\n    setShowNewMinorCategory(false);\n  }, [selectedSubCategory]);\n\n  // Get current category path\n  const getCurrentPath = () => {\n    const parts = [];\n    if (selectedMainCategory) {\n      const mainCat = categories.find(cat => cat.id === selectedMainCategory);\n      if (mainCat) parts.push(mainCat.name);\n    }\n    if (selectedCategory) {\n      const cat = categories.find(cat => cat.id === selectedCategory);\n      if (cat) parts.push(cat.name);\n    }\n    if (selectedSubCategory) {\n      const subCat = categories.find(cat => cat.id === selectedSubCategory);\n      if (subCat) parts.push(subCat.name);\n    }\n    if (selectedMinorCategory) {\n      const minorCat = categories.find(cat => cat.id === selectedMinorCategory);\n      if (minorCat) parts.push(minorCat.name);\n    }\n    return parts.join(' > ');\n  };\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await getCategories(); // This is the API call from categoryApi\n      setCategories(data);\n    } catch (err) {\n      setError('Failed to load categories');\n      console.error('Error loading categories:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateCategory = async (type, name, parentId) => {\n    if (!name.trim()) {\n      setError('Category name is required');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      setSuccess(null);\n      const newCategory = {\n        name: name.trim(),\n        type: type,\n        parent_id: parentId,\n        description: description.trim() || undefined,\n        sort_order: categories.length + 1\n      };\n      const response = await createCategory(newCategory);\n      if (response.success) {\n        setSuccess(`${type.replace('_', ' ')} \"${name}\" created successfully!`);\n\n        // Clear the input for the created category\n        switch (type) {\n          case 'main_category':\n            setNewMainCategory('');\n            setShowNewMainCategory(false);\n            break;\n          case 'category':\n            setNewCategory('');\n            setShowNewCategory(false);\n            break;\n          case 'sub_category':\n            setNewSubCategory('');\n            setShowNewSubCategory(false);\n            break;\n          case 'minor_category':\n            setNewMinorCategory('');\n            setShowNewMinorCategory(false);\n            break;\n        }\n        setDescription('');\n\n        // Reload categories to show the new one\n        await loadCategories();\n\n        // Notify parent component\n        if (onCategoryCreated && response.category) {\n          onCategoryCreated(response.category);\n        }\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to create category');\n      console.error('Error creating category:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteCategory = async (categoryId, categoryName) => {\n    if (!window.confirm(`Are you sure you want to delete the category \"${categoryName}\"?`)) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      await deleteCategory(categoryId);\n      setSuccess(`Category \"${categoryName}\" deleted successfully!`);\n\n      // Reload categories\n      await loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to delete category');\n      console.error('Error deleting category:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderCategoryList = () => {\n    if (categories.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(FolderPlus, {\n          className: \"mx-auto h-12 w-12 mb-4 text-gray-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No categories yet. Create your first category below!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Group categories by type for better organization\n    const groupedCategories = categoryTypes.reduce((acc, type) => {\n      acc[type.value] = categories.filter(cat => cat.type === type.value);\n      return acc;\n    }, {});\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"font-medium text-gray-900 mb-3\",\n        children: \"Existing Categories:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), categoryTypes.map(type => {\n        const categoriesOfType = groupedCategories[type.value] || [];\n        if (categoriesOfType.length === 0) return null;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"text-sm font-medium text-gray-700 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gray-100 px-2 py-1 rounded text-xs mr-2\",\n              children: [\"Level \", type.level + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), type.label, \" (\", categoriesOfType.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), categoriesOfType.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg border ml-4\",\n            style: {\n              marginLeft: `${type.level * 16 + 16}px`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-900\",\n                  children: category.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                  children: type.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 21\n              }, this), category.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: category.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: [\"Path: \", category.full_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleDeleteCategory(category.id, category.name),\n              className: \"ml-3 p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n              title: \"Delete category\",\n              disabled: loading,\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 19\n            }, this)]\n          }, category.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 17\n          }, this))]\n        }, type.value, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this);\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Manage Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-blue-900 mb-2\",\n            children: \"Category Hierarchy Guide\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-blue-700 space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-blue-100 px-2 py-1 rounded mr-2\",\n                children: \"Level 1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Main Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this), \" - Top level (e.g., Safety, Operations)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-blue-100 px-2 py-1 rounded mr-2\",\n                children: \"Level 2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 23\n                }, this), \" - Under Main Category (e.g., Guidelines, Procedures)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-blue-100 px-2 py-1 rounded mr-2\",\n                children: \"Level 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sub Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 23\n                }, this), \" - Under Category (e.g., Emergency, Routine)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-blue-100 px-2 py-1 rounded mr-2\",\n                children: \"Level 4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Minor Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this), \" - Under Sub Category (e.g., Fire Safety, Equipment)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-blue-600 mt-2\",\n            children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Tip:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 18\n            }, this), \" Start with Main Categories, then build your hierarchy step by step.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg\",\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this), renderCategoryList(), getCurrentPath() && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-50 border border-green-200 rounded-lg p-3\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-green-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Current Path:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this), \" \", getCurrentPath()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t pt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Create Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-blue-900\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-blue-100 px-2 py-1 rounded text-xs mr-2\",\n                    children: \"Level 1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 21\n                  }, this), \"Main Category\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Select Existing Main Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: selectedMainCategory,\n                    onChange: e => setSelectedMainCategory(e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                    disabled: loading,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select a main category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 23\n                    }, this), getMainCategoriesList().map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: cat.id,\n                      children: cat.name\n                    }, cat.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Or Create New Main Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: !showNewMainCategory ? /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setShowNewMainCategory(true),\n                      className: \"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\",\n                      disabled: loading,\n                      children: [/*#__PURE__*/_jsxDEV(Plus, {\n                        className: \"h-4 w-4 inline mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 417,\n                        columnNumber: 27\n                      }, this), \"New\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        value: newMainCategory,\n                        onChange: e => setNewMainCategory(e.target.value),\n                        placeholder: \"Main category name\",\n                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                        disabled: loading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => handleCreateCategory('main_category', newMainCategory),\n                        className: \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\",\n                        disabled: loading || !newMainCategory.trim(),\n                        children: \"Create\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 430,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => {\n                          setShowNewMainCategory(false);\n                          setNewMainCategory('');\n                        },\n                        className: \"px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm\",\n                        disabled: loading,\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 438,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `bg-green-50 border border-green-200 rounded-lg p-4 ${!selectedMainCategory ? 'opacity-50' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-green-900\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-green-100 px-2 py-1 rounded text-xs mr-2\",\n                    children: \"Level 2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 21\n                  }, this), \"Category\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this), !selectedMainCategory && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Select a Main Category first\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Select Existing Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: selectedCategory,\n                    onChange: e => setSelectedCategory(e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                    disabled: loading || !selectedMainCategory,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select a category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 23\n                    }, this), selectedMainCategory && getCategoriesList(selectedMainCategory).map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: cat.id,\n                      children: cat.name\n                    }, cat.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Or Create New Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: !showNewCategory ? /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setShowNewCategory(true),\n                      className: \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\",\n                      disabled: loading || !selectedMainCategory,\n                      children: [/*#__PURE__*/_jsxDEV(Plus, {\n                        className: \"h-4 w-4 inline mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 498,\n                        columnNumber: 27\n                      }, this), \"New\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        value: newCategory,\n                        onChange: e => setNewCategory(e.target.value),\n                        placeholder: \"Category name\",\n                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                        disabled: loading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => handleCreateCategory('category', newCategory, selectedMainCategory),\n                        className: \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\",\n                        disabled: loading || !newCategory.trim() || !selectedMainCategory,\n                        children: \"Create\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => {\n                          setShowNewCategory(false);\n                          setNewCategory('');\n                        },\n                        className: \"px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm\",\n                        disabled: loading,\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 519,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${!selectedCategory ? 'opacity-50' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-yellow-900\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-yellow-100 px-2 py-1 rounded text-xs mr-2\",\n                    children: \"Level 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 21\n                  }, this), \"Sub Category\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 19\n                }, this), !selectedCategory && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Select a Category first\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Select Existing Sub Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: selectedSubCategory,\n                    onChange: e => setSelectedSubCategory(e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500\",\n                    disabled: loading || !selectedCategory,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select a sub category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 23\n                    }, this), selectedCategory && getSubCategoriesList(selectedCategory).map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: cat.id,\n                      children: cat.name\n                    }, cat.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Or Create New Sub Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: !showNewSubCategory ? /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setShowNewSubCategory(true),\n                      className: \"px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm\",\n                      disabled: loading || !selectedCategory,\n                      children: [/*#__PURE__*/_jsxDEV(Plus, {\n                        className: \"h-4 w-4 inline mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 579,\n                        columnNumber: 27\n                      }, this), \"New\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        value: newSubCategory,\n                        onChange: e => setNewSubCategory(e.target.value),\n                        placeholder: \"Sub category name\",\n                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500\",\n                        disabled: loading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 584,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => handleCreateCategory('sub_category', newSubCategory, selectedCategory),\n                        className: \"px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm\",\n                        disabled: loading || !newSubCategory.trim() || !selectedCategory,\n                        children: \"Create\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 592,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => {\n                          setShowNewSubCategory(false);\n                          setNewSubCategory('');\n                        },\n                        className: \"px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm\",\n                        disabled: loading,\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `bg-purple-50 border border-purple-200 rounded-lg p-4 ${!selectedSubCategory ? 'opacity-50' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-purple-900\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-purple-100 px-2 py-1 rounded text-xs mr-2\",\n                    children: \"Level 4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 21\n                  }, this), \"Minor Category\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 19\n                }, this), !selectedSubCategory && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Select a Sub Category first\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Select Existing Minor Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: selectedMinorCategory,\n                    onChange: e => setSelectedMinorCategory(e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500\",\n                    disabled: loading || !selectedSubCategory,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select a minor category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 23\n                    }, this), selectedSubCategory && getMinorCategoriesList(selectedSubCategory).map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: cat.id,\n                      children: cat.name\n                    }, cat.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 643,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 635,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Or Create New Minor Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: !showNewMinorCategory ? /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setShowNewMinorCategory(true),\n                      className: \"px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm\",\n                      disabled: loading || !selectedSubCategory,\n                      children: [/*#__PURE__*/_jsxDEV(Plus, {\n                        className: \"h-4 w-4 inline mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 27\n                      }, this), \"New\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        value: newMinorCategory,\n                        onChange: e => setNewMinorCategory(e.target.value),\n                        placeholder: \"Minor category name\",\n                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500\",\n                        disabled: loading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => handleCreateCategory('minor_category', newMinorCategory, selectedSubCategory),\n                        className: \"px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm\",\n                        disabled: loading || !newMinorCategory.trim() || !selectedSubCategory,\n                        children: \"Create\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 673,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => {\n                          setShowNewMinorCategory(false);\n                          setNewMinorCategory('');\n                        },\n                        className: \"px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm\",\n                        disabled: loading,\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Description (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                value: description,\n                onChange: e => setDescription(e.target.value),\n                rows: 2,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Enter description for new categories\",\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end pt-4\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: onClose,\n                className: \"px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors\",\n                disabled: loading,\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 307,\n    columnNumber: 5\n  }, this);\n};\nexport default CategoryCreator;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Plus", "X", "Trash2", "FolderPlus", "getCategories", "createCategory", "deleteCategory", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CategoryCreator", "isOpen", "onClose", "onCategoryCreated", "categories", "setCategories", "loading", "setLoading", "error", "setError", "success", "setSuccess", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedMainCategory", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "selectedMinorCategory", "setSelectedMinorCategory", "newMainCategory", "setNewMainCategory", "newCategory", "setNewCategory", "newSubCategory", "setNewSubCategory", "newMinorCategory", "setNewMinorCategory", "showNewMainCategory", "setShowNewMainCategory", "showNewCategory", "setShowNewCategory", "showNewSubCategory", "setShowNewSubCategory", "showNewMinorCategory", "setShowNewMinorCategory", "description", "setDescription", "categoryTypes", "value", "label", "level", "loadCategories", "getMainCategoriesList", "filter", "cat", "type", "getCategoriesList", "mainCategoryId", "parent_id", "getSubCategoriesList", "categoryId", "getMinorCategoriesList", "subCategoryId", "getCurrentPath", "parts", "mainCat", "find", "id", "push", "name", "subCat", "minorCat", "join", "data", "err", "console", "handleCreateCategory", "parentId", "trim", "undefined", "sort_order", "length", "response", "replace", "category", "message", "handleDeleteCategory", "categoryName", "window", "confirm", "renderCategoryList", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "groupedCategories", "reduce", "acc", "map", "categoriesOfType", "style", "marginLeft", "full_path", "onClick", "title", "disabled", "onChange", "e", "target", "placeholder", "htmlFor", "rows"], "sources": ["C:/IR App/frontend/src/components/categories/CategoryCreator.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Plus, X, Trash2, FolderPlus } from 'lucide-react';\nimport { getCategories, createCategory, deleteCategory } from '../../services/categoryApi';\nimport { CategoryHierarchy, CategoryCreate } from '../../types/documents';\n\ninterface CategoryCreatorProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onCategoryCreated?: (category: any) => void; // Use any to avoid type conflicts\n}\n\nconst CategoryCreator: React.FC<CategoryCreatorProps> = ({\n  isOpen,\n  onClose,\n  onCategoryCreated\n}) => {\n  const [categories, setCategories] = useState<CategoryHierarchy[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Form state for 4-row layout\n  const [selectedMainCategory, setSelectedMainCategory] = useState<string>('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');\n  const [selectedMinorCategory, setSelectedMinorCategory] = useState<string>('');\n\n  // New category creation states\n  const [newMainCategory, setNewMainCategory] = useState('');\n  const [newCategory, setNewCategory] = useState('');\n  const [newSubCategory, setNewSubCategory] = useState('');\n  const [newMinorCategory, setNewMinorCategory] = useState('');\n\n  // Show/hide new category inputs\n  const [showNewMainCategory, setShowNewMainCategory] = useState(false);\n  const [showNewCategory, setShowNewCategory] = useState(false);\n  const [showNewSubCategory, setShowNewSubCategory] = useState(false);\n  const [showNewMinorCategory, setShowNewMinorCategory] = useState(false);\n\n  // Description for new categories\n  const [description, setDescription] = useState('');\n\n  const categoryTypes: Array<{\n    value: 'main_category' | 'category' | 'sub_category' | 'minor_category';\n    label: string;\n    description: string;\n    level: number;\n  }> = [\n    {\n      value: 'main_category',\n      label: 'Main Category',\n      description: 'Top-level category (e.g., Safety, Operations)',\n      level: 0\n    },\n    {\n      value: 'category',\n      label: 'Category',\n      description: 'Second-level category under Main Category',\n      level: 1\n    },\n    {\n      value: 'sub_category',\n      label: 'Sub Category',\n      description: 'Third-level category under Category',\n      level: 2\n    },\n    {\n      value: 'minor_category',\n      label: 'Minor Category',\n      description: 'Fourth-level category under Sub Category',\n      level: 3\n    }\n  ];\n\n  useEffect(() => {\n    if (isOpen) {\n      loadCategories();\n    }\n  }, [isOpen]);\n\n  // Helper functions to get categories by type\n  const getMainCategoriesList = () => categories.filter(cat => cat.type === 'main_category');\n  const getCategoriesList = (mainCategoryId: string) =>\n    categories.filter(cat => cat.type === 'category' && cat.parent_id === mainCategoryId);\n  const getSubCategoriesList = (categoryId: string) =>\n    categories.filter(cat => cat.type === 'sub_category' && cat.parent_id === categoryId);\n  const getMinorCategoriesList = (subCategoryId: string) =>\n    categories.filter(cat => cat.type === 'minor_category' && cat.parent_id === subCategoryId);\n\n  // Reset dependent selections when parent changes\n  useEffect(() => {\n    setSelectedCategory('');\n    setSelectedSubCategory('');\n    setSelectedMinorCategory('');\n    setShowNewCategory(false);\n    setShowNewSubCategory(false);\n    setShowNewMinorCategory(false);\n  }, [selectedMainCategory]);\n\n  useEffect(() => {\n    setSelectedSubCategory('');\n    setSelectedMinorCategory('');\n    setShowNewSubCategory(false);\n    setShowNewMinorCategory(false);\n  }, [selectedCategory]);\n\n  useEffect(() => {\n    setSelectedMinorCategory('');\n    setShowNewMinorCategory(false);\n  }, [selectedSubCategory]);\n\n  // Get current category path\n  const getCurrentPath = () => {\n    const parts = [];\n\n    if (selectedMainCategory) {\n      const mainCat = categories.find(cat => cat.id === selectedMainCategory);\n      if (mainCat) parts.push(mainCat.name);\n    }\n\n    if (selectedCategory) {\n      const cat = categories.find(cat => cat.id === selectedCategory);\n      if (cat) parts.push(cat.name);\n    }\n\n    if (selectedSubCategory) {\n      const subCat = categories.find(cat => cat.id === selectedSubCategory);\n      if (subCat) parts.push(subCat.name);\n    }\n\n    if (selectedMinorCategory) {\n      const minorCat = categories.find(cat => cat.id === selectedMinorCategory);\n      if (minorCat) parts.push(minorCat.name);\n    }\n\n    return parts.join(' > ');\n  };\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await getCategories(); // This is the API call from categoryApi\n      setCategories(data);\n    } catch (err) {\n      setError('Failed to load categories');\n      console.error('Error loading categories:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateCategory = async (type: 'main_category' | 'category' | 'sub_category' | 'minor_category', name: string, parentId?: string) => {\n    if (!name.trim()) {\n      setError('Category name is required');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n      setSuccess(null);\n\n      const newCategory: CategoryCreate = {\n        name: name.trim(),\n        type: type,\n        parent_id: parentId,\n        description: description.trim() || undefined,\n        sort_order: categories.length + 1\n      };\n\n      const response = await createCategory(newCategory);\n\n      if (response.success) {\n        setSuccess(`${type.replace('_', ' ')} \"${name}\" created successfully!`);\n\n        // Clear the input for the created category\n        switch (type) {\n          case 'main_category':\n            setNewMainCategory('');\n            setShowNewMainCategory(false);\n            break;\n          case 'category':\n            setNewCategory('');\n            setShowNewCategory(false);\n            break;\n          case 'sub_category':\n            setNewSubCategory('');\n            setShowNewSubCategory(false);\n            break;\n          case 'minor_category':\n            setNewMinorCategory('');\n            setShowNewMinorCategory(false);\n            break;\n        }\n\n        setDescription('');\n\n        // Reload categories to show the new one\n        await loadCategories();\n\n        // Notify parent component\n        if (onCategoryCreated && response.category) {\n          onCategoryCreated(response.category);\n        }\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to create category');\n      console.error('Error creating category:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteCategory = async (categoryId: string, categoryName: string) => {\n    if (!window.confirm(`Are you sure you want to delete the category \"${categoryName}\"?`)) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n      \n      await deleteCategory(categoryId);\n      setSuccess(`Category \"${categoryName}\" deleted successfully!`);\n      \n      // Reload categories\n      await loadCategories();\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete category');\n      console.error('Error deleting category:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderCategoryList = () => {\n    if (categories.length === 0) {\n      return (\n        <div className=\"text-center py-8 text-gray-500\">\n          <FolderPlus className=\"mx-auto h-12 w-12 mb-4 text-gray-300\" />\n          <p>No categories yet. Create your first category below!</p>\n        </div>\n      );\n    }\n\n    // Group categories by type for better organization\n    const groupedCategories = categoryTypes.reduce((acc, type) => {\n      acc[type.value] = categories.filter(cat => cat.type === type.value);\n      return acc;\n    }, {} as Record<string, CategoryHierarchy[]>);\n\n    return (\n      <div className=\"space-y-4\">\n        <h4 className=\"font-medium text-gray-900 mb-3\">Existing Categories:</h4>\n\n        {categoryTypes.map((type) => {\n          const categoriesOfType = groupedCategories[type.value] || [];\n          if (categoriesOfType.length === 0) return null;\n\n          return (\n            <div key={type.value} className=\"space-y-2\">\n              <h5 className=\"text-sm font-medium text-gray-700 flex items-center\">\n                <span className=\"bg-gray-100 px-2 py-1 rounded text-xs mr-2\">\n                  Level {type.level + 1}\n                </span>\n                {type.label} ({categoriesOfType.length})\n              </h5>\n\n              {categoriesOfType.map((category) => (\n                <div\n                  key={category.id}\n                  className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg border ml-4\"\n                  style={{ marginLeft: `${type.level * 16 + 16}px` }}\n                >\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-medium text-gray-900\">{category.name}</span>\n                      <span className=\"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded\">\n                        {type.label}\n                      </span>\n                    </div>\n                    {category.description && (\n                      <p className=\"text-sm text-gray-600 mt-1\">{category.description}</p>\n                    )}\n                    <p className=\"text-xs text-gray-500 mt-1\">Path: {category.full_path}</p>\n                  </div>\n                  <button\n                    onClick={() => handleDeleteCategory(category.id, category.name)}\n                    className=\"ml-3 p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\"\n                    title=\"Delete category\"\n                    disabled={loading}\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              ))}\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">\n            Manage Categories\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6 space-y-6\">\n          {/* Category Hierarchy Guide */}\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <h3 className=\"text-sm font-medium text-blue-900 mb-2\">Category Hierarchy Guide</h3>\n            <div className=\"text-xs text-blue-700 space-y-1\">\n              <div className=\"flex items-center\">\n                <span className=\"bg-blue-100 px-2 py-1 rounded mr-2\">Level 1</span>\n                <span><strong>Main Category</strong> - Top level (e.g., Safety, Operations)</span>\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"bg-blue-100 px-2 py-1 rounded mr-2\">Level 2</span>\n                <span><strong>Category</strong> - Under Main Category (e.g., Guidelines, Procedures)</span>\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"bg-blue-100 px-2 py-1 rounded mr-2\">Level 3</span>\n                <span><strong>Sub Category</strong> - Under Category (e.g., Emergency, Routine)</span>\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"bg-blue-100 px-2 py-1 rounded mr-2\">Level 4</span>\n                <span><strong>Minor Category</strong> - Under Sub Category (e.g., Fire Safety, Equipment)</span>\n              </div>\n            </div>\n            <p className=\"text-xs text-blue-600 mt-2\">\n              💡 <strong>Tip:</strong> Start with Main Categories, then build your hierarchy step by step.\n            </p>\n          </div>\n\n          {/* Success/Error Messages */}\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n              {error}\n            </div>\n          )}\n\n          {success && (\n            <div className=\"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg\">\n              {success}\n            </div>\n          )}\n\n          {/* Category List */}\n          {renderCategoryList()}\n\n          {/* Current Category Path Display */}\n          {getCurrentPath() && (\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-3\">\n              <p className=\"text-sm text-green-700\">\n                <strong>Current Path:</strong> {getCurrentPath()}\n              </p>\n            </div>\n          )}\n\n          {/* 4-Row Category Creation Form */}\n          <div className=\"border-t pt-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Create Categories</h3>\n\n            <div className=\"space-y-6\">\n              {/* Row 1: Main Category */}\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h4 className=\"font-medium text-blue-900\">\n                    <span className=\"bg-blue-100 px-2 py-1 rounded text-xs mr-2\">Level 1</span>\n                    Main Category\n                  </h4>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Select Existing Main Category\n                    </label>\n                    <select\n                      value={selectedMainCategory}\n                      onChange={(e) => setSelectedMainCategory(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      disabled={loading}\n                    >\n                      <option value=\"\">Select a main category</option>\n                      {getMainCategoriesList().map((cat) => (\n                        <option key={cat.id} value={cat.id}>{cat.name}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Or Create New Main Category\n                    </label>\n                    <div className=\"flex space-x-2\">\n                      {!showNewMainCategory ? (\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowNewMainCategory(true)}\n                          className=\"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\"\n                          disabled={loading}\n                        >\n                          <Plus className=\"h-4 w-4 inline mr-1\" />\n                          New\n                        </button>\n                      ) : (\n                        <>\n                          <input\n                            type=\"text\"\n                            value={newMainCategory}\n                            onChange={(e) => setNewMainCategory(e.target.value)}\n                            placeholder=\"Main category name\"\n                            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                            disabled={loading}\n                          />\n                          <button\n                            type=\"button\"\n                            onClick={() => handleCreateCategory('main_category', newMainCategory)}\n                            className=\"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\"\n                            disabled={loading || !newMainCategory.trim()}\n                          >\n                            Create\n                          </button>\n                          <button\n                            type=\"button\"\n                            onClick={() => {\n                              setShowNewMainCategory(false);\n                              setNewMainCategory('');\n                            }}\n                            className=\"px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm\"\n                            disabled={loading}\n                          >\n                            Cancel\n                          </button>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Row 2: Category */}\n              <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${!selectedMainCategory ? 'opacity-50' : ''}`}>\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h4 className=\"font-medium text-green-900\">\n                    <span className=\"bg-green-100 px-2 py-1 rounded text-xs mr-2\">Level 2</span>\n                    Category\n                  </h4>\n                  {!selectedMainCategory && (\n                    <span className=\"text-xs text-gray-500\">Select a Main Category first</span>\n                  )}\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Select Existing Category\n                    </label>\n                    <select\n                      value={selectedCategory}\n                      onChange={(e) => setSelectedCategory(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\"\n                      disabled={loading || !selectedMainCategory}\n                    >\n                      <option value=\"\">Select a category</option>\n                      {selectedMainCategory && getCategoriesList(selectedMainCategory).map((cat) => (\n                        <option key={cat.id} value={cat.id}>{cat.name}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Or Create New Category\n                    </label>\n                    <div className=\"flex space-x-2\">\n                      {!showNewCategory ? (\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowNewCategory(true)}\n                          className=\"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\"\n                          disabled={loading || !selectedMainCategory}\n                        >\n                          <Plus className=\"h-4 w-4 inline mr-1\" />\n                          New\n                        </button>\n                      ) : (\n                        <>\n                          <input\n                            type=\"text\"\n                            value={newCategory}\n                            onChange={(e) => setNewCategory(e.target.value)}\n                            placeholder=\"Category name\"\n                            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\"\n                            disabled={loading}\n                          />\n                          <button\n                            type=\"button\"\n                            onClick={() => handleCreateCategory('category', newCategory, selectedMainCategory)}\n                            className=\"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\"\n                            disabled={loading || !newCategory.trim() || !selectedMainCategory}\n                          >\n                            Create\n                          </button>\n                          <button\n                            type=\"button\"\n                            onClick={() => {\n                              setShowNewCategory(false);\n                              setNewCategory('');\n                            }}\n                            className=\"px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm\"\n                            disabled={loading}\n                          >\n                            Cancel\n                          </button>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Row 3: Sub Category */}\n              <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${!selectedCategory ? 'opacity-50' : ''}`}>\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h4 className=\"font-medium text-yellow-900\">\n                    <span className=\"bg-yellow-100 px-2 py-1 rounded text-xs mr-2\">Level 3</span>\n                    Sub Category\n                  </h4>\n                  {!selectedCategory && (\n                    <span className=\"text-xs text-gray-500\">Select a Category first</span>\n                  )}\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Select Existing Sub Category\n                    </label>\n                    <select\n                      value={selectedSubCategory}\n                      onChange={(e) => setSelectedSubCategory(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500\"\n                      disabled={loading || !selectedCategory}\n                    >\n                      <option value=\"\">Select a sub category</option>\n                      {selectedCategory && getSubCategoriesList(selectedCategory).map((cat) => (\n                        <option key={cat.id} value={cat.id}>{cat.name}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Or Create New Sub Category\n                    </label>\n                    <div className=\"flex space-x-2\">\n                      {!showNewSubCategory ? (\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowNewSubCategory(true)}\n                          className=\"px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm\"\n                          disabled={loading || !selectedCategory}\n                        >\n                          <Plus className=\"h-4 w-4 inline mr-1\" />\n                          New\n                        </button>\n                      ) : (\n                        <>\n                          <input\n                            type=\"text\"\n                            value={newSubCategory}\n                            onChange={(e) => setNewSubCategory(e.target.value)}\n                            placeholder=\"Sub category name\"\n                            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500\"\n                            disabled={loading}\n                          />\n                          <button\n                            type=\"button\"\n                            onClick={() => handleCreateCategory('sub_category', newSubCategory, selectedCategory)}\n                            className=\"px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm\"\n                            disabled={loading || !newSubCategory.trim() || !selectedCategory}\n                          >\n                            Create\n                          </button>\n                          <button\n                            type=\"button\"\n                            onClick={() => {\n                              setShowNewSubCategory(false);\n                              setNewSubCategory('');\n                            }}\n                            className=\"px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm\"\n                            disabled={loading}\n                          >\n                            Cancel\n                          </button>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Row 4: Minor Category */}\n              <div className={`bg-purple-50 border border-purple-200 rounded-lg p-4 ${!selectedSubCategory ? 'opacity-50' : ''}`}>\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h4 className=\"font-medium text-purple-900\">\n                    <span className=\"bg-purple-100 px-2 py-1 rounded text-xs mr-2\">Level 4</span>\n                    Minor Category\n                  </h4>\n                  {!selectedSubCategory && (\n                    <span className=\"text-xs text-gray-500\">Select a Sub Category first</span>\n                  )}\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Select Existing Minor Category\n                    </label>\n                    <select\n                      value={selectedMinorCategory}\n                      onChange={(e) => setSelectedMinorCategory(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500\"\n                      disabled={loading || !selectedSubCategory}\n                    >\n                      <option value=\"\">Select a minor category</option>\n                      {selectedSubCategory && getMinorCategoriesList(selectedSubCategory).map((cat) => (\n                        <option key={cat.id} value={cat.id}>{cat.name}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Or Create New Minor Category\n                    </label>\n                    <div className=\"flex space-x-2\">\n                      {!showNewMinorCategory ? (\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowNewMinorCategory(true)}\n                          className=\"px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm\"\n                          disabled={loading || !selectedSubCategory}\n                        >\n                          <Plus className=\"h-4 w-4 inline mr-1\" />\n                          New\n                        </button>\n                      ) : (\n                        <>\n                          <input\n                            type=\"text\"\n                            value={newMinorCategory}\n                            onChange={(e) => setNewMinorCategory(e.target.value)}\n                            placeholder=\"Minor category name\"\n                            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500\"\n                            disabled={loading}\n                          />\n                          <button\n                            type=\"button\"\n                            onClick={() => handleCreateCategory('minor_category', newMinorCategory, selectedSubCategory)}\n                            className=\"px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm\"\n                            disabled={loading || !newMinorCategory.trim() || !selectedSubCategory}\n                          >\n                            Create\n                          </button>\n                          <button\n                            type=\"button\"\n                            onClick={() => {\n                              setShowNewMinorCategory(false);\n                              setNewMinorCategory('');\n                            }}\n                            className=\"px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm\"\n                            disabled={loading}\n                          >\n                            Cancel\n                          </button>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Description Field */}\n              <div>\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Description (Optional)\n                </label>\n                <textarea\n                  id=\"description\"\n                  value={description}\n                  onChange={(e) => setDescription(e.target.value)}\n                  rows={2}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter description for new categories\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Close Button */}\n              <div className=\"flex justify-end pt-4\">\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  className=\"px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors\"\n                  disabled={loading}\n                >\n                  Close\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CategoryCreator;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,CAAC,EAAEC,MAAM,EAAEC,UAAU,QAAQ,cAAc;AAC1D,SAASC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAS3F,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,MAAM;EACNC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAsB,EAAE,CAAC;EACrE,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACyB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1B,QAAQ,CAAS,EAAE,CAAC;EAC5E,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAS,EAAE,CAAC;EAC1E,MAAM,CAAC+B,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGhC,QAAQ,CAAS,EAAE,CAAC;;EAE9E;EACA,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAACyC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAMmD,aAKJ,GAAG,CACH;IACEC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,eAAe;IACtBJ,WAAW,EAAE,+CAA+C;IAC5DK,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,UAAU;IACjBJ,WAAW,EAAE,2CAA2C;IACxDK,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,cAAc;IACrBJ,WAAW,EAAE,qCAAqC;IAClDK,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,gBAAgB;IACvBJ,WAAW,EAAE,0CAA0C;IACvDK,KAAK,EAAE;EACT,CAAC,CACF;EAEDrD,SAAS,CAAC,MAAM;IACd,IAAIa,MAAM,EAAE;MACVyC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACzC,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAM0C,qBAAqB,GAAGA,CAAA,KAAMvC,UAAU,CAACwC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,eAAe,CAAC;EAC1F,MAAMC,iBAAiB,GAAIC,cAAsB,IAC/C5C,UAAU,CAACwC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,UAAU,IAAID,GAAG,CAACI,SAAS,KAAKD,cAAc,CAAC;EACvF,MAAME,oBAAoB,GAAIC,UAAkB,IAC9C/C,UAAU,CAACwC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,cAAc,IAAID,GAAG,CAACI,SAAS,KAAKE,UAAU,CAAC;EACvF,MAAMC,sBAAsB,GAAIC,aAAqB,IACnDjD,UAAU,CAACwC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,gBAAgB,IAAID,GAAG,CAACI,SAAS,KAAKI,aAAa,CAAC;;EAE5F;EACAjE,SAAS,CAAC,MAAM;IACd2B,mBAAmB,CAAC,EAAE,CAAC;IACvBE,sBAAsB,CAAC,EAAE,CAAC;IAC1BE,wBAAwB,CAAC,EAAE,CAAC;IAC5BY,kBAAkB,CAAC,KAAK,CAAC;IACzBE,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC,EAAE,CAACvB,oBAAoB,CAAC,CAAC;EAE1BxB,SAAS,CAAC,MAAM;IACd6B,sBAAsB,CAAC,EAAE,CAAC;IAC1BE,wBAAwB,CAAC,EAAE,CAAC;IAC5Bc,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC,EAAE,CAACrB,gBAAgB,CAAC,CAAC;EAEtB1B,SAAS,CAAC,MAAM;IACd+B,wBAAwB,CAAC,EAAE,CAAC;IAC5BgB,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC,EAAE,CAACnB,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMsC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAG,EAAE;IAEhB,IAAI3C,oBAAoB,EAAE;MACxB,MAAM4C,OAAO,GAAGpD,UAAU,CAACqD,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK9C,oBAAoB,CAAC;MACvE,IAAI4C,OAAO,EAAED,KAAK,CAACI,IAAI,CAACH,OAAO,CAACI,IAAI,CAAC;IACvC;IAEA,IAAI9C,gBAAgB,EAAE;MACpB,MAAM+B,GAAG,GAAGzC,UAAU,CAACqD,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK5C,gBAAgB,CAAC;MAC/D,IAAI+B,GAAG,EAAEU,KAAK,CAACI,IAAI,CAACd,GAAG,CAACe,IAAI,CAAC;IAC/B;IAEA,IAAI5C,mBAAmB,EAAE;MACvB,MAAM6C,MAAM,GAAGzD,UAAU,CAACqD,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAK1C,mBAAmB,CAAC;MACrE,IAAI6C,MAAM,EAAEN,KAAK,CAACI,IAAI,CAACE,MAAM,CAACD,IAAI,CAAC;IACrC;IAEA,IAAI1C,qBAAqB,EAAE;MACzB,MAAM4C,QAAQ,GAAG1D,UAAU,CAACqD,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAACa,EAAE,KAAKxC,qBAAqB,CAAC;MACzE,IAAI4C,QAAQ,EAAEP,KAAK,CAACI,IAAI,CAACG,QAAQ,CAACF,IAAI,CAAC;IACzC;IAEA,OAAOL,KAAK,CAACQ,IAAI,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMrB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyD,IAAI,GAAG,MAAMvE,aAAa,CAAC,CAAC,CAAC,CAAC;MACpCY,aAAa,CAAC2D,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZxD,QAAQ,CAAC,2BAA2B,CAAC;MACrCyD,OAAO,CAAC1D,KAAK,CAAC,2BAA2B,EAAEyD,GAAG,CAAC;IACjD,CAAC,SAAS;MACR1D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4D,oBAAoB,GAAG,MAAAA,CAAOrB,IAAsE,EAAEc,IAAY,EAAEQ,QAAiB,KAAK;IAC9I,IAAI,CAACR,IAAI,CAACS,IAAI,CAAC,CAAC,EAAE;MAChB5D,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEA,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMW,WAA2B,GAAG;QAClCsC,IAAI,EAAEA,IAAI,CAACS,IAAI,CAAC,CAAC;QACjBvB,IAAI,EAAEA,IAAI;QACVG,SAAS,EAAEmB,QAAQ;QACnBhC,WAAW,EAAEA,WAAW,CAACiC,IAAI,CAAC,CAAC,IAAIC,SAAS;QAC5CC,UAAU,EAAEnE,UAAU,CAACoE,MAAM,GAAG;MAClC,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAM/E,cAAc,CAAC4B,WAAW,CAAC;MAElD,IAAImD,QAAQ,CAAC/D,OAAO,EAAE;QACpBC,UAAU,CAAC,GAAGmC,IAAI,CAAC4B,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAKd,IAAI,yBAAyB,CAAC;;QAEvE;QACA,QAAQd,IAAI;UACV,KAAK,eAAe;YAClBzB,kBAAkB,CAAC,EAAE,CAAC;YACtBQ,sBAAsB,CAAC,KAAK,CAAC;YAC7B;UACF,KAAK,UAAU;YACbN,cAAc,CAAC,EAAE,CAAC;YAClBQ,kBAAkB,CAAC,KAAK,CAAC;YACzB;UACF,KAAK,cAAc;YACjBN,iBAAiB,CAAC,EAAE,CAAC;YACrBQ,qBAAqB,CAAC,KAAK,CAAC;YAC5B;UACF,KAAK,gBAAgB;YACnBN,mBAAmB,CAAC,EAAE,CAAC;YACvBQ,uBAAuB,CAAC,KAAK,CAAC;YAC9B;QACJ;QAEAE,cAAc,CAAC,EAAE,CAAC;;QAElB;QACA,MAAMK,cAAc,CAAC,CAAC;;QAEtB;QACA,IAAIvC,iBAAiB,IAAIsE,QAAQ,CAACE,QAAQ,EAAE;UAC1CxE,iBAAiB,CAACsE,QAAQ,CAACE,QAAQ,CAAC;QACtC;MACF;IACF,CAAC,CAAC,OAAOV,GAAQ,EAAE;MACjBxD,QAAQ,CAACwD,GAAG,CAACW,OAAO,IAAI,2BAA2B,CAAC;MACpDV,OAAO,CAAC1D,KAAK,CAAC,0BAA0B,EAAEyD,GAAG,CAAC;IAChD,CAAC,SAAS;MACR1D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsE,oBAAoB,GAAG,MAAAA,CAAO1B,UAAkB,EAAE2B,YAAoB,KAAK;IAC/E,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,iDAAiDF,YAAY,IAAI,CAAC,EAAE;MACtF;IACF;IAEA,IAAI;MACFvE,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMd,cAAc,CAACwD,UAAU,CAAC;MAChCxC,UAAU,CAAC,aAAamE,YAAY,yBAAyB,CAAC;;MAE9D;MACA,MAAMpC,cAAc,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOuB,GAAQ,EAAE;MACjBxD,QAAQ,CAACwD,GAAG,CAACW,OAAO,IAAI,2BAA2B,CAAC;MACpDV,OAAO,CAAC1D,KAAK,CAAC,0BAA0B,EAAEyD,GAAG,CAAC;IAChD,CAAC,SAAS;MACR1D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0E,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI7E,UAAU,CAACoE,MAAM,KAAK,CAAC,EAAE;MAC3B,oBACE3E,OAAA;QAAKqF,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CtF,OAAA,CAACL,UAAU;UAAC0F,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/D1F,OAAA;UAAAsF,QAAA,EAAG;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEV;;IAEA;IACA,MAAMC,iBAAiB,GAAGlD,aAAa,CAACmD,MAAM,CAAC,CAACC,GAAG,EAAE5C,IAAI,KAAK;MAC5D4C,GAAG,CAAC5C,IAAI,CAACP,KAAK,CAAC,GAAGnC,UAAU,CAACwC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAKA,IAAI,CAACP,KAAK,CAAC;MACnE,OAAOmD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAwC,CAAC;IAE7C,oBACE7F,OAAA;MAAKqF,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBtF,OAAA;QAAIqF,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEvEjD,aAAa,CAACqD,GAAG,CAAE7C,IAAI,IAAK;QAC3B,MAAM8C,gBAAgB,GAAGJ,iBAAiB,CAAC1C,IAAI,CAACP,KAAK,CAAC,IAAI,EAAE;QAC5D,IAAIqD,gBAAgB,CAACpB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAE9C,oBACE3E,OAAA;UAAsBqF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACzCtF,OAAA;YAAIqF,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBACjEtF,OAAA;cAAMqF,SAAS,EAAC,4CAA4C;cAAAC,QAAA,GAAC,QACrD,EAACrC,IAAI,CAACL,KAAK,GAAG,CAAC;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACNzC,IAAI,CAACN,KAAK,EAAC,IAAE,EAACoD,gBAAgB,CAACpB,MAAM,EAAC,GACzC;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJK,gBAAgB,CAACD,GAAG,CAAEhB,QAAQ,iBAC7B9E,OAAA;YAEEqF,SAAS,EAAC,yEAAyE;YACnFW,KAAK,EAAE;cAAEC,UAAU,EAAE,GAAGhD,IAAI,CAACL,KAAK,GAAG,EAAE,GAAG,EAAE;YAAK,CAAE;YAAA0C,QAAA,gBAEnDtF,OAAA;cAAKqF,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBtF,OAAA;gBAAKqF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CtF,OAAA;kBAAMqF,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAER,QAAQ,CAACf;gBAAI;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClE1F,OAAA;kBAAMqF,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAClErC,IAAI,CAACN;gBAAK;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACLZ,QAAQ,CAACvC,WAAW,iBACnBvC,OAAA;gBAAGqF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAER,QAAQ,CAACvC;cAAW;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACpE,eACD1F,OAAA;gBAAGqF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,QAAM,EAACR,QAAQ,CAACoB,SAAS;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACN1F,OAAA;cACEmG,OAAO,EAAEA,CAAA,KAAMnB,oBAAoB,CAACF,QAAQ,CAACjB,EAAE,EAAEiB,QAAQ,CAACf,IAAI,CAAE;cAChEsB,SAAS,EAAC,oEAAoE;cAC9Ee,KAAK,EAAC,iBAAiB;cACvBC,QAAQ,EAAE5F,OAAQ;cAAA6E,QAAA,eAElBtF,OAAA,CAACN,MAAM;gBAAC2F,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA,GAvBJZ,QAAQ,CAACjB,EAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBb,CACN,CAAC;QAAA,GAnCMzC,IAAI,CAACP,KAAK;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoCf,CAAC;MAEV,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,IAAI,CAACtF,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAKqF,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFtF,OAAA;MAAKqF,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBAC/FtF,OAAA;QAAKqF,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DtF,OAAA;UAAIqF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1F,OAAA;UACEmG,OAAO,EAAE9F,OAAQ;UACjBgF,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAE/DtF,OAAA,CAACP,CAAC;YAAC4F,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN1F,OAAA;QAAKqF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BtF,OAAA;UAAKqF,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/DtF,OAAA;YAAIqF,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpF1F,OAAA;YAAKqF,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CtF,OAAA;cAAKqF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtF,OAAA;gBAAMqF,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnE1F,OAAA;gBAAAsF,QAAA,gBAAMtF,OAAA;kBAAAsF,QAAA,EAAQ;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,2CAAuC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACN1F,OAAA;cAAKqF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtF,OAAA;gBAAMqF,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnE1F,OAAA;gBAAAsF,QAAA,gBAAMtF,OAAA;kBAAAsF,QAAA,EAAQ;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,yDAAqD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC,eACN1F,OAAA;cAAKqF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtF,OAAA;gBAAMqF,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnE1F,OAAA;gBAAAsF,QAAA,gBAAMtF,OAAA;kBAAAsF,QAAA,EAAQ;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gDAA4C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACN1F,OAAA;cAAKqF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtF,OAAA;gBAAMqF,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnE1F,OAAA;gBAAAsF,QAAA,gBAAMtF,OAAA;kBAAAsF,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,wDAAoD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1F,OAAA;YAAGqF,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GAAC,eACrC,eAAAtF,OAAA;cAAAsF,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wEAC1B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGL/E,KAAK,iBACJX,OAAA;UAAKqF,SAAS,EAAC,mEAAmE;UAAAC,QAAA,EAC/E3E;QAAK;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA7E,OAAO,iBACNb,OAAA;UAAKqF,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACrFzE;QAAO;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAGAN,kBAAkB,CAAC,CAAC,EAGpB3B,cAAc,CAAC,CAAC,iBACfzD,OAAA;UAAKqF,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eACjEtF,OAAA;YAAGqF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnCtF,OAAA;cAAAsF,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjC,cAAc,CAAC,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAGD1F,OAAA;UAAKqF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BtF,OAAA;YAAIqF,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE7E1F,OAAA;YAAKqF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBtF,OAAA;cAAKqF,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC/DtF,OAAA;gBAAKqF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrDtF,OAAA;kBAAIqF,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACvCtF,OAAA;oBAAMqF,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,iBAE7E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEN1F,OAAA;gBAAKqF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDtF,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAOqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR1F,OAAA;oBACE0C,KAAK,EAAE3B,oBAAqB;oBAC5BuF,QAAQ,EAAGC,CAAC,IAAKvF,uBAAuB,CAACuF,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;oBACzD2C,SAAS,EAAC,2GAA2G;oBACrHgB,QAAQ,EAAE5F,OAAQ;oBAAA6E,QAAA,gBAElBtF,OAAA;sBAAQ0C,KAAK,EAAC,EAAE;sBAAA4C,QAAA,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC/C5C,qBAAqB,CAAC,CAAC,CAACgD,GAAG,CAAE9C,GAAG,iBAC/BhD,OAAA;sBAAqB0C,KAAK,EAAEM,GAAG,CAACa,EAAG;sBAAAyB,QAAA,EAAEtC,GAAG,CAACe;oBAAI,GAAhCf,GAAG,CAACa,EAAE;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAmC,CACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN1F,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAOqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR1F,OAAA;oBAAKqF,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAC5B,CAACvD,mBAAmB,gBACnB/B,OAAA;sBACEiD,IAAI,EAAC,QAAQ;sBACbkD,OAAO,EAAEA,CAAA,KAAMnE,sBAAsB,CAAC,IAAI,CAAE;sBAC5CqD,SAAS,EAAC,yFAAyF;sBACnGgB,QAAQ,EAAE5F,OAAQ;sBAAA6E,QAAA,gBAElBtF,OAAA,CAACR,IAAI;wBAAC6F,SAAS,EAAC;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,OAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,gBAET1F,OAAA,CAAAE,SAAA;sBAAAoF,QAAA,gBACEtF,OAAA;wBACEiD,IAAI,EAAC,MAAM;wBACXP,KAAK,EAAEnB,eAAgB;wBACvB+E,QAAQ,EAAGC,CAAC,IAAK/E,kBAAkB,CAAC+E,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;wBACpD+D,WAAW,EAAC,oBAAoB;wBAChCpB,SAAS,EAAC,2GAA2G;wBACrHgB,QAAQ,EAAE5F;sBAAQ;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACF1F,OAAA;wBACEiD,IAAI,EAAC,QAAQ;wBACbkD,OAAO,EAAEA,CAAA,KAAM7B,oBAAoB,CAAC,eAAe,EAAE/C,eAAe,CAAE;wBACtE8D,SAAS,EAAC,2FAA2F;wBACrGgB,QAAQ,EAAE5F,OAAO,IAAI,CAACc,eAAe,CAACiD,IAAI,CAAC,CAAE;wBAAAc,QAAA,EAC9C;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT1F,OAAA;wBACEiD,IAAI,EAAC,QAAQ;wBACbkD,OAAO,EAAEA,CAAA,KAAM;0BACbnE,sBAAsB,CAAC,KAAK,CAAC;0BAC7BR,kBAAkB,CAAC,EAAE,CAAC;wBACxB,CAAE;wBACF6D,SAAS,EAAC,4FAA4F;wBACtGgB,QAAQ,EAAE5F,OAAQ;wBAAA6E,QAAA,EACnB;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1F,OAAA;cAAKqF,SAAS,EAAE,sDAAsD,CAACtE,oBAAoB,GAAG,YAAY,GAAG,EAAE,EAAG;cAAAuE,QAAA,gBAChHtF,OAAA;gBAAKqF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDtF,OAAA;kBAAIqF,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACxCtF,OAAA;oBAAMqF,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,YAE9E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACJ,CAAC3E,oBAAoB,iBACpBf,OAAA;kBAAMqF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC3E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1F,OAAA;gBAAKqF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDtF,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAOqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR1F,OAAA;oBACE0C,KAAK,EAAEzB,gBAAiB;oBACxBqF,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAACqF,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;oBACrD2C,SAAS,EAAC,6GAA6G;oBACvHgB,QAAQ,EAAE5F,OAAO,IAAI,CAACM,oBAAqB;oBAAAuE,QAAA,gBAE3CtF,OAAA;sBAAQ0C,KAAK,EAAC,EAAE;sBAAA4C,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC1C3E,oBAAoB,IAAImC,iBAAiB,CAACnC,oBAAoB,CAAC,CAAC+E,GAAG,CAAE9C,GAAG,iBACvEhD,OAAA;sBAAqB0C,KAAK,EAAEM,GAAG,CAACa,EAAG;sBAAAyB,QAAA,EAAEtC,GAAG,CAACe;oBAAI,GAAhCf,GAAG,CAACa,EAAE;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAmC,CACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN1F,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAOqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR1F,OAAA;oBAAKqF,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAC5B,CAACrD,eAAe,gBACfjC,OAAA;sBACEiD,IAAI,EAAC,QAAQ;sBACbkD,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,IAAI,CAAE;sBACxCmD,SAAS,EAAC,2FAA2F;sBACrGgB,QAAQ,EAAE5F,OAAO,IAAI,CAACM,oBAAqB;sBAAAuE,QAAA,gBAE3CtF,OAAA,CAACR,IAAI;wBAAC6F,SAAS,EAAC;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,OAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,gBAET1F,OAAA,CAAAE,SAAA;sBAAAoF,QAAA,gBACEtF,OAAA;wBACEiD,IAAI,EAAC,MAAM;wBACXP,KAAK,EAAEjB,WAAY;wBACnB6E,QAAQ,EAAGC,CAAC,IAAK7E,cAAc,CAAC6E,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;wBAChD+D,WAAW,EAAC,eAAe;wBAC3BpB,SAAS,EAAC,6GAA6G;wBACvHgB,QAAQ,EAAE5F;sBAAQ;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACF1F,OAAA;wBACEiD,IAAI,EAAC,QAAQ;wBACbkD,OAAO,EAAEA,CAAA,KAAM7B,oBAAoB,CAAC,UAAU,EAAE7C,WAAW,EAAEV,oBAAoB,CAAE;wBACnFsE,SAAS,EAAC,2FAA2F;wBACrGgB,QAAQ,EAAE5F,OAAO,IAAI,CAACgB,WAAW,CAAC+C,IAAI,CAAC,CAAC,IAAI,CAACzD,oBAAqB;wBAAAuE,QAAA,EACnE;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT1F,OAAA;wBACEiD,IAAI,EAAC,QAAQ;wBACbkD,OAAO,EAAEA,CAAA,KAAM;0BACbjE,kBAAkB,CAAC,KAAK,CAAC;0BACzBR,cAAc,CAAC,EAAE,CAAC;wBACpB,CAAE;wBACF2D,SAAS,EAAC,4FAA4F;wBACtGgB,QAAQ,EAAE5F,OAAQ;wBAAA6E,QAAA,EACnB;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1F,OAAA;cAAKqF,SAAS,EAAE,wDAAwD,CAACpE,gBAAgB,GAAG,YAAY,GAAG,EAAE,EAAG;cAAAqE,QAAA,gBAC9GtF,OAAA;gBAAKqF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDtF,OAAA;kBAAIqF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBACzCtF,OAAA;oBAAMqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,gBAE/E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACJ,CAACzE,gBAAgB,iBAChBjB,OAAA;kBAAMqF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACtE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1F,OAAA;gBAAKqF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDtF,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAOqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR1F,OAAA;oBACE0C,KAAK,EAAEvB,mBAAoB;oBAC3BmF,QAAQ,EAAGC,CAAC,IAAKnF,sBAAsB,CAACmF,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;oBACxD2C,SAAS,EAAC,+GAA+G;oBACzHgB,QAAQ,EAAE5F,OAAO,IAAI,CAACQ,gBAAiB;oBAAAqE,QAAA,gBAEvCtF,OAAA;sBAAQ0C,KAAK,EAAC,EAAE;sBAAA4C,QAAA,EAAC;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9CzE,gBAAgB,IAAIoC,oBAAoB,CAACpC,gBAAgB,CAAC,CAAC6E,GAAG,CAAE9C,GAAG,iBAClEhD,OAAA;sBAAqB0C,KAAK,EAAEM,GAAG,CAACa,EAAG;sBAAAyB,QAAA,EAAEtC,GAAG,CAACe;oBAAI,GAAhCf,GAAG,CAACa,EAAE;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAmC,CACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN1F,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAOqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR1F,OAAA;oBAAKqF,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAC5B,CAACnD,kBAAkB,gBAClBnC,OAAA;sBACEiD,IAAI,EAAC,QAAQ;sBACbkD,OAAO,EAAEA,CAAA,KAAM/D,qBAAqB,CAAC,IAAI,CAAE;sBAC3CiD,SAAS,EAAC,6FAA6F;sBACvGgB,QAAQ,EAAE5F,OAAO,IAAI,CAACQ,gBAAiB;sBAAAqE,QAAA,gBAEvCtF,OAAA,CAACR,IAAI;wBAAC6F,SAAS,EAAC;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,OAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,gBAET1F,OAAA,CAAAE,SAAA;sBAAAoF,QAAA,gBACEtF,OAAA;wBACEiD,IAAI,EAAC,MAAM;wBACXP,KAAK,EAAEf,cAAe;wBACtB2E,QAAQ,EAAGC,CAAC,IAAK3E,iBAAiB,CAAC2E,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;wBACnD+D,WAAW,EAAC,mBAAmB;wBAC/BpB,SAAS,EAAC,+GAA+G;wBACzHgB,QAAQ,EAAE5F;sBAAQ;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACF1F,OAAA;wBACEiD,IAAI,EAAC,QAAQ;wBACbkD,OAAO,EAAEA,CAAA,KAAM7B,oBAAoB,CAAC,cAAc,EAAE3C,cAAc,EAAEV,gBAAgB,CAAE;wBACtFoE,SAAS,EAAC,6FAA6F;wBACvGgB,QAAQ,EAAE5F,OAAO,IAAI,CAACkB,cAAc,CAAC6C,IAAI,CAAC,CAAC,IAAI,CAACvD,gBAAiB;wBAAAqE,QAAA,EAClE;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT1F,OAAA;wBACEiD,IAAI,EAAC,QAAQ;wBACbkD,OAAO,EAAEA,CAAA,KAAM;0BACb/D,qBAAqB,CAAC,KAAK,CAAC;0BAC5BR,iBAAiB,CAAC,EAAE,CAAC;wBACvB,CAAE;wBACFyD,SAAS,EAAC,4FAA4F;wBACtGgB,QAAQ,EAAE5F,OAAQ;wBAAA6E,QAAA,EACnB;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1F,OAAA;cAAKqF,SAAS,EAAE,wDAAwD,CAAClE,mBAAmB,GAAG,YAAY,GAAG,EAAE,EAAG;cAAAmE,QAAA,gBACjHtF,OAAA;gBAAKqF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDtF,OAAA;kBAAIqF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBACzCtF,OAAA;oBAAMqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,kBAE/E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACJ,CAACvE,mBAAmB,iBACnBnB,OAAA;kBAAMqF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1F,OAAA;gBAAKqF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDtF,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAOqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR1F,OAAA;oBACE0C,KAAK,EAAErB,qBAAsB;oBAC7BiF,QAAQ,EAAGC,CAAC,IAAKjF,wBAAwB,CAACiF,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;oBAC1D2C,SAAS,EAAC,+GAA+G;oBACzHgB,QAAQ,EAAE5F,OAAO,IAAI,CAACU,mBAAoB;oBAAAmE,QAAA,gBAE1CtF,OAAA;sBAAQ0C,KAAK,EAAC,EAAE;sBAAA4C,QAAA,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAChDvE,mBAAmB,IAAIoC,sBAAsB,CAACpC,mBAAmB,CAAC,CAAC2E,GAAG,CAAE9C,GAAG,iBAC1EhD,OAAA;sBAAqB0C,KAAK,EAAEM,GAAG,CAACa,EAAG;sBAAAyB,QAAA,EAAEtC,GAAG,CAACe;oBAAI,GAAhCf,GAAG,CAACa,EAAE;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAmC,CACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN1F,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAOqF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR1F,OAAA;oBAAKqF,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAC5B,CAACjD,oBAAoB,gBACpBrC,OAAA;sBACEiD,IAAI,EAAC,QAAQ;sBACbkD,OAAO,EAAEA,CAAA,KAAM7D,uBAAuB,CAAC,IAAI,CAAE;sBAC7C+C,SAAS,EAAC,6FAA6F;sBACvGgB,QAAQ,EAAE5F,OAAO,IAAI,CAACU,mBAAoB;sBAAAmE,QAAA,gBAE1CtF,OAAA,CAACR,IAAI;wBAAC6F,SAAS,EAAC;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,OAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,gBAET1F,OAAA,CAAAE,SAAA;sBAAAoF,QAAA,gBACEtF,OAAA;wBACEiD,IAAI,EAAC,MAAM;wBACXP,KAAK,EAAEb,gBAAiB;wBACxByE,QAAQ,EAAGC,CAAC,IAAKzE,mBAAmB,CAACyE,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;wBACrD+D,WAAW,EAAC,qBAAqB;wBACjCpB,SAAS,EAAC,+GAA+G;wBACzHgB,QAAQ,EAAE5F;sBAAQ;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACF1F,OAAA;wBACEiD,IAAI,EAAC,QAAQ;wBACbkD,OAAO,EAAEA,CAAA,KAAM7B,oBAAoB,CAAC,gBAAgB,EAAEzC,gBAAgB,EAAEV,mBAAmB,CAAE;wBAC7FkE,SAAS,EAAC,6FAA6F;wBACvGgB,QAAQ,EAAE5F,OAAO,IAAI,CAACoB,gBAAgB,CAAC2C,IAAI,CAAC,CAAC,IAAI,CAACrD,mBAAoB;wBAAAmE,QAAA,EACvE;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT1F,OAAA;wBACEiD,IAAI,EAAC,QAAQ;wBACbkD,OAAO,EAAEA,CAAA,KAAM;0BACb7D,uBAAuB,CAAC,KAAK,CAAC;0BAC9BR,mBAAmB,CAAC,EAAE,CAAC;wBACzB,CAAE;wBACFuD,SAAS,EAAC,4FAA4F;wBACtGgB,QAAQ,EAAE5F,OAAQ;wBAAA6E,QAAA,EACnB;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1F,OAAA;cAAAsF,QAAA,gBACEtF,OAAA;gBAAO0G,OAAO,EAAC,aAAa;gBAACrB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEtF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1F,OAAA;gBACE6D,EAAE,EAAC,aAAa;gBAChBnB,KAAK,EAAEH,WAAY;gBACnB+D,QAAQ,EAAGC,CAAC,IAAK/D,cAAc,CAAC+D,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;gBAChDiE,IAAI,EAAE,CAAE;gBACRtB,SAAS,EAAC,2GAA2G;gBACrHoB,WAAW,EAAC,sCAAsC;gBAClDJ,QAAQ,EAAE5F;cAAQ;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN1F,OAAA;cAAKqF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCtF,OAAA;gBACEiD,IAAI,EAAC,QAAQ;gBACbkD,OAAO,EAAE9F,OAAQ;gBACjBgF,SAAS,EAAC,oFAAoF;gBAC9FgB,QAAQ,EAAE5F,OAAQ;gBAAA6E,QAAA,EACnB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAED,eAAevF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}