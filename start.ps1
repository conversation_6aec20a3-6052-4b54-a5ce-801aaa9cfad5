# PowerShell script to start the RailGPT application
Write-Host "Starting RailGPT Application..." -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Clear browser cache suggestion
Write-Host "`nTip: If images still don't appear, try clearing your browser cache" -ForegroundColor Cyan
Write-Host "or open the app in an incognito/private window.`n" -ForegroundColor Cyan

# Check if node_modules exists, if not install dependencies
if (-not (Test-Path -Path ".\frontend\node_modules")) {
    Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
    Set-Location -Path .\frontend
    npm install
    Set-Location -Path ..
}

# Start the frontend application
Write-Host "Starting frontend application..." -ForegroundColor Cyan
Set-Location -Path .\frontend
try {
    # Set NODE_OPTIONS to use legacy OpenSSL provider if needed
    $env:NODE_OPTIONS="--openssl-legacy-provider"
    npm start
}
catch {
    Write-Host "Error starting frontend: $_" -ForegroundColor Red
    Write-Host "Please make sure Node.js is installed and try again." -ForegroundColor Red
}
finally {
    Set-Location -Path ..
}

Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 