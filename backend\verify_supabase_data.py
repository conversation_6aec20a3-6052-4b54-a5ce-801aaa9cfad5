"""
Script to verify Supabase data.
"""

import os
import sys
import logging
import json
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import Supabase client
from supabase_client import supabase

# Load environment variables
load_dotenv()

def query_documents():
    """Query documents from Supabase."""
    try:
        logger.info("Querying documents from Supabase...")
        
        # Query documents
        result = supabase.supabase.table("documents").select("*").execute()
        
        if hasattr(result, 'data'):
            doc_count = len(result.data)
            logger.info(f"Found {doc_count} documents in Supabase")
            
            # Log document data
            if doc_count > 0:
                for i, doc in enumerate(result.data[:5]):  # Show up to 5 documents
                    logger.info(f"Document {i+1}: {json.dumps(doc, indent=2)}")
            
            return result.data
        else:
            logger.error(f"Failed to query documents: {result}")
            return []
    except Exception as e:
        logger.error(f"Error querying documents: {str(e)}")
        return []

def query_document_chunks():
    """Query document chunks from Supabase."""
    try:
        logger.info("Querying document chunks from Supabase...")
        
        # Query document chunks
        result = supabase.supabase.table("document_chunks").select("*").execute()
        
        if hasattr(result, 'data'):
            chunk_count = len(result.data)
            logger.info(f"Found {chunk_count} document chunks in Supabase")
            
            # Log chunk data (without embeddings for brevity)
            if chunk_count > 0:
                for i, chunk in enumerate(result.data[:5]):  # Show up to 5 chunks
                    # Remove embedding for readability
                    chunk_copy = dict(chunk)
                    if "embedding" in chunk_copy:
                        chunk_copy["embedding"] = f"[{len(json.loads(chunk_copy['embedding'])) if isinstance(chunk_copy['embedding'], str) else 'unknown'} values]"
                    logger.info(f"Document Chunk {i+1}: {json.dumps(chunk_copy, indent=2)}")
            
            return result.data
        else:
            logger.error(f"Failed to query document chunks: {result}")
            return []
    except Exception as e:
        logger.error(f"Error querying document chunks: {str(e)}")
        return []

def query_websites():
    """Query websites from Supabase."""
    try:
        logger.info("Querying websites from Supabase...")
        
        # Query websites
        result = supabase.supabase.table("websites").select("*").execute()
        
        if hasattr(result, 'data'):
            website_count = len(result.data)
            logger.info(f"Found {website_count} websites in Supabase")
            
            # Log website data
            if website_count > 0:
                for i, website in enumerate(result.data[:5]):  # Show up to 5 websites
                    logger.info(f"Website {i+1}: {json.dumps(website, indent=2)}")
            
            return result.data
        else:
            logger.error(f"Failed to query websites: {result}")
            return []
    except Exception as e:
        logger.error(f"Error querying websites: {str(e)}")
        return []

def query_website_chunks():
    """Query website chunks from Supabase."""
    try:
        logger.info("Querying website chunks from Supabase...")
        
        # Query website chunks
        result = supabase.supabase.table("website_chunks").select("*").execute()
        
        if hasattr(result, 'data'):
            chunk_count = len(result.data)
            logger.info(f"Found {chunk_count} website chunks in Supabase")
            
            # Log chunk data (without embeddings for brevity)
            if chunk_count > 0:
                for i, chunk in enumerate(result.data[:5]):  # Show up to 5 chunks
                    # Remove embedding for readability
                    chunk_copy = dict(chunk)
                    if "embedding" in chunk_copy:
                        chunk_copy["embedding"] = f"[{len(json.loads(chunk_copy['embedding'])) if isinstance(chunk_copy['embedding'], str) else 'unknown'} values]"
                    logger.info(f"Website Chunk {i+1}: {json.dumps(chunk_copy, indent=2)}")
            
            return result.data
        else:
            logger.error(f"Failed to query website chunks: {result}")
            return []
    except Exception as e:
        logger.error(f"Error querying website chunks: {str(e)}")
        return []

def run_verification():
    """Run verification of Supabase data."""
    logger.info("Starting Supabase data verification...")
    
    # Query documents
    documents = query_documents()
    
    # Query document chunks
    document_chunks = query_document_chunks()
    
    # Query websites
    websites = query_websites()
    
    # Query website chunks
    website_chunks = query_website_chunks()
    
    # Print summary
    logger.info("\nSUMMARY:")
    logger.info(f"Documents: {len(documents)}")
    logger.info(f"Document Chunks: {len(document_chunks)}")
    logger.info(f"Websites: {len(websites)}")
    logger.info(f"Website Chunks: {len(website_chunks)}")
    
    # Check if we have data in all tables
    if documents and document_chunks and websites and website_chunks:
        logger.info("\nVERIFICATION RESULT: PASSED")
        logger.info("Data found in all tables. Supabase integration is working correctly.")
    else:
        logger.info("\nVERIFICATION RESULT: PARTIAL")
        logger.info("Data not found in all tables. Some aspects of Supabase integration may not be working correctly.")
        
        # Provide more detailed information
        if not documents:
            logger.warning("No documents found in the documents table.")
        if not document_chunks:
            logger.warning("No document chunks found in the document_chunks table.")
        if not websites:
            logger.warning("No websites found in the websites table.")
        if not website_chunks:
            logger.warning("No website chunks found in the website_chunks table.")

if __name__ == "__main__":
    run_verification()
