<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>RailGPT Chat Functionality Test</h1>
    
    <div class="test-section">
        <h2>Supabase Connection Test</h2>
        <button onclick="testSupabaseConnection()">Test Connection</button>
        <div id="connection-result"></div>
    </div>

    <div class="test-section">
        <h2>Chat Session Management</h2>
        <button onclick="testCreateChatSession()">Create Chat Session</button>
        <button onclick="testGetChatSessions()">Get Chat Sessions</button>
        <button onclick="testUpdateChatSession()">Update Chat Session</button>
        <button onclick="testDeleteChatSession()">Delete Test Sessions</button>
        <div id="chat-session-result"></div>
    </div>

    <div class="test-section">
        <h2>Message Management</h2>
        <button onclick="testSaveMessages()">Save Messages</button>
        <button onclick="testLoadMessages()">Load Messages</button>
        <div id="message-result"></div>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="test-log" class="log"></div>
    </div>

    <script>
        const SUPABASE_URL = "https://rkllidjktazafeinezgo.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA";
        
        let testChatId = null;

        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        function setResult(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        async function makeSupabaseRequest(endpoint, method = 'GET', data = null) {
            const headers = {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            };

            const config = {
                method,
                headers
            };

            if (data) {
                config.body = JSON.stringify(data);
            }

            const response = await fetch(`${SUPABASE_URL}/rest/v1/${endpoint}`, config);
            return { response, data: await response.json() };
        }

        async function testSupabaseConnection() {
            log('Testing Supabase connection...');
            setResult('connection-result', 'Testing connection...', 'loading');
            
            try {
                const { response, data } = await makeSupabaseRequest('chat_sessions?limit=1');
                
                if (response.ok) {
                    setResult('connection-result', '✅ Supabase connection successful!', 'success');
                    log('✅ Supabase connection successful');
                } else {
                    setResult('connection-result', `❌ Connection failed: ${response.status}`, 'error');
                    log(`❌ Connection failed: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                setResult('connection-result', `❌ Connection error: ${error.message}`, 'error');
                log(`❌ Connection error: ${error.message}`);
            }
        }

        async function testCreateChatSession() {
            log('Creating test chat session...');
            setResult('chat-session-result', 'Creating chat session...', 'loading');
            
            try {
                const chatData = {
                    title: 'Test Chat Session',
                    messages: [],
                    model_used: 'gemini-2.0-flash',
                    has_document: false,
                    has_website: false
                };

                const { response, data } = await makeSupabaseRequest('chat_sessions', 'POST', chatData);
                
                if (response.status === 201) {
                    testChatId = data[0].id;
                    setResult('chat-session-result', `✅ Chat session created: ${testChatId}`, 'success');
                    log(`✅ Chat session created: ${testChatId}`);
                } else {
                    setResult('chat-session-result', `❌ Failed to create session: ${response.status}`, 'error');
                    log(`❌ Failed to create session: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                setResult('chat-session-result', `❌ Error: ${error.message}`, 'error');
                log(`❌ Create session error: ${error.message}`);
            }
        }

        async function testGetChatSessions() {
            log('Getting chat sessions...');
            setResult('chat-session-result', 'Loading chat sessions...', 'loading');
            
            try {
                const { response, data } = await makeSupabaseRequest('chat_sessions?order=updated_at.desc&limit=5');
                
                if (response.ok) {
                    setResult('chat-session-result', `✅ Found ${data.length} chat sessions`, 'success');
                    log(`✅ Found ${data.length} chat sessions`);
                    data.forEach(session => {
                        log(`  - ${session.title} (${session.id.substring(0, 8)}...)`);
                    });
                } else {
                    setResult('chat-session-result', `❌ Failed to get sessions: ${response.status}`, 'error');
                    log(`❌ Failed to get sessions: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                setResult('chat-session-result', `❌ Error: ${error.message}`, 'error');
                log(`❌ Get sessions error: ${error.message}`);
            }
        }

        async function testSaveMessages() {
            if (!testChatId) {
                setResult('message-result', '❌ No test chat session. Create one first.', 'error');
                return;
            }

            log('Saving test messages...');
            setResult('message-result', 'Saving messages...', 'loading');
            
            try {
                const messages = [
                    {
                        id: 'user-1',
                        content: 'Hello, what is Indian Railways?',
                        sender: 'user',
                        timestamp: new Date().toISOString(),
                        chatId: testChatId
                    },
                    {
                        id: 'ai-1',
                        content: 'Indian Railways is the national railway system of India.',
                        sender: 'ai',
                        timestamp: new Date().toISOString(),
                        chatId: testChatId,
                        llm_model: 'gemini-2.0-flash'
                    }
                ];

                const updateData = {
                    messages: messages,
                    has_document: false,
                    has_website: false
                };

                const { response, data } = await makeSupabaseRequest(`chat_sessions?id=eq.${testChatId}`, 'PATCH', updateData);
                
                if (response.status === 204) {
                    setResult('message-result', '✅ Messages saved successfully!', 'success');
                    log('✅ Messages saved successfully');
                } else {
                    setResult('message-result', `❌ Failed to save messages: ${response.status}`, 'error');
                    log(`❌ Failed to save messages: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                setResult('message-result', `❌ Error: ${error.message}`, 'error');
                log(`❌ Save messages error: ${error.message}`);
            }
        }

        async function testLoadMessages() {
            if (!testChatId) {
                setResult('message-result', '❌ No test chat session. Create one first.', 'error');
                return;
            }

            log('Loading messages...');
            setResult('message-result', 'Loading messages...', 'loading');
            
            try {
                const { response, data } = await makeSupabaseRequest(`chat_sessions?id=eq.${testChatId}`);
                
                if (response.ok && data.length > 0) {
                    const session = data[0];
                    const messageCount = session.messages ? session.messages.length : 0;
                    setResult('message-result', `✅ Loaded ${messageCount} messages`, 'success');
                    log(`✅ Loaded ${messageCount} messages from session: ${session.title}`);
                    
                    if (session.messages) {
                        session.messages.forEach(msg => {
                            log(`  - ${msg.sender}: ${msg.content.substring(0, 50)}...`);
                        });
                    }
                } else {
                    setResult('message-result', `❌ Failed to load messages: ${response.status}`, 'error');
                    log(`❌ Failed to load messages: ${response.status}`);
                }
            } catch (error) {
                setResult('message-result', `❌ Error: ${error.message}`, 'error');
                log(`❌ Load messages error: ${error.message}`);
            }
        }

        async function testUpdateChatSession() {
            if (!testChatId) {
                await testCreateChatSession();
                if (!testChatId) return;
            }

            log('Updating chat session title...');
            setResult('chat-session-result', 'Updating session...', 'loading');
            
            try {
                const updateData = {
                    title: 'Updated Test Chat Session'
                };

                const { response, data } = await makeSupabaseRequest(`chat_sessions?id=eq.${testChatId}`, 'PATCH', updateData);
                
                if (response.status === 204) {
                    setResult('chat-session-result', '✅ Chat session updated successfully!', 'success');
                    log('✅ Chat session title updated');
                } else {
                    setResult('chat-session-result', `❌ Failed to update session: ${response.status}`, 'error');
                    log(`❌ Failed to update session: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                setResult('chat-session-result', `❌ Error: ${error.message}`, 'error');
                log(`❌ Update session error: ${error.message}`);
            }
        }

        async function testDeleteChatSession() {
            log('Deleting test chat sessions...');
            setResult('chat-session-result', 'Deleting test sessions...', 'loading');
            
            try {
                const { response, data } = await makeSupabaseRequest('chat_sessions?title=like.*Test*', 'DELETE');
                
                if (response.status === 204) {
                    setResult('chat-session-result', '✅ Test sessions deleted successfully!', 'success');
                    log('✅ Test chat sessions deleted');
                    testChatId = null;
                } else {
                    setResult('chat-session-result', `❌ Failed to delete sessions: ${response.status}`, 'error');
                    log(`❌ Failed to delete sessions: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                setResult('chat-session-result', `❌ Error: ${error.message}`, 'error');
                log(`❌ Delete sessions error: ${error.message}`);
            }
        }

        // Auto-run connection test on page load
        window.onload = function() {
            log('Page loaded. Ready to test chat functionality.');
            testSupabaseConnection();
        };
    </script>
</body>
</html>
