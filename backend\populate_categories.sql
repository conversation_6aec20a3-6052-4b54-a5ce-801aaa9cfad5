-- Populate Categories for RailGPT Application
-- This script creates a comprehensive categorization system for documents and websites

-- First, create the new schema tables if they don't exist
-- (This ensures the script can be run independently)

-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create categories table for hierarchical categorization
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('main_category', 'category', 'sub_category', 'minor_category')),
    parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name, type, parent_id)
);

-- Create website_categories table for website domain categorization
CREATE TABLE IF NOT EXISTS website_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Clear existing data (for fresh start)
DELETE FROM categories;
DELETE FROM website_categories;

-- Insert Main Categories for Documents
INSERT INTO categories (name, type, description, sort_order) VALUES
('Safety', 'main_category', 'Safety-related documents including guidelines, protocols, and procedures', 1),
('Technical', 'main_category', 'Technical documentation including manuals, specifications, and engineering documents', 2),
('Operations', 'main_category', 'Operational documents including schedules, procedures, and workflows', 3),
('Administrative', 'main_category', 'Administrative documents including policies, meeting minutes, and reports', 4),
('Regulatory', 'main_category', 'Regulatory and compliance documents including rules, circulars, and notifications', 5),
('Training', 'main_category', 'Training materials, handbooks, and educational resources', 6),
('Finance', 'main_category', 'Financial documents including budgets, reports, and accounting procedures', 7),
('Infrastructure', 'main_category', 'Infrastructure-related documents including construction, maintenance, and development', 8);

-- Insert Categories (second level)
-- Safety Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Guidelines', 'category', (SELECT id FROM categories WHERE name = 'Safety' AND type = 'main_category'), 'Safety guidelines and best practices', 1),
('Protocols', 'category', (SELECT id FROM categories WHERE name = 'Safety' AND type = 'main_category'), 'Safety protocols and emergency procedures', 2),
('Inspections', 'category', (SELECT id FROM categories WHERE name = 'Safety' AND type = 'main_category'), 'Safety inspection reports and checklists', 3),
('Incidents', 'category', (SELECT id FROM categories WHERE name = 'Safety' AND type = 'main_category'), 'Incident reports and analysis', 4);

-- Technical Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Manuals', 'category', (SELECT id FROM categories WHERE name = 'Technical' AND type = 'main_category'), 'Technical manuals and documentation', 1),
('Specifications', 'category', (SELECT id FROM categories WHERE name = 'Technical' AND type = 'main_category'), 'Technical specifications and standards', 2),
('Drawings', 'category', (SELECT id FROM categories WHERE name = 'Technical' AND type = 'main_category'), 'Engineering drawings and blueprints', 3),
('Maintenance', 'category', (SELECT id FROM categories WHERE name = 'Technical' AND type = 'main_category'), 'Maintenance procedures and schedules', 4);

-- Operations Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Schedules', 'category', (SELECT id FROM categories WHERE name = 'Operations' AND type = 'main_category'), 'Operational schedules and timetables', 1),
('Procedures', 'category', (SELECT id FROM categories WHERE name = 'Operations' AND type = 'main_category'), 'Standard operating procedures', 2),
('Reports', 'category', (SELECT id FROM categories WHERE name = 'Operations' AND type = 'main_category'), 'Operational reports and analytics', 3),
('Planning', 'category', (SELECT id FROM categories WHERE name = 'Operations' AND type = 'main_category'), 'Operational planning documents', 4);

-- Administrative Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Policies', 'category', (SELECT id FROM categories WHERE name = 'Administrative' AND type = 'main_category'), 'Organizational policies and procedures', 1),
('Meeting Minutes', 'category', (SELECT id FROM categories WHERE name = 'Administrative' AND type = 'main_category'), 'Meeting minutes and records', 2),
('Handbooks', 'category', (SELECT id FROM categories WHERE name = 'Administrative' AND type = 'main_category'), 'Employee handbooks and guides', 3),
('Correspondence', 'category', (SELECT id FROM categories WHERE name = 'Administrative' AND type = 'main_category'), 'Official correspondence and communications', 4);

-- Regulatory Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Circulars', 'category', (SELECT id FROM categories WHERE name = 'Regulatory' AND type = 'main_category'), 'Official circulars and notifications', 1),
('Rules', 'category', (SELECT id FROM categories WHERE name = 'Regulatory' AND type = 'main_category'), 'Rules and regulations', 2),
('Compliance', 'category', (SELECT id FROM categories WHERE name = 'Regulatory' AND type = 'main_category'), 'Compliance documents and audits', 3),
('Legal', 'category', (SELECT id FROM categories WHERE name = 'Regulatory' AND type = 'main_category'), 'Legal documents and contracts', 4);

-- Training Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Courses', 'category', (SELECT id FROM categories WHERE name = 'Training' AND type = 'main_category'), 'Training courses and curricula', 1),
('Materials', 'category', (SELECT id FROM categories WHERE name = 'Training' AND type = 'main_category'), 'Training materials and resources', 2),
('Assessments', 'category', (SELECT id FROM categories WHERE name = 'Training' AND type = 'main_category'), 'Training assessments and evaluations', 3),
('Certifications', 'category', (SELECT id FROM categories WHERE name = 'Training' AND type = 'main_category'), 'Certification requirements and records', 4);

-- Finance Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Budgets', 'category', (SELECT id FROM categories WHERE name = 'Finance' AND type = 'main_category'), 'Budget documents and planning', 1),
('Accounts', 'category', (SELECT id FROM categories WHERE name = 'Finance' AND type = 'main_category'), 'Accounting records and statements', 2),
('Audits', 'category', (SELECT id FROM categories WHERE name = 'Finance' AND type = 'main_category'), 'Financial audits and reviews', 3),
('Procurement', 'category', (SELECT id FROM categories WHERE name = 'Finance' AND type = 'main_category'), 'Procurement and purchasing documents', 4);

-- Infrastructure Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Construction', 'category', (SELECT id FROM categories WHERE name = 'Infrastructure' AND type = 'main_category'), 'Construction projects and documentation', 1),
('Development', 'category', (SELECT id FROM categories WHERE name = 'Infrastructure' AND type = 'main_category'), 'Infrastructure development plans', 2),
('Upgrades', 'category', (SELECT id FROM categories WHERE name = 'Infrastructure' AND type = 'main_category'), 'Infrastructure upgrade projects', 3),
('Assets', 'category', (SELECT id FROM categories WHERE name = 'Infrastructure' AND type = 'main_category'), 'Asset management and inventory', 4);

-- Insert Sub-Categories (third level)
-- Technical > Manuals Sub-Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Diesel Loco', 'sub_category', (SELECT id FROM categories WHERE name = 'Manuals' AND type = 'category'), 'Diesel locomotive manuals and documentation', 1),
('Electric Loco', 'sub_category', (SELECT id FROM categories WHERE name = 'Manuals' AND type = 'category'), 'Electric locomotive manuals and documentation', 2),
('Rolling Stock', 'sub_category', (SELECT id FROM categories WHERE name = 'Manuals' AND type = 'category'), 'Rolling stock manuals and documentation', 3),
('Signaling', 'sub_category', (SELECT id FROM categories WHERE name = 'Manuals' AND type = 'category'), 'Signaling system manuals', 4),
('Track', 'sub_category', (SELECT id FROM categories WHERE name = 'Manuals' AND type = 'category'), 'Track maintenance and construction manuals', 5);

-- Operations > Schedules Sub-Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Passenger', 'sub_category', (SELECT id FROM categories WHERE name = 'Schedules' AND type = 'category'), 'Passenger train schedules', 1),
('Freight', 'sub_category', (SELECT id FROM categories WHERE name = 'Schedules' AND type = 'category'), 'Freight train schedules', 2),
('Maintenance Windows', 'sub_category', (SELECT id FROM categories WHERE name = 'Schedules' AND type = 'category'), 'Maintenance window schedules', 3),
('Special Services', 'sub_category', (SELECT id FROM categories WHERE name = 'Schedules' AND type = 'category'), 'Special service schedules', 4);

-- Administrative > Handbooks Sub-Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Station Operations', 'sub_category', (SELECT id FROM categories WHERE name = 'Handbooks' AND type = 'category'), 'Station operation handbooks', 1),
('Employee Guidelines', 'sub_category', (SELECT id FROM categories WHERE name = 'Handbooks' AND type = 'category'), 'Employee guidelines and procedures', 2),
('Customer Service', 'sub_category', (SELECT id FROM categories WHERE name = 'Handbooks' AND type = 'category'), 'Customer service handbooks', 3),
('Emergency Procedures', 'sub_category', (SELECT id FROM categories WHERE name = 'Handbooks' AND type = 'category'), 'Emergency procedure handbooks', 4);

-- Safety > Guidelines Sub-Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('General', 'sub_category', (SELECT id FROM categories WHERE name = 'Guidelines' AND type = 'category'), 'General safety guidelines', 1),
('Operational Safety', 'sub_category', (SELECT id FROM categories WHERE name = 'Guidelines' AND type = 'category'), 'Operational safety guidelines', 2),
('Equipment Safety', 'sub_category', (SELECT id FROM categories WHERE name = 'Guidelines' AND type = 'category'), 'Equipment safety guidelines', 3),
('Personnel Safety', 'sub_category', (SELECT id FROM categories WHERE name = 'Guidelines' AND type = 'category'), 'Personnel safety guidelines', 4);

-- Training > Courses Sub-Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Technical Training', 'sub_category', (SELECT id FROM categories WHERE name = 'Courses' AND type = 'category'), 'Technical training courses', 1),
('Safety Training', 'sub_category', (SELECT id FROM categories WHERE name = 'Courses' AND type = 'category'), 'Safety training courses', 2),
('Management Training', 'sub_category', (SELECT id FROM categories WHERE name = 'Courses' AND type = 'category'), 'Management training courses', 3),
('Operational Training', 'sub_category', (SELECT id FROM categories WHERE name = 'Courses' AND type = 'category'), 'Operational training courses', 4);

-- Insert Minor Categories (fourth level)
-- Administrative > Handbooks > Station Operations Minor Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Management', 'minor_category', (SELECT id FROM categories WHERE name = 'Station Operations' AND type = 'sub_category'), 'Station management procedures', 1),
('Operations', 'minor_category', (SELECT id FROM categories WHERE name = 'Station Operations' AND type = 'sub_category'), 'Day-to-day station operations', 2),
('Maintenance', 'minor_category', (SELECT id FROM categories WHERE name = 'Station Operations' AND type = 'sub_category'), 'Station maintenance procedures', 3),
('Security', 'minor_category', (SELECT id FROM categories WHERE name = 'Station Operations' AND type = 'sub_category'), 'Station security procedures', 4);

-- Technical > Manuals > Diesel Loco Minor Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Engine Systems', 'minor_category', (SELECT id FROM categories WHERE name = 'Diesel Loco' AND type = 'sub_category'), 'Diesel engine systems documentation', 1),
('Electrical Systems', 'minor_category', (SELECT id FROM categories WHERE name = 'Diesel Loco' AND type = 'sub_category'), 'Electrical systems documentation', 2),
('Brake Systems', 'minor_category', (SELECT id FROM categories WHERE name = 'Diesel Loco' AND type = 'sub_category'), 'Brake systems documentation', 3),
('Control Systems', 'minor_category', (SELECT id FROM categories WHERE name = 'Diesel Loco' AND type = 'sub_category'), 'Control systems documentation', 4);

-- Technical > Manuals > Electric Loco Minor Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Traction Motors', 'minor_category', (SELECT id FROM categories WHERE name = 'Electric Loco' AND type = 'sub_category'), 'Traction motor documentation', 1),
('Power Electronics', 'minor_category', (SELECT id FROM categories WHERE name = 'Electric Loco' AND type = 'sub_category'), 'Power electronics documentation', 2),
('Pantograph Systems', 'minor_category', (SELECT id FROM categories WHERE name = 'Electric Loco' AND type = 'sub_category'), 'Pantograph systems documentation', 3),
('Auxiliary Systems', 'minor_category', (SELECT id FROM categories WHERE name = 'Electric Loco' AND type = 'sub_category'), 'Auxiliary systems documentation', 4);

-- Operations > Schedules > Passenger Minor Categories
INSERT INTO categories (name, type, parent_id, description, sort_order) VALUES
('Express Trains', 'minor_category', (SELECT id FROM categories WHERE name = 'Passenger' AND type = 'sub_category'), 'Express train schedules', 1),
('Local Trains', 'minor_category', (SELECT id FROM categories WHERE name = 'Passenger' AND type = 'sub_category'), 'Local train schedules', 2),
('Suburban Trains', 'minor_category', (SELECT id FROM categories WHERE name = 'Passenger' AND type = 'sub_category'), 'Suburban train schedules', 3),
('Special Trains', 'minor_category', (SELECT id FROM categories WHERE name = 'Passenger' AND type = 'sub_category'), 'Special train schedules', 4);

-- Website Categories
INSERT INTO website_categories (name, description, sort_order) VALUES
('Official Railways', 'Official Indian Railways and government railway websites', 1),
('News Portals', 'News websites covering railway and transportation news', 2),
('Travel Guides', 'Travel and tourism websites with railway information', 3),
('Government Sites', 'Government websites with railway-related information', 4),
('Educational Resources', 'Educational and training websites for railway professionals', 5),
('Technical Resources', 'Technical documentation and engineering resources', 6),
('Industry Publications', 'Railway industry publications and journals', 7),
('Research Organizations', 'Railway research and development organizations', 8),
('Equipment Manufacturers', 'Railway equipment and technology manufacturers', 9),
('Transportation Authorities', 'Regional and local transportation authorities', 10),
('Safety Organizations', 'Railway safety and regulatory organizations', 11),
('Professional Associations', 'Railway professional associations and societies', 12);

-- Create useful views for category management
CREATE OR REPLACE VIEW category_hierarchy AS
WITH RECURSIVE category_tree AS (
    -- Base case: main categories
    SELECT
        id,
        name,
        type,
        parent_id,
        description,
        sort_order,
        name as full_path,
        1 as level
    FROM categories
    WHERE type = 'main_category'

    UNION ALL

    -- Recursive case: child categories
    SELECT
        c.id,
        c.name,
        c.type,
        c.parent_id,
        c.description,
        c.sort_order,
        ct.full_path || ' > ' || c.name as full_path,
        ct.level + 1 as level
    FROM categories c
    INNER JOIN category_tree ct ON c.parent_id = ct.id
)
SELECT * FROM category_tree
ORDER BY full_path;

-- Create function to get category path
CREATE OR REPLACE FUNCTION get_category_path(category_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    result TEXT := '';
    current_id UUID := category_id;
    current_name TEXT;
    parent_id UUID;
BEGIN
    WHILE current_id IS NOT NULL LOOP
        SELECT name, parent_id INTO current_name, parent_id
        FROM categories
        WHERE id = current_id;

        IF current_name IS NULL THEN
            EXIT;
        END IF;

        IF result = '' THEN
            result := current_name;
        ELSE
            result := current_name || ' > ' || result;
        END IF;

        current_id := parent_id;
    END LOOP;

    RETURN result;
END;
$$;

-- Create indexes for the new tables
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);
CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);
CREATE INDEX IF NOT EXISTS idx_website_categories_name ON website_categories(name);

-- Display summary of created categories
SELECT
    'Document Categories Created' as summary,
    COUNT(*) as count
FROM categories
UNION ALL
SELECT
    'Website Categories Created' as summary,
    COUNT(*) as count
FROM website_categories
UNION ALL
SELECT
    type || ' Categories' as summary,
    COUNT(*) as count
FROM categories
GROUP BY type
ORDER BY summary;
