"""
Fix timeout issues in the IR App by optimizing document search and LLM response handling.
"""
import os
import re

def update_llm_router():
    """Update llm_router.py to handle timeouts better."""
    try:
        with open("llm_router.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 1. Find the generate_answer function
        gen_answer_pos = content.find("def generate_answer(")
        
        if gen_answer_pos == -1:
            print("ERROR: Could not find generate_answer function")
            return False
        
        # 2. Increase the LLM timeout
        if "timeout=" in content:
            # Update existing timeout
            content = re.sub(
                r'timeout=\d+',
                'timeout=60',  # Increase to 60 seconds
                content
            )
            print("Updated LLM timeout to 60 seconds")
        
        # 3. Add retry logic for model timeouts
        if "response = client.chat.completions.create" in content:
            # Find the chat completion block
            chat_pos = content.find("response = client.chat.completions.create")
            
            if chat_pos != -1:
                # Find where this block ends (next line with same or less indentation)
                indent_level = 0
                for i in range(chat_pos-1, 0, -1):
                    if content[i] == '\n':
                        break
                    indent_level += 1
                
                # Create retry block
                retry_block = """    # Add retry logic for timeouts
    retry_count = 0
    max_retries = 2
    while retry_count <= max_retries:
        try:
            response = client.chat.completions.create(
                model=model_to_use["model_id"],
                messages=messages,
                temperature=0.3,
                timeout=60  # Increase timeout to 60 seconds
            )
            break  # Success, exit the retry loop
        except Exception as e:
            retry_count += 1
            if "timeout" in str(e).lower() and retry_count <= max_retries:
                logger.warning(f"Timeout occurred. Retrying ({retry_count}/{max_retries})...")
                # Try with a simpler prompt on retries
                if retry_count == max_retries:
                    # On last retry, simplify the messages to speed up processing
                    simple_prompt = f"Answer this question based on the provided context. Question: {query}"
                    messages = [
                        {"role": "system", "content": "You are a helpful assistant. Be concise."},
                        {"role": "user", "content": simple_prompt}
                    ]
            else:
                # Non-timeout error or max retries reached
                if retry_count > max_retries:
                    logger.error(f"Max retries reached. Error: {str(e)}")
                else:
                    logger.error(f"Error calling LLM API: {str(e)}")
                raise"""
                
                # Replace the original chat completion block
                content = content.replace(
                    "    response = client.chat.completions.create", 
                    retry_block
                )
                print("Added retry logic for timeouts")
        
        # 4. Save the updated file
        with open("llm_router.py", "w", encoding="utf-8") as f:
            f.write(content)
            
        return True
    except Exception as e:
        print(f"ERROR updating llm_router.py: {str(e)}")
        return False

def update_server_file():
    """Update server.py to optimize document search."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 1. Optimize document search by limiting chunks
        if "find_similar_chunks(" in content:
            # Lower the number of chunks returned to speed up processing
            content = re.sub(
                r'top_k=50',
                'top_k=20',  # Reduce chunks to process
                content
            )
            print("Reduced number of chunks processed to speed up queries")
        
        # 2. Add timeout handling for query processing
        if "def process_query(" in content:
            # Find the process_query function
            process_query_pos = content.find("def process_query(")
            
            if process_query_pos != -1:
                # Find where the try block starts
                try_pos = content.find("    try:", process_query_pos)
                
                if try_pos != -1:
                    # Add timeout information in the except block
                    except_pos = content.find("    except Exception as e:", try_pos)
                    
                    if except_pos != -1:
                        # Find the error handling code after except
                        error_log_pos = content.find("logger.error", except_pos)
                        
                        if error_log_pos != -1:
                            # Insert timeout specific handling
                            timeout_handling = """        # Check specifically for timeout errors
        if "timeout" in str(e).lower():
            logger.error(f"Query processing timed out: {str(e)}")
            return QueryResponse(
                answer="Your query timed out due to the complexity of processing many document sources. Please try a simpler query or wait a moment and try again.",
                sources=[],
                document_sources=[],
                website_sources=[],
                llm_model=model_id,
                llm_fallback=False
            )
"""
                            
                            # Insert before the general error handling
                            content = content[:error_log_pos] + timeout_handling + content[error_log_pos:]
                            print("Added specific timeout handling in process_query")
        
        # 3. Update the search logic to return faster results for text search
        if "# If vector search fails or returns no results, try text search" in content:
            # Already implemented text search, which is faster
            print("Text search already implemented")
            
        # 4. Save the updated file
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
            
        return True
    except Exception as e:
        print(f"ERROR updating server.py: {str(e)}")
        return False

def update_models_config():
    """Update models configuration to use faster models."""
    try:
        # Check if OpenAI models are configured
        if os.path.exists("llm_router.py"):
            with open("llm_router.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            # Find the DEFAULT_MODEL setting
            default_model_pos = content.find("DEFAULT_MODEL = ")
            
            if default_model_pos != -1:
                # Find the end of line
                eol_pos = content.find("\n", default_model_pos)
                
                if eol_pos != -1:
                    # Set default model to gemini-2.0-flash which should be faster
                    new_default = 'DEFAULT_MODEL = "gemini-2.0-flash"'
                    content = content[:default_model_pos] + new_default + content[eol_pos:]
                    
                    # Save the updated file
                    with open("llm_router.py", "w", encoding="utf-8") as f:
                        f.write(content)
                        
                    print("Set default model to gemini-2.0-flash")
        
        return True
    except Exception as e:
        print(f"ERROR updating models config: {str(e)}")
        return False

def main():
    print("\n=== FIXING TIMEOUT ISSUES ===\n")
    
    # 1. Update LLM router with better timeout handling
    print("Updating LLM router to handle timeouts better...")
    if update_llm_router():
        print("+ Successfully updated LLM router")
    else:
        print("- Failed to update LLM router")
    
    # 2. Optimize document search
    print("\nOptimizing document search...")
    if update_server_file():
        print("+ Successfully optimized document search")
    else:
        print("- Failed to optimize document search")
    
    # 3. Update default model config
    print("\nUpdating model configuration...")
    if update_models_config():
        print("+ Successfully updated model configuration")
    else:
        print("- Failed to update model configuration")
    
    print("\nAll optimizations complete! Please restart your server:")
    print("python -m uvicorn server:app --reload")

if __name__ == "__main__":
    main()
