import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import {
  ChatSession,
  ChatMessage,
  createChatSession,
  getChatSessionById,
  saveChatMessages,
  updateChatTitle
} from '../services/supabase';

interface ChatContextType {
  currentSession: ChatSession | null;
  messages: ChatMessage[];
  isLoading: boolean;

  // Actions
  createNewChat: () => Promise<void>;
  loadChatSession: (sessionId: string) => Promise<void>;
  addMessage: (message: ChatMessage) => void;
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  saveCurrentChat: () => Promise<void>;
  clearCurrentChat: () => void;

  // Auto-save functionality
  enableAutoSave: boolean;
  setEnableAutoSave: (enabled: boolean) => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChatContext = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
};

interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [enableAutoSave, setEnableAutoSave] = useState(true);

  const saveCurrentChat = useCallback(async (): Promise<void> => {
    if (!currentSession || messages.length === 0) return;

    try {
      await saveChatMessages(currentSession.id, messages);
      console.log('Chat saved successfully');
    } catch (error) {
      console.error('Error saving chat:', error);
    }
  }, [currentSession, messages]);

  // Auto-save messages when they change
  useEffect(() => {
    if (enableAutoSave && currentSession && messages.length > 0) {
      const timeoutId = setTimeout(() => {
        saveCurrentChat();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [messages, currentSession, enableAutoSave, saveCurrentChat]);

  const createNewChat = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const newSession = await createChatSession('New Chat', 'gemini-2.0-flash');
      if (newSession) {
        setCurrentSession(newSession);
        setMessages([]);
        console.log('New chat session created:', newSession.id);
        
        // Dispatch event to notify sidebar of new chat creation
        const chatCreatedEvent = new CustomEvent('chatCreated', {
          detail: newSession
        });
        window.dispatchEvent(chatCreatedEvent);
      }
    } catch (error) {
      console.error('Error creating new chat:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadChatSession = async (sessionId: string): Promise<void> => {
    if (currentSession?.id === sessionId) return; // Already loaded

    setIsLoading(true);
    try {
      const session = await getChatSessionById(sessionId);
      if (session) {
        setCurrentSession(session);
        setMessages(session.messages || []);
      }
    } catch (error) {
      console.error('Error loading chat session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addMessage = (message: ChatMessage): void => {
    setMessages(prev => {
      const newMessages = [...prev, message];
      
      // If this is the first user message and we're in a new chat, update the title
      if (message.sender === 'user' && currentSession && currentSession.title === 'New Chat' && prev.length === 0) {
        const newTitle = message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '');
        setCurrentSession(prevSession => prevSession ? { ...prevSession, title: newTitle } : null);

        // Also update the title in the database
        updateChatTitle(currentSession.id, newTitle).then(() => {
          // Dispatch event to update sidebar with new title
          const chatUpdatedEvent = new CustomEvent('chatUpdated', {
            detail: { ...currentSession, title: newTitle }
          });
          window.dispatchEvent(chatUpdatedEvent);
        });
      }
      
      return newMessages;
    });
  };

  const updateMessage = (messageId: string, updates: Partial<ChatMessage>): void => {
    setMessages(prev => {
      const messageIndex = prev.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        console.warn(`Message with id ${messageId} not found for update`);
        return prev;
      }
      return prev.map(msg => msg.id === messageId ? { ...msg, ...updates } : msg);
    });
  };



  const clearCurrentChat = (): void => {
    setCurrentSession(null);
    setMessages([]);
  };

  const contextValue: ChatContextType = {
    currentSession,
    messages,
    isLoading,
    createNewChat,
    loadChatSession,
    addMessage,
    updateMessage,
    saveCurrentChat,
    clearCurrentChat,
    enableAutoSave,
    setEnableAutoSave
  };

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

export default ChatContext;