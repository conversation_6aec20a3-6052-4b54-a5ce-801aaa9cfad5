"""
Test script for RailGPT search fixes.

This script:
1. Tests the new search functions
2. Tests document extraction and storage
3. Tests website extraction and storage
4. Tests the query endpoint with different scenarios

Usage:
    python test_search_fixes.py
"""

import os
import sys
import logging
import json
import requests
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from supabase_client import supabase
from vector_db import vector_db
from llm_router import generate_embedding

# API endpoint
API_ENDPOINT = "http://localhost:8000"

def test_search_functions():
    """Test the new search functions."""
    logger.info("Testing search functions...")
    
    # Test simple_search_document_chunks
    try:
        result = supabase.execute_query("""
        SELECT * FROM simple_search_document_chunks('railway', 5)
        """)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing simple_search_document_chunks: {result['error']}")
        else:
            logger.info(f"simple_search_document_chunks returned {len(result) if result else 0} results")
            
            # Print the first result
            if result and len(result) > 0:
                logger.info(f"First result: {result[0].get('text', '')[:100]}...")
    except Exception as e:
        logger.error(f"Error testing simple_search_document_chunks: {str(e)}")
    
    # Test get_all_document_chunks
    try:
        result = supabase.execute_query("""
        SELECT * FROM get_all_document_chunks(5)
        """)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing get_all_document_chunks: {result['error']}")
        else:
            logger.info(f"get_all_document_chunks returned {len(result) if result else 0} results")
            
            # Print the first result
            if result and len(result) > 0:
                logger.info(f"First result: {result[0].get('text', '')[:100]}...")
    except Exception as e:
        logger.error(f"Error testing get_all_document_chunks: {str(e)}")
    
    return True

def test_query_endpoint():
    """Test the query endpoint with different scenarios."""
    logger.info("Testing query endpoint...")
    
    # Test queries
    queries = [
        "What is FSDS Isolation Monitoring System?",  # Should find document chunks
        "What services does rapidresponseapp.com offer?",  # Should find website chunks
        "What is the capital of India?"  # Should use Gemini fallback
    ]
    
    for query in queries:
        try:
            # Send query to API
            response = requests.post(
                f"{API_ENDPOINT}/api/query",
                json={
                    "query": query,
                    "model": "gemini-2.0-flash",
                    "extract_format": "paragraph",
                    "use_hybrid_search": True,
                    "retry_on_timeout": True,
                    "fallback_enabled": True,
                    "context_mode": "flexible"
                }
            )
            
            if response.status_code != 200:
                logger.error(f"Error querying API: {response.text}")
                continue
            
            result = response.json()
            
            # Check if we got an answer
            if "answer" not in result or not result["answer"]:
                logger.error(f"No answer returned for query: {query}")
                continue
            
            logger.info(f"Query: {query}")
            logger.info(f"Answer: {result['answer'][:100]}...")
            
            # Check if we got sources
            sources = result.get("sources", [])
            logger.info(f"Found {len(sources)} sources")
            
            # Check if we used fallback
            llm_fallback = result.get("llm_fallback", False)
            logger.info(f"Used LLM fallback: {llm_fallback}")
            
        except Exception as e:
            logger.error(f"Error testing query endpoint: {str(e)}")
    
    return True

def test_document_search():
    """Test searching for documents directly."""
    logger.info("Testing document search...")
    
    # Generate embedding for a query
    query_text = "railway safety"
    query_embedding = generate_embedding(query_text)
    
    # Try direct search
    try:
        embedding_str = json.dumps(query_embedding)
        query = f"""
        SELECT * FROM direct_search_document_chunks(
            '{embedding_str}'::vector,
            0.0,
            5
        )
        """
        
        result = supabase.execute_query(query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error in direct search: {result['error']}")
        else:
            logger.info(f"Direct search returned {len(result) if result else 0} results")
            
            # Print the first result
            if result and len(result) > 0:
                logger.info(f"First result: {result[0].get('text', '')[:100]}...")
    except Exception as e:
        logger.error(f"Error in direct search: {str(e)}")
    
    # Try text search
    try:
        query = f"""
        SELECT * FROM simple_search_document_chunks(
            '{query_text}',
            5
        )
        """
        
        result = supabase.execute_query(query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error in text search: {result['error']}")
        else:
            logger.info(f"Text search returned {len(result) if result else 0} results")
            
            # Print the first result
            if result and len(result) > 0:
                logger.info(f"First result: {result[0].get('text', '')[:100]}...")
    except Exception as e:
        logger.error(f"Error in text search: {str(e)}")
    
    return True

def main():
    """Main function to run all tests."""
    logger.info("Starting tests...")
    
    # Test search functions
    if test_search_functions():
        logger.info("Search functions test passed")
    else:
        logger.error("Search functions test failed")
    
    # Test document search
    if test_document_search():
        logger.info("Document search test passed")
    else:
        logger.error("Document search test failed")
    
    # Test query endpoint
    if test_query_endpoint():
        logger.info("Query endpoint test passed")
    else:
        logger.error("Query endpoint test failed")
    
    logger.info("Tests completed")

if __name__ == "__main__":
    main()
