#!/usr/bin/env python3
"""
Test querying for website content
"""

import requests
import json

def test_query():
    """Test querying for content that should be in website chunks"""
    
    print("=== Testing Website Query ===")
    
    # Test query that should find content in the website chunks
    query = "Rapid Response App"
    
    print(f"Testing query: '{query}'")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json={
                "query": query,
                "model": "gemini-2.0-flash"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ Query successful!")
            print(f"Answer source: {result.get('answer_source', 'unknown')}")
            print(f"Document chunks: {result.get('document_chunks', 0)}")
            print(f"Website chunks: {result.get('website_chunks', 0)}")
            print(f"LLM fallback: {result.get('llm_fallback_used', False)}")
            
            # Check if website answer is present
            if result.get('website_answer'):
                print("\n✅ Website answer found!")
                print(f"Website answer: {result['website_answer']}")
            else:
                print("\n❌ No website answer found")
            
            # Check website sources
            website_sources = result.get('website_sources', [])
            if website_sources:
                print(f"\n✅ Website sources found: {len(website_sources)}")
                for i, source in enumerate(website_sources, 1):
                    print(f"  {i}. {source.get('url', 'unknown')}")
                    print(f"     Title: {source.get('title', 'N/A')}")
                    print(f"     Relevance: {source.get('relevance', 'N/A')}")
            else:
                print("\n❌ No website sources found")
                
            # Check document answer too
            if result.get('document_answer'):
                print(f"\n📄 Document answer also found")
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_query()
