#!/usr/bin/env python3
"""
Test script to add a website and verify chunks are stored properly
"""

import requests
import json
import time
from supabase_client import supabase

def test_website_addition():
    """Test adding a website and verify chunks are stored"""

    # Test URL - using a simple website
    test_url = "https://example.com"

    print("Testing website addition functionality...")
    print(f"Server should be running on http://localhost:8000")

    print(f"Testing website addition for: {test_url}")

    # Check initial state
    print("\n=== Initial State ===")
    websites_count = supabase.execute_query("SELECT COUNT(*) as count FROM websites")
    chunks_count = supabase.execute_query("SELECT COUNT(*) as count FROM website_chunks")
    print(f"Websites in DB: {websites_count[0]['count'] if websites_count else 0}")
    print(f"Website chunks in DB: {chunks_count[0]['count'] if chunks_count else 0}")

    # Add website via API
    print(f"\n=== Adding Website: {test_url} ===")
    try:
        response = requests.post(
            "http://localhost:8000/api/add-website",
            json={"url": test_url},
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            print(f"✅ Website added successfully!")
            print(f"Response: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Failed to add website. Status: {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error adding website: {str(e)}")
        return False

    # Wait a moment for processing
    time.sleep(2)

    # Check final state
    print("\n=== Final State ===")
    websites_count = supabase.execute_query("SELECT COUNT(*) as count FROM websites")
    chunks_count = supabase.execute_query("SELECT COUNT(*) as count FROM website_chunks")
    print(f"Websites in DB: {websites_count[0]['count'] if websites_count else 0}")
    print(f"Website chunks in DB: {chunks_count[0]['count'] if chunks_count else 0}")

    # Check specific website chunks
    if chunks_count and chunks_count[0]['count'] > 0:
        print("\n=== Sample Website Chunks ===")
        sample_chunks = supabase.execute_query("""
            SELECT wc.id, wc.website_id, wc.chunk_index,
                   LEFT(wc.text, 100) as text_sample,
                   w.url, w.domain
            FROM website_chunks wc
            JOIN websites w ON wc.website_id = w.id
            ORDER BY wc.created_at DESC
            LIMIT 3
        """)

        for chunk in sample_chunks:
            print(f"Chunk {chunk['chunk_index']}: {chunk['text_sample']}...")

    return True

if __name__ == "__main__":
    test_website_addition()
