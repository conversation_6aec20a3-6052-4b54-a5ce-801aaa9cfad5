'''
Cleanup and normalize document embeddings in Supabase.
This script converts all string embeddings to proper numeric arrays.
'''
import os
import sys
import json
import logging
import numpy as np
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def cleanup_embeddings():
    '''Clean up and normalize embeddings in document_chunks table.'''
    try:
        # Get all document chunks
        chunks_query = "SELECT id, embedding FROM document_chunks"
        chunks = supabase.execute_query(chunks_query)
        
        if isinstance(chunks, dict) and "error" in chunks:
            logger.error(f"Error getting document chunks: {chunks['error']}")
            return False
        
        logger.info(f"Found {len(chunks)} document chunks to check")
        fixed_count = 0
        
        for chunk in chunks:
            chunk_id = chunk.get("id")
            embedding = chunk.get("embedding")
            
            # Skip if no embedding
            if not embedding:
                continue
            
            needs_fixing = False
            
            # Check if embedding is a string and needs conversion
            if isinstance(embedding, str):
                try:
                    # Try to parse as JSON
                    parsed = json.loads(embedding)
                    if isinstance(parsed, list) and len(parsed) > 0:
                        # Valid JSON array, update embedding
                        embedding = parsed
                        needs_fixing = True
                except:
                    # Not valid JSON, create mock embedding
                    embedding = [0.01] * 768
                    needs_fixing = True
            
            # Fix if needed
            if needs_fixing:
                # Update the chunk with fixed embedding
                update_query = f"UPDATE document_chunks SET embedding = '{json.dumps(embedding)}' WHERE id = '{chunk_id}'"
                result = supabase.execute_query(update_query)
                
                if isinstance(result, dict) and "error" in result:
                    logger.error(f"Error updating chunk {chunk_id}: {result['error']}")
                else:
                    fixed_count += 1
                    logger.info(f"Fixed embedding for chunk {chunk_id}")
        
        logger.info(f"Fixed {fixed_count} document chunk embeddings")
        return fixed_count > 0
    except Exception as e:
        logger.error(f"Error cleaning up embeddings: {str(e)}")
        return False

if __name__ == "__main__":
    print("Cleaning up document embeddings...")
    if cleanup_embeddings():
        print("Successfully fixed document embeddings")
    else:
        print("Failed to fix document embeddings")
