#!/bin/bash

# RailGPT GCP Environment Setup Script
# This script helps you configure your environment for GCP deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
        while [ -z "$input" ]; do
            print_warning "This field is required!"
            read -p "$prompt: " input
        done
    fi
    
    eval "$var_name='$input'"
}

# Function to validate API key format
validate_api_key() {
    local key="$1"
    local key_type="$2"
    
    case $key_type in
        "gemini")
            if [[ $key =~ ^AI[a-zA-Z0-9_-]{35,}$ ]]; then
                return 0
            fi
            ;;
        "openai")
            if [[ $key =~ ^sk-[a-zA-Z0-9]{48,}$ ]]; then
                return 0
            fi
            ;;
        "groq")
            if [[ $key =~ ^gsk_[a-zA-Z0-9]{52}$ ]]; then
                return 0
            fi
            ;;
    esac
    return 1
}

# Function to test API key
test_api_key() {
    local key="$1"
    local key_type="$2"
    
    print_status "Testing $key_type API key..."
    
    case $key_type in
        "gemini")
            # Test Gemini API key
            response=$(curl -s -w "%{http_code}" -o /dev/null \
                "https://generativelanguage.googleapis.com/v1/models?key=$key")
            if [ "$response" = "200" ]; then
                print_success "Gemini API key is valid"
                return 0
            fi
            ;;
        "openai")
            # Test OpenAI API key
            response=$(curl -s -w "%{http_code}" -o /dev/null \
                -H "Authorization: Bearer $key" \
                "https://api.openai.com/v1/models")
            if [ "$response" = "200" ]; then
                print_success "OpenAI API key is valid"
                return 0
            fi
            ;;
        "groq")
            # Test Groq API key
            response=$(curl -s -w "%{http_code}" -o /dev/null \
                -H "Authorization: Bearer $key" \
                "https://api.groq.com/openai/v1/models")
            if [ "$response" = "200" ]; then
                print_success "Groq API key is valid"
                return 0
            fi
            ;;
    esac
    
    print_warning "$key_type API key test failed (key might still work)"
    return 1
}

# Main setup function
setup_environment() {
    echo "========================================="
    echo "    RailGPT GCP Environment Setup       "
    echo "========================================="
    echo
    
    print_status "This script will help you configure your environment for GCP deployment."
    echo
    
    # Check if .env already exists
    if [ -f ".env" ]; then
        print_warning ".env file already exists."
        read -p "Do you want to overwrite it? (y/N): " overwrite
        if [[ ! $overwrite =~ ^[Yy]$ ]]; then
            print_status "Keeping existing .env file. Exiting."
            exit 0
        fi
    fi
    
    # GCP Configuration
    print_status "=== Google Cloud Platform Configuration ==="
    
    # Generate default project ID
    default_project="railgpt-production-$(date +%s)"
    prompt_with_default "Enter your GCP Project ID" "$default_project" "PROJECT_ID"
    
    # Region selection
    echo
    print_status "Available regions:"
    echo "  us-central1 (Iowa) - Recommended for US"
    echo "  us-east1 (South Carolina)"
    echo "  europe-west1 (Belgium) - Recommended for Europe"
    echo "  asia-southeast1 (Singapore) - Recommended for Asia"
    
    prompt_with_default "Enter your preferred region" "us-central1" "REGION"
    
    # Domain configuration
    echo
    prompt_with_default "Enter your domain name (optional, press Enter to skip)" "" "DOMAIN"
    
    # Supabase Configuration
    echo
    print_status "=== Supabase Configuration ==="
    print_status "Using pre-configured Supabase instance for RailGPT"
    
    SUPABASE_URL="https://rkllidjktazafeinezgo.supabase.co"
    SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA"
    
    prompt_with_default "Enter your Supabase Service Key" "" "SUPABASE_KEY"
    
    # API Keys Configuration
    echo
    print_status "=== AI Model API Keys ==="
    print_status "At least one API key is required. Gemini is recommended (free tier available)."
    echo
    
    # Gemini API Key (Required)
    print_status "Gemini API Key (Required):"
    print_status "Get your key from: https://makersuite.google.com/app/apikey"
    prompt_with_default "Enter your Gemini API key" "" "GEMINI_API_KEY"
    
    # Validate Gemini key
    if ! validate_api_key "$GEMINI_API_KEY" "gemini"; then
        print_warning "Gemini API key format seems incorrect, but continuing..."
    else
        test_api_key "$GEMINI_API_KEY" "gemini"
    fi
    
    # OpenAI API Key (Optional)
    echo
    print_status "OpenAI API Key (Optional):"
    print_status "Get your key from: https://platform.openai.com/api-keys"
    prompt_with_default "Enter your OpenAI API key (press Enter to skip)" "" "OPENAI_API_KEY"
    
    if [ -n "$OPENAI_API_KEY" ]; then
        if ! validate_api_key "$OPENAI_API_KEY" "openai"; then
            print_warning "OpenAI API key format seems incorrect, but continuing..."
        else
            test_api_key "$OPENAI_API_KEY" "openai"
        fi
    fi
    
    # Groq API Key (Optional)
    echo
    print_status "Groq API Key (Optional):"
    print_status "Get your key from: https://console.groq.com/keys"
    prompt_with_default "Enter your Groq API key (press Enter to skip)" "" "GROQ_API_KEY"
    
    if [ -n "$GROQ_API_KEY" ]; then
        if ! validate_api_key "$GROQ_API_KEY" "groq"; then
            print_warning "Groq API key format seems incorrect, but continuing..."
        else
            test_api_key "$GROQ_API_KEY" "groq"
        fi
    fi
    
    # Create .env file
    echo
    print_status "Creating .env file..."
    
    cat > .env << EOF
# RailGPT Environment Configuration
# Generated on $(date)

# ==============================================
# GOOGLE CLOUD PLATFORM CONFIGURATION
# ==============================================
PROJECT_ID=$PROJECT_ID
REGION=$REGION
DOMAIN=$DOMAIN

# ==============================================
# SUPABASE CONFIGURATION
# ==============================================
SUPABASE_URL=$SUPABASE_URL
SUPABASE_KEY=$SUPABASE_KEY
SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY

# ==============================================
# AI MODEL API KEYS
# ==============================================
GEMINI_API_KEY=$GEMINI_API_KEY
OPENAI_API_KEY=$OPENAI_API_KEY
GROQ_API_KEY=$GROQ_API_KEY

# ==============================================
# APPLICATION SETTINGS
# ==============================================
ENVIRONMENT=production
API_HOST=0.0.0.0
API_PORT=8000
LOG_LEVEL=INFO

# ==============================================
# FRONTEND CONFIGURATION
# ==============================================
REACT_APP_SUPABASE_URL=$SUPABASE_URL
REACT_APP_SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY

# ==============================================
# OPTIONAL SETTINGS
# ==============================================
USE_SUPABASE=true
VECTOR_DIMENSION=768
DOCUMENT_PRIORITY_WEIGHT=1.5
WEBSITE_PRIORITY_WEIGHT=1.2
RELEVANCE_THRESHOLD=0.2
DEFAULT_USER_ID=a7fbeebf-9025-4a39-aefb-e128ccb6060f
EOF
    
    print_success ".env file created successfully!"
    
    # Display summary
    echo
    print_status "=== Configuration Summary ==="
    echo "  Project ID: $PROJECT_ID"
    echo "  Region: $REGION"
    echo "  Domain: ${DOMAIN:-"Not specified"}"
    echo "  Supabase URL: $SUPABASE_URL"
    echo "  Gemini API: ${GEMINI_API_KEY:+Configured}"
    echo "  OpenAI API: ${OPENAI_API_KEY:+Configured}"
    echo "  Groq API: ${GROQ_API_KEY:+Configured}"
    
    # Next steps
    echo
    print_status "=== Next Steps ==="
    echo "1. Review the .env file if needed: nano .env"
    echo "2. Deploy to GCP: ./deploy/gcp/setup-complete.sh"
    echo "3. Or deploy components separately:"
    echo "   - Backend: ./deploy/gcp/deploy-backend.sh"
    echo "   - Frontend: ./deploy/gcp/deploy-frontend.sh"
    
    if [ -n "$DOMAIN" ]; then
        echo
        print_warning "=== Domain Configuration Required ==="
        print_warning "After deployment, you'll need to:"
        print_warning "1. Point your domain to the load balancer IP"
        print_warning "2. Wait for SSL certificate provisioning (up to 24 hours)"
    fi
    
    echo
    print_success "Environment setup completed! You're ready to deploy."
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v curl &> /dev/null; then
        print_error "curl is required but not installed."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Main execution
main() {
    # Change to script directory
    cd "$(dirname "$0")/../.."
    
    check_prerequisites
    setup_environment
}

# Run main function
main "$@"
