# Fix for RailGPT 'similarity' and 'text' Errors

This document provides instructions for fixing the 'similarity' and 'text' errors in the RailGPT application.

## Issues Identified

Based on the logs and code analysis, the following issues were identified:

1. **'similarity' Error**: The error `Error searching vector database: 'similarity'` occurs because some chunks returned from the Supabase hybrid search functions don't have a 'similarity' field or it's not properly formatted.

2. **'text' Error**: The error `Error generating LLM answer with gemini-2.0-flash: 'text'` occurs because some chunks don't have a 'text' field or it's not properly formatted.

3. **Hybrid Search Functions**: The hybrid search functions in Supabase are using `ts_rank` which might be causing issues if the text search functionality isn't properly set up.

## Fix Instructions

### 1. Apply Server.py Patches

The server.py file has been patched to add error handling for the 'similarity' and 'text' errors. The patches:

1. Add default 'similarity' values if missing
2. Add default 'text' values if missing or not a string
3. Skip chunks with invalid text when generating LLM answers

These changes have already been applied to the server.py file.

### 2. Fix Hybrid Search Functions in Supabase

Run the following script to fix the hybrid search functions in Supabase:

```powershell
cd backend
python fix_hybrid_search_sql.py
```

This script:
1. Drops the existing hybrid search functions
2. Creates simplified versions that don't use `ts_rank`
3. Tests the functions to make sure they work

### 3. Restart the Backend Server

After applying the fixes, restart the backend server:

```powershell
cd backend
uvicorn server:app --reload
```

## Manual SQL Execution

If the script doesn't work, you can manually execute the SQL in the Supabase SQL Editor:

1. Go to the Supabase dashboard for the "RailGPT" project.
2. Go to the SQL Editor.
3. Copy and paste the following SQL:

```sql
-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS hybrid_search_document_chunks(text, vector, float, int);
DROP FUNCTION IF EXISTS hybrid_search_website_chunks(text, vector, float, int);

-- Create function for hybrid document search (using only vector similarity)
CREATE OR REPLACE FUNCTION hybrid_search_document_chunks(
    query_text text,
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        dc.metadata,
        d.file_path as url,
        d.main_category as domain,
        d.display_name as title,
        -- Use only vector similarity for now
        (1 - (dc.embedding <=> query_embedding)) as similarity,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE 
        -- Vector similarity threshold
        (1 - (dc.embedding <=> query_embedding) > match_threshold)
        -- Text search condition (optional but helps with relevance)
        OR (dc.text ILIKE '%' || query_text || '%')
    ORDER BY (1 - (dc.embedding <=> query_embedding)) DESC
    LIMIT match_count;
END;
$$;

-- Create function for hybrid website search (using only vector similarity)
CREATE OR REPLACE FUNCTION hybrid_search_website_chunks(
    query_text text,
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id uuid,
    website_id uuid,
    chunk_index int,
    text text,
    metadata jsonb,
    url text,
    domain text,
    title text,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id,
        wc.website_id,
        wc.chunk_index,
        wc.text,
        wc.metadata,
        w.url,
        w.domain,
        w.title,
        -- Use only vector similarity for now
        (1 - (wc.embedding <=> query_embedding)) as similarity,
        'website'::TEXT as source_type
    FROM website_chunks wc
    JOIN websites w ON wc.website_id = w.id
    WHERE 
        -- Vector similarity threshold
        (1 - (wc.embedding <=> query_embedding) > match_threshold)
        -- Text search condition (optional but helps with relevance)
        OR (wc.text ILIKE '%' || query_text || '%')
    ORDER BY (1 - (wc.embedding <=> query_embedding)) DESC
    LIMIT match_count;
END;
$$;
```

## Verification

To verify that the fixes worked:

1. Check the logs for any errors related to 'similarity' or 'text'.
2. Try searching for "rapid response app" in the RailGPT interface.
3. Check if you get a proper answer instead of an error message.

## Troubleshooting

If you still encounter issues:

1. Check the logs for specific error messages.
2. Verify that the Supabase URL and API key are correct.
3. Check if the required tables exist in the Supabase database.
4. Try running the test scripts to verify the functionality:

```powershell
cd backend
python test_vector_db.py
```
