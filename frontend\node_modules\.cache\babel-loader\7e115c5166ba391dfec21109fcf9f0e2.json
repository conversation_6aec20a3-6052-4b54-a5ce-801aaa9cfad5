{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\pages\\\\documents\\\\DocumentsPage.tsx\";\nimport React, { useState, useEffect } from 'react';\nimport DocumentUploadForm from '../../components/documents/DocumentUploadForm';\nimport DocumentsTable from '../../components/documents/DocumentsTable';\nimport DocumentViewModal from '../../components/documents/DocumentViewModal';\nimport CategoryManagement from '../../components/documents/CategoryManagement';\nimport { getDocuments } from '../../services/api';\nimport { Settings } from 'lucide-react';\n\n// Sample data for demonstration\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SAMPLE_DOCUMENTS = [{\n  id: '1',\n  name: 'Railway Safety Guidelines 2025',\n  uploadedAt: '2025-05-01T10:30:00Z',\n  mainCategory: 'Safety',\n  category: 'Guidelines',\n  subCategory: 'General',\n  uploadedBy: '<EMAIL>',\n  qualityScore: 92,\n  status: 'Extracted',\n  filePath: '/documents/railway-safety-guidelines-2025.pdf',\n  fileType: 'pdf',\n  fileSize: 2457600\n}, {\n  id: '2',\n  name: 'Engine Maintenance Manual',\n  uploadedAt: '2025-04-28T14:15:00Z',\n  mainCategory: 'Technical',\n  category: 'Manuals',\n  subCategory: 'Diesel Loco',\n  uploadedBy: '<EMAIL>',\n  qualityScore: 87,\n  status: 'Extracted',\n  filePath: '/documents/engine-maintenance-manual.pdf',\n  fileType: 'pdf',\n  fileSize: 15728640\n}, {\n  id: '3',\n  name: 'Schedule of Train Operations - Q2 2025',\n  uploadedAt: '2025-04-15T09:45:00Z',\n  mainCategory: 'Operations',\n  category: 'Schedules',\n  uploadedBy: '<EMAIL>',\n  qualityScore: 75,\n  status: 'Extracted',\n  filePath: '/documents/train-schedule-q2-2025.xlsx',\n  fileType: 'xlsx',\n  fileSize: 1048576\n}, {\n  id: '4',\n  name: 'Station Master Handbook',\n  uploadedAt: '2025-03-20T11:00:00Z',\n  mainCategory: 'Administrative',\n  category: 'Handbooks',\n  subCategory: 'Station Operations',\n  minorCategory: 'Management',\n  uploadedBy: '<EMAIL>',\n  qualityScore: 95,\n  status: 'Extracted',\n  filePath: '/documents/station-master-handbook.docx',\n  fileType: 'docx',\n  fileSize: 5242880\n}, {\n  id: '5',\n  name: 'Railway Board Meeting Minutes - April 2025',\n  uploadedAt: '2025-05-02T16:20:00Z',\n  mainCategory: 'Administrative',\n  category: 'Meeting Minutes',\n  uploadedBy: '<EMAIL>',\n  status: 'Pending',\n  filePath: '/documents/railway-board-minutes-apr-2025.pdf',\n  fileType: 'pdf',\n  fileSize: 524288\n}];\n\n// Sample extraction details for demonstration\nconst SAMPLE_EXTRACTION_DETAILS = {\n  extractionMethod: 'PyMuPDF',\n  qualityScore: 92,\n  warnings: ['Some tables may not be properly extracted'],\n  extractedContent: `\n# Railway Safety Guidelines 2025\n\n## 1. Introduction\n\nThis document outlines the comprehensive safety guidelines for all railway operations in 2025. These guidelines are mandated by the Railway Safety Commission and must be followed by all railway personnel.\n\n## 2. General Safety Protocols\n\n### 2.1 Personal Protective Equipment (PPE)\n\nAll railway staff working on or near the tracks must wear:\n- High-visibility vest or clothing\n- Safety helmet\n- Safety boots with ankle support\n- Hearing protection when working near operating machinery\n\n### 2.2 Communication Protocols\n\nClear communication is essential for railway safety:\n- Use standard radio communication protocols\n- Confirm all instructions with a repeat-back\n- Use established hand signals when radio communication is not possible\n- Report any communication equipment failures immediately\n\n## 3. Track Maintenance Safety\n\n### 3.1 Track Inspection\n\nRegular track inspections must be conducted:\n- Visual inspections daily\n- Ultrasonic testing monthly\n- Comprehensive structural assessment quarterly\n\n### 3.2 Work Zone Safety\n\nFor maintenance work on active tracks:\n- Establish clear work zone boundaries\n- Assign a dedicated lookout person\n- Use track circuit operating devices where available\n- Implement temporary speed restrictions on adjacent tracks\n\n## 4. Train Operation Safety\n\n### 4.1 Pre-departure Checks\n\nBefore any train departure, complete the following safety checks:\n- Brake system functionality\n- Signal system responsiveness\n- Communication equipment testing\n- Door operation verification\n\n### 4.2 Speed Restrictions\n\nAdhere to all speed restrictions, especially in:\n- Curves and bends\n- Bridges and tunnels\n- Areas with ongoing maintenance\n- Bad weather conditions\n\n## 5. Emergency Procedures\n\n### 5.1 Accident Response\n\nIn case of an accident:\n- Immediately secure the site\n- Notify central control\n- Provide first aid as necessary\n- Document all relevant details\n\n### 5.2 Evacuation Protocols\n\nStandard evacuation procedures for railway emergencies:\n- Identify safe exit routes\n- Guide passengers to designated assembly points\n- Account for all passengers and staff\n- Provide regular updates to emergency services\n\n## 6. Compliance and Reporting\n\nAll safety incidents, near misses, and potential hazards must be reported through the official Railway Safety Management System within 24 hours of occurrence.\n\nRegular safety audits will be conducted to ensure compliance with these guidelines.\n  `,\n  processingTime: 3450,\n  chunks: 12\n};\nconst DocumentsPage = () => {\n  const [documents, setDocuments] = useState(SAMPLE_DOCUMENTS);\n  const [selectedDocument, setSelectedDocument] = useState(null);\n  const [isViewModalOpen, setIsViewModalOpen] = useState(false);\n  const [extractionDetails, setExtractionDetails] = useState(SAMPLE_EXTRACTION_DETAILS);\n  const [isCategoryManagementOpen, setIsCategoryManagementOpen] = useState(false);\n  const [isCategoryCreatorOpen, setIsCategoryCreatorOpen] = useState(false);\n\n  // Fetch documents from the backend\n  useEffect(() => {\n    fetchDocuments();\n\n    // Set up document upload event listener\n    const handleDocumentUploaded = event => {\n      const newDocument = event.detail;\n      setDocuments(prev => [newDocument, ...prev]);\n      // Refresh the documents list to get the latest from Supabase\n      setTimeout(() => fetchDocuments(), 1000); // Small delay to allow backend processing\n    };\n\n    // Add event listener\n    window.addEventListener('documentUploaded', handleDocumentUploaded);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('documentUploaded', handleDocumentUploaded);\n    };\n  }, []);\n  const fetchDocuments = async () => {\n    try {\n      const data = await getDocuments();\n      if (data && data.length > 0) {\n        // Use real data from Supabase, don't mix with sample data\n        setDocuments(data);\n        console.log(`Loaded ${data.length} documents from Supabase`);\n      } else {\n        console.log('No documents found from API, using sample data');\n        // Only use sample data if no real data is available\n        setDocuments(SAMPLE_DOCUMENTS);\n      }\n    } catch (error) {\n      console.error('Error fetching documents:', error);\n      // Keep using sample data if API call fails\n      setDocuments(SAMPLE_DOCUMENTS);\n    }\n  };\n  const handleViewDocument = document => {\n    setSelectedDocument(document);\n\n    // Fetch actual extraction details from the backend\n    async function fetchExtractionDetails() {\n      try {\n        // Import our API functions\n        const {\n          getDocumentExtractionDetails,\n          getDocumentContent\n        } = await import('../../services/api');\n\n        // First try the specific extraction details endpoint\n        try {\n          const data = await getDocumentExtractionDetails(document.id);\n          console.log('Extraction details response:', data);\n          setExtractionDetails(data);\n          return;\n        } catch (extractionError) {\n          console.warn('Failed to get extraction details, trying content endpoint:', extractionError);\n        }\n\n        // If that fails, try the general document content endpoint\n        try {\n          const contentData = await getDocumentContent(document.id);\n          console.log('Document content response:', contentData);\n\n          // Create extraction details from content data\n          const details = {\n            extractedContent: contentData.content || 'No content available',\n            extractionMethod: contentData.extraction_method || 'Unknown',\n            qualityScore: contentData.quality_score || 75,\n            processingTime: contentData.processing_time || 1250,\n            chunks: contentData.chunks_count || 5,\n            warnings: [],\n            fallbackReason: ''\n          };\n          setExtractionDetails(details);\n          return;\n        } catch (contentError) {\n          console.error('Failed to get document content:', contentError);\n          throw contentError; // Re-throw to be caught by the outer try-catch\n        }\n      } catch (error) {\n        console.error('Error fetching extraction details:', error);\n\n        // Fallback data in case of error\n        const errorFallbackDetails = {\n          extractedContent: `Unable to retrieve content for ${document.name} due to an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again later.`,\n          extractionMethod: 'Error Fallback',\n          qualityScore: 30,\n          processingTime: 0,\n          chunks: 0,\n          warnings: ['Error retrieving content', error instanceof Error ? error.message : 'Unknown error'],\n          fallbackReason: 'Error occurred while fetching content'\n        };\n        setExtractionDetails(errorFallbackDetails);\n      }\n    }\n    fetchExtractionDetails();\n    setIsViewModalOpen(true);\n  };\n  const handleEditDocument = document => {\n    // In a real app, you would implement an edit modal or navigate to an edit page\n    alert(`Edit document: ${document.name}`);\n  };\n  const handleDeleteDocument = document => {\n    if (window.confirm(`Are you sure you want to delete \"${document.name}\"?`)) {\n      // In a real app, you would make an API call to delete the document\n      // async function deleteDocument() {\n      //   try {\n      //     await fetch(`/api/documents/${document.id}`, { method: 'DELETE' });\n      //     setDocuments(documents.filter(doc => doc.id !== document.id));\n      //   } catch (error) {\n      //     console.error('Error deleting document:', error);\n      //   }\n      // }\n      // deleteDocument();\n\n      // For the demo, just filter it out\n      setDocuments(documents.filter(doc => doc.id !== document.id));\n    }\n  };\n  const handleReprocessDocument = document => {\n    // In a real app, you would implement a reprocessing logic\n    alert(`Reprocess document: ${document.name}`);\n  };\n  const handleReprocessWithTool = async (document, tool) => {\n    // In a real app, you would make an API call to reprocess with the selected tool\n    alert(`Reprocessing ${document.name} with ${tool}`);\n\n    // Simulate processing time\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    return true;\n  };\n  const handleCategoryUpdate = updatedDocument => {\n    // Update the document in the local state\n    setDocuments(prevDocuments => prevDocuments.map(doc => doc.id === updatedDocument.id ? updatedDocument : doc));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full flex flex-col bg-gray-50 transition-colors duration-300\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 shadow-sm z-10 transition-colors duration-300\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Document Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsCategoryManagementOpen(true),\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\",\n            children: [/*#__PURE__*/_jsxDEV(Settings, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), \"Manage Categories\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:col-span-1\",\n            children: /*#__PURE__*/_jsxDEV(DocumentUploadForm, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:col-span-2\",\n            children: /*#__PURE__*/_jsxDEV(DocumentsTable, {\n              documents: documents,\n              onView: handleViewDocument,\n              onEdit: handleEditDocument,\n              onDelete: handleDeleteDocument,\n              onReprocess: handleReprocessDocument,\n              onCategoryUpdate: handleCategoryUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), selectedDocument && /*#__PURE__*/_jsxDEV(DocumentViewModal, {\n      document: selectedDocument,\n      extractionDetails: extractionDetails,\n      isOpen: isViewModalOpen,\n      onClose: () => setIsViewModalOpen(false),\n      onReprocess: handleReprocessWithTool\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CategoryManagement, {\n      isOpen: isCategoryManagementOpen,\n      onClose: () => setIsCategoryManagementOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n};\nexport default DocumentsPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "DocumentUploadForm", "DocumentsTable", "DocumentViewModal", "CategoryManagement", "getDocuments", "Settings", "jsxDEV", "_jsxDEV", "SAMPLE_DOCUMENTS", "id", "name", "uploadedAt", "mainCategory", "category", "subCategory", "uploadedBy", "qualityScore", "status", "filePath", "fileType", "fileSize", "minorCategory", "SAMPLE_EXTRACTION_DETAILS", "extractionMethod", "warnings", "extractedContent", "processingTime", "chunks", "DocumentsPage", "documents", "setDocuments", "selectedDocument", "setSelectedDocument", "isViewModalOpen", "setIsViewModalOpen", "extractionDetails", "setExtractionDetails", "isCategoryManagementOpen", "setIsCategoryManagementOpen", "isCategoryCreatorOpen", "setIsCategoryCreatorOpen", "fetchDocuments", "handleDocumentUploaded", "event", "newDocument", "detail", "prev", "setTimeout", "window", "addEventListener", "removeEventListener", "data", "length", "console", "log", "error", "handleViewDocument", "document", "fetchExtractionDetails", "getDocumentExtractionDetails", "getDocumentContent", "extractionError", "warn", "contentData", "details", "content", "extraction_method", "quality_score", "processing_time", "chunks_count", "fallbackReason", "contentError", "errorFallbackDetails", "Error", "message", "handleEditDocument", "alert", "handleDeleteDocument", "confirm", "filter", "doc", "handleReprocessDocument", "handleReprocessWithTool", "tool", "Promise", "resolve", "handleCategoryUpdate", "updatedDocument", "prevDocuments", "map", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onView", "onEdit", "onDelete", "onReprocess", "onCategoryUpdate", "isOpen", "onClose"], "sources": ["C:/IR App/frontend/src/pages/documents/DocumentsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Document, DocumentExtractionDetails, ExtractionTool } from '../../types/documents';\nimport DocumentUploadForm from '../../components/documents/DocumentUploadForm';\nimport DocumentsTable from '../../components/documents/DocumentsTable';\nimport DocumentViewModal from '../../components/documents/DocumentViewModal';\nimport CategoryManagement from '../../components/documents/CategoryManagement';\nimport CategoryCreator from '../../components/categories/CategoryCreator';\nimport { getDocuments } from '../../services/api';\nimport { Settings, FolderPlus } from 'lucide-react';\n\n// Sample data for demonstration\nconst SAMPLE_DOCUMENTS: Document[] = [\n  {\n    id: '1',\n    name: 'Railway Safety Guidelines 2025',\n    uploadedAt: '2025-05-01T10:30:00Z',\n    mainCategory: 'Safety',\n    category: 'Guidelines',\n    subCategory: 'General',\n    uploadedBy: '<EMAIL>',\n    qualityScore: 92,\n    status: 'Extracted',\n    filePath: '/documents/railway-safety-guidelines-2025.pdf',\n    fileType: 'pdf',\n    fileSize: 2457600,\n  },\n  {\n    id: '2',\n    name: 'Engine Maintenance Manual',\n    uploadedAt: '2025-04-28T14:15:00Z',\n    mainCategory: 'Technical',\n    category: 'Manuals',\n    subCategory: 'Diesel Loco',\n    uploadedBy: '<EMAIL>',\n    qualityScore: 87,\n    status: 'Extracted',\n    filePath: '/documents/engine-maintenance-manual.pdf',\n    fileType: 'pdf',\n    fileSize: 15728640,\n  },\n  {\n    id: '3',\n    name: 'Schedule of Train Operations - Q2 2025',\n    uploadedAt: '2025-04-15T09:45:00Z',\n    mainCategory: 'Operations',\n    category: 'Schedules',\n    uploadedBy: '<EMAIL>',\n    qualityScore: 75,\n    status: 'Extracted',\n    filePath: '/documents/train-schedule-q2-2025.xlsx',\n    fileType: 'xlsx',\n    fileSize: 1048576,\n  },\n  {\n    id: '4',\n    name: 'Station Master Handbook',\n    uploadedAt: '2025-03-20T11:00:00Z',\n    mainCategory: 'Administrative',\n    category: 'Handbooks',\n    subCategory: 'Station Operations',\n    minorCategory: 'Management',\n    uploadedBy: '<EMAIL>',\n    qualityScore: 95,\n    status: 'Extracted',\n    filePath: '/documents/station-master-handbook.docx',\n    fileType: 'docx',\n    fileSize: 5242880,\n  },\n  {\n    id: '5',\n    name: 'Railway Board Meeting Minutes - April 2025',\n    uploadedAt: '2025-05-02T16:20:00Z',\n    mainCategory: 'Administrative',\n    category: 'Meeting Minutes',\n    uploadedBy: '<EMAIL>',\n    status: 'Pending',\n    filePath: '/documents/railway-board-minutes-apr-2025.pdf',\n    fileType: 'pdf',\n    fileSize: 524288,\n  },\n];\n\n// Sample extraction details for demonstration\nconst SAMPLE_EXTRACTION_DETAILS: DocumentExtractionDetails = {\n  extractionMethod: 'PyMuPDF',\n  qualityScore: 92,\n  warnings: ['Some tables may not be properly extracted'],\n  extractedContent: `\n# Railway Safety Guidelines 2025\n\n## 1. Introduction\n\nThis document outlines the comprehensive safety guidelines for all railway operations in 2025. These guidelines are mandated by the Railway Safety Commission and must be followed by all railway personnel.\n\n## 2. General Safety Protocols\n\n### 2.1 Personal Protective Equipment (PPE)\n\nAll railway staff working on or near the tracks must wear:\n- High-visibility vest or clothing\n- Safety helmet\n- Safety boots with ankle support\n- Hearing protection when working near operating machinery\n\n### 2.2 Communication Protocols\n\nClear communication is essential for railway safety:\n- Use standard radio communication protocols\n- Confirm all instructions with a repeat-back\n- Use established hand signals when radio communication is not possible\n- Report any communication equipment failures immediately\n\n## 3. Track Maintenance Safety\n\n### 3.1 Track Inspection\n\nRegular track inspections must be conducted:\n- Visual inspections daily\n- Ultrasonic testing monthly\n- Comprehensive structural assessment quarterly\n\n### 3.2 Work Zone Safety\n\nFor maintenance work on active tracks:\n- Establish clear work zone boundaries\n- Assign a dedicated lookout person\n- Use track circuit operating devices where available\n- Implement temporary speed restrictions on adjacent tracks\n\n## 4. Train Operation Safety\n\n### 4.1 Pre-departure Checks\n\nBefore any train departure, complete the following safety checks:\n- Brake system functionality\n- Signal system responsiveness\n- Communication equipment testing\n- Door operation verification\n\n### 4.2 Speed Restrictions\n\nAdhere to all speed restrictions, especially in:\n- Curves and bends\n- Bridges and tunnels\n- Areas with ongoing maintenance\n- Bad weather conditions\n\n## 5. Emergency Procedures\n\n### 5.1 Accident Response\n\nIn case of an accident:\n- Immediately secure the site\n- Notify central control\n- Provide first aid as necessary\n- Document all relevant details\n\n### 5.2 Evacuation Protocols\n\nStandard evacuation procedures for railway emergencies:\n- Identify safe exit routes\n- Guide passengers to designated assembly points\n- Account for all passengers and staff\n- Provide regular updates to emergency services\n\n## 6. Compliance and Reporting\n\nAll safety incidents, near misses, and potential hazards must be reported through the official Railway Safety Management System within 24 hours of occurrence.\n\nRegular safety audits will be conducted to ensure compliance with these guidelines.\n  `,\n  processingTime: 3450,\n  chunks: 12,\n};\n\nconst DocumentsPage: React.FC = () => {\n  const [documents, setDocuments] = useState<Document[]>(SAMPLE_DOCUMENTS);\n  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);\n  const [isViewModalOpen, setIsViewModalOpen] = useState(false);\n  const [extractionDetails, setExtractionDetails] = useState<DocumentExtractionDetails>(SAMPLE_EXTRACTION_DETAILS);\n  const [isCategoryManagementOpen, setIsCategoryManagementOpen] = useState(false);\n  const [isCategoryCreatorOpen, setIsCategoryCreatorOpen] = useState(false);\n\n  // Fetch documents from the backend\n  useEffect(() => {\n    fetchDocuments();\n\n    // Set up document upload event listener\n    const handleDocumentUploaded = (event: CustomEvent) => {\n      const newDocument = event.detail;\n      setDocuments(prev => [newDocument, ...prev]);\n      // Refresh the documents list to get the latest from Supabase\n      setTimeout(() => fetchDocuments(), 1000); // Small delay to allow backend processing\n    };\n\n    // Add event listener\n    window.addEventListener('documentUploaded', handleDocumentUploaded as EventListener);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('documentUploaded', handleDocumentUploaded as EventListener);\n    };\n  }, []);\n\n  const fetchDocuments = async () => {\n    try {\n      const data = await getDocuments();\n      if (data && data.length > 0) {\n        // Use real data from Supabase, don't mix with sample data\n        setDocuments(data);\n        console.log(`Loaded ${data.length} documents from Supabase`);\n      } else {\n        console.log('No documents found from API, using sample data');\n        // Only use sample data if no real data is available\n        setDocuments(SAMPLE_DOCUMENTS);\n      }\n    } catch (error) {\n      console.error('Error fetching documents:', error);\n      // Keep using sample data if API call fails\n      setDocuments(SAMPLE_DOCUMENTS);\n    }\n  };\n\n  const handleViewDocument = (document: Document) => {\n    setSelectedDocument(document);\n\n    // Fetch actual extraction details from the backend\n    async function fetchExtractionDetails() {\n      try {\n        // Import our API functions\n        const { getDocumentExtractionDetails, getDocumentContent } = await import('../../services/api');\n\n        // First try the specific extraction details endpoint\n        try {\n          const data = await getDocumentExtractionDetails(document.id);\n          console.log('Extraction details response:', data);\n          setExtractionDetails(data);\n          return;\n        } catch (extractionError) {\n          console.warn('Failed to get extraction details, trying content endpoint:', extractionError);\n        }\n\n        // If that fails, try the general document content endpoint\n        try {\n          const contentData = await getDocumentContent(document.id);\n          console.log('Document content response:', contentData);\n\n          // Create extraction details from content data\n          const details = {\n            extractedContent: contentData.content || 'No content available',\n            extractionMethod: contentData.extraction_method || 'Unknown',\n            qualityScore: contentData.quality_score || 75,\n            processingTime: contentData.processing_time || 1250,\n            chunks: contentData.chunks_count || 5,\n            warnings: [],\n            fallbackReason: ''\n          };\n\n          setExtractionDetails(details);\n          return;\n        } catch (contentError) {\n          console.error('Failed to get document content:', contentError);\n          throw contentError; // Re-throw to be caught by the outer try-catch\n        }\n      } catch (error) {\n        console.error('Error fetching extraction details:', error);\n\n        // Fallback data in case of error\n        const errorFallbackDetails = {\n          extractedContent: `Unable to retrieve content for ${document.name} due to an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again later.`,\n          extractionMethod: 'Error Fallback',\n          qualityScore: 30,\n          processingTime: 0,\n          chunks: 0,\n          warnings: ['Error retrieving content', error instanceof Error ? error.message : 'Unknown error'],\n          fallbackReason: 'Error occurred while fetching content'\n        };\n\n        setExtractionDetails(errorFallbackDetails);\n      }\n    }\n\n    fetchExtractionDetails();\n    setIsViewModalOpen(true);\n  };\n\n  const handleEditDocument = (document: Document) => {\n    // In a real app, you would implement an edit modal or navigate to an edit page\n    alert(`Edit document: ${document.name}`);\n  };\n\n  const handleDeleteDocument = (document: Document) => {\n    if (window.confirm(`Are you sure you want to delete \"${document.name}\"?`)) {\n      // In a real app, you would make an API call to delete the document\n      // async function deleteDocument() {\n      //   try {\n      //     await fetch(`/api/documents/${document.id}`, { method: 'DELETE' });\n      //     setDocuments(documents.filter(doc => doc.id !== document.id));\n      //   } catch (error) {\n      //     console.error('Error deleting document:', error);\n      //   }\n      // }\n      // deleteDocument();\n\n      // For the demo, just filter it out\n      setDocuments(documents.filter(doc => doc.id !== document.id));\n    }\n  };\n\n  const handleReprocessDocument = (document: Document) => {\n    // In a real app, you would implement a reprocessing logic\n    alert(`Reprocess document: ${document.name}`);\n  };\n\n  const handleReprocessWithTool = async (document: Document, tool: ExtractionTool) => {\n    // In a real app, you would make an API call to reprocess with the selected tool\n    alert(`Reprocessing ${document.name} with ${tool}`);\n\n    // Simulate processing time\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    return true;\n  };\n\n  const handleCategoryUpdate = (updatedDocument: Document) => {\n    // Update the document in the local state\n    setDocuments(prevDocuments =>\n      prevDocuments.map(doc =>\n        doc.id === updatedDocument.id ? updatedDocument : doc\n      )\n    );\n  };\n\n  return (\n    <div className=\"h-full flex flex-col bg-gray-50 transition-colors duration-300\">\n      {/* Fixed header section */}\n      <div className=\"bg-white p-4 shadow-sm z-10 transition-colors duration-300\">\n        <div className=\"container mx-auto\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">Document Management</h1>\n            <button\n              onClick={() => setIsCategoryManagementOpen(true)}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\"\n            >\n              <Settings className=\"h-4 w-4 mr-2\" />\n              Manage Categories\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Scrollable content section */}\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <div className=\"container mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {/* Left side: Upload form */}\n            <div className=\"lg:col-span-1\">\n              <DocumentUploadForm />\n            </div>\n\n            {/* Right side: Documents table */}\n            <div className=\"lg:col-span-2\">\n              <DocumentsTable\n                documents={documents}\n                onView={handleViewDocument}\n                onEdit={handleEditDocument}\n                onDelete={handleDeleteDocument}\n                onReprocess={handleReprocessDocument}\n                onCategoryUpdate={handleCategoryUpdate}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Document View Modal */}\n      {selectedDocument && (\n        <DocumentViewModal\n          document={selectedDocument}\n          extractionDetails={extractionDetails}\n          isOpen={isViewModalOpen}\n          onClose={() => setIsViewModalOpen(false)}\n          onReprocess={handleReprocessWithTool}\n        />\n      )}\n\n      {/* Category Management Modal */}\n      <CategoryManagement\n        isOpen={isCategoryManagementOpen}\n        onClose={() => setIsCategoryManagementOpen(false)}\n      />\n    </div>\n  );\n};\n\nexport default DocumentsPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,OAAOC,kBAAkB,MAAM,+CAA+C;AAC9E,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,kBAAkB,MAAM,+CAA+C;AAE9E,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,QAAQ,QAAoB,cAAc;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAA4B,GAAG,CACnC;EACEC,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,gCAAgC;EACtCC,UAAU,EAAE,sBAAsB;EAClCC,YAAY,EAAE,QAAQ;EACtBC,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,SAAS;EACtBC,UAAU,EAAE,mBAAmB;EAC/BC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,WAAW;EACnBC,QAAQ,EAAE,+CAA+C;EACzDC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,2BAA2B;EACjCC,UAAU,EAAE,sBAAsB;EAClCC,YAAY,EAAE,WAAW;EACzBC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,wBAAwB;EACpCC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,WAAW;EACnBC,QAAQ,EAAE,0CAA0C;EACpDC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,wCAAwC;EAC9CC,UAAU,EAAE,sBAAsB;EAClCC,YAAY,EAAE,YAAY;EAC1BC,QAAQ,EAAE,WAAW;EACrBE,UAAU,EAAE,wBAAwB;EACpCC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,WAAW;EACnBC,QAAQ,EAAE,wCAAwC;EAClDC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,yBAAyB;EAC/BC,UAAU,EAAE,sBAAsB;EAClCC,YAAY,EAAE,gBAAgB;EAC9BC,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,oBAAoB;EACjCO,aAAa,EAAE,YAAY;EAC3BN,UAAU,EAAE,sBAAsB;EAClCC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,WAAW;EACnBC,QAAQ,EAAE,yCAAyC;EACnDC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,4CAA4C;EAClDC,UAAU,EAAE,sBAAsB;EAClCC,YAAY,EAAE,gBAAgB;EAC9BC,QAAQ,EAAE,iBAAiB;EAC3BE,UAAU,EAAE,uBAAuB;EACnCE,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE,+CAA+C;EACzDC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACZ,CAAC,CACF;;AAED;AACA,MAAME,yBAAoD,GAAG;EAC3DC,gBAAgB,EAAE,SAAS;EAC3BP,YAAY,EAAE,EAAE;EAChBQ,QAAQ,EAAE,CAAC,2CAA2C,CAAC;EACvDC,gBAAgB,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EACDC,cAAc,EAAE,IAAI;EACpBC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EACpC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAaU,gBAAgB,CAAC;EACxE,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAkB,IAAI,CAAC;EAC/E,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtC,QAAQ,CAA4BwB,yBAAyB,CAAC;EAChH,MAAM,CAACe,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACyC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd0C,cAAc,CAAC,CAAC;;IAEhB;IACA,MAAMC,sBAAsB,GAAIC,KAAkB,IAAK;MACrD,MAAMC,WAAW,GAAGD,KAAK,CAACE,MAAM;MAChCf,YAAY,CAACgB,IAAI,IAAI,CAACF,WAAW,EAAE,GAAGE,IAAI,CAAC,CAAC;MAC5C;MACAC,UAAU,CAAC,MAAMN,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAC5C,CAAC;;IAED;IACAO,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEP,sBAAuC,CAAC;;IAEpF;IACA,OAAO,MAAM;MACXM,MAAM,CAACE,mBAAmB,CAAC,kBAAkB,EAAER,sBAAuC,CAAC;IACzF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMU,IAAI,GAAG,MAAM/C,YAAY,CAAC,CAAC;MACjC,IAAI+C,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3B;QACAtB,YAAY,CAACqB,IAAI,CAAC;QAClBE,OAAO,CAACC,GAAG,CAAC,UAAUH,IAAI,CAACC,MAAM,0BAA0B,CAAC;MAC9D,CAAC,MAAM;QACLC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D;QACAxB,YAAY,CAACtB,gBAAgB,CAAC;MAChC;IACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACAzB,YAAY,CAACtB,gBAAgB,CAAC;IAChC;EACF,CAAC;EAED,MAAMgD,kBAAkB,GAAIC,QAAkB,IAAK;IACjDzB,mBAAmB,CAACyB,QAAQ,CAAC;;IAE7B;IACA,eAAeC,sBAAsBA,CAAA,EAAG;MACtC,IAAI;QACF;QACA,MAAM;UAAEC,4BAA4B;UAAEC;QAAmB,CAAC,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC;;QAE/F;QACA,IAAI;UACF,MAAMT,IAAI,GAAG,MAAMQ,4BAA4B,CAACF,QAAQ,CAAChD,EAAE,CAAC;UAC5D4C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEH,IAAI,CAAC;UACjDf,oBAAoB,CAACe,IAAI,CAAC;UAC1B;QACF,CAAC,CAAC,OAAOU,eAAe,EAAE;UACxBR,OAAO,CAACS,IAAI,CAAC,4DAA4D,EAAED,eAAe,CAAC;QAC7F;;QAEA;QACA,IAAI;UACF,MAAME,WAAW,GAAG,MAAMH,kBAAkB,CAACH,QAAQ,CAAChD,EAAE,CAAC;UACzD4C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAES,WAAW,CAAC;;UAEtD;UACA,MAAMC,OAAO,GAAG;YACdvC,gBAAgB,EAAEsC,WAAW,CAACE,OAAO,IAAI,sBAAsB;YAC/D1C,gBAAgB,EAAEwC,WAAW,CAACG,iBAAiB,IAAI,SAAS;YAC5DlD,YAAY,EAAE+C,WAAW,CAACI,aAAa,IAAI,EAAE;YAC7CzC,cAAc,EAAEqC,WAAW,CAACK,eAAe,IAAI,IAAI;YACnDzC,MAAM,EAAEoC,WAAW,CAACM,YAAY,IAAI,CAAC;YACrC7C,QAAQ,EAAE,EAAE;YACZ8C,cAAc,EAAE;UAClB,CAAC;UAEDlC,oBAAoB,CAAC4B,OAAO,CAAC;UAC7B;QACF,CAAC,CAAC,OAAOO,YAAY,EAAE;UACrBlB,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEgB,YAAY,CAAC;UAC9D,MAAMA,YAAY,CAAC,CAAC;QACtB;MACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;;QAE1D;QACA,MAAMiB,oBAAoB,GAAG;UAC3B/C,gBAAgB,EAAE,kCAAkCgC,QAAQ,CAAC/C,IAAI,qBAAqB6C,KAAK,YAAYkB,KAAK,GAAGlB,KAAK,CAACmB,OAAO,GAAG,eAAe,2BAA2B;UACzKnD,gBAAgB,EAAE,gBAAgB;UAClCP,YAAY,EAAE,EAAE;UAChBU,cAAc,EAAE,CAAC;UACjBC,MAAM,EAAE,CAAC;UACTH,QAAQ,EAAE,CAAC,0BAA0B,EAAE+B,KAAK,YAAYkB,KAAK,GAAGlB,KAAK,CAACmB,OAAO,GAAG,eAAe,CAAC;UAChGJ,cAAc,EAAE;QAClB,CAAC;QAEDlC,oBAAoB,CAACoC,oBAAoB,CAAC;MAC5C;IACF;IAEAd,sBAAsB,CAAC,CAAC;IACxBxB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMyC,kBAAkB,GAAIlB,QAAkB,IAAK;IACjD;IACAmB,KAAK,CAAC,kBAAkBnB,QAAQ,CAAC/C,IAAI,EAAE,CAAC;EAC1C,CAAC;EAED,MAAMmE,oBAAoB,GAAIpB,QAAkB,IAAK;IACnD,IAAIT,MAAM,CAAC8B,OAAO,CAAC,oCAAoCrB,QAAQ,CAAC/C,IAAI,IAAI,CAAC,EAAE;MACzE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACAoB,YAAY,CAACD,SAAS,CAACkD,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACvE,EAAE,KAAKgD,QAAQ,CAAChD,EAAE,CAAC,CAAC;IAC/D;EACF,CAAC;EAED,MAAMwE,uBAAuB,GAAIxB,QAAkB,IAAK;IACtD;IACAmB,KAAK,CAAC,uBAAuBnB,QAAQ,CAAC/C,IAAI,EAAE,CAAC;EAC/C,CAAC;EAED,MAAMwE,uBAAuB,GAAG,MAAAA,CAAOzB,QAAkB,EAAE0B,IAAoB,KAAK;IAClF;IACAP,KAAK,CAAC,gBAAgBnB,QAAQ,CAAC/C,IAAI,SAASyE,IAAI,EAAE,CAAC;;IAEnD;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAItC,UAAU,CAACsC,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,oBAAoB,GAAIC,eAAyB,IAAK;IAC1D;IACAzD,YAAY,CAAC0D,aAAa,IACxBA,aAAa,CAACC,GAAG,CAACT,GAAG,IACnBA,GAAG,CAACvE,EAAE,KAAK8E,eAAe,CAAC9E,EAAE,GAAG8E,eAAe,GAAGP,GACpD,CACF,CAAC;EACH,CAAC;EAED,oBACEzE,OAAA;IAAKmF,SAAS,EAAC,gEAAgE;IAAAC,QAAA,gBAE7EpF,OAAA;MAAKmF,SAAS,EAAC,4DAA4D;MAAAC,QAAA,eACzEpF,OAAA;QAAKmF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCpF,OAAA;UAAKmF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDpF,OAAA;YAAImF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzExF,OAAA;YACEyF,OAAO,EAAEA,CAAA,KAAM1D,2BAA2B,CAAC,IAAI,CAAE;YACjDoD,SAAS,EAAC,0LAA0L;YAAAC,QAAA,gBAEpMpF,OAAA,CAACF,QAAQ;cAACqF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxF,OAAA;MAAKmF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCpF,OAAA;QAAKmF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCpF,OAAA;UAAKmF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpDpF,OAAA;YAAKmF,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BpF,OAAA,CAACP,kBAAkB;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAGNxF,OAAA;YAAKmF,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BpF,OAAA,CAACN,cAAc;cACb4B,SAAS,EAAEA,SAAU;cACrBoE,MAAM,EAAEzC,kBAAmB;cAC3B0C,MAAM,EAAEvB,kBAAmB;cAC3BwB,QAAQ,EAAEtB,oBAAqB;cAC/BuB,WAAW,EAAEnB,uBAAwB;cACrCoB,gBAAgB,EAAEf;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhE,gBAAgB,iBACfxB,OAAA,CAACL,iBAAiB;MAChBuD,QAAQ,EAAE1B,gBAAiB;MAC3BI,iBAAiB,EAAEA,iBAAkB;MACrCmE,MAAM,EAAErE,eAAgB;MACxBsE,OAAO,EAAEA,CAAA,KAAMrE,kBAAkB,CAAC,KAAK,CAAE;MACzCkE,WAAW,EAAElB;IAAwB;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF,eAGDxF,OAAA,CAACJ,kBAAkB;MACjBmG,MAAM,EAAEjE,wBAAyB;MACjCkE,OAAO,EAAEA,CAAA,KAAMjE,2BAA2B,CAAC,KAAK;IAAE;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAED,eAAenE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}