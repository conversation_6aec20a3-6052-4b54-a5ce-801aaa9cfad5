[{"C:\\IR App\\frontend\\src\\index.tsx": "1", "C:\\IR App\\frontend\\src\\reportWebVitals.ts": "2", "C:\\IR App\\frontend\\src\\AppRouter.tsx": "3", "C:\\IR App\\frontend\\src\\App.tsx": "4", "C:\\IR App\\frontend\\src\\pages\\DocumentViewer.tsx": "5", "C:\\IR App\\frontend\\src\\pages\\documents\\DocumentsPage.tsx": "6", "C:\\IR App\\frontend\\src\\pages\\websites\\WebsitesPage.tsx": "7", "C:\\IR App\\frontend\\src\\components\\layout\\Header.tsx": "8", "C:\\IR App\\frontend\\src\\pages\\settings\\SettingsPage.tsx": "9", "C:\\IR App\\frontend\\src\\services\\api.ts": "10", "C:\\IR App\\frontend\\src\\components\\ui\\LLMSelector.tsx": "11", "C:\\IR App\\frontend\\src\\components\\ui\\InteractiveAnswer.tsx": "12", "C:\\IR App\\frontend\\src\\components\\documents\\SimplePDFViewer.tsx": "13", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentViewModal.tsx": "14", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentUploadForm.tsx": "15", "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteExtractForm.tsx": "16", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentsTable.tsx": "17", "C:\\IR App\\frontend\\src\\components\\websites\\WebsitesTable.tsx": "18", "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteViewModal.tsx": "19", "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackEmailConfig.tsx": "20", "C:\\IR App\\frontend\\src\\services\\supabase.ts": "21", "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackModal.tsx": "22", "C:\\IR App\\frontend\\src\\components\\documents\\PDFViewer.tsx": "23", "C:\\IR App\\frontend\\src\\components\\ui\\alert.tsx": "24", "C:\\IR App\\frontend\\src\\contexts\\ChatContext.tsx": "25", "C:\\IR App\\frontend\\src\\components\\chat\\ChatSidebar.tsx": "26", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentCategoryEditor.tsx": "27", "C:\\IR App\\frontend\\src\\services\\categoryApi.ts": "28", "C:\\IR App\\frontend\\src\\components\\documents\\CategoryManagement.tsx": "29", "C:\\IR App\\frontend\\src\\components\\ui\\TrainLoader.tsx": "30", "C:\\IR App\\frontend\\src\\components\\ui\\VisualContent.tsx": "31", "C:\\IR App\\frontend\\src\\components\\documents\\BulkCategoryEditor.tsx": "32", "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteCategoryManagement.tsx": "33", "C:\\IR App\\frontend\\src\\components\\categories\\CategoryCreator.tsx": "34"}, {"size": 518, "mtime": 1746886123631, "results": "35", "hashOfConfig": "36"}, {"size": 425, "mtime": 1746757926945, "results": "37", "hashOfConfig": "36"}, {"size": 1618, "mtime": 1748503578318, "results": "38", "hashOfConfig": "36"}, {"size": 44909, "mtime": 1748514242974, "results": "39", "hashOfConfig": "36"}, {"size": 10762, "mtime": 1748095932232, "results": "40", "hashOfConfig": "36"}, {"size": 14596, "mtime": 1748509097074, "results": "41", "hashOfConfig": "36"}, {"size": 13348, "mtime": 1748500978274, "results": "42", "hashOfConfig": "36"}, {"size": 2799, "mtime": 1748503610160, "results": "43", "hashOfConfig": "36"}, {"size": 18739, "mtime": 1748489500192, "results": "44", "hashOfConfig": "36"}, {"size": 34987, "mtime": 1748500677580, "results": "45", "hashOfConfig": "36"}, {"size": 8620, "mtime": 1748335238627, "results": "46", "hashOfConfig": "36"}, {"size": 5431, "mtime": 1748489621678, "results": "47", "hashOfConfig": "36"}, {"size": 4449, "mtime": 1748082417413, "results": "48", "hashOfConfig": "36"}, {"size": 9718, "mtime": 1748489558185, "results": "49", "hashOfConfig": "36"}, {"size": 27395, "mtime": 1748520433359, "results": "50", "hashOfConfig": "36"}, {"size": 20569, "mtime": 1748334590793, "results": "51", "hashOfConfig": "36"}, {"size": 28956, "mtime": 1748500828222, "results": "52", "hashOfConfig": "36"}, {"size": 10429, "mtime": 1748502803836, "results": "53", "hashOfConfig": "36"}, {"size": 8113, "mtime": 1746848396668, "results": "54", "hashOfConfig": "36"}, {"size": 5634, "mtime": 1748333597589, "results": "55", "hashOfConfig": "36"}, {"size": 13195, "mtime": 1748513814971, "results": "56", "hashOfConfig": "36"}, {"size": 5354, "mtime": 1748489621678, "results": "57", "hashOfConfig": "36"}, {"size": 6103, "mtime": 1748094001907, "results": "58", "hashOfConfig": "36"}, {"size": 1514, "mtime": 1748265464172, "results": "59", "hashOfConfig": "36"}, {"size": 5260, "mtime": 1748339426726, "results": "60", "hashOfConfig": "36"}, {"size": 14385, "mtime": 1748512061385, "results": "61", "hashOfConfig": "36"}, {"size": 12758, "mtime": 1748265464075, "results": "62", "hashOfConfig": "36"}, {"size": 9021, "mtime": 1748513858027, "results": "63", "hashOfConfig": "36"}, {"size": 14307, "mtime": 1748265464038, "results": "64", "hashOfConfig": "36"}, {"size": 6360, "mtime": 1748339169110, "results": "65", "hashOfConfig": "36"}, {"size": 19905, "mtime": 1748369722086, "results": "66", "hashOfConfig": "36"}, {"size": 9886, "mtime": 1748500881530, "results": "67", "hashOfConfig": "36"}, {"size": 9157, "mtime": 1748502759713, "results": "68", "hashOfConfig": "36"}, {"size": 30902, "mtime": 1748512713305, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, "ngactd", {"filePath": "73", "messages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "75", "messages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "77", "messages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "79", "messages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "81", "messages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "83", "messages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "85", "messages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "87", "messages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "89", "messages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "91", "messages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "93", "messages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "95", "messages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "97", "messages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "99", "messages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "101"}, {"filePath": "102", "messages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "104", "messages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "108", "messages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "110", "messages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "112", "messages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "114", "usedDeprecatedRules": "72"}, {"filePath": "115", "messages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "117", "messages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "119", "messages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "121", "messages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "123", "messages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "125", "messages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "127", "messages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "129", "messages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "131", "messages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "133", "messages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "135", "usedDeprecatedRules": "72"}, {"filePath": "136", "messages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "138", "messages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "140", "messages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, "C:\\IR App\\frontend\\src\\index.tsx", [], ["142", "143"], "C:\\IR App\\frontend\\src\\reportWebVitals.ts", [], "C:\\IR App\\frontend\\src\\AppRouter.tsx", [], "C:\\IR App\\frontend\\src\\App.tsx", [], "C:\\IR App\\frontend\\src\\pages\\DocumentViewer.tsx", [], "C:\\IR App\\frontend\\src\\pages\\documents\\DocumentsPage.tsx", [], "C:\\IR App\\frontend\\src\\pages\\websites\\WebsitesPage.tsx", [], "C:\\IR App\\frontend\\src\\components\\layout\\Header.tsx", [], "C:\\IR App\\frontend\\src\\pages\\settings\\SettingsPage.tsx", [], "C:\\IR App\\frontend\\src\\services\\api.ts", [], "C:\\IR App\\frontend\\src\\components\\ui\\LLMSelector.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\InteractiveAnswer.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\SimplePDFViewer.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentViewModal.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentUploadForm.tsx", ["144"], "import React, { useState, useEffect } from 'react';\r\nimport { uploadDocument } from '../../services/api';\r\nimport { Alert, AlertDescription, AlertTitle } from \"../ui/alert\";\r\nimport { DocumentCategory, CategoryHierarchy, CategoryCreate } from '../../types/documents';\r\nimport { getCategories, createCategory } from '../../services/categoryApi';\r\n\r\n// Component for uploading documents with category selection\r\ninterface FormErrors {\r\n  documentName?: string;\r\n  mainCategory?: string;\r\n  category?: string;\r\n  file?: string;\r\n}\r\n\r\nconst DocumentUploadForm: React.FC = (): React.ReactElement => {\r\n  // Form state\r\n  const [documentName, setDocumentName] = useState('');\r\n  const [mainCategory, setMainCategory] = useState('');\r\n  const [category, setCategory] = useState('');\r\n  const [subCategory, setSubCategory] = useState('');\r\n  const [minorCategory, setMinorCategory] = useState('');\r\n  const [file, setFile] = useState<File | null>(null);\r\n\r\n  // UI state\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [newCategoryInput, setNewCategoryInput] = useState('');\r\n  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);\r\n  const [categoryType, setCategoryType] = useState<'Main' | 'Category' | 'Sub' | 'Minor'>('Main');\r\n\r\n  // Upload status tracking\r\n  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'processing' | 'success' | 'error'>('idle');\r\n  const [uploadProgress, setUploadProgress] = useState(0);\r\n  const [extractedChunks, setExtractedChunks] = useState<any[]>([]);\r\n\r\n  // Form validation and feedback\r\n  const [errors, setErrors] = useState<FormErrors>({});\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [touched, setTouched] = useState<Record<string, boolean>>({});\r\n\r\n  // Categories from API\r\n  const [allCategories, setAllCategories] = useState<CategoryHierarchy[]>([]);\r\n  const [loadingCategories, setLoadingCategories] = useState(false);\r\n\r\n  // Helper functions to filter categories by type\r\n  const getMainCategories = () => allCategories.filter(cat => cat.type === 'main_category');\r\n  const getCategoriesByParent = (parentId: string) => allCategories.filter(cat => cat.parent_id === parentId);\r\n  const getSubCategoriesByParent = (parentId: string) => allCategories.filter(cat => cat.parent_id === parentId);\r\n  const getMinorCategoriesByParent = (parentId: string) => allCategories.filter(cat => cat.parent_id === parentId);\r\n\r\n  // Load categories from API\r\n  useEffect(() => {\r\n    const loadCategories = async () => {\r\n      try {\r\n        setLoadingCategories(true);\r\n        const categories = await getCategories();\r\n        setAllCategories(categories);\r\n      } catch (error) {\r\n        console.warn('Failed to load categories:', error);\r\n        setErrorMessage('Failed to load categories. Category selection may be limited.');\r\n      } finally {\r\n        setLoadingCategories(false);\r\n      }\r\n    };\r\n\r\n    loadCategories();\r\n  }, []);\r\n\r\n  // Effect to validate form\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const newErrors: FormErrors = {};\r\n\r\n    if (touched.documentName && !documentName.trim()) {\r\n      newErrors.documentName = 'Document name is required';\r\n    }\r\n\r\n    if (touched.mainCategory && !mainCategory) {\r\n      newErrors.mainCategory = 'Main category is required';\r\n    }\r\n\r\n    if (touched.category && !category) {\r\n      newErrors.category = 'Category is required';\r\n    }\r\n\r\n    if (touched.file && !file) {\r\n      newErrors.file = 'Document file is required';\r\n    } else if (file) {\r\n      // Validate file type\r\n      const allowedTypes = [\r\n        'application/pdf',\r\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n        'image/jpeg',\r\n        'image/png',\r\n        'text/plain'\r\n      ];\r\n\r\n      if (!allowedTypes.includes(file.type)) {\r\n        newErrors.file = 'File type not supported. Please upload PDF, DOCX, XLSX, JPG, PNG, or TXT';\r\n      }\r\n\r\n      // Validate file size (200MB max)\r\n      const maxSize = 200 * 1024 * 1024; // 200MB\r\n      if (file.size > maxSize) {\r\n        newErrors.file = 'File too large. Maximum size is 200MB';\r\n      }\r\n    }\r\n\r\n    // Only update state if component is still mounted\r\n    if (isMounted) {\r\n      setErrors(newErrors);\r\n    }\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [documentName, mainCategory, category, file, touched]);\r\n\r\n  // Mark fields as touched when user interacts with them\r\n  const markAsTouched = (field: string) => {\r\n    setTouched(prev => ({ ...prev, [field]: true }));\r\n  };\r\n\r\n\r\n\r\n  // useEffect to clear success message after 5 seconds\r\n  useEffect(() => {\r\n    if (successMessage) {\r\n      const timer = setTimeout(() => {\r\n        setSuccessMessage('');\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [successMessage]);\r\n\r\n  // useEffect to clear error message after 5 seconds\r\n  useEffect(() => {\r\n    if (errorMessage) {\r\n      const timer = setTimeout(() => {\r\n        setErrorMessage('');\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [errorMessage]);\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files[0]) {\r\n      const selectedFile = e.target.files[0];\r\n      setFile(selectedFile);\r\n      markAsTouched('file');\r\n\r\n      // Set document name from filename if empty\r\n      if (!documentName.trim()) {\r\n        // Remove extension and replace underscores/hyphens with spaces\r\n        const nameFromFile = selectedFile.name.replace(/\\.[^/.]+$/, '').replace(/[_-]/g, ' ');\r\n        setDocumentName(nameFromFile);\r\n        markAsTouched('documentName');\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleNewCategorySubmit = async () => {\r\n    if (!newCategoryInput.trim()) return;\r\n\r\n    setIsUploading(true);\r\n    try {\r\n      // Determine parent ID based on category type\r\n      let parentId = null;\r\n      let categoryTypeForAPI: 'main_category' | 'category' | 'sub_category' | 'minor_category';\r\n\r\n      if (categoryType === 'Main') {\r\n        categoryTypeForAPI = 'main_category';\r\n      } else if (categoryType === 'Category') {\r\n        categoryTypeForAPI = 'category';\r\n        parentId = mainCategory;\r\n      } else if (categoryType === 'Sub') {\r\n        categoryTypeForAPI = 'sub_category';\r\n        parentId = category;\r\n      } else if (categoryType === 'Minor') {\r\n        categoryTypeForAPI = 'minor_category';\r\n        parentId = subCategory;\r\n      } else {\r\n        // Default fallback\r\n        categoryTypeForAPI = 'main_category';\r\n      }\r\n\r\n      // Create category using API\r\n      const newCategoryData: CategoryCreate = {\r\n        name: newCategoryInput,\r\n        type: categoryTypeForAPI,\r\n        parent_id: parentId,\r\n        description: ''\r\n      };\r\n\r\n      const response = await createCategory(newCategoryData);\r\n\r\n      // Reload categories to get the updated list\r\n      const updatedCategories = await getCategories();\r\n      setAllCategories(updatedCategories);\r\n\r\n      // Set the newly created category as selected\r\n      if (categoryType === 'Main') {\r\n        setMainCategory(response.category?.id || '');\r\n      } else if (categoryType === 'Category') {\r\n        setCategory(response.category?.id || '');\r\n      } else if (categoryType === 'Sub') {\r\n        setSubCategory(response.category?.id || '');\r\n      } else if (categoryType === 'Minor') {\r\n        setMinorCategory(response.category?.id || '');\r\n      }\r\n\r\n      setSuccessMessage(`Created new ${categoryType} category: ${newCategoryInput}`);\r\n    } catch (error) {\r\n      setErrorMessage(`Failed to create category: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    } finally {\r\n      setIsUploading(false);\r\n      setNewCategoryInput('');\r\n      setShowNewCategoryInput(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent): Promise<void> => {\r\n    e.preventDefault();\r\n\r\n    // Mark all fields as touched to show any errors\r\n    markAsTouched('documentName');\r\n    markAsTouched('mainCategory');\r\n    markAsTouched('category');\r\n    markAsTouched('file');\r\n\r\n    // Check if there are any errors\r\n    if (Object.keys(errors).length > 0 || !file) {\r\n      setErrorMessage('Please correct the errors before submitting');\r\n      return;\r\n    }\r\n\r\n    // Track if component is still mounted\r\n    let isMounted = true;\r\n    let progressInterval: NodeJS.Timeout | null = null;\r\n\r\n    // Update UI state\r\n    const safeSetState = (updater: () => void) => {\r\n      if (isMounted) {\r\n        updater();\r\n      }\r\n    };\r\n\r\n    safeSetState(() => {\r\n      setIsUploading(true);\r\n      setUploadStatus('uploading');\r\n      setUploadProgress(10);\r\n      setSuccessMessage('');\r\n      setErrorMessage('');\r\n      setExtractedChunks([]);\r\n    });\r\n\r\n\r\n\r\n    try {\r\n      // Simulate upload progress updates (in a real app, use XHR or fetch with progress events)\r\n      progressInterval = setInterval(() => {\r\n        if (isMounted) {\r\n          setUploadProgress(prev => {\r\n            const newProgress = prev + 5;\r\n            if (newProgress >= 90) {\r\n              if (progressInterval) clearInterval(progressInterval);\r\n              return 90; // Hold at 90% until processing is complete\r\n            }\r\n            return newProgress;\r\n          });\r\n        }\r\n      }, 300);\r\n\r\n\r\n\r\n      // Upload the document\r\n      safeSetState(() => setUploadStatus('uploading'));\r\n\r\n      // Upload the document using the API function\r\n      const response = await uploadDocument(file, \"<EMAIL>\");\r\n\r\n      // Clear the progress interval if it exists\r\n      if (progressInterval) {\r\n        clearInterval(progressInterval);\r\n        progressInterval = null;\r\n      }\r\n\r\n      // Only update state if component is still mounted\r\n      if (!isMounted) return;\r\n\r\n      if (response.success) {\r\n        // Set progress to 100%\r\n        setUploadProgress(100);\r\n        setUploadStatus('success');\r\n\r\n        // Store extracted chunks if available\r\n        if (response.chunks && response.chunks.length > 0) {\r\n          setExtractedChunks(response.chunks);\r\n          console.log('Extracted chunks:', response.chunks);\r\n        }\r\n\r\n        // Create event data for custom event\r\n        const eventData = {\r\n          detail: {\r\n            documentName,\r\n            mainCategory,\r\n            category,\r\n            subCategory,\r\n            minorCategory,\r\n            file,\r\n            uploadedAt: new Date().toISOString(),\r\n            id: response.data?.id || `doc-${Date.now()}`,\r\n            status: 'Extracted', // Update to real status from backend\r\n            fileType: file.name.split('.').pop()?.toLowerCase(),\r\n            qualityScore: 90, // This would come from backend in real app\r\n            chunks: response.chunks || [],\r\n            extractedContent: response.chunks ? response.chunks.map((chunk: any) => chunk.text).join('\\n\\n') : '',\r\n            chunks_extracted: response.chunks_extracted || 0\r\n          }\r\n        };\r\n\r\n        // Dispatch event to notify the DocumentsPage\r\n        const event = new CustomEvent('documentUploaded', { detail: eventData.detail });\r\n        window.dispatchEvent(event);\r\n\r\n        // Set success message (ensure error is cleared)\r\n        setErrorMessage('');\r\n        setSuccessMessage(`Document \"${documentName}\" uploaded successfully!`);\r\n\r\n        // Auto-clear success message after 5 seconds\r\n        const clearSuccessMessage = () => {\r\n          if (isMounted) {\r\n            setSuccessMessage('');\r\n          }\r\n        };\r\n\r\n        setTimeout(clearSuccessMessage, 5000);\r\n\r\n        // Reset form after successful upload\r\n        const resetForm = () => {\r\n          if (isMounted) {\r\n            setDocumentName('');\r\n            setMainCategory('');\r\n            setCategory('');\r\n            setSubCategory('');\r\n            setMinorCategory('');\r\n            setFile(null);\r\n            setUploadStatus('idle');\r\n            setUploadProgress(0);\r\n            setExtractedChunks([]);\r\n            setIsUploading(false);\r\n            setTouched({});\r\n          }\r\n        };\r\n\r\n        setTimeout(resetForm, 3000);\r\n      } else {\r\n        // Handle upload error\r\n        setUploadStatus('error');\r\n        setErrorMessage(response.message || 'Failed to upload document');\r\n      }\r\n    } catch (error) {\r\n      console.error('Upload failed:', error);\r\n      if (isMounted) {\r\n        setUploadStatus('error');\r\n        setErrorMessage(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n      }\r\n    } finally {\r\n      if (isMounted) {\r\n        setIsUploading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md p-6 transition-colors duration-300\">\r\n      <h2 className=\"text-lg font-semibold mb-4 text-gray-900\">Upload Document</h2>\r\n\r\n      {/* Success message */}\r\n      {successMessage && (\r\n        <div className=\"mb-4\">\r\n          <Alert variant=\"success\">\r\n            <AlertTitle>Success</AlertTitle>\r\n            <AlertDescription>{successMessage}</AlertDescription>\r\n          </Alert>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error message */}\r\n      {errorMessage && (\r\n        <div className=\"mb-4\">\r\n          <Alert variant=\"destructive\">\r\n            <AlertTitle>Error</AlertTitle>\r\n            <AlertDescription>{errorMessage}</AlertDescription>\r\n          </Alert>\r\n        </div>\r\n      )}\r\n\r\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n        {/* Document Name */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Document Name <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            value={documentName}\r\n            onChange={(e) => setDocumentName(e.target.value)}\r\n            onBlur={() => markAsTouched('documentName')}\r\n            className={`w-full p-2 border ${errors.documentName ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}\r\n            required\r\n          />\r\n          {errors.documentName && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.documentName}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Main Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Main Category <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={mainCategory}\r\n              onChange={(e) => {\r\n                setMainCategory(e.target.value);\r\n                // Reset dependent categories when main category changes\r\n                setCategory('');\r\n                setSubCategory('');\r\n                setMinorCategory('');\r\n              }}\r\n              onBlur={() => markAsTouched('mainCategory')}\r\n              className={`flex-1 p-2 border ${errors.mainCategory ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}\r\n              required\r\n              disabled={loadingCategories}\r\n            >\r\n              <option value=\"\">{loadingCategories ? 'Loading...' : 'Select Main Category'}</option>\r\n              {getMainCategories().map((cat) => (\r\n                <option key={cat.id} value={cat.id}>\r\n                  {cat.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Main');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n          {errors.mainCategory && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.mainCategory}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Category <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={category}\r\n              onChange={(e) => {\r\n                setCategory(e.target.value);\r\n                // Reset dependent categories when category changes\r\n                setSubCategory('');\r\n                setMinorCategory('');\r\n              }}\r\n              onBlur={() => markAsTouched('category')}\r\n              className={`flex-1 p-2 border ${errors.category ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}\r\n              required\r\n              disabled={!mainCategory || loadingCategories}\r\n            >\r\n              <option value=\"\">Select Category</option>\r\n              {mainCategory && getCategoriesByParent(mainCategory)\r\n                .filter(cat => cat.type === 'category')\r\n                .map((cat) => (\r\n                  <option key={cat.id} value={cat.id}>\r\n                    {cat.name}\r\n                  </option>\r\n                ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Category');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n              disabled={!mainCategory}\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n          {errors.category && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.category}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Sub Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Sub Category\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={subCategory}\r\n              onChange={(e) => {\r\n                setSubCategory(e.target.value);\r\n                // Reset dependent categories when sub category changes\r\n                setMinorCategory('');\r\n              }}\r\n              className=\"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\r\n              disabled={!category || loadingCategories}\r\n            >\r\n              <option value=\"\">Select Sub Category</option>\r\n              {category && getSubCategoriesByParent(category)\r\n                .filter(cat => cat.type === 'sub_category')\r\n                .map((cat) => (\r\n                  <option key={cat.id} value={cat.id}>\r\n                    {cat.name}\r\n                  </option>\r\n                ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Sub');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n              disabled={!category}\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Minor Category */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Minor Category\r\n          </label>\r\n          <div className=\"flex gap-2\">\r\n            <select\r\n              value={minorCategory}\r\n              onChange={(e) => setMinorCategory(e.target.value)}\r\n              className=\"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\r\n              disabled={!subCategory || loadingCategories}\r\n            >\r\n              <option value=\"\">Select Minor Category</option>\r\n              {subCategory && getMinorCategoriesByParent(subCategory)\r\n                .filter(cat => cat.type === 'minor_category')\r\n                .map((cat) => (\r\n                  <option key={cat.id} value={cat.id}>\r\n                    {cat.name}\r\n                  </option>\r\n                ))}\r\n            </select>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setCategoryType('Minor');\r\n                setShowNewCategoryInput(true);\r\n              }}\r\n              className=\"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300\"\r\n              disabled={!subCategory}\r\n            >\r\n              + New\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* File Upload */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Upload Document <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div className=\"flex items-center justify-center w-full\">\r\n            <label\r\n              className={`flex flex-col w-full h-32 border-2 ${errors.file ? 'border-red-500' : 'border-blue-300'} border-dashed hover:bg-gray-50 hover:border-blue-500 rounded-lg cursor-pointer`}\r\n            >\r\n              <div className=\"flex flex-col items-center justify-center pt-7\">\r\n                <svg\r\n                  className=\"w-8 h-8 text-gray-400 group-hover:text-gray-600\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth=\"2\"\r\n                    d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\r\n                  ></path>\r\n                </svg>\r\n                <p className=\"pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600\">\r\n                  {file ? file.name : 'Attach document (PDF, DOCX, XLSX, etc.)'}\r\n                </p>\r\n              </div>\r\n              <input\r\n                type=\"file\"\r\n                className=\"opacity-0\"\r\n                accept=\".pdf,.docx,.xlsx,.txt,.png,.jpg,.jpeg\"\r\n                onChange={handleFileChange}\r\n                onBlur={() => markAsTouched('file')}\r\n                required\r\n              />\r\n            </label>\r\n          </div>\r\n          {errors.file && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.file}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Submit Button */}\r\n        <div>\r\n          <button\r\n            type=\"submit\"\r\n            disabled={isUploading}\r\n            className={`w-full px-4 py-2 text-white font-medium rounded-md ${\r\n              isUploading ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'\r\n            } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n          >\r\n            {isUploading ? 'Uploading...' : 'Upload Document'}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Upload Progress */}\r\n        {uploadStatus !== 'idle' && (\r\n          <div className=\"mt-4\">\r\n            <div className=\"flex items-center justify-between mb-1\">\r\n              <span className=\"text-sm font-medium text-gray-700\">\r\n                {uploadStatus === 'uploading' ? 'Uploading...' :\r\n                 uploadStatus === 'processing' ? 'Processing...' :\r\n                 uploadStatus === 'success' ? 'Upload Complete' :\r\n                 'Upload Failed'}\r\n              </span>\r\n              <span className=\"text-sm font-medium text-gray-700\">{uploadProgress}%</span>\r\n            </div>\r\n            <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n              <div\r\n                className={`h-2.5 rounded-full ${\r\n                  uploadStatus === 'error' ? 'bg-red-600' :\r\n                  uploadStatus === 'success' ? 'bg-green-600' : 'bg-blue-600'\r\n                }`}\r\n                style={{ width: `${uploadProgress}%` }}\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Extracted Chunks Preview (shown after successful upload) */}\r\n        {uploadStatus === 'success' && extractedChunks.length > 0 && (\r\n          <div className=\"mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50\">\r\n            <h3 className=\"text-sm font-medium text-gray-700 mb-2\">Extracted Content Preview:</h3>\r\n            <div className=\"max-h-40 overflow-y-auto text-sm text-gray-600\">\r\n              {extractedChunks.slice(0, 3).map((chunk, index) => (\r\n                <div key={index} className=\"mb-2 p-2 bg-white rounded border border-gray-200\">\r\n                  <p className=\"text-xs text-gray-500 mb-1\">\r\n                    {chunk.source_type === 'document' ?\r\n                      `Page ${chunk.page || 'N/A'}` :\r\n                      `Source: ${chunk.source || 'Unknown'}`}\r\n                  </p>\r\n                  <p>{chunk.text?.substring(0, 150)}...</p>\r\n                </div>\r\n              ))}\r\n              {extractedChunks.length > 3 && (\r\n                <p className=\"text-xs text-gray-500 text-center mt-2\">\r\n                  + {extractedChunks.length - 3} more chunks not shown\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </form>\r\n\r\n      {/* New Category Modal */}\r\n      {showNewCategoryInput && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\r\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\r\n            <h3 className=\"text-lg font-medium mb-4\">Add New {categoryType} Category</h3>\r\n            <input\r\n              type=\"text\"\r\n              value={newCategoryInput}\r\n              onChange={(e) => setNewCategoryInput(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4\"\r\n              placeholder={`Enter ${categoryType} Category name`}\r\n              autoFocus\r\n            />\r\n            <div className=\"flex justify-end gap-2\">\r\n              <button\r\n                onClick={() => setShowNewCategoryInput(false)}\r\n                className=\"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={handleNewCategorySubmit}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\r\n              >\r\n                Add Category\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DocumentUploadForm;\r\n", "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteExtractForm.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentsTable.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsitesTable.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteViewModal.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackEmailConfig.tsx", [], "C:\\IR App\\frontend\\src\\services\\supabase.ts", ["145"], "// Supabase client for frontend\nimport { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://rkllidjktazafeinezgo.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA';\n\n// Create Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Document types\nexport interface Document {\n  id: string;\n  filename: string;\n  display_name?: string;\n  file_path: string;\n  file_type?: string;\n  file_size?: number;\n  main_category?: string;\n  category?: string;\n  sub_category?: string;\n  minor_category?: string;\n  uploaded_by?: string;\n  created_at?: string;\n  updated_at?: string;\n  status?: string;\n  quality_score?: number;\n}\n\nexport interface DocumentChunk {\n  id: string;\n  document_id: string;\n  chunk_index: number;\n  page_number: number;\n  text: string;\n  metadata?: any;\n  created_at?: string;\n}\n\n// Website types\nexport interface Website {\n  id: string;\n  url: string;\n  domain?: string;\n  title?: string;\n  description?: string;\n  category?: string;\n  submitted_by?: string;\n  created_at?: string;\n  updated_at?: string;\n  status?: string;\n  quality_score?: number;\n}\n\nexport interface WebsiteChunk {\n  id: string;\n  website_id: string;\n  chunk_index: number;\n  text: string;\n  metadata?: any;\n  created_at?: string;\n}\n\n// Query types\nexport interface Query {\n  id: string;\n  user_id?: string;\n  query_text: string;\n  answer_text?: string;\n  llm_model?: string;\n  sources?: any;\n  created_at?: string;\n  processing_time?: number;\n}\n\n// Document operations\nexport const getDocuments = async (): Promise<Document[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('documents')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching documents:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocuments:', error);\n    return [];\n  }\n};\n\nexport const getDocumentById = async (id: string): Promise<Document | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('documents')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching document:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in getDocumentById:', error);\n    return null;\n  }\n};\n\nexport const getDocumentChunks = async (documentId: string): Promise<DocumentChunk[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('document_chunks')\n      .select('*')\n      .eq('document_id', documentId)\n      .order('chunk_index', { ascending: true });\n\n    if (error) {\n      console.error('Error fetching document chunks:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocumentChunks:', error);\n    return [];\n  }\n};\n\n// Website operations\nexport const getWebsites = async (): Promise<Website[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('websites')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching websites:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getWebsites:', error);\n    return [];\n  }\n};\n\nexport const getWebsiteById = async (id: string): Promise<Website | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('websites')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching website:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in getWebsiteById:', error);\n    return null;\n  }\n};\n\n// Query operations\nexport const saveQuery = async (query: Omit<Query, 'id' | 'created_at'>): Promise<Query | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('queries')\n      .insert([query])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error saving query:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in saveQuery:', error);\n    return null;\n  }\n};\n\nexport const getRecentQueries = async (limit: number = 10): Promise<Query[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('queries')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(limit);\n\n    if (error) {\n      console.error('Error fetching recent queries:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getRecentQueries:', error);\n    return [];\n  }\n};\n\n// Storage operations\nexport const uploadFile = async (\n  filePath: string,\n  file: File,\n  onProgress?: (progress: number) => void\n): Promise<string | null> => {\n  try {\n    const { data, error } = await supabase.storage\n      .from('documents')\n      .upload(filePath, file, {\n        cacheControl: '3600',\n        upsert: false\n      });\n\n    if (error) {\n      console.error('Error uploading file:', error);\n      return null;\n    }\n\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage\n      .from('documents')\n      .getPublicUrl(data.path);\n\n    return publicUrl;\n  } catch (error) {\n    console.error('Error in uploadFile:', error);\n    return null;\n  }\n};\n\nexport const getFileUrl = (filePath: string): string => {\n  const { data: { publicUrl } } = supabase.storage\n    .from('documents')\n    .getPublicUrl(filePath);\n\n  return publicUrl;\n};\n\n// Chat Session types\nexport interface ChatMessage {\n  id: string;\n  content: string;\n  document_answer?: string;\n  website_answer?: string;\n  llm_model?: string;\n  sender: 'user' | 'ai';\n  loading?: boolean;\n  sources?: Array<any>;\n  document_sources?: Array<any>;\n  website_sources?: Array<any>;\n  timestamp?: string;\n  chatId?: string;\n  llm_fallback?: boolean;\n}\n\nexport interface ChatSession {\n  id: string;\n  user_id?: string;\n  title: string;\n  messages: ChatMessage[];\n  model_used: string;\n  created_at: string;\n  updated_at: string;\n  tags?: string[];\n  has_document: boolean;\n  has_website: boolean;\n}\n\n// Chat Session operations\nexport const createChatSession = async (title: string = 'New Chat', modelUsed: string = 'gemini-2.0-flash'): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .insert([{\n        title,\n        messages: [],\n        model_used: modelUsed,\n        has_document: false,\n        has_website: false\n      }])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error creating chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in createChatSession:', error);\n    return null;\n  }\n};\n\nexport const getChatSessions = async (userId?: string): Promise<ChatSession[]> => {\n  try {\n    console.log('Attempting to fetch chat sessions...');\n\n    // First, try to check if the table exists with a simple query\n    const { data: tableCheck, error: tableError } = await supabase\n      .from('chat_sessions')\n      .select('id')\n      .limit(1);\n\n    if (tableError) {\n      console.warn('Chat sessions table not accessible:', tableError.message);\n      console.log('Returning empty chat sessions array');\n      return [];\n    }\n\n    // If table exists, try the full query with basic columns only\n    let query = supabase\n      .from('chat_sessions')\n      .select('id, title, created_at, updated_at')\n      .order('updated_at', { ascending: false })\n      .limit(10); // Reduced limit\n\n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n\n    const { data, error } = await query;\n\n    if (error) {\n      console.warn('Chat sessions query failed:', error.message);\n      return [];\n    }\n\n    // Process and return data with safe defaults\n    const sessions = (data || []).map(session => ({\n      id: session.id,\n      title: session.title || 'Untitled Chat',\n      created_at: session.created_at,\n      updated_at: session.updated_at,\n      model_used: 'gemini-2.0-flash',\n      has_document: false,\n      has_website: false,\n      messages: []\n    }));\n\n    console.log(`Successfully loaded ${sessions.length} chat sessions`);\n    return sessions;\n\n  } catch (error: any) {\n    console.warn('Error in getChatSessions:', error.message || error);\n    return [];\n  }\n};\n\nexport const getChatSessionById = async (id: string): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in getChatSessionById:', error);\n    return null;\n  }\n};\n\nexport const updateChatSession = async (id: string, updates: Partial<ChatSession>): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error updating chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in updateChatSession:', error);\n    return null;\n  }\n};\n\nexport const updateChatTitle = async (id: string, title: string): Promise<boolean> => {\n  try {\n    const { error } = await supabase\n      .from('chat_sessions')\n      .update({ title })\n      .eq('id', id);\n\n    if (error) {\n      console.error('Error updating chat title:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in updateChatTitle:', error);\n    return false;\n  }\n};\n\nexport const deleteChatSession = async (id: string): Promise<boolean> => {\n  try {\n    const { error } = await supabase\n      .from('chat_sessions')\n      .delete()\n      .eq('id', id);\n\n    if (error) {\n      console.error('Error deleting chat session:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in deleteChatSession:', error);\n    return false;\n  }\n};\n\nexport const saveChatMessages = async (chatId: string, messages: ChatMessage[]): Promise<boolean> => {\n  try {\n    // Ensure all message fields are preserved when saving to Supabase\n    const sanitizedMessages = messages.map(msg => ({\n      id: msg.id,\n      content: msg.content,\n      document_answer: msg.document_answer || undefined,\n      website_answer: msg.website_answer || undefined,\n      llm_model: msg.llm_model,\n      sender: msg.sender,\n      loading: msg.loading,\n      sources: msg.sources || undefined,\n      document_sources: msg.document_sources || undefined,\n      website_sources: msg.website_sources || undefined,\n      timestamp: msg.timestamp,\n      chatId: msg.chatId,\n      llm_fallback: msg.llm_fallback\n    }));\n\n    // Check if chat has documents or websites in the messages\n    const hasDocument = sanitizedMessages.some(msg => \n      msg.document_answer || \n      (msg.document_sources && msg.document_sources.length > 0) ||\n      (msg.sources && msg.sources.some((s: any) => s.source_type === 'document'))\n    );\n    \n    const hasWebsite = sanitizedMessages.some(msg => \n      msg.website_answer || \n      (msg.website_sources && msg.website_sources.length > 0) ||\n      (msg.sources && msg.sources.some((s: any) => s.source_type === 'website'))\n    );\n\n    console.log('Saving chat messages:', {\n      messageCount: sanitizedMessages.length,\n      hasDocument,\n      hasWebsite,\n      messagesWithDocAnswer: sanitizedMessages.filter(m => m.document_answer).length,\n      messagesWithWebAnswer: sanitizedMessages.filter(m => m.website_answer).length\n    });\n\n    const { error } = await supabase\n      .from('chat_sessions')\n      .update({ \n        messages: sanitizedMessages,\n        has_document: hasDocument,\n        has_website: hasWebsite\n      })\n      .eq('id', chatId);\n\n    if (error) {\n      console.error('Error saving chat messages:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in saveChatMessages:', error);\n    return false;\n  }\n};\n\nexport const clearAllChatSessions = async (userId?: string): Promise<boolean> => {\n  try {\n    let query = supabase.from('chat_sessions').delete();\n    \n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n\n    const { error } = await query;\n\n    if (error) {\n      console.error('Error clearing chat sessions:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in clearAllChatSessions:', error);\n    return false;\n  }\n};\n\nexport default supabase;\n", "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackModal.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\PDFViewer.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\alert.tsx", [], "C:\\IR App\\frontend\\src\\contexts\\ChatContext.tsx", [], "C:\\IR App\\frontend\\src\\components\\chat\\ChatSidebar.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentCategoryEditor.tsx", [], "C:\\IR App\\frontend\\src\\services\\categoryApi.ts", [], "C:\\IR App\\frontend\\src\\components\\documents\\CategoryManagement.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\TrainLoader.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\VisualContent.tsx", ["146", "147", "148", "149", "150", "151", "152", "153", "154", "155"], "import React, { useState, useEffect } from 'react';\n\ninterface VisualContentProps {\n  source: {\n    content_type?: string;\n    visual_content?: Record<string, any>;\n    storage_url?: string;\n    display_type?: string;\n    filename?: string;\n    page?: number;\n  };\n}\n\nconst VisualContent: React.FC<VisualContentProps> = ({ source }) => {\n  const [selectedTab, setSelectedTab] = useState<string>('content');\n  const [imageLoading, setImageLoading] = useState<boolean>(true);\n  const [imageError, setImageError] = useState<boolean>(false);\n  const [imageTries, setImageTries] = useState<number>(0);\n  const [cacheBuster, setCacheBuster] = useState<string>(`cb-${Date.now()}`);\n  const [visibleDebug, setVisibleDebug] = useState<boolean>(false);\n  \n  // Refresh image on source change\n  useEffect(() => {\n    // Reset state when source changes\n    setImageLoading(true);\n    setImageError(false);\n    setImageTries(0);\n    setCacheBuster(`cb-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`);\n    \n    console.log('🔄 Visual content source changed:', {\n      content_type: source.content_type,\n      filename: source.filename,\n      page: source.page,\n      has_visual_content: !!source.visual_content,\n      storage_url: source.storage_url ? `${source.storage_url.substring(0, 30)}...` : 'None',\n    });\n  }, [source.filename, source.page, source.storage_url]);\n\n  if (!source.content_type || source.content_type === 'text') {\n    return null; // No visual content to display\n  }\n\n  const renderTableContent = () => {\n    const visualContent = source.visual_content;\n    \n    if (!visualContent) return null;\n\n    // If we have table data, render it as a proper table\n    if (visualContent.table_data && Array.isArray(visualContent.table_data)) {\n      const tableData = visualContent.table_data;\n      if (tableData.length === 0) return null;\n\n      const headers = tableData[0] || [];\n      const rows = tableData.slice(1);\n\n      return (\n        <div className=\"overflow-x-auto w-full\">\n          <table className=\"min-w-full bg-white border border-gray-200 rounded-lg table-fixed\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                {headers.map((header: any, idx: number) => (\n                  <th \n                    key={idx}\n                    className=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\"\n                    style={{ minWidth: '100px', maxWidth: '200px' }}\n                  >\n                    {String(header || '').trim() || `Column ${idx + 1}`}\n                  </th>\n                ))}\n              </tr>\n            </thead>\n            <tbody>\n              {rows.map((row: any[], rowIdx: number) => (\n                <tr key={rowIdx} className=\"hover:bg-gray-50 transition-colors\">\n                  {headers.map((_: any, cellIdx: number) => (\n                    <td \n                      key={cellIdx}\n                      className=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100 truncate\"\n                      title={String(row[cellIdx] || '')}\n                    >\n                      {String(row[cellIdx] || '').trim() || '-'}\n                    </td>\n                  ))}\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      );\n    }\n    \n    // If we have markdown table, render it as HTML\n    if (visualContent.markdown_table) {\n      return (\n        <div className=\"overflow-x-auto w-full\">\n          <div className=\"bg-white rounded-lg border border-gray-200\">\n            <div \n              className=\"prose prose-sm max-w-none p-0\"\n              dangerouslySetInnerHTML={{\n                __html: markdownTableToHtml(visualContent.markdown_table)\n              }}\n            />\n          </div>\n        </div>\n      );\n    }\n\n    // Fallback: Try to display as text table if available\n    if (visualContent.text_table) {\n      return (\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n          <pre className=\"text-sm text-gray-700 whitespace-pre-wrap font-mono\">\n            {visualContent.text_table}\n          </pre>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"bg-gray-50 rounded-lg p-4 text-sm text-gray-600\">\n        Table data is not available in a displayable format.\n        {visualContent && (\n          <div className=\"mt-2 text-xs\">\n            Available data: {Object.keys(visualContent).join(', ')}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  const getImageSources = () => {\n    const visualContent = source.visual_content;\n    const sources = [];\n    \n    // 1. Try storage URL if available (with cache buster)\n    if (source.storage_url) {\n      const hasQueryParams = source.storage_url.includes('?');\n      const cacheParam = hasQueryParams ? `&_cb=${cacheBuster}` : `?_cb=${cacheBuster}`;\n      sources.push({\n        url: `${source.storage_url}${cacheParam}`,\n        type: 'Storage URL'\n      });\n    }\n    \n    // 2. Try base64 data if available\n    if (visualContent?.base64_data) {\n      sources.push({\n        url: `data:image/png;base64,${visualContent.base64_data}`,\n        type: 'Base64 Data'\n      });\n    }\n    \n    // 3. Generate path based on filename and page\n    if (source.filename && source.page !== undefined) {\n      const safeName = source.filename.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();\n      sources.push({\n        url: `/images/${safeName}_page${source.page}.png?_cb=${cacheBuster}`,\n        type: 'Generated Path'\n      });\n    }\n    \n    // 4. Logo as absolute fallback\n    sources.push({\n      url: `/logo512.png?_cb=${cacheBuster}`,\n      type: 'Default Logo'\n    });\n    \n    return sources;\n  };\n\n  const renderImageContent = () => {\n    const visualContent = source.visual_content;\n    \n    console.log('DEBUG: Rendering image for', source.filename, 'page', source.page);\n    console.log('DEBUG: storage_url =', source.storage_url);\n    console.log('DEBUG: base64_data available =', !!visualContent?.base64_data);\n    \n    // Show debug info with image\n    const debugInfo = (\n      <div className=\"bg-blue-50 p-2 mb-2 rounded text-xs\">\n        <p><strong>File:</strong> {source.filename || 'Unknown'}</p>\n        <p><strong>Page:</strong> {source.page || 'Unknown'}</p>\n        <p><strong>URL:</strong> {source.storage_url ? 'Available' : 'Not available'}</p>\n        <p><strong>Time:</strong> {new Date().toISOString()}</p>\n      </div>\n    );\n\n    // Try to find actual image matching Project 1, Project 2, etc. in query response\n    if (source.content_type === 'image' && source.filename && source.filename.includes('Project')) {\n      return (\n        <div className=\"relative\">\n          {debugInfo}\n          {imageLoading && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            </div>\n          )}\n          <img\n            src={source.storage_url || `/logo512.png?t=${Date.now()}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className=\"max-w-full h-auto rounded-lg border border-gray-200 shadow-md\"\n            onLoad={() => {\n              console.log('DEBUG: Image loaded successfully');\n              setImageLoading(false);\n            }}\n            onError={(e) => {\n              console.error('DEBUG: Image failed to load:', e);\n              setImageLoading(false);\n              setImageError(true);\n            }}\n          />\n          {imageError && (\n            <div className=\"bg-red-50 rounded-lg p-4 text-center text-gray-600 mt-2\">\n              <p>Image could not be loaded</p>\n              <p className=\"text-xs mt-1\">Please check the document and try again</p>\n              <button \n                onClick={() => {\n                  console.log('DEBUG: Retrying image load');\n                  setImageLoading(true);\n                  setImageError(false);\n                }}\n                className=\"mt-2 px-2 py-1 bg-blue-100 rounded text-sm\"\n              >\n                Retry\n              </button>\n            </div>\n          )}\n        </div>\n      );\n    }\n    \n    // Try storage URL first\n    if (source.storage_url) {\n      return (\n        <div className=\"relative\">\n          {debugInfo}\n          {imageLoading && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            </div>\n          )}\n          <img\n            src={`${source.storage_url}?t=${Date.now()}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className={`max-w-full h-auto rounded-lg border border-gray-200 ${imageLoading ? 'opacity-0' : 'opacity-100'}`}\n            onLoad={() => setImageLoading(false)}\n            onError={() => {\n              console.error('DEBUG: Storage URL image failed to load:', source.storage_url);\n              setImageLoading(false);\n              setImageError(true);\n            }}\n          />\n          {imageError && (\n            <div className=\"bg-gray-100 rounded-lg p-4 text-center text-gray-600\">\n              <p>Image could not be loaded</p>\n              <p className=\"text-xs mt-1\">URL: {source.storage_url?.substring(0, 30)}...</p>\n              <button \n                onClick={() => {\n                  console.log('DEBUG: Retrying image load');\n                  setImageLoading(true);\n                  setImageError(false);\n                }}\n                className=\"mt-2 px-2 py-1 bg-blue-100 rounded text-sm\"\n              >\n                Retry\n              </button>\n            </div>\n          )}\n        </div>\n      );\n    }\n\n    // Try base64 data if available\n    if (visualContent?.base64_data) {\n      return (\n        <div>\n          {debugInfo}\n          <img\n            src={`data:image/png;base64,${visualContent.base64_data}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className=\"max-w-full h-auto rounded-lg border border-gray-200\"\n            onError={() => {\n              console.error('DEBUG: Base64 image failed to load');\n              setImageError(true);\n            }}\n          />\n        </div>\n      );\n    }\n\n    // Default to logo if no other image is available\n    return (\n      <div className=\"bg-gray-50 rounded-lg p-4 text-center\">\n        {debugInfo}\n        <p className=\"text-sm text-gray-600 mb-2\">No image content available from the document</p>\n        <img \n          src={`/logo512.png?t=${Date.now()}`}\n          alt=\"Default logo\"\n          className=\"max-w-full h-auto rounded-lg border border-gray-200 mx-auto\"\n          style={{ maxHeight: '200px' }}\n        />\n      </div>\n    );\n  };\n\n  const renderChartContent = () => {\n    const visualContent = source.visual_content;\n    \n    if (!visualContent) return null;\n\n    return (\n      <div className=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200\">\n        <h4 className=\"font-semibold text-purple-800 mb-3 flex items-center\">\n          📊 Chart/Diagram Detected\n        </h4>\n        <div className=\"space-y-3\">\n          {visualContent.description && (\n            <p className=\"text-sm text-gray-700\">{visualContent.description}</p>\n          )}\n          <div className=\"grid grid-cols-2 gap-4 text-xs text-gray-600\">\n            {visualContent.drawing_count && (\n              <div>\n                <span className=\"font-medium\">Drawing Elements:</span> {visualContent.drawing_count}\n              </div>\n            )}\n            {visualContent.confidence && (\n              <div>\n                <span className=\"font-medium\">Confidence:</span> {visualContent.confidence}\n              </div>\n            )}\n            {visualContent.has_lines && (\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2\"></span>\n                Contains lines\n              </div>\n            )}\n            {visualContent.has_curves && (\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-blue-400 rounded-full mr-2\"></span>\n                Contains curves\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const getContentTypeLabel = () => {\n    switch (source.content_type) {\n      case 'table': return '📋 Table';\n      case 'image': return '🖼️ Image';\n      case 'chart_diagram': return '📊 Chart/Diagram';\n      default: return '📄 Content';\n    }\n  };\n\n  const getContentTypeColor = () => {\n    switch (source.content_type) {\n      case 'table': return 'from-green-50 to-emerald-50 border-green-200';\n      case 'image': return 'from-blue-50 to-cyan-50 border-blue-200';\n      case 'chart_diagram': return 'from-purple-50 to-indigo-50 border-purple-200';\n      default: return 'from-gray-50 to-slate-50 border-gray-200';\n    }\n  };\n\n  return (\n    <div className={`bg-gradient-to-br ${getContentTypeColor()} rounded-lg border p-4 mt-3`}>\n      <div className=\"flex items-center justify-between mb-3\">\n        <h4 className=\"font-semibold text-gray-800 text-sm flex items-center\">\n          {getContentTypeLabel()}\n          {source.page && (\n            <span className=\"ml-2 text-xs bg-white bg-opacity-70 px-2 py-1 rounded\">\n              Page {source.page}\n            </span>\n          )}\n        </h4>\n        \n        {/* Tab selector for complex content */}\n        {source.content_type === 'table' && source.visual_content?.metadata && (\n          <div className=\"flex text-xs bg-white bg-opacity-50 rounded\">\n            <button\n              onClick={() => setSelectedTab('content')}\n              className={`px-2 py-1 rounded-l ${\n                selectedTab === 'content' ? 'bg-white text-gray-800' : 'text-gray-600'\n              }`}\n            >\n              Table\n            </button>\n            <button\n              onClick={() => setSelectedTab('metadata')}\n              className={`px-2 py-1 rounded-r ${\n                selectedTab === 'metadata' ? 'bg-white text-gray-800' : 'text-gray-600'\n              }`}\n            >\n              Info\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Content display */}\n      {selectedTab === 'content' && (\n        <div>\n          {source.content_type === 'table' && renderTableContent()}\n          {source.content_type === 'image' && renderImageContent()}\n          {source.content_type === 'chart_diagram' && renderChartContent()}\n        </div>\n      )}\n\n      {/* Metadata tab */}\n      {selectedTab === 'metadata' && source.visual_content && (\n        <div className=\"bg-white bg-opacity-50 rounded p-3 text-xs text-gray-600\">\n          <div className=\"grid grid-cols-2 gap-2\">\n            <div><span className=\"font-medium\">Extraction Method:</span> {source.visual_content.extraction_method}</div>\n            <div><span className=\"font-medium\">Page:</span> {source.page}</div>\n            {source.visual_content.table_index !== undefined && (\n              <div><span className=\"font-medium\">Table Index:</span> {source.visual_content.table_index}</div>\n            )}\n            {source.visual_content.image_index !== undefined && (\n              <div><span className=\"font-medium\">Image Index:</span> {source.visual_content.image_index}</div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Helper function to convert markdown table to HTML\nconst markdownTableToHtml = (markdown: string): string => {\n  if (!markdown || typeof markdown !== 'string') {\n    return `<div class=\"p-4 text-gray-600\">Invalid table data</div>`;\n  }\n  \n  const lines = markdown.split('\\n').filter(line => line.trim());\n  if (lines.length < 2) return `<div class=\"p-4 text-gray-600\">Invalid table format</div>`;\n\n  let html = '<table class=\"min-w-full border border-gray-200 rounded-lg\">';\n  \n  try {\n    // Process header\n    const headerLine = lines[0];\n    const headerCells = headerLine.split('|')\n      .map(cell => cell.trim())\n      .filter(cell => cell !== ''); // Remove empty cells from start/end\n    \n    if (headerCells.length === 0) {\n      // Try alternate format where cells are separated by multiple spaces\n      const spaceSeparatedCells = headerLine.split(/\\s{2,}/).filter(cell => cell.trim());\n      if (spaceSeparatedCells.length > 0) {\n        html += '<thead class=\"bg-gray-50\"><tr>';\n        spaceSeparatedCells.forEach((cell, index) => {\n          const cleanCell = cell.replace(/\\*\\*/g, '').trim();\n          html += `<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell || `Column ${index + 1}`}</th>`;\n        });\n        html += '</tr></thead>';\n        \n        // Process data rows (skip separator row)\n        html += '<tbody>';\n        for (let i = 2; i < lines.length; i++) {\n          const rowCells = lines[i].split(/\\s{2,}/).filter(cell => cell.trim());\n          if (rowCells.length === 0) continue;\n          \n          html += '<tr class=\"hover:bg-gray-50 transition-colors\">';\n          // Make sure we have the right number of cells\n          while (rowCells.length < spaceSeparatedCells.length) {\n            rowCells.push('');\n          }\n          \n          spaceSeparatedCells.forEach((_, index) => {\n            const cellContent = rowCells[index] || '';\n            html += `<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cellContent.trim() || '-'}</td>`;\n          });\n          html += '</tr>';\n        }\n        html += '</tbody></table>';\n        return html;\n      }\n      \n      return `<div class=\"p-4 text-gray-600\">No table headers found</div>`;\n    }\n    \n    html += '<thead class=\"bg-gray-50\"><tr>';\n    headerCells.forEach((cell, index) => {\n      const cleanCell = cell.replace(/\\*\\*/g, '').trim(); // Remove markdown bold\n      html += `<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell || `Column ${index + 1}`}</th>`;\n    });\n    html += '</tr></thead>';\n\n    // Check if we have a separator row at index 1 (standard markdown table)\n    const hasSeparator = lines.length > 1 && lines[1].includes('|') && lines[1].includes('-');\n    const startRowIndex = hasSeparator ? 2 : 1;\n\n    // Process data rows\n    html += '<tbody>';\n    for (let i = startRowIndex; i < lines.length; i++) {\n      const rowLine = lines[i];\n      if (!rowLine.includes('|')) continue; // Skip non-table lines\n      \n      const cells = rowLine.split('|')\n        .map(cell => cell.trim())\n        .filter((cell, index, array) => {\n          // Keep all cells except first and last if they're empty (markdown format)\n          if (index === 0 || index === array.length - 1) {\n            return cell !== '';\n          }\n          return true;\n        });\n      \n      // Ensure we have the right number of cells\n      while (cells.length < headerCells.length) {\n        cells.push('');\n      }\n      \n      html += '<tr class=\"hover:bg-gray-50 transition-colors\">';\n      headerCells.forEach((_, cellIndex) => {\n        const cellContent = cells[cellIndex] || '';\n        const cleanCell = cellContent.replace(/\\*\\*/g, '').trim(); // Remove markdown bold\n        html += `<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cleanCell || '-'}</td>`;\n      });\n      html += '</tr>';\n    }\n    html += '</tbody></table>';\n  } catch (error) {\n    console.error('Error parsing markdown table:', error);\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n    return `<div class=\"p-4 text-gray-600 bg-red-50 border border-red-200 rounded\">Error parsing table: ${errorMessage}</div>`;\n  }\n\n  return html;\n};\n\nexport default VisualContent; ", "C:\\IR App\\frontend\\src\\components\\documents\\BulkCategoryEditor.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteCategoryManagement.tsx", [], "C:\\IR App\\frontend\\src\\components\\categories\\CategoryCreator.tsx", [], {"ruleId": "156", "replacedBy": "157"}, {"ruleId": "158", "replacedBy": "159"}, {"ruleId": "160", "severity": 1, "message": "161", "line": 4, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 4, "endColumn": 26}, {"ruleId": "160", "severity": 1, "message": "164", "line": 321, "column": 19, "nodeType": "162", "messageId": "163", "endLine": 321, "endColumn": 29}, {"ruleId": "160", "severity": 1, "message": "165", "line": 18, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 18, "endColumn": 20}, {"ruleId": "160", "severity": 1, "message": "166", "line": 20, "column": 10, "nodeType": "162", "messageId": "163", "endLine": 20, "endColumn": 22}, {"ruleId": "160", "severity": 1, "message": "167", "line": 20, "column": 24, "nodeType": "162", "messageId": "163", "endLine": 20, "endColumn": 39}, {"ruleId": "168", "severity": 1, "message": "169", "line": 37, "column": 6, "nodeType": "170", "endLine": 37, "endColumn": 56, "suggestions": "171"}, {"ruleId": "160", "severity": 1, "message": "172", "line": 131, "column": 9, "nodeType": "162", "messageId": "163", "endLine": 131, "endColumn": 24}, {"ruleId": "173", "severity": 1, "message": "174", "line": 198, "column": 11, "nodeType": "175", "endLine": 211, "endColumn": 13}, {"ruleId": "173", "severity": 1, "message": "174", "line": 242, "column": 11, "nodeType": "175", "endLine": 252, "endColumn": 13}, {"ruleId": "173", "severity": 1, "message": "174", "line": 278, "column": 11, "nodeType": "175", "endLine": 286, "endColumn": 13}, {"ruleId": "176", "severity": 1, "message": "177", "line": 471, "column": 39, "nodeType": "178", "messageId": "179", "endLine": 474, "endColumn": 12}, {"ruleId": "176", "severity": 1, "message": "177", "line": 517, "column": 27, "nodeType": "178", "messageId": "179", "endLine": 521, "endColumn": 8}, "no-native-reassign", ["180"], "no-negated-in-lhs", ["181"], "@typescript-eslint/no-unused-vars", "'DocumentCategory' is defined but never used.", "Identifier", "unusedVar", "'tableCheck' is assigned a value but never used.", "'imageTries' is assigned a value but never used.", "'visibleDebug' is assigned a value but never used.", "'setVisibleDebug' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'source.content_type' and 'source.visual_content'. Either include them or remove the dependency array.", "ArrayExpression", ["182"], "'getImageSources' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'html'.", "ArrowFunctionExpression", "unsafeRefs", "no-global-assign", "no-unsafe-negation", {"desc": "183", "fix": "184"}, "Update the dependencies array to be: [source.content_type, source.filename, source.page, source.storage_url, source.visual_content]", {"range": "185", "text": "186"}, [1319, 1369], "[source.content_type, source.filename, source.page, source.storage_url, source.visual_content]"]