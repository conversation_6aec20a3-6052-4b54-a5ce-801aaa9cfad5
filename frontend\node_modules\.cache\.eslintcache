[{"C:\\IR App\\frontend\\src\\index.tsx": "1", "C:\\IR App\\frontend\\src\\reportWebVitals.ts": "2", "C:\\IR App\\frontend\\src\\AppRouter.tsx": "3", "C:\\IR App\\frontend\\src\\App.tsx": "4", "C:\\IR App\\frontend\\src\\pages\\DocumentViewer.tsx": "5", "C:\\IR App\\frontend\\src\\pages\\documents\\DocumentsPage.tsx": "6", "C:\\IR App\\frontend\\src\\pages\\websites\\WebsitesPage.tsx": "7", "C:\\IR App\\frontend\\src\\components\\layout\\Header.tsx": "8", "C:\\IR App\\frontend\\src\\pages\\settings\\SettingsPage.tsx": "9", "C:\\IR App\\frontend\\src\\services\\api.ts": "10", "C:\\IR App\\frontend\\src\\components\\ui\\LLMSelector.tsx": "11", "C:\\IR App\\frontend\\src\\components\\ui\\InteractiveAnswer.tsx": "12", "C:\\IR App\\frontend\\src\\components\\documents\\SimplePDFViewer.tsx": "13", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentViewModal.tsx": "14", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentUploadForm.tsx": "15", "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteExtractForm.tsx": "16", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentsTable.tsx": "17", "C:\\IR App\\frontend\\src\\components\\websites\\WebsitesTable.tsx": "18", "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteViewModal.tsx": "19", "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackEmailConfig.tsx": "20", "C:\\IR App\\frontend\\src\\services\\supabase.ts": "21", "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackModal.tsx": "22", "C:\\IR App\\frontend\\src\\components\\documents\\PDFViewer.tsx": "23", "C:\\IR App\\frontend\\src\\components\\ui\\alert.tsx": "24", "C:\\IR App\\frontend\\src\\contexts\\ChatContext.tsx": "25", "C:\\IR App\\frontend\\src\\components\\chat\\ChatSidebar.tsx": "26", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentCategoryEditor.tsx": "27", "C:\\IR App\\frontend\\src\\services\\categoryApi.ts": "28", "C:\\IR App\\frontend\\src\\components\\documents\\CategoryManagement.tsx": "29", "C:\\IR App\\frontend\\src\\components\\ui\\TrainLoader.tsx": "30", "C:\\IR App\\frontend\\src\\components\\ui\\VisualContent.tsx": "31", "C:\\IR App\\frontend\\src\\components\\documents\\BulkCategoryEditor.tsx": "32", "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteCategoryManagement.tsx": "33", "C:\\IR App\\frontend\\src\\components\\categories\\CategoryCreator.tsx": "34"}, {"size": 518, "mtime": 1746886123631, "results": "35", "hashOfConfig": "36"}, {"size": 425, "mtime": 1746757926945, "results": "37", "hashOfConfig": "36"}, {"size": 1618, "mtime": 1748503578318, "results": "38", "hashOfConfig": "36"}, {"size": 48607, "mtime": 1748492290712, "results": "39", "hashOfConfig": "36"}, {"size": 10762, "mtime": 1748095932232, "results": "40", "hashOfConfig": "36"}, {"size": 14596, "mtime": 1748509097074, "results": "41", "hashOfConfig": "36"}, {"size": 13348, "mtime": 1748500978274, "results": "42", "hashOfConfig": "36"}, {"size": 2799, "mtime": 1748503610160, "results": "43", "hashOfConfig": "36"}, {"size": 18739, "mtime": 1748489500192, "results": "44", "hashOfConfig": "36"}, {"size": 34987, "mtime": 1748500677580, "results": "45", "hashOfConfig": "36"}, {"size": 8620, "mtime": 1748335238627, "results": "46", "hashOfConfig": "36"}, {"size": 5431, "mtime": 1748489621678, "results": "47", "hashOfConfig": "36"}, {"size": 4449, "mtime": 1748082417413, "results": "48", "hashOfConfig": "36"}, {"size": 9718, "mtime": 1748489558185, "results": "49", "hashOfConfig": "36"}, {"size": 27220, "mtime": 1748509195960, "results": "50", "hashOfConfig": "36"}, {"size": 20569, "mtime": 1748334590793, "results": "51", "hashOfConfig": "36"}, {"size": 28956, "mtime": 1748500828222, "results": "52", "hashOfConfig": "36"}, {"size": 10429, "mtime": 1748502803836, "results": "53", "hashOfConfig": "36"}, {"size": 8113, "mtime": 1746848396668, "results": "54", "hashOfConfig": "36"}, {"size": 5634, "mtime": 1748333597589, "results": "55", "hashOfConfig": "36"}, {"size": 13445, "mtime": 1748509610560, "results": "56", "hashOfConfig": "36"}, {"size": 5354, "mtime": 1748489621678, "results": "57", "hashOfConfig": "36"}, {"size": 6103, "mtime": 1748094001907, "results": "58", "hashOfConfig": "36"}, {"size": 1514, "mtime": 1748265464172, "results": "59", "hashOfConfig": "36"}, {"size": 5260, "mtime": 1748339426726, "results": "60", "hashOfConfig": "36"}, {"size": 14511, "mtime": 1748509631459, "results": "61", "hashOfConfig": "36"}, {"size": 12758, "mtime": 1748265464075, "results": "62", "hashOfConfig": "36"}, {"size": 8641, "mtime": 1748240154522, "results": "63", "hashOfConfig": "36"}, {"size": 14307, "mtime": 1748265464038, "results": "64", "hashOfConfig": "36"}, {"size": 6360, "mtime": 1748339169110, "results": "65", "hashOfConfig": "36"}, {"size": 19905, "mtime": 1748369722086, "results": "66", "hashOfConfig": "36"}, {"size": 9886, "mtime": 1748500881530, "results": "67", "hashOfConfig": "36"}, {"size": 9157, "mtime": 1748502759713, "results": "68", "hashOfConfig": "36"}, {"size": 10859, "mtime": 1748510201502, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, "ngactd", {"filePath": "73", "messages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "75", "messages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "77", "messages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "79", "messages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "81", "messages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "83", "messages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "85", "messages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "87", "messages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "89", "messages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "91", "messages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "93", "messages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "95", "messages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "97", "messages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "99", "messages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "101", "messages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "103", "messages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "105", "messages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "107", "messages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "109", "messages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "111", "messages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "115", "messages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "117", "messages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "119", "messages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "121", "messages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "125", "messages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "127", "messages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "129", "messages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "131", "messages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "133", "usedDeprecatedRules": "72"}, {"filePath": "134", "messages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "136", "messages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "72"}, {"filePath": "138", "messages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\IR App\\frontend\\src\\index.tsx", [], ["140", "141"], "C:\\IR App\\frontend\\src\\reportWebVitals.ts", [], "C:\\IR App\\frontend\\src\\AppRouter.tsx", [], "C:\\IR App\\frontend\\src\\App.tsx", [], "C:\\IR App\\frontend\\src\\pages\\DocumentViewer.tsx", [], "C:\\IR App\\frontend\\src\\pages\\documents\\DocumentsPage.tsx", [], "C:\\IR App\\frontend\\src\\pages\\websites\\WebsitesPage.tsx", [], "C:\\IR App\\frontend\\src\\components\\layout\\Header.tsx", [], "C:\\IR App\\frontend\\src\\pages\\settings\\SettingsPage.tsx", [], "C:\\IR App\\frontend\\src\\services\\api.ts", [], "C:\\IR App\\frontend\\src\\components\\ui\\LLMSelector.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\InteractiveAnswer.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\SimplePDFViewer.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentViewModal.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentUploadForm.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteExtractForm.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentsTable.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsitesTable.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteViewModal.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackEmailConfig.tsx", [], "C:\\IR App\\frontend\\src\\services\\supabase.ts", [], "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackModal.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\PDFViewer.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\alert.tsx", [], "C:\\IR App\\frontend\\src\\contexts\\ChatContext.tsx", [], "C:\\IR App\\frontend\\src\\components\\chat\\ChatSidebar.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentCategoryEditor.tsx", [], "C:\\IR App\\frontend\\src\\services\\categoryApi.ts", [], "C:\\IR App\\frontend\\src\\components\\documents\\CategoryManagement.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\TrainLoader.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\VisualContent.tsx", ["142", "143", "144", "145", "146", "147", "148", "149", "150", "151"], "import React, { useState, useEffect } from 'react';\n\ninterface VisualContentProps {\n  source: {\n    content_type?: string;\n    visual_content?: Record<string, any>;\n    storage_url?: string;\n    display_type?: string;\n    filename?: string;\n    page?: number;\n  };\n}\n\nconst VisualContent: React.FC<VisualContentProps> = ({ source }) => {\n  const [selectedTab, setSelectedTab] = useState<string>('content');\n  const [imageLoading, setImageLoading] = useState<boolean>(true);\n  const [imageError, setImageError] = useState<boolean>(false);\n  const [imageTries, setImageTries] = useState<number>(0);\n  const [cacheBuster, setCacheBuster] = useState<string>(`cb-${Date.now()}`);\n  const [visibleDebug, setVisibleDebug] = useState<boolean>(false);\n  \n  // Refresh image on source change\n  useEffect(() => {\n    // Reset state when source changes\n    setImageLoading(true);\n    setImageError(false);\n    setImageTries(0);\n    setCacheBuster(`cb-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`);\n    \n    console.log('🔄 Visual content source changed:', {\n      content_type: source.content_type,\n      filename: source.filename,\n      page: source.page,\n      has_visual_content: !!source.visual_content,\n      storage_url: source.storage_url ? `${source.storage_url.substring(0, 30)}...` : 'None',\n    });\n  }, [source.filename, source.page, source.storage_url]);\n\n  if (!source.content_type || source.content_type === 'text') {\n    return null; // No visual content to display\n  }\n\n  const renderTableContent = () => {\n    const visualContent = source.visual_content;\n    \n    if (!visualContent) return null;\n\n    // If we have table data, render it as a proper table\n    if (visualContent.table_data && Array.isArray(visualContent.table_data)) {\n      const tableData = visualContent.table_data;\n      if (tableData.length === 0) return null;\n\n      const headers = tableData[0] || [];\n      const rows = tableData.slice(1);\n\n      return (\n        <div className=\"overflow-x-auto w-full\">\n          <table className=\"min-w-full bg-white border border-gray-200 rounded-lg table-fixed\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                {headers.map((header: any, idx: number) => (\n                  <th \n                    key={idx}\n                    className=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\"\n                    style={{ minWidth: '100px', maxWidth: '200px' }}\n                  >\n                    {String(header || '').trim() || `Column ${idx + 1}`}\n                  </th>\n                ))}\n              </tr>\n            </thead>\n            <tbody>\n              {rows.map((row: any[], rowIdx: number) => (\n                <tr key={rowIdx} className=\"hover:bg-gray-50 transition-colors\">\n                  {headers.map((_: any, cellIdx: number) => (\n                    <td \n                      key={cellIdx}\n                      className=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100 truncate\"\n                      title={String(row[cellIdx] || '')}\n                    >\n                      {String(row[cellIdx] || '').trim() || '-'}\n                    </td>\n                  ))}\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      );\n    }\n    \n    // If we have markdown table, render it as HTML\n    if (visualContent.markdown_table) {\n      return (\n        <div className=\"overflow-x-auto w-full\">\n          <div className=\"bg-white rounded-lg border border-gray-200\">\n            <div \n              className=\"prose prose-sm max-w-none p-0\"\n              dangerouslySetInnerHTML={{\n                __html: markdownTableToHtml(visualContent.markdown_table)\n              }}\n            />\n          </div>\n        </div>\n      );\n    }\n\n    // Fallback: Try to display as text table if available\n    if (visualContent.text_table) {\n      return (\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n          <pre className=\"text-sm text-gray-700 whitespace-pre-wrap font-mono\">\n            {visualContent.text_table}\n          </pre>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"bg-gray-50 rounded-lg p-4 text-sm text-gray-600\">\n        Table data is not available in a displayable format.\n        {visualContent && (\n          <div className=\"mt-2 text-xs\">\n            Available data: {Object.keys(visualContent).join(', ')}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  const getImageSources = () => {\n    const visualContent = source.visual_content;\n    const sources = [];\n    \n    // 1. Try storage URL if available (with cache buster)\n    if (source.storage_url) {\n      const hasQueryParams = source.storage_url.includes('?');\n      const cacheParam = hasQueryParams ? `&_cb=${cacheBuster}` : `?_cb=${cacheBuster}`;\n      sources.push({\n        url: `${source.storage_url}${cacheParam}`,\n        type: 'Storage URL'\n      });\n    }\n    \n    // 2. Try base64 data if available\n    if (visualContent?.base64_data) {\n      sources.push({\n        url: `data:image/png;base64,${visualContent.base64_data}`,\n        type: 'Base64 Data'\n      });\n    }\n    \n    // 3. Generate path based on filename and page\n    if (source.filename && source.page !== undefined) {\n      const safeName = source.filename.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();\n      sources.push({\n        url: `/images/${safeName}_page${source.page}.png?_cb=${cacheBuster}`,\n        type: 'Generated Path'\n      });\n    }\n    \n    // 4. Logo as absolute fallback\n    sources.push({\n      url: `/logo512.png?_cb=${cacheBuster}`,\n      type: 'Default Logo'\n    });\n    \n    return sources;\n  };\n\n  const renderImageContent = () => {\n    const visualContent = source.visual_content;\n    \n    console.log('DEBUG: Rendering image for', source.filename, 'page', source.page);\n    console.log('DEBUG: storage_url =', source.storage_url);\n    console.log('DEBUG: base64_data available =', !!visualContent?.base64_data);\n    \n    // Show debug info with image\n    const debugInfo = (\n      <div className=\"bg-blue-50 p-2 mb-2 rounded text-xs\">\n        <p><strong>File:</strong> {source.filename || 'Unknown'}</p>\n        <p><strong>Page:</strong> {source.page || 'Unknown'}</p>\n        <p><strong>URL:</strong> {source.storage_url ? 'Available' : 'Not available'}</p>\n        <p><strong>Time:</strong> {new Date().toISOString()}</p>\n      </div>\n    );\n\n    // Try to find actual image matching Project 1, Project 2, etc. in query response\n    if (source.content_type === 'image' && source.filename && source.filename.includes('Project')) {\n      return (\n        <div className=\"relative\">\n          {debugInfo}\n          {imageLoading && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            </div>\n          )}\n          <img\n            src={source.storage_url || `/logo512.png?t=${Date.now()}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className=\"max-w-full h-auto rounded-lg border border-gray-200 shadow-md\"\n            onLoad={() => {\n              console.log('DEBUG: Image loaded successfully');\n              setImageLoading(false);\n            }}\n            onError={(e) => {\n              console.error('DEBUG: Image failed to load:', e);\n              setImageLoading(false);\n              setImageError(true);\n            }}\n          />\n          {imageError && (\n            <div className=\"bg-red-50 rounded-lg p-4 text-center text-gray-600 mt-2\">\n              <p>Image could not be loaded</p>\n              <p className=\"text-xs mt-1\">Please check the document and try again</p>\n              <button \n                onClick={() => {\n                  console.log('DEBUG: Retrying image load');\n                  setImageLoading(true);\n                  setImageError(false);\n                }}\n                className=\"mt-2 px-2 py-1 bg-blue-100 rounded text-sm\"\n              >\n                Retry\n              </button>\n            </div>\n          )}\n        </div>\n      );\n    }\n    \n    // Try storage URL first\n    if (source.storage_url) {\n      return (\n        <div className=\"relative\">\n          {debugInfo}\n          {imageLoading && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            </div>\n          )}\n          <img\n            src={`${source.storage_url}?t=${Date.now()}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className={`max-w-full h-auto rounded-lg border border-gray-200 ${imageLoading ? 'opacity-0' : 'opacity-100'}`}\n            onLoad={() => setImageLoading(false)}\n            onError={() => {\n              console.error('DEBUG: Storage URL image failed to load:', source.storage_url);\n              setImageLoading(false);\n              setImageError(true);\n            }}\n          />\n          {imageError && (\n            <div className=\"bg-gray-100 rounded-lg p-4 text-center text-gray-600\">\n              <p>Image could not be loaded</p>\n              <p className=\"text-xs mt-1\">URL: {source.storage_url?.substring(0, 30)}...</p>\n              <button \n                onClick={() => {\n                  console.log('DEBUG: Retrying image load');\n                  setImageLoading(true);\n                  setImageError(false);\n                }}\n                className=\"mt-2 px-2 py-1 bg-blue-100 rounded text-sm\"\n              >\n                Retry\n              </button>\n            </div>\n          )}\n        </div>\n      );\n    }\n\n    // Try base64 data if available\n    if (visualContent?.base64_data) {\n      return (\n        <div>\n          {debugInfo}\n          <img\n            src={`data:image/png;base64,${visualContent.base64_data}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className=\"max-w-full h-auto rounded-lg border border-gray-200\"\n            onError={() => {\n              console.error('DEBUG: Base64 image failed to load');\n              setImageError(true);\n            }}\n          />\n        </div>\n      );\n    }\n\n    // Default to logo if no other image is available\n    return (\n      <div className=\"bg-gray-50 rounded-lg p-4 text-center\">\n        {debugInfo}\n        <p className=\"text-sm text-gray-600 mb-2\">No image content available from the document</p>\n        <img \n          src={`/logo512.png?t=${Date.now()}`}\n          alt=\"Default logo\"\n          className=\"max-w-full h-auto rounded-lg border border-gray-200 mx-auto\"\n          style={{ maxHeight: '200px' }}\n        />\n      </div>\n    );\n  };\n\n  const renderChartContent = () => {\n    const visualContent = source.visual_content;\n    \n    if (!visualContent) return null;\n\n    return (\n      <div className=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200\">\n        <h4 className=\"font-semibold text-purple-800 mb-3 flex items-center\">\n          📊 Chart/Diagram Detected\n        </h4>\n        <div className=\"space-y-3\">\n          {visualContent.description && (\n            <p className=\"text-sm text-gray-700\">{visualContent.description}</p>\n          )}\n          <div className=\"grid grid-cols-2 gap-4 text-xs text-gray-600\">\n            {visualContent.drawing_count && (\n              <div>\n                <span className=\"font-medium\">Drawing Elements:</span> {visualContent.drawing_count}\n              </div>\n            )}\n            {visualContent.confidence && (\n              <div>\n                <span className=\"font-medium\">Confidence:</span> {visualContent.confidence}\n              </div>\n            )}\n            {visualContent.has_lines && (\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2\"></span>\n                Contains lines\n              </div>\n            )}\n            {visualContent.has_curves && (\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-blue-400 rounded-full mr-2\"></span>\n                Contains curves\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const getContentTypeLabel = () => {\n    switch (source.content_type) {\n      case 'table': return '📋 Table';\n      case 'image': return '🖼️ Image';\n      case 'chart_diagram': return '📊 Chart/Diagram';\n      default: return '📄 Content';\n    }\n  };\n\n  const getContentTypeColor = () => {\n    switch (source.content_type) {\n      case 'table': return 'from-green-50 to-emerald-50 border-green-200';\n      case 'image': return 'from-blue-50 to-cyan-50 border-blue-200';\n      case 'chart_diagram': return 'from-purple-50 to-indigo-50 border-purple-200';\n      default: return 'from-gray-50 to-slate-50 border-gray-200';\n    }\n  };\n\n  return (\n    <div className={`bg-gradient-to-br ${getContentTypeColor()} rounded-lg border p-4 mt-3`}>\n      <div className=\"flex items-center justify-between mb-3\">\n        <h4 className=\"font-semibold text-gray-800 text-sm flex items-center\">\n          {getContentTypeLabel()}\n          {source.page && (\n            <span className=\"ml-2 text-xs bg-white bg-opacity-70 px-2 py-1 rounded\">\n              Page {source.page}\n            </span>\n          )}\n        </h4>\n        \n        {/* Tab selector for complex content */}\n        {source.content_type === 'table' && source.visual_content?.metadata && (\n          <div className=\"flex text-xs bg-white bg-opacity-50 rounded\">\n            <button\n              onClick={() => setSelectedTab('content')}\n              className={`px-2 py-1 rounded-l ${\n                selectedTab === 'content' ? 'bg-white text-gray-800' : 'text-gray-600'\n              }`}\n            >\n              Table\n            </button>\n            <button\n              onClick={() => setSelectedTab('metadata')}\n              className={`px-2 py-1 rounded-r ${\n                selectedTab === 'metadata' ? 'bg-white text-gray-800' : 'text-gray-600'\n              }`}\n            >\n              Info\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Content display */}\n      {selectedTab === 'content' && (\n        <div>\n          {source.content_type === 'table' && renderTableContent()}\n          {source.content_type === 'image' && renderImageContent()}\n          {source.content_type === 'chart_diagram' && renderChartContent()}\n        </div>\n      )}\n\n      {/* Metadata tab */}\n      {selectedTab === 'metadata' && source.visual_content && (\n        <div className=\"bg-white bg-opacity-50 rounded p-3 text-xs text-gray-600\">\n          <div className=\"grid grid-cols-2 gap-2\">\n            <div><span className=\"font-medium\">Extraction Method:</span> {source.visual_content.extraction_method}</div>\n            <div><span className=\"font-medium\">Page:</span> {source.page}</div>\n            {source.visual_content.table_index !== undefined && (\n              <div><span className=\"font-medium\">Table Index:</span> {source.visual_content.table_index}</div>\n            )}\n            {source.visual_content.image_index !== undefined && (\n              <div><span className=\"font-medium\">Image Index:</span> {source.visual_content.image_index}</div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Helper function to convert markdown table to HTML\nconst markdownTableToHtml = (markdown: string): string => {\n  if (!markdown || typeof markdown !== 'string') {\n    return `<div class=\"p-4 text-gray-600\">Invalid table data</div>`;\n  }\n  \n  const lines = markdown.split('\\n').filter(line => line.trim());\n  if (lines.length < 2) return `<div class=\"p-4 text-gray-600\">Invalid table format</div>`;\n\n  let html = '<table class=\"min-w-full border border-gray-200 rounded-lg\">';\n  \n  try {\n    // Process header\n    const headerLine = lines[0];\n    const headerCells = headerLine.split('|')\n      .map(cell => cell.trim())\n      .filter(cell => cell !== ''); // Remove empty cells from start/end\n    \n    if (headerCells.length === 0) {\n      // Try alternate format where cells are separated by multiple spaces\n      const spaceSeparatedCells = headerLine.split(/\\s{2,}/).filter(cell => cell.trim());\n      if (spaceSeparatedCells.length > 0) {\n        html += '<thead class=\"bg-gray-50\"><tr>';\n        spaceSeparatedCells.forEach((cell, index) => {\n          const cleanCell = cell.replace(/\\*\\*/g, '').trim();\n          html += `<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell || `Column ${index + 1}`}</th>`;\n        });\n        html += '</tr></thead>';\n        \n        // Process data rows (skip separator row)\n        html += '<tbody>';\n        for (let i = 2; i < lines.length; i++) {\n          const rowCells = lines[i].split(/\\s{2,}/).filter(cell => cell.trim());\n          if (rowCells.length === 0) continue;\n          \n          html += '<tr class=\"hover:bg-gray-50 transition-colors\">';\n          // Make sure we have the right number of cells\n          while (rowCells.length < spaceSeparatedCells.length) {\n            rowCells.push('');\n          }\n          \n          spaceSeparatedCells.forEach((_, index) => {\n            const cellContent = rowCells[index] || '';\n            html += `<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cellContent.trim() || '-'}</td>`;\n          });\n          html += '</tr>';\n        }\n        html += '</tbody></table>';\n        return html;\n      }\n      \n      return `<div class=\"p-4 text-gray-600\">No table headers found</div>`;\n    }\n    \n    html += '<thead class=\"bg-gray-50\"><tr>';\n    headerCells.forEach((cell, index) => {\n      const cleanCell = cell.replace(/\\*\\*/g, '').trim(); // Remove markdown bold\n      html += `<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell || `Column ${index + 1}`}</th>`;\n    });\n    html += '</tr></thead>';\n\n    // Check if we have a separator row at index 1 (standard markdown table)\n    const hasSeparator = lines.length > 1 && lines[1].includes('|') && lines[1].includes('-');\n    const startRowIndex = hasSeparator ? 2 : 1;\n\n    // Process data rows\n    html += '<tbody>';\n    for (let i = startRowIndex; i < lines.length; i++) {\n      const rowLine = lines[i];\n      if (!rowLine.includes('|')) continue; // Skip non-table lines\n      \n      const cells = rowLine.split('|')\n        .map(cell => cell.trim())\n        .filter((cell, index, array) => {\n          // Keep all cells except first and last if they're empty (markdown format)\n          if (index === 0 || index === array.length - 1) {\n            return cell !== '';\n          }\n          return true;\n        });\n      \n      // Ensure we have the right number of cells\n      while (cells.length < headerCells.length) {\n        cells.push('');\n      }\n      \n      html += '<tr class=\"hover:bg-gray-50 transition-colors\">';\n      headerCells.forEach((_, cellIndex) => {\n        const cellContent = cells[cellIndex] || '';\n        const cleanCell = cellContent.replace(/\\*\\*/g, '').trim(); // Remove markdown bold\n        html += `<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cleanCell || '-'}</td>`;\n      });\n      html += '</tr>';\n    }\n    html += '</tbody></table>';\n  } catch (error) {\n    console.error('Error parsing markdown table:', error);\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n    return `<div class=\"p-4 text-gray-600 bg-red-50 border border-red-200 rounded\">Error parsing table: ${errorMessage}</div>`;\n  }\n\n  return html;\n};\n\nexport default VisualContent; ", "C:\\IR App\\frontend\\src\\components\\documents\\BulkCategoryEditor.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteCategoryManagement.tsx", [], "C:\\IR App\\frontend\\src\\components\\categories\\CategoryCreator.tsx", [], {"ruleId": "152", "replacedBy": "153"}, {"ruleId": "154", "replacedBy": "155"}, {"ruleId": "156", "severity": 1, "message": "157", "line": 18, "column": 10, "nodeType": "158", "messageId": "159", "endLine": 18, "endColumn": 20}, {"ruleId": "156", "severity": 1, "message": "160", "line": 20, "column": 10, "nodeType": "158", "messageId": "159", "endLine": 20, "endColumn": 22}, {"ruleId": "156", "severity": 1, "message": "161", "line": 20, "column": 24, "nodeType": "158", "messageId": "159", "endLine": 20, "endColumn": 39}, {"ruleId": "162", "severity": 1, "message": "163", "line": 37, "column": 6, "nodeType": "164", "endLine": 37, "endColumn": 56, "suggestions": "165"}, {"ruleId": "156", "severity": 1, "message": "166", "line": 131, "column": 9, "nodeType": "158", "messageId": "159", "endLine": 131, "endColumn": 24}, {"ruleId": "167", "severity": 1, "message": "168", "line": 198, "column": 11, "nodeType": "169", "endLine": 211, "endColumn": 13}, {"ruleId": "167", "severity": 1, "message": "168", "line": 242, "column": 11, "nodeType": "169", "endLine": 252, "endColumn": 13}, {"ruleId": "167", "severity": 1, "message": "168", "line": 278, "column": 11, "nodeType": "169", "endLine": 286, "endColumn": 13}, {"ruleId": "170", "severity": 1, "message": "171", "line": 471, "column": 39, "nodeType": "172", "messageId": "173", "endLine": 474, "endColumn": 12}, {"ruleId": "170", "severity": 1, "message": "171", "line": 517, "column": 27, "nodeType": "172", "messageId": "173", "endLine": 521, "endColumn": 8}, "no-native-reassign", ["174"], "no-negated-in-lhs", ["175"], "@typescript-eslint/no-unused-vars", "'imageTries' is assigned a value but never used.", "Identifier", "unusedVar", "'visibleDebug' is assigned a value but never used.", "'setVisibleDebug' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'source.content_type' and 'source.visual_content'. Either include them or remove the dependency array.", "ArrayExpression", ["176"], "'getImageSources' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'html'.", "ArrowFunctionExpression", "unsafeRefs", "no-global-assign", "no-unsafe-negation", {"desc": "177", "fix": "178"}, "Update the dependencies array to be: [source.content_type, source.filename, source.page, source.storage_url, source.visual_content]", {"range": "179", "text": "180"}, [1319, 1369], "[source.content_type, source.filename, source.page, source.storage_url, source.visual_content]"]