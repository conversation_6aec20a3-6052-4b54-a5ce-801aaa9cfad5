#!/usr/bin/env python3
"""
Test script to verify visual content query functionality
"""

import requests
import json
import time

def test_visual_query_endpoint():
    """Test the query endpoint with visual content queries"""
    print("🔍 Testing Visual Content Query Endpoint")
    print("=" * 45)
    
    base_url = "http://localhost:8000"
    
    # Test queries that should find visual content
    test_queries = [
        {
            "query": "Show me the railway component specifications table",
            "description": "Table-specific query"
        },
        {
            "query": "What are the part numbers for railway components?",
            "description": "Table data query"
        },
        {
            "query": "List the specifications for steel socket head cap screws",
            "description": "Specific component query"
        },
        {
            "query": "How many warning horns are specified?",
            "description": "Quantity query from table"
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n📋 Test {i}: {test_case['description']}")
        print(f"Query: {test_case['query']}")
        print("-" * 50)
        
        try:
            # Make query request
            response = requests.post(
                f"{base_url}/api/query",
                json={
                    "query": test_case["query"],
                    "model": "gemini-2.0-flash",
                    "extract_format": "paragraph"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Query successful")
                print(f"📄 Answer: {result.get('answer', 'No answer')[:200]}...")
                print(f"🔍 Visual content found: {result.get('visual_content_found', False)}")
                print(f"📊 Visual content types: {result.get('visual_content_types', [])}")
                print(f"📚 Sources: {len(result.get('sources', []))}")
                
                # Check for visual content in sources
                sources = result.get('sources', [])
                visual_sources = [s for s in sources if s.get('content_type') != 'text']
                if visual_sources:
                    print(f"🎯 Visual sources found: {len(visual_sources)}")
                    for vs in visual_sources:
                        print(f"   - {vs.get('content_type', 'unknown')} from {vs.get('name', 'unknown')}")
                else:
                    print("⚠️  No visual sources in response")
                    
            else:
                print(f"❌ Query failed: {response.status_code}")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Query error: {str(e)}")
        
        time.sleep(1)  # Brief pause between queries

def test_visual_query_detection():
    """Test the visual query detection functionality"""
    print("\n🔍 Testing Visual Query Detection")
    print("=" * 35)
    
    # Test the detect_visual_query function directly
    import sys
    sys.path.append('backend')
    
    try:
        from server import detect_visual_query
        
        test_queries = [
            "Show me the table with component specifications",
            "What images are available in the document?",
            "Display the chart showing railway statistics",
            "What is the meaning of LLM?",  # Non-visual query
            "List all the diagrams in the technical manual"
        ]
        
        for query in test_queries:
            result = detect_visual_query(query)
            print(f"Query: {query}")
            print(f"Visual detection: {result}")
            print("-" * 30)
            
    except ImportError as e:
        print(f"❌ Could not import visual query detection: {e}")

def main():
    """Run all visual content tests"""
    print("🚀 Visual Content Query Testing")
    print("=" * 50)
    
    # Test visual query detection
    test_visual_query_detection()
    
    # Test query endpoint
    test_visual_query_endpoint()
    
    print("\n✅ Visual content query testing completed!")

if __name__ == "__main__":
    main() 