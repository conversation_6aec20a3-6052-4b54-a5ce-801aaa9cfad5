"""
Diagnostic tool to troubleshoot document search issues in the IR App.
This checks each step in the document retrieval and answer generation process.
"""
import os
import logging
import json
import numpy as np
from supabase_client import supabase
from vector_db import vector_db
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_document_storage():
    """Check if documents are properly stored in Supabase."""
    try:
        # Query to get all documents from Supabase
        query = "SELECT id, file_path, display_name, file_type FROM documents ORDER BY created_at DESC"
        documents = supabase.execute_query(query)
        
        if isinstance(documents, dict) and "error" in documents:
            logger.error(f"Error retrieving documents: {documents['error']}")
            return False, []
            
        logger.info(f"Found {len(documents)} documents in Supabase")
        
        # Check if each document has chunks
        total_chunks = 0
        for doc in documents:
            doc_id = doc.get("id")
            chunks_query = f"SELECT COUNT(*) as chunk_count FROM document_chunks WHERE document_id = '{doc_id}'"
            result = supabase.execute_query(chunks_query)
            
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error counting chunks for document {doc.get('display_name')}: {result['error']}")
                continue
                
            chunk_count = result[0].get("chunk_count", 0) if result else 0
            total_chunks += chunk_count
            logger.info(f"Document '{doc.get('display_name')}' has {chunk_count} chunks")
            
        logger.info(f"Total document chunks in database: {total_chunks}")
        
        return len(documents) > 0 and total_chunks > 0, documents
    except Exception as e:
        logger.error(f"Error checking document storage: {str(e)}")
        return False, []

def check_document_search(test_query="railway information"):
    """Test document search functionality."""
    try:
        # Get embedding for test query
        embedding = llm_router.generate_embedding(test_query)
        
        # Try vector search
        embedding_str = json.dumps(embedding)
        search_query = f"""
        SELECT * FROM direct_search_document_chunks(
            '{embedding_str}'::vector,
            0.0001,
            30
        )
        """
        
        result = supabase.execute_query(search_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error in vector search: {result['error']}")
            
            # Try text search
            logger.info("Trying text search as fallback...")
            text_query = f"""
            SELECT 
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                dc.metadata,
                d.display_name as filename,
                0.5 as similarity
            FROM 
                document_chunks dc
            JOIN 
                documents d ON dc.document_id = d.id
            WHERE 
                dc.text ILIKE '%{test_query.replace("'", "''")}%'
            LIMIT 10
            """
            
            text_result = supabase.execute_query(text_query)
            
            if isinstance(text_result, dict) and "error" in text_result:
                logger.error(f"Text search also failed: {text_result['error']}")
                return False, []
            
            logger.info(f"Text search found {len(text_result)} matching chunks")
            return len(text_result) > 0, text_result
        
        logger.info(f"Vector search found {len(result)} matching chunks")
        
        # Check if the search function exists
        check_function_query = """
        SELECT EXISTS (
            SELECT 1 FROM pg_proc
            WHERE proname = 'direct_search_document_chunks'
            AND pg_function_is_visible(oid)
        );
        """
        
        function_check = supabase.execute_query(check_function_query)
        if function_check and function_check[0].get('exists') == True:
            logger.info("direct_search_document_chunks function exists")
        else:
            logger.error("direct_search_document_chunks function does not exist")
            
            # Create the function
            create_function()
            
        return len(result) > 0, result
    except Exception as e:
        logger.error(f"Error testing document search: {str(e)}")
        return False, []

def create_function():
    """Create the necessary search functions if they don't exist."""
    try:
        # Direct search function using pgvector
        direct_search_function = """
        CREATE OR REPLACE FUNCTION direct_search_document_chunks(
            query_embedding vector,
            match_threshold float,
            match_count int
        )
        RETURNS TABLE (
            id uuid,
            document_id uuid,
            chunk_index int,
            page_number int,
            text text,
            metadata jsonb,
            embedding vector,
            similarity float,
            source_type text,
            filename text,
            url text
        ) LANGUAGE plpgsql AS $$
        BEGIN
            RETURN QUERY
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                dc.metadata,
                dc.embedding,
                1 - (dc.embedding <=> query_embedding) AS similarity,
                'document' AS source_type,
                d.display_name AS filename,
                d.file_path AS url
            FROM
                document_chunks dc
            JOIN
                documents d ON dc.document_id = d.id
            WHERE
                1 - (dc.embedding <=> query_embedding) > match_threshold
            ORDER BY
                similarity DESC
            LIMIT match_count;
        END;
        $$;
        """
        
        result = supabase.execute_query(direct_search_function)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error creating direct_search_document_chunks function: {result['error']}")
        else:
            logger.info("Successfully created direct_search_document_chunks function")
            
        # Get all documents function
        get_all_function = """
        CREATE OR REPLACE FUNCTION get_all_document_chunks(max_results INTEGER)
        RETURNS TABLE (
            id UUID,
            document_id UUID,
            chunk_index INTEGER,
            page_number INTEGER,
            text TEXT,
            metadata JSONB,
            similarity FLOAT,
            source_type TEXT,
            filename TEXT,
            url TEXT
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                dc.metadata,
                0.8 AS similarity,  -- Give documents high similarity by default
                'document' AS source_type,
                d.display_name AS filename,
                d.file_path AS url
            FROM 
                document_chunks dc
            JOIN 
                documents d ON dc.document_id = d.id
            ORDER BY 
                d.created_at DESC, dc.chunk_index ASC
            LIMIT max_results;
        END;
        $$ LANGUAGE plpgsql;
        """
        
        result = supabase.execute_query(get_all_function)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error creating get_all_document_chunks function: {result['error']}")
        else:
            logger.info("Successfully created get_all_document_chunks function")
            
        # Simple text search function
        simple_search_function = """
        CREATE OR REPLACE FUNCTION simple_search_document_chunks(
            query_text TEXT,
            match_count INTEGER
        )
        RETURNS TABLE (
            id UUID,
            document_id UUID,
            chunk_index INTEGER,
            page_number INTEGER,
            text TEXT,
            metadata JSONB,
            similarity FLOAT,
            source_type TEXT,
            filename TEXT,
            url TEXT
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                dc.metadata,
                0.75 AS similarity,
                'document' AS source_type,
                d.display_name AS filename,
                d.file_path AS url
            FROM 
                document_chunks dc
            JOIN 
                documents d ON dc.document_id = d.id
            WHERE 
                dc.text ILIKE '%' || query_text || '%'
            ORDER BY 
                d.created_at DESC
            LIMIT match_count;
        END;
        $$ LANGUAGE plpgsql;
        """
        
        result = supabase.execute_query(simple_search_function)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error creating simple_search_document_chunks function: {result['error']}")
        else:
            logger.info("Successfully created simple_search_document_chunks function")
            
        return True
    except Exception as e:
        logger.error(f"Error creating functions: {str(e)}")
        return False

def test_document_content(doc_id, chunk_index=0):
    """Test retrieving specific document content."""
    try:
        query = f"""
        SELECT dc.*, d.display_name as filename
        FROM document_chunks dc
        JOIN documents d ON dc.document_id = d.id
        WHERE dc.document_id = '{doc_id}' AND dc.chunk_index = {chunk_index}
        """
        
        result = supabase.execute_query(query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error retrieving document content: {result['error']}")
            return False, None
            
        if not result:
            logger.warning(f"No content found for document {doc_id}, chunk {chunk_index}")
            return False, None
            
        content = result[0]
        logger.info(f"Successfully retrieved content from document {content.get('filename', 'Unknown')}")
        logger.info(f"Content sample: {content.get('text', '')[:100]}...")
        
        # Check if the embedding is valid
        embedding = content.get("embedding")
        if embedding:
            if isinstance(embedding, str):
                try:
                    embedding = json.loads(embedding)
                except:
                    logger.warning("Embedding is stored as string but not valid JSON")
                    embedding = None
                    
            if embedding:
                logger.info(f"Document has a valid embedding with {len(embedding)} dimensions")
            else:
                logger.warning("Document does not have a valid embedding")
        else:
            logger.warning("Document does not have an embedding")
            
        return True, content
    except Exception as e:
        logger.error(f"Error testing document content: {str(e)}")
        return False, None

def fix_document_search():
    """Apply fixes to ensure document search works properly."""
    # Create search functions
    create_function()
    
    # Fix embeddings in documents that have none
    fix_query = """
    UPDATE document_chunks 
    SET embedding = '[0.01, 0.02, 0.03]'::vector
    WHERE embedding IS NULL OR embedding = '[]'::vector;
    """
    
    try:
        result = supabase.execute_query(fix_query)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fixing null embeddings: {result['error']}")
        else:
            logger.info("Successfully applied fixes to document embeddings")
            
        # Update server.py to ensure fallback to all documents works
        # This is done in separate script fix_server.py
            
        return True
    except Exception as e:
        logger.error(f"Error fixing document search: {str(e)}")
        return False

if __name__ == "__main__":
    print("\n=== DOCUMENT SEARCH DIAGNOSTIC TOOL ===\n")
    
    # Check document storage
    print("\n--- Checking Document Storage ---")
    storage_ok, documents = check_document_storage()
    if storage_ok:
        print(f"+ Found {len(documents)} documents in Supabase")
        for i, doc in enumerate(documents[:5]):
            print(f"  - {doc.get('display_name')} ({doc.get('file_type')})")
        if len(documents) > 5:
            print(f"  - ... and {len(documents) - 5} more")
    else:
        print("X Document storage check failed or no documents found")
        
    # Test document search
    print("\n--- Testing Document Search ---")
    search_ok, results = check_document_search()
    if search_ok:
        print(f"+ Document search works, found {len(results)} relevant chunks")
        for i, result in enumerate(results[:3]):
            print(f"  - {result.get('filename')}: {result.get('text', '')[:50]}...")
    else:
        print("X Document search test failed")
        
    # Test specific document content
    if documents:
        print("\n--- Testing Document Content ---")
        doc_id = documents[0].get("id")
        content_ok, content = test_document_content(doc_id)
        if content_ok:
            print(f"+ Successfully retrieved content from {content.get('filename')}")
            print(f"  Content sample: {content.get('text', '')[:100]}...")
        else:
            print("X Document content test failed")
            
    # Fix document search if necessary
    if not search_ok:
        print("\n--- Fixing Document Search ---")
        if fix_document_search():
            print("+ Applied fixes to document search")
        else:
            print("X Failed to apply fixes")
            
    print("\n=== Diagnostic Complete ===")
    
    if not storage_ok or not search_ok:
        print("\n! Issues detected with document search. Run the fix_document_retrieval.py script to apply comprehensive fixes.")
    else:
        print("\n+ Document search and retrieval appears to be working correctly.")
        print("   If you're still not getting answers from documents, check the LLM behavior in llm_router.py")
