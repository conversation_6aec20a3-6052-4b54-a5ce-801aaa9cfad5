{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\App.tsx\";\nimport React, { useState, useRef, useEffect } from 'react';\nimport './App.css';\nimport { sendQuery } from './services/api';\nimport LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';\nimport InteractiveAnswer from './components/ui/InteractiveAnswer';\nimport ChatSidebar from './components/chat/ChatSidebar';\nimport TrainLoader from './components/ui/TrainLoader';\nimport VisualContent from './components/ui/VisualContent';\nimport { useChatContext } from './contexts/ChatContext';\n\n// Using ChatMessage interface from services/supabase.ts\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ChatInterface({\n  sidebarOpen,\n  setSidebarOpen\n}) {\n  var _DEFAULT_LLM_MODELS$f;\n  const {\n    currentSession,\n    messages,\n    createNewChat,\n    loadChatSession,\n    addMessage,\n    updateMessage,\n    clearCurrentChat\n  } = useChatContext();\n  const [input, setInput] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model\n  const [showTrainLoader, setShowTrainLoader] = useState(false);\n  const [currentSearchStage, setCurrentSearchStage] = useState('initializing');\n  const messagesEndRef = useRef(null);\n\n  // Scroll to bottom of chat whenever messages change\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n\n  // Helper function to determine if visual content should be shown\n  const shouldShowVisualContent = (userQuestion, documentAnswer) => {\n    const userQuery = userQuestion.toLowerCase();\n\n    // Always show visual content when user asks for images\n    if (userQuery.includes('image')) {\n      return true;\n    }\n\n    // Detect different types of requests\n    const askedForImages = userQuery.includes('image') || userQuery.includes('picture') || userQuery.includes('diagram') || userQuery.includes('chart');\n    const askedForTableData = (userQuery.includes('table') || userQuery.includes('quotation')) && (userQuery.includes('give me') || userQuery.includes('show me table') || userQuery.includes('get') || userQuery.includes('provide'));\n    const askedToShowImages = userQuery.includes('show me image') || userQuery.includes('display image') || userQuery.includes('see image');\n    const answerLower = (documentAnswer === null || documentAnswer === void 0 ? void 0 : documentAnswer.toLowerCase()) || '';\n    const hasTableInAnswer = answerLower.includes('<table>') || answerLower.includes('|') || answerLower.includes('table') && answerLower.length > 200;\n    const hasImageDescription = answerLower.includes('image') || answerLower.includes('figure') || answerLower.includes('diagram');\n    const answerHasContent = answerLower.length > 50;\n\n    // ALWAYS show visual content if user specifically asks to see images/visuals\n    if (askedToShowImages || askedForImages && (userQuery.includes('show') || userQuery.includes('display'))) {\n      return true;\n    }\n\n    // NEVER show if user asked for table data (they want the actual data, not images)\n    if (askedForTableData && !askedForImages) {\n      return false;\n    }\n\n    // For other cases, show only if answer doesn't already provide adequate information\n    const shouldShow = !hasTableInAnswer &&\n    // Don't show if answer already has table data\n    !hasImageDescription && (\n    // Don't show if answer already describes images well\n\n    askedForImages && !answerHasContent ||\n    // Show images if user asked and answer is short\n    !answerHasContent && answerLower.length < 50 // Show for very short answers\n    );\n    return shouldShow;\n  };\n\n  // Helper function to filter visual content types based on user query\n  const filterVisualContent = (source, userQuestion) => {\n    if (typeof source !== 'object' || !source.visual_content) {\n      return false;\n    }\n    const userQueryLower = userQuestion.toLowerCase();\n    const sourceObj = source;\n\n    // Extract specific project/quotation numbers from user query\n    const projectMatch = userQueryLower.match(/project\\s*(\\d+)/);\n    const quotationMatch = userQueryLower.match(/quotation\\s*(\\d+)/);\n\n    // Extract company names for logo queries\n    const logoQueryPatterns = [/(?:logo\\s+(?:of\\s+)?|show\\s+me\\s+(?:the\\s+)?logo\\s+(?:of\\s+)?)([A-Z][A-Za-z\\s&]+?)(?:\\s+logo|\\s+enterprises|\\s+company|\\s+corp|\\s+ltd|\\s+inc|\\s*$)/i, /([A-Z][A-Za-z\\s&]+?)\\s+(?:enterprises|company|corp|ltd|inc)\\s+logo/i, /([A-Z][A-Za-z\\s&]+?)\\s+logo/i];\n    let companyMatch = null;\n    for (const pattern of logoQueryPatterns) {\n      const match = userQuestion.match(pattern);\n      if (match) {\n        companyMatch = match[1].trim();\n        break;\n      }\n    }\n\n    // Check if the visual content contains relevant information for the specific request\n    const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();\n\n    // If user asked for a specific company logo\n    if (companyMatch && userQueryLower.includes('logo')) {\n      const companyNameLower = companyMatch.toLowerCase();\n      const isImage = sourceObj.content_type === 'image';\n      if (isImage) {\n        var _sourceObj$visual_con, _sourceObj$visual_con2, _sourceObj$visual_con3;\n        // Check if this image contains the requested company\n        const hasCompanyInContent = visualContentStr.includes(companyNameLower);\n        const hasCompanyInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(companyNameLower);\n\n        // Check detected companies from OCR\n        const detectedCompanies = ((_sourceObj$visual_con = sourceObj.visual_content) === null || _sourceObj$visual_con === void 0 ? void 0 : _sourceObj$visual_con.detected_companies) || [];\n        const hasDetectedCompany = detectedCompanies.some(company => company.toLowerCase().includes(companyNameLower) || companyNameLower.includes(company.toLowerCase()));\n\n        // Check OCR text\n        const ocrText = ((_sourceObj$visual_con2 = sourceObj.visual_content) === null || _sourceObj$visual_con2 === void 0 ? void 0 : _sourceObj$visual_con2.ocr_text) || '';\n        const hasOcrMatch = ocrText.toLowerCase().includes(companyNameLower);\n\n        // Check if marked as logo\n        const isLogo = ((_sourceObj$visual_con3 = sourceObj.visual_content) === null || _sourceObj$visual_con3 === void 0 ? void 0 : _sourceObj$visual_con3.is_logo) || false;\n        const isRelevantLogo = hasCompanyInContent || hasCompanyInFilename || hasDetectedCompany || hasOcrMatch || isLogo;\n        console.log('🔍 DEBUG: Company logo filter result:', {\n          company: companyMatch,\n          hasCompanyInContent,\n          hasCompanyInFilename,\n          hasDetectedCompany,\n          hasOcrMatch,\n          isLogo,\n          isRelevantLogo,\n          should_show: isImage && isRelevantLogo\n        });\n        return isRelevantLogo;\n      }\n      return false; // Not an image for logo query\n    }\n\n    // If user asked for a specific project image\n    if (projectMatch && userQueryLower.includes('image')) {\n      var _sourceObj$visual_con4, _sourceObj$visual_con5;\n      const projectNumber = projectMatch[1];\n      const isImage = sourceObj.content_type === 'image';\n\n      // Check if this image is related to the specific project\n      const hasProjectInContent = visualContentStr.includes(`project ${projectNumber}`) || visualContentStr.includes(`project${projectNumber}`);\n      const hasProjectInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(`project ${projectNumber}`);\n      const isOnProjectPage = sourceObj.page && Math.abs(sourceObj.page - parseInt(projectNumber)) <= 1; // Allow adjacent pages\n      const hasProjectContext = ((_sourceObj$visual_con4 = sourceObj.visual_content) === null || _sourceObj$visual_con4 === void 0 ? void 0 : _sourceObj$visual_con4.project_context) && sourceObj.visual_content.project_context.includes(`project ${projectNumber}`);\n      const isRelevantProject = hasProjectInContent || hasProjectInFilename || isOnProjectPage || hasProjectContext;\n      console.log('🔍 DEBUG: Project', projectNumber, 'image filter:', {\n        isImage,\n        hasProjectInContent,\n        hasProjectInFilename,\n        isOnProjectPage,\n        hasProjectContext,\n        isRelevantProject,\n        visualContentStr_sample: visualContentStr.substring(0, 150),\n        project_context: (_sourceObj$visual_con5 = sourceObj.visual_content) === null || _sourceObj$visual_con5 === void 0 ? void 0 : _sourceObj$visual_con5.project_context,\n        page: sourceObj.page,\n        should_show: isImage && isRelevantProject\n      });\n\n      // If this is an image and it's relevant to the project, show it\n      // If no images are relevant, we'll fall back to showing all images\n      return isImage && isRelevantProject;\n    }\n\n    // If user asked for a specific quotation table\n    if (quotationMatch && userQueryLower.includes('table')) {\n      const quotationNumber = quotationMatch[1];\n      const isTable = sourceObj.content_type === 'table';\n\n      // Check if this table is related to the specific quotation\n      const isRelevantQuotation = visualContentStr.includes(`quotation ${quotationNumber}`) || visualContentStr.includes(`quotation${quotationNumber}`) || visualContentStr.includes(`quote ${quotationNumber}`);\n      console.log('🔍 DEBUG: Quotation', quotationNumber, 'table filter:', {\n        isTable,\n        isRelevantQuotation,\n        should_show: isTable && isRelevantQuotation\n      });\n      return isTable && isRelevantQuotation;\n    }\n\n    // If user specifically asked for images (but no specific project), only show images\n    if (userQueryLower.includes('image') && !userQueryLower.includes('table')) {\n      const isImage = sourceObj.content_type === 'image';\n      console.log('🔍 DEBUG: User asked for images only, source is image:', isImage);\n      return isImage;\n    }\n\n    // If user specifically asked for tables (but no specific quotation), only show tables\n    if (userQueryLower.includes('table') && !userQueryLower.includes('image')) {\n      const isTable = sourceObj.content_type === 'table';\n      console.log('🔍 DEBUG: User asked for tables only, source is table:', isTable);\n      return isTable;\n    }\n\n    // Default: show all visual content\n    console.log('🔍 DEBUG: Showing all visual content by default');\n    return true;\n  };\n\n  // Handle command shortcuts in the textbox\n  const handleCommandShortcut = input => {\n    // Check if the input is a command\n    if (input.startsWith('/')) {\n      const command = input.split(' ')[0].toLowerCase();\n\n      // Command: /model <model-name>\n      if (command === '/model') {\n        const modelArg = input.substring(7).trim();\n        const matchedModel = DEFAULT_LLM_MODELS.find(m => m.name.toLowerCase().includes(modelArg.toLowerCase()) || m.id.toLowerCase().includes(modelArg.toLowerCase()));\n        if (matchedModel && matchedModel.enabled) {\n          setActiveLLMModel(matchedModel.id);\n          setInput('');\n          return 'processed';\n        }\n      }\n\n      // Command: /reset or /clear - clear chat history\n      else if (command === '/reset' || command === '/clear') {\n        clearCurrentChat();\n        setInput('');\n        return 'processed';\n      }\n    }\n    return 'not_processed';\n  };\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (!input.trim() || isSubmitting) return;\n\n    // Handle command shortcuts like /reset, /model, etc.\n    if (input.startsWith('/')) {\n      const result = handleCommandShortcut(input);\n      if (result === 'processed') {\n        setInput('');\n        return;\n      }\n      // If not processed as a command, continue as a regular message\n    }\n    return await sendUserMessage(input);\n  };\n  const sendUserMessage = async messageText => {\n    var _messagesEndRef$curre;\n    // Create new chat session if none exists\n    if (!currentSession) {\n      await createNewChat();\n    }\n    const userMessage = {\n      id: `user-${Date.now()}`,\n      content: messageText,\n      sender: 'user',\n      timestamp: new Date().toISOString(),\n      chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp'\n    };\n    addMessage(userMessage);\n    setInput('');\n    const messageId = Date.now();\n    const tempAiMessage = {\n      id: `ai-${messageId}`,\n      content: '',\n      sender: 'ai',\n      loading: true,\n      timestamp: new Date().toISOString(),\n      llm_model: activeLLMModel,\n      chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp'\n    };\n    addMessage(tempAiMessage);\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n    setIsSubmitting(true);\n    setShowTrainLoader(true);\n    setCurrentSearchStage('initializing');\n    try {\n      var _messagesEndRef$curre2, _response$document_so, _response$website_sou, _messagesEndRef$curre3;\n      (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n        behavior: 'smooth'\n      });\n\n      // Simulate search progress updates with train loader\n      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);\n      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);\n      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);\n      const response = await sendQuery(messageText, activeLLMModel);\n\n      // Create the AI message based on strict priority logic\n      const aiMessage = {\n        id: `ai-${messageId}`,\n        content: response.answer,\n        document_answer: response.document_answer,\n        website_answer: response.website_answer,\n        llm_model: response.llm_model || activeLLMModel,\n        sender: 'ai',\n        sources: response.sources,\n        document_sources: response.document_sources,\n        website_sources: response.website_sources,\n        chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp',\n        llm_fallback: response.llm_fallback\n      };\n\n      // Debug logging\n      console.log('AI Message created:', {\n        document_answer: response.document_answer ? 'EXISTS' : 'MISSING',\n        website_answer: response.website_answer ? 'EXISTS' : 'MISSING',\n        document_sources_count: ((_response$document_so = response.document_sources) === null || _response$document_so === void 0 ? void 0 : _response$document_so.length) || 0,\n        website_sources_count: ((_response$website_sou = response.website_sources) === null || _response$website_sou === void 0 ? void 0 : _response$website_sou.length) || 0,\n        llm_fallback: response.llm_fallback\n      });\n\n      // Debug visual content sources\n      console.log('🔍 DEBUG: Document sources from API:', response.document_sources);\n      if (response.document_sources) {\n        response.document_sources.forEach((source, index) => {\n          console.log(`🔍 DEBUG: Source ${index}:`, {\n            content_type: source.content_type,\n            has_visual_content: !!source.visual_content,\n            storage_url: source.storage_url,\n            display_type: source.display_type,\n            visual_content_keys: source.visual_content ? Object.keys(source.visual_content) : []\n          });\n        });\n      }\n      updateMessage(tempAiMessage.id, aiMessage);\n      (_messagesEndRef$curre3 = messagesEndRef.current) === null || _messagesEndRef$curre3 === void 0 ? void 0 : _messagesEndRef$curre3.scrollIntoView({\n        behavior: 'smooth'\n      });\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      // Provide more helpful error message without the specific query\n      const errorMessage = {\n        id: `ai-${messageId}`,\n        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,\n        sender: 'ai',\n        chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp',\n        llm_fallback: true\n      };\n      updateMessage(tempAiMessage.id, errorMessage);\n    } finally {\n      setIsSubmitting(false);\n      setShowTrainLoader(false);\n      setCurrentSearchStage('complete');\n    }\n  };\n  const processDocumentSources = sources => {\n    if (!sources || sources.length === 0) return [];\n\n    // Group by filename to avoid repetition\n    const groupedSources = {};\n    sources.forEach(source => {\n      let filename;\n      let page;\n      if (typeof source === 'string') {\n        // Parse string format like \"MaintenanceManual.pdf – Page 3\"\n        const match = source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);\n        filename = match ? match[1].trim() : source;\n        page = match && match[2] ? parseInt(match[2]) : 1;\n      } else {\n        filename = source.name || source.filename || \"Unknown Document\";\n        page = source.page || 1;\n      }\n      if (!groupedSources[filename]) {\n        groupedSources[filename] = {\n          filename,\n          pages: []\n        };\n      }\n      if (!groupedSources[filename].pages.includes(page)) {\n        groupedSources[filename].pages.push(page);\n      }\n    });\n\n    // Convert to display format with viewer links\n    return Object.values(groupedSources).map(group => {\n      const sortedPages = group.pages.sort((a, b) => a - b);\n      const pageText = sortedPages.length === 1 ? `Page ${sortedPages[0]}` : `Pages ${sortedPages.join(', ')}`;\n\n      // Create link for document viewer that opens at exact page number\n      return {\n        text: `${group.filename} – ${pageText}`,\n        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,\n        isDocument: true\n      };\n    });\n  };\n  const processWebsiteSources = sources => {\n    if (!sources || sources.length === 0) return [];\n\n    // Remove duplicates and format\n    const uniqueUrls = new Set();\n    const processed = [];\n    sources.forEach(source => {\n      let url;\n      let displayText;\n      if (typeof source === 'string') {\n        url = source.startsWith('http') ? source : `https://${source}`;\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = source;\n        }\n      } else {\n        url = source.url || 'https://railgpt.indianrailways.gov.in';\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = url;\n        }\n      }\n      if (!uniqueUrls.has(url)) {\n        uniqueUrls.add(url);\n        processed.push({\n          text: displayText,\n          link: url,\n          isDocument: false // Mark as website source\n        });\n      }\n    });\n    return processed; // Return all website sources\n  };\n\n  // Component for expandable source list with appropriate click behaviors\n  const SourceList = ({\n    items,\n    maxVisible = 3\n  }) => {\n    const [expanded, setExpanded] = useState(items.length <= maxVisible);\n    if (items.length === 0) return null;\n    const visibleItems = expanded ? items : items.slice(0, maxVisible);\n    const hasMore = items.length > maxVisible;\n    const handleDocumentClick = (e, link) => {\n      // If we want to handle document links in a special way, we can do so here\n      // For example, we could open a modal or new tab with the document viewer\n      // Currently, just allowing regular link behavior\n    };\n    return /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"text-xs list-disc pl-4 mt-1 space-y-1\",\n      children: [visibleItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: item.link ? /*#__PURE__*/_jsxDEV(\"a\", {\n          href: item.link,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"hover:underline text-blue-600 transition-colors duration-200\",\n          title: item.isDocument ? \"Open document at this page\" : \"Open website in new tab\",\n          onClick: item.isDocument ? e => handleDocumentClick(e, item.link) : undefined,\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 15\n      }, this)), hasMore && !expanded && /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"list-none\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setExpanded(true),\n          className: \"text-blue-500 hover:underline text-xs transition-colors duration-200\",\n          children: [\"+ \", items.length - maxVisible, \" more sources\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 11\n      }, this), hasMore && expanded && /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"list-none\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setExpanded(false),\n          className: \"text-blue-500 hover:underline text-xs transition-colors duration-200\",\n          children: \"Show less\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Sidebar handlers\n  const handleChatSelect = async chatSession => {\n    // Load the selected chat session using context\n    await loadChatSession(chatSession.id);\n    setActiveLLMModel(chatSession.model_used || 'gemini-2.0-flash');\n    setSidebarOpen(false); // Close sidebar on mobile after selection\n  };\n  const handleNewChat = async () => {\n    console.log('Creating new chat...');\n    await createNewChat();\n    setSidebarOpen(false); // Close sidebar on mobile after creating new chat\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-100 transition-colors duration-300\",\n    children: [/*#__PURE__*/_jsxDEV(TrainLoader, {\n      isVisible: showTrainLoader,\n      message: (() => {\n        switch (currentSearchStage) {\n          case 'searching_documents':\n            return \"RailGPT Searching in Documents...\";\n          case 'searching_websites':\n            return \"RailGPT Searching in Websites...\";\n          case 'generating_answer':\n            return \"RailGPT Generating Response...\";\n          default:\n            return \"RailGPT Processing Your Query...\";\n        }\n      })(),\n      trainType: \"express\",\n      currentStage: currentSearchStage,\n      sidebarOpen: sidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChatSidebar, {\n      isOpen: sidebarOpen,\n      onToggle: () => setSidebarOpen(!sidebarOpen),\n      currentChatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || '',\n      onChatSelect: handleChatSelect,\n      onNewChat: handleNewChat\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex flex-col flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-80' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex-1 ${messages.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-4 pb-32`,\n        children: messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl font-semibold mb-3\",\n              children: \"Welcome to RailGPT!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Ask questions about Indian Railways...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `mb-4 ${message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `max-w-4xl rounded-lg p-4 transition-colors duration-300 ${message.sender === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-800 shadow-md'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: message.sender === 'user' ? 'You' : 'RailGPT'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 23\n                }, this), message.timestamp && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs ml-2 ${message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'}`,\n                  children: new Date(message.timestamp).toLocaleTimeString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 21\n              }, this), message.sender === 'user' && message.content && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 whitespace-pre-wrap\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 23\n              }, this), message.sender === 'ai' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: ((_message$document_ans, _message$website_answ, _message$document_sou, _message$website_sour) => {\n                  // Only hide if this specific message is loading AND has no content yet\n                  if (message.loading && showTrainLoader && !message.content && !message.document_answer && !message.website_answer) {\n                    return null;\n                  }\n\n                  // Process sources with improved deduplication\n                  const documentSourceItems = processDocumentSources(message.document_sources);\n                  const websiteSourceItems = processWebsiteSources(message.website_sources);\n\n                  // Check what content is available for conditional display\n                  const hasDocumentContent = !!(message.document_answer && message.document_answer.trim() !== \"\");\n                  const hasWebsiteContent = !!(message.website_answer && message.website_answer.trim() !== \"\");\n                  // LLM fallback happens if NEITHER document sources NOR website sources are found, or if explicitly set\n                  const hasLLMFallback = !hasDocumentContent && !hasWebsiteContent || message.llm_fallback;\n\n                  // Debug logging for rendering\n                  console.log(`🔍 Rendering message ${message.id}:`, {\n                    hasDocumentContent,\n                    hasWebsiteContent,\n                    hasLLMFallback,\n                    documentAnswerLength: ((_message$document_ans = message.document_answer) === null || _message$document_ans === void 0 ? void 0 : _message$document_ans.length) || 0,\n                    websiteAnswerLength: ((_message$website_answ = message.website_answer) === null || _message$website_answ === void 0 ? void 0 : _message$website_answ.length) || 0,\n                    documentSourcesCount: documentSourceItems.length,\n                    websiteSourcesCount: websiteSourceItems.length,\n                    rawDocumentAnswer: message.document_answer ? 'EXISTS' : 'MISSING',\n                    rawWebsiteAnswer: message.website_answer ? 'EXISTS' : 'MISSING',\n                    rawDocumentSources: ((_message$document_sou = message.document_sources) === null || _message$document_sou === void 0 ? void 0 : _message$document_sou.length) || 0,\n                    rawWebsiteSources: ((_message$website_sour = message.website_sources) === null || _message$website_sour === void 0 ? void 0 : _message$website_sour.length) || 0\n                  });\n\n                  // Get the user's question for context - find the most recent user message before this AI message\n                  const currentMessageIndex = messages.findIndex(m => m.id === message.id);\n                  let userQuestion = '';\n\n                  // Look backwards from current AI message to find the most recent user message\n                  for (let i = currentMessageIndex - 1; i >= 0; i--) {\n                    if (messages[i].sender === 'user' && messages[i].content) {\n                      userQuestion = messages[i].content;\n                      break;\n                    }\n                  }\n                  console.log('🔍 DEBUG: Found user question for AI message:', {\n                    aiMessageId: message.id,\n                    userQuestion\n                  });\n\n                  // Conditional display logic based on answer sources\n                  const components = [];\n                  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                  let answerSource = '';\n\n                  // Case 1: Answer exists from both uploaded documents and websites\n                  if (hasDocumentContent && hasWebsiteContent) {\n                    answerSource = 'document_and_website';\n\n                    // Document answer card\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    const documentName = documentSourceItems.length === 1 ? documentSourceItems[0].text.split(' – ')[0] : 'Uploaded Documents';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-blue-800 text-sm mb-3 flex items-center\",\n                        children: [\"\\uD83D\\uDCC4 Answer from \", documentName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 711,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.document_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 714,\n                        columnNumber: 33\n                      }, this), (_message$document_sou2 => {\n                        const visualSources = ((_message$document_sou2 = message.document_sources) === null || _message$document_sou2 === void 0 ? void 0 : _message$document_sou2.filter(source => typeof source === 'object' && source.visual_content)) || [];\n\n                        // First try to find sources that match the user's specific request\n                        let relevantSources = visualSources.filter(source => filterVisualContent(source, userQuestion));\n\n                        // If no specific matches and user asked for images, show any available images\n                        if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                          relevantSources = visualSources.filter(source => source.content_type === 'image');\n                          console.log('🔍 DEBUG: No specific project matches, showing all available images:', relevantSources.length);\n                        }\n                        return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                            className: \"text-sm font-semibold text-blue-800 mb-3\",\n                            children: \"\\uD83D\\uDCCA Visual Content:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 742,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"space-y-3\",\n                            children: relevantSources.map((source, index) => /*#__PURE__*/_jsxDEV(VisualContent, {\n                              source: source\n                            }, index, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 745,\n                              columnNumber: 43\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 743,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 741,\n                          columnNumber: 37\n                        }, this) : null;\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-blue-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-blue-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 753,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: documentSourceItems,\n                          maxVisible: 5\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 754,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 752,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"document-card\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 710,\n                      columnNumber: 31\n                    }, this));\n\n                    // Website answer card\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-green-800 text-sm mb-3 flex items-center\",\n                        children: [\"\\uD83C\\uDF10 Answer from \", websiteLabel]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 765,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.website_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 768,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-green-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-green-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 775,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: websiteSourceItems,\n                          maxVisible: 3\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 776,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 774,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"website-card\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 764,\n                      columnNumber: 31\n                    }, this));\n\n                    // Case 2: Answer exists only from documents\n                  } else if (hasDocumentContent) {\n                    answerSource = 'document_only';\n\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    const documentName = documentSourceItems.length === 1 ? documentSourceItems[0].text.split(' – ')[0] : 'Uploaded Documents';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-blue-800 text-sm mb-3 flex items-center\",\n                        children: \"\\uD83D\\uDCC4 Answer from Uploaded Documents\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 792,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.document_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 795,\n                        columnNumber: 33\n                      }, this), (_message$document_sou3 => {\n                        const visualSources = ((_message$document_sou3 = message.document_sources) === null || _message$document_sou3 === void 0 ? void 0 : _message$document_sou3.filter(source => typeof source === 'object' && source.visual_content)) || [];\n\n                        // First try to find sources that match the user's specific request\n                        let relevantSources = visualSources.filter(source => filterVisualContent(source, userQuestion));\n\n                        // If no specific matches and user asked for images, show any available images\n                        if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                          relevantSources = visualSources.filter(source => source.content_type === 'image');\n                          console.log('🔍 DEBUG: No specific project matches for document-only case, showing all available images:', relevantSources.length);\n                        }\n                        return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                            className: \"text-sm font-semibold text-blue-800 mb-3\",\n                            children: \"\\uD83D\\uDCCA Visual Content:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 823,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"space-y-3\",\n                            children: relevantSources.map((source, index) => /*#__PURE__*/_jsxDEV(VisualContent, {\n                              source: source\n                            }, index, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 826,\n                              columnNumber: 43\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 824,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 822,\n                          columnNumber: 37\n                        }, this) : null;\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-blue-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-blue-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 834,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: documentSourceItems,\n                          maxVisible: 5\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 835,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 833,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"document-priority\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 791,\n                      columnNumber: 31\n                    }, this));\n\n                    // Case 3: Answer exists only from websites\n                  } else if (hasWebsiteContent) {\n                    answerSource = 'website_only';\n\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-green-800 text-sm mb-3 flex items-center\",\n                        children: \"\\uD83C\\uDF10 Answer from Extracted Websites\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 849,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.website_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 852,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-green-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-green-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 859,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: websiteSourceItems,\n                          maxVisible: 3\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 860,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 858,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"website-priority\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 848,\n                      columnNumber: 31\n                    }, this));\n\n                    // Case 4: No answer from either - fallback to LLM\n                  } else if (hasLLMFallback) {\n                    var _message$llm_model, _message$llm_model2, _message$llm_model3, _message$llm_model4, _message$llm_model5, _message$llm_model6;\n                    answerSource = 'llm';\n                    const modelName = message.llm_model || 'Gemini';\n                    const modelLogo = (_message$llm_model = message.llm_model) !== null && _message$llm_model !== void 0 && _message$llm_model.includes('chatgpt') ? '🤖' : (_message$llm_model2 = message.llm_model) !== null && _message$llm_model2 !== void 0 && _message$llm_model2.includes('groq') ? '⚡' : (_message$llm_model3 = message.llm_model) !== null && _message$llm_model3 !== void 0 && _message$llm_model3.includes('deepseek') ? '🔍' : (_message$llm_model4 = message.llm_model) !== null && _message$llm_model4 !== void 0 && _message$llm_model4.includes('qwen') ? '🌐' : (_message$llm_model5 = message.llm_model) !== null && _message$llm_model5 !== void 0 && _message$llm_model5.includes('ollama') ? '🏠' : (_message$llm_model6 = message.llm_model) !== null && _message$llm_model6 !== void 0 && _message$llm_model6.includes('huggingface') ? '🤗' : '🧠';\n\n                    // Only show the LLM fallback card if not in loading state\n                    if (!showTrainLoader || !message.loading) {\n                      components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"font-semibold text-purple-800 text-sm mb-3 flex items-center\",\n                          children: [modelLogo, \" Answer generated by \", modelName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 881,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                          content: message.content || \"I couldn't find any relevant information to answer your question.\",\n                          query: userQuestion,\n                          model: message.llm_model || 'Gemini',\n                          chatId: message.chatId\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 884,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-3 pt-3 border-t border-purple-200\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-purple-600 italic\",\n                            children: \"This answer was generated by an AI model as no relevant information was found in your documents or websites.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 891,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 890,\n                          columnNumber: 33\n                        }, this)]\n                      }, \"llm-fallback\", true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 880,\n                        columnNumber: 31\n                      }, this));\n                    }\n\n                    // Case 5: No sources found and fallback disabled (or similar edge case)\n                  } else {\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    answerSource = 'no_results';\n\n                    // Only show the \"no results\" card if not in loading state\n                    if (!showTrainLoader || !message.loading) {\n                      components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-600 mb-2\",\n                          children: \"No sources found\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 908,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                          content: message.content || \"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\",\n                          query: userQuestion,\n                          model: message.llm_model || 'Gemini',\n                          chatId: message.chatId\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 909,\n                          columnNumber: 33\n                        }, this)]\n                      }, \"no-results\", true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 907,\n                        columnNumber: 31\n                      }, this));\n                    }\n                  }\n\n                  // If we have components to display, render them\n                  if (components.length > 0) {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3\",\n                      children: components\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 923,\n                      columnNumber: 31\n                    }, this);\n                  }\n\n                  // Fallback for any unhandled edge cases (should rarely happen)\n                  console.warn(\"Frontend: Unhandled rendering case\");\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 mb-2\",\n                      children: \"Rendering Error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 933,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                      content: message.content || \"An error occurred while rendering the response.\",\n                      query: userQuestion,\n                      model: message.llm_model || 'Gemini',\n                      chatId: message.chatId\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 29\n                  }, this);\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 19\n            }, this)\n          }, message.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef,\n            style: {\n              float: 'left',\n              clear: 'both'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 948,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen ? 'lg:left-80 left-0' : 'left-0'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSendMessage,\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative flex\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: input,\n              onChange: e => {\n                const newValue = e.target.value;\n                setInput(newValue);\n                // Don't handle command shortcuts as you type, only on submit\n              },\n              placeholder: \"Type your message... (/model, /reset, /clear)\",\n              className: \"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\",\n              disabled: isSubmitting,\n              \"aria-label\": \"Message input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(LLMSelector, {\n              currentModel: activeLLMModel,\n              onModelChange: modelId => setActiveLLMModel(modelId),\n              isLoading: isSubmitting\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isSubmitting || !input.trim(),\n              className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\",\n              title: \"Send message\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isSubmitting ? \"Sending...\" : \"Send\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 978,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 972,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 955,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-400 mt-1 text-center\",\n          children: [\"Current model: \", ((_DEFAULT_LLM_MODELS$f = DEFAULT_LLM_MODELS.find(m => m.id === activeLLMModel)) === null || _DEFAULT_LLM_MODELS$f === void 0 ? void 0 : _DEFAULT_LLM_MODELS$f.name) || activeLLMModel]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 23\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 954,\n        columnNumber: 9\n      }, this), messages.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-36\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 993,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 569,\n    columnNumber: 5\n  }, this);\n}\nfunction App({\n  sidebarOpen,\n  setSidebarOpen\n}) {\n  return /*#__PURE__*/_jsxDEV(ChatInterface, {\n    sidebarOpen: sidebarOpen,\n    setSidebarOpen: setSidebarOpen\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1006,\n    columnNumber: 5\n  }, this);\n}\nexport default App;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "LLMSelector", "DEFAULT_LLM_MODELS", "InteractiveAnswer", "ChatSidebar", "TrainLoader", "VisualContent", "useChatContext", "jsxDEV", "_jsxDEV", "ChatInterface", "sidebarOpen", "setSidebarOpen", "_DEFAULT_LLM_MODELS$f", "currentSession", "messages", "createNewChat", "loadChatSession", "addMessage", "updateMessage", "clearCurrentChat", "input", "setInput", "isSubmitting", "setIsSubmitting", "activeLLMModel", "setActiveLLMModel", "showTrainLoader", "setShowTrainLoader", "currentSearchStage", "setCurrentSearchStage", "messagesEndRef", "current", "scrollIntoView", "behavior", "shouldShowVisualContent", "userQuestion", "documentAnswer", "userQuery", "toLowerCase", "includes", "askedForImages", "askedForTableData", "askedToShowImages", "answerLower", "hasTableInAnswer", "length", "hasImageDescription", "answerHasContent", "shouldShow", "filterVisualContent", "source", "visual_content", "userQueryLower", "sourceObj", "projectMatch", "match", "quotationMatch", "logoQueryPatterns", "companyMatch", "pattern", "trim", "visualContentStr", "JSON", "stringify", "companyNameLower", "isImage", "content_type", "_sourceObj$visual_con", "_sourceObj$visual_con2", "_sourceObj$visual_con3", "hasCompanyInContent", "hasCompanyInFilename", "filename", "detectedCompanies", "detected_companies", "hasDetectedCompany", "some", "company", "ocrText", "ocr_text", "hasOcrMatch", "isLogo", "is_logo", "isRelevantLogo", "console", "log", "should_show", "_sourceObj$visual_con4", "_sourceObj$visual_con5", "projectNumber", "hasProjectInContent", "hasProjectInFilename", "isOnProjectPage", "page", "Math", "abs", "parseInt", "hasProjectContext", "project_context", "isRelevantProject", "visualContentStr_sample", "substring", "quotationNumber", "isTable", "isRelevantQuotation", "handleCommandShortcut", "startsWith", "command", "split", "modelArg", "matchedModel", "find", "m", "name", "id", "enabled", "handleSendMessage", "e", "preventDefault", "result", "sendUserMessage", "messageText", "_messagesEndRef$curre", "userMessage", "Date", "now", "content", "sender", "timestamp", "toISOString", "chatId", "messageId", "tempAiMessage", "loading", "llm_model", "_messagesEndRef$curre2", "_response$document_so", "_response$website_sou", "_messagesEndRef$curre3", "setTimeout", "response", "aiMessage", "answer", "document_answer", "website_answer", "sources", "document_sources", "website_sources", "llm_fallback", "document_sources_count", "website_sources_count", "for<PERSON>ach", "index", "has_visual_content", "storage_url", "display_type", "visual_content_keys", "Object", "keys", "error", "errorMessage", "processDocumentSources", "groupedSources", "pages", "push", "values", "map", "group", "sortedPages", "sort", "a", "b", "pageText", "join", "text", "link", "encodeURIComponent", "isDocument", "processWebsiteSources", "uniqueUrls", "Set", "processed", "url", "displayText", "url<PERSON>bj", "URL", "hostname", "replace", "has", "add", "SourceList", "items", "maxVisible", "expanded", "setExpanded", "visibleItems", "slice", "hasMore", "handleDocumentClick", "className", "children", "item", "href", "target", "rel", "title", "onClick", "undefined", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleChatSelect", "chatSession", "model_used", "handleNewChat", "isVisible", "message", "trainType", "currentStage", "isOpen", "onToggle", "currentChatId", "onChatSelect", "onNewChat", "toLocaleTimeString", "_message$document_ans", "_message$website_answ", "_message$document_sou", "_message$website_sour", "documentSourceItems", "websiteSourceItems", "hasDocumentContent", "has<PERSON>ebsite<PERSON><PERSON>nt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentAnswerLength", "websiteAnswerLength", "documentSourcesCount", "websiteSourcesCount", "rawDocumentAnswer", "rawWebsiteAnswer", "rawDocumentSources", "rawWebsiteSources", "currentMessageIndex", "findIndex", "i", "aiMessageId", "components", "answerSource", "documentName", "query", "model", "_message$document_sou2", "visualSources", "filter", "relevantSources", "websiteLabel", "_message$document_sou3", "_message$llm_model", "_message$llm_model2", "_message$llm_model3", "_message$llm_model4", "_message$llm_model5", "_message$llm_model6", "modelName", "modelLogo", "warn", "ref", "style", "float", "clear", "onSubmit", "type", "value", "onChange", "newValue", "placeholder", "disabled", "currentModel", "onModelChange", "modelId", "isLoading", "App"], "sources": ["C:/IR App/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './App.css';\nimport { sendQuery } from './services/api';\nimport LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';\nimport InteractiveAnswer from './components/ui/InteractiveAnswer';\nimport ChatSidebar from './components/chat/ChatSidebar';\nimport TrainLoader from './components/ui/TrainLoader';\nimport VisualContent from './components/ui/VisualContent';\nimport { useChatContext } from './contexts/ChatContext';\nimport { ChatSession, ChatMessage } from './services/supabase';\n\ninterface Source {\n  source_type: string;\n  filename?: string;\n  page?: number;\n  url?: string;\n  link?: string; // For document viewer links\n  name?: string; // For display name\n  // Visual content fields\n  content_type?: string;  // \"text\", \"table\", \"image\", \"chart_diagram\"\n  visual_content?: Record<string, any>;  // Visual content metadata\n  storage_url?: string;  // URL for stored visual content\n  display_type?: string;  // \"text\", \"html_table\", \"image\", \"base64_image\"\n}\n\n// Using ChatMessage interface from services/supabase.ts\n\ninterface ChatInterfaceProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction ChatInterface({ sidebarOpen, setSidebarOpen }: ChatInterfaceProps) {\n  const {\n    currentSession,\n    messages,\n    createNewChat,\n    loadChatSession,\n    addMessage,\n    updateMessage,\n    clearCurrentChat\n  } = useChatContext();\n\n  const [input, setInput] = useState<string>('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model\n  const [showTrainLoader, setShowTrainLoader] = useState(false);\n  const [currentSearchStage, setCurrentSearchStage] = useState<'initializing' | 'searching_documents' | 'searching_websites' | 'generating_answer' | 'complete'>('initializing');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Scroll to bottom of chat whenever messages change\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  // Helper function to determine if visual content should be shown\n  const shouldShowVisualContent = (userQuestion: string, documentAnswer: string): boolean => {\n    const userQuery = userQuestion.toLowerCase();\n\n    // Always show visual content when user asks for images\n    if (userQuery.includes('image')) {\n      return true;\n    }\n    \n    // Detect different types of requests\n    const askedForImages = userQuery.includes('image') || userQuery.includes('picture') || userQuery.includes('diagram') || userQuery.includes('chart');\n    const askedForTableData = (userQuery.includes('table') || userQuery.includes('quotation')) && \n                             (userQuery.includes('give me') || userQuery.includes('show me table') || userQuery.includes('get') || userQuery.includes('provide'));\n    const askedToShowImages = userQuery.includes('show me image') || userQuery.includes('display image') || userQuery.includes('see image');\n    \n    const answerLower = documentAnswer?.toLowerCase() || '';\n    const hasTableInAnswer = answerLower.includes('<table>') || answerLower.includes('|') || \n                            (answerLower.includes('table') && answerLower.length > 200);\n    const hasImageDescription = answerLower.includes('image') || answerLower.includes('figure') || answerLower.includes('diagram');\n    const answerHasContent = answerLower.length > 50;\n    \n    // ALWAYS show visual content if user specifically asks to see images/visuals\n    if (askedToShowImages || (askedForImages && (userQuery.includes('show') || userQuery.includes('display')))) {\n      return true;\n    }\n\n    // NEVER show if user asked for table data (they want the actual data, not images)\n    if (askedForTableData && !askedForImages) {\n      return false;\n    }\n    \n    // For other cases, show only if answer doesn't already provide adequate information\n    const shouldShow = !hasTableInAnswer && // Don't show if answer already has table data\n                      !hasImageDescription && // Don't show if answer already describes images well\n                      (\n                        (askedForImages && !answerHasContent) || // Show images if user asked and answer is short\n                        (!answerHasContent && answerLower.length < 50) // Show for very short answers\n                      );\n    \n    return shouldShow;\n  };\n\n  // Helper function to filter visual content types based on user query\n  const filterVisualContent = (source: Source | string, userQuestion: string) => {\n    if (typeof source !== 'object' || !source.visual_content) {\n      return false;\n    }\n\n    const userQueryLower = userQuestion.toLowerCase();\n    const sourceObj = source as Source;\n    \n    // Extract specific project/quotation numbers from user query\n    const projectMatch = userQueryLower.match(/project\\s*(\\d+)/);\n    const quotationMatch = userQueryLower.match(/quotation\\s*(\\d+)/);\n    \n    // Extract company names for logo queries\n    const logoQueryPatterns = [\n      /(?:logo\\s+(?:of\\s+)?|show\\s+me\\s+(?:the\\s+)?logo\\s+(?:of\\s+)?)([A-Z][A-Za-z\\s&]+?)(?:\\s+logo|\\s+enterprises|\\s+company|\\s+corp|\\s+ltd|\\s+inc|\\s*$)/i,\n      /([A-Z][A-Za-z\\s&]+?)\\s+(?:enterprises|company|corp|ltd|inc)\\s+logo/i,\n      /([A-Z][A-Za-z\\s&]+?)\\s+logo/i,\n    ];\n    \n    let companyMatch = null;\n    for (const pattern of logoQueryPatterns) {\n      const match = userQuestion.match(pattern);\n      if (match) {\n        companyMatch = match[1].trim();\n        break;\n      }\n    }\n    \n    // Check if the visual content contains relevant information for the specific request\n    const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();\n    \n    // If user asked for a specific company logo\n    if (companyMatch && userQueryLower.includes('logo')) {\n      const companyNameLower = companyMatch.toLowerCase();\n      const isImage = sourceObj.content_type === 'image';\n      \n\n      \n      if (isImage) {\n        // Check if this image contains the requested company\n        const hasCompanyInContent = visualContentStr.includes(companyNameLower);\n        const hasCompanyInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(companyNameLower);\n        \n        // Check detected companies from OCR\n        const detectedCompanies: string[] = sourceObj.visual_content?.detected_companies || [];\n        const hasDetectedCompany = detectedCompanies.some((company: string) =>\n          company.toLowerCase().includes(companyNameLower) ||\n          companyNameLower.includes(company.toLowerCase())\n        );\n        \n        // Check OCR text\n        const ocrText = sourceObj.visual_content?.ocr_text || '';\n        const hasOcrMatch = ocrText.toLowerCase().includes(companyNameLower);\n        \n        // Check if marked as logo\n        const isLogo = sourceObj.visual_content?.is_logo || false;\n        \n        const isRelevantLogo = hasCompanyInContent || hasCompanyInFilename || hasDetectedCompany || hasOcrMatch || isLogo;\n        \n        console.log('🔍 DEBUG: Company logo filter result:', {\n          company: companyMatch,\n          hasCompanyInContent,\n          hasCompanyInFilename,\n          hasDetectedCompany,\n          hasOcrMatch,\n          isLogo,\n          isRelevantLogo,\n          should_show: isImage && isRelevantLogo\n        });\n        \n        return isRelevantLogo;\n      }\n      \n      return false; // Not an image for logo query\n    }\n    \n    // If user asked for a specific project image\n    if (projectMatch && userQueryLower.includes('image')) {\n      const projectNumber = projectMatch[1];\n      const isImage = sourceObj.content_type === 'image';\n      \n      // Check if this image is related to the specific project\n      const hasProjectInContent = visualContentStr.includes(`project ${projectNumber}`) || \n                                  visualContentStr.includes(`project${projectNumber}`);\n      const hasProjectInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(`project ${projectNumber}`);\n      const isOnProjectPage = sourceObj.page && Math.abs(sourceObj.page - parseInt(projectNumber)) <= 1; // Allow adjacent pages\n      const hasProjectContext = sourceObj.visual_content?.project_context && \n                               sourceObj.visual_content.project_context.includes(`project ${projectNumber}`);\n      \n      const isRelevantProject = hasProjectInContent || hasProjectInFilename || isOnProjectPage || hasProjectContext;\n      \n      console.log('🔍 DEBUG: Project', projectNumber, 'image filter:', {\n        isImage,\n        hasProjectInContent,\n        hasProjectInFilename,\n        isOnProjectPage,\n        hasProjectContext,\n        isRelevantProject,\n        visualContentStr_sample: visualContentStr.substring(0, 150),\n        project_context: sourceObj.visual_content?.project_context,\n        page: sourceObj.page,\n        should_show: isImage && isRelevantProject\n      });\n      \n      // If this is an image and it's relevant to the project, show it\n      // If no images are relevant, we'll fall back to showing all images\n      return isImage && isRelevantProject;\n    }\n    \n    // If user asked for a specific quotation table\n    if (quotationMatch && userQueryLower.includes('table')) {\n      const quotationNumber = quotationMatch[1];\n      const isTable = sourceObj.content_type === 'table';\n      \n      // Check if this table is related to the specific quotation\n      const isRelevantQuotation = visualContentStr.includes(`quotation ${quotationNumber}`) || \n                                  visualContentStr.includes(`quotation${quotationNumber}`) ||\n                                  visualContentStr.includes(`quote ${quotationNumber}`);\n      \n      console.log('🔍 DEBUG: Quotation', quotationNumber, 'table filter:', {\n        isTable,\n        isRelevantQuotation,\n        should_show: isTable && isRelevantQuotation\n      });\n      \n      return isTable && isRelevantQuotation;\n    }\n    \n    // If user specifically asked for images (but no specific project), only show images\n    if (userQueryLower.includes('image') && !userQueryLower.includes('table')) {\n      const isImage = sourceObj.content_type === 'image';\n      console.log('🔍 DEBUG: User asked for images only, source is image:', isImage);\n      return isImage;\n    }\n    \n    // If user specifically asked for tables (but no specific quotation), only show tables\n    if (userQueryLower.includes('table') && !userQueryLower.includes('image')) {\n      const isTable = sourceObj.content_type === 'table';\n      console.log('🔍 DEBUG: User asked for tables only, source is table:', isTable);\n      return isTable;\n    }\n    \n    // Default: show all visual content\n    console.log('🔍 DEBUG: Showing all visual content by default');\n    return true;\n  };\n\n  // Handle command shortcuts in the textbox\n  const handleCommandShortcut = (input: string) => {\n    // Check if the input is a command\n    if (input.startsWith('/')) {\n      const command = input.split(' ')[0].toLowerCase();\n\n      // Command: /model <model-name>\n      if (command === '/model') {\n        const modelArg = input.substring(7).trim();\n        const matchedModel = DEFAULT_LLM_MODELS.find(m =>\n          m.name.toLowerCase().includes(modelArg.toLowerCase()) ||\n          m.id.toLowerCase().includes(modelArg.toLowerCase())\n        );\n\n        if (matchedModel && matchedModel.enabled) {\n          setActiveLLMModel(matchedModel.id);\n          setInput('');\n          return 'processed';\n        }\n      }\n\n      // Command: /reset or /clear - clear chat history\n      else if (command === '/reset' || command === '/clear') {\n        clearCurrentChat();\n        setInput('');\n        return 'processed';\n      }\n    }\n\n    return 'not_processed';\n  };\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isSubmitting) return;\n\n    // Handle command shortcuts like /reset, /model, etc.\n    if (input.startsWith('/')) {\n      const result = handleCommandShortcut(input);\n      if (result === 'processed') {\n        setInput('');\n        return;\n      }\n      // If not processed as a command, continue as a regular message\n    }\n\n    return await sendUserMessage(input);\n  };\n\n  const sendUserMessage = async (messageText: string) => {\n    // Create new chat session if none exists\n    if (!currentSession) {\n      await createNewChat();\n    }\n\n    const userMessage: ChatMessage = {\n      id: `user-${Date.now()}`,\n      content: messageText,\n      sender: 'user',\n      timestamp: new Date().toISOString(),\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(userMessage);\n    setInput('');\n\n    const messageId = Date.now();\n    const tempAiMessage: ChatMessage = {\n      id: `ai-${messageId}`,\n      content: '',\n      sender: 'ai',\n      loading: true,\n      timestamp: new Date().toISOString(),\n      llm_model: activeLLMModel,\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(tempAiMessage);\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n    setIsSubmitting(true);\n    setShowTrainLoader(true);\n    setCurrentSearchStage('initializing');\n\n    try {\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n      // Simulate search progress updates with train loader\n      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);\n      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);\n      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);\n\n      const response = await sendQuery(messageText, activeLLMModel);\n\n      // Create the AI message based on strict priority logic\n      const aiMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: response.answer,\n        document_answer: response.document_answer,\n        website_answer: response.website_answer,\n        llm_model: response.llm_model || activeLLMModel,\n        sender: 'ai',\n        sources: response.sources,\n        document_sources: response.document_sources,\n        website_sources: response.website_sources,\n        chatId: currentSession?.id || 'temp',\n        llm_fallback: response.llm_fallback,\n      };\n\n      // Debug logging\n      console.log('AI Message created:', {\n        document_answer: response.document_answer ? 'EXISTS' : 'MISSING',\n        website_answer: response.website_answer ? 'EXISTS' : 'MISSING',\n        document_sources_count: response.document_sources?.length || 0,\n        website_sources_count: response.website_sources?.length || 0,\n        llm_fallback: response.llm_fallback\n      });\n\n      // Debug visual content sources\n      console.log('🔍 DEBUG: Document sources from API:', response.document_sources);\n      if (response.document_sources) {\n        response.document_sources.forEach((source, index) => {\n          console.log(`🔍 DEBUG: Source ${index}:`, {\n            content_type: source.content_type,\n            has_visual_content: !!source.visual_content,\n            storage_url: source.storage_url,\n            display_type: source.display_type,\n            visual_content_keys: source.visual_content ? Object.keys(source.visual_content) : []\n          });\n        });\n      }\n\n      updateMessage(tempAiMessage.id, aiMessage);\n\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      // Provide more helpful error message without the specific query\n      const errorMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,\n        sender: 'ai',\n        chatId: currentSession?.id || 'temp',\n        llm_fallback: true,\n      };\n\n      updateMessage(tempAiMessage.id, errorMessage);\n    } finally {\n      setIsSubmitting(false);\n      setShowTrainLoader(false);\n      setCurrentSearchStage('complete');\n    }\n  };\n\n\n\n  const processDocumentSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Group by filename to avoid repetition\n    const groupedSources: { [key: string]: { filename: string; pages: number[] } } = {};\n\n    sources.forEach(source => {\n      let filename: string;\n      let page: number;\n\n      if (typeof source === 'string') {\n        // Parse string format like \"MaintenanceManual.pdf – Page 3\"\n        const match = source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);\n        filename = match ? match[1].trim() : source;\n        page = match && match[2] ? parseInt(match[2]) : 1;\n      } else {\n        filename = source.name || source.filename || \"Unknown Document\";\n        page = source.page || 1;\n        }\n\n      if (!groupedSources[filename]) {\n        groupedSources[filename] = { filename, pages: [] };\n      }\n\n      if (!groupedSources[filename].pages.includes(page)) {\n        groupedSources[filename].pages.push(page);\n      }\n    });\n\n    // Convert to display format with viewer links\n    return Object.values(groupedSources).map(group => {\n      const sortedPages = group.pages.sort((a, b) => a - b);\n      const pageText = sortedPages.length === 1\n        ? `Page ${sortedPages[0]}`\n        : `Pages ${sortedPages.join(', ')}`;\n\n      // Create link for document viewer that opens at exact page number\n      return {\n        text: `${group.filename} – ${pageText}`,\n        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,\n        isDocument: true\n      };\n    });\n  };\n\n  const processWebsiteSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Remove duplicates and format\n    const uniqueUrls = new Set<string>();\n    const processed: Array<{ text: string; link: string; isDocument?: boolean }> = [];\n\n    sources.forEach(source => {\n      let url: string;\n      let displayText: string;\n\n      if (typeof source === 'string') {\n        url = source.startsWith('http') ? source : `https://${source}`;\n          try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n          } catch {\n          displayText = source;\n        }\n      } else {\n        url = source.url || 'https://railgpt.indianrailways.gov.in';\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = url;\n        }\n      }\n\n      if (!uniqueUrls.has(url)) {\n        uniqueUrls.add(url);\n        processed.push({\n          text: displayText,\n          link: url,\n          isDocument: false  // Mark as website source\n        });\n        }\n    });\n\n    return processed; // Return all website sources\n  };\n\n  // Component for expandable source list with appropriate click behaviors\n  const SourceList = ({ items, maxVisible = 3 }: {\n    items: Array<{ text: string; link?: string; isDocument?: boolean }>;\n    maxVisible?: number\n  }) => {\n    const [expanded, setExpanded] = useState(items.length <= maxVisible);\n\n    if (items.length === 0) return null;\n\n    const visibleItems = expanded ? items : items.slice(0, maxVisible);\n    const hasMore = items.length > maxVisible;\n\n    const handleDocumentClick = (e: React.MouseEvent<HTMLAnchorElement>, link: string) => {\n      // If we want to handle document links in a special way, we can do so here\n      // For example, we could open a modal or new tab with the document viewer\n      // Currently, just allowing regular link behavior\n    };\n\n    return (\n      <ul className=\"text-xs list-disc pl-4 mt-1 space-y-1\">\n        {visibleItems.map((item, index) => (\n              <li key={index}>\n                {item.link ? (\n              <a\n                href={item.link}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"hover:underline text-blue-600 transition-colors duration-200\"\n                title={item.isDocument ? \"Open document at this page\" : \"Open website in new tab\"}\n                onClick={item.isDocument ? (e) => handleDocumentClick(e, item.link!) : undefined}\n              >\n                    {item.text}\n                  </a>\n                ) : (\n              <span className=\"text-gray-700\">{item.text}</span>\n                )}\n              </li>\n            ))}\n        {hasMore && !expanded && (\n          <li className=\"list-none\">\n            <button\n              onClick={() => setExpanded(true)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n            >\n              + {items.length - maxVisible} more sources\n            </button>\n          </li>\n        )}\n        {hasMore && expanded && (\n          <li className=\"list-none\">\n              <button\n                onClick={() => setExpanded(false)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n              >\n                Show less\n              </button>\n            </li>\n                )}\n      </ul>\n    );\n  };\n\n  // Sidebar handlers\n  const handleChatSelect = async (chatSession: ChatSession) => {\n    // Load the selected chat session using context\n    await loadChatSession(chatSession.id);\n    setActiveLLMModel(chatSession.model_used || 'gemini-2.0-flash');\n    setSidebarOpen(false); // Close sidebar on mobile after selection\n  };\n\n  const handleNewChat = async () => {\n    console.log('Creating new chat...');\n    await createNewChat();\n    setSidebarOpen(false); // Close sidebar on mobile after creating new chat\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-100 transition-colors duration-300\">\n      {/* Train Loader Overlay */}\n      <TrainLoader\n        isVisible={showTrainLoader}\n        message={(() => {\n          switch (currentSearchStage) {\n            case 'searching_documents':\n              return \"RailGPT Searching in Documents...\";\n            case 'searching_websites':\n              return \"RailGPT Searching in Websites...\";\n            case 'generating_answer':\n              return \"RailGPT Generating Response...\";\n            default:\n              return \"RailGPT Processing Your Query...\";\n          }\n        })()}\n        trainType=\"express\"\n        currentStage={currentSearchStage}\n        sidebarOpen={sidebarOpen}\n      />\n\n      {/* Chat Sidebar */}\n      <ChatSidebar\n        isOpen={sidebarOpen}\n        onToggle={() => setSidebarOpen(!sidebarOpen)}\n        currentChatId={currentSession?.id || ''}\n        onChatSelect={handleChatSelect}\n        onNewChat={handleNewChat}\n      />\n\n      {/* Main Chat Area */}\n      <div className={`flex flex-col flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-80' : ''}`}>\n        {/* Message Area - only scrollable when messages exist */}\n        <div className={`flex-1 ${messages.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-4 pb-32`}>\n          {messages.length === 0 ? (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center text-gray-500\">\n                <p className=\"text-xl font-semibold mb-3\">Welcome to RailGPT!</p>\n                <p>Ask questions about Indian Railways...</p>\n              </div>\n            </div>\n          ) : (\n            <div>\n              {messages.map((message) => (\n                <div\n                  key={message.id}\n                  className={`mb-4 ${\n                    message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'\n                  }`}\n                >\n                  <div\n                    className={`max-w-4xl rounded-lg p-4 transition-colors duration-300 ${\n                      message.sender === 'user'\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-white text-gray-800 shadow-md'\n                    }`}\n                  >\n                    <div className=\"flex justify-between items-start mb-1\">\n                      <span className=\"font-semibold\">\n                        {message.sender === 'user' ? 'You' : 'RailGPT'}\n                      </span>\n                      {message.timestamp && (\n                        <span className={`text-xs ml-2 ${\n                          message.sender === 'user'\n                            ? 'text-blue-100'\n                            : 'text-gray-500'\n                        }`}>\n                          {new Date(message.timestamp).toLocaleTimeString()}\n                        </span>\n                      )}\n                    </div>\n\n                    {/* Only show content directly for user messages */}\n                    {message.sender === 'user' && message.content && (\n                      <div className=\"mt-2 whitespace-pre-wrap\">{message.content}</div>\n                    )}\n\n                    {/* AI messages with strict priority display logic */}\n                    {message.sender === 'ai' && (\n                      <div>\n                        {(() => {\n                          // Only hide if this specific message is loading AND has no content yet\n                          if (message.loading && showTrainLoader && !message.content && !message.document_answer && !message.website_answer) {\n                            return null;\n                          }\n\n                          // Process sources with improved deduplication\n                          const documentSourceItems = processDocumentSources(message.document_sources);\n                          const websiteSourceItems = processWebsiteSources(message.website_sources);\n\n                          // Check what content is available for conditional display\n                          const hasDocumentContent = !!(message.document_answer && message.document_answer.trim() !== \"\");\n                          const hasWebsiteContent = !!(message.website_answer && message.website_answer.trim() !== \"\");\n                          // LLM fallback happens if NEITHER document sources NOR website sources are found, or if explicitly set\n                          const hasLLMFallback = (!hasDocumentContent && !hasWebsiteContent) || message.llm_fallback;\n\n                          // Debug logging for rendering\n                          console.log(`🔍 Rendering message ${message.id}:`, {\n                            hasDocumentContent,\n                            hasWebsiteContent,\n                            hasLLMFallback,\n                            documentAnswerLength: message.document_answer?.length || 0,\n                            websiteAnswerLength: message.website_answer?.length || 0,\n                            documentSourcesCount: documentSourceItems.length,\n                            websiteSourcesCount: websiteSourceItems.length,\n                            rawDocumentAnswer: message.document_answer ? 'EXISTS' : 'MISSING',\n                            rawWebsiteAnswer: message.website_answer ? 'EXISTS' : 'MISSING',\n                            rawDocumentSources: message.document_sources?.length || 0,\n                            rawWebsiteSources: message.website_sources?.length || 0\n                          });\n\n                          // Get the user's question for context - find the most recent user message before this AI message\n                          const currentMessageIndex = messages.findIndex(m => m.id === message.id);\n                          let userQuestion = '';\n                          \n                          // Look backwards from current AI message to find the most recent user message\n                          for (let i = currentMessageIndex - 1; i >= 0; i--) {\n                            if (messages[i].sender === 'user' && messages[i].content) {\n                              userQuestion = messages[i].content;\n                              break;\n                            }\n                          }\n                          \n                          console.log('🔍 DEBUG: Found user question for AI message:', { aiMessageId: message.id, userQuestion });\n\n                          // Conditional display logic based on answer sources\n                          const components = [];\n                          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                          let answerSource = '';\n\n                          // Case 1: Answer exists from both uploaded documents and websites\n                          if (hasDocumentContent && hasWebsiteContent) {\n                            answerSource = 'document_and_website';\n\n                            // Document answer card\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const documentName = documentSourceItems.length === 1\n                              ? documentSourceItems[0].text.split(' – ')[0]\n                              : 'Uploaded Documents';\n\n                            components.push(\n                              <div key=\"document-card\" className=\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-blue-800 text-sm mb-3 flex items-center\">\n                                  📄 Answer from {documentName}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.document_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                \n                                {/* Display Visual Content with Smart Fallback */}\n                                {(() => {\n                                  const visualSources = message.document_sources?.filter(source => \n                                    typeof source === 'object' && source.visual_content\n                                  ) || [];\n                                  \n                                  // First try to find sources that match the user's specific request\n                                  let relevantSources = visualSources.filter(source => \n                                    filterVisualContent(source as Source, userQuestion)\n                                  );\n                                  \n                                  // If no specific matches and user asked for images, show any available images\n                                  if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                                    relevantSources = visualSources.filter(source => \n                                      (source as Source).content_type === 'image'\n                                    );\n                                    console.log('🔍 DEBUG: No specific project matches, showing all available images:', relevantSources.length);\n                                  }\n                                  \n                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (\n                                    <div className=\"mt-4\">\n                                      <h5 className=\"text-sm font-semibold text-blue-800 mb-3\">📊 Visual Content:</h5>\n                                      <div className=\"space-y-3\">\n                                        {relevantSources.map((source, index) => (\n                                          <VisualContent key={index} source={source as Source} />\n                                        ))}\n                                      </div>\n                                    </div>\n                                  ) : null;\n                                })()}\n                                \n                                <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                                  <p className=\"text-xs text-blue-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={documentSourceItems} maxVisible={5} />\n                                </div>\n                              </div>\n                            );\n\n                            // Website answer card\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n\n                            components.push(\n                              <div key=\"website-card\" className=\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-green-800 text-sm mb-3 flex items-center\">\n                                  🌐 Answer from {websiteLabel}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.website_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-green-200\">\n                                  <p className=\"text-xs text-green-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={websiteSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 2: Answer exists only from documents\n                          } else if (hasDocumentContent) {\n                            answerSource = 'document_only';\n\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const documentName = documentSourceItems.length === 1\n                              ? documentSourceItems[0].text.split(' – ')[0]\n                              : 'Uploaded Documents';\n\n                            components.push(\n                              <div key=\"document-priority\" className=\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-blue-800 text-sm mb-3 flex items-center\">\n                                  📄 Answer from Uploaded Documents\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.document_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                \n                                {/* Display Visual Content with Smart Fallback */}\n                                {(() => {\n                                  const visualSources = message.document_sources?.filter(source => \n                                    typeof source === 'object' && source.visual_content\n                                  ) || [];\n                                  \n                                  // First try to find sources that match the user's specific request\n                                  let relevantSources = visualSources.filter(source => \n                                    filterVisualContent(source as Source, userQuestion)\n                                  );\n                                  \n                                  // If no specific matches and user asked for images, show any available images\n                                  if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {\n                                    relevantSources = visualSources.filter(source => \n                                      (source as Source).content_type === 'image'\n                                    );\n                                    console.log('🔍 DEBUG: No specific project matches for document-only case, showing all available images:', relevantSources.length);\n                                  }\n                                  \n                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (\n                                    <div className=\"mt-4\">\n                                      <h5 className=\"text-sm font-semibold text-blue-800 mb-3\">📊 Visual Content:</h5>\n                                      <div className=\"space-y-3\">\n                                        {relevantSources.map((source, index) => (\n                                          <VisualContent key={index} source={source as Source} />\n                                        ))}\n                                      </div>\n                                    </div>\n                                  ) : null;\n                                })()}\n                                \n                                <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                                  <p className=\"text-xs text-blue-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={documentSourceItems} maxVisible={5} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 3: Answer exists only from websites\n                          } else if (hasWebsiteContent) {\n                            answerSource = 'website_only';\n\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n\n                            components.push(\n                              <div key=\"website-priority\" className=\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-green-800 text-sm mb-3 flex items-center\">\n                                  🌐 Answer from Extracted Websites\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.website_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-green-200\">\n                                  <p className=\"text-xs text-green-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={websiteSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 4: No answer from either - fallback to LLM\n                          } else if (hasLLMFallback) {\n                            answerSource = 'llm';\n\n                            const modelName = message.llm_model || 'Gemini';\n                            const modelLogo = message.llm_model?.includes('chatgpt') ? '🤖' :\n                                            message.llm_model?.includes('groq') ? '⚡' :\n                                            message.llm_model?.includes('deepseek') ? '🔍' :\n                                            message.llm_model?.includes('qwen') ? '🌐' :\n                                            message.llm_model?.includes('ollama') ? '🏠' :\n                                            message.llm_model?.includes('huggingface') ? '🤗' : '🧠';\n\n                            // Only show the LLM fallback card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"llm-fallback\" className=\"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-purple-800 text-sm mb-3 flex items-center\">\n                                  {modelLogo} Answer generated by {modelName}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't find any relevant information to answer your question.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Gemini'}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-purple-200\">\n                                  <p className=\"text-xs text-purple-600 italic\">\n                                    This answer was generated by an AI model as no relevant information was found in your documents or websites.\n                                  </p>\n                                </div>\n                              </div>\n                            );\n                            }\n\n                          // Case 5: No sources found and fallback disabled (or similar edge case)\n                          } else {\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            answerSource = 'no_results';\n\n                            // Only show the \"no results\" card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"no-results\" className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                                <p className=\"text-sm text-gray-600 mb-2\">No sources found</p>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Gemini'}\n                                  chatId={message.chatId}\n                                />\n                              </div>\n                            );\n                            }\n                          }\n\n                          // If we have components to display, render them\n                          if (components.length > 0) {\n                            return (\n                              <div className=\"mt-3\">\n                                {components}\n                              </div>\n                            );\n                          }\n\n                          // Fallback for any unhandled edge cases (should rarely happen)\n                          console.warn(\"Frontend: Unhandled rendering case\");\n                          return (\n                            <div className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                              <p className=\"text-sm text-gray-600 mb-2\">Rendering Error</p>\n                              <InteractiveAnswer\n                                content={message.content || \"An error occurred while rendering the response.\"}\n                                query={userQuestion}\n                                model={message.llm_model || 'Gemini'}\n                                chatId={message.chatId}\n                              />\n                            </div>\n                          );\n                        })()}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n              <div ref={messagesEndRef} style={{ float: 'left', clear: 'both' }} />\n            </div>\n          )}\n        </div>\n\n        {/* Fixed Chat Input Box at Bottom - using fixed positioning */}\n        <div className={`border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen ? 'lg:left-80 left-0' : 'left-0'}`}>\n          <form onSubmit={handleSendMessage} className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 relative flex\">\n              <input\n                type=\"text\"\n                value={input}\n                onChange={(e) => {\n                  const newValue = e.target.value;\n                  setInput(newValue);\n                  // Don't handle command shortcuts as you type, only on submit\n                }}\n                placeholder=\"Type your message... (/model, /reset, /clear)\"\n                className=\"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\n                disabled={isSubmitting}\n                aria-label=\"Message input\"\n              />\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <LLMSelector\n                currentModel={activeLLMModel}\n                onModelChange={(modelId) => setActiveLLMModel(modelId)}\n                isLoading={isSubmitting}\n              />\n              <button\n                type=\"submit\"\n                disabled={isSubmitting || !input.trim()}\n                className=\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\"\n                title=\"Send message\"\n              >\n                <span>{isSubmitting ? \"Sending...\" : \"Send\"}</span>\n              </button>\n            </div>\n          </form>\n                      <div className=\"text-xs text-gray-400 mt-1 text-center\">\n            Current model: {DEFAULT_LLM_MODELS.find(m => m.id === activeLLMModel)?.name || activeLLMModel}\n          </div>\n        </div>\n        {/* Spacer div to push content up above the fixed input box - only needed when there are messages */}\n        {messages.length > 0 && <div className=\"h-36\"></div>}\n      </div>\n    </div>\n  );\n}\n\ninterface AppProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction App({ sidebarOpen, setSidebarOpen }: AppProps) {\n  return (\n    <ChatInterface sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,WAAW;AAClB,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,WAAW,IAAIC,kBAAkB,QAAQ,6BAA6B;AAC7E,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,cAAc,QAAQ,wBAAwB;;AAiBvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,SAASC,aAAaA,CAAC;EAAEC,WAAW;EAAEC;AAAmC,CAAC,EAAE;EAAA,IAAAC,qBAAA;EAC1E,MAAM;IACJC,cAAc;IACdC,QAAQ;IACRC,aAAa;IACbC,eAAe;IACfC,UAAU;IACVC,aAAa;IACbC;EACF,CAAC,GAAGb,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC;EAC1E,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAmG,cAAc,CAAC;EAC9K,MAAMkC,cAAc,GAAGjC,MAAM,CAAiB,IAAI,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgC,cAAc,CAACC,OAAO,EAAE;MAC1BD,cAAc,CAACC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAACnB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMoB,uBAAuB,GAAGA,CAACC,YAAoB,EAAEC,cAAsB,KAAc;IACzF,MAAMC,SAAS,GAAGF,YAAY,CAACG,WAAW,CAAC,CAAC;;IAE5C;IACA,IAAID,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC/B,OAAO,IAAI;IACb;;IAEA;IACA,MAAMC,cAAc,GAAGH,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC;IACnJ,MAAME,iBAAiB,GAAG,CAACJ,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC,MAC/DF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC7J,MAAMG,iBAAiB,GAAGL,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC;IAEvI,MAAMI,WAAW,GAAG,CAAAP,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEE,WAAW,CAAC,CAAC,KAAI,EAAE;IACvD,MAAMM,gBAAgB,GAAGD,WAAW,CAACJ,QAAQ,CAAC,SAAS,CAAC,IAAII,WAAW,CAACJ,QAAQ,CAAC,GAAG,CAAC,IAC5DI,WAAW,CAACJ,QAAQ,CAAC,OAAO,CAAC,IAAII,WAAW,CAACE,MAAM,GAAG,GAAI;IACnF,MAAMC,mBAAmB,GAAGH,WAAW,CAACJ,QAAQ,CAAC,OAAO,CAAC,IAAII,WAAW,CAACJ,QAAQ,CAAC,QAAQ,CAAC,IAAII,WAAW,CAACJ,QAAQ,CAAC,SAAS,CAAC;IAC9H,MAAMQ,gBAAgB,GAAGJ,WAAW,CAACE,MAAM,GAAG,EAAE;;IAEhD;IACA,IAAIH,iBAAiB,IAAKF,cAAc,KAAKH,SAAS,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAE,EAAE;MAC1G,OAAO,IAAI;IACb;;IAEA;IACA,IAAIE,iBAAiB,IAAI,CAACD,cAAc,EAAE;MACxC,OAAO,KAAK;IACd;;IAEA;IACA,MAAMQ,UAAU,GAAG,CAACJ,gBAAgB;IAAI;IACtB,CAACE,mBAAmB;IAAI;;IAErBN,cAAc,IAAI,CAACO,gBAAgB;IAAK;IACxC,CAACA,gBAAgB,IAAIJ,WAAW,CAACE,MAAM,GAAG,EAAG,CAAC;IAAA,CAChD;IAEnB,OAAOG,UAAU;EACnB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAACC,MAAuB,EAAEf,YAAoB,KAAK;IAC7E,IAAI,OAAOe,MAAM,KAAK,QAAQ,IAAI,CAACA,MAAM,CAACC,cAAc,EAAE;MACxD,OAAO,KAAK;IACd;IAEA,MAAMC,cAAc,GAAGjB,YAAY,CAACG,WAAW,CAAC,CAAC;IACjD,MAAMe,SAAS,GAAGH,MAAgB;;IAElC;IACA,MAAMI,YAAY,GAAGF,cAAc,CAACG,KAAK,CAAC,iBAAiB,CAAC;IAC5D,MAAMC,cAAc,GAAGJ,cAAc,CAACG,KAAK,CAAC,mBAAmB,CAAC;;IAEhE;IACA,MAAME,iBAAiB,GAAG,CACxB,qJAAqJ,EACrJ,qEAAqE,EACrE,8BAA8B,CAC/B;IAED,IAAIC,YAAY,GAAG,IAAI;IACvB,KAAK,MAAMC,OAAO,IAAIF,iBAAiB,EAAE;MACvC,MAAMF,KAAK,GAAGpB,YAAY,CAACoB,KAAK,CAACI,OAAO,CAAC;MACzC,IAAIJ,KAAK,EAAE;QACTG,YAAY,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;QAC9B;MACF;IACF;;IAEA;IACA,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,SAAS,CAACV,SAAS,CAACF,cAAc,IAAI,CAAC,CAAC,CAAC,CAACb,WAAW,CAAC,CAAC;;IAErF;IACA,IAAIoB,YAAY,IAAIN,cAAc,CAACb,QAAQ,CAAC,MAAM,CAAC,EAAE;MACnD,MAAMyB,gBAAgB,GAAGN,YAAY,CAACpB,WAAW,CAAC,CAAC;MACnD,MAAM2B,OAAO,GAAGZ,SAAS,CAACa,YAAY,KAAK,OAAO;MAIlD,IAAID,OAAO,EAAE;QAAA,IAAAE,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACX;QACA,MAAMC,mBAAmB,GAAGT,gBAAgB,CAACtB,QAAQ,CAACyB,gBAAgB,CAAC;QACvE,MAAMO,oBAAoB,GAAGlB,SAAS,CAACmB,QAAQ,IAAInB,SAAS,CAACmB,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyB,gBAAgB,CAAC;;QAE9G;QACA,MAAMS,iBAA2B,GAAG,EAAAN,qBAAA,GAAAd,SAAS,CAACF,cAAc,cAAAgB,qBAAA,uBAAxBA,qBAAA,CAA0BO,kBAAkB,KAAI,EAAE;QACtF,MAAMC,kBAAkB,GAAGF,iBAAiB,CAACG,IAAI,CAAEC,OAAe,IAChEA,OAAO,CAACvC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyB,gBAAgB,CAAC,IAChDA,gBAAgB,CAACzB,QAAQ,CAACsC,OAAO,CAACvC,WAAW,CAAC,CAAC,CACjD,CAAC;;QAED;QACA,MAAMwC,OAAO,GAAG,EAAAV,sBAAA,GAAAf,SAAS,CAACF,cAAc,cAAAiB,sBAAA,uBAAxBA,sBAAA,CAA0BW,QAAQ,KAAI,EAAE;QACxD,MAAMC,WAAW,GAAGF,OAAO,CAACxC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyB,gBAAgB,CAAC;;QAEpE;QACA,MAAMiB,MAAM,GAAG,EAAAZ,sBAAA,GAAAhB,SAAS,CAACF,cAAc,cAAAkB,sBAAA,uBAAxBA,sBAAA,CAA0Ba,OAAO,KAAI,KAAK;QAEzD,MAAMC,cAAc,GAAGb,mBAAmB,IAAIC,oBAAoB,IAAII,kBAAkB,IAAIK,WAAW,IAAIC,MAAM;QAEjHG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;UACnDR,OAAO,EAAEnB,YAAY;UACrBY,mBAAmB;UACnBC,oBAAoB;UACpBI,kBAAkB;UAClBK,WAAW;UACXC,MAAM;UACNE,cAAc;UACdG,WAAW,EAAErB,OAAO,IAAIkB;QAC1B,CAAC,CAAC;QAEF,OAAOA,cAAc;MACvB;MAEA,OAAO,KAAK,CAAC,CAAC;IAChB;;IAEA;IACA,IAAI7B,YAAY,IAAIF,cAAc,CAACb,QAAQ,CAAC,OAAO,CAAC,EAAE;MAAA,IAAAgD,sBAAA,EAAAC,sBAAA;MACpD,MAAMC,aAAa,GAAGnC,YAAY,CAAC,CAAC,CAAC;MACrC,MAAMW,OAAO,GAAGZ,SAAS,CAACa,YAAY,KAAK,OAAO;;MAElD;MACA,MAAMwB,mBAAmB,GAAG7B,gBAAgB,CAACtB,QAAQ,CAAC,WAAWkD,aAAa,EAAE,CAAC,IACrD5B,gBAAgB,CAACtB,QAAQ,CAAC,UAAUkD,aAAa,EAAE,CAAC;MAChF,MAAME,oBAAoB,GAAGtC,SAAS,CAACmB,QAAQ,IAAInB,SAAS,CAACmB,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAWkD,aAAa,EAAE,CAAC;MACxH,MAAMG,eAAe,GAAGvC,SAAS,CAACwC,IAAI,IAAIC,IAAI,CAACC,GAAG,CAAC1C,SAAS,CAACwC,IAAI,GAAGG,QAAQ,CAACP,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;MACnG,MAAMQ,iBAAiB,GAAG,EAAAV,sBAAA,GAAAlC,SAAS,CAACF,cAAc,cAAAoC,sBAAA,uBAAxBA,sBAAA,CAA0BW,eAAe,KAC1C7C,SAAS,CAACF,cAAc,CAAC+C,eAAe,CAAC3D,QAAQ,CAAC,WAAWkD,aAAa,EAAE,CAAC;MAEtG,MAAMU,iBAAiB,GAAGT,mBAAmB,IAAIC,oBAAoB,IAAIC,eAAe,IAAIK,iBAAiB;MAE7Gb,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEI,aAAa,EAAE,eAAe,EAAE;QAC/DxB,OAAO;QACPyB,mBAAmB;QACnBC,oBAAoB;QACpBC,eAAe;QACfK,iBAAiB;QACjBE,iBAAiB;QACjBC,uBAAuB,EAAEvC,gBAAgB,CAACwC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;QAC3DH,eAAe,GAAAV,sBAAA,GAAEnC,SAAS,CAACF,cAAc,cAAAqC,sBAAA,uBAAxBA,sBAAA,CAA0BU,eAAe;QAC1DL,IAAI,EAAExC,SAAS,CAACwC,IAAI;QACpBP,WAAW,EAAErB,OAAO,IAAIkC;MAC1B,CAAC,CAAC;;MAEF;MACA;MACA,OAAOlC,OAAO,IAAIkC,iBAAiB;IACrC;;IAEA;IACA,IAAI3C,cAAc,IAAIJ,cAAc,CAACb,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,MAAM+D,eAAe,GAAG9C,cAAc,CAAC,CAAC,CAAC;MACzC,MAAM+C,OAAO,GAAGlD,SAAS,CAACa,YAAY,KAAK,OAAO;;MAElD;MACA,MAAMsC,mBAAmB,GAAG3C,gBAAgB,CAACtB,QAAQ,CAAC,aAAa+D,eAAe,EAAE,CAAC,IACzDzC,gBAAgB,CAACtB,QAAQ,CAAC,YAAY+D,eAAe,EAAE,CAAC,IACxDzC,gBAAgB,CAACtB,QAAQ,CAAC,SAAS+D,eAAe,EAAE,CAAC;MAEjFlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEiB,eAAe,EAAE,eAAe,EAAE;QACnEC,OAAO;QACPC,mBAAmB;QACnBlB,WAAW,EAAEiB,OAAO,IAAIC;MAC1B,CAAC,CAAC;MAEF,OAAOD,OAAO,IAAIC,mBAAmB;IACvC;;IAEA;IACA,IAAIpD,cAAc,CAACb,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACa,cAAc,CAACb,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzE,MAAM0B,OAAO,GAAGZ,SAAS,CAACa,YAAY,KAAK,OAAO;MAClDkB,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEpB,OAAO,CAAC;MAC9E,OAAOA,OAAO;IAChB;;IAEA;IACA,IAAIb,cAAc,CAACb,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACa,cAAc,CAACb,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzE,MAAMgE,OAAO,GAAGlD,SAAS,CAACa,YAAY,KAAK,OAAO;MAClDkB,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEkB,OAAO,CAAC;MAC9E,OAAOA,OAAO;IAChB;;IAEA;IACAnB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9D,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMoB,qBAAqB,GAAIrF,KAAa,IAAK;IAC/C;IACA,IAAIA,KAAK,CAACsF,UAAU,CAAC,GAAG,CAAC,EAAE;MACzB,MAAMC,OAAO,GAAGvF,KAAK,CAACwF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACtE,WAAW,CAAC,CAAC;;MAEjD;MACA,IAAIqE,OAAO,KAAK,QAAQ,EAAE;QACxB,MAAME,QAAQ,GAAGzF,KAAK,CAACiF,SAAS,CAAC,CAAC,CAAC,CAACzC,IAAI,CAAC,CAAC;QAC1C,MAAMkD,YAAY,GAAG7G,kBAAkB,CAAC8G,IAAI,CAACC,CAAC,IAC5CA,CAAC,CAACC,IAAI,CAAC3E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACsE,QAAQ,CAACvE,WAAW,CAAC,CAAC,CAAC,IACrD0E,CAAC,CAACE,EAAE,CAAC5E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACsE,QAAQ,CAACvE,WAAW,CAAC,CAAC,CACpD,CAAC;QAED,IAAIwE,YAAY,IAAIA,YAAY,CAACK,OAAO,EAAE;UACxC1F,iBAAiB,CAACqF,YAAY,CAACI,EAAE,CAAC;UAClC7F,QAAQ,CAAC,EAAE,CAAC;UACZ,OAAO,WAAW;QACpB;MACF;;MAEA;MAAA,KACK,IAAIsF,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;QACrDxF,gBAAgB,CAAC,CAAC;QAClBE,QAAQ,CAAC,EAAE,CAAC;QACZ,OAAO,WAAW;MACpB;IACF;IAEA,OAAO,eAAe;EACxB,CAAC;EAED,MAAM+F,iBAAiB,GAAG,MAAOC,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAClG,KAAK,CAACwC,IAAI,CAAC,CAAC,IAAItC,YAAY,EAAE;;IAEnC;IACA,IAAIF,KAAK,CAACsF,UAAU,CAAC,GAAG,CAAC,EAAE;MACzB,MAAMa,MAAM,GAAGd,qBAAqB,CAACrF,KAAK,CAAC;MAC3C,IAAImG,MAAM,KAAK,WAAW,EAAE;QAC1BlG,QAAQ,CAAC,EAAE,CAAC;QACZ;MACF;MACA;IACF;IAEA,OAAO,MAAMmG,eAAe,CAACpG,KAAK,CAAC;EACrC,CAAC;EAED,MAAMoG,eAAe,GAAG,MAAOC,WAAmB,IAAK;IAAA,IAAAC,qBAAA;IACrD;IACA,IAAI,CAAC7G,cAAc,EAAE;MACnB,MAAME,aAAa,CAAC,CAAC;IACvB;IAEA,MAAM4G,WAAwB,GAAG;MAC/BT,EAAE,EAAE,QAAQU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACxBC,OAAO,EAAEL,WAAW;MACpBM,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MACnCC,MAAM,EAAE,CAAArH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEqG,EAAE,KAAI;IAChC,CAAC;IAEDjG,UAAU,CAAC0G,WAAW,CAAC;IACvBtG,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAM8G,SAAS,GAAGP,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMO,aAA0B,GAAG;MACjClB,EAAE,EAAE,MAAMiB,SAAS,EAAE;MACrBL,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,IAAI;MACZM,OAAO,EAAE,IAAI;MACbL,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MACnCK,SAAS,EAAE9G,cAAc;MACzB0G,MAAM,EAAE,CAAArH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEqG,EAAE,KAAI;IAChC,CAAC;IAEDjG,UAAU,CAACmH,aAAa,CAAC;IACzB,CAAAV,qBAAA,GAAA5F,cAAc,CAACC,OAAO,cAAA2F,qBAAA,uBAAtBA,qBAAA,CAAwB1F,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;IAE9DV,eAAe,CAAC,IAAI,CAAC;IACrBI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,cAAc,CAAC;IAErC,IAAI;MAAA,IAAA0G,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF,CAAAH,sBAAA,GAAAzG,cAAc,CAACC,OAAO,cAAAwG,sBAAA,uBAAtBA,sBAAA,CAAwBvG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;;MAE9D;MACA0G,UAAU,CAAC,MAAM9G,qBAAqB,CAAC,qBAAqB,CAAC,EAAE,GAAG,CAAC;MACnE8G,UAAU,CAAC,MAAM9G,qBAAqB,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC;MACnE8G,UAAU,CAAC,MAAM9G,qBAAqB,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC;MAElE,MAAM+G,QAAQ,GAAG,MAAM7I,SAAS,CAAC0H,WAAW,EAAEjG,cAAc,CAAC;;MAE7D;MACA,MAAMqH,SAAsB,GAAG;QAC7B3B,EAAE,EAAE,MAAMiB,SAAS,EAAE;QACrBL,OAAO,EAAEc,QAAQ,CAACE,MAAM;QACxBC,eAAe,EAAEH,QAAQ,CAACG,eAAe;QACzCC,cAAc,EAAEJ,QAAQ,CAACI,cAAc;QACvCV,SAAS,EAAEM,QAAQ,CAACN,SAAS,IAAI9G,cAAc;QAC/CuG,MAAM,EAAE,IAAI;QACZkB,OAAO,EAAEL,QAAQ,CAACK,OAAO;QACzBC,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB;QAC3CC,eAAe,EAAEP,QAAQ,CAACO,eAAe;QACzCjB,MAAM,EAAE,CAAArH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEqG,EAAE,KAAI,MAAM;QACpCkC,YAAY,EAAER,QAAQ,CAACQ;MACzB,CAAC;;MAED;MACAhE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjC0D,eAAe,EAAEH,QAAQ,CAACG,eAAe,GAAG,QAAQ,GAAG,SAAS;QAChEC,cAAc,EAAEJ,QAAQ,CAACI,cAAc,GAAG,QAAQ,GAAG,SAAS;QAC9DK,sBAAsB,EAAE,EAAAb,qBAAA,GAAAI,QAAQ,CAACM,gBAAgB,cAAAV,qBAAA,uBAAzBA,qBAAA,CAA2B3F,MAAM,KAAI,CAAC;QAC9DyG,qBAAqB,EAAE,EAAAb,qBAAA,GAAAG,QAAQ,CAACO,eAAe,cAAAV,qBAAA,uBAAxBA,qBAAA,CAA0B5F,MAAM,KAAI,CAAC;QAC5DuG,YAAY,EAAER,QAAQ,CAACQ;MACzB,CAAC,CAAC;;MAEF;MACAhE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEuD,QAAQ,CAACM,gBAAgB,CAAC;MAC9E,IAAIN,QAAQ,CAACM,gBAAgB,EAAE;QAC7BN,QAAQ,CAACM,gBAAgB,CAACK,OAAO,CAAC,CAACrG,MAAM,EAAEsG,KAAK,KAAK;UACnDpE,OAAO,CAACC,GAAG,CAAC,oBAAoBmE,KAAK,GAAG,EAAE;YACxCtF,YAAY,EAAEhB,MAAM,CAACgB,YAAY;YACjCuF,kBAAkB,EAAE,CAAC,CAACvG,MAAM,CAACC,cAAc;YAC3CuG,WAAW,EAAExG,MAAM,CAACwG,WAAW;YAC/BC,YAAY,EAAEzG,MAAM,CAACyG,YAAY;YACjCC,mBAAmB,EAAE1G,MAAM,CAACC,cAAc,GAAG0G,MAAM,CAACC,IAAI,CAAC5G,MAAM,CAACC,cAAc,CAAC,GAAG;UACpF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEAjC,aAAa,CAACkH,aAAa,CAAClB,EAAE,EAAE2B,SAAS,CAAC;MAE1C,CAAAH,sBAAA,GAAA5G,cAAc,CAACC,OAAO,cAAA2G,sBAAA,uBAAtBA,sBAAA,CAAwB1G,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAChE,CAAC,CAAC,OAAO8H,KAAK,EAAE;MACd3E,OAAO,CAAC2E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,MAAMC,YAAyB,GAAG;QAChC9C,EAAE,EAAE,MAAMiB,SAAS,EAAE;QACrBL,OAAO,EAAE,iNAAiN;QAC1NC,MAAM,EAAE,IAAI;QACZG,MAAM,EAAE,CAAArH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEqG,EAAE,KAAI,MAAM;QACpCkC,YAAY,EAAE;MAChB,CAAC;MAEDlI,aAAa,CAACkH,aAAa,CAAClB,EAAE,EAAE8C,YAAY,CAAC;IAC/C,CAAC,SAAS;MACRzI,eAAe,CAAC,KAAK,CAAC;MACtBI,kBAAkB,CAAC,KAAK,CAAC;MACzBE,qBAAqB,CAAC,UAAU,CAAC;IACnC;EACF,CAAC;EAID,MAAMoI,sBAAsB,GAAIhB,OAAgC,IAAK;IACnE,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACpG,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;IAE/C;IACA,MAAMqH,cAAwE,GAAG,CAAC,CAAC;IAEnFjB,OAAO,CAACM,OAAO,CAACrG,MAAM,IAAI;MACxB,IAAIsB,QAAgB;MACpB,IAAIqB,IAAY;MAEhB,IAAI,OAAO3C,MAAM,KAAK,QAAQ,EAAE;QAC9B;QACA,MAAMK,KAAK,GAAGL,MAAM,CAACK,KAAK,CAAC,kCAAkC,CAAC;QAC9DiB,QAAQ,GAAGjB,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,GAAGV,MAAM;QAC3C2C,IAAI,GAAGtC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGyC,QAAQ,CAACzC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACnD,CAAC,MAAM;QACLiB,QAAQ,GAAGtB,MAAM,CAAC+D,IAAI,IAAI/D,MAAM,CAACsB,QAAQ,IAAI,kBAAkB;QAC/DqB,IAAI,GAAG3C,MAAM,CAAC2C,IAAI,IAAI,CAAC;MACvB;MAEF,IAAI,CAACqE,cAAc,CAAC1F,QAAQ,CAAC,EAAE;QAC7B0F,cAAc,CAAC1F,QAAQ,CAAC,GAAG;UAAEA,QAAQ;UAAE2F,KAAK,EAAE;QAAG,CAAC;MACpD;MAEA,IAAI,CAACD,cAAc,CAAC1F,QAAQ,CAAC,CAAC2F,KAAK,CAAC5H,QAAQ,CAACsD,IAAI,CAAC,EAAE;QAClDqE,cAAc,CAAC1F,QAAQ,CAAC,CAAC2F,KAAK,CAACC,IAAI,CAACvE,IAAI,CAAC;MAC3C;IACF,CAAC,CAAC;;IAEF;IACA,OAAOgE,MAAM,CAACQ,MAAM,CAACH,cAAc,CAAC,CAACI,GAAG,CAACC,KAAK,IAAI;MAChD,MAAMC,WAAW,GAAGD,KAAK,CAACJ,KAAK,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;MACrD,MAAMC,QAAQ,GAAGJ,WAAW,CAAC3H,MAAM,KAAK,CAAC,GACrC,QAAQ2H,WAAW,CAAC,CAAC,CAAC,EAAE,GACxB,SAASA,WAAW,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE;;MAErC;MACA,OAAO;QACLC,IAAI,EAAE,GAAGP,KAAK,CAAC/F,QAAQ,MAAMoG,QAAQ,EAAE;QACvCG,IAAI,EAAE,gBAAgBC,kBAAkB,CAACT,KAAK,CAAC/F,QAAQ,CAAC,SAASgG,WAAW,CAAC,CAAC,CAAC,EAAE;QACjFS,UAAU,EAAE;MACd,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,qBAAqB,GAAIjC,OAAgC,IAAK;IAClE,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACpG,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;IAE/C;IACA,MAAMsI,UAAU,GAAG,IAAIC,GAAG,CAAS,CAAC;IACpC,MAAMC,SAAsE,GAAG,EAAE;IAEjFpC,OAAO,CAACM,OAAO,CAACrG,MAAM,IAAI;MACxB,IAAIoI,GAAW;MACf,IAAIC,WAAmB;MAEvB,IAAI,OAAOrI,MAAM,KAAK,QAAQ,EAAE;QAC9BoI,GAAG,GAAGpI,MAAM,CAACwD,UAAU,CAAC,MAAM,CAAC,GAAGxD,MAAM,GAAG,WAAWA,MAAM,EAAE;QAC5D,IAAI;UACJ,MAAMsI,MAAM,GAAG,IAAIC,GAAG,CAACH,GAAG,CAAC;UAC3BC,WAAW,GAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACnD,CAAC,CAAC,MAAM;UACRJ,WAAW,GAAGrI,MAAM;QACtB;MACF,CAAC,MAAM;QACLoI,GAAG,GAAGpI,MAAM,CAACoI,GAAG,IAAI,uCAAuC;QAC3D,IAAI;UACF,MAAME,MAAM,GAAG,IAAIC,GAAG,CAACH,GAAG,CAAC;UAC3BC,WAAW,GAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACrD,CAAC,CAAC,MAAM;UACNJ,WAAW,GAAGD,GAAG;QACnB;MACF;MAEA,IAAI,CAACH,UAAU,CAACS,GAAG,CAACN,GAAG,CAAC,EAAE;QACxBH,UAAU,CAACU,GAAG,CAACP,GAAG,CAAC;QACnBD,SAAS,CAACjB,IAAI,CAAC;UACbU,IAAI,EAAES,WAAW;UACjBR,IAAI,EAAEO,GAAG;UACTL,UAAU,EAAE,KAAK,CAAE;QACrB,CAAC,CAAC;MACF;IACJ,CAAC,CAAC;IAEF,OAAOI,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMS,UAAU,GAAGA,CAAC;IAAEC,KAAK;IAAEC,UAAU,GAAG;EAG1C,CAAC,KAAK;IACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtM,QAAQ,CAACmM,KAAK,CAAClJ,MAAM,IAAImJ,UAAU,CAAC;IAEpE,IAAID,KAAK,CAAClJ,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEnC,MAAMsJ,YAAY,GAAGF,QAAQ,GAAGF,KAAK,GAAGA,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEJ,UAAU,CAAC;IAClE,MAAMK,OAAO,GAAGN,KAAK,CAAClJ,MAAM,GAAGmJ,UAAU;IAEzC,MAAMM,mBAAmB,GAAGA,CAACjF,CAAsC,EAAE0D,IAAY,KAAK;MACpF;MACA;MACA;IAAA,CACD;IAED,oBACEvK,OAAA;MAAI+L,SAAS,EAAC,uCAAuC;MAAAC,QAAA,GAClDL,YAAY,CAAC7B,GAAG,CAAC,CAACmC,IAAI,EAAEjD,KAAK,kBACxBhJ,OAAA;QAAAgM,QAAA,EACGC,IAAI,CAAC1B,IAAI,gBACZvK,OAAA;UACEkM,IAAI,EAAED,IAAI,CAAC1B,IAAK;UAChB4B,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzBL,SAAS,EAAC,8DAA8D;UACxEM,KAAK,EAAEJ,IAAI,CAACxB,UAAU,GAAG,4BAA4B,GAAG,yBAA0B;UAClF6B,OAAO,EAAEL,IAAI,CAACxB,UAAU,GAAI5D,CAAC,IAAKiF,mBAAmB,CAACjF,CAAC,EAAEoF,IAAI,CAAC1B,IAAK,CAAC,GAAGgC,SAAU;UAAAP,QAAA,EAE5EC,IAAI,CAAC3B;QAAI;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,gBAER3M,OAAA;UAAM+L,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEC,IAAI,CAAC3B;QAAI;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAC9C,GAdM3D,KAAK;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeV,CACL,CAAC,EACLd,OAAO,IAAI,CAACJ,QAAQ,iBACnBzL,OAAA;QAAI+L,SAAS,EAAC,WAAW;QAAAC,QAAA,eACvBhM,OAAA;UACEsM,OAAO,EAAEA,CAAA,KAAMZ,WAAW,CAAC,IAAI,CAAE;UACjCK,SAAS,EAAC,sEAAsE;UAAAC,QAAA,GACjF,IACG,EAACT,KAAK,CAAClJ,MAAM,GAAGmJ,UAAU,EAAC,eAC/B;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACL,EACAd,OAAO,IAAIJ,QAAQ,iBAClBzL,OAAA;QAAI+L,SAAS,EAAC,WAAW;QAAAC,QAAA,eACrBhM,OAAA;UACEsM,OAAO,EAAEA,CAAA,KAAMZ,WAAW,CAAC,KAAK,CAAE;UACpCK,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EAC/E;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAET,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAOC,WAAwB,IAAK;IAC3D;IACA,MAAMrM,eAAe,CAACqM,WAAW,CAACnG,EAAE,CAAC;IACrCzF,iBAAiB,CAAC4L,WAAW,CAACC,UAAU,IAAI,kBAAkB,CAAC;IAC/D3M,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAM4M,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCnI,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC,MAAMtE,aAAa,CAAC,CAAC;IACrBJ,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,oBACEH,OAAA;IAAK+L,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBAEvEhM,OAAA,CAACJ,WAAW;MACVoN,SAAS,EAAE9L,eAAgB;MAC3B+L,OAAO,EAAE,CAAC,MAAM;QACd,QAAQ7L,kBAAkB;UACxB,KAAK,qBAAqB;YACxB,OAAO,mCAAmC;UAC5C,KAAK,oBAAoB;YACvB,OAAO,kCAAkC;UAC3C,KAAK,mBAAmB;YACtB,OAAO,gCAAgC;UACzC;YACE,OAAO,kCAAkC;QAC7C;MACF,CAAC,EAAE,CAAE;MACL8L,SAAS,EAAC,SAAS;MACnBC,YAAY,EAAE/L,kBAAmB;MACjClB,WAAW,EAAEA;IAAY;MAAAsM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGF3M,OAAA,CAACL,WAAW;MACVyN,MAAM,EAAElN,WAAY;MACpBmN,QAAQ,EAAEA,CAAA,KAAMlN,cAAc,CAAC,CAACD,WAAW,CAAE;MAC7CoN,aAAa,EAAE,CAAAjN,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEqG,EAAE,KAAI,EAAG;MACxC6G,YAAY,EAAEX,gBAAiB;MAC/BY,SAAS,EAAET;IAAc;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGF3M,OAAA;MAAK+L,SAAS,EAAE,oDAAoD7L,WAAW,GAAG,UAAU,GAAG,EAAE,EAAG;MAAA8L,QAAA,gBAElGhM,OAAA;QAAK+L,SAAS,EAAE,UAAUzL,QAAQ,CAAC+B,MAAM,GAAG,CAAC,GAAG,iBAAiB,GAAG,iBAAiB,YAAa;QAAA2J,QAAA,EAC/F1L,QAAQ,CAAC+B,MAAM,KAAK,CAAC,gBACpBrC,OAAA;UAAK+L,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDhM,OAAA;YAAK+L,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxChM,OAAA;cAAG+L,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjE3M,OAAA;cAAAgM,QAAA,EAAG;YAAsC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN3M,OAAA;UAAAgM,QAAA,GACG1L,QAAQ,CAACwJ,GAAG,CAAEmD,OAAO,iBACpBjN,OAAA;YAEE+L,SAAS,EAAE,QACTkB,OAAO,CAAC1F,MAAM,KAAK,MAAM,GAAG,kBAAkB,GAAG,oBAAoB,EACpE;YAAAyE,QAAA,eAEHhM,OAAA;cACE+L,SAAS,EAAE,2DACTkB,OAAO,CAAC1F,MAAM,KAAK,MAAM,GACrB,wBAAwB,GACxB,kCAAkC,EACrC;cAAAyE,QAAA,gBAEHhM,OAAA;gBAAK+L,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDhM,OAAA;kBAAM+L,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAC5BiB,OAAO,CAAC1F,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG;gBAAS;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,EACNM,OAAO,CAACzF,SAAS,iBAChBxH,OAAA;kBAAM+L,SAAS,EAAE,gBACfkB,OAAO,CAAC1F,MAAM,KAAK,MAAM,GACrB,eAAe,GACf,eAAe,EAClB;kBAAAyE,QAAA,EACA,IAAI5E,IAAI,CAAC6F,OAAO,CAACzF,SAAS,CAAC,CAACiG,kBAAkB,CAAC;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGLM,OAAO,CAAC1F,MAAM,KAAK,MAAM,IAAI0F,OAAO,CAAC3F,OAAO,iBAC3CtH,OAAA;gBAAK+L,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEiB,OAAO,CAAC3F;cAAO;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACjE,EAGAM,OAAO,CAAC1F,MAAM,KAAK,IAAI,iBACtBvH,OAAA;gBAAAgM,QAAA,EACG,CAAC,CAAA0B,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,KAAM;kBACN;kBACA,IAAIZ,OAAO,CAACpF,OAAO,IAAI3G,eAAe,IAAI,CAAC+L,OAAO,CAAC3F,OAAO,IAAI,CAAC2F,OAAO,CAAC1E,eAAe,IAAI,CAAC0E,OAAO,CAACzE,cAAc,EAAE;oBACjH,OAAO,IAAI;kBACb;;kBAEA;kBACA,MAAMsF,mBAAmB,GAAGrE,sBAAsB,CAACwD,OAAO,CAACvE,gBAAgB,CAAC;kBAC5E,MAAMqF,kBAAkB,GAAGrD,qBAAqB,CAACuC,OAAO,CAACtE,eAAe,CAAC;;kBAEzE;kBACA,MAAMqF,kBAAkB,GAAG,CAAC,EAAEf,OAAO,CAAC1E,eAAe,IAAI0E,OAAO,CAAC1E,eAAe,CAACnF,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;kBAC/F,MAAM6K,iBAAiB,GAAG,CAAC,EAAEhB,OAAO,CAACzE,cAAc,IAAIyE,OAAO,CAACzE,cAAc,CAACpF,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;kBAC5F;kBACA,MAAM8K,cAAc,GAAI,CAACF,kBAAkB,IAAI,CAACC,iBAAiB,IAAKhB,OAAO,CAACrE,YAAY;;kBAE1F;kBACAhE,OAAO,CAACC,GAAG,CAAC,wBAAwBoI,OAAO,CAACvG,EAAE,GAAG,EAAE;oBACjDsH,kBAAkB;oBAClBC,iBAAiB;oBACjBC,cAAc;oBACdC,oBAAoB,EAAE,EAAAT,qBAAA,GAAAT,OAAO,CAAC1E,eAAe,cAAAmF,qBAAA,uBAAvBA,qBAAA,CAAyBrL,MAAM,KAAI,CAAC;oBAC1D+L,mBAAmB,EAAE,EAAAT,qBAAA,GAAAV,OAAO,CAACzE,cAAc,cAAAmF,qBAAA,uBAAtBA,qBAAA,CAAwBtL,MAAM,KAAI,CAAC;oBACxDgM,oBAAoB,EAAEP,mBAAmB,CAACzL,MAAM;oBAChDiM,mBAAmB,EAAEP,kBAAkB,CAAC1L,MAAM;oBAC9CkM,iBAAiB,EAAEtB,OAAO,CAAC1E,eAAe,GAAG,QAAQ,GAAG,SAAS;oBACjEiG,gBAAgB,EAAEvB,OAAO,CAACzE,cAAc,GAAG,QAAQ,GAAG,SAAS;oBAC/DiG,kBAAkB,EAAE,EAAAb,qBAAA,GAAAX,OAAO,CAACvE,gBAAgB,cAAAkF,qBAAA,uBAAxBA,qBAAA,CAA0BvL,MAAM,KAAI,CAAC;oBACzDqM,iBAAiB,EAAE,EAAAb,qBAAA,GAAAZ,OAAO,CAACtE,eAAe,cAAAkF,qBAAA,uBAAvBA,qBAAA,CAAyBxL,MAAM,KAAI;kBACxD,CAAC,CAAC;;kBAEF;kBACA,MAAMsM,mBAAmB,GAAGrO,QAAQ,CAACsO,SAAS,CAACpI,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKuG,OAAO,CAACvG,EAAE,CAAC;kBACxE,IAAI/E,YAAY,GAAG,EAAE;;kBAErB;kBACA,KAAK,IAAIkN,CAAC,GAAGF,mBAAmB,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;oBACjD,IAAIvO,QAAQ,CAACuO,CAAC,CAAC,CAACtH,MAAM,KAAK,MAAM,IAAIjH,QAAQ,CAACuO,CAAC,CAAC,CAACvH,OAAO,EAAE;sBACxD3F,YAAY,GAAGrB,QAAQ,CAACuO,CAAC,CAAC,CAACvH,OAAO;sBAClC;oBACF;kBACF;kBAEA1C,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;oBAAEiK,WAAW,EAAE7B,OAAO,CAACvG,EAAE;oBAAE/E;kBAAa,CAAC,CAAC;;kBAEvG;kBACA,MAAMoN,UAAU,GAAG,EAAE;kBACrB;kBACA,IAAIC,YAAY,GAAG,EAAE;;kBAErB;kBACA,IAAIhB,kBAAkB,IAAIC,iBAAiB,EAAE;oBAC3Ce,YAAY,GAAG,sBAAsB;;oBAErC;oBACA;oBACA,MAAMC,YAAY,GAAGnB,mBAAmB,CAACzL,MAAM,KAAK,CAAC,GACjDyL,mBAAmB,CAAC,CAAC,CAAC,CAACxD,IAAI,CAAClE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAC3C,oBAAoB;oBAExB2I,UAAU,CAACnF,IAAI,cACb5J,OAAA;sBAAyB+L,SAAS,EAAC,gGAAgG;sBAAAC,QAAA,gBACjIhM,OAAA;wBAAI+L,SAAS,EAAC,4DAA4D;wBAAAC,QAAA,GAAC,2BAC1D,EAACiD,YAAY;sBAAA;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC,eACL3M,OAAA,CAACN,iBAAiB;wBAChB4H,OAAO,EAAE2F,OAAO,CAAC1E,eAAe,IAAI,EAAG;wBACvC2G,KAAK,EAAEvN,YAAa;wBACpBwN,KAAK,EAAElC,OAAO,CAACnF,SAAU;wBACzBJ,MAAM,EAAEuF,OAAO,CAACvF;sBAAO;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,EAGD,CAACyC,sBAAA,IAAM;wBACN,MAAMC,aAAa,GAAG,EAAAD,sBAAA,GAAAnC,OAAO,CAACvE,gBAAgB,cAAA0G,sBAAA,uBAAxBA,sBAAA,CAA0BE,MAAM,CAAC5M,MAAM,IAC3D,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,cACvC,CAAC,KAAI,EAAE;;wBAEP;wBACA,IAAI4M,eAAe,GAAGF,aAAa,CAACC,MAAM,CAAC5M,MAAM,IAC/CD,mBAAmB,CAACC,MAAM,EAAYf,YAAY,CACpD,CAAC;;wBAED;wBACA,IAAI4N,eAAe,CAAClN,MAAM,KAAK,CAAC,IAAIV,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;0BAChFwN,eAAe,GAAGF,aAAa,CAACC,MAAM,CAAC5M,MAAM,IAC1CA,MAAM,CAAYgB,YAAY,KAAK,OACtC,CAAC;0BACDkB,OAAO,CAACC,GAAG,CAAC,sEAAsE,EAAE0K,eAAe,CAAClN,MAAM,CAAC;wBAC7G;wBAEA,OAAOkN,eAAe,CAAClN,MAAM,GAAG,CAAC,IAAIX,uBAAuB,CAACC,YAAY,EAAEsL,OAAO,CAAC1E,eAAe,IAAI,EAAE,CAAC,gBACvGvI,OAAA;0BAAK+L,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhM,OAAA;4BAAI+L,SAAS,EAAC,0CAA0C;4BAAAC,QAAA,EAAC;0BAAkB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAChF3M,OAAA;4BAAK+L,SAAS,EAAC,WAAW;4BAAAC,QAAA,EACvBuD,eAAe,CAACzF,GAAG,CAAC,CAACpH,MAAM,EAAEsG,KAAK,kBACjChJ,OAAA,CAACH,aAAa;8BAAa6C,MAAM,EAAEA;4BAAiB,GAAhCsG,KAAK;8BAAAwD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAA6B,CACvD;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,GACJ,IAAI;sBACV,CAAC,EAAE,CAAC,eAEJ3M,OAAA;wBAAK+L,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDhM,OAAA;0BAAG+L,SAAS,EAAC,0CAA0C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACpE3M,OAAA,CAACsL,UAAU;0BAACC,KAAK,EAAEuC,mBAAoB;0BAACtC,UAAU,EAAE;wBAAE;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC;oBAAA,GA7CC,eAAe;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA8CnB,CACP,CAAC;;oBAED;oBACA;oBACA,MAAM6C,YAAY,GAAGzB,kBAAkB,CAAC1L,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,mBAAmB;oBAE/F0M,UAAU,CAACnF,IAAI,cACb5J,OAAA;sBAAwB+L,SAAS,EAAC,kGAAkG;sBAAAC,QAAA,gBAClIhM,OAAA;wBAAI+L,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,GAAC,2BAC3D,EAACwD,YAAY;sBAAA;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC,eACL3M,OAAA,CAACN,iBAAiB;wBAChB4H,OAAO,EAAE2F,OAAO,CAACzE,cAAc,IAAI,EAAG;wBACtC0G,KAAK,EAAEvN,YAAa;wBACpBwN,KAAK,EAAElC,OAAO,CAACnF,SAAU;wBACzBJ,MAAM,EAAEuF,OAAO,CAACvF;sBAAO;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACF3M,OAAA;wBAAK+L,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,gBAClDhM,OAAA;0BAAG+L,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACrE3M,OAAA,CAACsL,UAAU;0BAACC,KAAK,EAAEwC,kBAAmB;0BAACvC,UAAU,EAAE;wBAAE;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA,GAbC,cAAc;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAclB,CACP,CAAC;;oBAEH;kBACA,CAAC,MAAM,IAAIqB,kBAAkB,EAAE;oBAC7BgB,YAAY,GAAG,eAAe;;oBAE9B;oBACA,MAAMC,YAAY,GAAGnB,mBAAmB,CAACzL,MAAM,KAAK,CAAC,GACjDyL,mBAAmB,CAAC,CAAC,CAAC,CAACxD,IAAI,CAAClE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAC3C,oBAAoB;oBAExB2I,UAAU,CAACnF,IAAI,cACb5J,OAAA;sBAA6B+L,SAAS,EAAC,gGAAgG;sBAAAC,QAAA,gBACrIhM,OAAA;wBAAI+L,SAAS,EAAC,4DAA4D;wBAAAC,QAAA,EAAC;sBAE3E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL3M,OAAA,CAACN,iBAAiB;wBAChB4H,OAAO,EAAE2F,OAAO,CAAC1E,eAAe,IAAI,EAAG;wBACvC2G,KAAK,EAAEvN,YAAa;wBACpBwN,KAAK,EAAElC,OAAO,CAACnF,SAAU;wBACzBJ,MAAM,EAAEuF,OAAO,CAACvF;sBAAO;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,EAGD,CAAC8C,sBAAA,IAAM;wBACN,MAAMJ,aAAa,GAAG,EAAAI,sBAAA,GAAAxC,OAAO,CAACvE,gBAAgB,cAAA+G,sBAAA,uBAAxBA,sBAAA,CAA0BH,MAAM,CAAC5M,MAAM,IAC3D,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,cACvC,CAAC,KAAI,EAAE;;wBAEP;wBACA,IAAI4M,eAAe,GAAGF,aAAa,CAACC,MAAM,CAAC5M,MAAM,IAC/CD,mBAAmB,CAACC,MAAM,EAAYf,YAAY,CACpD,CAAC;;wBAED;wBACA,IAAI4N,eAAe,CAAClN,MAAM,KAAK,CAAC,IAAIV,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;0BAChFwN,eAAe,GAAGF,aAAa,CAACC,MAAM,CAAC5M,MAAM,IAC1CA,MAAM,CAAYgB,YAAY,KAAK,OACtC,CAAC;0BACDkB,OAAO,CAACC,GAAG,CAAC,6FAA6F,EAAE0K,eAAe,CAAClN,MAAM,CAAC;wBACpI;wBAEA,OAAOkN,eAAe,CAAClN,MAAM,GAAG,CAAC,IAAIX,uBAAuB,CAACC,YAAY,EAAEsL,OAAO,CAAC1E,eAAe,IAAI,EAAE,CAAC,gBACvGvI,OAAA;0BAAK+L,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhM,OAAA;4BAAI+L,SAAS,EAAC,0CAA0C;4BAAAC,QAAA,EAAC;0BAAkB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAChF3M,OAAA;4BAAK+L,SAAS,EAAC,WAAW;4BAAAC,QAAA,EACvBuD,eAAe,CAACzF,GAAG,CAAC,CAACpH,MAAM,EAAEsG,KAAK,kBACjChJ,OAAA,CAACH,aAAa;8BAAa6C,MAAM,EAAEA;4BAAiB,GAAhCsG,KAAK;8BAAAwD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAA6B,CACvD;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,GACJ,IAAI;sBACV,CAAC,EAAE,CAAC,eAEJ3M,OAAA;wBAAK+L,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDhM,OAAA;0BAAG+L,SAAS,EAAC,0CAA0C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACpE3M,OAAA,CAACsL,UAAU;0BAACC,KAAK,EAAEuC,mBAAoB;0BAACtC,UAAU,EAAE;wBAAE;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC;oBAAA,GA7CC,mBAAmB;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA8CvB,CACP,CAAC;;oBAEH;kBACA,CAAC,MAAM,IAAIsB,iBAAiB,EAAE;oBAC5Be,YAAY,GAAG,cAAc;;oBAE7B;oBACA,MAAMQ,YAAY,GAAGzB,kBAAkB,CAAC1L,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,mBAAmB;oBAE/F0M,UAAU,CAACnF,IAAI,cACb5J,OAAA;sBAA4B+L,SAAS,EAAC,kGAAkG;sBAAAC,QAAA,gBACtIhM,OAAA;wBAAI+L,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,EAAC;sBAE5E;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL3M,OAAA,CAACN,iBAAiB;wBAChB4H,OAAO,EAAE2F,OAAO,CAACzE,cAAc,IAAI,EAAG;wBACtC0G,KAAK,EAAEvN,YAAa;wBACpBwN,KAAK,EAAElC,OAAO,CAACnF,SAAU;wBACzBJ,MAAM,EAAEuF,OAAO,CAACvF;sBAAO;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACF3M,OAAA;wBAAK+L,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,gBAClDhM,OAAA;0BAAG+L,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACrE3M,OAAA,CAACsL,UAAU;0BAACC,KAAK,EAAEwC,kBAAmB;0BAACvC,UAAU,EAAE;wBAAE;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA,GAbC,kBAAkB;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OActB,CACP,CAAC;;oBAEH;kBACA,CAAC,MAAM,IAAIuB,cAAc,EAAE;oBAAA,IAAAwB,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;oBACzBf,YAAY,GAAG,KAAK;oBAEpB,MAAMgB,SAAS,GAAG/C,OAAO,CAACnF,SAAS,IAAI,QAAQ;oBAC/C,MAAMmI,SAAS,GAAG,CAAAP,kBAAA,GAAAzC,OAAO,CAACnF,SAAS,cAAA4H,kBAAA,eAAjBA,kBAAA,CAAmB3N,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,GAC/C,CAAA4N,mBAAA,GAAA1C,OAAO,CAACnF,SAAS,cAAA6H,mBAAA,eAAjBA,mBAAA,CAAmB5N,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,GACzC,CAAA6N,mBAAA,GAAA3C,OAAO,CAACnF,SAAS,cAAA8H,mBAAA,eAAjBA,mBAAA,CAAmB7N,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAC9C,CAAA8N,mBAAA,GAAA5C,OAAO,CAACnF,SAAS,cAAA+H,mBAAA,eAAjBA,mBAAA,CAAmB9N,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,GAC1C,CAAA+N,mBAAA,GAAA7C,OAAO,CAACnF,SAAS,cAAAgI,mBAAA,eAAjBA,mBAAA,CAAmB/N,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,GAC5C,CAAAgO,mBAAA,GAAA9C,OAAO,CAACnF,SAAS,cAAAiI,mBAAA,eAAjBA,mBAAA,CAAmBhO,QAAQ,CAAC,aAAa,CAAC,GAAG,IAAI,GAAG,IAAI;;oBAExE;oBACA,IAAI,CAACb,eAAe,IAAI,CAAC+L,OAAO,CAACpF,OAAO,EAAE;sBAC1CkH,UAAU,CAACnF,IAAI,cACb5J,OAAA;wBAAwB+L,SAAS,EAAC,oGAAoG;wBAAAC,QAAA,gBACpIhM,OAAA;0BAAI+L,SAAS,EAAC,8DAA8D;0BAAAC,QAAA,GACzEiE,SAAS,EAAC,uBAAqB,EAACD,SAAS;wBAAA;0BAAAxD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC,eACL3M,OAAA,CAACN,iBAAiB;0BAChB4H,OAAO,EAAE2F,OAAO,CAAC3F,OAAO,IAAI,mEAAoE;0BAChG4H,KAAK,EAAEvN,YAAa;0BACpBwN,KAAK,EAAElC,OAAO,CAACnF,SAAS,IAAI,QAAS;0BACrCJ,MAAM,EAAEuF,OAAO,CAACvF;wBAAO;0BAAA8E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC,eACF3M,OAAA;0BAAK+L,SAAS,EAAC,sCAAsC;0BAAAC,QAAA,eACnDhM,OAAA;4BAAG+L,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,EAAC;0BAE9C;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA,GAdC,cAAc;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAelB,CACP,CAAC;oBACD;;oBAEF;kBACA,CAAC,MAAM;oBACL;oBACAqC,YAAY,GAAG,YAAY;;oBAE3B;oBACA,IAAI,CAAC9N,eAAe,IAAI,CAAC+L,OAAO,CAACpF,OAAO,EAAE;sBAC1CkH,UAAU,CAACnF,IAAI,cACb5J,OAAA;wBAAsB+L,SAAS,EAAC,sFAAsF;wBAAAC,QAAA,gBACpHhM,OAAA;0BAAG+L,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAC;wBAAgB;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAC9D3M,OAAA,CAACN,iBAAiB;0BAChB4H,OAAO,EAAE2F,OAAO,CAAC3F,OAAO,IAAI,sGAAuG;0BACnI4H,KAAK,EAAEvN,YAAa;0BACpBwN,KAAK,EAAElC,OAAO,CAACnF,SAAS,IAAI,QAAS;0BACrCJ,MAAM,EAAEuF,OAAO,CAACvF;wBAAO;0BAAA8E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA,GAPK,YAAY;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAQhB,CACP,CAAC;oBACD;kBACF;;kBAEA;kBACA,IAAIoC,UAAU,CAAC1M,MAAM,GAAG,CAAC,EAAE;oBACzB,oBACErC,OAAA;sBAAK+L,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClB+C;oBAAU;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAEV;;kBAEA;kBACA/H,OAAO,CAACsL,IAAI,CAAC,oCAAoC,CAAC;kBAClD,oBACElQ,OAAA;oBAAK+L,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,gBACnGhM,OAAA;sBAAG+L,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAe;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC7D3M,OAAA,CAACN,iBAAiB;sBAChB4H,OAAO,EAAE2F,OAAO,CAAC3F,OAAO,IAAI,iDAAkD;sBAC9E4H,KAAK,EAAEvN,YAAa;sBACpBwN,KAAK,EAAElC,OAAO,CAACnF,SAAS,IAAI,QAAS;sBACrCJ,MAAM,EAAEuF,OAAO,CAACvF;oBAAO;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAEV,CAAC,EAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GA3UDM,OAAO,CAACvG,EAAE;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4UZ,CACN,CAAC,eACF3M,OAAA;YAAKmQ,GAAG,EAAE7O,cAAe;YAAC8O,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAO;UAAE;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN3M,OAAA;QAAK+L,SAAS,EAAE,2GAA2G7L,WAAW,GAAG,mBAAmB,GAAG,QAAQ,EAAG;QAAA8L,QAAA,gBACxKhM,OAAA;UAAMuQ,QAAQ,EAAE3J,iBAAkB;UAACmF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBACxEhM,OAAA;YAAK+L,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnChM,OAAA;cACEwQ,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE7P,KAAM;cACb8P,QAAQ,EAAG7J,CAAC,IAAK;gBACf,MAAM8J,QAAQ,GAAG9J,CAAC,CAACsF,MAAM,CAACsE,KAAK;gBAC/B5P,QAAQ,CAAC8P,QAAQ,CAAC;gBAClB;cACF,CAAE;cACFC,WAAW,EAAC,+CAA+C;cAC3D7E,SAAS,EAAC,wJAAwJ;cAClK8E,QAAQ,EAAE/P,YAAa;cACvB,cAAW;YAAe;cAAA0L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3M,OAAA;YAAK+L,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChM,OAAA,CAACR,WAAW;cACVsR,YAAY,EAAE9P,cAAe;cAC7B+P,aAAa,EAAGC,OAAO,IAAK/P,iBAAiB,CAAC+P,OAAO,CAAE;cACvDC,SAAS,EAAEnQ;YAAa;cAAA0L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACF3M,OAAA;cACEwQ,IAAI,EAAC,QAAQ;cACbK,QAAQ,EAAE/P,YAAY,IAAI,CAACF,KAAK,CAACwC,IAAI,CAAC,CAAE;cACxC2I,SAAS,EAAC,uMAAuM;cACjNM,KAAK,EAAC,cAAc;cAAAL,QAAA,eAEpBhM,OAAA;gBAAAgM,QAAA,EAAOlL,YAAY,GAAG,YAAY,GAAG;cAAM;gBAAA0L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACK3M,OAAA;UAAK+L,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GAAC,iBACnD,EAAC,EAAA5L,qBAAA,GAAAX,kBAAkB,CAAC8G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAK1F,cAAc,CAAC,cAAAZ,qBAAA,uBAArDA,qBAAA,CAAuDqG,IAAI,KAAIzF,cAAc;QAAA;UAAAwL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELrM,QAAQ,CAAC+B,MAAM,GAAG,CAAC,iBAAIrC,OAAA;QAAK+L,SAAS,EAAC;MAAM;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAOA,SAASuE,GAAGA,CAAC;EAAEhR,WAAW;EAAEC;AAAyB,CAAC,EAAE;EACtD,oBACEH,OAAA,CAACC,aAAa;IAACC,WAAW,EAAEA,WAAY;IAACC,cAAc,EAAEA;EAAe;IAAAqM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAE/E;AAEA,eAAeuE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}