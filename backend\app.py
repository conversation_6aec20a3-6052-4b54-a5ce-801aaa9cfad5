from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Create app
app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define data model
class Query(BaseModel):
    query: str

# Root endpoint
@app.get("/")
def read_root():
    return {"message": "RailGPT API is running"}

# Query endpoint - note that this is just /query not /api/query
@app.post("/query")
def query(query_request: Query):
    print(f"Received query: {query_request.query}")
    return {
        "answer": "This is a mocked response from RailGPT backend.",
        "sources": ["SampleDoc.pdf Page 1", "SampleDoc.pdf Page 2"]
    }
