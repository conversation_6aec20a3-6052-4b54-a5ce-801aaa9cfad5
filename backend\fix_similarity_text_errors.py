"""
<PERSON><PERSON><PERSON> to fix the 'similarity' and 'text' errors in the RailGPT application.

This script:
1. Modifies the hybrid search functions to use a simpler similarity calculation
2. Adds error handling for the text extraction and LLM answer generation
"""

import os
import sys
import logging
import json
from typing import List, Dict, Any
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import required modules
from supabase_client import supabase, SupabaseClient

# Load environment variables
load_dotenv()

def fix_hybrid_search_functions():
    """Fix the hybrid search functions to use a simpler similarity calculation."""
    try:
        logger.info("Fixing hybrid search functions...")
        
        # Create a simpler version of the hybrid search functions
        sql = """
        -- Drop existing functions if they exist
        DROP FUNCTION IF EXISTS hybrid_search_document_chunks(text, vector, float, int);
        DROP FUNCTION IF EXISTS hybrid_search_website_chunks(text, vector, float, int);

        -- Create function for hybrid document search (using only vector similarity for now)
        CREATE OR REPLACE FUNCTION hybrid_search_document_chunks(
            query_text text,
            query_embedding vector(768),
            match_threshold float,
            match_count int
        )
        RETURNS TABLE (
            id UUID,
            document_id UUID,
            chunk_index INTEGER,
            page_number INTEGER,
            text TEXT,
            metadata JSONB,
            url TEXT,
            domain TEXT,
            title TEXT,
            similarity float,
            source_type TEXT
        )
        LANGUAGE plpgsql
        AS $$
        BEGIN
            RETURN QUERY
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                dc.metadata,
                d.file_path as url,
                d.main_category as domain,
                d.display_name as title,
                -- Use only vector similarity for now
                (1 - (dc.embedding <=> query_embedding)) as similarity,
                'document'::TEXT as source_type
            FROM document_chunks dc
            JOIN documents d ON dc.document_id = d.id
            WHERE 
                -- Vector similarity threshold
                (1 - (dc.embedding <=> query_embedding) > match_threshold)
                -- Text search condition (optional but helps with relevance)
                OR (dc.text ILIKE '%' || query_text || '%')
            ORDER BY similarity DESC
            LIMIT match_count;
        END;
        $$;

        -- Create function for hybrid website search (using only vector similarity for now)
        CREATE OR REPLACE FUNCTION hybrid_search_website_chunks(
            query_text text,
            query_embedding vector(768),
            match_threshold float,
            match_count int
        )
        RETURNS TABLE (
            id uuid,
            website_id uuid,
            chunk_index int,
            text text,
            metadata jsonb,
            url text,
            domain text,
            title text,
            similarity float,
            source_type TEXT
        )
        LANGUAGE plpgsql
        AS $$
        BEGIN
            RETURN QUERY
            SELECT
                wc.id,
                wc.website_id,
                wc.chunk_index,
                wc.text,
                wc.metadata,
                w.url,
                w.domain,
                w.title,
                -- Use only vector similarity for now
                (1 - (wc.embedding <=> query_embedding)) as similarity,
                'website'::TEXT as source_type
            FROM website_chunks wc
            JOIN websites w ON wc.website_id = w.id
            WHERE 
                -- Vector similarity threshold
                (1 - (wc.embedding <=> query_embedding) > match_threshold)
                -- Text search condition (optional but helps with relevance)
                OR (wc.text ILIKE '%' || query_text || '%')
            ORDER BY similarity DESC
            LIMIT match_count;
        END;
        $$;
        """
        
        # Execute the SQL
        result = supabase.execute_sql(sql)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fixing hybrid search functions: {result['error']}")
            return False
        
        logger.info("Successfully fixed hybrid search functions")
        return True
    
    except Exception as e:
        logger.error(f"Error fixing hybrid search functions: {str(e)}")
        return False

def fix_server_error_handling():
    """Create a patch file to fix the error handling in the server.py file."""
    try:
        logger.info("Creating patch file for server.py...")
        
        patch_content = """
# Apply this patch to server.py to fix the error handling

# Find the find_similar_chunks function and add this error handling code:

def find_similar_chunks(query_embedding: List[float], query_text: str = None, top_k: int = 10, use_hybrid_search: bool = True) -> List[Dict[str, Any]]:
    # ... existing code ...
    
    # Add this error handling for similarity
    for chunk in similar_chunks:
        if "similarity" not in chunk:
            # Add a default similarity value if missing
            chunk["similarity"] = 0.5
            logger.warning(f"Added default similarity value to chunk {chunk.get('id', 'unknown')}")
        
        # Ensure text field exists and is a string
        if "text" not in chunk or not isinstance(chunk["text"], str):
            # Add a default text value if missing or not a string
            chunk["text"] = "No text content available"
            logger.warning(f"Added default text value to chunk {chunk.get('id', 'unknown')}")
    
    return similar_chunks

# Find the generate_llm_answer function and add this error handling code:

def generate_llm_answer(query: str, similar_chunks: List[Dict[str, Any]], system_prompt: str = None, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    # ... existing code ...
    
    try:
        # Prepare context from similar chunks
        context_texts = []
        sources = []
        document_sources = []
        website_sources = []
        
        for chunk in similar_chunks:
            # Ensure text field exists and is a string
            if "text" not in chunk or not isinstance(chunk["text"], str):
                # Skip this chunk
                logger.warning(f"Skipping chunk with missing or invalid text: {chunk.get('id', 'unknown')}")
                continue
                
            # ... rest of the function ...
    
    except Exception as e:
        logger.error(f"Error generating LLM answer with {model_id}: {str(e)}")
        return f"I encountered an error while trying to generate an answer.", [], [], []
"""
        
        # Save the patch file
        with open('server_error_handling_patch.txt', 'w') as f:
            f.write(patch_content)
        
        logger.info("Successfully created patch file: server_error_handling_patch.txt")
        return True
    
    except Exception as e:
        logger.error(f"Error creating patch file: {str(e)}")
        return False

def create_server_patch():
    """Create a patched version of the server.py file with the error handling fixes."""
    try:
        logger.info("Creating patched version of server.py...")
        
        # Read the original server.py file
        with open('server.py', 'r') as f:
            server_content = f.read()
        
        # Add the error handling for similarity and text
        patched_content = server_content
        
        # Find the find_similar_chunks function
        if "def find_similar_chunks" in patched_content:
            # Find the end of the function
            function_start = patched_content.find("def find_similar_chunks")
            function_end = patched_content.find("return similar_chunks", function_start)
            
            if function_end > function_start:
                # Add the error handling code before the return statement
                error_handling_code = """
    # Add error handling for similarity and text
    for chunk in similar_chunks:
        if "similarity" not in chunk:
            # Add a default similarity value if missing
            chunk["similarity"] = 0.5
            logger.warning(f"Added default similarity value to chunk {chunk.get('id', 'unknown')}")
        
        # Ensure text field exists and is a string
        if "text" not in chunk or not isinstance(chunk["text"], str):
            # Add a default text value if missing or not a string
            chunk["text"] = "No text content available"
            logger.warning(f"Added default text value to chunk {chunk.get('id', 'unknown')}")
    
"""
                patched_content = patched_content[:function_end] + error_handling_code + patched_content[function_end:]
        
        # Find the generate_llm_answer function
        if "def generate_llm_answer" in patched_content:
            # Find the try block
            function_start = patched_content.find("def generate_llm_answer")
            try_start = patched_content.find("try:", function_start)
            
            if try_start > function_start:
                # Find the first line after the try statement
                try_end = patched_content.find("\n", try_start) + 1
                
                # Add the error handling code after the try statement
                error_handling_code = """
        # Ensure all chunks have valid text
        valid_chunks = []
        for chunk in similar_chunks:
            # Ensure text field exists and is a string
            if "text" not in chunk or not isinstance(chunk["text"], str):
                # Skip this chunk
                logger.warning(f"Skipping chunk with missing or invalid text: {chunk.get('id', 'unknown')}")
                continue
            valid_chunks.append(chunk)
        
        # Use only valid chunks
        similar_chunks = valid_chunks
        
        # If no valid chunks, return early
        if not similar_chunks:
            logger.warning("No valid chunks found for LLM answer generation")
            return "I couldn't find any valid information to answer your question.", [], [], []
            
"""
                patched_content = patched_content[:try_end] + error_handling_code + patched_content[try_end:]
        
        # Save the patched file
        with open('server_patched.py', 'w') as f:
            f.write(patched_content)
        
        logger.info("Successfully created patched file: server_patched.py")
        return True
    
    except Exception as e:
        logger.error(f"Error creating patched file: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting fixes for similarity and text errors...")
    
    # Fix hybrid search functions
    if fix_hybrid_search_functions():
        logger.info("Successfully fixed hybrid search functions")
    else:
        logger.error("Failed to fix hybrid search functions")
    
    # Fix server error handling
    if fix_server_error_handling():
        logger.info("Successfully created patch file for server.py")
    else:
        logger.error("Failed to create patch file for server.py")
    
    # Create patched server.py
    if os.path.exists('server.py'):
        if create_server_patch():
            logger.info("Successfully created patched version of server.py")
        else:
            logger.error("Failed to create patched version of server.py")
    else:
        logger.warning("server.py not found, skipping patch creation")
    
    logger.info("Fixes completed. Please apply the patches and restart the server.")
