"""
Simple script to check if the database has any documents.
"""
import os
import logging
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def check_documents():
    """Check if the database has any documents."""
    logger.info("Checking if the database has any documents...")
    
    # Query to get all documents
    query = """
    SELECT
        d.id,
        d.display_name,
        d.file_path,
        d.file_type,
        d.created_at,
        d.updated_at
    FROM
        documents d
    LIMIT 10
    """
    
    try:
        result = supabase.execute_query(query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error querying documents: {result['error']}")
            return False
        
        logger.info(f"Found {len(result)} documents")
        
        # Print document details
        for i, doc in enumerate(result):
            logger.info(f"Document {i+1}:")
            logger.info(f"  ID: {doc.get('id')}")
            logger.info(f"  Name: {doc.get('display_name')}")
            logger.info(f"  Path: {doc.get('file_path')}")
            logger.info(f"  Type: {doc.get('file_type')}")
            logger.info("")
        
        return len(result) > 0
    except Exception as e:
        logger.error(f"Error checking documents: {str(e)}")
        return False

def check_document_chunks():
    """Check if the database has any document chunks."""
    logger.info("Checking if the database has any document chunks...")
    
    # Query to get all document chunks
    query = """
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    LIMIT 10
    """
    
    try:
        result = supabase.execute_query(query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error querying document chunks: {result['error']}")
            return False
        
        logger.info(f"Found {len(result)} document chunks")
        
        # Print chunk details
        for i, chunk in enumerate(result):
            logger.info(f"Chunk {i+1}:")
            logger.info(f"  ID: {chunk.get('id')}")
            logger.info(f"  Document ID: {chunk.get('document_id')}")
            logger.info(f"  Chunk Index: {chunk.get('chunk_index')}")
            logger.info(f"  Page Number: {chunk.get('page_number')}")
            logger.info(f"  Filename: {chunk.get('filename')}")
            logger.info(f"  Text: {chunk.get('text')[:100]}..." if chunk.get('text') else "  Text: None")
            logger.info("")
        
        return len(result) > 0
    except Exception as e:
        logger.error(f"Error checking document chunks: {str(e)}")
        return False

def main():
    """Main function."""
    has_documents = check_documents()
    has_chunks = check_document_chunks()
    
    if has_documents and has_chunks:
        logger.info("Database has documents and chunks")
    elif has_documents:
        logger.info("Database has documents but no chunks")
    elif has_chunks:
        logger.info("Database has chunks but no documents")
    else:
        logger.info("Database has no documents or chunks")

if __name__ == "__main__":
    main()
