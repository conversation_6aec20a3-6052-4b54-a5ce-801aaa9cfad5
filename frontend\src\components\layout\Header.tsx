import React from 'react';
import { Link, useLocation } from 'react-router-dom';

interface HeaderProps {
  title?: string;
  onSidebarToggle?: () => void;
  sidebarOpen?: boolean;
}

const Header: React.FC<HeaderProps> = ({ title = 'RailGPT', onSidebarToggle, sidebarOpen = false }) => {
  const location = useLocation();
  const isDocumentsActive = location.pathname.includes('/documents');
  const isWebsitesActive = location.pathname.includes('/websites');
  const isCategoriesActive = location.pathname.includes('/categories');

  return (
    <header className="bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50 transition-colors duration-300">
      <div className="container mx-auto flex flex-col sm:flex-row justify-between items-center">
        <div className="flex items-center mb-4 sm:mb-0">
          {location.pathname === '/' && onSidebarToggle && (
            <button
              onClick={onSidebarToggle}
              className="p-2 mr-3 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50"
              title={sidebarOpen ? "Close chat history" : "Open chat history"}
            >
              ☰
            </button>
          )}
          <h1 className="text-xl font-bold">{title}</h1>
        </div>

        <nav className="flex gap-4">
          <Link
            to="/"
            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${
              location.pathname === '/' ? 'bg-blue-700 font-medium' : ''
            }`}
          >
            Chat
          </Link>
          <Link
            to="/documents"
            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${
              isDocumentsActive ? 'bg-blue-700 font-medium' : ''
            }`}
          >
            Documents
          </Link>
          <Link
            to="/websites"
            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${
              isWebsitesActive ? 'bg-blue-700 font-medium' : ''
            }`}
          >
            Websites
          </Link>
          <Link
            to="/categories"
            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${
              isCategoriesActive ? 'bg-blue-700 font-medium' : ''
            }`}
          >
            📂 Categories
          </Link>
          <Link
            to="/settings"
            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${
              location.pathname === '/settings' ? 'bg-blue-700 font-medium' : ''
            }`}
          >
            ⚙️ Settings
          </Link>
        </nav>
      </div>
    </header>
  );
};

export default Header;
