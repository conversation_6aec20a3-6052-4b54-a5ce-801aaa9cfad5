from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import os
import fitz  # PyMuPDF
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global variable to store document chunks
DOCUMENT_CHUNKS = []

# Create FastAPI app
app = FastAPI()

# Configure CORS - allow all origins for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define data models
class QueryRequest(BaseModel):
    query: str

class ChunkResponse(BaseModel):
    filename: str
    page: int
    chunk_id: str
    text: str

# Document processing functions
def clean_text(text: str) -> str:
    """Clean and normalize text from PDF extraction."""
    # Replace multiple whitespace with single space
    text = re.sub(r'\s+', ' ', text)
    # Remove any leading/trailing whitespace
    text = text.strip()
    return text

def create_chunks(text: str, filename: str, page_num: int, target_size: int = 400, overlap: int = 50) -> List[Dict[str, Any]]:
    """Split text into chunks of approximately target_size words with overlap."""
    words = text.split()
    total_words = len(words)
    chunks = []
    
    if total_words == 0:
        return chunks
    
    # If text is smaller than target size, return as a single chunk
    if total_words <= target_size:
        chunk_id = f"{os.path.splitext(filename)[0]}_{page_num}_0"
        chunks.append({
            "filename": filename,
            "page": page_num,
            "chunk_id": chunk_id,
            "text": text
        })
        return chunks
    
    # Create overlapping chunks
    chunk_index = 0
    start_idx = 0
    
    while start_idx < total_words:
        end_idx = min(start_idx + target_size, total_words)
        
        # Create chunk
        chunk_text = " ".join(words[start_idx:end_idx])
        chunk_id = f"{os.path.splitext(filename)[0]}_{page_num}_{chunk_index}"
        chunks.append({
            "filename": filename,
            "page": page_num,
            "chunk_id": chunk_id,
            "text": chunk_text
        })
        
        # If this is the last chunk, break
        if end_idx >= total_words:
            break
            
        # Move to next chunk start, accounting for overlap
        start_idx = end_idx - overlap
        chunk_index += 1
    
    return chunks

def load_documents(data_dir: str = './data') -> List[Dict[str, Any]]:
    """Load and process all PDF documents from a directory."""
    global DOCUMENT_CHUNKS
    DOCUMENT_CHUNKS = []
    
    # Ensure data directory exists
    if not os.path.exists(data_dir):
        logger.warning(f"Data directory {data_dir} does not exist")
        return DOCUMENT_CHUNKS
    
    # Get all PDF files in the directory
    pdf_files = [f for f in os.listdir(data_dir) if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        logger.warning(f"No PDF files found in {data_dir}")
        return DOCUMENT_CHUNKS
    
    logger.info(f"Found {len(pdf_files)} PDF files in {data_dir}")
    
    # Process each PDF file
    for filename in pdf_files:
        file_path = os.path.join(data_dir, filename)
        logger.info(f"Processing {file_path}")
        
        try:
            # Open PDF
            doc = fitz.open(file_path)
            
            # Process each page
            for page_num, page in enumerate(doc, 1):  # 1-based page numbering for readability
                text = page.get_text()
                cleaned_text = clean_text(text)
                
                if not cleaned_text:
                    logger.warning(f"No text extracted from page {page_num} in {filename}")
                    continue
                
                # Create chunks from page text
                page_chunks = create_chunks(cleaned_text, filename, page_num)
                DOCUMENT_CHUNKS.extend(page_chunks)
                
                logger.info(f"Processed page {page_num} of {filename}: created {len(page_chunks)} chunks")
            
            # Close the document
            doc.close()
            
        except Exception as e:
            logger.error(f"Error processing {filename}: {str(e)}")
    
    logger.info(f"Finished processing. Total chunks created: {len(DOCUMENT_CHUNKS)}")
    return DOCUMENT_CHUNKS

# Load documents at startup
@app.on_event("startup")
async def startup_event():
    logger.info("Loading documents on startup")
    try:
        # Get the absolute path for the data directory
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
        chunks = load_documents(data_dir)
        logger.info(f"Loaded {len(chunks)} document chunks on startup")
    except Exception as e:
        logger.error(f"Error loading documents: {str(e)}")

# Root endpoint
@app.get("/")
def read_root():
    return {"message": "RailGPT API is running", "status": "online"}

# Chunks endpoint
@app.get("/api/chunks")
def get_chunks():
    logger.info(f"Returning first 5 chunks out of {len(DOCUMENT_CHUNKS)}")
    return DOCUMENT_CHUNKS[:5] if DOCUMENT_CHUNKS else []

# Main query endpoint
@app.post("/api/query")
def query(request: QueryRequest):
    logger.info(f"Received query: {request.query}")
    
    # Create a response that references our actual documents
    sources = []
    if DOCUMENT_CHUNKS:
        # Use actual document references for sources
        sources = [f"{chunk['filename']} Page {chunk['page']}" for chunk in DOCUMENT_CHUNKS[:2]]
    else:
        sources = ["SampleDoc.pdf Page 1", "SampleDoc.pdf Page 2"]
    
    return {
        "answer": "This is a mocked response from RailGPT backend.",
        "sources": sources
    }

# Document extraction details endpoint
@app.get("/api/documents/{document_id}/extraction-details")
def get_document_extraction_details(document_id: str):
    logger.info(f"Fetching extraction details for document ID: {document_id}")
    
    # In a production system, this would query a database
    # For now, generate mock details based on document ID
    
    # Extract details from document ID if possible
    doc_type = "pdf"
    if "-" in document_id:
        parts = document_id.split("-")
        if len(parts) > 1:
            doc_type = parts[0].lower()
    
    # Generate sample content based on document type
    if doc_type == "pdf":
        sample_content = f"""# Document Content for {document_id}

This is extracted content from a PDF document. The document appears to contain information about railway operations and schedules.

## Section 1: Introduction
The Indian Railways operates approximately 14,000 trains daily, including passenger and freight services.

## Section 2: Schedules
Train Number: 12301
Route: Delhi to Mumbai
Departure: 16:55
Arrival: 08:15

Train Number: 12302
Route: Mumbai to Delhi
Departure: 17:40
Arrival: 10:05
"""
    elif doc_type == "doc" or doc_type == "docx":
        sample_content = f"""# Document Content for {document_id}

This is extracted content from a Word document. The document contains policy information.

## Railway Safety Policy

All railway staff must adhere to the following safety protocols:

1. Regular equipment inspection
2. Adherence to signal protocols
3. Communication verification before departure
4. Weather condition assessment
"""
    else:
        sample_content = f"Content extracted from document {document_id}. This is a generic representation."
    
    return {
        "extractedContent": sample_content,
        "extractionMethod": "PyMuPDF",
        "qualityScore": 85,
        "processingTime": 1250,
        "chunks": 12,
        "warnings": [],
        "fallbackReason": ""
    }

# Document content endpoint
@app.get("/api/documents/{document_id}/content")
def get_document_content(document_id: str):
    logger.info(f"Fetching content for document ID: {document_id}")
    
    # Similar to extraction details but simpler response
    sample_content = f"Content from document {document_id}. This includes the full text of the document extracted using our processing pipeline."
    
    return {
        "content": sample_content,
        "extraction_method": "PyMuPDF",
        "quality_score": 80,
        "processing_time": 1120,
        "chunks_count": 10
    }

# Website extraction details endpoint
@app.get("/api/websites/{website_id}/extraction-details")
def get_website_extraction_details(website_id: str):
    logger.info(f"Fetching extraction details for website ID: {website_id}")
    
    # Generate sample website content
    sample_content = f"""# Website Content for {website_id}

## Indian Railways Information

Welcome to the official website of Indian Railways. Below you'll find information about our services.

### Popular Routes
- Delhi to Mumbai
- Chennai to Kolkata
- Bangalore to Hyderabad

### Ticket Booking
Tickets can be booked online through our IRCTC portal or at railway stations.

### Latest News
- New Vande Bharat Express routes announced
- Railway electrification project completed on Eastern corridor
- Special trains for upcoming festival season
"""
    
    return {
        "extractedContent": sample_content,
        "extractionMethod": "Trafilatura",
        "fallbackHistory": ["Trafilatura", "BS4"],
        "contentQuality": 90,
        "warnings": [],
        "processingTime": 850,
        "chunks": 8
    }

# Website content endpoint
@app.get("/api/websites/{website_id}/content")
def get_website_content(website_id: str):
    logger.info(f"Fetching content for website ID: {website_id}")
    
    # Simplified response
    sample_content = f"Content from website {website_id}. This is the extracted HTML content processed and cleaned for readability."
    
    return {
        "content": sample_content,
        "extraction_method": "BS4",
        "quality_score": 85,
        "processing_time": 780,
        "pages_processed": 3,
        "total_links": 24
    }
