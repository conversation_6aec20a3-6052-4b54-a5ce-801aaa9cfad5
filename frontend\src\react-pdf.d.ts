declare module 'react-pdf' {
  import { FC, ReactNode } from 'react';

  interface DocumentProps {
    file: string | { url: string } | { data: ArrayBuffer | Uint8Array };
    onLoadSuccess?: (pdf: { numPages: number }) => void;
    onLoadError?: (error: Error) => void;
    loading?: ReactNode;
    error?: ReactNode;
    children?: ReactNode;
  }

  interface PageProps {
    pageNumber: number;
    scale?: number;
    renderTextLayer?: boolean;
    renderAnnotationLayer?: boolean;
    error?: string | ReactNode;
  }

  // Export components as Function Components
  export const Document: FC<DocumentProps>;
  export const Page: FC<PageProps>;
  
  // Namespace for pdfjs global settings
  export const pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: string;
    };
    version: string;
  };
}
