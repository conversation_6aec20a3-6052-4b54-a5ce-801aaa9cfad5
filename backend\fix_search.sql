-- Fix script for RailGPT search functions
-- Run this in the Supabase SQL Editor

-- First, let's check if the functions exist
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_type = 'FUNCTION' 
AND routine_name IN (
  'direct_search_document_chunks',
  'search_document_chunks',
  'hybrid_search_document_chunks',
  'search_website_chunks',
  'hybrid_search_website_chunks'
);

-- Create a simpler direct search function for document chunks that doesn't rely on embeddings
DROP FUNCTION IF EXISTS simple_search_document_chunks(text, int);
CREATE OR REPLACE FUNCTION simple_search_document_chunks(
    query_text text,
    match_count int
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        dc.metadata,
        d.file_path as url,
        d.main_category as domain,
        d.display_name as title,
        0.8 as similarity,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE 
        dc.text IS NOT NULL 
        AND dc.text != ''
        AND (
            dc.text ILIKE '%' || query_text || '%'
            OR d.display_name ILIKE '%' || query_text || '%'
            OR d.filename ILIKE '%' || query_text || '%'
        )
    ORDER BY 
        CASE 
            WHEN dc.text ILIKE '%' || query_text || '%' THEN 1
            WHEN d.display_name ILIKE '%' || query_text || '%' THEN 2
            ELSE 3
        END
    LIMIT match_count;
END;
$$;

-- Create a simpler direct search function for website chunks that doesn't rely on embeddings
DROP FUNCTION IF EXISTS simple_search_website_chunks(text, int);
CREATE OR REPLACE FUNCTION simple_search_website_chunks(
    query_text text,
    match_count int
)
RETURNS TABLE (
    id UUID,
    website_id UUID,
    chunk_index INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id,
        wc.website_id,
        wc.chunk_index,
        wc.text,
        wc.metadata,
        w.url,
        w.domain,
        w.title,
        0.7 as similarity,
        'website'::TEXT as source_type
    FROM website_chunks wc
    JOIN websites w ON wc.website_id = w.id
    WHERE 
        wc.text IS NOT NULL 
        AND wc.text != ''
        AND (
            wc.text ILIKE '%' || query_text || '%'
            OR w.title ILIKE '%' || query_text || '%'
            OR w.domain ILIKE '%' || query_text || '%'
        )
    ORDER BY 
        CASE 
            WHEN wc.text ILIKE '%' || query_text || '%' THEN 1
            WHEN w.title ILIKE '%' || query_text || '%' THEN 2
            ELSE 3
        END
    LIMIT match_count;
END;
$$;

-- Create a function to get all document chunks
DROP FUNCTION IF EXISTS get_all_document_chunks(int);
CREATE OR REPLACE FUNCTION get_all_document_chunks(
    match_count int
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        dc.metadata,
        d.file_path as url,
        d.main_category as domain,
        d.display_name as title,
        0.5 as similarity,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE dc.text IS NOT NULL AND dc.text != ''
    ORDER BY dc.created_at DESC
    LIMIT match_count;
END;
$$;

-- Create a function to get all website chunks
DROP FUNCTION IF EXISTS get_all_website_chunks(int);
CREATE OR REPLACE FUNCTION get_all_website_chunks(
    match_count int
)
RETURNS TABLE (
    id UUID,
    website_id UUID,
    chunk_index INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id,
        wc.website_id,
        wc.chunk_index,
        wc.text,
        wc.metadata,
        w.url,
        w.domain,
        w.title,
        0.5 as similarity,
        'website'::TEXT as source_type
    FROM website_chunks wc
    JOIN websites w ON wc.website_id = w.id
    WHERE wc.text IS NOT NULL AND wc.text != ''
    ORDER BY wc.created_at DESC
    LIMIT match_count;
END;
$$;
