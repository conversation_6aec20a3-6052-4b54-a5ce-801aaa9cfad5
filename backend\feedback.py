import os
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List, Dict, Optional
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default notification email if none configured
DEFAULT_FEEDBACK_EMAIL = "<EMAIL>"

class FeedbackData(BaseModel):
    query: str
    answer: str
    issue_type: str  # "Inaccurate", "Incomplete", "Offensive", "Too Slow", "Other"
    comment: Optional[str] = None
    model: Optional[str] = None
    chat_id: Optional[str] = None
    timestamp: str

class FeedbackEmailConfig(BaseModel):
    emails: List[str] = []
    enabled: bool = True

# In-memory storage for feedback emails (should be moved to a database in production)
# This is just a placeholder - in real deployment this should be in a database
feedback_config = FeedbackEmailConfig(
    emails=[os.getenv("FEEDBACK_EMAIL", DEFAULT_FEEDBACK_EMAIL)],
    enabled=True
)

def get_feedback_emails() -> List[str]:
    """Get the list of configured feedback emails."""
    return feedback_config.emails

def update_feedback_emails(emails: List[str]) -> bool:
    """Update the list of configured feedback emails."""
    if len(emails) > 5:
        logger.warning("Too many feedback emails provided. Maximum is 5.")
        return False
    
    feedback_config.emails = emails
    logger.info(f"Updated feedback emails: {emails}")
    return True

def send_feedback_email(feedback: FeedbackData) -> Dict[str, any]:
    """Send feedback notification email to configured addresses."""
    if not feedback_config.enabled or not feedback_config.emails:
        logger.warning("Feedback email notifications are disabled or no emails configured.")
        return {"success": False, "message": "Email notifications are disabled"}
    
    try:
        # Configure SMTP settings (should be from environment variables in production)
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        smtp_username = os.getenv("SMTP_USERNAME", "")
        smtp_password = os.getenv("SMTP_PASSWORD", "")
        
        # If SMTP credentials are not configured, log a warning but don't fail
        if not smtp_username or not smtp_password:
            logger.warning("SMTP credentials not configured. Email not sent.")
            return {"success": False, "message": "SMTP credentials not configured"}
        
        # Create email content
        msg = MIMEMultipart()
        msg['Subject'] = f'IR App Feedback: {feedback.issue_type}'
        msg['From'] = smtp_username
        
        # Format the email body
        body = f"""
        <html>
        <body>
        <h2>IR App Feedback Received</h2>
        <p><strong>Issue Type:</strong> {feedback.issue_type}</p>
        <p><strong>Time:</strong> {feedback.timestamp}</p>
        <p><strong>Model Used:</strong> {feedback.model or 'Unknown'}</p>
        
        <h3>User Query:</h3>
        <div style="background-color: #f5f5f5; padding: 10px; border-radius: 5px;">
        {feedback.query}
        </div>
        
        <h3>AI Answer:</h3>
        <div style="background-color: #f5f5f5; padding: 10px; border-radius: 5px;">
        {feedback.answer}
        </div>
        
        {f'<h3>User Comment:</h3><div style="background-color: #f5f5f5; padding: 10px; border-radius: 5px;">{feedback.comment}</div>' if feedback.comment else ''}
        
        <p><strong>Chat ID:</strong> {feedback.chat_id or 'N/A'}</p>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(body, 'html'))
        
        # Send email to all configured addresses
        for email in feedback_config.emails:
            try:
                msg['To'] = email
                server = smtplib.SMTP(smtp_server, smtp_port)
                server.starttls()
                server.login(smtp_username, smtp_password)
                server.send_message(msg)
                server.quit()
                logger.info(f"Feedback email sent to {email}")
            except Exception as e:
                logger.error(f"Failed to send feedback email to {email}: {str(e)}")
        
        return {"success": True, "message": "Feedback email sent successfully"}
    except Exception as e:
        logger.error(f"Error sending feedback email: {str(e)}")
        return {"success": False, "message": f"Error sending email: {str(e)}"}
