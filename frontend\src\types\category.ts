// Category type definitions for RailGPT

export interface CategoryBase {
  name: string;
  type: string; // main_category, category, sub_category, minor_category
  parent_id?: string | null;
  description?: string | null;
  sort_order?: number;
}

export interface CategoryCreate extends CategoryBase {
  // All fields from CategoryBase are required for creation
}

export interface CategoryUpdate {
  name?: string;
  description?: string;
  sort_order?: number;
  is_active?: boolean;
}

export interface Category extends CategoryBase {
  id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CategoryHierarchy extends Category {
  full_path: string;
  level: number;
  children?: CategoryHierarchy[];
}

export interface DocumentCategoryUpdate {
  main_category?: string;
  category?: string;
  sub_category?: string;
  minor_category?: string;
}

export interface WebsiteCategoryUpdate {
  category?: string;
  website_category_id?: string;
}

export interface WebsiteCategory {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface WebsiteCategoryCreate {
  name: string;
  description?: string;
  sort_order?: number;
}

// Category type options
export const CATEGORY_TYPES = [
  { value: 'main_category', label: 'Main Category' },
  { value: 'category', label: 'Category' },
  { value: 'sub_category', label: 'Sub Category' },
  { value: 'minor_category', label: 'Minor Category' }
] as const;

export type CategoryType = typeof CATEGORY_TYPES[number]['value'];
