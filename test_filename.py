#!/usr/bin/env python3

import sys
import os
sys.path.append('backend')

from server import search_documents_by_content

def test_filename_mapping():
    print("=== Testing filename mapping ===")
    
    # Test the direct search function
    result = search_documents_by_content('authority transfer', limit=2)
    
    if not result:
        print("❌ No results from search_documents_by_content")
        return
    
    print(f"✅ Found {len(result)} results")
    
    for i, chunk in enumerate(result):
        print(f"\nChunk {i+1}:")
        print(f"  Keys available: {list(chunk.keys())}")
        
        # Check all possible filename fields
        filename_fields = ['filename', 'display_name', 'file_name', 'document_name', 'name']
        for field in filename_fields:
            value = chunk.get(field)
            if value:
                print(f"  {field}: {value}")
        
        # Show text preview
        text = chunk.get('text', '')
        print(f"  text preview: {text[:100]}...")

if __name__ == "__main__":
    test_filename_mapping() 