#!/usr/bin/env python3
import requests
import json

# Test query
response = requests.post('http://localhost:8000/api/query', 
                        headers={'Content-Type': 'application/json'}, 
                        json={'query': 'authority transfer'})

if response.status_code == 200:
    data = response.json()
    doc_sources = data.get('document_sources', [])
    llm_fallback = data.get('llm_fallback', False)
    
    print(f"✅ Query successful!")
    print(f"Document sources found: {len(doc_sources)}")
    print(f"LLM fallback used: {llm_fallback}")
    
    if doc_sources:
        print(f"First source link: {doc_sources[0].get('link', 'No link')}")
        print(f"First source name: {doc_sources[0].get('name', 'No name')}")
    else:
        print("No document sources found")
else:
    print(f"❌ Query failed: {response.status_code}") 