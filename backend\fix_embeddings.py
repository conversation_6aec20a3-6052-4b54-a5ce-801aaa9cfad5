#!/usr/bin/env python3
"""
Standalone script to fix embeddings and test local search.
"""

import requests
import json
import numpy as np
from typing import List, Dict, Any

# Configuration
API_URL = "http://localhost:8000"

def cosine_similarity(embedding1, embedding2):
    """Calculate cosine similarity between two embeddings."""
    try:
        # Convert to numpy arrays
        embedding1 = np.array(embedding1, dtype=np.float32)
        embedding2 = np.array(embedding2, dtype=np.float32)
        
        # Compute cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    except Exception as e:
        print(f"Error calculating similarity: {str(e)}")
        return 0.0

def test_local_search_simulation():
    """Simulate local search with actual chunks."""
    print("🔬 Testing Local Search Simulation")
    print("="*50)
    
    # Get chunks from server
    try:
        chunks_response = requests.get(f"{API_URL}/api/chunks", timeout=10)
        if chunks_response.status_code != 200:
            print(f"❌ Failed to get chunks: {chunks_response.status_code}")
            return
        
        chunks = chunks_response.json()
        print(f"📊 Total chunks loaded: {len(chunks)}")
        
        # Filter document chunks
        document_chunks = [chunk for chunk in chunks if chunk.get('source_type') == 'document']
        website_chunks = [chunk for chunk in chunks if chunk.get('source_type') == 'website']
        
        print(f"📄 Document chunks: {len(document_chunks)}")
        print(f"🌐 Website chunks: {len(website_chunks)}")
        
        # Test query
        test_query = "FSDS monitoring system"
        print(f"\n🔍 Testing query: '{test_query}'")
        
        # Create a simple query embedding (mock for testing)
        query_embedding = [0.1] * 768  # Simple mock embedding
        
        # Test document search simulation
        print("\n📄 Document Search Results:")
        doc_matches = []
        
        for chunk in document_chunks[:10]:  # Test first 10 chunks
            chunk_text = chunk.get('text', '')
            
            # Simple text matching for simulation
            if any(term.lower() in chunk_text.lower() for term in ['fsds', 'monitoring', 'system']):
                # Create mock similarity score
                similarity = 0.8 if 'fsds' in chunk_text.lower() else 0.6
                doc_matches.append({
                    'chunk': chunk,
                    'similarity': similarity,
                    'text_preview': chunk_text[:100] + "..."
                })
        
        # Sort by similarity
        doc_matches.sort(key=lambda x: x['similarity'], reverse=True)
        
        print(f"Found {len(doc_matches)} document matches:")
        for i, match in enumerate(doc_matches[:3]):
            print(f"  {i+1}. Similarity: {match['similarity']:.3f}")
            print(f"     Text: {match['text_preview']}")
        
        # Test website search simulation
        print("\n🌐 Website Search Results:")
        web_matches = []
        
        for chunk in website_chunks:
            chunk_text = chunk.get('text', '')
            
            # Simple text matching for simulation
            if any(term.lower() in chunk_text.lower() for term in ['railway', 'train', 'transport']):
                # Create mock similarity score
                similarity = 0.7 if 'railway' in chunk_text.lower() else 0.5
                web_matches.append({
                    'chunk': chunk,
                    'similarity': similarity,
                    'text_preview': chunk_text[:100] + "..."
                })
        
        # Sort by similarity
        web_matches.sort(key=lambda x: x['similarity'], reverse=True)
        
        print(f"Found {len(web_matches)} website matches:")
        for i, match in enumerate(web_matches[:3]):
            print(f"  {i+1}. Similarity: {match['similarity']:.3f}")
            print(f"     Text: {match['text_preview']}")
        
        # Determine what should happen
        print(f"\n🎯 Expected Behavior:")
        if doc_matches:
            print(f"✅ Should use document answer (found {len(doc_matches)} relevant document chunks)")
            print(f"✅ LLM fallback should be: False")
        elif web_matches:
            print(f"✅ Should use website answer (found {len(web_matches)} relevant website chunks)")
            print(f"✅ LLM fallback should be: False")
        else:
            print(f"✅ Should use LLM fallback (no relevant chunks found)")
            print(f"✅ LLM fallback should be: True")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_actual_query():
    """Test the actual query endpoint."""
    print(f"\n🔍 Testing Actual Query Endpoint")
    print("="*30)
    
    try:
        query_data = {
            "query": "FSDS monitoring system",
            "model": "gemini-2.0-flash",
            "fallback_enabled": True
        }
        
        response = requests.post(
            f"{API_URL}/api/query",
            json=query_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Query successful")
            print(f"📄 Document answer: {result.get('document_answer') is not None}")
            print(f"🌐 Website answer: {result.get('website_answer') is not None}")
            print(f"🧠 LLM fallback used: {result.get('llm_fallback', False)}")
            print(f"📊 Total sources: {len(result.get('sources', []))}")
            
            # Compare with expected
            if result.get('llm_fallback', False):
                print(f"❌ ISSUE: Using LLM fallback when document content should be available")
            else:
                print(f"✅ SUCCESS: Using document/website content as expected")
        else:
            print(f"❌ Query failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Error with query: {str(e)}")

if __name__ == "__main__":
    test_local_search_simulation()
    test_actual_query()
