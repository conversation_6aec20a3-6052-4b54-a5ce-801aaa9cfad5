"""
<PERSON><PERSON><PERSON> to test the search priority logic in RailGPT without requiring the server to be running.
"""
import os
import logging
import json
from dotenv import load_dotenv
from supabase_client import supabase
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def search_documents_by_title(query: str, limit: int = 5):
    """Search for documents by title."""
    logger.info(f"Searching for documents with title containing '{query}'...")

    # Prepare the query
    search_query = f"""
    SELECT
        d.id,
        d.display_name,
        d.file_path,
        d.file_type,
        d.created_at,
        d.updated_at
    FROM
        documents d
    WHERE
        LOWER(d.display_name) LIKE LOWER('%{query}%')
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by title: {result['error']}")
            return []

        logger.info(f"Found {len(result)} documents matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by title: {str(e)}")
        return []

def get_document_chunks(document_id: str, limit: int = 3):
    """Get chunks for a specific document."""
    logger.info(f"Getting chunks for document {document_id}...")

    # Prepare the query
    chunks_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.document_id = '{document_id}'
    ORDER BY
        dc.page_number, dc.chunk_index
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error getting document chunks: {result['error']}")
            return []

        logger.info(f"Found {len(result)} chunks for document {document_id}")
        return result
    except Exception as e:
        logger.error(f"Error getting document chunks: {str(e)}")
        return []

def search_documents_by_content(query: str, limit: int = 5):
    """Search for documents by content using text search."""
    logger.info(f"Searching for documents with content containing '{query}'...")

    # Extract key terms for better search
    import re
    from string import punctuation

    # Clean the query and extract key terms
    clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
    words = clean_query.split()
    stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
    key_terms = [word for word in words if word not in stop_words and len(word) > 2]

    # If no key terms found, use the original query
    if not key_terms:
        key_terms = [query]

    logger.info(f"Extracted key terms for content search: {key_terms}")

    # Create a tsquery string with OR operators between terms for broader matches
    ts_query_terms = " | ".join([term.replace("'", "''") for term in key_terms])

    # Sanitize the full query for logging
    sanitized_full_query = query.replace("'", "''")

    # Prepare the query with both exact phrase matching and key term matching
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type,
        GREATEST(
            ts_rank(to_tsvector('english', dc.text), to_tsquery('english', '{ts_query_terms}')),
            ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', '{sanitized_full_query}'))
        ) AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        to_tsvector('english', dc.text) @@ to_tsquery('english', '{ts_query_terms}')
        OR to_tsvector('english', dc.text) @@ plainto_tsquery('english', '{sanitized_full_query}')
        OR dc.text ILIKE '%{sanitized_full_query}%'
    ORDER BY
        similarity DESC
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by content: {result['error']}")
            return []

        logger.info(f"Found {len(result)} document chunks matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by content: {str(e)}")
        # Try a simpler query if the complex one fails
        try:
            logger.info("Trying simpler content search query...")
            simple_query = f"""
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                d.display_name as filename,
                d.file_path as url,
                'document' as source_type,
                1.0 AS similarity
            FROM
                document_chunks dc
            JOIN
                documents d ON dc.document_id = d.id
            WHERE
                dc.text ILIKE '%{sanitized_full_query}%'
            LIMIT {limit}
            """
            result = supabase.execute_query(simple_query)

            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error with simple content search: {result['error']}")
                return []

            logger.info(f"Found {len(result)} document chunks with simple search")
            return result
        except Exception as simple_error:
            logger.error(f"Simple content search also failed: {str(simple_error)}")
            return []

def search_websites_by_content(query: str, limit: int = 5):
    """Search for websites by content using text search."""
    logger.info(f"Searching for websites with content containing '{query}'...")

    # Extract key terms for better search
    import re
    from string import punctuation

    # Clean the query and extract key terms
    clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
    words = clean_query.split()
    stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
    key_terms = [word for word in words if word not in stop_words and len(word) > 2]

    # If no key terms found, use the original query
    if not key_terms:
        key_terms = [query]

    logger.info(f"Extracted key terms for website search: {key_terms}")

    # Search for each key term
    all_results = []
    for term in key_terms:
        # Sanitize the term
        sanitized_term = term.replace("'", "''")

        # Query to search website chunks by content
        website_query = f"""
        SELECT
            wc.id,
            wc.website_id,
            wc.chunk_index,
            wc.text,
            wc.url,
            w.name as website_name,
            'website' as source_type,
            ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', '{sanitized_term}')) AS similarity
        FROM
            website_chunks wc
        JOIN
            websites w ON wc.website_id = w.id
        WHERE
            to_tsvector('english', wc.text) @@ plainto_tsquery('english', '{sanitized_term}')
            OR wc.text ILIKE '%{sanitized_term}%'
        ORDER BY
            similarity DESC
        LIMIT {limit}
        """

        try:
            result = supabase.execute_query(website_query)

            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error searching websites by content: {result['error']}")
            else:
                logger.info(f"Found {len(result)} website chunks matching term '{term}'")
                all_results.extend(result)
        except Exception as e:
            logger.error(f"Error searching websites by term '{term}': {str(e)}")

    logger.info(f"Found {len(all_results)} total website chunks")
    return all_results

def test_search_priority():
    """Test the search priority logic."""
    logger.info("Testing search priority logic...")

    # Test queries
    test_queries = [
        "What is the full form of ACP?",
        "What is the full form of FSDS?",
        "What is the Rapid Response app?",
        "What is VASP and who developed it?"
    ]

    for query in test_queries:
        logger.info(f"\n=== Testing query: '{query}' ===\n")

        # Extract key terms for better search
        import re
        from string import punctuation

        # Clean the query and extract key terms
        clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
        words = clean_query.split()
        stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
        key_terms = [word for word in words if word not in stop_words and len(word) > 2]

        # If no key terms found, use the original query
        if not key_terms:
            key_terms = [query]

        logger.info(f"Extracted key terms: {key_terms}")

        # STEP 1: Search for documents by title
        title_results = []
        for term in key_terms:
            results = search_documents_by_title(term)
            title_results.extend(results)

        # Remove duplicates
        unique_ids = set()
        unique_title_results = []
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id and doc_id not in unique_ids:
                unique_ids.add(doc_id)
                unique_title_results.append(doc)

        title_results = unique_title_results

        # Get chunks for each document found by title
        direct_document_chunks = []
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id:
                chunks = get_document_chunks(doc_id)
                direct_document_chunks.extend(chunks)

        # STEP 2: Search for documents by content
        content_results = search_documents_by_content(query)

        # Combine document results
        all_document_chunks = direct_document_chunks + content_results

        # STEP 3: If no document chunks found, search for website chunks
        website_chunks = []
        if not all_document_chunks:
            logger.info("No document chunks found, searching for website chunks...")
            website_chunks = search_websites_by_content(query)

        # STEP 4: Determine which source to use
        if all_document_chunks:
            logger.info(f"✅ Using {len(all_document_chunks)} document chunks (PRIORITY 1)")
            # Print the first chunk
            if all_document_chunks:
                chunk = all_document_chunks[0]
                logger.info(f"Sample document chunk: {chunk.get('filename')} (Page {chunk.get('page_number')})")
                logger.info(f"Text: {chunk.get('text', '')[:100]}...")
        elif website_chunks:
            logger.info(f"✅ Using {len(website_chunks)} website chunks (PRIORITY 2)")
            # Print the first chunk
            if website_chunks:
                chunk = website_chunks[0]
                logger.info(f"Sample website chunk: {chunk.get('website_name')} - {chunk.get('url')}")
                logger.info(f"Text: {chunk.get('text', '')[:100]}...")
        else:
            logger.info("❌ No chunks found, would use LLM fallback (PRIORITY 3)")

def main():
    """Main function to test search priority."""
    test_search_priority()

if __name__ == "__main__":
    main()
