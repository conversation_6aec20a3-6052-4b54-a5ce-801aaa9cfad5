def load_documents(data_dir: str = './data'):
    """Load and process all supported documents from a directory."""
    global DOCUMENT_CHUNKS
    DOCUMENT_CHUNKS = []
    
    logger.info(f"Loading documents on startup")
    
    # Ensure data directory exists
    if not os.path.exists(data_dir):
        logger.warning(f"Data directory {data_dir} does not exist")
        return DOCUMENT_CHUNKS
    
    # Get all supported document files in the directory
    supported_extensions = ['.pdf', '.docx', '.doc', '.txt']
    document_files = []
    
    for ext in supported_extensions:
        files = [f for f in os.listdir(data_dir) if f.lower().endswith(ext)]
        document_files.extend(files)
    
    if not document_files:
        logger.warning(f"No supported document files found in {data_dir}")
        return DOCUMENT_CHUNKS
    
    logger.info(f"Found {len(document_files)} supported document files in {data_dir}")
    
    # Process each document file
    for filename in document_files:
        file_path = os.path.join(data_dir, filename)
        logger.info(f"Processing {file_path}")
        
        try:
            # Extract document using our extraction utility
            logger.info(f"Extracting document: {file_path}")
            document_chunks = extract_document(file_path)
            
            if not document_chunks:
                logger.warning(f"No chunks extracted from {filename}")
                continue
            
            # Generate embeddings for each chunk
            for chunk in document_chunks:
                # Add source_type metadata
                chunk["source_type"] = "document"
                
                # Generate and add embedding
                chunk_text = chunk.get("text", "")
                embedding = generate_embedding(chunk_text)
                chunk["embedding"] = embedding
            
            # Add to global document chunks
            DOCUMENT_CHUNKS.extend(document_chunks)
            
            # Add to vector database
            vector_db.add_chunks(document_chunks)
            
            logger.info(f"Processed {filename}: extracted {len(document_chunks)} chunks")
            
        except Exception as e:
            logger.error(f"Error processing {filename}: {str(e)}")
    
    logger.info(f"Finished processing. Total chunks created: {len(DOCUMENT_CHUNKS)}")
    return DOCUMENT_CHUNKS

@app.on_event("startup")
async def startup_event():
    """Load documents on server startup."""
    logger.info("Loading documents on startup")
    load_documents()
    
    # Log how many chunks were loaded
    logger.info(f"Loaded {len(DOCUMENT_CHUNKS)} document chunks on startup")

# Root endpoint
@app.get("/")
async def read_root():
    return {"message": "Document Management System API is running"}

# Get all documents endpoint
@app.get("/api/documents")
async def get_documents():
    # This would normally query a database, but for now, we'll generate mock data
    documents = [
        {
            "id": "doc-1746815214001",
            "name": "Indian Railway Safety Guidelines.pdf",
            "uploadedAt": "2025-05-08T10:15:14.001Z",
            "mainCategory": "Safety",
            "category": "Guidelines",
            "subCategory": "Operations",
            "minorCategory": "Personnel",
            "status": "Extracted",
            "fileType": "pdf",
            "qualityScore": 92,
            "uploadedBy": "Admin"
        },
        {
            "id": "doc-1746815214002",
            "name": "Maintenance Schedule 2025.docx",
            "uploadedAt": "2025-05-07T14:22:33.002Z",
            "mainCategory": "Maintenance",
            "category": "Schedules",
            "status": "Extracted",
            "fileType": "docx",
            "qualityScore": 88,
            "uploadedBy": "Admin"
        },
        {
            "id": "doc-1746815214003",
            "name": "FSDS Isolation Identification System.docx",
            "uploadedAt": "2025-05-09T23:56:52.003Z",
            "mainCategory": "Technical",
            "category": "Systems",
            "subCategory": "FSDS",
            "status": "Extracted",
            "fileType": "docx",
            "qualityScore": 95,
            "uploadedBy": "User"
        }
    ]
    
    return documents

# Get all websites endpoint
@app.get("/api/websites")
async def get_websites():
    # Mock data for websites
    websites = [
        {
            "id": "web-1746815314001",
            "name": "Indian Railways Official Portal",
            "url": "https://indianrailways.gov.in",
            "addedAt": "2025-05-05T09:12:14.001Z",
            "category": "Official",
            "status": "Processed",
            "qualityScore": 90,
            "addedBy": "Admin"
        },
        {
            "id": "web-1746815314002",
            "name": "IRCTC Booking Portal",
            "url": "https://irctc.co.in",
            "addedAt": "2025-05-06T11:32:45.002Z",
            "category": "Services",
            "status": "Processed",
            "qualityScore": 85,
            "addedBy": "Admin"
        }
    ]
    
    return websites

# Chunks endpoint
@app.get("/api/chunks")
async def get_chunks():
    # Return document chunks without embeddings for response size efficiency
    chunks_without_embeddings = []
    for chunk in DOCUMENT_CHUNKS:
        chunk_copy = dict(chunk)
        if "embedding" in chunk_copy:
            del chunk_copy["embedding"]
        chunks_without_embeddings.append(chunk_copy)
    
    return chunks_without_embeddings

# Query endpoint for semantic search and LLM answer generation
@app.post("/api/query")
async def query(request: QueryRequest):
    query_text = request.query
    logger.info(f"Received query: {query_text}")
    
    if not query_text or not query_text.strip():
        raise HTTPException(status_code=400, detail="Query cannot be empty")
    
    try:
        # Generate embedding for the query
        query_embedding = generate_embedding(query_text)
        
        # Find similar chunks
        similar_chunks = find_similar_chunks(query_embedding)
        
        if not similar_chunks:
            return {
                "answer": "I couldn't find any relevant information to answer your query. Please try rephrasing your question or ask about a different topic.",
                "sources": []
            }
        
        # Group by source type
        document_chunks, website_chunks = group_chunks_by_source(similar_chunks)
        
        # Generate answer and track sources
        answer, sources = generate_llm_answer(query_text, similar_chunks)
        
        # Convert sources to Source objects
        source_objects = []
        for source in sources:
            source_objects.append(Source(**source))
        
        # Generate document-specific answer if document chunks exist
        document_answer = None
        document_sources = None
        if document_chunks:
            document_answer, doc_sources = generate_llm_answer(
                query_text, document_chunks, 
                "You are answering based on railway document contents only. Be specific and precise."
            )
            document_sources = [Source(**s) for s in doc_sources]
        
        # Generate website-specific answer if website chunks exist
        website_answer = None
        website_sources = None
        if website_chunks:
            website_answer, web_sources = generate_llm_answer(
                query_text, website_chunks,
                "You are answering based on railway website contents only. Be specific and precise."
            )
            website_sources = [Source(**s) for s in web_sources]
        
        # Construct response
        response = QueryResponse(
            answer=answer,
            document_answer=document_answer,
            website_answer=website_answer,
            sources=source_objects,
            document_sources=document_sources,
            website_sources=website_sources
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

# Document upload endpoint
@app.post("/api/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    uploaded_by: Optional[str] = Form(None),
    role: Optional[str] = Form(None)
):
    logger.info(f"Received document upload request: {file.filename}")
    
    # Check if the file has content
    if not file.filename:
        raise HTTPException(status_code=400, detail="File has no filename")
    
    try:
        # Create uploads directory if it doesn't exist
        uploads_dir = os.path.join("data", "uploads")
        os.makedirs(uploads_dir, exist_ok=True)
        
        # Save the file to the uploads directory
        file_path = os.path.join(uploads_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        logger.info(f"File saved to {file_path}")
        
        # Process the document to extract content
        document_chunks = extract_document(file_path)
        
        if not document_chunks:
            raise HTTPException(
                status_code=422, 
                detail=f"Failed to extract content from {file.filename}"
            )
        
        # Generate embeddings for each chunk
        for chunk in document_chunks:
            # Add metadata
            chunk["source_type"] = "document"
            if uploaded_by:
                chunk["uploaded_by"] = uploaded_by
            if role:
                chunk["role"] = role
            
            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding
        
        # Add to global document chunks (legacy in-memory storage)
        global DOCUMENT_CHUNKS
        DOCUMENT_CHUNKS.extend(document_chunks)
        
        # Add to vector database for efficient search
        vector_db.add_chunks(document_chunks)
        
        logger.info(f"Added {len(document_chunks)} document chunks to knowledge base and vector database")
        
        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in document_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)
        
        return {
            "message": f"Successfully processed {file.filename}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding
        }
        
    except Exception as e:
        logger.error(f"Error processing uploaded document {file.filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")
    finally:
        file.file.close()

# Website add endpoint
@app.post("/api/add-website")
async def add_website(request: WebsiteAddRequest):
    url = request.url
    submitted_by = request.submitted_by
    role = request.role
    
    logger.info(f"Received request to add website: {url}")
    
    if not url or not url.strip():
        raise HTTPException(status_code=400, detail="URL cannot be empty")
    
    try:
        # Extract text from website using fallback extraction methods
        website_chunks = extract_website_text(url)
        
        if not website_chunks:
            raise HTTPException(status_code=422, detail=f"Failed to extract content from {url}")
        
        # Generate embeddings for each chunk
        for chunk in website_chunks:
            # Add additional metadata
            if submitted_by:
                chunk["submitted_by"] = submitted_by
            if role:
                chunk["role"] = role
            
            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding
        
        # Add to global document chunks (legacy in-memory storage)
        global DOCUMENT_CHUNKS
        DOCUMENT_CHUNKS.extend(website_chunks)
        
        # Add to vector database for efficient search
        vector_db.add_chunks(website_chunks)
        
        logger.info(f"Added {len(website_chunks)} website chunks to knowledge base and vector database")
        
        # Log vector database stats
        logger.info(f"Vector database stats: {vector_db.get_stats()}")
        
        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in website_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)
        
        return {
            "message": f"Successfully added website {url}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding
        }
        
    except Exception as e:
        logger.error(f"Error adding website {url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding website: {str(e)}")
