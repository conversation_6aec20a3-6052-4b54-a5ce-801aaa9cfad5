{"ast": null, "code": "import React,{useState}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WebsitesTable=_ref=>{let{websites,onView,onRetry,onDelete,onCategoryUpdate}=_ref;const[sortField,setSortField]=useState('extractedAt');const[sortDirection,setSortDirection]=useState('desc');const[searchQuery,setSearchQuery]=useState('');const[domainFilter,setDomainFilter]=useState('');const[statusFilter,setStatusFilter]=useState('');// Handle sorting\nconst handleSort=field=>{if(sortField===field){setSortDirection(sortDirection==='asc'?'desc':'asc');}else{setSortField(field);setSortDirection('asc');}};// Filter websites based on search query and filters\nconst filteredWebsites=websites.filter(site=>{// Search query filter (URLs or domains)\nif(searchQuery&&!site.url.toLowerCase().includes(searchQuery.toLowerCase())&&!site.domain.toLowerCase().includes(searchQuery.toLowerCase())){return false;}// Domain filter\nif(domainFilter&&site.domain!==domainFilter&&site.domainCategory!==domainFilter){return false;}// Status filter\nif(statusFilter&&site.status!==statusFilter){return false;}return true;});// Sort websites\nconst sortedWebsites=[...filteredWebsites].sort((a,b)=>{var _a$sortField,_b$sortField;// Use optional chaining and nullish coalescing to handle undefined\nconst aValue=(_a$sortField=a[sortField])!==null&&_a$sortField!==void 0?_a$sortField:'';const bValue=(_b$sortField=b[sortField])!==null&&_b$sortField!==void 0?_b$sortField:'';if(aValue<bValue){return sortDirection==='asc'?-1:1;}if(aValue>bValue){return sortDirection==='asc'?1:-1;}return 0;});// Format the date for display\nconst formatDate=dateStr=>{if(!dateStr)return'N/A';try{// Check if dateStr is a valid date\nconst date=new Date(dateStr);if(isNaN(date.getTime())){return'Invalid date';}return new Intl.DateTimeFormat('en-IN',{day:'2-digit',month:'short',year:'numeric',hour:'2-digit',minute:'2-digit'}).format(date);}catch(error){console.error('Error formatting date:',error);return'Invalid date';}};// Render status badge\nconst renderStatusBadge=status=>{let bgColor;switch(status){case'Success':bgColor='bg-green-100 text-green-800';break;case'Failed':bgColor='bg-red-100 text-red-800';break;case'Manual Required':bgColor='bg-yellow-100 text-yellow-800';break;default:bgColor='bg-gray-100 text-gray-800';}return/*#__PURE__*/_jsx(\"span\",{className:`${bgColor} px-2 py-1 rounded-full text-xs font-medium`,children:status});};// Get unique domains for filtering\nconst uniqueDomains=Array.from(new Set(websites.flatMap(site=>[site.domain,site.domainCategory||'']).filter(Boolean)));return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-md overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border-b border-gray-200\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold\",children:\"Manage Websites\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row gap-4 mt-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search URLs or domains...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3\",children:/*#__PURE__*/_jsxs(\"select\",{value:domainFilter,onChange:e=>setDomainFilter(e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Domains\"}),uniqueDomains.map(domain=>/*#__PURE__*/_jsx(\"option\",{value:domain,children:domain},domain))]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3\",children:/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Statuses\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Success\",children:\"Success\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Failed\",children:\"Failed\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Manual Required\",children:\"Manual Required\"})]})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('url'),children:[\"URL\",sortField==='url'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('domain'),children:[\"Domain\",sortField==='domain'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('extractedAt'),children:[\"Extracted At\",sortField==='extractedAt'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('status'),children:[\"Status\",sortField==='status'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:sortedWebsites.length>0?sortedWebsites.map(website=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-blue-600 hover:underline\",children:/*#__PURE__*/_jsx(\"a\",{href:website.url,target:\"_blank\",rel:\"noopener noreferrer\",children:website.url.length>50?`${website.url.substring(0,50)}...`:website.url})})}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-900\",children:website.domain}),website.domainCategory&&/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-500\",children:website.domainCategory})]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:formatDate(website.extractedAt)}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:renderStatusBadge(website.status)}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onView(website),className:\"text-blue-600 hover:text-blue-900\",children:\"View\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onRetry(website),className:\"text-yellow-600 hover:text-yellow-900\",children:\"Retry\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(website),className:\"text-red-600 hover:text-red-900\",children:\"Delete\"})]})})]},website.id)):/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:5,className:\"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\",children:\"No websites found\"})})})]})})]});};export default WebsitesTable;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "WebsitesTable", "_ref", "websites", "onView", "onRetry", "onDelete", "onCategoryUpdate", "sortField", "setSortField", "sortDirection", "setSortDirection", "searchQuery", "setSearch<PERSON>uery", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statusFilter", "setStatus<PERSON>ilter", "handleSort", "field", "filteredWebsites", "filter", "site", "url", "toLowerCase", "includes", "domain", "domainCategory", "status", "sortedWebsites", "sort", "a", "b", "_a$sortField", "_b$sortField", "aValue", "bValue", "formatDate", "dateStr", "date", "Date", "isNaN", "getTime", "Intl", "DateTimeFormat", "day", "month", "year", "hour", "minute", "format", "error", "console", "renderStatusBadge", "bgColor", "className", "children", "uniqueDomains", "Array", "from", "Set", "flatMap", "Boolean", "type", "placeholder", "value", "onChange", "e", "target", "map", "scope", "onClick", "length", "website", "href", "rel", "substring", "extractedAt", "id", "colSpan"], "sources": ["C:/IR App/frontend/src/components/websites/WebsitesTable.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Website } from '../../types/websites';\n\ninterface WebsitesTableProps {\n  websites: Website[];\n  onView: (website: Website) => void;\n  onRetry: (website: Website) => void;\n  onDelete: (website: Website) => void;\n  onCategoryUpdate?: (updatedWebsite: Website) => void;\n}\n\nconst WebsitesTable: React.FC<WebsitesTableProps> = ({\n  websites,\n  onView,\n  onRetry,\n  onDelete,\n  onCategoryUpdate,\n}) => {\n  const [sortField, setSortField] = useState<keyof Website>('extractedAt');\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [domainFilter, setDomainFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n\n  // Handle sorting\n  const handleSort = (field: keyof Website) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  // Filter websites based on search query and filters\n  const filteredWebsites = websites.filter((site) => {\n    // Search query filter (URLs or domains)\n    if (\n      searchQuery &&\n      !site.url.toLowerCase().includes(searchQuery.toLowerCase()) &&\n      !site.domain.toLowerCase().includes(searchQuery.toLowerCase())\n    ) {\n      return false;\n    }\n\n    // Domain filter\n    if (\n      domainFilter &&\n      site.domain !== domainFilter &&\n      site.domainCategory !== domainFilter\n    ) {\n      return false;\n    }\n\n    // Status filter\n    if (statusFilter && site.status !== statusFilter) {\n      return false;\n    }\n\n    return true;\n  });\n\n  // Sort websites\n  const sortedWebsites = [...filteredWebsites].sort((a, b) => {\n    // Use optional chaining and nullish coalescing to handle undefined\n    const aValue = a[sortField] ?? '';\n    const bValue = b[sortField] ?? '';\n    \n    if (aValue < bValue) {\n      return sortDirection === 'asc' ? -1 : 1;\n    }\n    if (aValue > bValue) {\n      return sortDirection === 'asc' ? 1 : -1;\n    }\n    return 0;\n  });\n\n  // Format the date for display\n  const formatDate = (dateStr: string | null | undefined) => {\n    if (!dateStr) return 'N/A';\n    \n    try {\n      // Check if dateStr is a valid date\n      const date = new Date(dateStr);\n      if (isNaN(date.getTime())) {\n        return 'Invalid date';\n      }\n      \n      return new Intl.DateTimeFormat('en-IN', {\n        day: '2-digit',\n        month: 'short',\n        year: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit',\n      }).format(date);\n    } catch (error) {\n      console.error('Error formatting date:', error);\n      return 'Invalid date';\n    }\n  };\n\n  // Render status badge\n  const renderStatusBadge = (status: Website['status']) => {\n    let bgColor;\n    switch (status) {\n      case 'Success':\n        bgColor = 'bg-green-100 text-green-800';\n        break;\n      case 'Failed':\n        bgColor = 'bg-red-100 text-red-800';\n        break;\n      case 'Manual Required':\n        bgColor = 'bg-yellow-100 text-yellow-800';\n        break;\n      default:\n        bgColor = 'bg-gray-100 text-gray-800';\n    }\n\n    return (\n      <span className={`${bgColor} px-2 py-1 rounded-full text-xs font-medium`}>\n        {status}\n      </span>\n    );\n  };\n\n  // Get unique domains for filtering\n  const uniqueDomains = Array.from(\n    new Set(\n      websites.flatMap((site) => [\n        site.domain,\n        site.domainCategory || '',\n      ]).filter(Boolean)\n    )\n  );\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n      <div className=\"p-4 border-b border-gray-200\">\n        <h2 className=\"text-lg font-semibold\">Manage Websites</h2>\n        \n        {/* Filters and Search */}\n        <div className=\"flex flex-col md:flex-row gap-4 mt-4\">\n          <div className=\"md:w-1/3\">\n            <input\n              type=\"text\"\n              placeholder=\"Search URLs or domains...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n          \n          <div className=\"md:w-1/3\">\n            <select\n              value={domainFilter}\n              onChange={(e) => setDomainFilter(e.target.value)}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">All Domains</option>\n              {uniqueDomains.map((domain) => (\n                <option key={domain as string} value={domain as string}>\n                  {domain as string}\n                </option>\n              ))}\n            </select>\n          </div>\n          \n          <div className=\"md:w-1/3\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">All Statuses</option>\n              <option value=\"Success\">Success</option>\n              <option value=\"Failed\">Failed</option>\n              <option value=\"Manual Required\">Manual Required</option>\n            </select>\n          </div>\n        </div>\n      </div>\n      \n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('url')}\n              >\n                URL\n                {sortField === 'url' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('domain')}\n              >\n                Domain\n                {sortField === 'domain' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('extractedAt')}\n              >\n                Extracted At\n                {sortField === 'extractedAt' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('status')}\n              >\n                Status\n                {sortField === 'status' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n              >\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {sortedWebsites.length > 0 ? (\n              sortedWebsites.map((website) => (\n                <tr key={website.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-blue-600 hover:underline\">\n                      <a href={website.url} target=\"_blank\" rel=\"noopener noreferrer\">\n                        {website.url.length > 50 ? `${website.url.substring(0, 50)}...` : website.url}\n                      </a>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900\">\n                      {website.domain}\n                    </div>\n                    {website.domainCategory && (\n                      <div className=\"text-xs text-gray-500\">\n                        {website.domainCategory}\n                      </div>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {formatDate(website.extractedAt)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {renderStatusBadge(website.status)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                    <div className=\"flex space-x-3\">\n                      <button\n                        onClick={() => onView(website)}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                      >\n                        View\n                      </button>\n                      <button\n                        onClick={() => onRetry(website)}\n                        className=\"text-yellow-600 hover:text-yellow-900\"\n                      >\n                        Retry\n                      </button>\n                      <button\n                        onClick={() => onDelete(website)}\n                        className=\"text-red-600 hover:text-red-900\"\n                      >\n                        Delete\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))\n            ) : (\n              <tr>\n                <td colSpan={5} className=\"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\">\n                  No websites found\n                </td>\n              </tr>\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n};\n\nexport default WebsitesTable;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWxC,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAM9C,IAN+C,CACnDC,QAAQ,CACRC,MAAM,CACNC,OAAO,CACPC,QAAQ,CACRC,gBACF,CAAC,CAAAL,IAAA,CACC,KAAM,CAACM,SAAS,CAAEC,YAAY,CAAC,CAAGb,QAAQ,CAAgB,aAAa,CAAC,CACxE,KAAM,CAACc,aAAa,CAAEC,gBAAgB,CAAC,CAAGf,QAAQ,CAAiB,MAAM,CAAC,CAC1E,KAAM,CAACgB,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACkB,YAAY,CAAEC,eAAe,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACoB,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAEpD;AACA,KAAM,CAAAsB,UAAU,CAAIC,KAAoB,EAAK,CAC3C,GAAIX,SAAS,GAAKW,KAAK,CAAE,CACvBR,gBAAgB,CAACD,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC5D,CAAC,IAAM,CACLD,YAAY,CAACU,KAAK,CAAC,CACnBR,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAS,gBAAgB,CAAGjB,QAAQ,CAACkB,MAAM,CAAEC,IAAI,EAAK,CACjD;AACA,GACEV,WAAW,EACX,CAACU,IAAI,CAACC,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACb,WAAW,CAACY,WAAW,CAAC,CAAC,CAAC,EAC3D,CAACF,IAAI,CAACI,MAAM,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACb,WAAW,CAACY,WAAW,CAAC,CAAC,CAAC,CAC9D,CACA,MAAO,MAAK,CACd,CAEA;AACA,GACEV,YAAY,EACZQ,IAAI,CAACI,MAAM,GAAKZ,YAAY,EAC5BQ,IAAI,CAACK,cAAc,GAAKb,YAAY,CACpC,CACA,MAAO,MAAK,CACd,CAEA;AACA,GAAIE,YAAY,EAAIM,IAAI,CAACM,MAAM,GAAKZ,YAAY,CAAE,CAChD,MAAO,MAAK,CACd,CAEA,MAAO,KAAI,CACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAAa,cAAc,CAAG,CAAC,GAAGT,gBAAgB,CAAC,CAACU,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,KAAAC,YAAA,CAAAC,YAAA,CAC1D;AACA,KAAM,CAAAC,MAAM,EAAAF,YAAA,CAAGF,CAAC,CAACvB,SAAS,CAAC,UAAAyB,YAAA,UAAAA,YAAA,CAAI,EAAE,CACjC,KAAM,CAAAG,MAAM,EAAAF,YAAA,CAAGF,CAAC,CAACxB,SAAS,CAAC,UAAA0B,YAAA,UAAAA,YAAA,CAAI,EAAE,CAEjC,GAAIC,MAAM,CAAGC,MAAM,CAAE,CACnB,MAAO,CAAA1B,aAAa,GAAK,KAAK,CAAG,CAAC,CAAC,CAAG,CAAC,CACzC,CACA,GAAIyB,MAAM,CAAGC,MAAM,CAAE,CACnB,MAAO,CAAA1B,aAAa,GAAK,KAAK,CAAG,CAAC,CAAG,CAAC,CAAC,CACzC,CACA,MAAO,EAAC,CACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAA2B,UAAU,CAAIC,OAAkC,EAAK,CACzD,GAAI,CAACA,OAAO,CAAE,MAAO,KAAK,CAE1B,GAAI,CACF;AACA,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,OAAO,CAAC,CAC9B,GAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,CAAE,CACzB,MAAO,cAAc,CACvB,CAEA,MAAO,IAAI,CAAAC,IAAI,CAACC,cAAc,CAAC,OAAO,CAAE,CACtCC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,OAAO,CACdC,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CAACC,MAAM,CAACX,IAAI,CAAC,CACjB,CAAE,MAAOY,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,cAAc,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAAE,iBAAiB,CAAIzB,MAAyB,EAAK,CACvD,GAAI,CAAA0B,OAAO,CACX,OAAQ1B,MAAM,EACZ,IAAK,SAAS,CACZ0B,OAAO,CAAG,6BAA6B,CACvC,MACF,IAAK,QAAQ,CACXA,OAAO,CAAG,yBAAyB,CACnC,MACF,IAAK,iBAAiB,CACpBA,OAAO,CAAG,+BAA+B,CACzC,MACF,QACEA,OAAO,CAAG,2BAA2B,CACzC,CAEA,mBACExD,IAAA,SAAMyD,SAAS,CAAE,GAAGD,OAAO,6CAA8C,CAAAE,QAAA,CACtE5B,MAAM,CACH,CAAC,CAEX,CAAC,CAED;AACA,KAAM,CAAA6B,aAAa,CAAGC,KAAK,CAACC,IAAI,CAC9B,GAAI,CAAAC,GAAG,CACLzD,QAAQ,CAAC0D,OAAO,CAAEvC,IAAI,EAAK,CACzBA,IAAI,CAACI,MAAM,CACXJ,IAAI,CAACK,cAAc,EAAI,EAAE,CAC1B,CAAC,CAACN,MAAM,CAACyC,OAAO,CACnB,CACF,CAAC,CAED,mBACE9D,KAAA,QAAKuD,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DxD,KAAA,QAAKuD,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3C1D,IAAA,OAAIyD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAG1DxD,KAAA,QAAKuD,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnD1D,IAAA,QAAKyD,SAAS,CAAC,UAAU,CAAAC,QAAA,cACvB1D,IAAA,UACEiE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCC,KAAK,CAAErD,WAAY,CACnBsD,QAAQ,CAAGC,CAAC,EAAKtD,cAAc,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChDV,SAAS,CAAC,kGAAkG,CAC7G,CAAC,CACC,CAAC,cAENzD,IAAA,QAAKyD,SAAS,CAAC,UAAU,CAAAC,QAAA,cACvBxD,KAAA,WACEiE,KAAK,CAAEnD,YAAa,CACpBoD,QAAQ,CAAGC,CAAC,EAAKpD,eAAe,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDV,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAE5G1D,IAAA,WAAQmE,KAAK,CAAC,EAAE,CAAAT,QAAA,CAAC,aAAW,CAAQ,CAAC,CACpCC,aAAa,CAACY,GAAG,CAAE3C,MAAM,eACxB5B,IAAA,WAA+BmE,KAAK,CAAEvC,MAAiB,CAAA8B,QAAA,CACpD9B,MAAM,EADIA,MAEL,CACT,CAAC,EACI,CAAC,CACN,CAAC,cAEN5B,IAAA,QAAKyD,SAAS,CAAC,UAAU,CAAAC,QAAA,cACvBxD,KAAA,WACEiE,KAAK,CAAEjD,YAAa,CACpBkD,QAAQ,CAAGC,CAAC,EAAKlD,eAAe,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDV,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAE5G1D,IAAA,WAAQmE,KAAK,CAAC,EAAE,CAAAT,QAAA,CAAC,cAAY,CAAQ,CAAC,cACtC1D,IAAA,WAAQmE,KAAK,CAAC,SAAS,CAAAT,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxC1D,IAAA,WAAQmE,KAAK,CAAC,QAAQ,CAAAT,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC1D,IAAA,WAAQmE,KAAK,CAAC,iBAAiB,CAAAT,QAAA,CAAC,iBAAe,CAAQ,CAAC,EAClD,CAAC,CACN,CAAC,EACH,CAAC,EACH,CAAC,cAGN1D,IAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BxD,KAAA,UAAOuD,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD1D,IAAA,UAAOyD,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3BxD,KAAA,OAAAwD,QAAA,eACExD,KAAA,OACEsE,KAAK,CAAC,KAAK,CACXf,SAAS,CAAC,+FAA+F,CACzGgB,OAAO,CAAEA,CAAA,GAAMrD,UAAU,CAAC,KAAK,CAAE,CAAAsC,QAAA,EAClC,KAEC,CAAChD,SAAS,GAAK,KAAK,eAClBV,IAAA,SAAMyD,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnB9C,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLV,KAAA,OACEsE,KAAK,CAAC,KAAK,CACXf,SAAS,CAAC,+FAA+F,CACzGgB,OAAO,CAAEA,CAAA,GAAMrD,UAAU,CAAC,QAAQ,CAAE,CAAAsC,QAAA,EACrC,QAEC,CAAChD,SAAS,GAAK,QAAQ,eACrBV,IAAA,SAAMyD,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnB9C,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLV,KAAA,OACEsE,KAAK,CAAC,KAAK,CACXf,SAAS,CAAC,+FAA+F,CACzGgB,OAAO,CAAEA,CAAA,GAAMrD,UAAU,CAAC,aAAa,CAAE,CAAAsC,QAAA,EAC1C,cAEC,CAAChD,SAAS,GAAK,aAAa,eAC1BV,IAAA,SAAMyD,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnB9C,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLV,KAAA,OACEsE,KAAK,CAAC,KAAK,CACXf,SAAS,CAAC,+FAA+F,CACzGgB,OAAO,CAAEA,CAAA,GAAMrD,UAAU,CAAC,QAAQ,CAAE,CAAAsC,QAAA,EACrC,QAEC,CAAChD,SAAS,GAAK,QAAQ,eACrBV,IAAA,SAAMyD,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnB9C,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLZ,IAAA,OACEwE,KAAK,CAAC,KAAK,CACXf,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC3F,SAED,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR1D,IAAA,UAAOyD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjD3B,cAAc,CAAC2C,MAAM,CAAG,CAAC,CACxB3C,cAAc,CAACwC,GAAG,CAAEI,OAAO,eACzBzE,KAAA,OAAqBuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/C1D,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC1D,IAAA,QAAKyD,SAAS,CAAC,mDAAmD,CAAAC,QAAA,cAChE1D,IAAA,MAAG4E,IAAI,CAAED,OAAO,CAAClD,GAAI,CAAC6C,MAAM,CAAC,QAAQ,CAACO,GAAG,CAAC,qBAAqB,CAAAnB,QAAA,CAC5DiB,OAAO,CAAClD,GAAG,CAACiD,MAAM,CAAG,EAAE,CAAG,GAAGC,OAAO,CAAClD,GAAG,CAACqD,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,KAAK,CAAGH,OAAO,CAAClD,GAAG,CAC5E,CAAC,CACD,CAAC,CACJ,CAAC,cACLvB,KAAA,OAAIuD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACzC1D,IAAA,QAAKyD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnCiB,OAAO,CAAC/C,MAAM,CACZ,CAAC,CACL+C,OAAO,CAAC9C,cAAc,eACrB7B,IAAA,QAAKyD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnCiB,OAAO,CAAC9C,cAAc,CACpB,CACN,EACC,CAAC,cACL7B,IAAA,OAAIyD,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DnB,UAAU,CAACoC,OAAO,CAACI,WAAW,CAAC,CAC9B,CAAC,cACL/E,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CACxCH,iBAAiB,CAACoB,OAAO,CAAC7C,MAAM,CAAC,CAChC,CAAC,cACL9B,IAAA,OAAIyD,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACxExD,KAAA,QAAKuD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B1D,IAAA,WACEyE,OAAO,CAAEA,CAAA,GAAMnE,MAAM,CAACqE,OAAO,CAAE,CAC/BlB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9C,MAED,CAAQ,CAAC,cACT1D,IAAA,WACEyE,OAAO,CAAEA,CAAA,GAAMlE,OAAO,CAACoE,OAAO,CAAE,CAChClB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAClD,OAED,CAAQ,CAAC,cACT1D,IAAA,WACEyE,OAAO,CAAEA,CAAA,GAAMjE,QAAQ,CAACmE,OAAO,CAAE,CACjClB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC5C,QAED,CAAQ,CAAC,EACN,CAAC,CACJ,CAAC,GA7CEiB,OAAO,CAACK,EA8Cb,CACL,CAAC,cAEFhF,IAAA,OAAA0D,QAAA,cACE1D,IAAA,OAAIiF,OAAO,CAAE,CAAE,CAACxB,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAAC,mBAE1F,CAAI,CAAC,CACH,CACL,CACI,CAAC,EACH,CAAC,CACL,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}