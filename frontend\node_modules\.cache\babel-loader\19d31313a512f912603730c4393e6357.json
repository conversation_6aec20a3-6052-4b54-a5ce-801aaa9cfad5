{"ast": null, "code": "import React,{useState}from'react';import DocumentCategoryEditor from'./DocumentCategoryEditor';import BulkCategoryEditor from'./BulkCategoryEditor';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DocumentsTable=_ref=>{let{documents,onView,onEdit,onDelete,onReprocess,onCategoryUpdate}=_ref;const[sortField,setSortField]=useState('uploadedAt');const[sortDirection,setSortDirection]=useState('desc');const[searchQuery,setSearchQuery]=useState('');const[categoryFilter,setCategoryFilter]=useState('');const[statusFilter,setStatusFilter]=useState('');const[mainCategoryFilter,setMainCategoryFilter]=useState('');const[dateFilter,setDateFilter]=useState('');const[fileTypeFilter,setFileTypeFilter]=useState('');const[selectedDocuments,setSelectedDocuments]=useState([]);const[showFilters,setShowFilters]=useState(false);const[dateRangeFilter,setDateRangeFilter]=useState({startDate:'',endDate:''});// Category editor state\nconst[categoryEditorOpen,setCategoryEditorOpen]=useState(false);const[documentToEdit,setDocumentToEdit]=useState(null);// Bulk category editor state\nconst[bulkCategoryEditorOpen,setBulkCategoryEditorOpen]=useState(false);// Handle sorting\nconst handleSort=field=>{if(sortField===field){setSortDirection(sortDirection==='asc'?'desc':'asc');}else{setSortField(field);setSortDirection('asc');}};// Toggle selection of all documents\nconst toggleSelectAll=docs=>{if(selectedDocuments.length===docs.length){setSelectedDocuments([]);}else{setSelectedDocuments(docs.map(doc=>doc.id));}};// Toggle selection of a single document\nconst toggleSelectDocument=id=>{if(selectedDocuments.includes(id)){setSelectedDocuments(selectedDocuments.filter(docId=>docId!==id));}else{setSelectedDocuments([...selectedDocuments,id]);}};// Handle batch operations\nconst handleBatchOperation=operation=>{if(selectedDocuments.length===0)return;const selectedDocs=documents.filter(doc=>selectedDocuments.includes(doc.id));if(operation==='delete'){if(window.confirm(`Are you sure you want to delete ${selectedDocuments.length} selected document(s)?`)){selectedDocs.forEach(doc=>onDelete(doc));setSelectedDocuments([]);}}else if(operation==='reprocess'){selectedDocs.forEach(doc=>onReprocess(doc));setSelectedDocuments([]);}else if(operation==='bulk-category'){setBulkCategoryEditorOpen(true);}};// Reset all filters\nconst resetFilters=()=>{setSearchQuery('');setMainCategoryFilter('');setCategoryFilter('');setStatusFilter('');setFileTypeFilter('');setDateFilter('');setDateRangeFilter({startDate:'',endDate:''});};// Handle date range filter changes\nconst handleDateRangeChange=(field,value)=>{setDateRangeFilter(prev=>({...prev,[field]:value}));};// Handle category editing\nconst handleEditCategories=document=>{setDocumentToEdit(document);setCategoryEditorOpen(true);};const handleCategoryEditorClose=()=>{setCategoryEditorOpen(false);setDocumentToEdit(null);};const handleCategoryUpdate=updatedDocument=>{if(onCategoryUpdate){onCategoryUpdate(updatedDocument);}};// Handle bulk category update\nconst handleBulkCategoryUpdate=updatedDocuments=>{if(onCategoryUpdate){updatedDocuments.forEach(doc=>onCategoryUpdate(doc));}setSelectedDocuments([]);setBulkCategoryEditorOpen(false);};const handleBulkCategoryEditorClose=()=>{setBulkCategoryEditorOpen(false);};// Filter documents based on all filters\nconst filteredDocuments=documents.filter(doc=>{// Search query filter (check name and other fields)\nif(searchQuery){var _doc$uploadedBy,_doc$category,_doc$mainCategory;const query=searchQuery.toLowerCase();const matchesQuery=doc.name.toLowerCase().includes(query)||((_doc$uploadedBy=doc.uploadedBy)===null||_doc$uploadedBy===void 0?void 0:_doc$uploadedBy.toLowerCase().includes(query))||((_doc$category=doc.category)===null||_doc$category===void 0?void 0:_doc$category.toLowerCase().includes(query))||((_doc$mainCategory=doc.mainCategory)===null||_doc$mainCategory===void 0?void 0:_doc$mainCategory.toLowerCase().includes(query));if(!matchesQuery)return false;}// Main category filter\nif(mainCategoryFilter&&doc.mainCategory!==mainCategoryFilter){return false;}// Category filter\nif(categoryFilter&&!(doc.mainCategory===categoryFilter||doc.category===categoryFilter||doc.subCategory===categoryFilter||doc.minorCategory===categoryFilter)){return false;}// Status filter\nif(statusFilter&&doc.status!==statusFilter){return false;}// File type filter\nif(fileTypeFilter&&doc.fileType!==fileTypeFilter){return false;}// Date filter (last day, week, month)\nif(dateFilter){const docDate=new Date(doc.uploadedAt);const now=new Date();if(dateFilter==='day'){// Last 24 hours\nconst yesterday=new Date(now.getTime()-24*60*60*1000);if(docDate<yesterday)return false;}else if(dateFilter==='week'){// Last 7 days\nconst lastWeek=new Date(now.getTime()-7*24*60*60*1000);if(docDate<lastWeek)return false;}else if(dateFilter==='month'){// Last 30 days\nconst lastMonth=new Date(now.getTime()-30*24*60*60*1000);if(docDate<lastMonth)return false;}}// Date range filter\nif(dateRangeFilter.startDate||dateRangeFilter.endDate){const docDate=new Date(doc.uploadedAt);if(dateRangeFilter.startDate){const startDate=new Date(dateRangeFilter.startDate);// Set to beginning of day\nstartDate.setHours(0,0,0,0);if(docDate<startDate)return false;}if(dateRangeFilter.endDate){const endDate=new Date(dateRangeFilter.endDate);// Set to end of day\nendDate.setHours(23,59,59,999);if(docDate>endDate)return false;}}return true;});// Sort documents\nconst sortedDocuments=[...filteredDocuments].sort((a,b)=>{var _a$sortField,_b$sortField;// Use optional chaining and nullish coalescing to handle undefined\nconst aValue=(_a$sortField=a[sortField])!==null&&_a$sortField!==void 0?_a$sortField:'';const bValue=(_b$sortField=b[sortField])!==null&&_b$sortField!==void 0?_b$sortField:'';if(aValue<bValue){return sortDirection==='asc'?-1:1;}if(aValue>bValue){return sortDirection==='asc'?1:-1;}return 0;});// Format the date for display\nconst formatDate=dateStr=>{const date=new Date(dateStr);return new Intl.DateTimeFormat('en-IN',{day:'2-digit',month:'short',year:'numeric',hour:'2-digit',minute:'2-digit'}).format(date);};// Render status badge\nconst renderStatusBadge=status=>{let bgColor;switch(status){case'Extracted':bgColor='bg-green-100 text-green-800';break;case'Pending':bgColor='bg-yellow-100 text-yellow-800';break;case'Manual Review':bgColor='bg-red-100 text-red-800';break;default:bgColor='bg-gray-100 text-gray-800';}return/*#__PURE__*/_jsx(\"span\",{className:`${bgColor} px-2 py-1 rounded-full text-xs font-medium`,children:status});};// Get unique categories and other filter options\nconst uniqueMainCategories=Array.from(new Set(documents.map(doc=>doc.mainCategory).filter(Boolean)));const uniqueCategories=Array.from(new Set(documents.flatMap(doc=>[doc.category,doc.subCategory,doc.minorCategory]).filter(Boolean)));const uniqueFileTypes=Array.from(new Set(documents.map(doc=>doc.fileType).filter(Boolean)));return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-md overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border-b border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-4\",children:[/*#__PURE__*/_jsx(\"h2\",{\"aria-label\":\"Filter controls\",className:\"text-lg font-semibold\",children:\"Manage Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowFilters(!showFilters),className:\"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md flex items-center text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{children:showFilters?'Hide Filters':'Show Filters'}),/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",className:\"h-4 w-4 ml-1\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"})})]}),selectedDocuments.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleBatchOperation('bulk-category'),className:\"px-3 py-1 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-md text-sm\",children:[\"Update Categories (\",selectedDocuments.length,\")\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleBatchOperation('reprocess'),className:\"px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 rounded-md text-sm\",children:[\"Reprocess (\",selectedDocuments.length,\")\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleBatchOperation('delete'),className:\"px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm\",children:[\"Delete (\",selectedDocuments.length,\")\"]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row gap-4 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 lg:w-2/3\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search documents by name, category, or uploader...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 lg:w-1/3\",children:/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Statuses\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Extracted\",children:\"Extracted\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Processing\",children:\"Processing\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Manual Review\",children:\"Manual Review\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Failed\",children:\"Failed\"})]})})]}),showFilters&&/*#__PURE__*/_jsx(\"div\",{className:\"p-4 bg-gray-100 rounded-lg mt-4 mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-gray-700 mb-1\",children:\"Main Category\"}),/*#__PURE__*/_jsxs(\"select\",{value:mainCategoryFilter,onChange:e=>setMainCategoryFilter(e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Main Categories\"}),uniqueMainCategories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category,children:category},category))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-gray-700 mb-1\",children:\"Category\"}),/*#__PURE__*/_jsxs(\"select\",{value:categoryFilter,onChange:e=>setCategoryFilter(e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Categories\"}),uniqueCategories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category,children:category},category))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-gray-700 mb-1\",children:\"File Type\"}),/*#__PURE__*/_jsxs(\"select\",{value:fileTypeFilter,onChange:e=>setFileTypeFilter(e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All File Types\"}),uniqueFileTypes.map(fileType=>/*#__PURE__*/_jsx(\"option\",{value:fileType,children:fileType.toUpperCase()},fileType))]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-gray-700 mb-1\",children:\"Date Added\"}),/*#__PURE__*/_jsxs(\"select\",{value:dateFilter,onChange:e=>setDateFilter(e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"day\",children:\"Last 24 Hours\"}),/*#__PURE__*/_jsx(\"option\",{value:\"week\",children:\"Last 7 Days\"}),/*#__PURE__*/_jsx(\"option\",{value:\"month\",children:\"Last 30 Days\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-gray-700 mb-1\",children:\"Date Range\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2\",children:/*#__PURE__*/_jsx(\"input\",{type:\"date\",id:\"start-date\",value:dateRangeFilter.startDate,onChange:e=>handleDateRangeChange('startDate',e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",placeholder:\"Start date\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2\",children:/*#__PURE__*/_jsx(\"input\",{type:\"date\",id:\"end-date\",value:dateRangeFilter.endDate,onChange:e=>handleDateRangeChange('endDate',e.target.value),className:\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",placeholder:\"End date\"})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3 flex items-end\",children:/*#__PURE__*/_jsx(\"button\",{onClick:resetFilters,className:\"px-3 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md text-sm w-full\",children:\"Reset Filters\"})})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500 mb-2\",children:[\"Showing \",filteredDocuments.length,\" of \",documents.length,\" documents\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-3 py-3 text-center\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500\",checked:selectedDocuments.length>0&&selectedDocuments.length===filteredDocuments.length,onChange:()=>toggleSelectAll(filteredDocuments)})}),/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('name'),children:[\"Name\",sortField==='name'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('uploadedAt'),children:[\"Date Uploaded\",sortField==='uploadedAt'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('mainCategory'),children:[\"Category\",sortField==='mainCategory'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('uploadedBy'),children:[\"Uploaded By\",sortField==='uploadedBy'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('qualityScore'),children:[\"Quality\",sortField==='qualityScore'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",onClick:()=>handleSort('status'),children:[\"Status\",sortField==='status'&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:sortedDocuments.length>0?sortedDocuments.map(document=>/*#__PURE__*/_jsxs(\"tr\",{className:selectedDocuments.includes(document.id)?'bg-blue-50':undefined,children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-4 whitespace-nowrap text-center\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500\",checked:selectedDocuments.includes(document.id),onChange:()=>toggleSelectDocument(document.id)})}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:document.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:document.fileType.toUpperCase()})]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:formatDate(document.uploadedAt)}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-900\",children:[document.mainCategory||'N/A',document.category&&/*#__PURE__*/_jsxs(\"span\",{children:[\" / \",document.category]})]}),document.subCategory&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-500\",children:[document.subCategory,document.minorCategory&&/*#__PURE__*/_jsxs(\"span\",{children:[\" / \",document.minorCategory]})]})]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:document.uploadedBy||'Unknown'}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:document.qualityScore!==undefined?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-24 bg-gray-200 rounded-full h-2.5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-600 h-2.5 rounded-full\",style:{width:`${document.qualityScore}%`}}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-300 h-2.5 rounded-r-full\",style:{width:`${100-document.qualityScore}%`,float:'right'}})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-700 ml-2\",children:[document.qualityScore,\"%\"]})]}):/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-500\",children:\"N/A\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:renderStatusBadge(document.status)}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2 justify-end\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onView(document),className:\"text-blue-600 hover:text-blue-900\",title:\"View document details\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",className:\"h-5 w-5\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:[/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"}),/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"})]})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onEdit(document),className:\"text-indigo-600 hover:text-indigo-900\",title:\"Edit document\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",className:\"h-5 w-5\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"})})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditCategories(document),className:\"text-purple-600 hover:text-purple-900\",title:\"Edit categories\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",className:\"h-5 w-5\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"})})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onReprocess(document),className:\"text-yellow-600 hover:text-yellow-900\",title:\"Reprocess document\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",className:\"h-5 w-5\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"})})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(document),className:\"text-red-600 hover:text-red-900\",title:\"Delete document\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",className:\"h-5 w-5\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"})})})]})})]},document.id)):/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:8,className:\"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\",children:\"No documents found\"})})})]})}),documentToEdit&&/*#__PURE__*/_jsx(DocumentCategoryEditor,{document:documentToEdit,isOpen:categoryEditorOpen,onClose:handleCategoryEditorClose,onSave:handleCategoryUpdate}),/*#__PURE__*/_jsx(BulkCategoryEditor,{documents:documents.filter(doc=>selectedDocuments.includes(doc.id)),isOpen:bulkCategoryEditorOpen,onClose:handleBulkCategoryEditorClose,onSave:handleBulkCategoryUpdate})]});};export default DocumentsTable;", "map": {"version": 3, "names": ["React", "useState", "DocumentCategoryEditor", "BulkCategoryEditor", "jsx", "_jsx", "jsxs", "_jsxs", "DocumentsTable", "_ref", "documents", "onView", "onEdit", "onDelete", "onReprocess", "onCategoryUpdate", "sortField", "setSortField", "sortDirection", "setSortDirection", "searchQuery", "setSearch<PERSON>uery", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "statusFilter", "setStatus<PERSON>ilter", "mainCategoryFilter", "setMainCategory<PERSON><PERSON><PERSON>", "dateFilter", "setDateFilter", "fileTypeFilter", "setFileTypeFilter", "selectedDocuments", "setSelectedDocuments", "showFilters", "setShowFilters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setDateRangeFilter", "startDate", "endDate", "categoryEditorOpen", "setCategoryEditorOpen", "documentToEdit", "setDocumentToEdit", "bulkCategoryEditorOpen", "setBulkCategoryEditorOpen", "handleSort", "field", "toggleSelectAll", "docs", "length", "map", "doc", "id", "toggleSelectDocument", "includes", "filter", "docId", "handleBatchOperation", "operation", "selectedDocs", "window", "confirm", "for<PERSON>ach", "resetFilters", "handleDateRangeChange", "value", "prev", "handleEditCategories", "document", "handleCategoryEditorClose", "handleCategoryUpdate", "updatedDocument", "handleBulkCategoryUpdate", "updatedDocuments", "handleBulkCategoryEditorClose", "filteredDocuments", "_doc$uploadedBy", "_doc$category", "_doc$mainCategory", "query", "toLowerCase", "matchesQuery", "name", "uploadedBy", "category", "mainCategory", "subCategory", "minorCategory", "status", "fileType", "docDate", "Date", "uploadedAt", "now", "yesterday", "getTime", "lastWeek", "lastM<PERSON>h", "setHours", "sortedDocuments", "sort", "a", "b", "_a$sortField", "_b$sortField", "aValue", "bValue", "formatDate", "dateStr", "date", "Intl", "DateTimeFormat", "day", "month", "year", "hour", "minute", "format", "renderStatusBadge", "bgColor", "className", "children", "uniqueMainCategories", "Array", "from", "Set", "Boolean", "uniqueCategories", "flatMap", "uniqueFileTypes", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "type", "placeholder", "onChange", "e", "target", "toUpperCase", "scope", "checked", "undefined", "qualityScore", "style", "width", "float", "title", "colSpan", "isOpen", "onClose", "onSave"], "sources": ["C:/IR App/frontend/src/components/documents/DocumentsTable.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Document } from '../../types/documents';\nimport DocumentCategoryEditor from './DocumentCategoryEditor';\nimport BulkCategoryEditor from './BulkCategoryEditor';\n\ninterface DocumentsTableProps {\n  documents: Document[];\n  onView: (document: Document) => void;\n  onEdit: (document: Document) => void;\n  onDelete: (document: Document) => void;\n  onReprocess: (document: Document) => void;\n  onCategoryUpdate?: (updatedDocument: Document) => void;\n}\n\nconst DocumentsTable: React.FC<DocumentsTableProps> = ({\n  documents,\n  onView,\n  onEdit,\n  onDelete,\n  onReprocess,\n  onCategoryUpdate,\n}) => {\n  const [sortField, setSortField] = useState<keyof Document>('uploadedAt');\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [mainCategoryFilter, setMainCategoryFilter] = useState('');\n  const [dateFilter, setDateFilter] = useState('');\n  const [fileTypeFilter, setFileTypeFilter] = useState('');\n  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);\n  const [showFilters, setShowFilters] = useState(false);\n  const [dateRangeFilter, setDateRangeFilter] = useState<{startDate: string; endDate: string}>({startDate: '', endDate: ''});\n\n  // Category editor state\n  const [categoryEditorOpen, setCategoryEditorOpen] = useState(false);\n  const [documentToEdit, setDocumentToEdit] = useState<Document | null>(null);\n\n  // Bulk category editor state\n  const [bulkCategoryEditorOpen, setBulkCategoryEditorOpen] = useState(false);\n\n  // Handle sorting\n  const handleSort = (field: keyof Document) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  // Toggle selection of all documents\n  const toggleSelectAll = (docs: Document[]) => {\n    if (selectedDocuments.length === docs.length) {\n      setSelectedDocuments([]);\n    } else {\n      setSelectedDocuments(docs.map(doc => doc.id));\n    }\n  };\n\n  // Toggle selection of a single document\n  const toggleSelectDocument = (id: string) => {\n    if (selectedDocuments.includes(id)) {\n      setSelectedDocuments(selectedDocuments.filter(docId => docId !== id));\n    } else {\n      setSelectedDocuments([...selectedDocuments, id]);\n    }\n  };\n\n  // Handle batch operations\n  const handleBatchOperation = (operation: 'delete' | 'reprocess' | 'bulk-category') => {\n    if (selectedDocuments.length === 0) return;\n\n    const selectedDocs = documents.filter(doc => selectedDocuments.includes(doc.id));\n\n    if (operation === 'delete') {\n      if (window.confirm(`Are you sure you want to delete ${selectedDocuments.length} selected document(s)?`)) {\n        selectedDocs.forEach(doc => onDelete(doc));\n        setSelectedDocuments([]);\n      }\n    } else if (operation === 'reprocess') {\n      selectedDocs.forEach(doc => onReprocess(doc));\n      setSelectedDocuments([]);\n    } else if (operation === 'bulk-category') {\n      setBulkCategoryEditorOpen(true);\n    }\n  };\n\n  // Reset all filters\n  const resetFilters = () => {\n    setSearchQuery('');\n    setMainCategoryFilter('');\n    setCategoryFilter('');\n    setStatusFilter('');\n    setFileTypeFilter('');\n    setDateFilter('');\n    setDateRangeFilter({ startDate: '', endDate: '' });\n  };\n\n  // Handle date range filter changes\n  const handleDateRangeChange = (field: 'startDate' | 'endDate', value: string) => {\n    setDateRangeFilter(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Handle category editing\n  const handleEditCategories = (document: Document) => {\n    setDocumentToEdit(document);\n    setCategoryEditorOpen(true);\n  };\n\n  const handleCategoryEditorClose = () => {\n    setCategoryEditorOpen(false);\n    setDocumentToEdit(null);\n  };\n\n  const handleCategoryUpdate = (updatedDocument: Document) => {\n    if (onCategoryUpdate) {\n      onCategoryUpdate(updatedDocument);\n    }\n  };\n\n  // Handle bulk category update\n  const handleBulkCategoryUpdate = (updatedDocuments: Document[]) => {\n    if (onCategoryUpdate) {\n      updatedDocuments.forEach(doc => onCategoryUpdate(doc));\n    }\n    setSelectedDocuments([]);\n    setBulkCategoryEditorOpen(false);\n  };\n\n  const handleBulkCategoryEditorClose = () => {\n    setBulkCategoryEditorOpen(false);\n  };\n\n  // Filter documents based on all filters\n  const filteredDocuments = documents.filter((doc) => {\n    // Search query filter (check name and other fields)\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      const matchesQuery =\n        doc.name.toLowerCase().includes(query) ||\n        doc.uploadedBy?.toLowerCase().includes(query) ||\n        doc.category?.toLowerCase().includes(query) ||\n        doc.mainCategory?.toLowerCase().includes(query);\n\n      if (!matchesQuery) return false;\n    }\n\n    // Main category filter\n    if (mainCategoryFilter && doc.mainCategory !== mainCategoryFilter) {\n      return false;\n    }\n\n    // Category filter\n    if (\n      categoryFilter &&\n      !(\n        doc.mainCategory === categoryFilter ||\n        doc.category === categoryFilter ||\n        doc.subCategory === categoryFilter ||\n        doc.minorCategory === categoryFilter\n      )\n    ) {\n      return false;\n    }\n\n    // Status filter\n    if (statusFilter && doc.status !== statusFilter) {\n      return false;\n    }\n\n    // File type filter\n    if (fileTypeFilter && doc.fileType !== fileTypeFilter) {\n      return false;\n    }\n\n    // Date filter (last day, week, month)\n    if (dateFilter) {\n      const docDate = new Date(doc.uploadedAt);\n      const now = new Date();\n\n      if (dateFilter === 'day') {\n        // Last 24 hours\n        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n        if (docDate < yesterday) return false;\n      } else if (dateFilter === 'week') {\n        // Last 7 days\n        const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        if (docDate < lastWeek) return false;\n      } else if (dateFilter === 'month') {\n        // Last 30 days\n        const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        if (docDate < lastMonth) return false;\n      }\n    }\n\n    // Date range filter\n    if (dateRangeFilter.startDate || dateRangeFilter.endDate) {\n      const docDate = new Date(doc.uploadedAt);\n\n      if (dateRangeFilter.startDate) {\n        const startDate = new Date(dateRangeFilter.startDate);\n        // Set to beginning of day\n        startDate.setHours(0, 0, 0, 0);\n        if (docDate < startDate) return false;\n      }\n\n      if (dateRangeFilter.endDate) {\n        const endDate = new Date(dateRangeFilter.endDate);\n        // Set to end of day\n        endDate.setHours(23, 59, 59, 999);\n        if (docDate > endDate) return false;\n      }\n    }\n\n    return true;\n  });\n\n  // Sort documents\n  const sortedDocuments = [...filteredDocuments].sort((a, b) => {\n    // Use optional chaining and nullish coalescing to handle undefined\n    const aValue = a[sortField] ?? '';\n    const bValue = b[sortField] ?? '';\n\n    if (aValue < bValue) {\n      return sortDirection === 'asc' ? -1 : 1;\n    }\n    if (aValue > bValue) {\n      return sortDirection === 'asc' ? 1 : -1;\n    }\n    return 0;\n  });\n\n  // Format the date for display\n  const formatDate = (dateStr: string) => {\n    const date = new Date(dateStr);\n    return new Intl.DateTimeFormat('en-IN', {\n      day: '2-digit',\n      month: 'short',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    }).format(date);\n  };\n\n  // Render status badge\n  const renderStatusBadge = (status: Document['status']) => {\n    let bgColor;\n    switch (status) {\n      case 'Extracted':\n        bgColor = 'bg-green-100 text-green-800';\n        break;\n      case 'Pending':\n        bgColor = 'bg-yellow-100 text-yellow-800';\n        break;\n      case 'Manual Review':\n        bgColor = 'bg-red-100 text-red-800';\n        break;\n      default:\n        bgColor = 'bg-gray-100 text-gray-800';\n    }\n\n    return (\n      <span className={`${bgColor} px-2 py-1 rounded-full text-xs font-medium`}>\n        {status}\n      </span>\n    );\n  };\n\n  // Get unique categories and other filter options\n  const uniqueMainCategories = Array.from(new Set(documents.map(doc => doc.mainCategory).filter(Boolean)));\n  const uniqueCategories = Array.from(\n    new Set(\n      documents.flatMap((doc) => [\n        doc.category,\n        doc.subCategory,\n        doc.minorCategory,\n      ]).filter(Boolean)\n    )\n  );\n  const uniqueFileTypes = Array.from(new Set(documents.map(doc => doc.fileType).filter(Boolean)));\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 aria-label=\"Filter controls\" className=\"text-lg font-semibold\">Manage Documents</h2>\n\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md flex items-center text-sm\"\n            >\n              <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 ml-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\" />\n              </svg>\n            </button>\n\n            {selectedDocuments.length > 0 && (\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => handleBatchOperation('bulk-category')}\n                  className=\"px-3 py-1 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-md text-sm\"\n                >\n                  Update Categories ({selectedDocuments.length})\n                </button>\n                <button\n                  onClick={() => handleBatchOperation('reprocess')}\n                  className=\"px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 rounded-md text-sm\"\n                >\n                  Reprocess ({selectedDocuments.length})\n                </button>\n                <button\n                  onClick={() => handleBatchOperation('delete')}\n                  className=\"px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm\"\n                >\n                  Delete ({selectedDocuments.length})\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Basic Search */}\n        <div className=\"flex flex-col md:flex-row gap-4 mb-4\">\n          <div className=\"md:w-1/2 lg:w-2/3\">\n            <input\n              type=\"text\"\n              placeholder=\"Search documents by name, category, or uploader...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          <div className=\"md:w-1/2 lg:w-1/3\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">All Statuses</option>\n              <option value=\"Extracted\">Extracted</option>\n              <option value=\"Pending\">Pending</option>\n              <option value=\"Processing\">Processing</option>\n              <option value=\"Manual Review\">Manual Review</option>\n              <option value=\"Failed\">Failed</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Advanced Filters */}\n        {showFilters && (\n          <div className=\"p-4 bg-gray-100 rounded-lg mt-4 mb-6\">\n            <div className=\"flex flex-col space-y-4\">\n              <div className=\"flex flex-col md:flex-row gap-4\">\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">Main Category</label>\n                  <select\n                    value={mainCategoryFilter}\n                    onChange={(e) => setMainCategoryFilter(e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">All Main Categories</option>\n                    {uniqueMainCategories.map((category) => (\n                      <option key={category} value={category}>\n                        {category}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">Category</label>\n                  <select\n                    value={categoryFilter}\n                    onChange={(e) => setCategoryFilter(e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">All Categories</option>\n                    {uniqueCategories.map((category) => (\n                      <option key={category} value={category}>\n                        {category}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">File Type</label>\n                  <select\n                    value={fileTypeFilter}\n                    onChange={(e) => setFileTypeFilter(e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">All File Types</option>\n                    {uniqueFileTypes.map((fileType) => (\n                      <option key={fileType} value={fileType}>\n                        {fileType.toUpperCase()}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Second row with date filters */}\n              <div className=\"flex flex-col md:flex-row gap-4\">\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">Date Added</label>\n                  <select\n                    value={dateFilter}\n                    onChange={(e) => setDateFilter(e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">All Time</option>\n                    <option value=\"day\">Last 24 Hours</option>\n                    <option value=\"week\">Last 7 Days</option>\n                    <option value=\"month\">Last 30 Days</option>\n                  </select>\n                </div>\n\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">Date Range</label>\n                  <div className=\"flex space-x-2\">\n                    <div className=\"w-1/2\">\n                      <input\n                        type=\"date\"\n                        id=\"start-date\"\n                        value={dateRangeFilter.startDate}\n                        onChange={(e) => handleDateRangeChange('startDate', e.target.value)}\n                        className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"Start date\"\n                      />\n                    </div>\n                    <div className=\"w-1/2\">\n                      <input\n                        type=\"date\"\n                        id=\"end-date\"\n                        value={dateRangeFilter.endDate}\n                        onChange={(e) => handleDateRangeChange('endDate', e.target.value)}\n                        className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"End date\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"md:w-1/3 flex items-end\">\n                  <button\n                    onClick={resetFilters}\n                    className=\"px-3 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md text-sm w-full\"\n                  >\n                    Reset Filters\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"text-sm text-gray-500 mb-2\">\n          Showing {filteredDocuments.length} of {documents.length} documents\n        </div>\n      </div>\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th scope=\"col\" className=\"px-3 py-3 text-center\">\n                <input\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500\"\n                  checked={selectedDocuments.length > 0 && selectedDocuments.length === filteredDocuments.length}\n                  onChange={() => toggleSelectAll(filteredDocuments)}\n                />\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('name')}\n              >\n                Name\n                {sortField === 'name' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('uploadedAt')}\n              >\n                Date Uploaded\n                {sortField === 'uploadedAt' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('mainCategory')}\n              >\n                Category\n                {sortField === 'mainCategory' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('uploadedBy')}\n              >\n                Uploaded By\n                {sortField === 'uploadedBy' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('qualityScore')}\n              >\n                Quality\n                {sortField === 'qualityScore' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('status')}\n              >\n                Status\n                {sortField === 'status' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th scope=\"col\" className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {sortedDocuments.length > 0 ? (\n              sortedDocuments.map((document) => (\n                <tr key={document.id} className={selectedDocuments.includes(document.id) ? 'bg-blue-50' : undefined}>\n                  <td className=\"px-3 py-4 whitespace-nowrap text-center\">\n                    <input\n                      type=\"checkbox\"\n                      className=\"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500\"\n                      checked={selectedDocuments.includes(document.id)}\n                      onChange={() => toggleSelectDocument(document.id)}\n                    />\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {document.name}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">\n                      {document.fileType.toUpperCase()}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {formatDate(document.uploadedAt)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900\">\n                      {document.mainCategory || 'N/A'}\n                      {document.category && <span> / {document.category}</span>}\n                    </div>\n                    {document.subCategory && (\n                      <div className=\"text-xs text-gray-500\">\n                        {document.subCategory}\n                        {document.minorCategory && <span> / {document.minorCategory}</span>}\n                      </div>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {document.uploadedBy || 'Unknown'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {document.qualityScore !== undefined ? (\n                      <div className=\"flex items-center\">\n                        <div className=\"w-24 bg-gray-200 rounded-full h-2.5\">\n                          <div\n                            className=\"bg-blue-600 h-2.5 rounded-full\"\n                            style={{ width: `${document.qualityScore}%` }}\n                          ></div>\n                          <div\n                            className=\"bg-gray-300 h-2.5 rounded-r-full\"\n                            style={{\n                              width: `${100 - document.qualityScore}%`,\n                              float: 'right',\n                            }}\n                          ></div>\n                        </div>\n                        <span className=\"text-sm text-gray-700 ml-2\">\n                          {document.qualityScore}%\n                        </span>\n                      </div>\n                    ) : (\n                      <span className=\"text-sm text-gray-500\">N/A</span>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {renderStatusBadge(document.status)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                    <div className=\"flex space-x-2 justify-end\">\n                      <button\n                        onClick={() => onView(document)}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                        title=\"View document details\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                        </svg>\n                      </button>\n                      <button\n                        onClick={() => onEdit(document)}\n                        className=\"text-indigo-600 hover:text-indigo-900\"\n                        title=\"Edit document\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                        </svg>\n                      </button>\n                      <button\n                        onClick={() => handleEditCategories(document)}\n                        className=\"text-purple-600 hover:text-purple-900\"\n                        title=\"Edit categories\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\" />\n                        </svg>\n                      </button>\n                      <button\n                        onClick={() => onReprocess(document)}\n                        className=\"text-yellow-600 hover:text-yellow-900\"\n                        title=\"Reprocess document\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                        </svg>\n                      </button>\n                      <button\n                        onClick={() => onDelete(document)}\n                        className=\"text-red-600 hover:text-red-900\"\n                        title=\"Delete document\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                        </svg>\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))\n            ) : (\n              <tr>\n                <td colSpan={8} className=\"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\">\n                  No documents found\n                </td>\n              </tr>\n            )}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Category Editor Modal */}\n      {documentToEdit && (\n        <DocumentCategoryEditor\n          document={documentToEdit}\n          isOpen={categoryEditorOpen}\n          onClose={handleCategoryEditorClose}\n          onSave={handleCategoryUpdate}\n        />\n      )}\n\n      {/* Bulk Category Editor Modal */}\n      <BulkCategoryEditor\n        documents={documents.filter(doc => selectedDocuments.includes(doc.id))}\n        isOpen={bulkCategoryEditorOpen}\n        onClose={handleBulkCategoryEditorClose}\n        onSave={handleBulkCategoryUpdate}\n      />\n    </div>\n  );\n};\n\nexport default DocumentsTable;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAEvC,MAAO,CAAAC,sBAAsB,KAAM,0BAA0B,CAC7D,MAAO,CAAAC,kBAAkB,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWtD,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAOhD,IAPiD,CACrDC,SAAS,CACTC,MAAM,CACNC,MAAM,CACNC,QAAQ,CACRC,WAAW,CACXC,gBACF,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,SAAS,CAAEC,YAAY,CAAC,CAAGhB,QAAQ,CAAiB,YAAY,CAAC,CACxE,KAAM,CAACiB,aAAa,CAAEC,gBAAgB,CAAC,CAAGlB,QAAQ,CAAiB,MAAM,CAAC,CAC1E,KAAM,CAACmB,WAAW,CAAEC,cAAc,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACqB,cAAc,CAAEC,iBAAiB,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACuB,YAAY,CAAEC,eAAe,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyB,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CAAC2B,UAAU,CAAEC,aAAa,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6B,cAAc,CAAEC,iBAAiB,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC+B,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhC,QAAQ,CAAW,EAAE,CAAC,CACxE,KAAM,CAACiC,WAAW,CAAEC,cAAc,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACmC,eAAe,CAAEC,kBAAkB,CAAC,CAAGpC,QAAQ,CAAuC,CAACqC,SAAS,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAE,CAAC,CAAC,CAE1H;AACA,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACyC,cAAc,CAAEC,iBAAiB,CAAC,CAAG1C,QAAQ,CAAkB,IAAI,CAAC,CAE3E;AACA,KAAM,CAAC2C,sBAAsB,CAAEC,yBAAyB,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CAE3E;AACA,KAAM,CAAA6C,UAAU,CAAIC,KAAqB,EAAK,CAC5C,GAAI/B,SAAS,GAAK+B,KAAK,CAAE,CACvB5B,gBAAgB,CAACD,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC5D,CAAC,IAAM,CACLD,YAAY,CAAC8B,KAAK,CAAC,CACnB5B,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAA6B,eAAe,CAAIC,IAAgB,EAAK,CAC5C,GAAIjB,iBAAiB,CAACkB,MAAM,GAAKD,IAAI,CAACC,MAAM,CAAE,CAC5CjB,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAC,IAAM,CACLA,oBAAoB,CAACgB,IAAI,CAACE,GAAG,CAACC,GAAG,EAAIA,GAAG,CAACC,EAAE,CAAC,CAAC,CAC/C,CACF,CAAC,CAED;AACA,KAAM,CAAAC,oBAAoB,CAAID,EAAU,EAAK,CAC3C,GAAIrB,iBAAiB,CAACuB,QAAQ,CAACF,EAAE,CAAC,CAAE,CAClCpB,oBAAoB,CAACD,iBAAiB,CAACwB,MAAM,CAACC,KAAK,EAAIA,KAAK,GAAKJ,EAAE,CAAC,CAAC,CACvE,CAAC,IAAM,CACLpB,oBAAoB,CAAC,CAAC,GAAGD,iBAAiB,CAAEqB,EAAE,CAAC,CAAC,CAClD,CACF,CAAC,CAED;AACA,KAAM,CAAAK,oBAAoB,CAAIC,SAAmD,EAAK,CACpF,GAAI3B,iBAAiB,CAACkB,MAAM,GAAK,CAAC,CAAE,OAEpC,KAAM,CAAAU,YAAY,CAAGlD,SAAS,CAAC8C,MAAM,CAACJ,GAAG,EAAIpB,iBAAiB,CAACuB,QAAQ,CAACH,GAAG,CAACC,EAAE,CAAC,CAAC,CAEhF,GAAIM,SAAS,GAAK,QAAQ,CAAE,CAC1B,GAAIE,MAAM,CAACC,OAAO,CAAC,mCAAmC9B,iBAAiB,CAACkB,MAAM,wBAAwB,CAAC,CAAE,CACvGU,YAAY,CAACG,OAAO,CAACX,GAAG,EAAIvC,QAAQ,CAACuC,GAAG,CAAC,CAAC,CAC1CnB,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CACF,CAAC,IAAM,IAAI0B,SAAS,GAAK,WAAW,CAAE,CACpCC,YAAY,CAACG,OAAO,CAACX,GAAG,EAAItC,WAAW,CAACsC,GAAG,CAAC,CAAC,CAC7CnB,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAC,IAAM,IAAI0B,SAAS,GAAK,eAAe,CAAE,CACxCd,yBAAyB,CAAC,IAAI,CAAC,CACjC,CACF,CAAC,CAED;AACA,KAAM,CAAAmB,YAAY,CAAGA,CAAA,GAAM,CACzB3C,cAAc,CAAC,EAAE,CAAC,CAClBM,qBAAqB,CAAC,EAAE,CAAC,CACzBJ,iBAAiB,CAAC,EAAE,CAAC,CACrBE,eAAe,CAAC,EAAE,CAAC,CACnBM,iBAAiB,CAAC,EAAE,CAAC,CACrBF,aAAa,CAAC,EAAE,CAAC,CACjBQ,kBAAkB,CAAC,CAAEC,SAAS,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CACpD,CAAC,CAED;AACA,KAAM,CAAA0B,qBAAqB,CAAGA,CAAClB,KAA8B,CAAEmB,KAAa,GAAK,CAC/E7B,kBAAkB,CAAC8B,IAAI,GAAK,CAC1B,GAAGA,IAAI,CACP,CAACpB,KAAK,EAAGmB,KACX,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAE,oBAAoB,CAAIC,QAAkB,EAAK,CACnD1B,iBAAiB,CAAC0B,QAAQ,CAAC,CAC3B5B,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA6B,yBAAyB,CAAGA,CAAA,GAAM,CACtC7B,qBAAqB,CAAC,KAAK,CAAC,CAC5BE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAA4B,oBAAoB,CAAIC,eAAyB,EAAK,CAC1D,GAAIzD,gBAAgB,CAAE,CACpBA,gBAAgB,CAACyD,eAAe,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAIC,gBAA4B,EAAK,CACjE,GAAI3D,gBAAgB,CAAE,CACpB2D,gBAAgB,CAACX,OAAO,CAACX,GAAG,EAAIrC,gBAAgB,CAACqC,GAAG,CAAC,CAAC,CACxD,CACAnB,oBAAoB,CAAC,EAAE,CAAC,CACxBY,yBAAyB,CAAC,KAAK,CAAC,CAClC,CAAC,CAED,KAAM,CAAA8B,6BAA6B,CAAGA,CAAA,GAAM,CAC1C9B,yBAAyB,CAAC,KAAK,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAA+B,iBAAiB,CAAGlE,SAAS,CAAC8C,MAAM,CAAEJ,GAAG,EAAK,CAClD;AACA,GAAIhC,WAAW,CAAE,KAAAyD,eAAA,CAAAC,aAAA,CAAAC,iBAAA,CACf,KAAM,CAAAC,KAAK,CAAG5D,WAAW,CAAC6D,WAAW,CAAC,CAAC,CACvC,KAAM,CAAAC,YAAY,CAChB9B,GAAG,CAAC+B,IAAI,CAACF,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAACyB,KAAK,CAAC,IAAAH,eAAA,CACtCzB,GAAG,CAACgC,UAAU,UAAAP,eAAA,iBAAdA,eAAA,CAAgBI,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAACyB,KAAK,CAAC,KAAAF,aAAA,CAC7C1B,GAAG,CAACiC,QAAQ,UAAAP,aAAA,iBAAZA,aAAA,CAAcG,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAACyB,KAAK,CAAC,KAAAD,iBAAA,CAC3C3B,GAAG,CAACkC,YAAY,UAAAP,iBAAA,iBAAhBA,iBAAA,CAAkBE,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAACyB,KAAK,CAAC,EAEjD,GAAI,CAACE,YAAY,CAAE,MAAO,MAAK,CACjC,CAEA;AACA,GAAIxD,kBAAkB,EAAI0B,GAAG,CAACkC,YAAY,GAAK5D,kBAAkB,CAAE,CACjE,MAAO,MAAK,CACd,CAEA;AACA,GACEJ,cAAc,EACd,EACE8B,GAAG,CAACkC,YAAY,GAAKhE,cAAc,EACnC8B,GAAG,CAACiC,QAAQ,GAAK/D,cAAc,EAC/B8B,GAAG,CAACmC,WAAW,GAAKjE,cAAc,EAClC8B,GAAG,CAACoC,aAAa,GAAKlE,cAAc,CACrC,CACD,CACA,MAAO,MAAK,CACd,CAEA;AACA,GAAIE,YAAY,EAAI4B,GAAG,CAACqC,MAAM,GAAKjE,YAAY,CAAE,CAC/C,MAAO,MAAK,CACd,CAEA;AACA,GAAIM,cAAc,EAAIsB,GAAG,CAACsC,QAAQ,GAAK5D,cAAc,CAAE,CACrD,MAAO,MAAK,CACd,CAEA;AACA,GAAIF,UAAU,CAAE,CACd,KAAM,CAAA+D,OAAO,CAAG,GAAI,CAAAC,IAAI,CAACxC,GAAG,CAACyC,UAAU,CAAC,CACxC,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAF,IAAI,CAAC,CAAC,CAEtB,GAAIhE,UAAU,GAAK,KAAK,CAAE,CACxB;AACA,KAAM,CAAAmE,SAAS,CAAG,GAAI,CAAAH,IAAI,CAACE,GAAG,CAACE,OAAO,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAC/D,GAAIL,OAAO,CAAGI,SAAS,CAAE,MAAO,MAAK,CACvC,CAAC,IAAM,IAAInE,UAAU,GAAK,MAAM,CAAE,CAChC;AACA,KAAM,CAAAqE,QAAQ,CAAG,GAAI,CAAAL,IAAI,CAACE,GAAG,CAACE,OAAO,CAAC,CAAC,CAAG,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAClE,GAAIL,OAAO,CAAGM,QAAQ,CAAE,MAAO,MAAK,CACtC,CAAC,IAAM,IAAIrE,UAAU,GAAK,OAAO,CAAE,CACjC;AACA,KAAM,CAAAsE,SAAS,CAAG,GAAI,CAAAN,IAAI,CAACE,GAAG,CAACE,OAAO,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CACpE,GAAIL,OAAO,CAAGO,SAAS,CAAE,MAAO,MAAK,CACvC,CACF,CAEA;AACA,GAAI9D,eAAe,CAACE,SAAS,EAAIF,eAAe,CAACG,OAAO,CAAE,CACxD,KAAM,CAAAoD,OAAO,CAAG,GAAI,CAAAC,IAAI,CAACxC,GAAG,CAACyC,UAAU,CAAC,CAExC,GAAIzD,eAAe,CAACE,SAAS,CAAE,CAC7B,KAAM,CAAAA,SAAS,CAAG,GAAI,CAAAsD,IAAI,CAACxD,eAAe,CAACE,SAAS,CAAC,CACrD;AACAA,SAAS,CAAC6D,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC9B,GAAIR,OAAO,CAAGrD,SAAS,CAAE,MAAO,MAAK,CACvC,CAEA,GAAIF,eAAe,CAACG,OAAO,CAAE,CAC3B,KAAM,CAAAA,OAAO,CAAG,GAAI,CAAAqD,IAAI,CAACxD,eAAe,CAACG,OAAO,CAAC,CACjD;AACAA,OAAO,CAAC4D,QAAQ,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAC,CACjC,GAAIR,OAAO,CAAGpD,OAAO,CAAE,MAAO,MAAK,CACrC,CACF,CAEA,MAAO,KAAI,CACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAA6D,eAAe,CAAG,CAAC,GAAGxB,iBAAiB,CAAC,CAACyB,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,KAAAC,YAAA,CAAAC,YAAA,CAC5D;AACA,KAAM,CAAAC,MAAM,EAAAF,YAAA,CAAGF,CAAC,CAACtF,SAAS,CAAC,UAAAwF,YAAA,UAAAA,YAAA,CAAI,EAAE,CACjC,KAAM,CAAAG,MAAM,EAAAF,YAAA,CAAGF,CAAC,CAACvF,SAAS,CAAC,UAAAyF,YAAA,UAAAA,YAAA,CAAI,EAAE,CAEjC,GAAIC,MAAM,CAAGC,MAAM,CAAE,CACnB,MAAO,CAAAzF,aAAa,GAAK,KAAK,CAAG,CAAC,CAAC,CAAG,CAAC,CACzC,CACA,GAAIwF,MAAM,CAAGC,MAAM,CAAE,CACnB,MAAO,CAAAzF,aAAa,GAAK,KAAK,CAAG,CAAC,CAAG,CAAC,CAAC,CACzC,CACA,MAAO,EAAC,CACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAA0F,UAAU,CAAIC,OAAe,EAAK,CACtC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAlB,IAAI,CAACiB,OAAO,CAAC,CAC9B,MAAO,IAAI,CAAAE,IAAI,CAACC,cAAc,CAAC,OAAO,CAAE,CACtCC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,OAAO,CACdC,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,CAAC,CACjB,CAAC,CAED;AACA,KAAM,CAAAS,iBAAiB,CAAI9B,MAA0B,EAAK,CACxD,GAAI,CAAA+B,OAAO,CACX,OAAQ/B,MAAM,EACZ,IAAK,WAAW,CACd+B,OAAO,CAAG,6BAA6B,CACvC,MACF,IAAK,SAAS,CACZA,OAAO,CAAG,+BAA+B,CACzC,MACF,IAAK,eAAe,CAClBA,OAAO,CAAG,yBAAyB,CACnC,MACF,QACEA,OAAO,CAAG,2BAA2B,CACzC,CAEA,mBACEnH,IAAA,SAAMoH,SAAS,CAAE,GAAGD,OAAO,6CAA8C,CAAAE,QAAA,CACtEjC,MAAM,CACH,CAAC,CAEX,CAAC,CAED;AACA,KAAM,CAAAkC,oBAAoB,CAAGC,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAACpH,SAAS,CAACyC,GAAG,CAACC,GAAG,EAAIA,GAAG,CAACkC,YAAY,CAAC,CAAC9B,MAAM,CAACuE,OAAO,CAAC,CAAC,CAAC,CACxG,KAAM,CAAAC,gBAAgB,CAAGJ,KAAK,CAACC,IAAI,CACjC,GAAI,CAAAC,GAAG,CACLpH,SAAS,CAACuH,OAAO,CAAE7E,GAAG,EAAK,CACzBA,GAAG,CAACiC,QAAQ,CACZjC,GAAG,CAACmC,WAAW,CACfnC,GAAG,CAACoC,aAAa,CAClB,CAAC,CAAChC,MAAM,CAACuE,OAAO,CACnB,CACF,CAAC,CACD,KAAM,CAAAG,eAAe,CAAGN,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAACpH,SAAS,CAACyC,GAAG,CAACC,GAAG,EAAIA,GAAG,CAACsC,QAAQ,CAAC,CAAClC,MAAM,CAACuE,OAAO,CAAC,CAAC,CAAC,CAE/F,mBACExH,KAAA,QAAKkH,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DnH,KAAA,QAAKkH,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CnH,KAAA,QAAKkH,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDrH,IAAA,OAAI,aAAW,iBAAiB,CAACoH,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAExFnH,KAAA,QAAKkH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnH,KAAA,WACE4H,OAAO,CAAEA,CAAA,GAAMhG,cAAc,CAAC,CAACD,WAAW,CAAE,CAC5CuF,SAAS,CAAC,4FAA4F,CAAAC,QAAA,eAEtGrH,IAAA,SAAAqH,QAAA,CAAOxF,WAAW,CAAG,cAAc,CAAG,cAAc,CAAO,CAAC,cAC5D7B,IAAA,QAAK+H,KAAK,CAAC,4BAA4B,CAACX,SAAS,CAAC,cAAc,CAACY,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAb,QAAA,cACpHrH,IAAA,SAAMmI,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,yJAAyJ,CAAE,CAAC,CAC9N,CAAC,EACA,CAAC,CAER3G,iBAAiB,CAACkB,MAAM,CAAG,CAAC,eAC3B3C,KAAA,QAAKkH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnH,KAAA,WACE4H,OAAO,CAAEA,CAAA,GAAMzE,oBAAoB,CAAC,eAAe,CAAE,CACrD+D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,EAC3F,qBACoB,CAAC1F,iBAAiB,CAACkB,MAAM,CAAC,GAC/C,EAAQ,CAAC,cACT3C,KAAA,WACE4H,OAAO,CAAEA,CAAA,GAAMzE,oBAAoB,CAAC,WAAW,CAAE,CACjD+D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,EAC3F,aACY,CAAC1F,iBAAiB,CAACkB,MAAM,CAAC,GACvC,EAAQ,CAAC,cACT3C,KAAA,WACE4H,OAAO,CAAEA,CAAA,GAAMzE,oBAAoB,CAAC,QAAQ,CAAE,CAC9C+D,SAAS,CAAC,uEAAuE,CAAAC,QAAA,EAClF,UACS,CAAC1F,iBAAiB,CAACkB,MAAM,CAAC,GACpC,EAAQ,CAAC,EACN,CACN,EACE,CAAC,EACH,CAAC,cAGN3C,KAAA,QAAKkH,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnDrH,IAAA,QAAKoH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCrH,IAAA,UACEuI,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oDAAoD,CAChE3E,KAAK,CAAE9C,WAAY,CACnB0H,QAAQ,CAAGC,CAAC,EAAK1H,cAAc,CAAC0H,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE,CAChDuD,SAAS,CAAC,kGAAkG,CAC7G,CAAC,CACC,CAAC,cAENpH,IAAA,QAAKoH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCnH,KAAA,WACE2D,KAAK,CAAE1C,YAAa,CACpBsH,QAAQ,CAAGC,CAAC,EAAKtH,eAAe,CAACsH,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE,CACjDuD,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAE5GrH,IAAA,WAAQ6D,KAAK,CAAC,EAAE,CAAAwD,QAAA,CAAC,cAAY,CAAQ,CAAC,cACtCrH,IAAA,WAAQ6D,KAAK,CAAC,WAAW,CAAAwD,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5CrH,IAAA,WAAQ6D,KAAK,CAAC,SAAS,CAAAwD,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxCrH,IAAA,WAAQ6D,KAAK,CAAC,YAAY,CAAAwD,QAAA,CAAC,YAAU,CAAQ,CAAC,cAC9CrH,IAAA,WAAQ6D,KAAK,CAAC,eAAe,CAAAwD,QAAA,CAAC,eAAa,CAAQ,CAAC,cACpDrH,IAAA,WAAQ6D,KAAK,CAAC,QAAQ,CAAAwD,QAAA,CAAC,QAAM,CAAQ,CAAC,EAChC,CAAC,CACN,CAAC,EACH,CAAC,CAGLxF,WAAW,eACV7B,IAAA,QAAKoH,SAAS,CAAC,sCAAsC,CAAAC,QAAA,cACnDnH,KAAA,QAAKkH,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCnH,KAAA,QAAKkH,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CnH,KAAA,QAAKkH,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrH,IAAA,UAAOoH,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAAa,CAAO,CAAC,cACrFnH,KAAA,WACE2D,KAAK,CAAExC,kBAAmB,CAC1BoH,QAAQ,CAAGC,CAAC,EAAKpH,qBAAqB,CAACoH,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE,CACvDuD,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAE5GrH,IAAA,WAAQ6D,KAAK,CAAC,EAAE,CAAAwD,QAAA,CAAC,qBAAmB,CAAQ,CAAC,CAC5CC,oBAAoB,CAACxE,GAAG,CAAEkC,QAAQ,eACjChF,IAAA,WAAuB6D,KAAK,CAAEmB,QAAS,CAAAqC,QAAA,CACpCrC,QAAQ,EADEA,QAEL,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAEN9E,KAAA,QAAKkH,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrH,IAAA,UAAOoH,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAChFnH,KAAA,WACE2D,KAAK,CAAE5C,cAAe,CACtBwH,QAAQ,CAAGC,CAAC,EAAKxH,iBAAiB,CAACwH,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE,CACnDuD,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAE5GrH,IAAA,WAAQ6D,KAAK,CAAC,EAAE,CAAAwD,QAAA,CAAC,gBAAc,CAAQ,CAAC,CACvCM,gBAAgB,CAAC7E,GAAG,CAAEkC,QAAQ,eAC7BhF,IAAA,WAAuB6D,KAAK,CAAEmB,QAAS,CAAAqC,QAAA,CACpCrC,QAAQ,EADEA,QAEL,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAEN9E,KAAA,QAAKkH,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrH,IAAA,UAAOoH,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cACjFnH,KAAA,WACE2D,KAAK,CAAEpC,cAAe,CACtBgH,QAAQ,CAAGC,CAAC,EAAKhH,iBAAiB,CAACgH,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE,CACnDuD,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAE5GrH,IAAA,WAAQ6D,KAAK,CAAC,EAAE,CAAAwD,QAAA,CAAC,gBAAc,CAAQ,CAAC,CACvCQ,eAAe,CAAC/E,GAAG,CAAEuC,QAAQ,eAC5BrF,IAAA,WAAuB6D,KAAK,CAAEwB,QAAS,CAAAgC,QAAA,CACpChC,QAAQ,CAACuD,WAAW,CAAC,CAAC,EADZvD,QAEL,CACT,CAAC,EACI,CAAC,EACN,CAAC,EACH,CAAC,cAGNnF,KAAA,QAAKkH,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CnH,KAAA,QAAKkH,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrH,IAAA,UAAOoH,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,YAAU,CAAO,CAAC,cAClFnH,KAAA,WACE2D,KAAK,CAAEtC,UAAW,CAClBkH,QAAQ,CAAGC,CAAC,EAAKlH,aAAa,CAACkH,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE,CAC/CuD,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAE5GrH,IAAA,WAAQ6D,KAAK,CAAC,EAAE,CAAAwD,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAClCrH,IAAA,WAAQ6D,KAAK,CAAC,KAAK,CAAAwD,QAAA,CAAC,eAAa,CAAQ,CAAC,cAC1CrH,IAAA,WAAQ6D,KAAK,CAAC,MAAM,CAAAwD,QAAA,CAAC,aAAW,CAAQ,CAAC,cACzCrH,IAAA,WAAQ6D,KAAK,CAAC,OAAO,CAAAwD,QAAA,CAAC,cAAY,CAAQ,CAAC,EACrC,CAAC,EACN,CAAC,cAENnH,KAAA,QAAKkH,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrH,IAAA,UAAOoH,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,YAAU,CAAO,CAAC,cAClFnH,KAAA,QAAKkH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BrH,IAAA,QAAKoH,SAAS,CAAC,OAAO,CAAAC,QAAA,cACpBrH,IAAA,UACEuI,IAAI,CAAC,MAAM,CACXvF,EAAE,CAAC,YAAY,CACfa,KAAK,CAAE9B,eAAe,CAACE,SAAU,CACjCwG,QAAQ,CAAGC,CAAC,EAAK9E,qBAAqB,CAAC,WAAW,CAAE8E,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE,CACpEuD,SAAS,CAAC,kGAAkG,CAC5GoB,WAAW,CAAC,YAAY,CACzB,CAAC,CACC,CAAC,cACNxI,IAAA,QAAKoH,SAAS,CAAC,OAAO,CAAAC,QAAA,cACpBrH,IAAA,UACEuI,IAAI,CAAC,MAAM,CACXvF,EAAE,CAAC,UAAU,CACba,KAAK,CAAE9B,eAAe,CAACG,OAAQ,CAC/BuG,QAAQ,CAAGC,CAAC,EAAK9E,qBAAqB,CAAC,SAAS,CAAE8E,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE,CAClEuD,SAAS,CAAC,kGAAkG,CAC5GoB,WAAW,CAAC,UAAU,CACvB,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENxI,IAAA,QAAKoH,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtCrH,IAAA,WACE8H,OAAO,CAAEnE,YAAa,CACtByD,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAC5F,eAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,cAEDnH,KAAA,QAAKkH,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,UAClC,CAAC9C,iBAAiB,CAAC1B,MAAM,CAAC,MAAI,CAACxC,SAAS,CAACwC,MAAM,CAAC,YAC1D,EAAK,CAAC,EACH,CAAC,cAGN7C,IAAA,QAAKoH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BnH,KAAA,UAAOkH,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpDrH,IAAA,UAAOoH,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3BnH,KAAA,OAAAmH,QAAA,eACErH,IAAA,OAAI6I,KAAK,CAAC,KAAK,CAACzB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cAC/CrH,IAAA,UACEuI,IAAI,CAAC,UAAU,CACfnB,SAAS,CAAC,mEAAmE,CAC7E0B,OAAO,CAAEnH,iBAAiB,CAACkB,MAAM,CAAG,CAAC,EAAIlB,iBAAiB,CAACkB,MAAM,GAAK0B,iBAAiB,CAAC1B,MAAO,CAC/F4F,QAAQ,CAAEA,CAAA,GAAM9F,eAAe,CAAC4B,iBAAiB,CAAE,CACpD,CAAC,CACA,CAAC,cACLrE,KAAA,OACE2I,KAAK,CAAC,KAAK,CACXzB,SAAS,CAAC,+FAA+F,CACzGU,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,MAAM,CAAE,CAAA4E,QAAA,EACnC,MAEC,CAAC1G,SAAS,GAAK,MAAM,eACnBX,IAAA,SAAMoH,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnBxG,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLX,KAAA,OACE2I,KAAK,CAAC,KAAK,CACXzB,SAAS,CAAC,+FAA+F,CACzGU,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,YAAY,CAAE,CAAA4E,QAAA,EACzC,eAEC,CAAC1G,SAAS,GAAK,YAAY,eACzBX,IAAA,SAAMoH,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnBxG,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLX,KAAA,OACE2I,KAAK,CAAC,KAAK,CACXzB,SAAS,CAAC,+FAA+F,CACzGU,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,cAAc,CAAE,CAAA4E,QAAA,EAC3C,UAEC,CAAC1G,SAAS,GAAK,cAAc,eAC3BX,IAAA,SAAMoH,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnBxG,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLX,KAAA,OACE2I,KAAK,CAAC,KAAK,CACXzB,SAAS,CAAC,+FAA+F,CACzGU,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,YAAY,CAAE,CAAA4E,QAAA,EACzC,aAEC,CAAC1G,SAAS,GAAK,YAAY,eACzBX,IAAA,SAAMoH,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnBxG,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLX,KAAA,OACE2I,KAAK,CAAC,KAAK,CACXzB,SAAS,CAAC,+FAA+F,CACzGU,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,cAAc,CAAE,CAAA4E,QAAA,EAC3C,SAEC,CAAC1G,SAAS,GAAK,cAAc,eAC3BX,IAAA,SAAMoH,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnBxG,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLX,KAAA,OACE2I,KAAK,CAAC,KAAK,CACXzB,SAAS,CAAC,+FAA+F,CACzGU,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,QAAQ,CAAE,CAAA4E,QAAA,EACrC,QAEC,CAAC1G,SAAS,GAAK,QAAQ,eACrBX,IAAA,SAAMoH,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnBxG,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLb,IAAA,OAAI6I,KAAK,CAAC,KAAK,CAACzB,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,SAE5G,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRrH,IAAA,UAAOoH,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDtB,eAAe,CAAClD,MAAM,CAAG,CAAC,CACzBkD,eAAe,CAACjD,GAAG,CAAEkB,QAAQ,eAC3B9D,KAAA,OAAsBkH,SAAS,CAAEzF,iBAAiB,CAACuB,QAAQ,CAACc,QAAQ,CAAChB,EAAE,CAAC,CAAG,YAAY,CAAG+F,SAAU,CAAA1B,QAAA,eAClGrH,IAAA,OAAIoH,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACrDrH,IAAA,UACEuI,IAAI,CAAC,UAAU,CACfnB,SAAS,CAAC,mEAAmE,CAC7E0B,OAAO,CAAEnH,iBAAiB,CAACuB,QAAQ,CAACc,QAAQ,CAAChB,EAAE,CAAE,CACjDyF,QAAQ,CAAEA,CAAA,GAAMxF,oBAAoB,CAACe,QAAQ,CAAChB,EAAE,CAAE,CACnD,CAAC,CACA,CAAC,cACL9C,KAAA,OAAIkH,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACzCrH,IAAA,QAAKoH,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CrD,QAAQ,CAACc,IAAI,CACX,CAAC,cACN9E,IAAA,QAAKoH,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnCrD,QAAQ,CAACqB,QAAQ,CAACuD,WAAW,CAAC,CAAC,CAC7B,CAAC,EACJ,CAAC,cACL5I,IAAA,OAAIoH,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9Dd,UAAU,CAACvC,QAAQ,CAACwB,UAAU,CAAC,CAC9B,CAAC,cACLtF,KAAA,OAAIkH,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACzCnH,KAAA,QAAKkH,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCrD,QAAQ,CAACiB,YAAY,EAAI,KAAK,CAC9BjB,QAAQ,CAACgB,QAAQ,eAAI9E,KAAA,SAAAmH,QAAA,EAAM,KAAG,CAACrD,QAAQ,CAACgB,QAAQ,EAAO,CAAC,EACtD,CAAC,CACLhB,QAAQ,CAACkB,WAAW,eACnBhF,KAAA,QAAKkH,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCrD,QAAQ,CAACkB,WAAW,CACpBlB,QAAQ,CAACmB,aAAa,eAAIjF,KAAA,SAAAmH,QAAA,EAAM,KAAG,CAACrD,QAAQ,CAACmB,aAAa,EAAO,CAAC,EAChE,CACN,EACC,CAAC,cACLnF,IAAA,OAAIoH,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DrD,QAAQ,CAACe,UAAU,EAAI,SAAS,CAC/B,CAAC,cACL/E,IAAA,OAAIoH,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CACxCrD,QAAQ,CAACgF,YAAY,GAAKD,SAAS,cAClC7I,KAAA,QAAKkH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnH,KAAA,QAAKkH,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDrH,IAAA,QACEoH,SAAS,CAAC,gCAAgC,CAC1C6B,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGlF,QAAQ,CAACgF,YAAY,GAAI,CAAE,CAC1C,CAAC,cACPhJ,IAAA,QACEoH,SAAS,CAAC,kCAAkC,CAC5C6B,KAAK,CAAE,CACLC,KAAK,CAAE,GAAG,GAAG,CAAGlF,QAAQ,CAACgF,YAAY,GAAG,CACxCG,KAAK,CAAE,OACT,CAAE,CACE,CAAC,EACJ,CAAC,cACNjJ,KAAA,SAAMkH,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EACzCrD,QAAQ,CAACgF,YAAY,CAAC,GACzB,EAAM,CAAC,EACJ,CAAC,cAENhJ,IAAA,SAAMoH,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,KAAG,CAAM,CAClD,CACC,CAAC,cACLrH,IAAA,OAAIoH,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CACxCH,iBAAiB,CAAClD,QAAQ,CAACoB,MAAM,CAAC,CACjC,CAAC,cACLpF,IAAA,OAAIoH,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACxEnH,KAAA,QAAKkH,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCrH,IAAA,WACE8H,OAAO,CAAEA,CAAA,GAAMxH,MAAM,CAAC0D,QAAQ,CAAE,CAChCoD,SAAS,CAAC,mCAAmC,CAC7CgC,KAAK,CAAC,uBAAuB,CAAA/B,QAAA,cAE7BnH,KAAA,QAAK6H,KAAK,CAAC,4BAA4B,CAACX,SAAS,CAAC,SAAS,CAACY,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAb,QAAA,eAC/GrH,IAAA,SAAMmI,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,kCAAkC,CAAE,CAAC,cAC1GtI,IAAA,SAAMmI,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,yHAAyH,CAAE,CAAC,EAC9L,CAAC,CACA,CAAC,cACTtI,IAAA,WACE8H,OAAO,CAAEA,CAAA,GAAMvH,MAAM,CAACyD,QAAQ,CAAE,CAChCoD,SAAS,CAAC,uCAAuC,CACjDgC,KAAK,CAAC,eAAe,CAAA/B,QAAA,cAErBrH,IAAA,QAAK+H,KAAK,CAAC,4BAA4B,CAACX,SAAS,CAAC,SAAS,CAACY,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAb,QAAA,cAC/GrH,IAAA,SAAMmI,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,wHAAwH,CAAE,CAAC,CAC7L,CAAC,CACA,CAAC,cACTtI,IAAA,WACE8H,OAAO,CAAEA,CAAA,GAAM/D,oBAAoB,CAACC,QAAQ,CAAE,CAC9CoD,SAAS,CAAC,uCAAuC,CACjDgC,KAAK,CAAC,iBAAiB,CAAA/B,QAAA,cAEvBrH,IAAA,QAAK+H,KAAK,CAAC,4BAA4B,CAACX,SAAS,CAAC,SAAS,CAACY,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAb,QAAA,cAC/GrH,IAAA,SAAMmI,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,8HAA8H,CAAE,CAAC,CACnM,CAAC,CACA,CAAC,cACTtI,IAAA,WACE8H,OAAO,CAAEA,CAAA,GAAMrH,WAAW,CAACuD,QAAQ,CAAE,CACrCoD,SAAS,CAAC,uCAAuC,CACjDgC,KAAK,CAAC,oBAAoB,CAAA/B,QAAA,cAE1BrH,IAAA,QAAK+H,KAAK,CAAC,4BAA4B,CAACX,SAAS,CAAC,SAAS,CAACY,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAb,QAAA,cAC/GrH,IAAA,SAAMmI,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,6GAA6G,CAAE,CAAC,CAClL,CAAC,CACA,CAAC,cACTtI,IAAA,WACE8H,OAAO,CAAEA,CAAA,GAAMtH,QAAQ,CAACwD,QAAQ,CAAE,CAClCoD,SAAS,CAAC,iCAAiC,CAC3CgC,KAAK,CAAC,iBAAiB,CAAA/B,QAAA,cAEvBrH,IAAA,QAAK+H,KAAK,CAAC,4BAA4B,CAACX,SAAS,CAAC,SAAS,CAACY,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAb,QAAA,cAC/GrH,IAAA,SAAMmI,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,8HAA8H,CAAE,CAAC,CACnM,CAAC,CACA,CAAC,EACN,CAAC,CACJ,CAAC,GA/GEtE,QAAQ,CAAChB,EAgHd,CACL,CAAC,cAEFhD,IAAA,OAAAqH,QAAA,cACErH,IAAA,OAAIqJ,OAAO,CAAE,CAAE,CAACjC,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAAC,oBAE1F,CAAI,CAAC,CACH,CACL,CACI,CAAC,EACH,CAAC,CACL,CAAC,CAGLhF,cAAc,eACbrC,IAAA,CAACH,sBAAsB,EACrBmE,QAAQ,CAAE3B,cAAe,CACzBiH,MAAM,CAAEnH,kBAAmB,CAC3BoH,OAAO,CAAEtF,yBAA0B,CACnCuF,MAAM,CAAEtF,oBAAqB,CAC9B,CACF,cAGDlE,IAAA,CAACF,kBAAkB,EACjBO,SAAS,CAAEA,SAAS,CAAC8C,MAAM,CAACJ,GAAG,EAAIpB,iBAAiB,CAACuB,QAAQ,CAACH,GAAG,CAACC,EAAE,CAAC,CAAE,CACvEsG,MAAM,CAAE/G,sBAAuB,CAC/BgH,OAAO,CAAEjF,6BAA8B,CACvCkF,MAAM,CAAEpF,wBAAyB,CAClC,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}