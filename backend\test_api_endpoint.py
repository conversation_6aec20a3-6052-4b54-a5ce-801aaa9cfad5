"""
Script to test the API endpoint for document search and answer generation.
"""
import os
import logging
import json
import requests
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def test_api_endpoint():
    """Test the API endpoint for document search and answer generation."""
    logger.info("Testing API endpoint for document search and answer generation...")
    
    # API endpoint
    api_url = "http://localhost:8000/api/query"
    
    # Test queries
    test_queries = [
        "What is the full form of ACP?",
        "What is the full form of FSDS?",
        "What is the Rapid Response app?",
        "What is VASP and who developed it?"
    ]
    
    for query in test_queries:
        logger.info(f"\n=== Testing query: '{query}' ===\n")
        
        # Prepare request data
        request_data = {
            "query": query,
            "use_hybrid_search": True,
            "model_id": "gemini-2.0-flash",
            "extract_format": "paragraph"
        }
        
        # Send request
        try:
            logger.info(f"Sending request to API endpoint: {api_url}")
            response = requests.post(api_url, json=request_data)
            
            # Check response
            if response.status_code == 200:
                result = response.json()
                
                logger.info(f"Response status code: {response.status_code}")
                logger.info(f"Answer: {result.get('answer', 'No answer')}")
                logger.info(f"Sources: {result.get('sources', [])}")
                logger.info(f"Document sources: {result.get('document_sources', [])}")
                logger.info(f"Website sources: {result.get('website_sources', [])}")
            else:
                logger.error(f"Error response: {response.status_code} - {response.text}")
        except Exception as e:
            logger.error(f"Error sending request: {str(e)}")

def main():
    """Main function to test API endpoint."""
    test_api_endpoint()

if __name__ == "__main__":
    main()
