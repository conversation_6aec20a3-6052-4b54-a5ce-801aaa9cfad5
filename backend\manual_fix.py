"""
Directly fix the indentation issues in vector_db.py using a simpler approach.
"""
import os

def fix_vector_db():
    """Create a corrected version of the vector_db.py file with proper indentation."""
    # Create a backup first
    try:
        with open("vector_db.py", "r", encoding="utf-8") as f:
            original_content = f.read()
        
        with open("vector_db.py.backup", "w", encoding="utf-8") as f:
            f.write(original_content)
        
        print("Created backup at vector_db.py.backup")
        
        # Replace the hybrid_search method with a corrected version
        fixed_content = """    def hybrid_search(self, query_embedding: List[float], query_text: str = None, top_k: int = 50, threshold: float = 0.15):
        \"\"\"
        Search for chunks with hybrid method (combine vector similarity with text matching).
        
        Args:
            query_embedding: Query embedding vector
            query_text: Raw query text for text matching
            top_k: Number of most similar chunks to return
            threshold: Minimum similarity threshold
            
        Returns:
            List of metadata for most similar chunks
        \"\"\"
        # Ensure query_embedding is a numpy array of floats
        if not isinstance(query_embedding, np.ndarray):
            try:
                if isinstance(query_embedding, str):
                    import json
                    query_embedding = json.loads(query_embedding)
                query_embedding = np.array(query_embedding, dtype=np.float32)
            except Exception as e:
                logger.error(f"Error converting query_embedding: {str(e)}")
                # Create a default embedding as fallback
                query_embedding = np.array([0.01] * 768, dtype=np.float32)
                
        if self.use_supabase:
            # Supabase pgvector mode with hybrid search - search both documents and websites
            
            # If query_text is not provided, extract it from the first few chunks
            if query_text is None:
                # Fall back to regular semantic search
                logger.info("No query_text provided for hybrid search, falling back to semantic search")
                return self.search(query_embedding, top_k, threshold)
            
            # Define source priority weights
            SOURCE_PRIORITY = {
                "document": 1.5,  # Higher priority for documents
                "website": 1.2    # Medium priority for websites
            }
            
            try:
                # First search documents
                logger.info(f"Searching document chunks in Supabase with threshold {threshold}")
                doc_query = \"\"\"
                    SELECT 
                        dc.id,
                        dc.document_id, 
                        dc.content, 
                        dc.metadata,
                        dc.chunk_index,
                        d.title,
                        d.file_path,
                        1 - (dc.embedding <=> $1) as similarity
                    FROM 
                        document_chunks dc
                    JOIN 
                        documents d ON dc.document_id = d.id
                    WHERE 
                        1 - (dc.embedding <=> $1) > $2
                    ORDER BY 
                        similarity DESC
                    LIMIT $3
                \"\"\"
                
                doc_params = {
                    "1": query_embedding,
                    "2": 0.01,  # Extremely low threshold to find any matches
                    "3": top_k
                }
                
                doc_results = supabase.execute_query(doc_query, doc_params)
                
                if isinstance(doc_results, dict) and "error" in doc_results:
                    logger.error(f"Error in Supabase document search: {doc_results['error']}")
                    doc_results = []
                
                # Process document results
                for chunk in doc_results:
                    # Set source type for downstream processing
                    chunk["source_type"] = "document"
                    
                    # Add source title based on document title
                    if "title" in chunk:
                        chunk["source_title"] = chunk["title"]
                    
                    # Boost document similarity scores
                    if "similarity" in chunk:
                        # Ensure document chunks get higher scores
                        chunk["similarity"] = min(0.99, chunk["similarity"])
                
                # Then search websites
                logger.info(f"Searching website chunks in Supabase with threshold {threshold}")
                web_query = \"\"\"
                    SELECT 
                        wc.id,
                        wc.website_id, 
                        wc.content, 
                        wc.metadata,
                        wc.chunk_index,
                        w.title,
                        w.url,
                        1 - (wc.embedding <=> $1) as similarity
                    FROM 
                        website_chunks wc
                    JOIN 
                        websites w ON wc.website_id = w.id
                    WHERE 
                        1 - (wc.embedding <=> $1) > $2
                    ORDER BY 
                        similarity DESC
                    LIMIT $3
                \"\"\"
                
                web_params = {
                    "1": query_embedding,
                    "2": threshold,
                    "3": top_k
                }
                
                web_results = supabase.execute_query(web_query, web_params)
                
                if isinstance(web_results, dict) and "error" in web_results:
                    logger.error(f"Error in Supabase website search: {web_results['error']}")
                    web_results = []
                
                # Process website results
                for chunk in web_results:
                    # Set source type for downstream processing
                    chunk["source_type"] = "website"
                    
                    # Add source title based on website title
                    if "title" in chunk:
                        chunk["source_title"] = chunk["title"]
                
                # Apply priority weights to document results (force high similarity)
                for chunk in doc_results:
                    if "similarity" in chunk:
                        # Force document chunks to have very high similarity
                        chunk["similarity"] = min(1.0, chunk["similarity"] * SOURCE_PRIORITY.get("document", 1.2))
                
                # Apply priority weights to website results
                for chunk in web_results:
                    if "similarity" in chunk:
                        # Apply website priority weight (capped at 1.0)
                        chunk["similarity"] = min(1.0, chunk["similarity"] * SOURCE_PRIORITY.get("website", 1.0))
                
                # Combine results
                all_results = doc_results + web_results
                
                # Sort by similarity score (descending)
                all_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                
                # Limit to top_k results
                all_results = all_results[:top_k]
                
                logger.info(f"Found {len(doc_results)} document chunks and {len(web_results)} website chunks in Supabase hybrid search")
                return all_results
            except Exception as e:
                logger.error(f"Error in Supabase hybrid search: {str(e)}")
                # Fall back to regular search
                return self.search(query_embedding, top_k, threshold)
        else:
            # For local mode, just use regular search as hybrid search is not implemented
            return self.search(query_embedding, top_k, threshold)"""
        
        # Find the beginning and end of the hybrid_search method
        start_marker = "def hybrid_search"
        end_marker = "def clear"
        
        start_pos = original_content.find(start_marker)
        end_pos = original_content.find(end_marker, start_pos)
        
        if start_pos == -1 or end_pos == -1:
            print("ERROR: Could not find the hybrid_search method")
            return False
        
        # Extract the content before and after the method
        content_before = original_content[:start_pos]
        content_after = original_content[end_pos:]
        
        # Create new content with the fixed method
        new_content = content_before + fixed_content + "\n\n    " + content_after
        
        # Write the new content to the file
        with open("vector_db.py", "w", encoding="utf-8") as f:
            f.write(new_content)
        
        print("Successfully fixed vector_db.py")
        return True
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    print("\n=== MANUALLY FIXING VECTOR_DB.PY ===\n")
    
    if fix_vector_db():
        print("\nSuccessfully fixed the indentation in vector_db.py")
        print("Now you can restart your server:")
        print("python -m uvicorn server:app --reload")
    else:
        print("\nFailed to fix vector_db.py")
        print("Please manually edit the file to fix indentation issues")
