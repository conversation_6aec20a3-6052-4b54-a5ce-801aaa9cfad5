# RailGPT Google Cloud Platform Quick Start

## 🚀 One-Command Deployment

### Prerequisites (5 minutes)

1. **Install Google Cloud SDK**
   ```bash
   # macOS
   brew install google-cloud-sdk
   
   # Windows
   # Download from: https://cloud.google.com/sdk/docs/install
   
   # Linux
   curl https://sdk.cloud.google.com | bash
   ```

2. **Login to Google Cloud**
   ```bash
   gcloud auth login
   gcloud auth application-default login
   ```

3. **Get your API keys**
   - **Gemini API Key** (Required): [Get from Google AI Studio](https://makersuite.google.com/app/apikey)
   - **OpenAI API Key** (Optional): [Get from OpenAI](https://platform.openai.com/api-keys)

### 🎯 Quick Deployment (10 minutes)

```bash
# 1. Clone the repository
git clone https://github.com/yourusername/railgpt.git
cd railgpt

# 2. Set your configuration
export PROJECT_ID="railgpt-production-$(date +%s)"
export REGION="us-central1"
export DOMAIN="your-domain.com"  # Optional

# 3. Run the complete setup
./deploy/gcp/setup-complete.sh \
  --project $PROJECT_ID \
  --region $REGION \
  --domain $DOMAIN
```

**That's it! Your application will be deployed automatically.**

## 📋 Manual Step-by-Step (if you prefer control)

### Step 1: Create Project and Setup
```bash
# Create project
gcloud projects create railgpt-production --name="RailGPT Production"

# Set project
gcloud config set project railgpt-production

# Enable billing (required)
# Go to: https://console.cloud.google.com/billing/linkedaccount?project=railgpt-production
```

### Step 2: Configure Environment
```bash
# Copy environment template
cp deploy/.env.example .env

# Edit .env file with your API keys
nano .env
```

### Step 3: Deploy Backend
```bash
./deploy/gcp/deploy-backend.sh --project railgpt-production
```

### Step 4: Deploy Frontend
```bash
./deploy/gcp/deploy-frontend.sh --project railgpt-production
```

## 🌐 Domain Setup (Optional)

### If you have a domain:
```bash
# Deploy with domain
./deploy/gcp/setup-complete.sh \
  --project railgpt-production \
  --domain your-domain.com

# Configure DNS
# Point your domain to the load balancer IP shown in the output
```

### If you don't have a domain:
Your app will be available at:
- Backend: `https://railgpt-backend-xxx.a.run.app`
- Frontend: `https://storage.googleapis.com/your-bucket/index.html`

## 💰 Cost Breakdown

### Free Tier Usage:
- **Cloud Run**: 2 million requests/month free
- **Cloud Storage**: 5GB free
- **Cloud Build**: 120 build-minutes/day free

### Estimated Monthly Costs:
- **Small usage**: $0-10/month
- **Medium usage**: $10-30/month
- **High usage**: $30-100/month

### Cost Optimization Tips:
1. Set minimum instances to 0 for Cloud Run
2. Use Cloud CDN for better performance and lower costs
3. Set up billing alerts
4. Monitor usage in Cloud Console

## 🔧 Management Commands

### View Application Status
```bash
# Backend logs
gcloud logs read --service=railgpt-backend --limit=50

# Backend status
gcloud run services describe railgpt-backend --region=us-central1

# Frontend bucket contents
gsutil ls -la gs://your-bucket-name/
```

### Update Application
```bash
# Update backend only
./deploy/gcp/deploy-backend.sh

# Update frontend only
./deploy/gcp/deploy-frontend.sh

# Update everything
./deploy/gcp/setup-complete.sh
```

### Scale Application
```bash
# Scale backend
gcloud run services update railgpt-backend \
  --region=us-central1 \
  --min-instances=1 \
  --max-instances=100 \
  --concurrency=80
```

## 🔍 Troubleshooting

### Common Issues:

#### 1. "Permission denied" errors
```bash
# Ensure you're logged in
gcloud auth login
gcloud auth application-default login

# Check project permissions
gcloud projects get-iam-policy railgpt-production
```

#### 2. "Billing not enabled" errors
- Go to [Google Cloud Console](https://console.cloud.google.com/billing)
- Enable billing for your project

#### 3. "API not enabled" errors
```bash
# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable storage.googleapis.com
```

#### 4. Frontend not loading
- Check if bucket is public: `gsutil iam ch allUsers:objectViewer gs://your-bucket`
- Verify build completed: `gsutil ls gs://your-bucket/`

#### 5. Backend errors
```bash
# Check logs
gcloud logs read --service=railgpt-backend --limit=50

# Check environment variables
gcloud run services describe railgpt-backend --region=us-central1
```

### Debug Commands:
```bash
# Test backend health
curl https://railgpt-backend-xxx.a.run.app/health

# Test API documentation
curl https://railgpt-backend-xxx.a.run.app/docs

# Check Cloud Build history
gcloud builds list --limit=10

# Monitor resources
gcloud monitoring dashboards list
```

## 🔒 Security Best Practices

### 1. Environment Variables
- Never commit API keys to version control
- Use Google Secret Manager for sensitive data
- Rotate API keys regularly

### 2. Access Control
```bash
# Create service account with minimal permissions
gcloud iam service-accounts create railgpt-app \
  --display-name="RailGPT Application"

# Grant only necessary roles
gcloud projects add-iam-policy-binding railgpt-production \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/storage.objectViewer"
```

### 3. Network Security
- Use Cloud Armor for DDoS protection
- Enable HTTPS only
- Configure proper CORS settings

## 📊 Monitoring and Alerts

### Setup Monitoring:
```bash
# Create uptime checks
gcloud alpha monitoring uptime create \
  --display-name="RailGPT Health Check" \
  --hostname=your-domain.com \
  --path=/health

# Create billing alerts
gcloud alpha billing budgets create \
  --billing-account=YOUR_BILLING_ACCOUNT \
  --display-name="RailGPT Budget Alert" \
  --budget-amount=50USD
```

### Key Metrics to Monitor:
- Cloud Run request count and latency
- Storage bucket usage
- Error rates and logs
- Billing costs

## 🚀 CI/CD Setup (Advanced)

### Automatic Deployment on Git Push:
```bash
# Create build trigger
gcloud builds triggers create github \
  --repo-name=railgpt \
  --repo-owner=your-github-username \
  --branch-pattern="^main$" \
  --build-config=deploy/gcp/cloudbuild.yaml
```

### Manual Build:
```bash
# Run build manually
gcloud builds submit --config=deploy/gcp/cloudbuild.yaml
```

## 📞 Support and Resources

### Documentation:
- [Google Cloud Run](https://cloud.google.com/run/docs)
- [Cloud Storage](https://cloud.google.com/storage/docs)
- [Cloud Build](https://cloud.google.com/build/docs)

### Useful Links:
- [Google Cloud Console](https://console.cloud.google.com)
- [Cloud Monitoring](https://console.cloud.google.com/monitoring)
- [Cloud Billing](https://console.cloud.google.com/billing)

### Getting Help:
1. Check the troubleshooting section above
2. Review Google Cloud documentation
3. Check application logs
4. Contact support if needed

## 🎯 Next Steps After Deployment

1. **Test all functionality**
   - Upload documents
   - Add websites
   - Test chat features
   - Verify search results

2. **Configure monitoring**
   - Set up alerts
   - Monitor performance
   - Track costs

3. **Optimize performance**
   - Enable CDN
   - Configure caching
   - Optimize images

4. **Security hardening**
   - Review IAM permissions
   - Enable audit logs
   - Set up backup policies

5. **Scale preparation**
   - Monitor usage patterns
   - Plan for growth
   - Optimize costs

Your RailGPT application is now running on Google Cloud Platform with enterprise-grade infrastructure!
