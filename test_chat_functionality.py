#!/usr/bin/env python3

import requests
import json
import time

def test_supabase_chat_functionality():
    """Test the chat functionality with <PERSON><PERSON><PERSON>"""
    
    # Supabase configuration
    SUPABASE_URL = "https://rkllidjktazafeinezgo.supabase.co"
    SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA"
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    print("🧪 Testing Chat Functionality...")
    
    # Test 1: Create a new chat session
    print("\n1. Testing chat session creation...")
    create_data = {
        "title": "Test Chat Session",
        "messages": [],
        "model_used": "gemini-2.0-flash",
        "has_document": False,
        "has_website": False
    }
    
    response = requests.post(
        f"{SUPABASE_URL}/rest/v1/chat_sessions",
        headers=headers,
        json=create_data
    )
    
    if response.status_code == 201:
        chat_session = response.json()[0]
        chat_id = chat_session['id']
        print(f"✅ Chat session created successfully: {chat_id}")
        print(f"   Title: {chat_session['title']}")
        print(f"   Model: {chat_session['model_used']}")
    else:
        print(f"❌ Failed to create chat session: {response.status_code}")
        print(f"   Response: {response.text}")
        return False
    
    # Test 2: Add messages to the chat session
    print("\n2. Testing message saving...")
    test_messages = [
        {
            "id": "user-1",
            "content": "What is Indian Railways?",
            "sender": "user",
            "timestamp": "2025-01-20T10:00:00Z",
            "chatId": chat_id
        },
        {
            "id": "ai-1",
            "content": "Indian Railways is the national railway system of India.",
            "sender": "ai",
            "timestamp": "2025-01-20T10:00:05Z",
            "chatId": chat_id,
            "llm_model": "gemini-2.0-flash"
        }
    ]
    
    update_data = {
        "messages": test_messages,
        "has_document": False,
        "has_website": False
    }
    
    response = requests.patch(
        f"{SUPABASE_URL}/rest/v1/chat_sessions?id=eq.{chat_id}",
        headers=headers,
        json=update_data
    )
    
    if response.status_code == 204:
        print("✅ Messages saved successfully")
    else:
        print(f"❌ Failed to save messages: {response.status_code}")
        print(f"   Response: {response.text}")
    
    # Test 3: Retrieve chat sessions
    print("\n3. Testing chat session retrieval...")
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/chat_sessions?order=updated_at.desc",
        headers=headers
    )
    
    if response.status_code == 200:
        sessions = response.json()
        print(f"✅ Retrieved {len(sessions)} chat sessions")
        for session in sessions[:3]:  # Show first 3
            print(f"   - {session['title']} ({session['id'][:8]}...)")
    else:
        print(f"❌ Failed to retrieve chat sessions: {response.status_code}")
        print(f"   Response: {response.text}")
    
    # Test 4: Retrieve specific chat session
    print("\n4. Testing specific chat session retrieval...")
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/chat_sessions?id=eq.{chat_id}",
        headers=headers
    )
    
    if response.status_code == 200:
        session_data = response.json()[0]
        print(f"✅ Retrieved chat session: {session_data['title']}")
        print(f"   Messages count: {len(session_data.get('messages', []))}")
        if session_data.get('messages'):
            print(f"   First message: {session_data['messages'][0]['content'][:50]}...")
    else:
        print(f"❌ Failed to retrieve specific chat session: {response.status_code}")
        print(f"   Response: {response.text}")
    
    # Test 5: Update chat title
    print("\n5. Testing chat title update...")
    update_title_data = {
        "title": "Updated Test Chat"
    }
    
    response = requests.patch(
        f"{SUPABASE_URL}/rest/v1/chat_sessions?id=eq.{chat_id}",
        headers=headers,
        json=update_title_data
    )
    
    if response.status_code == 204:
        print("✅ Chat title updated successfully")
    else:
        print(f"❌ Failed to update chat title: {response.status_code}")
        print(f"   Response: {response.text}")
    
    # Test 6: Delete the test chat session
    print("\n6. Cleaning up test data...")
    response = requests.delete(
        f"{SUPABASE_URL}/rest/v1/chat_sessions?id=eq.{chat_id}",
        headers=headers
    )
    
    if response.status_code == 204:
        print("✅ Test chat session deleted successfully")
    else:
        print(f"❌ Failed to delete test chat session: {response.status_code}")
        print(f"   Response: {response.text}")
    
    print("\n🎉 Chat functionality test completed!")
    return True

if __name__ == "__main__":
    test_supabase_chat_functionality()
