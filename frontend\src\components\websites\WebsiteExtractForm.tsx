import React, { useState, useEffect } from 'react';
import { addWebsite } from '../../services/api';
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";

// Form validation interface
interface FormErrors {
  websiteUrl?: string;
  domainCategory?: string;
}

interface WebsiteExtractFormProps {}

const WebsiteExtractForm: React.FC<WebsiteExtractFormProps> = () => {
  // Form state
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [domainCategory, setDomainCategory] = useState('');
  const [isExtracting, setIsExtracting] = useState(false);
  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);
  const [newCategoryInput, setNewCategoryInput] = useState('');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Advanced options
  const [extractionDepth, setExtractionDepth] = useState<number>(1);
  const [followLinks, setFollowLinks] = useState<boolean>(true);
  const [extractImages, setExtractImages] = useState<boolean>(false);
  const [extractTables, setExtractTables] = useState<boolean>(true);
  const [maxPages, setMaxPages] = useState<number>(10);
  const [extractorType, setExtractorType] = useState<string>('auto');

  // Extraction status tracking
  const [extractionStatus, setExtractionStatus] = useState<'idle' | 'extracting' | 'processing' | 'success' | 'error'>('idle');
  const [extractionProgress, setExtractionProgress] = useState(0);
  const [extractedChunks, setExtractedChunks] = useState<any[]>([]);

  // Form validation and feedback
  const [errors, setErrors] = useState<FormErrors>({});
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Sample domain categories (would be fetched from API in production)
  const [domainCategories, setDomainCategories] = useState<string[]>([
    'Official Railways',
    'News Portals',
    'Travel Guides',
    'Government Sites',
    'Educational Resources'
  ]);

  // Effect to validate form
  useEffect(() => {
    const newErrors: FormErrors = {};

    if (touched.websiteUrl) {
      if (!websiteUrl.trim()) {
        newErrors.websiteUrl = 'Website URL is required';
      } else if (!validateUrl(websiteUrl)) {
        newErrors.websiteUrl = 'Please enter a valid URL';
      }
    }

    setErrors(newErrors);
  }, [websiteUrl, touched]);

  // Mark fields as touched when user interacts with them
  const markAsTouched = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  // Reset form after successful submission
  const resetForm = () => {
    setWebsiteUrl('');
    setDomainCategory('');
    setTouched({});
    setErrors({});
  };

  // Clear success message after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  // Clear error message after 5 seconds
  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => {
        setErrorMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage]);

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  const handleNewCategorySubmit = async () => {
    if (!newCategoryInput.trim()) return;

    setIsExtracting(true);
    try {
      // In a real app, you would make an API call to create the category
      // Example API call:
      // const response = await fetch(`${API_URL}/api/domain-categories`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ name: newCategoryInput })
      // });
      // if (!response.ok) throw new Error('Failed to create category');
      // const data = await response.json();

      // For now, simulate API call with a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update the domain categories
      setDomainCategories(prev => [...prev, newCategoryInput]);

      // Update the selected category
      setDomainCategory(newCategoryInput);

      setSuccessMessage(`Created new domain category: ${newCategoryInput}`);
    } catch (error) {
      setErrorMessage(`Failed to create category: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsExtracting(false);
      setNewCategoryInput('');
      setShowNewCategoryInput(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Mark URL field as touched to trigger validation
    markAsTouched('websiteUrl');

    // Check if there are any validation errors
    if (Object.keys(errors).length > 0) {
      return;
    }

    // Update UI state
    setIsExtracting(true);
    setExtractionStatus('extracting');
    setExtractionProgress(10);
    setSuccessMessage('');
    setErrorMessage('');
    setExtractedChunks([]);

    // Simulate extraction progress updates
    const progressInterval = setInterval(() => {
      setExtractionProgress(prev => {
        const newProgress = prev + 5;
        if (newProgress >= 90) {
          clearInterval(progressInterval);
          return 90; // Hold at 90% until processing is complete
        }
        return newProgress;
      });
    }, 300);

    // Prepare extraction options
    const extractionOptions = {
      followLinks: followLinks,
      extractionDepth: extractionDepth,
      extractImages: extractImages,
      extractTables: extractTables,
      maxPages: maxPages,
      extractorType: extractorType,
      domainCategory: domainCategory || undefined
    };

    // Add the website
    setExtractionStatus('extracting');
    addWebsite(websiteUrl, '<EMAIL>', extractionOptions)
      .then(response => {
        // Clear the progress interval
        clearInterval(progressInterval);

        if (response.success) {
          // Set progress to 100%
          setExtractionProgress(100);
          setExtractionStatus('success');

          // Store extracted chunks if available
          if (response.chunks && response.chunks.length > 0) {
            setExtractedChunks(response.chunks);
            console.log('Extracted chunks:', response.chunks);
          }

          // Create event data for custom event
          const eventData = {
            detail: {
              url: websiteUrl,
              domain: new URL(websiteUrl).hostname,
              extractedAt: new Date().toISOString(),
              id: response.data?.id || `web-${Date.now()}`,
              status: 'Success',
              domainCategory,
              chunks: response.chunks || [],
              extractedContent: response.chunks ? response.chunks.map(chunk => chunk.text).join('\n\n') : '',
              chunks_extracted: response.chunks_extracted || 0
            }
          };

          // Dispatch a custom event to notify parent components (WebsitesPage) of the extraction
          const extractEvent = new CustomEvent('websiteExtracted', eventData);
          window.dispatchEvent(extractEvent);

          setSuccessMessage(`Website ${websiteUrl} added successfully. ${response.chunks_extracted || 0} chunks extracted.`);

          // Reset form after a short delay to allow user to see success message and extracted content
          setTimeout(() => {
            resetForm();
            setExtractionStatus('idle');
            setExtractionProgress(0);
          }, 5000);
        } else {
          setExtractionStatus('error');
          setErrorMessage(`Extraction failed: ${response.message}`);
        }
      })
      .catch(error => {
        clearInterval(progressInterval);
        console.error('Extraction failed:', error);
        // Set error message (ensure success is cleared)
        setSuccessMessage('');
        setErrorMessage(`Extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setExtractionStatus('error');
      })
      .finally(() => {
        setIsExtracting(false);
      });
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 transition-colors duration-300">
      <h2 className="text-lg font-semibold mb-4 text-gray-900">Extract Website Data</h2>

      {/* Success message */}
      {successMessage && (
        <div className="mb-4">
          <Alert variant="success">
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{successMessage}</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Error message */}
      {errorMessage && (
        <div className="mb-4">
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Website URL */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Website URL <span className="text-red-500">*</span>
          </label>
          <input
            type="url"
            value={websiteUrl}
            onChange={(e) => setWebsiteUrl(e.target.value)}
            onBlur={() => markAsTouched('websiteUrl')}
            className={`w-full p-2 border ${errors.websiteUrl ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
            placeholder="https://example.com"
            required
          />
          {errors.websiteUrl && (
            <p className="mt-1 text-sm text-red-500">{errors.websiteUrl}</p>
          )}
        </div>

        {/* Domain Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Domain Category
          </label>
          <div className="flex gap-2">
            <select
              value={domainCategory}
              onChange={(e) => setDomainCategory(e.target.value)}
              className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Domain Category</option>
              {domainCategories.map(cat => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
            <button
              type="button"
              onClick={() => setShowNewCategoryInput(true)}
              className="px-3 py-2 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none"
            >
              + New
            </button>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Optional. Helps organize extracted content by domain type.
          </p>
        </div>

        {/* Extraction Options */}
        <div className="p-3 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-700">Extraction Options</h3>
            <button
              type="button"
              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium focus:outline-none"
            >
              {showAdvancedOptions ? 'Hide Advanced Options' : 'Show Advanced Options'}
            </button>
          </div>
          <div className="space-y-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="follow-links"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={followLinks}
                onChange={(e) => setFollowLinks(e.target.checked)}
              />
              <label htmlFor="follow-links" className="ml-2 block text-sm text-gray-700">
                Follow and extract linked pages
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="extract-images"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={extractImages}
                onChange={(e) => setExtractImages(e.target.checked)}
              />
              <label htmlFor="extract-images" className="ml-2 block text-sm text-gray-700">
                Extract image alt text
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="extract-tables"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={extractTables}
                onChange={(e) => setExtractTables(e.target.checked)}
              />
              <label htmlFor="extract-tables" className="ml-2 block text-sm text-gray-700">
                Extract tables as structured data
              </label>
            </div>

            {showAdvancedOptions && (
              <div className="mt-3 space-y-4 pt-3 border-t border-gray-200">
                <div>
                  <label htmlFor="extraction-depth" className="block text-sm font-medium text-gray-700 mb-1">
                    Extraction Depth: {extractionDepth}
                  </label>
                  <input
                    type="range"
                    id="extraction-depth"
                    min="1"
                    max="5"
                    value={extractionDepth}
                    onChange={(e) => setExtractionDepth(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>1 (Current page only)</span>
                    <span>5 (Deep crawl)</span>
                  </div>
                </div>

                <div>
                  <label htmlFor="max-pages" className="block text-sm font-medium text-gray-700 mb-1">
                    Maximum Pages: {maxPages}
                  </label>
                  <input
                    type="number"
                    id="max-pages"
                    min="1"
                    max="50"
                    value={maxPages}
                    onChange={(e) => setMaxPages(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">Maximum number of pages to extract (1-50)</p>
                </div>

                <div>
                  <label htmlFor="extractor-type" className="block text-sm font-medium text-gray-700 mb-1">
                    Extraction Method
                  </label>
                  <select
                    id="extractor-type"
                    value={extractorType}
                    onChange={(e) => setExtractorType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="auto">Auto (Recommended)</option>
                    <option value="html">HTML Only</option>
                    <option value="browser">Browser Rendering</option>
                    <option value="api">API-Based (If Available)</option>
                  </select>
                  <p className="mt-1 text-xs text-gray-500">Method used to extract content from the website</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div>
          <button
            type="submit"
            disabled={isExtracting}
            className={`w-full px-4 py-2 text-white font-medium rounded-md ${
              isExtracting ? 'bg-green-300' : 'bg-green-600 hover:bg-green-700'
            } focus:outline-none focus:ring-2 focus:ring-green-500`}
          >
            {isExtracting ? 'Extracting...' : 'Extract Website'}
          </button>
        </div>

        {/* Extraction Progress */}
        {extractionStatus !== 'idle' && (
          <div className="mt-4">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">
                {extractionStatus === 'extracting' ? 'Extracting website content...' :
                 extractionStatus === 'processing' ? 'Processing extracted content...' :
                 extractionStatus === 'success' ? 'Extraction Complete' :
                 'Extraction Failed'}
              </span>
              <span className="text-sm font-medium text-gray-700">{extractionProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  extractionStatus === 'error' ? 'bg-red-600' :
                  extractionStatus === 'success' ? 'bg-green-600' : 'bg-green-600'
                }`}
                style={{ width: `${extractionProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Extracted Chunks Preview (shown after successful extraction) */}
        {extractionStatus === 'success' && extractedChunks.length > 0 && (
          <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Extracted Content Preview:</h3>
            <div className="max-h-40 overflow-y-auto text-sm text-gray-600">
              {extractedChunks.slice(0, 3).map((chunk, index) => (
                <div key={index} className="mb-2 p-2 bg-white rounded border border-gray-200">
                  <p className="text-xs text-gray-500 mb-1">
                    {chunk.source_type === 'website' ?
                      `URL: ${chunk.url || chunk.source || 'Unknown'}` :
                      `Source: ${chunk.source || 'Unknown'}`}
                  </p>
                  <p>{chunk.text?.substring(0, 150)}...</p>
                </div>
              ))}
              {extractedChunks.length > 3 && (
                <p className="text-xs text-gray-500 text-center mt-2">
                  + {extractedChunks.length - 3} more chunks not shown
                </p>
              )}
            </div>
          </div>
        )}
      </form>

      {/* New Category Modal */}
      {showNewCategoryInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">Add New Domain Category</h3>
            <input
              type="text"
              value={newCategoryInput}
              onChange={(e) => setNewCategoryInput(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
              placeholder="Enter Domain Category name"
              autoFocus
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowNewCategoryInput(false)}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
              >
                Cancel
              </button>
              <button
                onClick={handleNewCategorySubmit}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Add Category
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WebsiteExtractForm;
