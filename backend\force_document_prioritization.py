"""
Force document prioritization in the IR App. 
This script makes critical changes to ensure answers come from documents and websites,
not from the LLM model (Gemini AI) unless no relevant documents are found.
"""
import re

def fix_similarity_evaluation():
    """Update similarity evaluation to consider more document matches as relevant."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 1. Lower the relevance threshold even further
        if "RELEVANCE_THRESHOLD" in content:
            content = re.sub(
                r'RELEVANCE_THRESHOLD\s*=\s*[0-9.]+', 
                'RELEVANCE_THRESHOLD = 0.05',  # Much lower threshold to include more documents
                content
            )
            print("Lowered RELEVANCE_THRESHOLD to 0.05")
        
        # 2. Make document filtering much more permissive
        if "(chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * " in content:
            content = content.replace(
                "(chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.5))",
                "(chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.1))"  # Much lower for documents
            )
            print("Made document filtering more permissive")
        
        # 3. Completely override the has_sufficient_document_answers function
        if "def has_sufficient_document_answers(" in content:
            new_evaluation = """def has_sufficient_document_answers(document_chunks):
    """
    ALWAYS consider document chunks as sufficient, even a single one.
    This ensures we always prioritize document content over AI-generated answers.
    """
    # Always return True if there are ANY document chunks
    if document_chunks and len(document_chunks) > 0:
        logger.info(f"Found {len(document_chunks)} document chunks. Using ONLY document content.")
        return True
    
    logger.warning("No document chunks found for query.")
    return False"""
            
            # Find the function
            func_start = content.find("def has_sufficient_document_answers(")
            if func_start != -1:
                # Find the end of function
                next_def = content.find("\ndef ", func_start + 10)
                if next_def != -1:
                    # Replace the function
                    content = content[:func_start] + new_evaluation + content[next_def:]
                    print("Completely overrode document evaluation function")
        
        # 4. Boost document similarity scores even higher
        if "if source_type == \"document\":" in content:
            # Find the if block for documents
            doc_boost_pos = content.find("if source_type == \"document\":")
            if doc_boost_pos != -1:
                # Find where similarity is set
                sim_line_pos = content.find("chunk[\"similarity\"] = ", doc_boost_pos)
                if sim_line_pos != -1:
                    # Find the end of that line
                    line_end_pos = content.find("\n", sim_line_pos)
                    if line_end_pos != -1:
                        # Replace with extreme boost
                        content = content[:sim_line_pos] + "chunk[\"similarity\"] = 0.99  # Force extreme document priority" + content[line_end_pos:]
                        print("Applied extreme boost to document similarity scores")
        
        # 5. Force document prioritization in generate_llm_answer
        if "def generate_llm_answer(" in content:
            # Find function
            generate_pos = content.find("def generate_llm_answer(")
            if generate_pos != -1:
                # Find where document_sources are checked
                doc_check_pos = content.find("if document_sources", generate_pos)
                if doc_check_pos != -1:
                    # Add strong document prioritization before this check
                    doc_prioritization = """    # FORCE document prioritization
    if document_chunks:
        logger.info("FORCING document prioritization")
        # Keep only document chunks if any exist
        context = [chunk for chunk in context if chunk.get("source_type") == "document"]
        
    """
                    
                    # Find a good insertion point
                    insert_pos = content.find("# Prepare context items for formatting", generate_pos)
                    if insert_pos != -1:
                        content = content[:insert_pos] + doc_prioritization + content[insert_pos:]
                        print("Added forced document prioritization")
        
        # Write the changes back
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error in fix_similarity_evaluation: {str(e)}")
        return False

def fix_llm_router():
    """Update LLM router to strictly use document content."""
    try:
        with open("llm_router.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 1. Modify generate_answer function to force document prioritization
        if "def generate_answer(" in content:
            # Find the function
            generate_pos = content.find("def generate_answer(")
            if generate_pos != -1:
                # Find context processing section
                context_proc_pos = content.find("# Format context for prompt", generate_pos)
                if context_proc_pos != -1:
                    # Replace context processing with forced document prioritization
                    new_context_processing = """    # Format context for prompt
    context_items = []
    document_items = []
    website_items = []
    other_items = []
    
    # First separate items by source type
    for i, context_item in enumerate(context):
        source_type = context_item.get("source_type", "unknown")
        
        if source_type == "document":
            # Force high similarity for documents
            context_item["similarity"] = 0.99
            document_items.append(context_item)
        elif source_type == "website":
            website_items.append(context_item)
        else:
            other_items.append(context_item)
    
    # CRITICAL: If ANY document sources exist, ONLY use document sources
    # Completely ignore website sources or LLM knowledge if documents exist
    if document_items:
        logger.info(f"FORCING document-only mode ({len(document_items)} document chunks)")
        context = document_items
    elif website_items:
        logger.info(f"Using website sources ({len(website_items)} website chunks)")
        context = website_items
    else:
        logger.info(f"No document or website sources, using general context ({len(other_items)} chunks)")
        context = other_items"""
                    
                    # Find where to insert this
                    old_context_start = content.find("# Format context for prompt", generate_pos)
                    old_context_end = content.find("for i, context_item in enumerate(context):", old_context_start)
                    
                    if old_context_start != -1 and old_context_end != -1:
                        # Replace old context processing
                        content = content[:old_context_start] + new_context_processing + "\n\n    # Process the selected context\n    " + content[old_context_end:]
                        print("Replaced context processing with forced document prioritization")
        
        # 2. Enhance the system prompt to strongly enforce document usage
        if "messages = [" in content:
            # Find messages section
            messages_pos = content.find("messages = [")
            if messages_pos != -1:
                # Find system message
                system_msg_pos = content.find('{"role": "system",', messages_pos)
                if system_msg_pos != -1:
                    # Find user message
                    user_msg_pos = content.find('{"role": "user",', system_msg_pos)
                    if user_msg_pos != -1:
                        # Create a new enhanced user message
                        new_user_msg = """        {"role": "user", "content": f'''
IMPORTANT INSTRUCTION:
Answer this question using ONLY the information provided in the context below.
If the context contains document sources, you MUST use ONLY that information and nothing else.
Do NOT use any knowledge outside what is specifically provided in the context below.

Question: {query}

Context:
{formatted_query}

Remember, ONLY use information from the provided context. If you don't find relevant information 
in the context, say "I don't have specific information about this in my documents."
'''}"""
                        
                        # Replace the old user message
                        line_end = content.find('\n', user_msg_pos)
                        next_line = content.find('\n', line_end+1)
                        if line_end != -1:
                            content = content[:user_msg_pos] + new_user_msg + content[next_line:]
                            print("Enhanced user prompt to enforce document usage")
        
        # Write changes back
        with open("llm_router.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error in fix_llm_router: {str(e)}")
        return False

def fix_context_formatting():
    """Improve context formatting to make document sources more obvious to LLM."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        # Find the function
        context_format_pos = content.find("def format_context_for_prompt(")
        if context_format_pos != -1:
            # Find where context is formatted
            format_start = content.find("formatted_context = \"\"", context_format_pos)
            if format_start != -1:
                # Replace formatting logic with improved version
                format_block = """    formatted_context = ""
    
    # Always prioritize document sources
    doc_sources = [item for item in context_items if item.get("source_type") == "document"]
    web_sources = [item for item in context_items if item.get("source_type") == "website"]
    other_sources = [item for item in context_items if item.get("source_type") not in ["document", "website"]]
    
    # Format document sources first and with special highlighting
    if doc_sources:
        formatted_context += "\\n\\n### DOCUMENT SOURCES (PRIORITIZE THESE):\\n\\n"
        for item in doc_sources:
            source_info = item.get("source_info", "")
            text = item.get("text", "")
            
            # Add special markers to make documents stand out
            formatted_context += f"[DOCUMENT] {source_info}\\n{text}\\n\\n"
    
    # Format website sources second
    if web_sources and not doc_sources:  # Only include websites if no documents
        formatted_context += "\\n\\n### WEBSITE SOURCES:\\n\\n"
        for item in web_sources:
            source_info = item.get("source_info", "")
            text = item.get("text", "")
            
            formatted_context += f"[WEBSITE] {source_info}\\n{text}\\n\\n"
    
    # Format other sources last, only if no documents or websites
    if other_sources and not doc_sources and not web_sources:
        formatted_context += "\\n\\n### OTHER SOURCES:\\n\\n"
        for item in other_sources:
            source_info = item.get("source_info", "")
            text = item.get("text", "")
            
            formatted_context += f"{source_info}\\n{text}\\n\\n"
    
    return formatted_context.strip()"""
                
                # Find where to replace
                format_end = content.find("return formatted_context", format_start)
                format_end = content.rfind("}", format_end) + 1
                
                if format_end != -1:
                    # Replace the formatting logic
                    content = content[:format_start] + format_block + content[format_end:]
                    print("Improved context formatting to highlight document sources")
        
        # Write changes back
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error in fix_context_formatting: {str(e)}")
        return False

def fix_hybid_search():
    """Fix search logic in vector_db.py to include more document results."""
    try:
        with open("vector_db.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 1. Reduce similarity threshold in hybrid_search
        if "def hybrid_search(" in content:
            content = re.sub(
                r'def hybrid_search\(self, query_text: str, query_embedding: List\[float\], match_threshold: float = [0-9.]+',
                'def hybrid_search(self, query_text: str, query_embedding: List[float], match_threshold: float = 0.05',
                content
            )
            print("Lowered hybrid search threshold to 0.05")
        
        # 2. Enhance results combination logic to force include documents
        if "# Combine and deduplicate results" in content:
            combined_logic_pos = content.find("# Combine and deduplicate results")
            if combined_logic_pos != -1:
                # Find the return statement
                return_pos = content.find("return combined_results", combined_logic_pos)
                if return_pos != -1:
                    # Insert document forcing logic before return
                    force_docs = """
        # CRITICAL: Force include at least one document result if available
        # This ensures documents are always considered even if similarity is low
        has_doc = any(r.get("source_type") == "document" for r in combined_results[:match_count])
        
        if not has_doc:
            # Find any document in all results
            doc_results = [r for r in semantic_results + keyword_results 
                         if r.get("source_type") == "document"]
            
            if doc_results:
                # Add document result at the top
                # Sort by highest similarity first
                doc_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                # Force high similarity score
                doc_results[0]["similarity"] = 0.99
                # Insert at beginning
                combined_results.insert(0, doc_results[0])
                logger.info("Forced document inclusion in search results")"""
                
                    # Insert before return statement
                    content = content[:return_pos] + force_docs + "\n        " + content[return_pos:]
                    print("Added forced document inclusion in search results")
        
        # Write changes back
        with open("vector_db.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error in fix_hybid_search: {str(e)}")
        return False

def fix_process_query():
    """Enhance process_query to always prioritize documents and websites over AI."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the process_query function
        query_pos = content.find("def process_query(")
        if query_pos != -1:
            # Find document and website source handling
            sources_pos = content.find("# Prioritize document and website sources", query_pos)
            if sources_pos != -1:
                # Force document and website prioritization
                source_priority = """        # Prioritize document and website sources
        document_chunks = []
        website_chunks = []
        other_chunks = []
        
        # Sort chunks by source type with higher priority to documents
        for chunk in similar_chunks:
            source_type = chunk.get("source_type", "unknown")
            
            # Give documents EXTREME priority
            if source_type == "document":
                # Force near-perfect similarity for documents
                chunk["similarity"] = 0.99
                document_chunks.append(chunk)
            elif source_type == "website":
                # Boost websites too, but less than documents
                chunk["similarity"] = min(0.9, chunk["similarity"] * 1.5)
                website_chunks.append(chunk)
            else:
                other_chunks.append(chunk)
        
        # CRITICAL FIX: If ANY documents are found, ONLY use those
        if document_chunks:
            logger.info(f"FORCING document-only mode ({len(document_chunks)} document chunks)")
            similar_chunks = document_chunks
            # Clear other chunks to force document usage
            website_chunks = []
            other_chunks = []
        elif website_chunks:
            logger.info(f"Using website sources ({len(website_chunks)} website chunks)")
            similar_chunks = website_chunks
            # Clear other chunks to force website usage
            other_chunks = []"""
                
                # Replace source prioritization
                end_priority = content.find("document_sources =", sources_pos)
                if end_priority != -1:
                    content = content[:sources_pos] + source_priority + "\n\n        # Prepare source information\n        " + content[end_priority:]
                    print("Enhanced source prioritization in process_query")
            
            # Add fallback override to FORCE use of documents
            fallback_pos = content.find("fallback_to_ai =", query_pos)
            if fallback_pos != -1:
                # Find the end of that line
                line_end = content.find("\n", fallback_pos)
                if line_end != -1:
                    # Replace with forced document priority
                    content = content[:fallback_pos] + "fallback_to_ai = False if document_chunks else (False if website_chunks else request.fallback_enabled)" + content[line_end:]
                    print("Forced document and website priority over AI fallback")
        
        # Write changes back
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error in fix_process_query: {str(e)}")
        return False

def main():
    print("\n=== FORCING DOCUMENT PRIORITIZATION ===\n")
    
    # 1. Fix similarity evaluation for documents
    print("Fixing document similarity evaluation...")
    if fix_similarity_evaluation():
        print("+ Successfully updated document similarity evaluation")
    else:
        print("- Failed to update document similarity evaluation")
    
    # 2. Fix LLM router to strictly use document content
    print("\nEnhancing LLM router to enforce document usage...")
    if fix_llm_router():
        print("+ Successfully enhanced LLM router")
    else:
        print("- Failed to enhance LLM router")
    
    # 3. Improve context formatting
    print("\nImproving context formatting...")
    if fix_context_formatting():
        print("+ Successfully improved context formatting")
    else:
        print("- Failed to improve context formatting")
    
    # 4. Fix hybrid search to include more document results
    print("\nFixing hybrid search...")
    if fix_hybid_search():
        print("+ Successfully enhanced hybrid search")
    else:
        print("- Failed to enhance hybrid search")
    
    # 5. Fix process_query to always prioritize documents
    print("\nEnhancing query processing...")
    if fix_process_query():
        print("+ Successfully enhanced query processing")
    else:
        print("- Failed to enhance query processing")
    
    print("\n=== DOCUMENT PRIORITIZATION ENFORCED ===")
    print("\nThe system has been modified to FORCE answers from documents and websites.")
    print("Now restart your server to apply these changes:")
    print("python -m uvicorn server:app --reload")

if __name__ == "__main__":
    main()
