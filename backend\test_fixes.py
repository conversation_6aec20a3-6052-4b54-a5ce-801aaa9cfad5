"""
Test script for RailGPT document and website extraction fixes.

This script:
1. Tests website extraction and storage
2. Tests document extraction and storage
3. Tests the query endpoint with different scenarios

Usage:
    python test_fixes.py
"""

import os
import sys
import logging
import json
import time
import requests
from typing import List, Dict, Any
from urllib.parse import urlparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from supabase_client import supabase
from website_scraper import extract_website_text
from vector_db import vector_db

# API endpoint
API_ENDPOINT = "http://localhost:8000"

def test_website_extraction():
    """Test website extraction and storage."""
    logger.info("Testing website extraction and storage...")
    
    # Test URL
    url = "https://rapidresponseapp.com"
    
    try:
        # Extract website text
        website_chunks = extract_website_text(url)
        
        if not website_chunks:
            logger.error("Failed to extract website text")
            return False
        
        logger.info(f"Successfully extracted {len(website_chunks)} chunks from {url}")
        
        # Store website in Supabase
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        
        website_data = supabase.store_website(
            url=url,
            domain=domain,
            title=f"{domain} Website",
            description=f"Content extracted from {url}",
            submitted_by="test_user"
        )
        
        if "error" in website_data:
            logger.error(f"Error storing website metadata: {website_data['error']}")
            return False
        
        website_id = website_data.get("id")
        if not website_id:
            logger.error("No website ID returned from Supabase")
            return False
        
        logger.info(f"Successfully stored website with ID: {website_id}")
        
        # Add website chunks to Supabase
        for i, chunk in enumerate(website_chunks):
            # Add website_id to each chunk
            chunk["website_id"] = website_id
            chunk["chunk_index"] = i
            
            # Add source_type if not present
            if "source_type" not in chunk:
                chunk["source_type"] = "website"
            
            # Add embedding
            if "embedding" not in chunk:
                # Use a mock embedding for testing
                import numpy as np
                np.random.seed(i)  # For reproducibility
                chunk["embedding"] = list(np.random.rand(768))
        
        # Add chunks to vector database
        vector_db.add_chunks(website_chunks, source_type="website")
        
        logger.info(f"Successfully added {len(website_chunks)} website chunks to vector database")
        
        # Verify chunks were added
        count_query = "SELECT COUNT(*) as count FROM website_chunks WHERE website_id = $1"
        count_result = supabase.execute_query(count_query, {"params": [website_id]})
        
        if "error" in count_result:
            logger.error(f"Error checking website chunks: {count_result['error']}")
            return False
        
        count = count_result[0]["count"] if count_result and "count" in count_result[0] else 0
        logger.info(f"Found {count} website chunks in Supabase")
        
        return count > 0
    
    except Exception as e:
        logger.error(f"Error testing website extraction: {str(e)}")
        return False

def test_query_endpoint():
    """Test the query endpoint with different scenarios."""
    logger.info("Testing query endpoint...")
    
    # Test queries
    queries = [
        "What is FSDS Isolation Monitoring System?",  # Should find document chunks
        "What services does rapidresponseapp.com offer?",  # Should find website chunks
        "What is the capital of India?"  # Should use Gemini fallback
    ]
    
    for query in queries:
        try:
            # Send query to API
            response = requests.post(
                f"{API_ENDPOINT}/api/query",
                json={
                    "query": query,
                    "model": "gemini-2.0-flash",
                    "extract_format": "paragraph",
                    "use_hybrid_search": True,
                    "retry_on_timeout": True,
                    "search_with_ai": True
                }
            )
            
            if response.status_code != 200:
                logger.error(f"Error querying API: {response.text}")
                continue
            
            result = response.json()
            
            # Check if we got an answer
            if "answer" not in result or not result["answer"]:
                logger.error(f"No answer returned for query: {query}")
                continue
            
            logger.info(f"Query: {query}")
            logger.info(f"Answer: {result['answer'][:100]}...")
            
            # Check if we got sources
            sources = result.get("sources", [])
            logger.info(f"Found {len(sources)} sources")
            
            # Check if we used fallback
            llm_fallback = result.get("llm_fallback", False)
            logger.info(f"Used LLM fallback: {llm_fallback}")
            
        except Exception as e:
            logger.error(f"Error testing query endpoint: {str(e)}")
    
    return True

def main():
    """Main function to run all tests."""
    logger.info("Starting tests...")
    
    # Test website extraction
    if test_website_extraction():
        logger.info("Website extraction test passed")
    else:
        logger.error("Website extraction test failed")
    
    # Test query endpoint
    if test_query_endpoint():
        logger.info("Query endpoint test passed")
    else:
        logger.error("Query endpoint test failed")
    
    logger.info("Tests completed")

if __name__ == "__main__":
    main()
