"""
Script to test cosine similarity calculation between embeddings.
"""
import os
import logging
import json
import numpy as np
from typing import List, Dict, Any
from dotenv import load_dotenv
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def cosine_similarity(embedding1, embedding2):
    """
    Calculate cosine similarity between two embeddings, handling string conversions.
    """
    # Convert embeddings to numpy arrays if they are not already
    try:
        # Handle string embeddings (from JSON)
        if isinstance(embedding1, str):
            try:
                embedding1 = json.loads(embedding1)
            except:
                logger.error("Failed to parse string embedding1")
                return 0.0

        if isinstance(embedding2, str):
            try:
                embedding2 = json.loads(embedding2)
            except:
                logger.error("Failed to parse string embedding2")
                return 0.0

        # Ensure embeddings are numpy arrays of float32
        embedding1 = np.array(embedding1, dtype=np.float32)
        embedding2 = np.array(embedding2, dtype=np.float32)

        # Compute cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)
    except Exception as e:
        logger.error(f"Error calculating cosine similarity: {str(e)}")
        return 0.0  # Return 0 similarity on error

def generate_embedding(text: str) -> List[float]:
    """Generate embedding for text using the LLM router."""
    try:
        return llm_router.generate_embedding(text)
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Try with default model
        try:
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Use a deterministic random embedding as last resort
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def test_cosine_similarity():
    """Test cosine similarity calculation between embeddings."""
    logger.info("Testing cosine similarity calculation between embeddings...")
    
    # Test queries
    test_queries = [
        "What is the Rapid Response app?",
        "What is VASP and who developed it?",
        "Tell me about the Rapid Response app for BD & DM"
    ]
    
    # Test documents
    test_documents = [
        "The Rapid Response app is a mobile application developed by VASP Group for Indian Railways.",
        "VASP is a software development company that creates solutions for the transportation industry.",
        "The Rapid Response App for BD & DM is a cutting-edge mobile application developed by VASP Group."
    ]
    
    # Generate embeddings for queries
    query_embeddings = []
    for query in test_queries:
        embedding = generate_embedding(query)
        query_embeddings.append(embedding)
    
    # Generate embeddings for documents
    document_embeddings = []
    for document in test_documents:
        embedding = generate_embedding(document)
        document_embeddings.append(embedding)
    
    # Calculate cosine similarity between queries and documents
    for i, query in enumerate(test_queries):
        logger.info(f"\n=== Testing query: '{query}' ===\n")
        
        for j, document in enumerate(test_documents):
            similarity = cosine_similarity(query_embeddings[i], document_embeddings[j])
            logger.info(f"Similarity with document {j+1}: {similarity:.4f}")
            logger.info(f"Document: {document}")
    
    # Test with embeddings from the database
    logger.info("\n=== Testing with embeddings from the database ===\n")
    
    # Sample embeddings from the database
    db_embeddings = [
        [0.37454012,0.9507143,0.7319939,0.5986585,0.15601864,0.15599452,0.058083612,0.8661761,0.601115,0.7080726],
        [0.049603347,-0.0063479478,-0.042130988,-0.005943678,0.031151915,0.028371345,-0.04152952,-0.034003902,-0.0036182373,0.04290454],
        [0.062164765,-0.012099094,-0.039135743,-0.009377313,0.01459367,0.028962469,-0.03642202,-0.04877552,0.0015278046,0.043327566]
    ]
    
    # Calculate cosine similarity between query embeddings and database embeddings
    for i, query in enumerate(test_queries):
        logger.info(f"\n=== Testing query: '{query}' with database embeddings ===\n")
        
        for j, db_embedding in enumerate(db_embeddings):
            # Use only the first 10 dimensions for simplicity
            query_embedding_short = query_embeddings[i][:10]
            similarity = cosine_similarity(query_embedding_short, db_embedding)
            logger.info(f"Similarity with database embedding {j+1}: {similarity:.4f}")

def main():
    """Main function to test cosine similarity calculation."""
    test_cosine_similarity()

if __name__ == "__main__":
    main()
