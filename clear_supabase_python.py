#!/usr/bin/env python3
"""
Clear Supabase Database for RailGPT Testing
===========================================
This script clears all uploaded documents, websites, chunks, and queries
so you can start fresh for testing the new answer display functionality.
"""

import os
import sys
from dotenv import load_dotenv

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from supabase_client import SupabaseClient
except ImportError:
    print("❌ Error: Could not import SupabaseClient. Make sure you're in the correct directory.")
    sys.exit(1)

def clear_supabase_data():
    """Clear all data from Supabase tables"""
    
    # Load environment variables
    load_dotenv()
    
    # Initialize Supabase client
    try:
        client = SupabaseClient()
        print("✅ Connected to Supabase")
    except Exception as e:
        print(f"❌ Error connecting to Supabase: {e}")
        return False
    
    # Clear data in the correct order (respecting foreign key constraints)
    tables_to_clear = [
        "document_chunks",
        "website_chunks", 
        "documents",
        "websites",
        "queries"
    ]
    
    for table in tables_to_clear:
        try:
            # Clear the table
            query = f"TRUNCATE {table} RESTART IDENTITY CASCADE;"
            result = client.execute_query(query)
            print(f"✅ Cleared {table} table")
            
        except Exception as e:
            print(f"❌ Error clearing {table}: {e}")
            return False
    
    # Verify data has been cleared
    print("\n📊 Verification - All counts should be 0:")
    verification_queries = {
        "document_chunks": "SELECT COUNT(*) as count FROM document_chunks;",
        "website_chunks": "SELECT COUNT(*) as count FROM website_chunks;", 
        "documents": "SELECT COUNT(*) as count FROM documents;",
        "websites": "SELECT COUNT(*) as count FROM websites;",
        "queries": "SELECT COUNT(*) as count FROM queries;"
    }
    
    for table_name, query in verification_queries.items():
        try:
            result = client.execute_query(query)
            count = result[0]['count'] if result else 0
            print(f"   {table_name}: {count} records")
        except Exception as e:
            print(f"❌ Error checking {table_name}: {e}")
    
    print("\n🎉 Database cleared successfully! You can now upload fresh documents and websites.")
    return True

if __name__ == "__main__":
    print("🧹 Clearing Supabase database for RailGPT testing...")
    print("This will remove all uploaded documents, websites, and query history.")
    
    # Ask for confirmation
    confirm = input("\n⚠️  Are you sure you want to proceed? (yes/no): ").lower().strip()
    
    if confirm in ['yes', 'y']:
        success = clear_supabase_data()
        if success:
            print("\n✅ All done! Your database is now clean and ready for testing.")
        else:
            print("\n❌ Some errors occurred during the clearing process.")
    else:
        print("Operation cancelled.") 