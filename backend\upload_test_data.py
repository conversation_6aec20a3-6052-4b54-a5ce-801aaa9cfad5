"""
<PERSON><PERSON><PERSON> to upload test data to the RailGPT database.
This script will:
1. Create sample documents with content related to the test queries
2. Create sample websites with content related to the test queries
3. Upload them to the database with proper embeddings
"""
import os
import logging
import json
import uuid
from typing import List, Dict, Any
import numpy as np
from dotenv import load_dotenv
from supabase_client import supabase
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Sample document content
SAMPLE_DOCUMENTS = [
    {
        "title": "Railway Abbreviations Guide",
        "content": """
        # Railway Abbreviations Guide
        
        ## Common Railway Abbreviations
        
        ### A
        - ACP: Alarm Chain Pulling - A safety feature in trains that allows passengers to stop the train in case of emergency.
        - ADMO: Assistant Divisional Medical Officer
        - AME: Assistant Mechanical Engineer
        
        ### F
        - FSDS: Fire Smoke Detection System - A system installed in trains to detect fire and smoke for passenger safety.
        - FOIS: Freight Operations Information System
        - FCI: Food Corporation of India
        
        ### R
        - RDSO: Research Design and Standards Organization
        - RMS: Railway Mail Service
        - Rapid Response App: A mobile application developed by Indian Railways for quick emergency response and assistance.
        
        ### V
        - VASP: Vehicle Access Security Platform - A security system developed by CRIS (Centre for Railway Information Systems) for controlling and monitoring vehicle access at railway premises.
        - VPU: Parcel Van
        - VHF: Very High Frequency
        """
    },
    {
        "title": "Indian Railways Technology Systems",
        "content": """
        # Indian Railways Technology Systems
        
        ## Mobile Applications
        
        ### Rapid Response App
        The Rapid Response app is a mobile application developed by Indian Railways to provide quick emergency response and assistance to passengers. Key features include:
        
        - Emergency SOS alerts
        - Real-time location tracking
        - Direct connection to railway control rooms
        - Medical emergency reporting
        - Security incident reporting
        
        The app was launched in 2022 and has been instrumental in reducing response time to passenger emergencies.
        
        ## Security Systems
        
        ### VASP (Vehicle Access Security Platform)
        VASP is an advanced security system developed by CRIS (Centre for Railway Information Systems) for Indian Railways. It provides:
        
        - Automated vehicle access control at railway premises
        - RFID-based identification and authentication
        - Real-time monitoring of vehicle movement
        - Integration with central security database
        - Alerts for unauthorized access attempts
        
        The system has been implemented at major railway stations and yards across India.
        """
    }
]

# Sample website content
SAMPLE_WEBSITES = [
    {
        "url": "https://indianrailways.gov.in/abbreviations",
        "title": "Indian Railways - Abbreviations and Terms",
        "content": """
        # Indian Railways Abbreviations and Terms
        
        ## Railway Terminology
        
        ### Common Abbreviations
        
        ACP (Alarm Chain Pulling): A safety mechanism that allows passengers to stop the train in case of an emergency by pulling a chain or pressing a button.
        
        FSDS (Fire Smoke Detection System): An advanced safety system installed in modern trains that automatically detects smoke or fire and triggers alarms and suppression systems.
        
        ### Safety Systems
        
        The Indian Railways has implemented several safety systems including the FSDS which has significantly reduced fire-related incidents in trains.
        """
    },
    {
        "url": "https://indianrailways.gov.in/technology/apps",
        "title": "Indian Railways - Technology Applications",
        "content": """
        # Indian Railways Technology Applications
        
        ## Mobile Apps for Passengers
        
        ### Rapid Response App
        
        The Rapid Response app is Indian Railways' emergency response application designed to provide immediate assistance to passengers in distress. The app allows passengers to:
        
        - Send emergency alerts with GPS location
        - Contact nearest railway station
        - Report security incidents
        - Request medical assistance
        - Track response status
        
        The app has been downloaded over 2 million times and has helped in numerous emergency situations.
        
        ## Security Infrastructure
        
        ### VASP System
        
        The Vehicle Access Security Platform (VASP) was developed by the Centre for Railway Information Systems (CRIS) in 2019. This system:
        
        - Controls vehicle entry and exit at railway premises
        - Uses RFID technology for authentication
        - Maintains digital records of all vehicle movements
        - Integrates with CCTV surveillance
        - Provides real-time alerts for security breaches
        
        VASP has been implemented at over 100 major railway stations across India.
        """
    }
]

def generate_embedding(text: str) -> List[float]:
    """Generate embedding for text using the LLM router."""
    try:
        return llm_router.generate_embedding(text)
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Try with default model
        try:
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Use a deterministic random embedding as last resort
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def chunk_text(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
    """Split text into overlapping chunks."""
    chunks = []
    if len(text) <= chunk_size:
        chunks.append(text)
    else:
        start = 0
        while start < len(text):
            end = min(start + chunk_size, len(text))
            # If we're not at the end, try to find a good break point
            if end < len(text):
                # Try to find a newline or period to break at
                break_point = text.rfind('\n', start, end)
                if break_point == -1 or break_point < start + chunk_size // 2:
                    break_point = text.rfind('. ', start, end)
                if break_point != -1 and break_point > start + chunk_size // 2:
                    end = break_point + 1
            chunks.append(text[start:end])
            start = end - overlap
    return chunks

def upload_documents():
    """Upload sample documents to the database."""
    logger.info("Uploading sample documents to the database...")
    
    for doc in SAMPLE_DOCUMENTS:
        try:
            # Create document record
            doc_id = str(uuid.uuid4())
            doc_name = doc["title"] + ".pdf"  # Simulate PDF file
            
            # Insert document record
            doc_query = f"""
            INSERT INTO documents (id, display_name, file_path, file_type, file_size, created_at, updated_at)
            VALUES (
                '{doc_id}',
                '{doc_name}',
                'pdf/{doc_name}',
                'application/pdf',
                {len(doc["content"])},
                NOW(),
                NOW()
            )
            RETURNING id
            """
            
            doc_result = supabase.execute_query(doc_query)
            
            if isinstance(doc_result, dict) and "error" in doc_result:
                logger.error(f"Error creating document: {doc_result['error']}")
                continue
            
            logger.info(f"Created document: {doc_name} with ID: {doc_id}")
            
            # Chunk the document content
            chunks = chunk_text(doc["content"])
            
            # Create chunks with embeddings
            for i, chunk_text in enumerate(chunks):
                try:
                    # Generate embedding
                    embedding = generate_embedding(chunk_text)
                    
                    # Create chunk record
                    chunk_id = str(uuid.uuid4())
                    chunk_query = f"""
                    INSERT INTO document_chunks (
                        id, document_id, chunk_index, page_number, text, embedding, metadata, created_at, updated_at
                    )
                    VALUES (
                        '{chunk_id}',
                        '{doc_id}',
                        {i},
                        1,
                        '{chunk_text.replace("'", "''")}',
                        '{json.dumps(embedding)}'::vector,
                        '{{"page": 1, "source": "upload_test_data.py"}}'::jsonb,
                        NOW(),
                        NOW()
                    )
                    """
                    
                    chunk_result = supabase.execute_query(chunk_query)
                    
                    if isinstance(chunk_result, dict) and "error" in chunk_result:
                        logger.error(f"Error creating chunk: {chunk_result['error']}")
                    else:
                        logger.info(f"Created chunk {i+1}/{len(chunks)} for document {doc_name}")
                
                except Exception as e:
                    logger.error(f"Error processing chunk {i}: {str(e)}")
        
        except Exception as e:
            logger.error(f"Error uploading document {doc['title']}: {str(e)}")

def upload_websites():
    """Upload sample websites to the database."""
    logger.info("Uploading sample websites to the database...")
    
    for website in SAMPLE_WEBSITES:
        try:
            # Create website record
            website_id = str(uuid.uuid4())
            
            # Insert website record
            web_query = f"""
            INSERT INTO websites (id, url, title, last_crawled_at, created_at, updated_at)
            VALUES (
                '{website_id}',
                '{website["url"]}',
                '{website["title"].replace("'", "''")}',
                NOW(),
                NOW(),
                NOW()
            )
            RETURNING id
            """
            
            web_result = supabase.execute_query(web_query)
            
            if isinstance(web_result, dict) and "error" in web_result:
                logger.error(f"Error creating website: {web_result['error']}")
                continue
            
            logger.info(f"Created website: {website['url']} with ID: {website_id}")
            
            # Chunk the website content
            chunks = chunk_text(website["content"])
            
            # Create chunks with embeddings
            for i, chunk_text in enumerate(chunks):
                try:
                    # Generate embedding
                    embedding = generate_embedding(chunk_text)
                    
                    # Create chunk record
                    chunk_id = str(uuid.uuid4())
                    chunk_query = f"""
                    INSERT INTO website_chunks (
                        id, website_id, url, text, embedding, metadata, created_at, updated_at
                    )
                    VALUES (
                        '{chunk_id}',
                        '{website_id}',
                        '{website["url"]}',
                        '{chunk_text.replace("'", "''")}',
                        '{json.dumps(embedding)}'::vector,
                        '{{"source": "upload_test_data.py"}}'::jsonb,
                        NOW(),
                        NOW()
                    )
                    """
                    
                    chunk_result = supabase.execute_query(chunk_query)
                    
                    if isinstance(chunk_result, dict) and "error" in chunk_result:
                        logger.error(f"Error creating chunk: {chunk_result['error']}")
                    else:
                        logger.info(f"Created chunk {i+1}/{len(chunks)} for website {website['url']}")
                
                except Exception as e:
                    logger.error(f"Error processing chunk {i}: {str(e)}")
        
        except Exception as e:
            logger.error(f"Error uploading website {website['url']}: {str(e)}")

def main():
    """Main function to upload test data."""
    # Upload documents
    upload_documents()
    
    # Upload websites
    upload_websites()
    
    # Check database content
    logger.info("Checking database content after upload...")
    
    # Helper function to extract count from result
    def extract_count(result):
        if not result or not isinstance(result, list) or len(result) == 0:
            return 0
        
        if "count" in result[0]:
            count_val = result[0]["count"]
        elif "COUNT" in result[0]:
            count_val = result[0]["COUNT"]
        else:
            # Try to get the first value regardless of key
            count_val = list(result[0].values())[0] if result[0] else 0
            
        # Convert to int if it's a string
        if isinstance(count_val, str):
            try:
                return int(count_val)
            except ValueError:
                return 0
        return count_val
    
    # Check documents
    doc_query = "SELECT COUNT(*) FROM documents"
    doc_result = supabase.execute_query(doc_query)
    doc_count = extract_count(doc_result) if not isinstance(doc_result, dict) else 0
    logger.info(f"Documents in database: {doc_count}")
    
    # Check document chunks
    chunk_query = "SELECT COUNT(*) FROM document_chunks"
    chunk_result = supabase.execute_query(chunk_query)
    chunk_count = extract_count(chunk_result) if not isinstance(chunk_result, dict) else 0
    logger.info(f"Document chunks in database: {chunk_count}")
    
    # Check websites
    web_query = "SELECT COUNT(*) FROM websites"
    web_result = supabase.execute_query(web_query)
    web_count = extract_count(web_result) if not isinstance(web_result, dict) else 0
    logger.info(f"Websites in database: {web_count}")
    
    # Check website chunks
    web_chunk_query = "SELECT COUNT(*) FROM website_chunks"
    web_chunk_result = supabase.execute_query(web_chunk_query)
    web_chunk_count = extract_count(web_chunk_result) if not isinstance(web_chunk_result, dict) else 0
    logger.info(f"Website chunks in database: {web_chunk_count}")

if __name__ == "__main__":
    main()
