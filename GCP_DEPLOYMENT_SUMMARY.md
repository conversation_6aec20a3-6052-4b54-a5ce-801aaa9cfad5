# 🚀 RailGPT Google Cloud Platform Deployment - Complete Solution

## 📋 What's Included

I've created a comprehensive GCP deployment solution for your RailGPT application with:

### **🔧 Automated Deployment Scripts**
- **`deploy/gcp/setup-env.sh`** - Interactive environment configuration
- **`deploy/gcp/deploy-backend.sh`** - Backend deployment to Cloud Run
- **`deploy/gcp/deploy-frontend.sh`** - Frontend deployment to Cloud Storage + CDN
- **`deploy/gcp/setup-complete.sh`** - Complete one-command deployment

### **📁 Configuration Files**
- **`deploy/gcp/cloudbuild.yaml`** - CI/CD pipeline configuration
- **`deploy/gcp/README.md`** - Comprehensive deployment guide
- **`deploy/gcp/QUICK_START.md`** - Quick start guide

### **🏗️ Architecture**
```
Internet → Cloud Load Balancer → Cloud CDN → Cloud Storage (Frontend)
                ↓
         Cloud Run (Backend API)
                ↓
         Supabase (Database)
```

## 🎯 **Quick Start (Choose Your Path)**

### **Option 1: Super Quick (10 minutes)**
```bash
# 1. Setup environment
./deploy/gcp/setup-env.sh

# 2. Deploy everything
./deploy/gcp/setup-complete.sh
```

### **Option 2: Step by Step**
```bash
# 1. Configure environment
./deploy/gcp/setup-env.sh

# 2. Deploy backend
./deploy/gcp/deploy-backend.sh

# 3. Deploy frontend
./deploy/gcp/deploy-frontend.sh
```

## 🔑 **Prerequisites**

### **1. Install Google Cloud SDK**
```bash
# macOS
brew install google-cloud-sdk

# Windows - Download from:
# https://cloud.google.com/sdk/docs/install

# Linux
curl https://sdk.cloud.google.com | bash
```

### **2. Login to Google Cloud**
```bash
gcloud auth login
gcloud auth application-default login
```

### **3. Get API Keys**
- **Gemini API Key** (Required): [Get from Google AI Studio](https://makersuite.google.com/app/apikey)
- **OpenAI API Key** (Optional): [Get from OpenAI](https://platform.openai.com/api-keys)
- **Groq API Key** (Optional): [Get from Groq](https://console.groq.com/keys)

## 💰 **Cost Breakdown**

### **Free Tier (Development)**
- Cloud Run: 2M requests/month free
- Cloud Storage: 5GB free
- Cloud Build: 120 build-minutes/day free
- **Total: $0/month**

### **Production (Small Scale)**
- Cloud Run: $5-15/month
- Cloud Storage + CDN: $2-5/month
- Load Balancer: $18/month
- **Total: $25-40/month**

### **Production (Medium Scale)**
- Cloud Run: $15-50/month
- Cloud Storage + CDN: $5-15/month
- Load Balancer: $18/month
- **Total: $40-85/month**

## 🌟 **Features Included**

### **✅ Production-Ready Infrastructure**
- Auto-scaling Cloud Run backend
- Global CDN for frontend
- HTTPS load balancer
- SSL certificate management
- Health checks and monitoring

### **✅ Security & Performance**
- Cloud Armor DDoS protection
- IAM service accounts
- Secure environment variable management
- Optimized caching headers
- Rate limiting

### **✅ CI/CD Pipeline**
- Automated builds with Cloud Build
- GitHub integration
- Automatic deployment on push
- Health checks and rollback

### **✅ Monitoring & Alerts**
- Uptime monitoring
- Error tracking
- Performance metrics
- Cost monitoring
- Billing alerts

## 📊 **What Gets Deployed**

### **Backend (Cloud Run)**
- FastAPI application
- Auto-scaling (0-100 instances)
- 2GB RAM, 2 CPU cores
- Health checks enabled
- Environment variables configured

### **Frontend (Cloud Storage + CDN)**
- React application build
- Global CDN distribution
- Optimized caching
- HTTPS enabled
- Custom domain support

### **Load Balancer (Optional)**
- HTTPS termination
- SSL certificate management
- Traffic routing
- DDoS protection

## 🔧 **Management Commands**

### **View Status**
```bash
# Backend logs
gcloud logs read --service=railgpt-backend --limit=50

# Frontend status
gsutil ls -la gs://your-bucket-name/

# Overall project status
gcloud projects describe your-project-id
```

### **Update Application**
```bash
# Update everything
./deploy/gcp/setup-complete.sh

# Update backend only
./deploy/gcp/deploy-backend.sh

# Update frontend only
./deploy/gcp/deploy-frontend.sh
```

### **Scale Application**
```bash
# Scale backend
gcloud run services update railgpt-backend \
  --region=us-central1 \
  --min-instances=1 \
  --max-instances=100
```

## 🔍 **Troubleshooting**

### **Common Issues & Solutions**

#### **1. Permission Errors**
```bash
gcloud auth login
gcloud auth application-default login
```

#### **2. Billing Not Enabled**
- Go to [Google Cloud Console](https://console.cloud.google.com/billing)
- Enable billing for your project

#### **3. API Not Enabled**
```bash
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable storage.googleapis.com
```

#### **4. Domain/SSL Issues**
- Verify DNS configuration
- Wait up to 24 hours for SSL provisioning
- Check load balancer status

## 🚀 **Advanced Features**

### **CI/CD Setup**
```bash
# Create build trigger for automatic deployment
gcloud builds triggers create github \
  --repo-name=railgpt \
  --repo-owner=your-username \
  --branch-pattern="^main$" \
  --build-config=deploy/gcp/cloudbuild.yaml
```

### **Custom Domain**
```bash
# Deploy with custom domain
./deploy/gcp/setup-complete.sh --domain your-domain.com

# Configure DNS to point to load balancer IP
```

### **Monitoring Setup**
```bash
# Create uptime checks
gcloud alpha monitoring uptime create \
  --display-name="RailGPT Health Check" \
  --hostname=your-domain.com \
  --path=/health
```

## 📞 **Support & Resources**

### **Documentation**
- [Complete GCP Guide](deploy/gcp/README.md)
- [Quick Start Guide](deploy/gcp/QUICK_START.md)
- [Google Cloud Documentation](https://cloud.google.com/docs)

### **Useful Links**
- [Google Cloud Console](https://console.cloud.google.com)
- [Cloud Monitoring](https://console.cloud.google.com/monitoring)
- [Cloud Billing](https://console.cloud.google.com/billing)

### **Getting Help**
1. Check troubleshooting guides
2. Review application logs
3. Check Google Cloud status page
4. Contact support if needed

## 🎯 **Next Steps After Deployment**

1. **Test Application**
   - Upload documents
   - Add websites
   - Test chat functionality
   - Verify search results

2. **Configure Domain** (if applicable)
   - Point DNS to load balancer IP
   - Wait for SSL certificate
   - Test HTTPS access

3. **Setup Monitoring**
   - Configure alerts
   - Monitor performance
   - Track costs

4. **Optimize Performance**
   - Review metrics
   - Adjust scaling settings
   - Optimize costs

5. **Security Review**
   - Check IAM permissions
   - Review security policies
   - Enable audit logs

## 🏆 **Production Checklist**

- [ ] Environment variables configured
- [ ] API keys tested and working
- [ ] Backend deployed and healthy
- [ ] Frontend deployed and accessible
- [ ] Domain configured (if applicable)
- [ ] SSL certificate active
- [ ] Monitoring and alerts setup
- [ ] Backup policies configured
- [ ] Security policies reviewed
- [ ] Cost monitoring enabled

## 🎉 **You're Ready!**

Your RailGPT application is now ready for deployment on Google Cloud Platform with enterprise-grade infrastructure, security, and scalability.

**Start your deployment journey:**
```bash
./deploy/gcp/setup-env.sh
```

**Questions?** Check the comprehensive guides in the `deploy/gcp/` directory!
