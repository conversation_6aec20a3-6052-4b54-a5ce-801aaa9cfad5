#!/usr/bin/env python3
"""
Test script to verify the strict answer display logic implementation.

This script tests that the system correctly prioritizes:
1. Document chunks (highest priority)
2. Website chunks (only if no documents)
3. LLM fallback (only if no relevant chunks)

Run this script to verify the implementation works correctly.
"""

import requests
import json
import time
import sys

# Configuration
API_URL = "http://localhost:8000"

def test_api_health():
    """Test if the API is available."""
    try:
        response = requests.get(f"{API_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API is healthy and responding")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def send_test_query(query, model="gemini-2.0-flash"):
    """Send a test query to the API."""
    try:
        response = requests.post(
            f"{API_URL}/api/query",
            json={
                "query": query,
                "model": model,
                "fallback_enabled": True,
                "extract_format": "paragraph",
                "use_hybrid_search": True
            },
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Query failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Query error: {e}")
        return None

def analyze_response(response, test_name):
    """Analyze the response to check if it follows strict priority logic."""
    print(f"\n🧪 {test_name}")
    print("=" * 50)
    
    if not response:
        print("❌ No response received")
        return False
    
    # Check response structure
    has_document_answer = bool(response.get('document_answer'))
    has_website_answer = bool(response.get('website_answer'))
    has_document_sources = bool(response.get('document_sources'))
    has_website_sources = bool(response.get('website_sources'))
    llm_fallback = response.get('llm_fallback', False)
    
    print(f"📄 Document answer: {'✅' if has_document_answer else '❌'}")
    print(f"🌐 Website answer: {'✅' if has_website_answer else '❌'}")
    print(f"📄 Document sources: {len(response.get('document_sources', []))} sources")
    print(f"🌐 Website sources: {len(response.get('website_sources', []))} sources")
    print(f"🧠 LLM fallback: {'✅' if llm_fallback else '❌'}")
    
    # Verify strict priority logic
    priority_correct = True
    
    if has_document_answer and has_document_sources:
        # PRIORITY 1: Document answer should be used
        if llm_fallback:
            print("❌ ERROR: LLM fallback is true when document sources exist")
            priority_correct = False
        else:
            print("✅ PRIORITY 1: Document answer correctly prioritized")
    
    elif has_website_answer and has_website_sources and not has_document_sources:
        # PRIORITY 2: Website answer should be used (only if no documents)
        if llm_fallback:
            print("❌ ERROR: LLM fallback is true when website sources exist")
            priority_correct = False
        else:
            print("✅ PRIORITY 2: Website answer correctly prioritized")
    
    elif llm_fallback and not has_document_sources and not has_website_sources:
        # PRIORITY 3: LLM fallback should be used (only if no relevant chunks)
        print("✅ PRIORITY 3: LLM fallback correctly used")
    
    else:
        print("❌ ERROR: Priority logic is incorrect")
        print(f"   Document sources: {len(response.get('document_sources', []))}")
        print(f"   Website sources: {len(response.get('website_sources', []))}")
        print(f"   LLM fallback: {llm_fallback}")
        priority_correct = False
    
    # Show answer preview
    answer = response.get('answer', '')
    preview = answer[:200] + "..." if len(answer) > 200 else answer
    print(f"💬 Answer preview: {preview}")
    
    return priority_correct

def main():
    """Run all tests."""
    print("🚀 Testing Answer Display Logic Implementation")
    print("=" * 60)
    
    # Test 1: Check API health
    if not test_api_health():
        print("❌ Cannot proceed - API is not available")
        sys.exit(1)
    
    # Test 2: Document-only query (should find documents)
    print("\n🧪 Test 1: Document-specific query")
    doc_response = send_test_query("What are the safety protocols for railway operations?")
    doc_test_passed = analyze_response(doc_response, "Document Priority Test")
    
    # Test 3: Website-only query (should find websites if no docs)
    print("\n🧪 Test 2: Website-specific query")
    web_response = send_test_query("What is the latest railway news in India?")
    web_test_passed = analyze_response(web_response, "Website Priority Test")
    
    # Test 4: General knowledge query (should use LLM fallback)
    print("\n🧪 Test 3: General knowledge query")
    llm_response = send_test_query("What is the capital of France?")
    llm_test_passed = analyze_response(llm_response, "LLM Fallback Test")
    
    # Test 5: Railway-specific query that might use fallback
    print("\n🧪 Test 4: Railway query with potential fallback")
    rail_response = send_test_query("What is the history of steam locomotives in India?")
    rail_test_passed = analyze_response(rail_response, "Railway Fallback Test")
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 40)
    print(f"Document priority test: {'✅ PASSED' if doc_test_passed else '❌ FAILED'}")
    print(f"Website priority test: {'✅ PASSED' if web_test_passed else '❌ FAILED'}")
    print(f"LLM fallback test: {'✅ PASSED' if llm_test_passed else '❌ FAILED'}")
    print(f"Railway fallback test: {'✅ PASSED' if rail_test_passed else '❌ FAILED'}")
    
    all_tests_passed = all([doc_test_passed, web_test_passed, llm_test_passed, rail_test_passed])
    
    if all_tests_passed:
        print("\n🎉 ALL TESTS PASSED - Answer display logic is working correctly!")
        return 0
    else:
        print("\n💥 SOME TESTS FAILED - Answer display logic needs attention")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 