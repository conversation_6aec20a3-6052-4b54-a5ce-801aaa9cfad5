-- =====================================================
-- CLEAR SUPABASE DATA FOR RAILGPT TESTING
-- =====================================================
-- This script will clear all uploaded documents, websites, 
-- chunks, and queries so you can start fresh for testing
-- =====================================================

-- Clear all data in the correct order (respecting foreign key constraints)

-- 1. Clear document chunks first (references documents table)
TRUNCATE document_chunks RESTART IDENTITY CASCADE;

-- 2. Clear website chunks first (references websites table)  
TRUNCATE website_chunks RESTART IDENTITY CASCADE;

-- 3. Clear documents table
TRUNCATE documents RESTART IDENTITY CASCADE;

-- 4. Clear websites table
TRUNCATE websites RESTART IDENTITY CASCADE;

-- 5. Clear queries table (optional - contains query history)
TRUNCATE queries RESTART IDENTITY CASCADE;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these to verify all data has been cleared:

-- Check document chunks count
SELECT COUNT(*) as document_chunks_count FROM document_chunks;

-- Check website chunks count  
SELECT COUNT(*) as website_chunks_count FROM website_chunks;

-- Check documents count
SELECT COUNT(*) as documents_count FROM documents;

-- Check websites count
SELECT COUNT(*) as websites_count FROM websites;

-- Check queries count
SELECT COUNT(*) as queries_count FROM queries;

-- =====================================================
-- SUMMARY
-- =====================================================
-- After running this script, you should see:
-- - document_chunks_count: 0
-- - website_chunks_count: 0  
-- - documents_count: 0
-- - websites_count: 0
-- - queries_count: 0
-- ===================================================== 