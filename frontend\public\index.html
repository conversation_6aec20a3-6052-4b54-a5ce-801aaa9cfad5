<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="RailGPT Chat Interface"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <script src="https://cdn.tailwindcss.com"></script>
    <title>RailGPT Chat</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    
    <!-- Image cache busting script -->
    <script>
      // Force image reload by disabling browser cache for images
      (function() {
        // Run when DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
          // Add a query parameter with current timestamp to all image URLs
          function addCacheBusterToImages() {
            const timestamp = new Date().getTime();
            const images = document.querySelectorAll('img');
            
            images.forEach(img => {
              if (!img.src) return;
              
              // Skip data URLs
              if (img.src.startsWith('data:')) return;
              
              // Add or update cache buster parameter
              const url = new URL(img.src, window.location.href);
              url.searchParams.set('_cb', timestamp);
              img.src = url.href;
              
              console.log('Cache busting applied to:', img.src);
            });
          }
          
          // Apply immediately
          addCacheBusterToImages();
          
          // Apply to new images that might be added dynamically
          const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
              if (mutation.addedNodes) {
                mutation.addedNodes.forEach(function(node) {
                  if (node.tagName === 'IMG') {
                    const timestamp = new Date().getTime();
                    if (!node.src.startsWith('data:')) {
                      const url = new URL(node.src, window.location.href);
                      url.searchParams.set('_cb', timestamp);
                      node.src = url.href;
                    }
                  }
                });
              }
            });
          });
          
          // Start observing DOM changes
          observer.observe(document.body, { 
            childList: true, 
            subtree: true 
          });
          
          console.log('Image cache busting initialized');
        });
      })();
    </script>
  </body>
</html>
