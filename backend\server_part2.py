def group_chunks_by_source(chunks):
    """
    Group chunks by source type into document and website categories.
    
    Args:
        chunks: List of chunks with source_type information
        
    Returns:
        Tuple of (document_chunks, website_chunks)
    """
    document_chunks = []
    website_chunks = []
    
    for chunk in chunks:
        source_type = chunk.get("source_type", "").lower()
        if source_type == "document":
            document_chunks.append(chunk)
        elif source_type == "website":
            website_chunks.append(chunk)
    
    return document_chunks, website_chunks

def find_similar_chunks(query_embedding, top_k=30):
    """Find chunks with embeddings most similar to the query embedding.
    
    Uses vector database for efficient search when available, with fallback to in-memory search.
    
    Args:
        query_embedding: The query embedding vector
        top_k: Number of most similar chunks to return
        
    Returns:
        List of chunks with highest cosine similarity to query
    """
    similar_chunks = []
    
    try:
        # Try using vector database first (more efficient)
        vector_search_results = vector_db.search(query_embedding, top_k=top_k)
        
        # If results found in vector db
        if vector_search_results:
            similar_chunks = vector_search_results
        else:
            logger.warning("No results from vector database, falling back to in-memory search")
            # Fall back to in-memory search
            chunk_similarities = []
            
            for chunk in DOCUMENT_CHUNKS:
                chunk_embedding = chunk.get("embedding")
                if not chunk_embedding:
                    continue
                
                similarity = cosine_similarity(query_embedding, chunk_embedding)
                if similarity > RELEVANCE_THRESHOLD:
                    chunk_similarities.append((chunk, similarity))
            
            # Sort by similarity (highest first)
            chunk_similarities.sort(key=lambda x: x[1], reverse=True)
            
            # Take top_k results
            similar_chunks = [chunk for chunk, _ in chunk_similarities[:top_k]]
    
    except Exception as e:
        logger.error(f"Error finding similar chunks: {str(e)}")
        # If all else fails, return some chunks (could be random sample)
        # This ensures we degrade gracefully
        if DOCUMENT_CHUNKS:
            similar_chunks = DOCUMENT_CHUNKS[:min(top_k, len(DOCUMENT_CHUNKS))]
    
    return similar_chunks

def generate_llm_answer(query: str, similar_chunks: List[Dict[str, Any]], system_prompt: str = None):
    """Generate a coherent answer using Gemini LLM based on similar chunks.
    
    Args:
        query: The user's question
        similar_chunks: List of context chunks to use for generating the answer
        system_prompt: Optional system prompt to guide the LLM's response
    
    Returns:
        Tuple of (answer_text, source_data)
    """
    # Extract text from chunks to use as context
    chunk_texts = []
    source_data = []
    
    for chunk in similar_chunks:
        # Get text
        text = chunk.get("text", "")
        if not text:
            continue
        
        chunk_texts.append(text)
        
        # Track source information
        source = {}
        source_type = chunk.get("source_type", "unknown")
        source["source_type"] = source_type
        
        if source_type == "document":
            source["filename"] = chunk.get("filename", "unknown")
            source["page"] = chunk.get("page", 0)
        elif source_type == "website":
            source["url"] = chunk.get("url", "unknown")
        
        source_data.append(source)
    
    # Combine all context texts
    context = "\n\n".join(chunk_texts)
    
    # Create prompt with context and query
    if not system_prompt:
        system_prompt = """
        You are an AI assistant for Indian Railways. You will be provided with text chunks from railway 
        documents and websites, and a question. Your task is to answer the question accurately based on
        the provided context ONLY. If the context doesn't contain the information needed to answer the 
        question, state that you don't have that information. Don't make up answers.
        """
    
    # Try to generate an answer with Gemini API if available
    try:
        if api_key:
            model = genai.GenerativeModel('gemini-2.0-flash')
            chat = model.start_chat(history=[])
            
            prompt = f"""
            Context information from railway documents:
            {context}
            
            User Question: {query}
            
            Please provide a clear, concise and accurate answer based solely on the information in the context.
            If the information cannot be found in the context, acknowledge that you don't have that information.
            """
            
            response = chat.send_message(prompt)
            answer = response.text
        else:
            # No API key, generate a generic response
            answer = f"Based on the available information in the railway documents, the answer to '{query}' would require access to the Gemini API to generate. Please provide an API key to enable full functionality."
    
    except Exception as e:
        logger.error(f"Error generating LLM answer: {str(e)}")
        # Fallback answer in case of failure
        answer = f"I encountered an issue while trying to answer your question about '{query}'. The system was unable to process your request due to a technical issue."
    
    return answer, source_data

def create_chunks(text: str, filename: str, page_num: int, target_size: int = 400, overlap: int = 50) -> List[Dict[str, Any]]:
    """Split text into chunks of approximately target_size words with overlap."""
    words = text.split()
    total_words = len(words)
    chunks = []
    
    if total_words == 0:
        return chunks
    
    # If text is smaller than target size, return as a single chunk
    if total_words <= target_size:
        chunk_id = f"{os.path.splitext(filename)[0]}_{page_num}_0"
        chunks.append({
            "filename": filename,
            "page": page_num,
            "chunk_id": chunk_id,
            "text": text,
            "source_type": "document"
        })
        return chunks
    
    # Create overlapping chunks
    chunk_index = 0
    start_idx = 0
    
    while start_idx < total_words:
        end_idx = min(start_idx + target_size, total_words)
        
        # Create chunk
        chunk_text = " ".join(words[start_idx:end_idx])
        chunk_id = f"{os.path.splitext(filename)[0]}_{page_num}_{chunk_index}"
        chunks.append({
            "filename": filename,
            "page": page_num,
            "chunk_id": chunk_id,
            "text": chunk_text,
            "source_type": "document"
        })
        
        # If this is the last chunk, break
        if end_idx >= total_words:
            break
            
        # Move to next chunk start, accounting for overlap
        start_idx = end_idx - overlap
        chunk_index += 1
    
    return chunks
