"""
<PERSON><PERSON>t to test the search priority logic in RailGPT.
"""
import os
import logging
import json
import requests
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def test_search_priority():
    """Test the search priority logic in RailGPT."""
    logger.info("Testing search priority logic in RailGPT...")
    
    # API endpoint
    api_url = "http://localhost:8000/api/query"
    
    # Test queries
    test_queries = [
        "What is the full form of ACP?",
        "What is the full form of FSDS?",
        "What is the Rapid Response app?",
        "What is VASP and who developed it?"
    ]
    
    for query in test_queries:
        logger.info(f"\n=== Testing query: '{query}' ===\n")
        
        # Prepare request data
        request_data = {
            "query": query,
            "use_hybrid_search": True,
            "model_id": "gemini-2.0-flash",
            "extract_format": "paragraph",
            "fallback_enabled": True
        }
        
        # Send request
        try:
            logger.info(f"Sending request to API endpoint: {api_url}")
            response = requests.post(api_url, json=request_data)
            
            # Check response
            if response.status_code == 200:
                result = response.json()
                
                logger.info(f"Response status code: {response.status_code}")
                logger.info(f"Answer: {result.get('answer', 'No answer')}")
                logger.info(f"Sources: {result.get('sources', [])}")
                logger.info(f"Document sources: {result.get('document_sources', [])}")
                logger.info(f"Website sources: {result.get('website_sources', [])}")
                logger.info(f"LLM fallback used: {result.get('llm_fallback', False)}")
                
                # Check if document sources were used
                if result.get('document_sources'):
                    logger.info("✅ Document sources were used (PRIORITY 1)")
                # Check if website sources were used
                elif result.get('website_sources'):
                    logger.info("✅ Website sources were used (PRIORITY 2)")
                # Check if LLM fallback was used
                elif result.get('llm_fallback'):
                    logger.info("✅ LLM fallback was used (PRIORITY 3)")
                else:
                    logger.warning("❌ No sources were used")
            else:
                logger.error(f"Error response: {response.status_code} - {response.text}")
        except Exception as e:
            logger.error(f"Error sending request: {str(e)}")

def test_debug_search():
    """Test the debug search endpoint."""
    logger.info("Testing debug search endpoint...")
    
    # API endpoint
    api_url = "http://localhost:8000/api/debug/search"
    
    # Test queries
    test_queries = [
        "What is the full form of ACP?",
        "What is the full form of FSDS?",
        "What is the Rapid Response app?",
        "What is VASP and who developed it?"
    ]
    
    for query in test_queries:
        logger.info(f"\n=== Testing debug search for query: '{query}' ===\n")
        
        # Send request
        try:
            logger.info(f"Sending request to debug endpoint: {api_url}?query={query}")
            response = requests.get(f"{api_url}?query={query}")
            
            # Check response
            if response.status_code == 200:
                result = response.json()
                
                logger.info(f"Response status code: {response.status_code}")
                logger.info(f"Key terms: {result.get('key_terms', [])}")
                
                # Check direct document search results
                direct_doc_search = result.get('direct_document_search', {})
                logger.info(f"Direct document search - Title results: {direct_doc_search.get('title_count', 0)}")
                logger.info(f"Direct document search - Content results: {direct_doc_search.get('content_count', 0)}")
                logger.info(f"Direct document search - Document chunks: {direct_doc_search.get('direct_document_count', 0)}")
                
                # Check vector document search results
                vector_doc_search = result.get('vector_document_search', {})
                logger.info(f"Vector document search - Document chunks: {vector_doc_search.get('document_count', 0)}")
                
                # Check direct website search results
                direct_web_search = result.get('direct_website_search', {})
                logger.info(f"Direct website search - Website chunks: {direct_web_search.get('website_count', 0)}")
                
                # Check vector website search results
                vector_web_search = result.get('vector_website_search', {})
                logger.info(f"Vector website search - Website chunks: {vector_web_search.get('website_count', 0)}")
                
                # Check search priority
                if direct_doc_search.get('direct_document_count', 0) > 0 or vector_doc_search.get('document_count', 0) > 0:
                    logger.info("✅ Document sources available (PRIORITY 1)")
                elif direct_web_search.get('website_count', 0) > 0 or vector_web_search.get('website_count', 0) > 0:
                    logger.info("✅ Website sources available (PRIORITY 2)")
                else:
                    logger.info("❌ No sources available, would use LLM fallback (PRIORITY 3)")
            else:
                logger.error(f"Error response: {response.status_code} - {response.text}")
        except Exception as e:
            logger.error(f"Error sending request: {str(e)}")

def main():
    """Main function to test search priority."""
    # Test debug search endpoint
    test_debug_search()
    
    # Test search priority logic
    test_search_priority()

if __name__ == "__main__":
    main()
