{"ast": null, "code": "// API endpoints and methods for interacting with the backend\nimport { supabase, saveQuery } from './supabase';\nexport const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Category Management Interfaces\n\n/**\n * Attempts to use the actual backend API but gracefully falls back to a mock response\n * if the backend is not available\n *\n * @param query - The user's question\n * @param model - The LLM model to use (optional, defaults to 'gemini-2.0-flash')\n * @param extractFormat - Format preference for extraction (paragraph, bullet, table)\n * @param useHybridSearch - Whether to use hybrid search (semantic + keyword)\n * @returns Promise with the response containing answer and sources\n */\nexport const sendQuery = async (query, model = 'gemini-2.0-flash', extractFormat = 'paragraph', useHybridSearch = true) => {\n  console.log('Processing query:', query, 'using model:', model);\n\n  // Create an error-focused response with NO sources for when LLM/backend fails\n  // This prevents showing misleading document references when actual query fails\n  const errorResponse = {\n    answer: `No meaningful answer found for '${query}'. The system encountered an error processing your request.`,\n    document_answer: \"\",\n    // Empty string instead of null to satisfy TypeScript\n    website_answer: \"\",\n    // Empty string instead of null to satisfy TypeScript\n    sources: [],\n    // IMPORTANT: No sources when there's an error to avoid misleading references\n    document_sources: [],\n    // Empty sources array for documents\n    website_sources: [] // Empty sources array for websites\n  };\n\n  // Simple informational response when backend is completely unreachable\n  // This is clearly marked as informational and doesn't provide fake sources\n  const connectionErrorResponse = {\n    answer: `Backend server at ${API_URL} is not available. Please ensure the server is running with 'uvicorn server:app --reload'.`,\n    document_answer: \"\",\n    // Empty string instead of null to satisfy TypeScript\n    website_answer: \"\",\n    // Empty string instead of null to satisfy TypeScript\n    sources: [],\n    // NO fabricated document sources\n    document_sources: [],\n    website_sources: []\n  };\n\n  // First, try to use the real backend\n  try {\n    // Check if the backend API is available\n    console.log('Connecting to backend at:', API_URL);\n    try {\n      // Implement retry mechanism with model fallback\n      const MAX_RETRIES = 2;\n      let currentRetry = 0;\n      let currentModel = model;\n      let response = null;\n      while (currentRetry <= MAX_RETRIES) {\n        // Log the current attempt\n        console.log(`Attempt ${currentRetry + 1}/${MAX_RETRIES + 1}: Sending query to: ${API_URL}/api/query with model ${currentModel}`);\n\n        // Set up timeout control\n        const controller = new AbortController();\n        const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout - doubled for complex queries\n\n        try {\n          // Connect directly to the main backend server\n          console.log(`Sending query to main server: ${API_URL}/api/query`);\n          response = await fetch(`${API_URL}/api/query`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Accept': 'application/json'\n            },\n            body: JSON.stringify({\n              query,\n              model: currentModel,\n              fallback_enabled: true,\n              extract_format: extractFormat,\n              use_hybrid_search: useHybridSearch,\n              retry_on_timeout: true\n            }),\n            signal: controller.signal\n          });\n\n          // Clear the timeout to prevent memory leaks\n          clearTimeout(timeoutId);\n\n          // If we got a successful response, break out of the retry loop\n          if (response.ok) {\n            break;\n          }\n        } catch (error) {\n          // Clear the timeout to prevent memory leaks\n          clearTimeout(timeoutId);\n\n          // Handle timeout or network error\n          if (error instanceof DOMException && error.name === 'AbortError') {\n            console.warn(`Query timed out with model ${currentModel}, retrying with fallback model...`);\n          } else {\n            console.error(`Fetch error with model ${currentModel}:`, error);\n          }\n\n          // Fall back to a faster model if available\n          if (currentModel === 'gemini-2.0-flash' || currentModel === 'gemini-2.0-flash') {\n            currentModel = 'gemini-2.0-flash'; // Fall back to faster model\n          } else if (currentModel !== 'gemini-2.0-flash') {\n            currentModel = 'gemini-2.0-flash'; // Default fallback\n          }\n\n          // If this is the last retry and it failed, throw the error to be caught by the outer try/catch\n          if (currentRetry === MAX_RETRIES) {\n            throw error;\n          }\n        }\n        currentRetry++;\n      }\n      if (response && response.ok) {\n        const data = await response.json();\n\n        // Add model information for UI display if it's not present\n        if (data && !data.llm_model) {\n          data.llm_model = model;\n        }\n\n        // Check if response contains valid data\n        if (!data.answer) {\n          console.error('Invalid response format from server:', data);\n          return {\n            ...errorResponse,\n            answer: `The server returned an invalid response format. Please try again later.`\n          };\n        }\n\n        // Save query to Supabase if enabled\n        try {\n          if (process.env.REACT_APP_SAVE_QUERIES === 'true') {\n            const startTime = performance.now();\n            await saveQuery({\n              query_text: query,\n              answer_text: data.answer,\n              llm_model: model,\n              sources: data.sources || [],\n              processing_time: (performance.now() - startTime) / 1000\n            });\n          }\n        } catch (error) {\n          console.error('Error saving query to Supabase:', error);\n          // Continue even if saving fails\n        }\n        return data;\n      } else if (response) {\n        // Non-OK response from backend\n        console.error('Error from backend:', response.status, response.statusText);\n        let errorDetails;\n        try {\n          errorDetails = await response.text();\n        } catch (e) {\n          errorDetails = 'Unable to parse error details';\n        }\n        console.error('Error details:', errorDetails);\n        return {\n          ...errorResponse,\n          answer: `Error ${response.status}: ${response.statusText}. ${errorDetails}`\n        };\n      } else {\n        // No response object at all (should never happen with our retry logic)\n        return {\n          ...errorResponse,\n          answer: `The server did not respond. Please try again later.`\n        };\n      }\n    } catch (error) {\n      if (error instanceof DOMException && error.name === 'AbortError') {\n        console.error('Query request timed out');\n\n        // Provide a more helpful suggestion based on the current model\n        let timeoutMessage = `Query timed out after 60 seconds.`;\n\n        // Suggest a different model based on what's currently being used\n        if (model === 'gemini-2.0-flash' || model === 'gemini-2.0-flash') {\n          timeoutMessage += ` Try a faster model like 'gemini-2.0-flash' or simplify your query.`;\n        } else if (model === 'gemini-2.0-flash') {\n          timeoutMessage += ` Try simplifying your query.`;\n        } else {\n          timeoutMessage += ` Try a faster model or simplify your query.`;\n        }\n        return {\n          ...errorResponse,\n          answer: timeoutMessage\n        };\n      }\n\n      // Other types of fetch errors (network issues, etc.)\n      console.error('Error querying backend:', error);\n      return connectionErrorResponse;\n    }\n  } catch (error) {\n    console.error('Error connecting to backend:', error);\n    return connectionErrorResponse;\n  }\n};\n\n/**\n * Upload a document file to the backend\n *\n * @param file - The file to upload\n * @param uploadedBy - Name of the uploader (optional)\n * @returns Promise with the normalized upload response\n */\nexport const uploadDocument = async (file, uploadedBy = 'default', extractTables = true, extractImages = true, extractCharts = true) => {\n  console.log('Uploading document:', file.name);\n  try {\n    // First, upload to Supabase Storage if enabled\n    let supabaseFilePath = null;\n    let supabaseFileUrl = null;\n    if (process.env.REACT_APP_USE_SUPABASE_STORAGE === 'true') {\n      try {\n        // Generate a unique file path to prevent collisions\n        const timestamp = Date.now();\n        const uniqueFilePath = `${uploadedBy}/${timestamp}_${file.name}`;\n\n        // Upload to Supabase Storage\n        const {\n          data: storageData,\n          error: storageError\n        } = await supabase.storage.from('documents').upload(uniqueFilePath, file, {\n          cacheControl: '3600',\n          upsert: false\n        });\n        if (storageError) {\n          console.error('Error uploading to Supabase Storage:', storageError);\n        } else {\n          supabaseFilePath = storageData.path;\n\n          // Get the public URL\n          const {\n            data: {\n              publicUrl\n            }\n          } = supabase.storage.from('documents').getPublicUrl(supabaseFilePath);\n          supabaseFileUrl = publicUrl;\n          console.log('Uploaded to Supabase Storage:', supabaseFilePath, supabaseFileUrl);\n        }\n      } catch (storageError) {\n        console.error('Error in Supabase Storage upload:', storageError);\n        // Continue with backend upload even if Supabase upload fails\n      }\n    }\n\n    // Create form data for file upload to backend\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('uploaded_by', uploadedBy);\n\n    // Add Supabase storage info if available\n    if (supabaseFilePath) {\n      formData.append('supabase_file_path', supabaseFilePath);\n      formData.append('supabase_file_url', supabaseFileUrl || '');\n    }\n\n    // Send the request to the backend\n    const response = await fetch(`${API_URL}/api/upload-document`, {\n      method: 'POST',\n      body: formData\n      // Don't set Content-Type header - browser will set it with boundary for FormData\n    });\n    if (!response.ok) {\n      // Format error response\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      return {\n        success: false,\n        message: errorData.detail || `Upload failed: ${response.status} ${response.statusText}`\n      };\n    }\n\n    // Process successful response\n    const data = await response.json();\n    console.log('Upload response:', data);\n\n    // Normalize the response to match our interface\n    return {\n      success: true,\n      message: data.message,\n      chunks_extracted: data.chunks_extracted,\n      chunks: data.chunks,\n      data: {\n        id: data.document_id || `doc-${Date.now()}`,\n        path: supabaseFilePath || `/documents/${file.name}`,\n        originalResponse: data\n      }\n    };\n  } catch (error) {\n    console.error('Error uploading document:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Unknown error occurred during upload'\n    };\n  }\n};\n\n/**\n * Add a website URL to be scraped and indexed\n *\n * @param url - The website URL to add\n * @param submittedBy - Name of the submitter (optional)\n * @param extractionOptions - Advanced options for website extraction\n * @returns Promise with the normalized website add response\n */\nexport const addWebsite = async (url, submittedBy = 'default', extractionOptions) => {\n  console.log('Adding website:', url, 'with options:', extractionOptions);\n  try {\n    const request = {\n      url,\n      submitted_by: submittedBy,\n      ...extractionOptions\n    };\n\n    // Send the request to the backend\n    const response = await fetch(`${API_URL}/api/add-website`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(request)\n    });\n    if (!response.ok) {\n      // Format error response\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      return {\n        success: false,\n        message: errorData.detail || `Website add failed: ${response.status} ${response.statusText}`\n      };\n    }\n\n    // Process successful response\n    const data = await response.json();\n    console.log('Website add response:', data);\n\n    // Normalize the response to match our interface\n    return {\n      success: true,\n      message: data.message,\n      chunks_extracted: data.chunks_extracted,\n      chunks: data.chunks,\n      data: {\n        id: `web-${Date.now()}`,\n        path: url,\n        originalResponse: data\n      }\n    };\n  } catch (error) {\n    console.error('Error adding website:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Unknown error occurred while adding website'\n    };\n  }\n};\n\n/**\n * Get document extraction details\n *\n * @param documentId - The ID of the document\n * @returns Promise with the extraction details\n */\nexport const getDocumentExtractionDetails = async documentId => {\n  console.log('Getting extraction details for document:', documentId);\n  try {\n    const response = await fetch(`${API_URL}/api/documents/${documentId}/extraction-details`);\n    if (!response.ok) {\n      console.error(`Failed to get document extraction details: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get document extraction details: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting document extraction details:', error);\n\n    // Return fallback data\n    return {\n      extractedContent: `Sample extracted content for ${documentId}. This is placeholder text because the actual content could not be retrieved from the server.`,\n      extractionMethod: 'Unknown (error occurred)',\n      qualityScore: 0,\n      processingTime: 0,\n      chunks: 0,\n      warnings: ['Failed to retrieve extraction details from server'],\n      fallbackReason: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n\n/**\n * Get document content\n *\n * @param documentId - The ID of the document\n * @returns Promise with the document content\n */\nexport const getDocumentContent = async documentId => {\n  console.log('Getting content for document:', documentId);\n  try {\n    const response = await fetch(`${API_URL}/api/documents/${documentId}/content`);\n    if (!response.ok) {\n      console.error(`Failed to get document content: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get document content: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting document content:', error);\n\n    // Return fallback data\n    return {\n      content: `# Document Content Unavailable\\n\\nThe content for document ${documentId} could not be retrieved from the server.\\n\\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\\n\\nPlease try again later or contact support if the problem persists.`,\n      extraction_method: 'Unknown (error occurred)',\n      quality_score: 0,\n      processing_time: 0,\n      chunks_count: 0\n    };\n  }\n};\n\n/**\n * Get website extraction details\n *\n * @param websiteId - The ID of the website\n * @returns Promise with the extraction details\n */\nexport const getWebsiteExtractionDetails = async websiteId => {\n  console.log('Getting extraction details for website:', websiteId);\n  try {\n    const response = await fetch(`${API_URL}/api/websites/${websiteId}/extraction-details`);\n    if (!response.ok) {\n      console.error(`Failed to get website extraction details: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get website extraction details: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting website extraction details:', error);\n\n    // Return fallback data\n    return {\n      extractedContent: `Sample extracted content for ${websiteId}. This is placeholder text because the actual content could not be retrieved from the server.`,\n      extractionMethod: 'Unknown (error occurred)',\n      fallbackHistory: [],\n      contentQuality: 0,\n      warnings: ['Failed to retrieve extraction details from server'],\n      processingTime: 0,\n      chunks: 0\n    };\n  }\n};\n\n/**\n * Get website content\n *\n * @param websiteId - The ID of the website\n * @returns Promise with the website content\n */\nexport const getWebsiteContent = async websiteId => {\n  console.log('Getting content for website:', websiteId);\n  try {\n    const response = await fetch(`${API_URL}/api/websites/${websiteId}/content`);\n    if (!response.ok) {\n      console.error(`Failed to get website content: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get website content: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting website content:', error);\n\n    // Return fallback data\n    return {\n      content: `# Website Content Unavailable\\n\\nThe content for website ${websiteId} could not be retrieved from the server.\\n\\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\\n\\nPlease try again later or contact support if the problem persists.`,\n      extraction_method: 'Unknown (error occurred)',\n      quality_score: 0,\n      processing_time: 0,\n      pages_processed: 0,\n      total_links: 0\n    };\n  }\n};\n\n/**\n * Submit feedback for an AI answer\n *\n * @param feedbackData - The feedback data containing query, answer, issue type, etc.\n * @returns Promise with the response indicating success or failure\n */\nexport const submitFeedback = async feedbackData => {\n  console.log('Submitting feedback:', feedbackData);\n  try {\n    const response = await fetch(`${API_URL}/api/feedback`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      },\n      body: JSON.stringify(feedbackData)\n    });\n    if (!response.ok) {\n      console.error(`Failed to submit feedback: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to submit feedback: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error submitting feedback:', error);\n    return {\n      success: false,\n      message: `Failed to submit feedback: ${error instanceof Error ? error.message : 'Unknown error'}`\n    };\n  }\n};\n\n/**\n * Get the configured feedback notification emails\n *\n * @returns Promise with the list of configured emails\n */\nexport const getFeedbackEmails = async () => {\n  try {\n    const response = await fetch(`${API_URL}/api/feedback/emails`);\n    if (!response.ok) {\n      console.error(`Failed to get feedback emails: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get feedback emails: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting feedback emails:', error);\n    return {\n      emails: []\n    };\n  }\n};\n\n/**\n * Update the configured feedback notification emails\n *\n * @param emails - The new list of emails to configure for feedback notifications\n * @returns Promise with the response indicating success or failure\n */\nexport const updateFeedbackEmails = async emails => {\n  try {\n    const response = await fetch(`${API_URL}/api/feedback/emails`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      },\n      body: JSON.stringify({\n        emails\n      })\n    });\n    if (!response.ok) {\n      console.error(`Failed to update feedback emails: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to update feedback emails: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating feedback emails:', error);\n    return {\n      success: false,\n      message: `Failed to update feedback emails: ${error instanceof Error ? error.message : 'Unknown error'}`\n    };\n  }\n};\n\n/**\n * Get all documents from the backend\n * @returns Promise with the list of documents\n */\nexport const getDocuments = async () => {\n  console.log('Fetching documents from backend...');\n  try {\n    const response = await fetch(`${API_URL}/api/documents`);\n    if (!response.ok) {\n      console.error(`Failed to get documents: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get documents: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting documents:', error);\n    return []; // Return empty array on error\n  }\n};\n\n/**\n * Get all websites from the backend\n * @returns Promise with the list of websites\n */\nexport const getWebsites = async () => {\n  console.log('Fetching websites from backend...');\n  try {\n    const response = await fetch(`${API_URL}/api/websites`);\n    if (!response.ok) {\n      console.error(`Failed to get websites: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get websites: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting websites:', error);\n    return []; // Return empty array on error\n  }\n};\n\n// ===== CATEGORY MANAGEMENT API FUNCTIONS =====\n\n/**\n * Get all categories with hierarchy\n * @returns Promise with the list of categories\n */\nexport const getCategories = async () => {\n  console.log('Fetching categories from backend...');\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`);\n    if (!response.ok) {\n      console.error(`Failed to get categories: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get categories: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting categories:', error);\n    return []; // Return empty array on error\n  }\n};\n\n/**\n * Create a new category\n * @param category - The category data to create\n * @returns Promise with the creation response\n */\nexport const createCategory = async category => {\n  console.log('Creating category:', category);\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(category)\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to create category: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error creating category:', error);\n    throw error;\n  }\n};\n\n/**\n * Update a category\n * @param categoryId - The ID of the category to update\n * @param updates - The updates to apply\n * @returns Promise with the update response\n */\nexport const updateCategory = async (categoryId, updates) => {\n  console.log('Updating category:', categoryId, updates);\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(updates)\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to update category: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating category:', error);\n    throw error;\n  }\n};\n\n/**\n * Delete a category\n * @param categoryId - The ID of the category to delete\n * @returns Promise with the deletion response\n */\nexport const deleteCategory = async categoryId => {\n  console.log('Deleting category:', categoryId);\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'DELETE'\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to delete category: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    throw error;\n  }\n};\n\n/**\n * Update document categories\n * @param documentId - The ID of the document to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const updateDocumentCategories = async (documentId, categoryUpdate) => {\n  console.log('Updating document categories:', documentId, categoryUpdate);\n  try {\n    const response = await fetch(`${API_URL}/api/categories/documents/${documentId}/categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(categoryUpdate)\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to update document categories: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating document categories:', error);\n    throw error;\n  }\n};\n\n/**\n * Bulk update document categories\n * @param documentIds - Array of document IDs to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const bulkUpdateDocumentCategories = async (documentIds, categoryUpdate) => {\n  console.log('Bulk updating document categories:', documentIds, categoryUpdate);\n  try {\n    const response = await fetch(`${API_URL}/api/categories/documents/bulk-update-categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        document_ids: documentIds,\n        ...categoryUpdate\n      })\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to bulk update document categories: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error bulk updating document categories:', error);\n    throw error;\n  }\n};\n\n/**\n * Get all website categories\n * @returns Promise with the list of website categories\n */\nexport const getWebsiteCategories = async () => {\n  console.log('Fetching website categories from backend...');\n  try {\n    const response = await fetch(`${API_URL}/api/categories/website-categories/`);\n    if (!response.ok) {\n      console.error(`Failed to get website categories: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get website categories: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting website categories:', error);\n    return []; // Return empty array on error\n  }\n};\n\n/**\n * Create a new website category\n * @param category - The website category data to create\n * @returns Promise with the creation response\n */\nexport const createWebsiteCategory = async category => {\n  console.log('Creating website category:', category);\n  try {\n    const response = await fetch(`${API_URL}/api/categories/website-categories/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(category)\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to create website category: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error creating website category:', error);\n    throw error;\n  }\n};\n\n/**\n * Update website categories\n * @param websiteId - The ID of the website to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const updateWebsiteCategories = async (websiteId, categoryUpdate) => {\n  console.log('Updating website categories:', websiteId, categoryUpdate);\n  try {\n    const response = await fetch(`${API_URL}/api/categories/websites/${websiteId}/categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(categoryUpdate)\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to update website categories: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating website categories:', error);\n    throw error;\n  }\n};\n\n/**\n * Bulk update website categories\n * @param websiteIds - Array of website IDs to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const bulkUpdateWebsiteCategories = async (websiteIds, categoryUpdate) => {\n  console.log('Bulk updating website categories:', websiteIds, categoryUpdate);\n  try {\n    const response = await fetch(`${API_URL}/api/categories/websites/bulk-update-categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        website_ids: websiteIds,\n        ...categoryUpdate\n      })\n    });\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({\n        detail: response.statusText\n      }));\n      throw new Error(errorData.detail || `Failed to bulk update website categories: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error bulk updating website categories:', error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["supabase", "saveQuery", "API_URL", "process", "env", "REACT_APP_API_URL", "<PERSON><PERSON><PERSON><PERSON>", "query", "model", "extractFormat", "useHybridSearch", "console", "log", "errorResponse", "answer", "document_answer", "website_answer", "sources", "document_sources", "website_sources", "connectionErrorResponse", "MAX_RETRIES", "currentRetry", "currentModel", "response", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "fetch", "method", "headers", "body", "JSON", "stringify", "fallback_enabled", "extract_format", "use_hybrid_search", "retry_on_timeout", "signal", "clearTimeout", "ok", "error", "DOMException", "name", "warn", "data", "json", "llm_model", "REACT_APP_SAVE_QUERIES", "startTime", "performance", "now", "query_text", "answer_text", "processing_time", "status", "statusText", "errorDetails", "text", "e", "timeoutMessage", "uploadDocument", "file", "uploadedBy", "extractTables", "extractImages", "extractCharts", "supabaseFilePath", "supabaseFileUrl", "REACT_APP_USE_SUPABASE_STORAGE", "timestamp", "Date", "uniqueFilePath", "storageData", "storageError", "storage", "from", "upload", "cacheControl", "upsert", "path", "publicUrl", "getPublicUrl", "formData", "FormData", "append", "errorData", "catch", "detail", "success", "message", "chunks_extracted", "chunks", "id", "document_id", "originalResponse", "Error", "addWebsite", "url", "submittedBy", "extractionOptions", "request", "submitted_by", "getDocumentExtractionDetails", "documentId", "extractedContent", "extractionMethod", "qualityScore", "processingTime", "warnings", "fallbackReason", "getDocumentContent", "content", "extraction_method", "quality_score", "chunks_count", "getWebsiteExtractionDetails", "websiteId", "fallbackHistory", "contentQuality", "getWebsiteContent", "pages_processed", "total_links", "submitFeedback", "feedbackData", "getFeedbackEmails", "emails", "updateFeedbackEmails", "getDocuments", "getWebsites", "getCategories", "createCategory", "category", "updateCategory", "categoryId", "updates", "deleteCategory", "updateDocumentCategories", "categoryUpdate", "bulkUpdateDocumentCategories", "documentIds", "document_ids", "getWebsiteCategories", "createWebsiteCategory", "updateWebsiteCategories", "bulkUpdateWebsiteCategories", "websiteIds", "website_ids"], "sources": ["C:/IR App/frontend/src/services/api.ts"], "sourcesContent": ["// API endpoints and methods for interacting with the backend\nimport { supabase, saveQuery } from './supabase';\n\nexport const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\nexport interface QueryRequest {\n  query: string;\n}\n\nexport interface Source {\n  source_type: string;\n  filename?: string;\n  name?: string;  // For display name of the document\n  page?: number;\n  url?: string;\n  link?: string;  // For document viewer links\n  // Visual content fields\n  content_type?: string;  // \"text\", \"table\", \"image\", \"chart_diagram\"\n  visual_content?: Record<string, any>;  // Visual content metadata\n  storage_url?: string;  // URL for stored visual content\n  display_type?: string;  // \"text\", \"html_table\", \"image\", \"base64_image\"\n}\n\nexport interface QueryResponse {\n  answer: string;  // Combined answer\n  document_answer?: string;  // Answer from document sources only\n  website_answer?: string;  // Answer from website sources only\n  sources: Array<Source>;  // All sources\n  document_sources?: Array<Source>;  // Document sources only\n  website_sources?: Array<Source>;  // Website sources only\n  llm_model?: string;  // The LLM model used for generating the answer\n  llm_fallback?: boolean;  // Whether the answer was generated using LLM fallback\n  visual_content_found?: boolean;  // Whether visual content was found\n  visual_content_types?: string[];  // Types of visual content found\n}\n\nexport interface WebsiteAddRequest {\n  url: string;\n  submitted_by?: string;\n  role?: string;\n  follow_links?: boolean;\n  extraction_depth?: number;\n  extract_images?: boolean;\n  extract_tables?: boolean;\n  max_pages?: number;\n  extractor_type?: string;\n  domain_category?: string;\n  [key: string]: any; // To allow for future extension options\n}\n\nexport interface UploadResponse {\n  success: boolean;\n  message: string;\n  chunks_extracted?: number;\n  chunks?: Array<any>;\n  data?: {\n    id: string;\n    path: string;\n    [key: string]: any;\n  };\n}\n\nexport interface FeedbackData {\n  query: string;  // The user's question\n  answer: string; // The AI's answer\n  issue_type: string; // Inaccurate, Incomplete, Offensive, Too Slow, Other\n  comment?: string; // Optional user comment\n  model?: string; // LLM model used\n  chat_id?: string; // Unique identifier for this chat\n  timestamp: string; // When the feedback was submitted\n}\n\nexport interface FeedbackResponse {\n  success: boolean;\n  message: string;\n}\n\n// Category Management Interfaces\nexport interface Category {\n  id: string;\n  name: string;\n  description?: string;\n  parent_id?: string;\n  level: number;\n  full_path: string;\n  sort_order: number;\n  is_active: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface CategoryHierarchy {\n  id: string;\n  name: string;\n  description?: string;\n  parent_id?: string;\n  level: number;\n  full_path: string;\n  children?: CategoryHierarchy[];\n}\n\nexport interface DocumentCategoryUpdate {\n  main_category?: string;\n  category?: string;\n  sub_category?: string;\n  minor_category?: string;\n}\n\nexport interface WebsiteCategoryUpdate {\n  category?: string;\n  website_category_id?: string;\n}\n\nexport interface WebsiteCategory {\n  id: string;\n  name: string;\n  description?: string;\n  is_active: boolean;\n  sort_order: number;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface CategoryCreateRequest {\n  name: string;\n  description?: string;\n  parent_id?: string;\n  sort_order?: number;\n}\n\nexport interface WebsiteCategoryCreateRequest {\n  name: string;\n  description?: string;\n  sort_order?: number;\n}\n\n/**\n * Attempts to use the actual backend API but gracefully falls back to a mock response\n * if the backend is not available\n *\n * @param query - The user's question\n * @param model - The LLM model to use (optional, defaults to 'gemini-2.0-flash')\n * @param extractFormat - Format preference for extraction (paragraph, bullet, table)\n * @param useHybridSearch - Whether to use hybrid search (semantic + keyword)\n * @returns Promise with the response containing answer and sources\n */\nexport const sendQuery = async (\n  query: string,\n  model: string = 'gemini-2.0-flash',\n  extractFormat: string = 'paragraph',\n  useHybridSearch: boolean = true\n): Promise<QueryResponse> => {\n  console.log('Processing query:', query, 'using model:', model);\n\n  // Create an error-focused response with NO sources for when LLM/backend fails\n  // This prevents showing misleading document references when actual query fails\n  const errorResponse: QueryResponse = {\n    answer: `No meaningful answer found for '${query}'. The system encountered an error processing your request.`,\n    document_answer: \"\", // Empty string instead of null to satisfy TypeScript\n    website_answer: \"\", // Empty string instead of null to satisfy TypeScript\n    sources: [], // IMPORTANT: No sources when there's an error to avoid misleading references\n    document_sources: [], // Empty sources array for documents\n    website_sources: [] // Empty sources array for websites\n  };\n\n  // Simple informational response when backend is completely unreachable\n  // This is clearly marked as informational and doesn't provide fake sources\n  const connectionErrorResponse: QueryResponse = {\n    answer: `Backend server at ${API_URL} is not available. Please ensure the server is running with 'uvicorn server:app --reload'.`,\n    document_answer: \"\", // Empty string instead of null to satisfy TypeScript\n    website_answer: \"\", // Empty string instead of null to satisfy TypeScript\n    sources: [], // NO fabricated document sources\n    document_sources: [],\n    website_sources: []\n  };\n\n  // First, try to use the real backend\n  try {\n    // Check if the backend API is available\n    console.log('Connecting to backend at:', API_URL);\n\n    try {\n      // Implement retry mechanism with model fallback\n      const MAX_RETRIES = 2;\n      let currentRetry = 0;\n      let currentModel = model;\n      let response = null;\n      \n      while (currentRetry <= MAX_RETRIES) {\n        // Log the current attempt\n        console.log(`Attempt ${currentRetry + 1}/${MAX_RETRIES + 1}: Sending query to: ${API_URL}/api/query with model ${currentModel}`);\n        \n        // Set up timeout control\n        const controller = new AbortController();\n        const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout - doubled for complex queries\n        \n        try {\n          // Connect directly to the main backend server\n          console.log(`Sending query to main server: ${API_URL}/api/query`);\n          \n          response = await fetch(`${API_URL}/api/query`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Accept': 'application/json'\n            },\n            body: JSON.stringify({\n              query,\n              model: currentModel,\n              fallback_enabled: true,\n              extract_format: extractFormat,\n              use_hybrid_search: useHybridSearch,\n              retry_on_timeout: true\n            }),\n            signal: controller.signal\n          });\n          \n          // Clear the timeout to prevent memory leaks\n          clearTimeout(timeoutId);\n          \n          // If we got a successful response, break out of the retry loop\n          if (response.ok) {\n            break;\n          }\n        } catch (error) {\n          // Clear the timeout to prevent memory leaks\n          clearTimeout(timeoutId);\n          \n          // Handle timeout or network error\n          if (error instanceof DOMException && error.name === 'AbortError') {\n            console.warn(`Query timed out with model ${currentModel}, retrying with fallback model...`);\n          } else {\n            console.error(`Fetch error with model ${currentModel}:`, error);\n          }\n          \n          // Fall back to a faster model if available\n          if (currentModel === 'gemini-2.0-flash' || currentModel === 'gemini-2.0-flash') {\n            currentModel = 'gemini-2.0-flash'; // Fall back to faster model\n          } else if (currentModel !== 'gemini-2.0-flash') {\n            currentModel = 'gemini-2.0-flash'; // Default fallback\n          }\n          \n          // If this is the last retry and it failed, throw the error to be caught by the outer try/catch\n          if (currentRetry === MAX_RETRIES) {\n            throw error;\n          }\n        }\n        \n        currentRetry++;\n      }\n\n      if (response && response.ok) {\n        const data = await response.json();\n\n        // Add model information for UI display if it's not present\n        if (data && !data.llm_model) {\n          data.llm_model = model;\n        }\n\n        // Check if response contains valid data\n        if (!data.answer) {\n          console.error('Invalid response format from server:', data);\n          return {\n            ...errorResponse,\n            answer: `The server returned an invalid response format. Please try again later.`\n          };\n        }\n\n        // Save query to Supabase if enabled\n        try {\n          if (process.env.REACT_APP_SAVE_QUERIES === 'true') {\n            const startTime = performance.now();\n            await saveQuery({\n              query_text: query,\n              answer_text: data.answer,\n              llm_model: model,\n              sources: data.sources || [],\n              processing_time: (performance.now() - startTime) / 1000\n            });\n          }\n        } catch (error) {\n          console.error('Error saving query to Supabase:', error);\n          // Continue even if saving fails\n        }\n\n        return data as QueryResponse;\n      } else if (response) {\n        // Non-OK response from backend\n        console.error('Error from backend:', response.status, response.statusText);\n\n        let errorDetails;\n        try {\n          errorDetails = await response.text();\n        } catch (e) {\n          errorDetails = 'Unable to parse error details';\n        }\n\n        console.error('Error details:', errorDetails);\n\n        return {\n          ...errorResponse,\n          answer: `Error ${response.status}: ${response.statusText}. ${errorDetails}`\n        };\n      } else {\n        // No response object at all (should never happen with our retry logic)\n        return {\n          ...errorResponse,\n          answer: `The server did not respond. Please try again later.`\n        };\n      }\n    } catch (error) {\n      if (error instanceof DOMException && error.name === 'AbortError') {\n        console.error('Query request timed out');\n\n        // Provide a more helpful suggestion based on the current model\n        let timeoutMessage = `Query timed out after 60 seconds.`;\n\n        // Suggest a different model based on what's currently being used\n        if (model === 'gemini-2.0-flash' || model === 'gemini-2.0-flash') {\n          timeoutMessage += ` Try a faster model like 'gemini-2.0-flash' or simplify your query.`;\n        } else if (model === 'gemini-2.0-flash') {\n          timeoutMessage += ` Try simplifying your query.`;\n        } else {\n          timeoutMessage += ` Try a faster model or simplify your query.`;\n        }\n\n        return {\n          ...errorResponse,\n          answer: timeoutMessage\n        };\n      }\n\n      // Other types of fetch errors (network issues, etc.)\n      console.error('Error querying backend:', error);\n      return connectionErrorResponse;\n    }\n  } catch (error: any) {\n    console.error('Error connecting to backend:', error);\n    return connectionErrorResponse;\n  }\n};\n\n/**\n * Upload a document file to the backend\n *\n * @param file - The file to upload\n * @param uploadedBy - Name of the uploader (optional)\n * @returns Promise with the normalized upload response\n */\nexport const uploadDocument = async (\n  file: File, \n  uploadedBy: string = 'default',\n  extractTables: boolean = true,\n  extractImages: boolean = true,\n  extractCharts: boolean = true\n): Promise<UploadResponse> => {\n  console.log('Uploading document:', file.name);\n\n  try {\n    // First, upload to Supabase Storage if enabled\n    let supabaseFilePath = null;\n    let supabaseFileUrl = null;\n\n    if (process.env.REACT_APP_USE_SUPABASE_STORAGE === 'true') {\n      try {\n        // Generate a unique file path to prevent collisions\n        const timestamp = Date.now();\n        const uniqueFilePath = `${uploadedBy}/${timestamp}_${file.name}`;\n\n        // Upload to Supabase Storage\n        const { data: storageData, error: storageError } = await supabase.storage\n          .from('documents')\n          .upload(uniqueFilePath, file, {\n            cacheControl: '3600',\n            upsert: false\n          });\n\n        if (storageError) {\n          console.error('Error uploading to Supabase Storage:', storageError);\n        } else {\n          supabaseFilePath = storageData.path;\n\n          // Get the public URL\n          const { data: { publicUrl } } = supabase.storage\n            .from('documents')\n            .getPublicUrl(supabaseFilePath);\n\n          supabaseFileUrl = publicUrl;\n          console.log('Uploaded to Supabase Storage:', supabaseFilePath, supabaseFileUrl);\n        }\n      } catch (storageError) {\n        console.error('Error in Supabase Storage upload:', storageError);\n        // Continue with backend upload even if Supabase upload fails\n      }\n    }\n\n    // Create form data for file upload to backend\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('uploaded_by', uploadedBy);\n\n    // Add Supabase storage info if available\n    if (supabaseFilePath) {\n      formData.append('supabase_file_path', supabaseFilePath);\n      formData.append('supabase_file_url', supabaseFileUrl || '');\n    }\n\n    // Send the request to the backend\n    const response = await fetch(`${API_URL}/api/upload-document`, {\n      method: 'POST',\n      body: formData,\n      // Don't set Content-Type header - browser will set it with boundary for FormData\n    });\n\n    if (!response.ok) {\n      // Format error response\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      return {\n        success: false,\n        message: errorData.detail || `Upload failed: ${response.status} ${response.statusText}`,\n      };\n    }\n\n    // Process successful response\n    const data = await response.json();\n    console.log('Upload response:', data);\n\n    // Normalize the response to match our interface\n    return {\n      success: true,\n      message: data.message,\n      chunks_extracted: data.chunks_extracted,\n      chunks: data.chunks,\n      data: {\n        id: data.document_id || `doc-${Date.now()}`,\n        path: supabaseFilePath || `/documents/${file.name}`,\n        originalResponse: data\n      }\n    };\n  } catch (error) {\n    console.error('Error uploading document:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Unknown error occurred during upload',\n    };\n  }\n};\n\n/**\n * Add a website URL to be scraped and indexed\n *\n * @param url - The website URL to add\n * @param submittedBy - Name of the submitter (optional)\n * @param extractionOptions - Advanced options for website extraction\n * @returns Promise with the normalized website add response\n */\nexport const addWebsite = async (\n  url: string,\n  submittedBy: string = 'default',\n  extractionOptions?: Record<string, any>\n): Promise<UploadResponse> => {\n  console.log('Adding website:', url, 'with options:', extractionOptions);\n\n  try {\n    const request: WebsiteAddRequest = {\n      url,\n      submitted_by: submittedBy,\n      ...extractionOptions\n    };\n\n    // Send the request to the backend\n    const response = await fetch(`${API_URL}/api/add-website`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(request),\n    });\n\n    if (!response.ok) {\n      // Format error response\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      return {\n        success: false,\n        message: errorData.detail || `Website add failed: ${response.status} ${response.statusText}`,\n      };\n    }\n\n    // Process successful response\n    const data = await response.json();\n    console.log('Website add response:', data);\n\n    // Normalize the response to match our interface\n    return {\n      success: true,\n      message: data.message,\n      chunks_extracted: data.chunks_extracted,\n      chunks: data.chunks,\n      data: {\n        id: `web-${Date.now()}`,\n        path: url,\n        originalResponse: data\n      }\n    };\n  } catch (error) {\n    console.error('Error adding website:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Unknown error occurred while adding website',\n    };\n  }\n};\n\n/**\n * Get document extraction details\n *\n * @param documentId - The ID of the document\n * @returns Promise with the extraction details\n */\nexport const getDocumentExtractionDetails = async (documentId: string): Promise<any> => {\n  console.log('Getting extraction details for document:', documentId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/documents/${documentId}/extraction-details`);\n\n    if (!response.ok) {\n      console.error(`Failed to get document extraction details: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get document extraction details: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting document extraction details:', error);\n\n    // Return fallback data\n    return {\n      extractedContent: `Sample extracted content for ${documentId}. This is placeholder text because the actual content could not be retrieved from the server.`,\n      extractionMethod: 'Unknown (error occurred)',\n      qualityScore: 0,\n      processingTime: 0,\n      chunks: 0,\n      warnings: ['Failed to retrieve extraction details from server'],\n      fallbackReason: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n\n/**\n * Get document content\n *\n * @param documentId - The ID of the document\n * @returns Promise with the document content\n */\nexport const getDocumentContent = async (documentId: string): Promise<any> => {\n  console.log('Getting content for document:', documentId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/documents/${documentId}/content`);\n\n    if (!response.ok) {\n      console.error(`Failed to get document content: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get document content: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting document content:', error);\n\n    // Return fallback data\n    return {\n      content: `# Document Content Unavailable\\n\\nThe content for document ${documentId} could not be retrieved from the server.\\n\\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\\n\\nPlease try again later or contact support if the problem persists.`,\n      extraction_method: 'Unknown (error occurred)',\n      quality_score: 0,\n      processing_time: 0,\n      chunks_count: 0\n    };\n  }\n};\n\n/**\n * Get website extraction details\n *\n * @param websiteId - The ID of the website\n * @returns Promise with the extraction details\n */\nexport const getWebsiteExtractionDetails = async (websiteId: string): Promise<any> => {\n  console.log('Getting extraction details for website:', websiteId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/websites/${websiteId}/extraction-details`);\n\n    if (!response.ok) {\n      console.error(`Failed to get website extraction details: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get website extraction details: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting website extraction details:', error);\n\n    // Return fallback data\n    return {\n      extractedContent: `Sample extracted content for ${websiteId}. This is placeholder text because the actual content could not be retrieved from the server.`,\n      extractionMethod: 'Unknown (error occurred)',\n      fallbackHistory: [],\n      contentQuality: 0,\n      warnings: ['Failed to retrieve extraction details from server'],\n      processingTime: 0,\n      chunks: 0\n    };\n  }\n};\n\n/**\n * Get website content\n *\n * @param websiteId - The ID of the website\n * @returns Promise with the website content\n */\nexport const getWebsiteContent = async (websiteId: string): Promise<any> => {\n  console.log('Getting content for website:', websiteId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/websites/${websiteId}/content`);\n\n    if (!response.ok) {\n      console.error(`Failed to get website content: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get website content: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting website content:', error);\n\n    // Return fallback data\n    return {\n      content: `# Website Content Unavailable\\n\\nThe content for website ${websiteId} could not be retrieved from the server.\\n\\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\\n\\nPlease try again later or contact support if the problem persists.`,\n      extraction_method: 'Unknown (error occurred)',\n      quality_score: 0,\n      processing_time: 0,\n      pages_processed: 0,\n      total_links: 0\n    };\n  }\n};\n\n/**\n * Submit feedback for an AI answer\n *\n * @param feedbackData - The feedback data containing query, answer, issue type, etc.\n * @returns Promise with the response indicating success or failure\n */\nexport const submitFeedback = async (feedbackData: FeedbackData): Promise<FeedbackResponse> => {\n  console.log('Submitting feedback:', feedbackData);\n\n  try {\n    const response = await fetch(`${API_URL}/api/feedback`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      },\n      body: JSON.stringify(feedbackData)\n    });\n\n    if (!response.ok) {\n      console.error(`Failed to submit feedback: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to submit feedback: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error submitting feedback:', error);\n    return {\n      success: false,\n      message: `Failed to submit feedback: ${error instanceof Error ? error.message : 'Unknown error'}`\n    };\n  }\n};\n\n/**\n * Get the configured feedback notification emails\n *\n * @returns Promise with the list of configured emails\n */\nexport const getFeedbackEmails = async (): Promise<{emails: string[]}> => {\n  try {\n    const response = await fetch(`${API_URL}/api/feedback/emails`);\n\n    if (!response.ok) {\n      console.error(`Failed to get feedback emails: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get feedback emails: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting feedback emails:', error);\n    return {\n      emails: []\n    };\n  }\n};\n\n/**\n * Update the configured feedback notification emails\n *\n * @param emails - The new list of emails to configure for feedback notifications\n * @returns Promise with the response indicating success or failure\n */\nexport const updateFeedbackEmails = async (emails: string[]): Promise<FeedbackResponse> => {\n  try {\n    const response = await fetch(`${API_URL}/api/feedback/emails`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      },\n      body: JSON.stringify({ emails })\n    });\n\n    if (!response.ok) {\n      console.error(`Failed to update feedback emails: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to update feedback emails: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating feedback emails:', error);\n    return {\n      success: false,\n      message: `Failed to update feedback emails: ${error instanceof Error ? error.message : 'Unknown error'}`\n    };\n  }\n};\n\n/**\n * Get all documents from the backend\n * @returns Promise with the list of documents\n */\nexport const getDocuments = async (): Promise<any[]> => {\n  console.log('Fetching documents from backend...');\n\n  try {\n    const response = await fetch(`${API_URL}/api/documents`);\n\n    if (!response.ok) {\n      console.error(`Failed to get documents: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get documents: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting documents:', error);\n    return []; // Return empty array on error\n  }\n};\n\n/**\n * Get all websites from the backend\n * @returns Promise with the list of websites\n */\nexport const getWebsites = async (): Promise<any[]> => {\n  console.log('Fetching websites from backend...');\n\n  try {\n    const response = await fetch(`${API_URL}/api/websites`);\n\n    if (!response.ok) {\n      console.error(`Failed to get websites: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get websites: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting websites:', error);\n    return []; // Return empty array on error\n  }\n};\n\n// ===== CATEGORY MANAGEMENT API FUNCTIONS =====\n\n/**\n * Get all categories with hierarchy\n * @returns Promise with the list of categories\n */\nexport const getCategories = async (): Promise<CategoryHierarchy[]> => {\n  console.log('Fetching categories from backend...');\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`);\n\n    if (!response.ok) {\n      console.error(`Failed to get categories: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting categories:', error);\n    return []; // Return empty array on error\n  }\n};\n\n/**\n * Create a new category\n * @param category - The category data to create\n * @returns Promise with the creation response\n */\nexport const createCategory = async (category: CategoryCreateRequest): Promise<any> => {\n  console.log('Creating category:', category);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(category),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to create category: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error creating category:', error);\n    throw error;\n  }\n};\n\n/**\n * Update a category\n * @param categoryId - The ID of the category to update\n * @param updates - The updates to apply\n * @returns Promise with the update response\n */\nexport const updateCategory = async (categoryId: string, updates: Partial<Category>): Promise<any> => {\n  console.log('Updating category:', categoryId, updates);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(updates),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to update category: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating category:', error);\n    throw error;\n  }\n};\n\n/**\n * Delete a category\n * @param categoryId - The ID of the category to delete\n * @returns Promise with the deletion response\n */\nexport const deleteCategory = async (categoryId: string): Promise<any> => {\n  console.log('Deleting category:', categoryId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'DELETE',\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to delete category: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    throw error;\n  }\n};\n\n/**\n * Update document categories\n * @param documentId - The ID of the document to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const updateDocumentCategories = async (\n  documentId: string,\n  categoryUpdate: DocumentCategoryUpdate\n): Promise<any> => {\n  console.log('Updating document categories:', documentId, categoryUpdate);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/documents/${documentId}/categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(categoryUpdate),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to update document categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating document categories:', error);\n    throw error;\n  }\n};\n\n/**\n * Bulk update document categories\n * @param documentIds - Array of document IDs to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const bulkUpdateDocumentCategories = async (\n  documentIds: string[],\n  categoryUpdate: DocumentCategoryUpdate\n): Promise<any> => {\n  console.log('Bulk updating document categories:', documentIds, categoryUpdate);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/documents/bulk-update-categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        document_ids: documentIds,\n        ...categoryUpdate\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to bulk update document categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error bulk updating document categories:', error);\n    throw error;\n  }\n};\n\n/**\n * Get all website categories\n * @returns Promise with the list of website categories\n */\nexport const getWebsiteCategories = async (): Promise<WebsiteCategory[]> => {\n  console.log('Fetching website categories from backend...');\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/website-categories/`);\n\n    if (!response.ok) {\n      console.error(`Failed to get website categories: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get website categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting website categories:', error);\n    return []; // Return empty array on error\n  }\n};\n\n/**\n * Create a new website category\n * @param category - The website category data to create\n * @returns Promise with the creation response\n */\nexport const createWebsiteCategory = async (category: WebsiteCategoryCreateRequest): Promise<any> => {\n  console.log('Creating website category:', category);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/website-categories/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(category),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to create website category: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error creating website category:', error);\n    throw error;\n  }\n};\n\n/**\n * Update website categories\n * @param websiteId - The ID of the website to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const updateWebsiteCategories = async (\n  websiteId: string,\n  categoryUpdate: WebsiteCategoryUpdate\n): Promise<any> => {\n  console.log('Updating website categories:', websiteId, categoryUpdate);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/websites/${websiteId}/categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(categoryUpdate),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to update website categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating website categories:', error);\n    throw error;\n  }\n};\n\n/**\n * Bulk update website categories\n * @param websiteIds - Array of website IDs to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const bulkUpdateWebsiteCategories = async (\n  websiteIds: string[],\n  categoryUpdate: WebsiteCategoryUpdate\n): Promise<any> => {\n  console.log('Bulk updating website categories:', websiteIds, categoryUpdate);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/websites/bulk-update-categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        website_ids: websiteIds,\n        ...categoryUpdate\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to bulk update website categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error bulk updating website categories:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA;AACA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,YAAY;AAEhD,OAAO,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AA0E/E;;AA2DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAG,MAAAA,CACvBC,KAAa,EACbC,KAAa,GAAG,kBAAkB,EAClCC,aAAqB,GAAG,WAAW,EACnCC,eAAwB,GAAG,IAAI,KACJ;EAC3BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEL,KAAK,EAAE,cAAc,EAAEC,KAAK,CAAC;;EAE9D;EACA;EACA,MAAMK,aAA4B,GAAG;IACnCC,MAAM,EAAE,mCAAmCP,KAAK,6DAA6D;IAC7GQ,eAAe,EAAE,EAAE;IAAE;IACrBC,cAAc,EAAE,EAAE;IAAE;IACpBC,OAAO,EAAE,EAAE;IAAE;IACbC,gBAAgB,EAAE,EAAE;IAAE;IACtBC,eAAe,EAAE,EAAE,CAAC;EACtB,CAAC;;EAED;EACA;EACA,MAAMC,uBAAsC,GAAG;IAC7CN,MAAM,EAAE,qBAAqBZ,OAAO,4FAA4F;IAChIa,eAAe,EAAE,EAAE;IAAE;IACrBC,cAAc,EAAE,EAAE;IAAE;IACpBC,OAAO,EAAE,EAAE;IAAE;IACbC,gBAAgB,EAAE,EAAE;IACpBC,eAAe,EAAE;EACnB,CAAC;;EAED;EACA,IAAI;IACF;IACAR,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEV,OAAO,CAAC;IAEjD,IAAI;MACF;MACA,MAAMmB,WAAW,GAAG,CAAC;MACrB,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAIC,YAAY,GAAGf,KAAK;MACxB,IAAIgB,QAAQ,GAAG,IAAI;MAEnB,OAAOF,YAAY,IAAID,WAAW,EAAE;QAClC;QACAV,OAAO,CAACC,GAAG,CAAC,WAAWU,YAAY,GAAG,CAAC,IAAID,WAAW,GAAG,CAAC,uBAAuBnB,OAAO,yBAAyBqB,YAAY,EAAE,CAAC;;QAEhI;QACA,MAAME,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;QACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;QAE/D,IAAI;UACF;UACAlB,OAAO,CAACC,GAAG,CAAC,iCAAiCV,OAAO,YAAY,CAAC;UAEjEsB,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,YAAY,EAAE;YAC7C6B,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,QAAQ,EAAE;YACZ,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnB5B,KAAK;cACLC,KAAK,EAAEe,YAAY;cACnBa,gBAAgB,EAAE,IAAI;cACtBC,cAAc,EAAE5B,aAAa;cAC7B6B,iBAAiB,EAAE5B,eAAe;cAClC6B,gBAAgB,EAAE;YACpB,CAAC,CAAC;YACFC,MAAM,EAAEf,UAAU,CAACe;UACrB,CAAC,CAAC;;UAEF;UACAC,YAAY,CAACd,SAAS,CAAC;;UAEvB;UACA,IAAIH,QAAQ,CAACkB,EAAE,EAAE;YACf;UACF;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd;UACAF,YAAY,CAACd,SAAS,CAAC;;UAEvB;UACA,IAAIgB,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,YAAY,EAAE;YAChElC,OAAO,CAACmC,IAAI,CAAC,8BAA8BvB,YAAY,mCAAmC,CAAC;UAC7F,CAAC,MAAM;YACLZ,OAAO,CAACgC,KAAK,CAAC,0BAA0BpB,YAAY,GAAG,EAAEoB,KAAK,CAAC;UACjE;;UAEA;UACA,IAAIpB,YAAY,KAAK,kBAAkB,IAAIA,YAAY,KAAK,kBAAkB,EAAE;YAC9EA,YAAY,GAAG,kBAAkB,CAAC,CAAC;UACrC,CAAC,MAAM,IAAIA,YAAY,KAAK,kBAAkB,EAAE;YAC9CA,YAAY,GAAG,kBAAkB,CAAC,CAAC;UACrC;;UAEA;UACA,IAAID,YAAY,KAAKD,WAAW,EAAE;YAChC,MAAMsB,KAAK;UACb;QACF;QAEArB,YAAY,EAAE;MAChB;MAEA,IAAIE,QAAQ,IAAIA,QAAQ,CAACkB,EAAE,EAAE;QAC3B,MAAMK,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;;QAElC;QACA,IAAID,IAAI,IAAI,CAACA,IAAI,CAACE,SAAS,EAAE;UAC3BF,IAAI,CAACE,SAAS,GAAGzC,KAAK;QACxB;;QAEA;QACA,IAAI,CAACuC,IAAI,CAACjC,MAAM,EAAE;UAChBH,OAAO,CAACgC,KAAK,CAAC,sCAAsC,EAAEI,IAAI,CAAC;UAC3D,OAAO;YACL,GAAGlC,aAAa;YAChBC,MAAM,EAAE;UACV,CAAC;QACH;;QAEA;QACA,IAAI;UACF,IAAIX,OAAO,CAACC,GAAG,CAAC8C,sBAAsB,KAAK,MAAM,EAAE;YACjD,MAAMC,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;YACnC,MAAMpD,SAAS,CAAC;cACdqD,UAAU,EAAE/C,KAAK;cACjBgD,WAAW,EAAER,IAAI,CAACjC,MAAM;cACxBmC,SAAS,EAAEzC,KAAK;cAChBS,OAAO,EAAE8B,IAAI,CAAC9B,OAAO,IAAI,EAAE;cAC3BuC,eAAe,EAAE,CAACJ,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,IAAI;YACrD,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAOR,KAAK,EAAE;UACdhC,OAAO,CAACgC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD;QACF;QAEA,OAAOI,IAAI;MACb,CAAC,MAAM,IAAIvB,QAAQ,EAAE;QACnB;QACAb,OAAO,CAACgC,KAAK,CAAC,qBAAqB,EAAEnB,QAAQ,CAACiC,MAAM,EAAEjC,QAAQ,CAACkC,UAAU,CAAC;QAE1E,IAAIC,YAAY;QAChB,IAAI;UACFA,YAAY,GAAG,MAAMnC,QAAQ,CAACoC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVF,YAAY,GAAG,+BAA+B;QAChD;QAEAhD,OAAO,CAACgC,KAAK,CAAC,gBAAgB,EAAEgB,YAAY,CAAC;QAE7C,OAAO;UACL,GAAG9C,aAAa;UAChBC,MAAM,EAAE,SAASU,QAAQ,CAACiC,MAAM,KAAKjC,QAAQ,CAACkC,UAAU,KAAKC,YAAY;QAC3E,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL,GAAG9C,aAAa;UAChBC,MAAM,EAAE;QACV,CAAC;MACH;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,YAAY,EAAE;QAChElC,OAAO,CAACgC,KAAK,CAAC,yBAAyB,CAAC;;QAExC;QACA,IAAImB,cAAc,GAAG,mCAAmC;;QAExD;QACA,IAAItD,KAAK,KAAK,kBAAkB,IAAIA,KAAK,KAAK,kBAAkB,EAAE;UAChEsD,cAAc,IAAI,qEAAqE;QACzF,CAAC,MAAM,IAAItD,KAAK,KAAK,kBAAkB,EAAE;UACvCsD,cAAc,IAAI,8BAA8B;QAClD,CAAC,MAAM;UACLA,cAAc,IAAI,6CAA6C;QACjE;QAEA,OAAO;UACL,GAAGjD,aAAa;UAChBC,MAAM,EAAEgD;QACV,CAAC;MACH;;MAEA;MACAnD,OAAO,CAACgC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAOvB,uBAAuB;IAChC;EACF,CAAC,CAAC,OAAOuB,KAAU,EAAE;IACnBhC,OAAO,CAACgC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAOvB,uBAAuB;EAChC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM2C,cAAc,GAAG,MAAAA,CAC5BC,IAAU,EACVC,UAAkB,GAAG,SAAS,EAC9BC,aAAsB,GAAG,IAAI,EAC7BC,aAAsB,GAAG,IAAI,EAC7BC,aAAsB,GAAG,IAAI,KACD;EAC5BzD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoD,IAAI,CAACnB,IAAI,CAAC;EAE7C,IAAI;IACF;IACA,IAAIwB,gBAAgB,GAAG,IAAI;IAC3B,IAAIC,eAAe,GAAG,IAAI;IAE1B,IAAInE,OAAO,CAACC,GAAG,CAACmE,8BAA8B,KAAK,MAAM,EAAE;MACzD,IAAI;QACF;QACA,MAAMC,SAAS,GAAGC,IAAI,CAACpB,GAAG,CAAC,CAAC;QAC5B,MAAMqB,cAAc,GAAG,GAAGT,UAAU,IAAIO,SAAS,IAAIR,IAAI,CAACnB,IAAI,EAAE;;QAEhE;QACA,MAAM;UAAEE,IAAI,EAAE4B,WAAW;UAAEhC,KAAK,EAAEiC;QAAa,CAAC,GAAG,MAAM5E,QAAQ,CAAC6E,OAAO,CACtEC,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAACL,cAAc,EAAEV,IAAI,EAAE;UAC5BgB,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE;QACV,CAAC,CAAC;QAEJ,IAAIL,YAAY,EAAE;UAChBjE,OAAO,CAACgC,KAAK,CAAC,sCAAsC,EAAEiC,YAAY,CAAC;QACrE,CAAC,MAAM;UACLP,gBAAgB,GAAGM,WAAW,CAACO,IAAI;;UAEnC;UACA,MAAM;YAAEnC,IAAI,EAAE;cAAEoC;YAAU;UAAE,CAAC,GAAGnF,QAAQ,CAAC6E,OAAO,CAC7CC,IAAI,CAAC,WAAW,CAAC,CACjBM,YAAY,CAACf,gBAAgB,CAAC;UAEjCC,eAAe,GAAGa,SAAS;UAC3BxE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyD,gBAAgB,EAAEC,eAAe,CAAC;QACjF;MACF,CAAC,CAAC,OAAOM,YAAY,EAAE;QACrBjE,OAAO,CAACgC,KAAK,CAAC,mCAAmC,EAAEiC,YAAY,CAAC;QAChE;MACF;IACF;;IAEA;IACA,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEvB,IAAI,CAAC;IAC7BqB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEtB,UAAU,CAAC;;IAE1C;IACA,IAAII,gBAAgB,EAAE;MACpBgB,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAElB,gBAAgB,CAAC;MACvDgB,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEjB,eAAe,IAAI,EAAE,CAAC;IAC7D;;IAEA;IACA,MAAM9C,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,sBAAsB,EAAE;MAC7D6B,MAAM,EAAE,MAAM;MACdE,IAAI,EAAEoD;MACN;IACF,CAAC,CAAC;IAEF,IAAI,CAAC7D,QAAQ,CAACkB,EAAE,EAAE;MAChB;MACA,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,OAAO;QACLiC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAEJ,SAAS,CAACE,MAAM,IAAI,kBAAkBlE,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU;MACvF,CAAC;IACH;;IAEA;IACA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClCrC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEmC,IAAI,CAAC;;IAErC;IACA,OAAO;MACL4C,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE7C,IAAI,CAAC6C,OAAO;MACrBC,gBAAgB,EAAE9C,IAAI,CAAC8C,gBAAgB;MACvCC,MAAM,EAAE/C,IAAI,CAAC+C,MAAM;MACnB/C,IAAI,EAAE;QACJgD,EAAE,EAAEhD,IAAI,CAACiD,WAAW,IAAI,OAAOvB,IAAI,CAACpB,GAAG,CAAC,CAAC,EAAE;QAC3C6B,IAAI,EAAEb,gBAAgB,IAAI,cAAcL,IAAI,CAACnB,IAAI,EAAE;QACnDoD,gBAAgB,EAAElD;MACpB;IACF,CAAC;EACH,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO;MACLgD,OAAO,EAAE,KAAK;MACdC,OAAO,EAAEjD,KAAK,YAAYuD,KAAK,GAAGvD,KAAK,CAACiD,OAAO,GAAG;IACpD,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,UAAU,GAAG,MAAAA,CACxBC,GAAW,EACXC,WAAmB,GAAG,SAAS,EAC/BC,iBAAuC,KACX;EAC5B3F,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEwF,GAAG,EAAE,eAAe,EAAEE,iBAAiB,CAAC;EAEvE,IAAI;IACF,MAAMC,OAA0B,GAAG;MACjCH,GAAG;MACHI,YAAY,EAAEH,WAAW;MACzB,GAAGC;IACL,CAAC;;IAED;IACA,MAAM9E,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,kBAAkB,EAAE;MACzD6B,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACoE,OAAO;IAC9B,CAAC,CAAC;IAEF,IAAI,CAAC/E,QAAQ,CAACkB,EAAE,EAAE;MAChB;MACA,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,OAAO;QACLiC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAEJ,SAAS,CAACE,MAAM,IAAI,uBAAuBlE,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU;MAC5F,CAAC;IACH;;IAEA;IACA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClCrC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmC,IAAI,CAAC;;IAE1C;IACA,OAAO;MACL4C,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE7C,IAAI,CAAC6C,OAAO;MACrBC,gBAAgB,EAAE9C,IAAI,CAAC8C,gBAAgB;MACvCC,MAAM,EAAE/C,IAAI,CAAC+C,MAAM;MACnB/C,IAAI,EAAE;QACJgD,EAAE,EAAE,OAAOtB,IAAI,CAACpB,GAAG,CAAC,CAAC,EAAE;QACvB6B,IAAI,EAAEkB,GAAG;QACTH,gBAAgB,EAAElD;MACpB;IACF,CAAC;EACH,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO;MACLgD,OAAO,EAAE,KAAK;MACdC,OAAO,EAAEjD,KAAK,YAAYuD,KAAK,GAAGvD,KAAK,CAACiD,OAAO,GAAG;IACpD,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMa,4BAA4B,GAAG,MAAOC,UAAkB,IAAmB;EACtF/F,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE8F,UAAU,CAAC;EAEnE,IAAI;IACF,MAAMlF,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,kBAAkBwG,UAAU,qBAAqB,CAAC;IAEzF,IAAI,CAAClF,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,8CAA8CnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MACrG,MAAM,IAAIwC,KAAK,CAAC,8CAA8C1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IACtF;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;;IAElE;IACA,OAAO;MACLgE,gBAAgB,EAAE,gCAAgCD,UAAU,+FAA+F;MAC3JE,gBAAgB,EAAE,0BAA0B;MAC5CC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBhB,MAAM,EAAE,CAAC;MACTiB,QAAQ,EAAE,CAAC,mDAAmD,CAAC;MAC/DC,cAAc,EAAErE,KAAK,YAAYuD,KAAK,GAAGvD,KAAK,CAACiD,OAAO,GAAG;IAC3D,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,kBAAkB,GAAG,MAAOP,UAAkB,IAAmB;EAC5E/F,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE8F,UAAU,CAAC;EAExD,IAAI;IACF,MAAMlF,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,kBAAkBwG,UAAU,UAAU,CAAC;IAE9E,IAAI,CAAClF,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,mCAAmCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MAC1F,MAAM,IAAIwC,KAAK,CAAC,mCAAmC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAC3E;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;;IAEvD;IACA,OAAO;MACLuE,OAAO,EAAE,8DAA8DR,UAAU,0DAA0D/D,KAAK,YAAYuD,KAAK,GAAGvD,KAAK,CAACiD,OAAO,GAAG,eAAe,wEAAwE;MAC3QuB,iBAAiB,EAAE,0BAA0B;MAC7CC,aAAa,EAAE,CAAC;MAChB5D,eAAe,EAAE,CAAC;MAClB6D,YAAY,EAAE;IAChB,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,2BAA2B,GAAG,MAAOC,SAAiB,IAAmB;EACpF5G,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE2G,SAAS,CAAC;EAEjE,IAAI;IACF,MAAM/F,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,iBAAiBqH,SAAS,qBAAqB,CAAC;IAEvF,IAAI,CAAC/F,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,6CAA6CnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MACpG,MAAM,IAAIwC,KAAK,CAAC,6CAA6C1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IACrF;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;IAEjE;IACA,OAAO;MACLgE,gBAAgB,EAAE,gCAAgCY,SAAS,+FAA+F;MAC1JX,gBAAgB,EAAE,0BAA0B;MAC5CY,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,CAAC;MACjBV,QAAQ,EAAE,CAAC,mDAAmD,CAAC;MAC/DD,cAAc,EAAE,CAAC;MACjBhB,MAAM,EAAE;IACV,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4B,iBAAiB,GAAG,MAAOH,SAAiB,IAAmB;EAC1E5G,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE2G,SAAS,CAAC;EAEtD,IAAI;IACF,MAAM/F,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,iBAAiBqH,SAAS,UAAU,CAAC;IAE5E,IAAI,CAAC/F,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,kCAAkCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MACzF,MAAM,IAAIwC,KAAK,CAAC,kCAAkC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAC1E;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;IAEtD;IACA,OAAO;MACLuE,OAAO,EAAE,4DAA4DK,SAAS,0DAA0D5E,KAAK,YAAYuD,KAAK,GAAGvD,KAAK,CAACiD,OAAO,GAAG,eAAe,wEAAwE;MACxQuB,iBAAiB,EAAE,0BAA0B;MAC7CC,aAAa,EAAE,CAAC;MAChB5D,eAAe,EAAE,CAAC;MAClBmE,eAAe,EAAE,CAAC;MAClBC,WAAW,EAAE;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAG,MAAOC,YAA0B,IAAgC;EAC7FnH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEkH,YAAY,CAAC;EAEjD,IAAI;IACF,MAAMtG,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,eAAe,EAAE;MACtD6B,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE;MACZ,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC2F,YAAY;IACnC,CAAC,CAAC;IAEF,IAAI,CAACtG,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,8BAA8BnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MACrF,MAAM,IAAIwC,KAAK,CAAC,8BAA8B1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IACtE;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO;MACLgD,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,8BAA8BjD,KAAK,YAAYuD,KAAK,GAAGvD,KAAK,CAACiD,OAAO,GAAG,eAAe;IACjG,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmC,iBAAiB,GAAG,MAAAA,CAAA,KAAyC;EACxE,IAAI;IACF,MAAMvG,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,sBAAsB,CAAC;IAE9D,IAAI,CAACsB,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,kCAAkCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MACzF,MAAM,IAAIwC,KAAK,CAAC,kCAAkC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAC1E;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,OAAO;MACLqF,MAAM,EAAE;IACV,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAOD,MAAgB,IAAgC;EACzF,IAAI;IACF,MAAMxG,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,sBAAsB,EAAE;MAC7D6B,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE;MACZ,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE6F;MAAO,CAAC;IACjC,CAAC,CAAC;IAEF,IAAI,CAACxG,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,qCAAqCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MAC5F,MAAM,IAAIwC,KAAK,CAAC,qCAAqC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAC7E;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,OAAO;MACLgD,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,qCAAqCjD,KAAK,YAAYuD,KAAK,GAAGvD,KAAK,CAACiD,OAAO,GAAG,eAAe;IACxG,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMsC,YAAY,GAAG,MAAAA,CAAA,KAA4B;EACtDvH,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;EAEjD,IAAI;IACF,MAAMY,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,gBAAgB,CAAC;IAExD,IAAI,CAACsB,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,4BAA4BnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MACnF,MAAM,IAAIwC,KAAK,CAAC,4BAA4B1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IACpE;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO,EAAE,CAAC,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMwF,WAAW,GAAG,MAAAA,CAAA,KAA4B;EACrDxH,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;EAEhD,IAAI;IACF,MAAMY,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,eAAe,CAAC;IAEvD,IAAI,CAACsB,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,2BAA2BnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MAClF,MAAM,IAAIwC,KAAK,CAAC,2BAA2B1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IACnE;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,OAAO,EAAE,CAAC,CAAC;EACb;AACF,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMyF,aAAa,GAAG,MAAAA,CAAA,KAA0C;EACrEzH,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EAElD,IAAI;IACF,MAAMY,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,kBAAkB,CAAC;IAE1D,IAAI,CAACsB,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,6BAA6BnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MACpF,MAAM,IAAIwC,KAAK,CAAC,6BAA6B1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IACrE;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,EAAE,CAAC,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0F,cAAc,GAAG,MAAOC,QAA+B,IAAmB;EACrF3H,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0H,QAAQ,CAAC;EAE3C,IAAI;IACF,MAAM9G,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,kBAAkB,EAAE;MACzD6B,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACmG,QAAQ;IAC/B,CAAC,CAAC;IAEF,IAAI,CAAC9G,QAAQ,CAACkB,EAAE,EAAE;MAChB,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAIwC,KAAK,CAACV,SAAS,CAACE,MAAM,IAAI,8BAA8BlE,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAC1F;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4F,cAAc,GAAG,MAAAA,CAAOC,UAAkB,EAAEC,OAA0B,KAAmB;EACpG9H,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4H,UAAU,EAAEC,OAAO,CAAC;EAEtD,IAAI;IACF,MAAMjH,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,mBAAmBsI,UAAU,EAAE,EAAE;MACtEzG,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACsG,OAAO;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACjH,QAAQ,CAACkB,EAAE,EAAE;MAChB,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAIwC,KAAK,CAACV,SAAS,CAACE,MAAM,IAAI,8BAA8BlE,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAC1F;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+F,cAAc,GAAG,MAAOF,UAAkB,IAAmB;EACxE7H,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4H,UAAU,CAAC;EAE7C,IAAI;IACF,MAAMhH,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,mBAAmBsI,UAAU,EAAE,EAAE;MACtEzG,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI,CAACP,QAAQ,CAACkB,EAAE,EAAE;MAChB,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAIwC,KAAK,CAACV,SAAS,CAACE,MAAM,IAAI,8BAA8BlE,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAC1F;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgG,wBAAwB,GAAG,MAAAA,CACtCjC,UAAkB,EAClBkC,cAAsC,KACrB;EACjBjI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE8F,UAAU,EAAEkC,cAAc,CAAC;EAExE,IAAI;IACF,MAAMpH,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,6BAA6BwG,UAAU,aAAa,EAAE;MAC3F3E,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACyG,cAAc;IACrC,CAAC,CAAC;IAEF,IAAI,CAACpH,QAAQ,CAACkB,EAAE,EAAE;MAChB,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAIwC,KAAK,CAACV,SAAS,CAACE,MAAM,IAAI,yCAAyClE,QAAQ,CAACkC,UAAU,EAAE,CAAC;IACrG;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC3D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkG,4BAA4B,GAAG,MAAAA,CAC1CC,WAAqB,EACrBF,cAAsC,KACrB;EACjBjI,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEkI,WAAW,EAAEF,cAAc,CAAC;EAE9E,IAAI;IACF,MAAMpH,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,kDAAkD,EAAE;MACzF6B,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnB4G,YAAY,EAAED,WAAW;QACzB,GAAGF;MACL,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAACpH,QAAQ,CAACkB,EAAE,EAAE;MAChB,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAIwC,KAAK,CAACV,SAAS,CAACE,MAAM,IAAI,8CAA8ClE,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAC1G;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IAChE,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMqG,oBAAoB,GAAG,MAAAA,CAAA,KAAwC;EAC1ErI,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAE1D,IAAI;IACF,MAAMY,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,qCAAqC,CAAC;IAE7E,IAAI,CAACsB,QAAQ,CAACkB,EAAE,EAAE;MAChB/B,OAAO,CAACgC,KAAK,CAAC,qCAAqCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC;MAC5F,MAAM,IAAIwC,KAAK,CAAC,qCAAqC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAC7E;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,OAAO,EAAE,CAAC,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsG,qBAAqB,GAAG,MAAOX,QAAsC,IAAmB;EACnG3H,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE0H,QAAQ,CAAC;EAEnD,IAAI;IACF,MAAM9G,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,qCAAqC,EAAE;MAC5E6B,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACmG,QAAQ;IAC/B,CAAC,CAAC;IAEF,IAAI,CAAC9G,QAAQ,CAACkB,EAAE,EAAE;MAChB,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAIwC,KAAK,CAACV,SAAS,CAACE,MAAM,IAAI,sCAAsClE,QAAQ,CAACkC,UAAU,EAAE,CAAC;IAClG;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuG,uBAAuB,GAAG,MAAAA,CACrC3B,SAAiB,EACjBqB,cAAqC,KACpB;EACjBjI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE2G,SAAS,EAAEqB,cAAc,CAAC;EAEtE,IAAI;IACF,MAAMpH,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,4BAA4BqH,SAAS,aAAa,EAAE;MACzFxF,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACyG,cAAc;IACrC,CAAC,CAAC;IAEF,IAAI,CAACpH,QAAQ,CAACkB,EAAE,EAAE;MAChB,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAIwC,KAAK,CAACV,SAAS,CAACE,MAAM,IAAI,wCAAwClE,QAAQ,CAACkC,UAAU,EAAE,CAAC;IACpG;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMwG,2BAA2B,GAAG,MAAAA,CACzCC,UAAoB,EACpBR,cAAqC,KACpB;EACjBjI,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEwI,UAAU,EAAER,cAAc,CAAC;EAE5E,IAAI;IACF,MAAMpH,QAAQ,GAAG,MAAMM,KAAK,CAAC,GAAG5B,OAAO,iDAAiD,EAAE;MACxF6B,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBkH,WAAW,EAAED,UAAU;QACvB,GAAGR;MACL,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAACpH,QAAQ,CAACkB,EAAE,EAAE;MAChB,MAAM8C,SAAS,GAAG,MAAMhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,OAAO;QAAEC,MAAM,EAAElE,QAAQ,CAACkC;MAAW,CAAC,CAAC,CAAC;MACtF,MAAM,IAAIwC,KAAK,CAACV,SAAS,CAACE,MAAM,IAAI,6CAA6ClE,QAAQ,CAACkC,UAAU,EAAE,CAAC;IACzG;IAEA,MAAMX,IAAI,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhC,OAAO,CAACgC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAC/D,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}