"""
Simple test script to check if the search thresholds are working correctly.
"""
import os
import logging
import json
from typing import List, Dict, Any
import numpy as np
from dotenv import load_dotenv
import llm_router
from vector_search import search_documents, search_websites

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def generate_embedding(text: str) -> List[float]:
    """Generate embedding for text using the LLM router."""
    try:
        return llm_router.generate_embedding(text)
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Try with default model
        try:
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Use a deterministic random embedding as last resort
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def test_search_thresholds():
    """Test if the search thresholds are working correctly."""
    logger.info("Testing search thresholds...")
    
    # Test queries
    test_queries = [
        "What is the full form of ACP?",
        "What is the full form of FSDS?",
        "What is the Rapid Response app?",
        "What is VASP and who developed it?"
    ]
    
    for query in test_queries:
        logger.info(f"\n=== Testing query: '{query}' ===\n")
        
        # Generate embedding for the query
        query_embedding = generate_embedding(query)
        
        # Test document search with different thresholds
        thresholds = [0.5, 0.2, 0.1, 0.05, 0.01]
        
        for threshold in thresholds:
            logger.info(f"Testing document search with threshold {threshold}...")
            
            # Test document search
            doc_results = search_documents(
                query_embedding=query_embedding,
                query_text=query,
                use_hybrid_search=True,
                match_threshold=threshold,
                match_count=10
            )
            
            logger.info(f"Document search with threshold {threshold} found {len(doc_results)} results")
            
            if doc_results:
                # Print top result
                top_result = doc_results[0]
                similarity = top_result.get("similarity", 0)
                text_snippet = top_result.get("text", "")[:100] + "..." if top_result.get("text") else "..."
                logger.info(f"Top result similarity: {similarity:.4f}")
                logger.info(f"Text snippet: {text_snippet}")
        
        # Test website search with different thresholds
        for threshold in thresholds:
            logger.info(f"Testing website search with threshold {threshold}...")
            
            # Test website search
            web_results = search_websites(
                query_embedding=query_embedding,
                query_text=query,
                use_hybrid_search=True,
                match_threshold=threshold,
                match_count=10
            )
            
            logger.info(f"Website search with threshold {threshold} found {len(web_results)} results")
            
            if web_results:
                # Print top result
                top_result = web_results[0]
                similarity = top_result.get("similarity", 0)
                text_snippet = top_result.get("text", "")[:100] + "..." if top_result.get("text") else "..."
                logger.info(f"Top result similarity: {similarity:.4f}")
                logger.info(f"Text snippet: {text_snippet}")
        
        logger.info("\n==================================================\n")

def main():
    """Main function."""
    test_search_thresholds()

if __name__ == "__main__":
    main()
