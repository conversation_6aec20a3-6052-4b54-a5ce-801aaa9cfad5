# RailGPT Environment Configuration
# Copy this file to .env and fill in your actual values

# ==============================================
# SUPABASE CONFIGURATION (Required)
# ==============================================
SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co
SUPABASE_KEY=your_service_key_here
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA

# ==============================================
# AI MODEL API KEYS (At least one required)
# ==============================================

# Gemini API Key (Recommended - Free tier available)
# Get from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API Key (Optional - Paid service)
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Groq API Key (Optional - Fast inference)
# Get from: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here

# ==============================================
# APPLICATION SETTINGS
# ==============================================
API_HOST=0.0.0.0
API_PORT=8000
ENVIRONMENT=production
LOG_LEVEL=INFO

# ==============================================
# VECTOR DATABASE SETTINGS
# ==============================================
USE_SUPABASE=true
VECTOR_DIMENSION=768

# ==============================================
# SEARCH CONFIGURATION
# ==============================================
DOCUMENT_PRIORITY_WEIGHT=1.5
WEBSITE_PRIORITY_WEIGHT=1.2
RELEVANCE_THRESHOLD=0.2

# ==============================================
# DEFAULT USER CONFIGURATION
# ==============================================
DEFAULT_USER_ID=a7fbeebf-9025-4a39-aefb-e128ccb6060f

# ==============================================
# FRONTEND CONFIGURATION (for React app)
# ==============================================
REACT_APP_API_URL=http://localhost:8000
REACT_APP_SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA

# ==============================================
# OPTIONAL: REDIS CONFIGURATION
# ==============================================
# REDIS_URL=redis://localhost:6379

# ==============================================
# OPTIONAL: EMAIL CONFIGURATION
# ==============================================
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your_app_password

# ==============================================
# OPTIONAL: FILE STORAGE CONFIGURATION
# ==============================================
# MAX_FILE_SIZE=200MB
# ALLOWED_FILE_TYPES=pdf,docx,txt,md

# ==============================================
# PRODUCTION ONLY SETTINGS
# ==============================================
# DOMAIN=your-domain.com
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# ==============================================
# MONITORING AND ANALYTICS (Optional)
# ==============================================
# SENTRY_DSN=your_sentry_dsn_here
# GOOGLE_ANALYTICS_ID=your_ga_id_here

# ==============================================
# NOTES:
# ==============================================
# 1. Replace all "your_*_here" values with actual credentials
# 2. Keep this file secure and never commit it to version control
# 3. For production, use your hosting platform's environment variable system
# 4. The Supabase keys provided are for the existing project
# 5. At minimum, you need GEMINI_API_KEY to run the application
