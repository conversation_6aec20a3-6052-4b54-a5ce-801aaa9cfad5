"""
<PERSON><PERSON><PERSON> to fix the search functions in the database for RailGPT.
This script will:
1. Create or update the vector search functions in the database
2. Test the functions with sample queries
"""
import os
import logging
import json
from typing import List, Dict, Any
import numpy as np
from dotenv import load_dotenv
from supabase_client import supabase
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def create_search_functions():
    """Create or update the vector search functions in the database."""
    logger.info("Creating or updating vector search functions in the database...")
    
    # Direct search for document chunks
    direct_search_doc_function = """
    CREATE OR REPLACE FUNCTION direct_search_document_chunks(
        query_embedding vector,
        match_threshold float DEFAULT 0.2,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        document_id uuid,
        chunk_index int,
        page_number int,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.embedding,
            dc.metadata,
            dc.created_at,
            dc.updated_at,
            1 - (dc.embedding <=> query_embedding) AS similarity
        FROM
            document_chunks dc
        WHERE
            dc.embedding IS NOT NULL
            AND 1 - (dc.embedding <=> query_embedding) > match_threshold
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Hybrid search for document chunks
    hybrid_search_doc_function = """
    CREATE OR REPLACE FUNCTION hybrid_search_document_chunks(
        query_text text,
        query_embedding vector,
        match_threshold float DEFAULT 0.2,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        document_id uuid,
        chunk_index int,
        page_number int,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.embedding,
            dc.metadata,
            dc.created_at,
            dc.updated_at,
            (1 - (dc.embedding <=> query_embedding)) * 0.8 +
            (ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text)) * 0.2) AS similarity
        FROM
            document_chunks dc
        WHERE
            dc.embedding IS NOT NULL
            AND (1 - (dc.embedding <=> query_embedding)) > match_threshold
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Direct search for website chunks
    direct_search_web_function = """
    CREATE OR REPLACE FUNCTION direct_search_website_chunks(
        query_embedding vector,
        match_threshold float DEFAULT 0.15,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        website_id uuid,
        url text,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            wc.id,
            wc.website_id,
            wc.url,
            wc.text,
            wc.embedding,
            wc.metadata,
            wc.created_at,
            wc.updated_at,
            1 - (wc.embedding <=> query_embedding) AS similarity
        FROM
            website_chunks wc
        WHERE
            wc.embedding IS NOT NULL
            AND 1 - (wc.embedding <=> query_embedding) > match_threshold
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Hybrid search for website chunks
    hybrid_search_web_function = """
    CREATE OR REPLACE FUNCTION hybrid_search_website_chunks(
        query_text text,
        query_embedding vector,
        match_threshold float DEFAULT 0.15,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        website_id uuid,
        url text,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            wc.id,
            wc.website_id,
            wc.url,
            wc.text,
            wc.embedding,
            wc.metadata,
            wc.created_at,
            wc.updated_at,
            (1 - (wc.embedding <=> query_embedding)) * 0.8 +
            (ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', query_text)) * 0.2) AS similarity
        FROM
            website_chunks wc
        WHERE
            wc.embedding IS NOT NULL
            AND (1 - (wc.embedding <=> query_embedding)) > match_threshold
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Execute the function creation queries
    functions = [
        ("direct_search_document_chunks", direct_search_doc_function),
        ("hybrid_search_document_chunks", hybrid_search_doc_function),
        ("direct_search_website_chunks", direct_search_web_function),
        ("hybrid_search_website_chunks", hybrid_search_web_function)
    ]
    
    for name, query in functions:
        try:
            result = supabase.execute_query(query)
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error creating function {name}: {result['error']}")
            else:
                logger.info(f"Successfully created or updated function {name}")
        except Exception as e:
            logger.error(f"Error creating function {name}: {str(e)}")

def test_search_functions():
    """Test the search functions with sample queries."""
    logger.info("Testing search functions with sample queries...")
    
    # Test direct search for document chunks
    direct_doc_query = """
    SELECT * FROM direct_search_document_chunks(
        '[0.1, 0.2, 0.3]'::vector,
        0.01,
        5
    );
    """
    
    try:
        result = supabase.execute_query(direct_doc_query)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing direct_search_document_chunks: {result['error']}")
        else:
            logger.info(f"direct_search_document_chunks returned {len(result)} results")
            for i, item in enumerate(result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
    except Exception as e:
        logger.error(f"Error testing direct_search_document_chunks: {str(e)}")
    
    # Test hybrid search for document chunks
    hybrid_doc_query = """
    SELECT * FROM hybrid_search_document_chunks(
        'test query',
        '[0.1, 0.2, 0.3]'::vector,
        0.01,
        5
    );
    """
    
    try:
        result = supabase.execute_query(hybrid_doc_query)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing hybrid_search_document_chunks: {result['error']}")
        else:
            logger.info(f"hybrid_search_document_chunks returned {len(result)} results")
            for i, item in enumerate(result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
    except Exception as e:
        logger.error(f"Error testing hybrid_search_document_chunks: {str(e)}")
    
    # Test direct search for website chunks
    direct_web_query = """
    SELECT * FROM direct_search_website_chunks(
        '[0.1, 0.2, 0.3]'::vector,
        0.01,
        5
    );
    """
    
    try:
        result = supabase.execute_query(direct_web_query)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing direct_search_website_chunks: {result['error']}")
        else:
            logger.info(f"direct_search_website_chunks returned {len(result)} results")
            for i, item in enumerate(result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
    except Exception as e:
        logger.error(f"Error testing direct_search_website_chunks: {str(e)}")
    
    # Test hybrid search for website chunks
    hybrid_web_query = """
    SELECT * FROM hybrid_search_website_chunks(
        'test query',
        '[0.1, 0.2, 0.3]'::vector,
        0.01,
        5
    );
    """
    
    try:
        result = supabase.execute_query(hybrid_web_query)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing hybrid_search_website_chunks: {result['error']}")
        else:
            logger.info(f"hybrid_search_website_chunks returned {len(result)} results")
            for i, item in enumerate(result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
    except Exception as e:
        logger.error(f"Error testing hybrid_search_website_chunks: {str(e)}")

def main():
    """Main function to fix the search functions in the database."""
    # Create or update search functions
    create_search_functions()
    
    # Test search functions
    test_search_functions()

if __name__ == "__main__":
    main()
