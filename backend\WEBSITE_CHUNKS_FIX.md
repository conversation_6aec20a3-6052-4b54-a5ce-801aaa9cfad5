# Website Chunks Fix

This document explains how to fix the issue with website chunks not showing up in Supabase despite the success message.

## Problem

When adding a website through the `/api/add-website` endpoint, the website metadata is correctly stored in the `websites` table, but the website chunks are not being stored in the `website_chunks` table. This happens because:

1. The `add_website` function in `server.py` was not explicitly storing the chunks in Supabase
2. The `vector_db.add_chunks()` method assumes the chunks have already been stored in Supabase when `USE_SUPABASE` is true
3. There might be issues with the `website_chunks` table structure

## Solution

We've implemented the following fixes:

1. Modified the `add_website` function in `server.py` to explicitly store each chunk in Supabase using `supabase.store_website_chunk()`
2. Created a script to check and fix the `website_chunks` table structure (`check_website_chunks_table.py`)
3. Created a utility script to fix website chunks for existing websites (`fix_website_chunks.py`)

## How to Apply the Fix

### 1. Check and Fix the Table Structure

Run the following command to check and fix the `website_chunks` table structure:

```bash
cd backend
python check_website_chunks_table.py
```

This script will:
- Check if the `website_chunks` table exists
- Check if all required columns are present
- Add any missing columns
- Create necessary indexes for better performance

### 2. Fix Website Chunks for Existing Websites

If you have existing websites that need to be fixed, run:

```bash
cd backend
python fix_website_chunks.py
```

This will:
- Get all websites from the `websites` table
- Extract content from each website
- Store the chunks in the `website_chunks` table

To fix a specific website, provide the URL as an argument:

```bash
python fix_website_chunks.py https://example.com
```

### 3. Test the Fix

After applying the fixes, try adding a new website through the UI or API and verify that:

1. The website metadata is stored in the `websites` table
2. The website chunks are stored in the `website_chunks` table
3. The website chunks appear in search results

You can use the debug endpoint to check if website chunks are being stored correctly:

```
GET /api/debug/website-chunks
```

## Technical Details

### Modified Files

1. `server.py`: Updated the `add_website` function to explicitly store chunks in Supabase
2. Added new utility scripts:
   - `check_website_chunks_table.py`: Checks and fixes the table structure
   - `fix_website_chunks.py`: Fixes website chunks for existing websites

### Database Schema

The `website_chunks` table should have the following columns:

- `id`: UUID (primary key)
- `website_id`: UUID (foreign key to websites.id)
- `chunk_index`: Integer
- `text`: Text
- `embedding`: Vector(768)
- `metadata`: JSONB
- `created_at`: Timestamp with time zone
- `source_type`: Text (default: 'website')

### Indexes

The following indexes should be created for better performance:

- `idx_website_chunks_website_id`: Index on website_id
- `idx_website_chunks_text_gin`: GIN index on text for full-text search
- `idx_website_chunks_embedding`: IVFFlat index on embedding for vector search

## Troubleshooting

If you still encounter issues after applying the fixes:

1. Check the logs for any errors during website extraction or chunk storage
2. Verify that the `website_chunks` table has the correct structure
3. Make sure the `supabase.store_website_chunk()` function is working correctly
4. Check if the vector search functions are using the correct table and columns

If you need to manually fix a specific website, use the `fix_website_chunks.py` script with the URL as an argument.

## Contact

If you have any questions or need further assistance, please contact the development team.
