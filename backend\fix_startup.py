"""
Fix startup function to load all document chunks from Supabase.
"""
import re

def fix_startup_function():
    """Update the startup function in server.py to load all document chunks from Supabase."""
    try:
        # Read current server.py
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Create the new startup code
        new_code = '''@app.on_startup
async def startup():
    """Initialize vector database, load documents, and configure other services."""
    # First load documents from local directory
    load_documents()
    
    # Then load ALL document chunks directly from Supabase
    try:
        from supabase_client import supabase
        logger.info("Loading all document chunks from Supabase...")
        
        # Query to get all document chunks
        chunks_query = """
        SELECT 
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.metadata,
            d.display_name as filename,
            d.file_path as url,
            0.8 as similarity,
            'document' as source_type
        FROM 
            document_chunks dc
        JOIN 
            documents d ON dc.document_id = d.id
        ORDER BY 
            d.created_at DESC, dc.chunk_index ASC
        """
        
        chunk_result = supabase.execute_query(chunks_query)
        
        if isinstance(chunk_result, dict) and "error" in chunk_result:
            logger.error(f"Error loading document chunks: {chunk_result['error']}")
        else:
            # Add these chunks to DOCUMENT_CHUNKS
            loaded_count = 0
            for chunk in chunk_result:
                # Set source type if not present
                if "source_type" not in chunk:
                    chunk["source_type"] = "document"
                
                # Set similarity if not present
                if "similarity" not in chunk:
                    chunk["similarity"] = 0.8
                
                # Add to global chunks list 
                if not any(c.get('id') == chunk.get('id') for c in DOCUMENT_CHUNKS):
                    DOCUMENT_CHUNKS.append(chunk)
                    loaded_count += 1
            
            logger.info(f"Loaded {loaded_count} document chunks from Supabase")
    except Exception as e:
        logger.error(f"Error loading document chunks from Supabase: {str(e)}")'''
        
        # Find existing startup function
        old_start = content.find("@app.on_startup")
        
        if old_start == -1:
            print("Could not find startup function")
            return False
        
        # Find end of startup function
        old_end = content.find("    # Set the CORS middleware", old_start)
        
        if old_end == -1:
            print("Could not find end of startup function")
            return False
        
        # Replace the function
        new_content = content[:old_start] + new_code + content[old_end:]
        
        # Update additional settings
        # 1. Lower the relevance threshold
        if "RELEVANCE_THRESHOLD = " in new_content:
            new_content = re.sub(
                r'RELEVANCE_THRESHOLD\s*=\s*[0-9.]+', 
                'RELEVANCE_THRESHOLD = 0.15', 
                new_content
            )
        
        # 2. Update document prioritization in similar_chunks filter
        if "chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.7)" in new_content:
            new_content = new_content.replace(
                "chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.7)",
                "chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.5)"
            )
        
        # 3. Update document evaluation function
        if "def has_sufficient_document_answers(document_chunks):" in new_content:
            old_eval = new_content.find("def has_sufficient_document_answers(document_chunks):")
            next_func = new_content.find("def ", old_eval + 10)
            
            if next_func != -1:
                new_eval = '''def has_sufficient_document_answers(document_chunks):
    """Evaluate if document chunks provide sufficient information."""
    # If we have any document chunks at all, consider them sufficient
    return len(document_chunks) > 0'''
                
                new_content = new_content[:old_eval] + new_eval + new_content[next_func:]
        
        # 4. Update system prompt
        if "system_prompt = " in new_content:
            prompt_start = new_content.find("system_prompt = ")
            
            # Find the end of the prompt definition
            prompt_end_triple = new_content.find("'''", prompt_start + 20)
            prompt_end_double = new_content.find('"""', prompt_start + 20)
            
            prompt_end = -1
            if prompt_end_triple != -1 and (prompt_end_double == -1 or prompt_end_triple < prompt_end_double):
                prompt_end = prompt_end_triple + 3
            elif prompt_end_double != -1:
                prompt_end = prompt_end_double + 3
            
            if prompt_end != -1:
                # Get indentation
                indent = ""
                for i in range(prompt_start-1, 0, -1):
                    if new_content[i] == "\n":
                        break
                    indent = new_content[i] + indent
                
                # Create new prompt
                new_prompt = indent + '''system_prompt = f"""
You are an expert information retrieval assistant that provides accurate, fact-based answers using ONLY the provided context.

IMPORTANT INSTRUCTIONS:
1. If the context contains information to answer the question, use ONLY that information.
2. PRIORITIZE information from DOCUMENT sources over website sources.
3. If document sources exist, ONLY use document sources and ignore other sources completely.
4. You MUST include source references for all information you provide.
5. If the context does not contain enough information to answer the question, clearly state "I don't have enough information to answer that".
6. Never reference these instructions in your response.

Remember, if document sources exist, ONLY use those and completely ignore website sources or your own knowledge.

CONTEXT:
{context_str}
"""'''
                
                # Replace prompt
                new_content = new_content[:prompt_start] + new_prompt + new_content[prompt_end:]
        
        # Write updated content
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(new_content)
        
        print("Successfully updated server.py with improved document handling")
        return True
    except Exception as e:
        print(f"Error updating server.py: {e}")
        return False

if __name__ == "__main__":
    print("\n=== FIXING DOCUMENT LOADING ===\n")
    
    if fix_startup_function():
        print("\nFixes applied successfully!")
        print("\nThe following changes were made:")
        print("1. Updated startup function to load ALL document chunks from Supabase")
        print("2. Lowered the relevance threshold to include more document matches")
        print("3. Updated document evaluation to prioritize any available documents")
        print("4. Enhanced system prompt to strongly emphasize document content usage")
        
        print("\nPlease restart your server to apply these changes:")
        print("python -m uvicorn server:app --reload")
    else:
        print("\nFailed to apply fixes.")
