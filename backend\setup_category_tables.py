#!/usr/bin/env python3
"""
Setup script to create category management tables in Supabase
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supabase_client import SupabaseClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_category_tables():
    """Create the category management tables and populate with initial data"""
    
    print("🚀 Setting up Category Management Tables")
    print("=" * 50)
    
    client = SupabaseClient()
    
    # Step 1: Create categories table
    print("\n1. Creating categories table...")
    try:
        # Use the Supabase client directly for table creation
        result = client.supabase.table("categories").select("id").limit(1).execute()
        print("✅ Categories table already exists")
    except Exception as e:
        print(f"❌ Categories table doesn't exist, need to create it")
        print(f"   Error: {str(e)}")
        return False
    
    # Step 2: Create website_categories table
    print("\n2. Creating website_categories table...")
    try:
        result = client.supabase.table("website_categories").select("id").limit(1).execute()
        print("✅ Website categories table already exists")
    except Exception as e:
        print(f"❌ Website categories table doesn't exist, need to create it")
        print(f"   Error: {str(e)}")
        return False
    
    # Step 3: Check if categories are populated
    print("\n3. Checking category data...")
    try:
        result = client.supabase.table("categories").select("*").execute()
        category_count = len(result.data) if result.data else 0
        print(f"✅ Found {category_count} categories in database")
        
        if category_count == 0:
            print("📝 Database tables exist but are empty")
            print("   Run the populate_categories.sql script to add initial data")
        else:
            # Show some sample categories
            for i, cat in enumerate(result.data[:5]):
                print(f"   - {cat.get('name', 'Unknown')} ({cat.get('type', 'Unknown')})")
            if category_count > 5:
                print(f"   ... and {category_count - 5} more")
                
    except Exception as e:
        print(f"❌ Error checking categories: {str(e)}")
        return False
    
    # Step 4: Check website categories
    print("\n4. Checking website category data...")
    try:
        result = client.supabase.table("website_categories").select("*").execute()
        website_cat_count = len(result.data) if result.data else 0
        print(f"✅ Found {website_cat_count} website categories in database")
        
        if website_cat_count > 0:
            for cat in result.data[:3]:
                print(f"   - {cat.get('name', 'Unknown')}")
                
    except Exception as e:
        print(f"❌ Error checking website categories: {str(e)}")
        return False
    
    # Step 5: Test category hierarchy view
    print("\n5. Testing category hierarchy view...")
    try:
        # Try to access the view through the REST API
        result = client.supabase.rpc('get_category_path', {'category_id': 'test'}).execute()
        print("✅ Category hierarchy functions are available")
    except Exception as e:
        print(f"❌ Category hierarchy view/function not available: {str(e)}")
        print("   This is expected if the view hasn't been created yet")
    
    print("\n" + "=" * 50)
    print("🎉 Category Management Setup Check Complete!")
    
    if category_count > 0:
        print("\n✅ Your database is ready for category management!")
        print("   You can now use the category management features in RailGPT.")
    else:
        print("\n📋 Next Steps:")
        print("   1. Run the populate_categories.sql script to add initial categories")
        print("   2. Create the category_hierarchy view and utility functions")
        print("   3. Test the category management API endpoints")
    
    return True

def test_basic_operations():
    """Test basic category operations"""
    print("\n🧪 Testing Basic Category Operations")
    print("=" * 50)
    
    client = SupabaseClient()
    
    try:
        # Test reading categories
        result = client.supabase.table("categories").select("id, name, type").limit(5).execute()
        if result.data:
            print("✅ Can read categories from database")
            for cat in result.data:
                print(f"   - {cat.get('name')} ({cat.get('type')})")
        else:
            print("ℹ️  No categories found in database")
            
        # Test reading website categories
        result = client.supabase.table("website_categories").select("id, name").limit(3).execute()
        if result.data:
            print("\n✅ Can read website categories from database")
            for cat in result.data:
                print(f"   - {cat.get('name')}")
        else:
            print("\nℹ️  No website categories found in database")
            
    except Exception as e:
        print(f"❌ Error testing basic operations: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("🔧 Category Management Database Setup")
    print("=" * 70)
    
    # Run setup check
    setup_success = setup_category_tables()
    
    if setup_success:
        # Run basic tests
        test_basic_operations()
    
    print("\n✨ Setup check completed!")
