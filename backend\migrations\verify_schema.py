"""
Schema verification script to check that source_type is properly defined in the database.

This script will:
1. Check if source_type column exists in document_chunks and website_chunks tables
2. Verify data types and constraints
3. Print a summary of the schema validation
"""

import os
import sys
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from backend
sys.path.append(str(Path(__file__).parent.parent))

# Import necessary modules
from supabase_client import supabase
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def verify_schema():
    """Verify that source_type is properly defined in the database schema."""
    logger.info("Verifying database schema for source_type field...")

    # Check document_chunks table
    verify_document_chunks_schema()
    
    # Check website_chunks table
    verify_website_chunks_schema()
    
    logger.info("Schema verification completed.")

def verify_document_chunks_schema():
    """Verify schema of document_chunks table."""
    logger.info("Checking document_chunks table schema...")
    
    # Check if source_type column exists in document_chunks table
    schema_query = """
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns
    WHERE table_name = 'document_chunks' AND column_name = 'source_type'
    """
    
    schema_result = supabase.execute_query(schema_query)
    if "error" in schema_result:
        logger.error(f"Error checking document_chunks schema: {schema_result['error']}")
        return
    
    if not schema_result:
        logger.error("source_type column does not exist in document_chunks table!")
        # Suggest adding the column
        logger.info("Consider running the following SQL to add the column:")
        logger.info("ALTER TABLE document_chunks ADD COLUMN source_type TEXT NOT NULL DEFAULT 'document';")
    else:
        column_info = schema_result[0]
        logger.info(f"source_type column exists in document_chunks table with data type: {column_info.get('data_type')}")
        logger.info(f"Nullable: {column_info.get('is_nullable')}, Default value: {column_info.get('column_default')}")
        
        # Check for null values
        null_check_query = """
        SELECT COUNT(*) 
        FROM document_chunks 
        WHERE source_type IS NULL
        """
        
        null_check_result = supabase.execute_query(null_check_query)
        if "error" not in null_check_result and null_check_result:
            try:
                null_count = null_check_result[0]['count']
                logger.info(f"Number of document chunks with NULL source_type: {null_count}")
                
                if null_count > 0:
                    logger.warning(f"Found {null_count} document chunks with NULL source_type - these should be fixed")
            except (KeyError, IndexError, TypeError) as e:
                logger.error(f"Error processing NULL check result: {e}")

def verify_website_chunks_schema():
    """Verify schema of website_chunks table."""
    logger.info("Checking website_chunks table schema...")
    
    # Check if source_type column exists in website_chunks table
    schema_query = """
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns
    WHERE table_name = 'website_chunks' AND column_name = 'source_type'
    """
    
    schema_result = supabase.execute_query(schema_query)
    if "error" in schema_result:
        logger.error(f"Error checking website_chunks schema: {schema_result['error']}")
        return
    
    if not schema_result:
        logger.error("source_type column does not exist in website_chunks table!")
        # Suggest adding the column
        logger.info("Consider running the following SQL to add the column:")
        logger.info("ALTER TABLE website_chunks ADD COLUMN source_type TEXT NOT NULL DEFAULT 'website';")
    else:
        column_info = schema_result[0]
        logger.info(f"source_type column exists in website_chunks table with data type: {column_info.get('data_type')}")
        logger.info(f"Nullable: {column_info.get('is_nullable')}, Default value: {column_info.get('column_default')}")
        
        # Check for null values
        null_check_query = """
        SELECT COUNT(*) 
        FROM website_chunks 
        WHERE source_type IS NULL
        """
        
        null_check_result = supabase.execute_query(null_check_query)
        if "error" not in null_check_result and null_check_result:
            try:
                null_count = null_check_result[0]['count']
                logger.info(f"Number of website chunks with NULL source_type: {null_count}")
                
                if null_count > 0:
                    logger.warning(f"Found {null_count} website chunks with NULL source_type - these should be fixed")
            except (KeyError, IndexError, TypeError) as e:
                logger.error(f"Error processing NULL check result: {e}")

if __name__ == "__main__":
    verify_schema()
