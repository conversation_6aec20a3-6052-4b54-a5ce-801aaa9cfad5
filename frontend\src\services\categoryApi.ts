// Category Management API Service
import { Category, CategoryHierarchy, CategoryCreate, CategoryUpdate, DocumentCategoryUpdate } from '../types/documents';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// API Response interfaces
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
}

interface CategoryResponse extends ApiResponse<Category> {
  category?: Category;
}

// Get all categories with hierarchy
export const getCategories = async (): Promise<CategoryHierarchy[]> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch categories: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
};

// Get categories by type
export const getCategoriesByType = async (type: string): Promise<Category[]> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/by-type/${type}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch categories by type: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching categories by type:', error);
    throw error;
  }
};

// Get categories by parent
export const getCategoriesByParent = async (parentId: string): Promise<Category[]> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/by-parent/${parentId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch categories by parent: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching categories by parent:', error);
    throw error;
  }
};

// Create new category
export const createCategory = async (category: CategoryCreate): Promise<CategoryResponse> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(category),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to create category: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating category:', error);
    throw error;
  }
};

// Update category
export const updateCategory = async (categoryId: string, categoryUpdate: CategoryUpdate): Promise<ApiResponse<void>> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(categoryUpdate),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to update category: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating category:', error);
    throw error;
  }
};

// Delete category (soft delete)
export const deleteCategory = async (categoryId: string): Promise<ApiResponse<void>> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to delete category: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error deleting category:', error);
    throw error;
  }
};

// Update document categories
export const updateDocumentCategories = async (
  documentId: string, 
  categoryUpdate: DocumentCategoryUpdate
): Promise<ApiResponse<void>> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/documents/${documentId}/categories`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(categoryUpdate),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to update document categories: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating document categories:', error);
    throw error;
  }
};

// Get category path
export const getCategoryPath = async (categoryId: string): Promise<string> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/${categoryId}/path`);
    
    if (!response.ok) {
      throw new Error(`Failed to get category path: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.path;
  } catch (error) {
    console.error('Error getting category path:', error);
    throw error;
  }
};

// Helper function to build category hierarchy tree from flat list
export const buildCategoryTree = (categories: CategoryHierarchy[]): CategoryHierarchy[] => {
  const categoryMap = new Map<string, CategoryHierarchy>();
  const rootCategories: CategoryHierarchy[] = [];

  // First pass: create map of all categories
  categories.forEach(category => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // Second pass: build tree structure
  categories.forEach(category => {
    const categoryNode = categoryMap.get(category.id)!;
    
    if (category.parent_id) {
      const parent = categoryMap.get(category.parent_id);
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(categoryNode);
      }
    } else {
      rootCategories.push(categoryNode);
    }
  });

  // Sort categories by sort_order and name
  const sortCategories = (cats: CategoryHierarchy[]) => {
    cats.sort((a, b) => {
      if (a.sort_order !== b.sort_order) {
        return a.sort_order - b.sort_order;
      }
      return a.name.localeCompare(b.name);
    });
    
    cats.forEach(cat => {
      if (cat.children && cat.children.length > 0) {
        sortCategories(cat.children);
      }
    });
  };

  sortCategories(rootCategories);
  return rootCategories;
};

// Helper function to get all categories of a specific type from hierarchy
export const getCategoriesOfType = (
  categories: CategoryHierarchy[], 
  type: string
): CategoryHierarchy[] => {
  const result: CategoryHierarchy[] = [];
  
  const traverse = (cats: CategoryHierarchy[]) => {
    cats.forEach(cat => {
      if (cat.type === type) {
        result.push(cat);
      }
      if (cat.children) {
        traverse(cat.children);
      }
    });
  };
  
  traverse(categories);
  return result;
};

// Helper function to find category by ID in hierarchy
export const findCategoryById = (
  categories: CategoryHierarchy[], 
  id: string
): CategoryHierarchy | null => {
  for (const category of categories) {
    if (category.id === id) {
      return category;
    }
    if (category.children) {
      const found = findCategoryById(category.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

// Helper function to get category options for dropdowns
export const getCategoryOptions = (
  categories: CategoryHierarchy[],
  type?: string,
  parentId?: string
): Array<{ value: string; label: string; fullPath: string }> => {
  const options: Array<{ value: string; label: string; fullPath: string }> = [];
  
  const traverse = (cats: CategoryHierarchy[], level = 0) => {
    cats.forEach(cat => {
      // Filter by type if specified
      if (type && cat.type !== type) {
        if (cat.children) {
          traverse(cat.children, level);
        }
        return;
      }
      
      // Filter by parent if specified
      if (parentId && cat.parent_id !== parentId) {
        if (cat.children) {
          traverse(cat.children, level);
        }
        return;
      }
      
      const indent = '  '.repeat(level);
      options.push({
        value: cat.id,
        label: `${indent}${cat.name}`,
        fullPath: cat.full_path
      });
      
      if (cat.children) {
        traverse(cat.children, level + 1);
      }
    });
  };
  
  traverse(categories);
  return options;
};
