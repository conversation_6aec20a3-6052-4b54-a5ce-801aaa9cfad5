{"version": 3, "sources": ["../node_modules/@supabase/realtime-js/node_modules/ws/browser.js"], "names": ["module", "exports", "Error"], "mappings": "sGAEAA,EAAOC,QAAU,WACf,MAAM,IAAIC,MACR,wFAGJ,C", "file": "static/js/3.c9ef755d.chunk.js", "sourcesContent": ["'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n"], "sourceRoot": ""}