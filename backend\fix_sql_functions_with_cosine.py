"""
<PERSON><PERSON><PERSON> to fix the SQL functions in the database for RailGPT.
This script will recreate the SQL functions with proper cosine similarity calculation.
"""
import os
import logging
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def recreate_sql_functions():
    """Recreate the SQL functions with proper cosine similarity calculation."""
    logger.info("Recreating SQL functions with proper cosine similarity calculation...")
    
    # Drop existing functions
    drop_functions = [
        "DROP FUNCTION IF EXISTS direct_search_document_chunks(vector, float, int);",
        "DROP FUNCTION IF EXISTS hybrid_search_document_chunks(text, vector, float, int);",
        "DROP FUNCTION IF EXISTS direct_search_website_chunks(vector, float, int);",
        "DROP FUNCTION IF EXISTS hybrid_search_website_chunks(text, vector, float, int);"
    ]
    
    for drop_query in drop_functions:
        try:
            result = supabase.execute_query(drop_query)
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error dropping function: {result['error']}")
            else:
                logger.info(f"Successfully dropped function: {drop_query}")
        except Exception as e:
            logger.error(f"Error dropping function: {str(e)}")
    
    # Create new functions
    # Direct search for document chunks
    direct_search_doc_function = """
    CREATE OR REPLACE FUNCTION direct_search_document_chunks(
        query_embedding vector,
        match_threshold float DEFAULT 0.0001,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        document_id uuid,
        chunk_index int,
        page_number int,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.embedding,
            dc.metadata,
            dc.created_at,
            dc.updated_at,
            (dc.embedding <#> query_embedding) * -1 + 1 AS similarity
        FROM
            document_chunks dc
        WHERE
            (dc.embedding <#> query_embedding) * -1 + 1 > match_threshold
            OR dc.embedding IS NULL
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Hybrid search for document chunks
    hybrid_search_doc_function = """
    CREATE OR REPLACE FUNCTION hybrid_search_document_chunks(
        query_text text,
        query_embedding vector,
        match_threshold float DEFAULT 0.0001,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        document_id uuid,
        chunk_index int,
        page_number int,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.embedding,
            dc.metadata,
            dc.created_at,
            dc.updated_at,
            CASE 
                WHEN dc.embedding IS NULL THEN 0.01 + (ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text)) * 0.2)
                ELSE ((dc.embedding <#> query_embedding) * -1 + 1) * 0.8 +
                     (ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text)) * 0.2)
            END AS similarity
        FROM
            document_chunks dc
        WHERE
            ((dc.embedding <#> query_embedding) * -1 + 1 > match_threshold
            OR dc.embedding IS NULL)
            OR to_tsvector('english', dc.text) @@ plainto_tsquery('english', query_text)
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Direct search for website chunks
    direct_search_web_function = """
    CREATE OR REPLACE FUNCTION direct_search_website_chunks(
        query_embedding vector,
        match_threshold float DEFAULT 0.0001,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        website_id uuid,
        url text,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            wc.id,
            wc.website_id,
            wc.url,
            wc.text,
            wc.embedding,
            wc.metadata,
            wc.created_at,
            wc.updated_at,
            (wc.embedding <#> query_embedding) * -1 + 1 AS similarity
        FROM
            website_chunks wc
        WHERE
            (wc.embedding <#> query_embedding) * -1 + 1 > match_threshold
            OR wc.embedding IS NULL
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Hybrid search for website chunks
    hybrid_search_web_function = """
    CREATE OR REPLACE FUNCTION hybrid_search_website_chunks(
        query_text text,
        query_embedding vector,
        match_threshold float DEFAULT 0.0001,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        website_id uuid,
        url text,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            wc.id,
            wc.website_id,
            wc.url,
            wc.text,
            wc.embedding,
            wc.metadata,
            wc.created_at,
            wc.updated_at,
            CASE 
                WHEN wc.embedding IS NULL THEN 0.01 + (ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', query_text)) * 0.2)
                ELSE ((wc.embedding <#> query_embedding) * -1 + 1) * 0.8 +
                     (ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', query_text)) * 0.2)
            END AS similarity
        FROM
            website_chunks wc
        WHERE
            ((wc.embedding <#> query_embedding) * -1 + 1 > match_threshold
            OR wc.embedding IS NULL)
            OR to_tsvector('english', wc.text) @@ plainto_tsquery('english', query_text)
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Execute the function creation queries
    functions = [
        ("direct_search_document_chunks", direct_search_doc_function),
        ("hybrid_search_document_chunks", hybrid_search_doc_function),
        ("direct_search_website_chunks", direct_search_web_function),
        ("hybrid_search_website_chunks", hybrid_search_web_function)
    ]
    
    for name, query in functions:
        try:
            result = supabase.execute_query(query)
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error creating function {name}: {result['error']}")
            else:
                logger.info(f"Successfully created function {name}")
        except Exception as e:
            logger.error(f"Error creating function {name}: {str(e)}")

def main():
    """Main function to fix SQL functions."""
    # Recreate SQL functions
    recreate_sql_functions()
    
    logger.info("SQL functions fixed successfully!")

if __name__ == "__main__":
    main()
