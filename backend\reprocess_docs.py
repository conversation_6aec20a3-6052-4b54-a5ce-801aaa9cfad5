#!/usr/bin/env python3
"""
Script to reprocess existing documents with enhanced project context metadata
"""

import os
import sys
import logging

# Add current directory to path
sys.path.append('.')

from document_extractor import extract_document_with_visual_content
from supabase_client import SupabaseClient

# Initialize Supabase client
supabase = SupabaseClient()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def reprocess_documents():
    """Reprocess existing documents to get enhanced metadata"""
    
    # Documents to reprocess - focusing on ones with visual content
    documents = [
        './data/uploads/#F089 VASP (1).pdf',  # This had images in the logs
        './data/uploads/ACP 110V.pdf',  # This was also mentioned in logs
        './data/uploads/3. SBP IOT Based Water Level Quotation V2.pdf',  # Quotation document
        # Add other documents as needed
    ]
    
    for doc_path in documents:
        if os.path.exists(doc_path):
            filename = os.path.basename(doc_path)
            print(f"\n🔄 Reprocessing {filename}...")
            
            try:
                # Delete existing entries - manual cleanup
                print(f"   Deleting existing entries for {filename}...")
                try:
                    # Delete from documents table
                    doc_result = supabase.client.table('documents').delete().eq('filename', filename).execute()
                    print(f"   Deleted document entries: {len(doc_result.data) if doc_result.data else 0}")
                    
                    # Delete from document_chunks table
                    chunk_result = supabase.client.table('document_chunks').delete().ilike('text', f'%{filename}%').execute()
                    print(f"   Deleted chunk entries: {len(chunk_result.data) if chunk_result.data else 0}")
                except Exception as e:
                    print(f"   Warning: Could not delete existing entries: {str(e)}")
                
                print(f"   Extracting with enhanced visual content...")
                # Reprocess with enhanced visual content and project context
                chunks = extract_document_with_visual_content(
                    doc_path, 
                    extract_images=True, 
                    extract_tables=True,
                    extract_charts=True
                )
                
                print(f"   ✅ Reprocessed {filename}: {len(chunks)} chunks created")
                
                # Show some debug info about visual content
                visual_chunks = [c for c in chunks if c.get('content_type') != 'text']
                print(f"   📊 Visual content: {len(visual_chunks)} visual chunks")
                
                for chunk in visual_chunks:
                    metadata = chunk.get('metadata', {})
                    project_context = metadata.get('project_context', 'none')
                    print(f"      - {chunk.get('content_type')} on page {chunk.get('page')} - context: {project_context}")
                
            except Exception as e:
                print(f"   ❌ Error reprocessing {filename}: {str(e)}")
                logger.error(f"Error reprocessing {filename}: {str(e)}")
        else:
            print(f"❌ Document not found: {doc_path}")

if __name__ == "__main__":
    print("🚀 Starting document reprocessing with enhanced metadata...")
    reprocess_documents()
    print("\n✅ Document reprocessing complete!") 