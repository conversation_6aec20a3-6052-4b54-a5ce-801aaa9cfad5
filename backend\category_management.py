# Category Management API Endpoints for RailGPT
# This module provides comprehensive category management functionality

from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
from supabase_client import SupabaseClient
import uuid

# Initialize Supabase client
supabase = SupabaseClient()

# Configure logging
logger = logging.getLogger(__name__)

# Create router for category management endpoints
router = APIRouter(prefix="/api/categories", tags=["categories"])

# Pydantic models for request/response
class CategoryBase(BaseModel):
    name: str
    type: str  # main_category, category, sub_category, minor_category
    parent_id: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = 0

class CategoryCreate(CategoryBase):
    pass

class CategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = None
    is_active: Optional[bool] = None

class Category(CategoryBase):
    id: str
    is_active: bool
    created_at: str
    updated_at: str

class CategoryHierarchy(Category):
    full_path: str
    level: int
    children: Optional[List['CategoryHierarchy']] = None

class DocumentCategoryUpdate(BaseModel):
    main_category: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    minor_category: Optional[str] = None

class WebsiteCategoryUpdate(BaseModel):
    category: Optional[str] = None
    website_category_id: Optional[str] = None

class WebsiteCategory(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    is_active: bool
    sort_order: int
    created_at: str
    updated_at: str

class WebsiteCategoryCreate(BaseModel):
    name: str
    description: Optional[str] = None
    sort_order: Optional[int] = 0

# Get all categories with hierarchy
@router.get("/", response_model=List[CategoryHierarchy])
async def get_categories():
    """Get all categories with their hierarchy structure."""
    try:
        query = """
        SELECT
            id, name, type, parent_id, description, is_active, sort_order,
            created_at, updated_at, full_path, level
        FROM category_hierarchy
        WHERE is_active = true
        ORDER BY full_path
        """
        result = supabase.execute_query(query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fetching categories: {result['error']}")
            raise HTTPException(status_code=500, detail="Error fetching categories")

        return result
    except Exception as e:
        logger.error(f"Error in get_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Get categories by type
@router.get("/by-type/{category_type}")
async def get_categories_by_type(category_type: str):
    """Get categories filtered by type."""
    try:
        query = f"""
        SELECT id, name, type, parent_id, description, is_active, sort_order,
               created_at, updated_at
        FROM categories
        WHERE type = '{category_type}' AND is_active = true
        ORDER BY sort_order, name
        """
        result = supabase.execute_query(query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fetching categories by type: {result['error']}")
            raise HTTPException(status_code=500, detail="Error fetching categories")

        return result
    except Exception as e:
        logger.error(f"Error in get_categories_by_type: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Get categories by parent
@router.get("/by-parent/{parent_id}")
async def get_categories_by_parent(parent_id: str):
    """Get child categories for a given parent."""
    try:
        query = f"""
        SELECT id, name, type, parent_id, description, is_active, sort_order,
               created_at, updated_at
        FROM categories
        WHERE parent_id = '{parent_id}' AND is_active = true
        ORDER BY sort_order, name
        """
        result = supabase.execute_query(query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fetching categories by parent: {result['error']}")
            raise HTTPException(status_code=500, detail="Error fetching categories")

        return result
    except Exception as e:
        logger.error(f"Error in get_categories_by_parent: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Create new category
@router.post("/", response_model=Dict[str, Any])
async def create_category(category: CategoryCreate):
    """Create a new category."""
    try:
        # Validate category type
        valid_types = ['main_category', 'category', 'sub_category', 'minor_category']
        if category.type not in valid_types:
            raise HTTPException(status_code=400, detail=f"Invalid category type. Must be one of: {valid_types}")

        # Check if parent exists (if parent_id is provided)
        if category.parent_id:
            parent_query = f"SELECT id FROM categories WHERE id = '{category.parent_id}'"
            parent_result = supabase.execute_query(parent_query)
            if not parent_result or len(parent_result) == 0:
                raise HTTPException(status_code=400, detail="Parent category not found")

        # Check for duplicate names within the same parent
        escaped_name = category.name.replace("'", "''")
        duplicate_check = f"""
        SELECT id FROM categories
        WHERE name = '{escaped_name}'
        AND type = '{category.type}'
        AND parent_id {'IS NULL' if not category.parent_id else f"= '{category.parent_id}'"}
        AND is_active = true
        """
        duplicate_result = supabase.execute_query(duplicate_check)
        if duplicate_result and len(duplicate_result) > 0:
            raise HTTPException(status_code=400, detail="Category with this name already exists in the same parent")

        # Generate new ID
        new_id = str(uuid.uuid4())

        # Insert new category
        escaped_name = category.name.replace("'", "''")
        escaped_desc = category.description.replace("'", "''") if category.description else None

        insert_query = f"""
        INSERT INTO categories (id, name, type, parent_id, description, sort_order, is_active)
        VALUES (
            '{new_id}',
            '{escaped_name}',
            '{category.type}',
            {f"'{category.parent_id}'" if category.parent_id else 'NULL'},
            {f"'{escaped_desc}'" if escaped_desc else 'NULL'},
            {category.sort_order or 0},
            true
        )
        """

        result = supabase.execute_query(insert_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error creating category: {result['error']}")
            raise HTTPException(status_code=500, detail="Error creating category")

        # Fetch the created category
        fetch_query = f"SELECT * FROM categories WHERE id = '{new_id}'"
        created_category = supabase.execute_query(fetch_query)

        return {
            "success": True,
            "message": "Category created successfully",
            "category": created_category[0] if created_category else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in create_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update category
@router.put("/{category_id}")
async def update_category(category_id: str, category_update: CategoryUpdate):
    """Update an existing category."""
    try:
        # Check if category exists
        check_query = f"SELECT id FROM categories WHERE id = '{category_id}'"
        existing = supabase.execute_query(check_query)
        if not existing or len(existing) == 0:
            raise HTTPException(status_code=404, detail="Category not found")

        # Build update query
        update_fields = []
        if category_update.name is not None:
            escaped_name = category_update.name.replace("'", "''")
            update_fields.append(f"name = '{escaped_name}'")
        if category_update.description is not None:
            escaped_desc = category_update.description.replace("'", "''")
            update_fields.append(f"description = '{escaped_desc}'")
        if category_update.sort_order is not None:
            update_fields.append(f"sort_order = {category_update.sort_order}")
        if category_update.is_active is not None:
            update_fields.append(f"is_active = {category_update.is_active}")

        update_fields.append("updated_at = CURRENT_TIMESTAMP")

        if not update_fields:
            raise HTTPException(status_code=400, detail="No fields to update")

        update_query = f"""
        UPDATE categories
        SET {', '.join(update_fields)}
        WHERE id = '{category_id}'
        """

        result = supabase.execute_query(update_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error updating category: {result['error']}")
            raise HTTPException(status_code=500, detail="Error updating category")

        return {"success": True, "message": "Category updated successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in update_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Soft delete category
@router.delete("/{category_id}")
async def delete_category(category_id: str):
    """Soft delete a category (set is_active = false)."""
    try:
        # Check if category exists
        check_query = f"SELECT id FROM categories WHERE id = '{category_id}'"
        existing = supabase.execute_query(check_query)
        if not existing or len(existing) == 0:
            raise HTTPException(status_code=404, detail="Category not found")

        # Soft delete (set is_active = false)
        delete_query = f"""
        UPDATE categories
        SET is_active = false, updated_at = CURRENT_TIMESTAMP
        WHERE id = '{category_id}'
        """

        result = supabase.execute_query(delete_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error deleting category: {result['error']}")
            raise HTTPException(status_code=500, detail="Error deleting category")

        return {"success": True, "message": "Category deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in delete_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update document categories
@router.put("/documents/{document_id}/categories")
async def update_document_categories(document_id: str, category_update: DocumentCategoryUpdate):
    """Update the category assignment for a document."""
    try:
        # Check if document exists
        check_query = f"SELECT id FROM documents WHERE id = '{document_id}'"
        existing = supabase.execute_query(check_query)
        if not existing or len(existing) == 0:
            raise HTTPException(status_code=404, detail="Document not found")

        # Build update query
        update_fields = []
        if category_update.main_category is not None:
            escaped_main = category_update.main_category.replace("'", "''")
            update_fields.append(f"main_category = '{escaped_main}'")
        if category_update.category is not None:
            escaped_cat = category_update.category.replace("'", "''")
            update_fields.append(f"category = '{escaped_cat}'")
        if category_update.sub_category is not None:
            escaped_sub = category_update.sub_category.replace("'", "''")
            update_fields.append(f"sub_category = '{escaped_sub}'")
        if category_update.minor_category is not None:
            escaped_minor = category_update.minor_category.replace("'", "''")
            update_fields.append(f"minor_category = '{escaped_minor}'")

        update_fields.append("updated_at = CURRENT_TIMESTAMP")

        if len(update_fields) == 1:  # Only timestamp update
            raise HTTPException(status_code=400, detail="No category fields to update")

        update_query = f"""
        UPDATE documents
        SET {', '.join(update_fields)}
        WHERE id = '{document_id}'
        """

        result = supabase.execute_query(update_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error updating document categories: {result['error']}")
            raise HTTPException(status_code=500, detail="Error updating document categories")

        return {"success": True, "message": "Document categories updated successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in update_document_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk update document categories
@router.put("/documents/bulk-update-categories")
async def bulk_update_document_categories(
    document_ids: List[str] = Body(...),
    category_update: DocumentCategoryUpdate = Body(...)
):
    """Update categories for multiple documents at once."""
    try:
        if not document_ids:
            raise HTTPException(status_code=400, detail="No document IDs provided")

        # Validate all documents exist
        ids_str = "', '".join(document_ids)
        check_query = f"SELECT id FROM documents WHERE id IN ('{ids_str}')"
        existing = supabase.execute_query(check_query)

        if not existing or len(existing) != len(document_ids):
            missing_count = len(document_ids) - (len(existing) if existing else 0)
            raise HTTPException(status_code=404, detail=f"{missing_count} document(s) not found")

        # Build update query
        update_fields = []
        if category_update.main_category is not None:
            escaped_main = category_update.main_category.replace("'", "''")
            update_fields.append(f"main_category = '{escaped_main}'")
        if category_update.category is not None:
            escaped_cat = category_update.category.replace("'", "''")
            update_fields.append(f"category = '{escaped_cat}'")
        if category_update.sub_category is not None:
            escaped_sub = category_update.sub_category.replace("'", "''")
            update_fields.append(f"sub_category = '{escaped_sub}'")
        if category_update.minor_category is not None:
            escaped_minor = category_update.minor_category.replace("'", "''")
            update_fields.append(f"minor_category = '{escaped_minor}'")

        update_fields.append("updated_at = CURRENT_TIMESTAMP")

        if len(update_fields) == 1:  # Only timestamp update
            raise HTTPException(status_code=400, detail="No category fields to update")

        update_query = f"""
        UPDATE documents
        SET {', '.join(update_fields)}
        WHERE id IN ('{ids_str}')
        """

        result = supabase.execute_query(update_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error bulk updating document categories: {result['error']}")
            raise HTTPException(status_code=500, detail="Error updating document categories")

        return {
            "success": True,
            "message": f"Successfully updated categories for {len(document_ids)} documents"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk_update_document_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Get category path
@router.get("/{category_id}/path")
async def get_category_path(category_id: str):
    """Get the full hierarchical path for a category."""
    try:
        query = f"SELECT get_category_path('{category_id}') as path"
        result = supabase.execute_query(query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error getting category path: {result['error']}")
            raise HTTPException(status_code=500, detail="Error getting category path")

        if not result or len(result) == 0:
            raise HTTPException(status_code=404, detail="Category not found")

        return {"path": result[0]["path"]}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_category_path: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== WEBSITE CATEGORY MANAGEMENT =====

# Get all website categories
@router.get("/website-categories/", response_model=List[WebsiteCategory])
async def get_website_categories():
    """Get all website categories."""
    try:
        query = """
        SELECT id, name, description, is_active, sort_order, created_at, updated_at
        FROM website_categories
        WHERE is_active = true
        ORDER BY sort_order, name
        """
        result = supabase.execute_query(query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fetching website categories: {result['error']}")
            raise HTTPException(status_code=500, detail="Error fetching website categories")

        return result

    except Exception as e:
        logger.error(f"Error in get_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Create website category
@router.post("/website-categories/", response_model=Dict[str, Any])
async def create_website_category(category: WebsiteCategoryCreate):
    """Create a new website category."""
    try:
        # Check if category name already exists
        check_query = f"SELECT id FROM website_categories WHERE name = '{category.name.replace(\"'\", \"''\")}'"
        existing = supabase.execute_query(check_query)
        if existing and len(existing) > 0:
            raise HTTPException(status_code=400, detail="Website category with this name already exists")

        # Generate new ID
        new_id = str(uuid.uuid4())

        # Insert new website category
        escaped_name = category.name.replace("'", "''")
        escaped_desc = category.description.replace("'", "''") if category.description else None

        insert_query = f"""
        INSERT INTO website_categories (id, name, description, sort_order, is_active)
        VALUES (
            '{new_id}',
            '{escaped_name}',
            {f"'{escaped_desc}'" if escaped_desc else 'NULL'},
            {category.sort_order or 0},
            true
        )
        """

        result = supabase.execute_query(insert_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error creating website category: {result['error']}")
            raise HTTPException(status_code=500, detail="Error creating website category")

        return {"success": True, "message": "Website category created successfully", "id": new_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in create_website_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update website category
@router.put("/website-categories/{category_id}")
async def update_website_category(category_id: str, category_update: CategoryUpdate):
    """Update a website category."""
    try:
        # Check if category exists
        check_query = f"SELECT id FROM website_categories WHERE id = '{category_id}'"
        existing = supabase.execute_query(check_query)
        if not existing or len(existing) == 0:
            raise HTTPException(status_code=404, detail="Website category not found")

        # Build update query
        update_fields = []
        if category_update.name is not None:
            escaped_name = category_update.name.replace("'", "''")
            update_fields.append(f"name = '{escaped_name}'")
        if category_update.description is not None:
            escaped_desc = category_update.description.replace("'", "''")
            update_fields.append(f"description = '{escaped_desc}'")
        if category_update.sort_order is not None:
            update_fields.append(f"sort_order = {category_update.sort_order}")
        if category_update.is_active is not None:
            update_fields.append(f"is_active = {category_update.is_active}")

        update_fields.append("updated_at = CURRENT_TIMESTAMP")

        if len(update_fields) == 1:  # Only timestamp update
            raise HTTPException(status_code=400, detail="No fields to update")

        update_query = f"""
        UPDATE website_categories
        SET {', '.join(update_fields)}
        WHERE id = '{category_id}'
        """

        result = supabase.execute_query(update_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error updating website category: {result['error']}")
            raise HTTPException(status_code=500, detail="Error updating website category")

        return {"success": True, "message": "Website category updated successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in update_website_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update website categories
@router.put("/websites/{website_id}/categories")
async def update_website_categories(website_id: str, category_update: WebsiteCategoryUpdate):
    """Update the category assignment for a website."""
    try:
        # Check if website exists
        check_query = f"SELECT id FROM websites WHERE id = '{website_id}'"
        existing = supabase.execute_query(check_query)
        if not existing or len(existing) == 0:
            raise HTTPException(status_code=404, detail="Website not found")

        # Build update query
        update_fields = []
        if category_update.category is not None:
            escaped_cat = category_update.category.replace("'", "''")
            update_fields.append(f"category = '{escaped_cat}'")
        if category_update.website_category_id is not None:
            update_fields.append(f"website_category_id = '{category_update.website_category_id}'")

        update_fields.append("updated_at = CURRENT_TIMESTAMP")

        if len(update_fields) == 1:  # Only timestamp update
            raise HTTPException(status_code=400, detail="No category fields to update")

        update_query = f"""
        UPDATE websites
        SET {', '.join(update_fields)}
        WHERE id = '{website_id}'
        """

        result = supabase.execute_query(update_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error updating website categories: {result['error']}")
            raise HTTPException(status_code=500, detail="Error updating website categories")

        return {"success": True, "message": "Website categories updated successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in update_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk update website categories
@router.put("/websites/bulk-update-categories")
async def bulk_update_website_categories(
    website_ids: List[str] = Body(...),
    category_update: WebsiteCategoryUpdate = Body(...)
):
    """Update categories for multiple websites at once."""
    try:
        if not website_ids:
            raise HTTPException(status_code=400, detail="No website IDs provided")

        # Validate all websites exist
        ids_str = "', '".join(website_ids)
        check_query = f"SELECT id FROM websites WHERE id IN ('{ids_str}')"
        existing = supabase.execute_query(check_query)

        if not existing or len(existing) != len(website_ids):
            missing_count = len(website_ids) - (len(existing) if existing else 0)
            raise HTTPException(status_code=404, detail=f"{missing_count} website(s) not found")

        # Build update query
        update_fields = []
        if category_update.category is not None:
            escaped_cat = category_update.category.replace("'", "''")
            update_fields.append(f"category = '{escaped_cat}'")
        if category_update.website_category_id is not None:
            update_fields.append(f"website_category_id = '{category_update.website_category_id}'")

        update_fields.append("updated_at = CURRENT_TIMESTAMP")

        if len(update_fields) == 1:  # Only timestamp update
            raise HTTPException(status_code=400, detail="No category fields to update")

        update_query = f"""
        UPDATE websites
        SET {', '.join(update_fields)}
        WHERE id IN ('{ids_str}')
        """

        result = supabase.execute_query(update_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error bulk updating website categories: {result['error']}")
            raise HTTPException(status_code=500, detail="Error updating website categories")

        return {
            "success": True,
            "message": f"Successfully updated categories for {len(website_ids)} websites"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk_update_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
