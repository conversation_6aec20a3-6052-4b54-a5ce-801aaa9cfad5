# Simple Category Management API Endpoints for RailGPT
# Mock implementation to avoid database dependency issues

from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
import uuid

# Initialize logger
logger = logging.getLogger(__name__)

# Create router for category management endpoints
router = APIRouter(prefix="/api/categories", tags=["categories"])

# Pydantic models for request/response
class CategoryBase(BaseModel):
    name: str
    type: str  # main_category, category, sub_category, minor_category
    parent_id: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = 0

class CategoryCreate(CategoryBase):
    pass

class CategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = None
    is_active: Optional[bool] = None

class Category(CategoryBase):
    id: str
    is_active: bool
    created_at: str
    updated_at: str

class CategoryHierarchy(Category):
    full_path: str
    level: int
    children: Optional[List['CategoryHierarchy']] = None

class DocumentCategoryUpdate(BaseModel):
    main_category: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    minor_category: Optional[str] = None

class WebsiteCategoryUpdate(BaseModel):
    category: Optional[str] = None
    website_category_id: Optional[str] = None

class WebsiteCategory(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    is_active: bool
    sort_order: int
    created_at: str
    updated_at: str

class WebsiteCategoryCreate(BaseModel):
    name: str
    description: Optional[str] = None
    sort_order: Optional[int] = 0

# Mock data for categories
MOCK_CATEGORIES = [
    {
        "id": "1",
        "name": "Safety",
        "type": "main_category",
        "parent_id": None,
        "description": "Safety related documents",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Safety",
        "level": 0
    },
    {
        "id": "2", 
        "name": "Operations",
        "type": "main_category",
        "parent_id": None,
        "description": "Operational documents",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Operations",
        "level": 0
    },
    {
        "id": "3",
        "name": "Maintenance",
        "type": "category",
        "parent_id": "1",
        "description": "Maintenance procedures",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Safety > Maintenance",
        "level": 1
    }
]

MOCK_WEBSITE_CATEGORIES = [
    {
        "id": "1",
        "name": "Government",
        "description": "Government websites",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": "2",
        "name": "Railway Authorities",
        "description": "Railway authority websites",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
]

# Get all categories with hierarchy
@router.get("/", response_model=List[CategoryHierarchy])
async def get_categories():
    """Get all categories with their hierarchy structure."""
    try:
        logger.info("Returning mock categories")
        return MOCK_CATEGORIES
    except Exception as e:
        logger.error(f"Error in get_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Create new category
@router.post("/", response_model=Dict[str, Any])
async def create_category(category: CategoryCreate):
    """Create a new category."""
    try:
        logger.info(f"Creating mock category: {category.name}")
        new_id = str(uuid.uuid4())
        return {
            "success": True,
            "message": "Category created successfully",
            "id": new_id
        }
    except Exception as e:
        logger.error(f"Error in create_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update document categories
@router.put("/documents/{document_id}/categories")
async def update_document_categories(document_id: str, category_update: DocumentCategoryUpdate):
    """Update the category assignment for a document."""
    try:
        logger.info(f"Updating categories for document: {document_id}")
        return {"success": True, "message": "Document categories updated successfully"}
    except Exception as e:
        logger.error(f"Error in update_document_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk update document categories
@router.put("/documents/bulk-update-categories")
async def bulk_update_document_categories(
    document_ids: List[str] = Body(...),
    category_update: DocumentCategoryUpdate = Body(...)
):
    """Update categories for multiple documents at once."""
    try:
        logger.info(f"Bulk updating categories for {len(document_ids)} documents")
        return {
            "success": True,
            "message": f"Successfully updated categories for {len(document_ids)} documents"
        }
    except Exception as e:
        logger.error(f"Error in bulk_update_document_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Get all website categories
@router.get("/website-categories/", response_model=List[WebsiteCategory])
async def get_website_categories():
    """Get all website categories."""
    try:
        logger.info("Returning mock website categories")
        return MOCK_WEBSITE_CATEGORIES
    except Exception as e:
        logger.error(f"Error in get_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Create website category
@router.post("/website-categories/", response_model=Dict[str, Any])
async def create_website_category(category: WebsiteCategoryCreate):
    """Create a new website category."""
    try:
        logger.info(f"Creating mock website category: {category.name}")
        new_id = str(uuid.uuid4())
        return {"success": True, "message": "Website category created successfully", "id": new_id}
    except Exception as e:
        logger.error(f"Error in create_website_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update website categories
@router.put("/websites/{website_id}/categories")
async def update_website_categories(website_id: str, category_update: WebsiteCategoryUpdate):
    """Update the category assignment for a website."""
    try:
        logger.info(f"Updating categories for website: {website_id}")
        return {"success": True, "message": "Website categories updated successfully"}
    except Exception as e:
        logger.error(f"Error in update_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk update website categories
@router.put("/websites/bulk-update-categories")
async def bulk_update_website_categories(
    website_ids: List[str] = Body(...),
    category_update: WebsiteCategoryUpdate = Body(...)
):
    """Update categories for multiple websites at once."""
    try:
        logger.info(f"Bulk updating categories for {len(website_ids)} websites")
        return {
            "success": True,
            "message": f"Successfully updated categories for {len(website_ids)} websites"
        }
    except Exception as e:
        logger.error(f"Error in bulk_update_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
