# Simple Category Management API Endpoints for RailGPT
# Mock implementation to avoid database dependency issues

from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
import uuid

# Initialize logger
logger = logging.getLogger(__name__)

# Create router for category management endpoints
router = APIRouter(prefix="/api/categories", tags=["categories"])

# Pydantic models for request/response
class CategoryBase(BaseModel):
    name: str
    type: str  # main_category, category, sub_category, minor_category
    parent_id: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = 0

class CategoryCreate(CategoryBase):
    pass

class CategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = None
    is_active: Optional[bool] = None

class Category(CategoryBase):
    id: str
    is_active: bool
    created_at: str
    updated_at: str

class CategoryHierarchy(Category):
    full_path: str
    level: int
    children: Optional[List['CategoryHierarchy']] = None

class DocumentCategoryUpdate(BaseModel):
    main_category: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    minor_category: Optional[str] = None

class WebsiteCategoryUpdate(BaseModel):
    category: Optional[str] = None
    website_category_id: Optional[str] = None

class WebsiteCategory(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    is_active: bool
    sort_order: int
    created_at: str
    updated_at: str

class WebsiteCategoryCreate(BaseModel):
    name: str
    description: Optional[str] = None
    sort_order: Optional[int] = 0

# Mock data for categories - Start with empty list for user to create their own
MOCK_CATEGORIES = []

MOCK_WEBSITE_CATEGORIES = [
    {
        "id": "1",
        "name": "Government",
        "description": "Government websites",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": "2",
        "name": "Railway Authorities",
        "description": "Railway authority websites",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
]

# Get all categories with hierarchy
@router.get("/", response_model=List[CategoryHierarchy])
async def get_categories():
    """Get all categories with their hierarchy structure."""
    try:
        logger.info("Returning mock categories")
        return MOCK_CATEGORIES
    except Exception as e:
        logger.error(f"Error in get_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Create new category
@router.post("/", response_model=Dict[str, Any])
async def create_category(category: CategoryCreate):
    """Create a new category."""
    try:
        logger.info(f"Creating category: {category.name}")

        # Check if category name already exists
        for existing_cat in MOCK_CATEGORIES:
            if existing_cat["name"].lower() == category.name.lower():
                raise HTTPException(status_code=400, detail="Category with this name already exists")

        # Create new category
        new_id = str(uuid.uuid4())
        from datetime import datetime
        current_time = datetime.now().isoformat() + "Z"

        # Calculate full path
        full_path = category.name
        level = 0
        if category.parent_id:
            # Find parent category
            parent = next((cat for cat in MOCK_CATEGORIES if cat["id"] == category.parent_id), None)
            if parent:
                full_path = f"{parent['full_path']} > {category.name}"
                level = parent["level"] + 1

        new_category = {
            "id": new_id,
            "name": category.name,
            "type": category.type,
            "parent_id": category.parent_id,
            "description": category.description or "",
            "is_active": True,
            "sort_order": category.sort_order or len(MOCK_CATEGORIES) + 1,
            "created_at": current_time,
            "updated_at": current_time,
            "full_path": full_path,
            "level": level
        }

        # Add to mock categories list
        MOCK_CATEGORIES.append(new_category)

        logger.info(f"Successfully created category: {category.name} with ID: {new_id}")
        return {
            "success": True,
            "message": "Category created successfully",
            "id": new_id,
            "category": new_category
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in create_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Delete category
@router.delete("/{category_id}")
async def delete_category(category_id: str):
    """Delete a category."""
    try:
        logger.info(f"Deleting category: {category_id}")

        # Find and remove the category
        global MOCK_CATEGORIES
        category_to_remove = None
        for i, cat in enumerate(MOCK_CATEGORIES):
            if cat["id"] == category_id:
                category_to_remove = i
                break

        if category_to_remove is None:
            raise HTTPException(status_code=404, detail="Category not found")

        # Check if category has children
        has_children = any(cat["parent_id"] == category_id for cat in MOCK_CATEGORIES)
        if has_children:
            raise HTTPException(status_code=400, detail="Cannot delete category that has subcategories")

        # Remove the category
        removed_category = MOCK_CATEGORIES.pop(category_to_remove)

        logger.info(f"Successfully deleted category: {removed_category['name']}")
        return {
            "success": True,
            "message": f"Category '{removed_category['name']}' deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in delete_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update document categories
@router.put("/documents/{document_id}/categories")
async def update_document_categories(document_id: str, category_update: DocumentCategoryUpdate):
    """Update the category assignment for a document."""
    try:
        logger.info(f"Updating categories for document: {document_id}")
        return {"success": True, "message": "Document categories updated successfully"}
    except Exception as e:
        logger.error(f"Error in update_document_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk update document categories
@router.put("/documents/bulk-update-categories")
async def bulk_update_document_categories(
    document_ids: List[str] = Body(...),
    category_update: DocumentCategoryUpdate = Body(...)
):
    """Update categories for multiple documents at once."""
    try:
        logger.info(f"Bulk updating categories for {len(document_ids)} documents")
        return {
            "success": True,
            "message": f"Successfully updated categories for {len(document_ids)} documents"
        }
    except Exception as e:
        logger.error(f"Error in bulk_update_document_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Get all website categories
@router.get("/website-categories/", response_model=List[WebsiteCategory])
async def get_website_categories():
    """Get all website categories."""
    try:
        logger.info("Returning mock website categories")
        return MOCK_WEBSITE_CATEGORIES
    except Exception as e:
        logger.error(f"Error in get_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Create website category
@router.post("/website-categories/", response_model=Dict[str, Any])
async def create_website_category(category: WebsiteCategoryCreate):
    """Create a new website category."""
    try:
        logger.info(f"Creating mock website category: {category.name}")
        new_id = str(uuid.uuid4())
        return {"success": True, "message": "Website category created successfully", "id": new_id}
    except Exception as e:
        logger.error(f"Error in create_website_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update website categories
@router.put("/websites/{website_id}/categories")
async def update_website_categories(website_id: str, category_update: WebsiteCategoryUpdate):
    """Update the category assignment for a website."""
    try:
        logger.info(f"Updating categories for website: {website_id}")
        return {"success": True, "message": "Website categories updated successfully"}
    except Exception as e:
        logger.error(f"Error in update_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk update website categories
@router.put("/websites/bulk-update-categories")
async def bulk_update_website_categories(
    website_ids: List[str] = Body(...),
    category_update: WebsiteCategoryUpdate = Body(...)
):
    """Update categories for multiple websites at once."""
    try:
        logger.info(f"Bulk updating categories for {len(website_ids)} websites")
        return {
            "success": True,
            "message": f"Successfully updated categories for {len(website_ids)} websites"
        }
    except Exception as e:
        logger.error(f"Error in bulk_update_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
