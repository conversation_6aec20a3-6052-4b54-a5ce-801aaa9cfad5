{"version": 2, "name": "railgpt", "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "build"}}], "routes": [{"src": "/api/(.*)", "dest": "https://your-backend-url.railway.app/api/$1"}, {"src": "/(.*)", "dest": "/frontend/$1"}], "env": {"REACT_APP_API_URL": "https://your-backend-url.railway.app"}, "build": {"env": {"NODE_OPTIONS": "--openssl-legacy-provider"}}, "functions": {"frontend/build/**": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "X-Requested-With, Content-Type, Authorization"}]}]}