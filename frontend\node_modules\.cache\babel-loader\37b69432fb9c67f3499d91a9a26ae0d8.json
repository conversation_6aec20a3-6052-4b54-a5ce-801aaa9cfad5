{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\";\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  title = 'RailGPT',\n  onSidebarToggle,\n  sidebarOpen = false\n}) => {\n  const location = useLocation();\n  const isDocumentsActive = location.pathname.includes('/documents');\n  const isWebsitesActive = location.pathname.includes('/websites');\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50 transition-colors duration-300\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto flex flex-col sm:flex-row justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4 sm:mb-0\",\n        children: [location.pathname === '/' && onSidebarToggle && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onSidebarToggle,\n          className: \"p-2 mr-3 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50\",\n          title: sidebarOpen ? \"Close chat history\" : \"Open chat history\",\n          children: \"\\u2630\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-bold\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: `px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${location.pathname === '/' ? 'bg-blue-700 font-medium' : ''}`,\n          children: \"Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/documents\",\n          className: `px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${isDocumentsActive ? 'bg-blue-700 font-medium' : ''}`,\n          children: \"Documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/websites\",\n          className: `px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${isWebsitesActive ? 'bg-blue-700 font-medium' : ''}`,\n          children: \"Websites\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/categories\",\n          className: `px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${isCategoriesActive ? 'bg-blue-700 font-medium' : ''}`,\n          children: \"\\uD83D\\uDCC2 Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/settings\",\n          className: `px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${location.pathname === '/settings' ? 'bg-blue-700 font-medium' : ''}`,\n          children: \"\\u2699\\uFE0F Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\nexport default Header;", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Header", "title", "onSidebarToggle", "sidebarOpen", "location", "isDocumentsActive", "pathname", "includes", "isWebsitesActive", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "isCategoriesActive"], "sources": ["C:/IR App/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\ninterface HeaderProps {\n  title?: string;\n  onSidebarToggle?: () => void;\n  sidebarOpen?: boolean;\n}\n\nconst Header: React.FC<HeaderProps> = ({ title = 'RailGPT', onSidebarToggle, sidebarOpen = false }) => {\n  const location = useLocation();\n  const isDocumentsActive = location.pathname.includes('/documents');\n  const isWebsitesActive = location.pathname.includes('/websites');\n\n  return (\n    <header className=\"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50 transition-colors duration-300\">\n      <div className=\"container mx-auto flex flex-col sm:flex-row justify-between items-center\">\n        <div className=\"flex items-center mb-4 sm:mb-0\">\n          {location.pathname === '/' && onSidebarToggle && (\n            <button\n              onClick={onSidebarToggle}\n              className=\"p-2 mr-3 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50\"\n              title={sidebarOpen ? \"Close chat history\" : \"Open chat history\"}\n            >\n              ☰\n            </button>\n          )}\n          <h1 className=\"text-xl font-bold\">{title}</h1>\n        </div>\n\n        <nav className=\"flex gap-4\">\n          <Link\n            to=\"/\"\n            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${\n              location.pathname === '/' ? 'bg-blue-700 font-medium' : ''\n            }`}\n          >\n            Chat\n          </Link>\n          <Link\n            to=\"/documents\"\n            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${\n              isDocumentsActive ? 'bg-blue-700 font-medium' : ''\n            }`}\n          >\n            Documents\n          </Link>\n          <Link\n            to=\"/websites\"\n            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${\n              isWebsitesActive ? 'bg-blue-700 font-medium' : ''\n            }`}\n          >\n            Websites\n          </Link>\n          <Link\n            to=\"/categories\"\n            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${\n              isCategoriesActive ? 'bg-blue-700 font-medium' : ''\n            }`}\n          >\n            📂 Categories\n          </Link>\n          <Link\n            to=\"/settings\"\n            className={`px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 ${\n              location.pathname === '/settings' ? 'bg-blue-700 font-medium' : ''\n            }`}\n          >\n            ⚙️ Settings\n          </Link>\n        </nav>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQrD,MAAMC,MAA6B,GAAGA,CAAC;EAAEC,KAAK,GAAG,SAAS;EAAEC,eAAe;EAAEC,WAAW,GAAG;AAAM,CAAC,KAAK;EACrG,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,iBAAiB,GAAGD,QAAQ,CAACE,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC;EAClE,MAAMC,gBAAgB,GAAGJ,QAAQ,CAACE,QAAQ,CAACC,QAAQ,CAAC,WAAW,CAAC;EAEhE,oBACER,OAAA;IAAQU,SAAS,EAAC,qGAAqG;IAAAC,QAAA,eACrHX,OAAA;MAAKU,SAAS,EAAC,0EAA0E;MAAAC,QAAA,gBACvFX,OAAA;QAAKU,SAAS,EAAC,gCAAgC;QAAAC,QAAA,GAC5CN,QAAQ,CAACE,QAAQ,KAAK,GAAG,IAAIJ,eAAe,iBAC3CH,OAAA;UACEY,OAAO,EAAET,eAAgB;UACzBO,SAAS,EAAC,uKAAuK;UACjLR,KAAK,EAAEE,WAAW,GAAG,oBAAoB,GAAG,mBAAoB;UAAAO,QAAA,EACjE;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACDhB,OAAA;UAAIU,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAET;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAENhB,OAAA;QAAKU,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBX,OAAA,CAACH,IAAI;UACHoB,EAAE,EAAC,GAAG;UACNP,SAAS,EAAE,mJACTL,QAAQ,CAACE,QAAQ,KAAK,GAAG,GAAG,yBAAyB,GAAG,EAAE,EACzD;UAAAI,QAAA,EACJ;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPhB,OAAA,CAACH,IAAI;UACHoB,EAAE,EAAC,YAAY;UACfP,SAAS,EAAE,mJACTJ,iBAAiB,GAAG,yBAAyB,GAAG,EAAE,EACjD;UAAAK,QAAA,EACJ;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPhB,OAAA,CAACH,IAAI;UACHoB,EAAE,EAAC,WAAW;UACdP,SAAS,EAAE,mJACTD,gBAAgB,GAAG,yBAAyB,GAAG,EAAE,EAChD;UAAAE,QAAA,EACJ;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPhB,OAAA,CAACH,IAAI;UACHoB,EAAE,EAAC,aAAa;UAChBP,SAAS,EAAE,mJACTQ,kBAAkB,GAAG,yBAAyB,GAAG,EAAE,EAClD;UAAAP,QAAA,EACJ;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPhB,OAAA,CAACH,IAAI;UACHoB,EAAE,EAAC,WAAW;UACdP,SAAS,EAAE,mJACTL,QAAQ,CAACE,QAAQ,KAAK,WAAW,GAAG,yBAAyB,GAAG,EAAE,EACjE;UAAAI,QAAA,EACJ;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAED,eAAef,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}