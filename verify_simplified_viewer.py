#!/usr/bin/env python3
"""
Verification script for the simplified PDF viewer solution.
"""

import requests
import sys

def test_backend_health():
    """Test backend server health."""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def test_pdf_serving():
    """Test PDF file serving."""
    try:
        response = requests.get("http://localhost:8000/api/documents/view/ACP%20110V.pdf", timeout=10)
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            is_pdf = response.content.startswith(b'%PDF')
            size = len(response.content)
            return {
                'success': True,
                'content_type': content_type,
                'is_pdf': is_pdf,
                'size': size
            }
        else:
            return {'success': False, 'error': f"HTTP {response.status_code}"}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def test_document_search():
    """Test document search functionality."""
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json={"query": "authority transfer", "model": "gemini-2.0-flash"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            return {
                'success': True,
                'document_sources': len(data.get('document_sources', [])),
                'llm_fallback': data.get('llm_fallback', True),
                'first_link': data.get('document_sources', [{}])[0].get('link', '') if data.get('document_sources') else ''
            }
        else:
            return {'success': False, 'error': f"HTTP {response.status_code}"}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def test_frontend():
    """Test frontend accessibility."""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """Run verification tests."""
    print("🔧 RailGPT Simplified PDF Viewer Verification")
    print("=" * 60)
    
    # Test backend health
    print("\n📡 Backend Health Check:")
    backend_ok = test_backend_health()
    print(f"   {'✅ PASS' if backend_ok else '❌ FAIL'} - Backend server")
    
    if not backend_ok:
        print("\n❌ Backend not running. Start with:")
        print("   cd backend && python -m uvicorn server:app --reload --port 8000")
        return 1
    
    # Test PDF serving
    print("\n📄 PDF Serving Test:")
    pdf_result = test_pdf_serving()
    if pdf_result['success']:
        print(f"   ✅ PASS - PDF serving")
        print(f"      Content-Type: {pdf_result['content_type']}")
        print(f"      Valid PDF: {pdf_result['is_pdf']}")
        print(f"      Size: {pdf_result['size']:,} bytes")
    else:
        print(f"   ❌ FAIL - PDF serving: {pdf_result['error']}")
    
    # Test document search
    print("\n🔍 Document Search Test:")
    search_result = test_document_search()
    if search_result['success']:
        print(f"   ✅ PASS - Document search")
        print(f"      Sources found: {search_result['document_sources']}")
        print(f"      LLM fallback: {search_result['llm_fallback']}")
        print(f"      First link: {search_result['first_link']}")
    else:
        print(f"   ❌ FAIL - Document search: {search_result['error']}")
    
    # Test frontend
    print("\n🌐 Frontend Test:")
    frontend_ok = test_frontend()
    print(f"   {'✅ PASS' if frontend_ok else '❌ FAIL'} - Frontend server")
    
    if not frontend_ok:
        print("\n❌ Frontend not running. Start with:")
        print("   cd frontend && npm start")
    
    # Overall status
    all_tests_pass = (
        backend_ok and 
        pdf_result['success'] and 
        search_result['success'] and 
        frontend_ok and
        search_result['document_sources'] > 0 and
        not search_result['llm_fallback']
    )
    
    print(f"\n🎯 Overall Status:")
    if all_tests_pass:
        print("   ✅ ALL TESTS PASSED")
        print("   🎉 Simplified PDF viewer is working correctly!")
    else:
        print("   ⚠️  Some tests failed - check individual results above")
    
    print(f"\n📋 Manual Testing:")
    print("1. Open: http://localhost:3000")
    print("2. Ask: 'What is authority transfer?'")
    print("3. Click on document source links")
    print("4. Verify PDF loads in browser-native viewer")
    print("5. Test download and new tab buttons")
    
    print(f"\n🔗 Direct PDF Viewer URLs:")
    print("   http://localhost:3000/viewer?file=ACP%20110V.pdf&page=1")
    print("   http://localhost:3000/viewer?file=Authority%20Transfer%20Declaration.pdf&page=1")
    
    print(f"\n✨ Expected Results:")
    print("   • PDF loads without configuration errors")
    print("   • Browser-native PDF controls available")
    print("   • Download and new tab buttons work")
    print("   • Clean, simplified interface")
    print("   • No PDF.js version conflicts")
    
    return 0 if all_tests_pass else 1

if __name__ == "__main__":
    sys.exit(main())
