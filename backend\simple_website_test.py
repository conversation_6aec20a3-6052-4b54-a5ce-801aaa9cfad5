#!/usr/bin/env python3
"""
Simple test to add a website and check if chunks are stored
"""

import requests
import json
from supabase_client import supabase

def test_simple_website():
    """Test adding a simple website"""
    
    print("=== Testing Website Addition ===")
    
    # Check initial state
    initial_count = supabase.execute_query("SELECT COUNT(*) as count FROM website_chunks")
    print(f"Initial website chunks: {initial_count[0]['count'] if initial_count else 0}")
    
    # Test data
    test_data = {
        "url": "https://httpbin.org/html",  # Simple test URL that returns HTML
        "submitted_by": "<EMAIL>"
    }
    
    print(f"Adding website: {test_data['url']}")
    
    try:
        # Make the API call
        response = requests.post(
            "http://localhost:8000/api/add-website",
            json=test_data,
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success! Response: {json.dumps(result, indent=2)}")
            
            # Check final state
            final_count = supabase.execute_query("SELECT COUNT(*) as count FROM website_chunks")
            print(f"Final website chunks: {final_count[0]['count'] if final_count else 0}")
            
            # Show sample chunks if any
            if final_count and final_count[0]['count'] > 0:
                sample = supabase.execute_query("""
                    SELECT wc.id, wc.chunk_index, LEFT(wc.text, 100) as text_sample
                    FROM website_chunks wc
                    ORDER BY wc.created_at DESC
                    LIMIT 2
                """)
                print("Sample chunks:")
                for chunk in sample:
                    print(f"  - Chunk {chunk['chunk_index']}: {chunk['text_sample']}...")
            
        else:
            print(f"Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_simple_website()
