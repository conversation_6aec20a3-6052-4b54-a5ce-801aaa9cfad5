#!/usr/bin/env python3
"""
Debug script to see exact chunk details for LLM meaning query.
"""

import requests
import json

API_URL = "http://localhost:8000"

def debug_chunk_details():
    """Debug the exact chunks being found for LLM meaning query."""
    
    print("🔍 Debugging Chunk Details for 'LLM meaning'")
    print("=" * 60)
    
    # Test the query with detailed logging
    try:
        print("\n🧪 Testing query with detailed chunk analysis:")
        response = requests.post(f"{API_URL}/api/query", 
            json={
                "query": "LLM meaning",
                "model": "gemini-2.0-flash",
                "fallback_enabled": True
            }, 
            timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Query successful")
            print(f"📄 Document answer: {'Yes' if data.get('document_answer') else 'No'}")
            print(f"🌐 Website answer: {'Yes' if data.get('website_answer') else 'No'}")
            print(f"🧠 LLM fallback: {'Yes' if data.get('llm_fallback') else 'No'}")
            print(f"🤖 Model used: {data.get('llm_model', 'Unknown')}")
            print(f"📊 Document sources: {len(data.get('document_sources', []))}")
            print(f"📊 Website sources: {len(data.get('website_sources', []))}")
            
            # Show the actual sources in detail
            if data.get('document_sources'):
                print(f"\n📄 Document sources details:")
                for i, source in enumerate(data['document_sources']):
                    print(f"   {i+1}. Type: {source.get('source_type', 'Unknown')}")
                    print(f"      Filename: {source.get('filename', 'Unknown')}")
                    print(f"      Page: {source.get('page', 'Unknown')}")
                    print(f"      Display: {source.get('display_text', 'Unknown')}")
                    
            if data.get('website_sources'):
                print(f"\n🌐 Website sources details:")
                for i, source in enumerate(data['website_sources']):
                    print(f"   {i+1}. Type: {source.get('source_type', 'Unknown')}")
                    print(f"      URL: {source.get('url', 'Unknown')}")
                    print(f"      Display: {source.get('display_text', 'Unknown')}")
            
            # Show the answers
            print(f"\n💬 Document answer:")
            print(f"{data.get('document_answer', 'None')}")
            
            print(f"\n🌐 Website answer:")
            print(f"{data.get('website_answer', 'None')}")
            
            print(f"\n🔄 Combined answer:")
            print(f"{data.get('answer', 'None')}")
                    
        else:
            print(f"❌ Query failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing query: {str(e)}")

    # Test the debug search endpoint to see what chunks are actually found
    try:
        print(f"\n🔍 Testing debug search endpoint:")
        response = requests.get(f"{API_URL}/api/debug/search", 
                              params={"query": "LLM meaning"}, 
                              timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Debug search successful")
            
            # Show document chunks
            doc_chunks = data.get('document_chunks', [])
            print(f"\n📄 Document chunks found: {len(doc_chunks)}")
            for i, chunk in enumerate(doc_chunks[:3]):  # Show first 3
                similarity = chunk.get('similarity', 0)
                filename = chunk.get('filename', 'Unknown')
                page = chunk.get('page', 'Unknown')
                text_preview = chunk.get('text', '')[:100] + '...' if len(chunk.get('text', '')) > 100 else chunk.get('text', '')
                print(f"   {i+1}. Similarity: {similarity:.4f}")
                print(f"      File: {filename} (page {page})")
                print(f"      Text: {text_preview}")
                print()
            
            # Show website chunks
            web_chunks = data.get('website_chunks', [])
            print(f"\n🌐 Website chunks found: {len(web_chunks)}")
            for i, chunk in enumerate(web_chunks[:3]):  # Show first 3
                similarity = chunk.get('similarity', 0)
                url = chunk.get('url', 'Unknown')
                text_preview = chunk.get('text', '')[:100] + '...' if len(chunk.get('text', '')) > 100 else chunk.get('text', '')
                print(f"   {i+1}. Similarity: {similarity:.4f}")
                print(f"      URL: {url}")
                print(f"      Text: {text_preview}")
                print()
                
        else:
            print(f"❌ Debug search failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing debug search: {str(e)}")

if __name__ == "__main__":
    print("🚀 Chunk Details Debug Tool")
    print("=" * 60)
    
    # Test if API is available
    try:
        response = requests.get(f"{API_URL}/api/health", timeout=3)
        if response.status_code == 200:
            print("✅ API is healthy and responding")
        else:
            print(f"❌ API health check failed: {response.status_code}")
            exit(1)
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        exit(1)
    
    debug_chunk_details()
    
    print(f"\n✅ Debug complete!") 