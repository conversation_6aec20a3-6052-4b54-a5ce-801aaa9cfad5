#!/usr/bin/env python3
"""
Test script to check actual similarity scores returned by search functions.
"""

import requests
import json
import time

# Configuration
API_URL = "http://localhost:8000"

def test_similarity_scores(query, description):
    """Test a query and show the actual similarity scores."""
    print(f"\n🔍 Testing similarity scores for: {description}")
    print(f"Query: '{query}'")
    
    try:
        # Use the debug search endpoint to see raw results
        response = requests.get(
            f"{API_URL}/api/debug/search",
            params={"query": query},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            # Check vector search results
            vector_docs = data.get("vector_document_search", {}).get("document_chunks", [])
            vector_webs = data.get("vector_website_search", {}).get("website_chunks", [])
            
            print(f"📄 Vector Document Results: {len(vector_docs)} chunks")
            for i, chunk in enumerate(vector_docs[:3]):
                similarity = chunk.get('similarity', 0)
                text_preview = chunk.get('text', '')[:50] + "..." if chunk.get('text') else 'No text'
                print(f"  {i+1}. Similarity: {similarity:.4f} - {text_preview}")
            
            print(f"🌐 Vector Website Results: {len(vector_webs)} chunks")
            for i, chunk in enumerate(vector_webs[:3]):
                similarity = chunk.get('similarity', 0)
                text_preview = chunk.get('text', '')[:50] + "..." if chunk.get('text') else 'No text'
                print(f"  {i+1}. Similarity: {similarity:.4f} - {text_preview}")
                
        else:
            print(f"❌ API Error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def main():
    print("🔬 Testing Similarity Scores")
    print("="*50)
    
    # Test relevant query
    test_similarity_scores(
        "FSDS monitoring system",
        "Railway-specific query (should have high similarity)"
    )
    
    # Test irrelevant query
    test_similarity_scores(
        "How to bake a chocolate cake",
        "Cooking query (should have low similarity)"
    )
    
    # Test transport query
    test_similarity_scores(
        "public transportation safety",
        "Transport query (should have medium similarity)"
    )
    
    print("\n" + "="*50)
    print("🏁 Similarity score testing completed!")

if __name__ == "__main__":
    main() 