import React, { useState } from 'react';
import { Website, WebsiteExtractionDetails, ExtractionParser } from '../../types/websites';

interface WebsiteViewModalProps {
  website: Website;
  extractionDetails: WebsiteExtractionDetails;
  isOpen: boolean;
  onClose: () => void;
  onRetry: (website: Website, parser: ExtractionParser) => Promise<boolean>;
}

const WebsiteViewModal: React.FC<WebsiteViewModalProps> = ({
  website,
  extractionDetails,
  isOpen,
  onClose,
  onRetry,
}) => {
  const [activeTab, setActiveTab] = useState<'content' | 'details'>('content');
  const [selectedParser, setSelectedParser] = useState<ExtractionParser>('Trafilatura');
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen) {
    return null;
  }

  const handleRetry = async () => {
    setIsProcessing(true);
    try {
      await onRetry(website, selectedParser);
      // In a real app, we would wait for the extraction to complete
      // and then refresh the extraction details
    } catch (error) {
      console.error('Error re-extracting website:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const renderQualityIndicator = () => {
    const { contentQuality } = extractionDetails;
    let color;
    if (contentQuality >= 80) color = 'bg-green-500';
    else if (contentQuality >= 60) color = 'bg-green-400';
    else if (contentQuality >= 40) color = 'bg-yellow-500';
    else if (contentQuality >= 20) color = 'bg-orange-500';
    else color = 'bg-red-500';

    return (
      <div className="flex items-center">
        <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
          <div className={`h-2.5 rounded-full ${color}`} style={{ width: `${contentQuality}%` }}></div>
        </div>
        <span className="text-sm font-medium">{contentQuality}%</span>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <div>
            <h2 className="text-lg font-semibold">
              <a href={website.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                {website.url}
              </a>
            </h2>
            <p className="text-sm text-gray-500">{website.domain}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            className={`px-4 py-2 font-medium ${
              activeTab === 'content'
                ? 'text-green-600 border-b-2 border-green-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('content')}
          >
            Extracted Content
          </button>
          <button
            className={`px-4 py-2 font-medium ${
              activeTab === 'details'
                ? 'text-green-600 border-b-2 border-green-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('details')}
          >
            Processing Details
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {activeTab === 'content' ? (
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-sm bg-gray-50 p-4 rounded-md overflow-auto h-[60vh]">
                {website.extractedContent || extractionDetails.extractedContent || 
                (website.chunks && website.chunks.length > 0 ? 
                  website.chunks.map((chunk: any) => chunk.text).join('\n\n') : 
                  'No extracted content available')}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900">Extraction Method</h3>
                <p className="mt-1 text-sm text-gray-600">{extractionDetails.extractionMethod}</p>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">Content Quality</h3>
                <div className="mt-1">{renderQualityIndicator()}</div>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">Processing Time</h3>
                <p className="mt-1 text-sm text-gray-600">
                  {extractionDetails.processingTime} ms ({(extractionDetails.processingTime / 1000).toFixed(2)} seconds)
                </p>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">Chunks Created</h3>
                <p className="mt-1 text-sm text-gray-600">{extractionDetails.chunks}</p>
              </div>

              {extractionDetails.fallbackHistory.length > 0 && (
                <div>
                  <h3 className="font-medium text-gray-900">Fallback History</h3>
                  <ol className="mt-1 text-sm text-gray-600 list-decimal pl-5">
                    {extractionDetails.fallbackHistory.map((method, index) => (
                      <li key={index}>
                        {method}
                        {index === extractionDetails.fallbackHistory.length - 1 && " (used)"}
                      </li>
                    ))}
                  </ol>
                </div>
              )}

              {extractionDetails.warnings.length > 0 && (
                <div>
                  <h3 className="font-medium text-gray-900">Warnings</h3>
                  <ul className="mt-1 text-sm text-gray-600 list-disc pl-5">
                    {extractionDetails.warnings.map((warning, index) => (
                      <li key={index} className="text-yellow-600">{warning}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="pt-4 border-t border-gray-200">
                <h3 className="font-medium text-gray-900">Retry Extraction</h3>
                <div className="mt-2 flex items-end gap-3">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Extraction Parser
                    </label>
                    <select
                      value={selectedParser}
                      onChange={(e) => setSelectedParser(e.target.value as ExtractionParser)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      disabled={isProcessing}
                    >
                      <option value="Trafilatura">Trafilatura</option>
                      <option value="BeautifulSoup">BeautifulSoup</option>
                      <option value="Scrapy">Scrapy</option>
                      <option value="Puppeteer">Puppeteer</option>
                    </select>
                  </div>
                  <button
                    onClick={handleRetry}
                    disabled={isProcessing}
                    className={`px-4 py-2 rounded-md ${
                      isProcessing
                        ? 'bg-gray-300 cursor-not-allowed'
                        : 'bg-green-600 text-white hover:bg-green-700'
                    }`}
                  >
                    {isProcessing ? 'Processing...' : 'Reprocess Website'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WebsiteViewModal;
