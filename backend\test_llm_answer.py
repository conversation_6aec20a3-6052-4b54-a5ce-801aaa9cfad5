"""
Script to test LLM answer generation with document chunks.
"""
import os
import logging
import json
from dotenv import load_dotenv
from server import generate_llm_answer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def test_acp_answer():
    """Test LLM answer generation for ACP query."""
    logger.info("Testing LLM answer generation for ACP query...")
    
    # Create a document chunk for ACP
    acp_chunk = {
        'id': '16c821c3-55c5-4ab4-9d76-4a1b8584e21a',
        'document_id': '6b40bb6b-3c87-4a9f-beac-b2c164516b27',
        'chunk_index': 0,
        'page_number': 1,
        'text': "Document: ACP 110V, Page: 1\n\nVASP ENTERPRISES A/P: Chinchwad, Taluka – Shirol, Dist: Kolhapur (MH) 416412 Email: <EMAIL> Website: www.dvaspgroup.com Contact number: 08668746815 / 08432372206 GST Number – 27CJDPT9389M1Z1 Scope of Work (SOW) for Alarm Chain Pulling (ACP) Monitoring System 1. Project Overview VASP Enterprises has developed an advanced Alarm Chain Pulling (ACP) Monitoring System to enhance passenger safety and operational efficiency in the Indian Railways network. The system is designed to provide real-time monitoring, instant notifications, and precise identification of alarm chain pulling events within train coaches. By integrating with existing railway infrastructure, the system helps railway personnel swiftly respond to emergencies, minimize delays, and analyse ACP incident trends for future improvements.",
        'source_type': 'document',
        'filename': 'ACP 110V.pdf',
        'url': 'documents/ACP 110V.pdf'
    }
    
    # Generate answer
    logger.info("Generating answer...")
    answer, sources, doc_sources, web_sources = generate_llm_answer(
        query="What is the full form of ACP?",
        similar_chunks=[acp_chunk],
        model_id="gemini-2.0-flash",
        extract_format="paragraph"
    )
    
    logger.info(f"Answer: {answer}")
    logger.info(f"Sources: {sources}")
    logger.info(f"Document sources: {doc_sources}")
    logger.info(f"Website sources: {web_sources}")

def test_fsds_answer():
    """Test LLM answer generation for FSDS query."""
    logger.info("Testing LLM answer generation for FSDS query...")
    
    # Create a document chunk for FSDS
    fsds_chunk = {
        'id': '9a97868c-f2da-401b-81a5-72885898cb36',
        'document_id': '77d0d4e7-9d63-4580-ad84-d42f3f84c3c3',
        'chunk_index': 1,
        'page_number': 2,
        'text': "Document: FSDS Isolation Identification System, Page: 2\n\nVASP ENTERPRISES A/P: Chinchwad, Taluka – Shirol, Dist: Kolhapur (MH) 416412 Email: <EMAIL> Website: www.dvaspgroup.com Contact number: 08668746815 / 08432372206 GST Number – 27CJDPT9389M1Z1 4. System Components The FSDS Isolation Monitoring System consists of the following components: i. FSDS Monitoring Sensor: Detects pneumatic isolation or bypassing of the FSDS. ii. Communication Module: Sends alerts and data via GSM/GPRS/LoRa/IoT networks. iii. Control & Monitoring Dashboard: A centralized system for railway authorities to monitor incidents and analyse trends. iv. Mobile Notifications: Real-time alerts sent to railway officials, supervisors, and safety personnel. v. Event Logging System: Records and archives FSDS isolation events for compliance tracking. 5. Deployment & Implementation i. Installation in Train Coaches: VASP Enterprises will install the FSDS Isolation Monitoring System in designated train coaches. ii. Integration with FSDS & Railway Systems: The system will be linked to the FSDS pneumatic input system. iii. Testing & Validation: Conduct a testing phase to ensure accurate detection and reliability. iv. Training for Railway Personnel: Provide training on system functionality, monitoring, and response actions. v. Live Monitoring & Support: Offer technical support, maintenance services, and periodic updates. 6. Benefits to Indian Railways • Enhanced Fire Safety: Ensures the automatic braking function remains active in case of fire and smoke. • Prevention of Unauthorized Isolation: Deters railway employees from bypassing safety systems. • Improved Compliance: Ensures safety protocols are strictly followed. • Data-Driven Decision Making: Helps identify patterns of FSDS isolation and improve system reliability. • Real-Time Incident Management: Enables swift action in case of FSDS tampering or malfunction. 7. Conclusion VASP Enterprises' FSDS Isolation Monitoring System plays a crucial role in maintaining fire safety integrity within Indian Railways. By providing real-time notifications, event logging, and compliance monitoring, this system ensures that the FSDS remains fully functional and prevents unauthorized isolation, thereby safeguarding passengers, railway assets, and operational efficiency.",
        'source_type': 'document',
        'filename': 'FSDS Isolation Identification System.pdf',
        'url': 'documents/FSDS Isolation Identification System.pdf'
    }
    
    # Generate answer
    logger.info("Generating answer...")
    answer, sources, doc_sources, web_sources = generate_llm_answer(
        query="What is the full form of FSDS?",
        similar_chunks=[fsds_chunk],
        model_id="gemini-2.0-flash",
        extract_format="paragraph"
    )
    
    logger.info(f"Answer: {answer}")
    logger.info(f"Sources: {sources}")
    logger.info(f"Document sources: {doc_sources}")
    logger.info(f"Website sources: {web_sources}")

def main():
    """Main function to test LLM answer generation."""
    test_acp_answer()
    test_fsds_answer()

if __name__ == "__main__":
    main()
