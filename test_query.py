import requests
import json

# Test query
response = requests.post(
    "http://localhost:8000/api/query",
    json={"query": "authority transfer", "model": "gemini-2.0-flash"},
    timeout=30
)

print("Status:", response.status_code)
if response.status_code == 200:
    data = response.json()
    print("Document sources:", len(data.get('document_sources', [])))
    print("LLM fallback:", data.get('llm_fallback', True))
    
    sources = data.get('document_sources', [])
    for i, source in enumerate(sources[:2]):
        if isinstance(source, dict):
            print(f"Source {i+1}:")
            print(f"  Filename: {source.get('filename', 'Unknown')}")
            print(f"  Page: {source.get('page', 'Unknown')}")
            print(f"  Link: {source.get('link', 'No link')}")
else:
    print("Error:", response.text)
