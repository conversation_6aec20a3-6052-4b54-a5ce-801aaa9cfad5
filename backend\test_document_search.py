"""
Script to test document search and answer generation.
"""
import os
import logging
import json
from typing import List, Dict, Any
import numpy as np
from dotenv import load_dotenv
import llm_router
from vector_search import search_documents, search_websites
from server import generate_llm_answer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def generate_embedding(text: str) -> List[float]:
    """Generate embedding for text using the LLM router."""
    try:
        return llm_router.generate_embedding(text)
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Try with default model
        try:
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Use a deterministic random embedding as last resort
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def test_document_search_and_answer():
    """Test document search and answer generation."""
    logger.info("Testing document search and answer generation...")
    
    # Test queries
    test_queries = [
        "What is the Rapid Response app?",
        "What is VASP and who developed it?",
        "What is the full form of ACP?",
        "What is the full form of FSDS?"
    ]
    
    for query in test_queries:
        logger.info(f"\n=== Testing query: '{query}' ===\n")
        
        # Generate embedding for the query
        query_embedding = generate_embedding(query)
        
        # Search for document chunks
        logger.info("Searching for document chunks...")
        doc_results = search_documents(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            match_threshold=0.0001,
            match_count=5
        )
        
        logger.info(f"Found {len(doc_results)} document chunks")
        
        # Search for website chunks
        logger.info("Searching for website chunks...")
        web_results = search_websites(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            match_threshold=0.0001,
            match_count=5
        )
        
        logger.info(f"Found {len(web_results)} website chunks")
        
        # Combine results
        all_chunks = []
        
        # Add document chunks
        for chunk in doc_results:
            chunk_dict = dict(chunk)
            chunk_dict["source_type"] = "document"
            all_chunks.append(chunk_dict)
        
        # Add website chunks
        for chunk in web_results:
            chunk_dict = dict(chunk)
            chunk_dict["source_type"] = "website"
            all_chunks.append(chunk_dict)
        
        # Generate answer
        if all_chunks:
            logger.info("Generating answer...")
            
            # Generate answer
            answer, sources, doc_sources, web_sources = generate_llm_answer(
                query=query,
                similar_chunks=all_chunks,
                model_id="gemini-2.0-flash",
                extract_format="paragraph"
            )
            
            logger.info(f"Answer: {answer}")
            logger.info(f"Sources: {sources}")
        else:
            logger.info("No chunks found, skipping answer generation")

def main():
    """Main function to test document search and answer generation."""
    test_document_search_and_answer()

if __name__ == "__main__":
    main()
