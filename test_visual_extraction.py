#!/usr/bin/env python3
"""
Test script to check visual content extraction from documents
"""

import os
import sys
import json
from pathlib import Path

# Add backend to path
sys.path.append('backend')

def check_visual_extraction_implementation():
    """Check if visual extraction is implemented in document_extractor.py"""
    print("Checking visual extraction implementation...")
    
    try:
        # Read the document_extractor.py file
        with open('backend/document_extractor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for table extraction methods
        table_methods = [
            'extract_tables',
            'get_tables', 
            'find_tables',
            'table_finder',
            'extract_table'
        ]
        
        image_methods = [
            'get_images',
            'extract_images',
            'find_images',
            'image_extraction'
        ]
        
        found_table_methods = [method for method in table_methods if method in content]
        found_image_methods = [method for method in image_methods if method in content]
        
        print(f"Table extraction methods found: {found_table_methods}")
        print(f"Image extraction methods found: {found_image_methods}")
        
        # Check for visual content functions
        visual_functions = [
            'extract_visual_content',
            'extract_visual_content_from_file'
        ]
        
        found_visual_functions = [func for func in visual_functions if func in content]
        print(f"Visual content functions found: {found_visual_functions}")
        
        if found_table_methods or found_image_methods or found_visual_functions:
            print("Some visual extraction methods are implemented")
            return True
        else:
            print("No visual extraction methods found in implementation")
            return False
            
    except Exception as e:
        print(f"Failed to check implementation: {e}")
        return False

def test_library_capabilities():
    """Test if required libraries support visual extraction"""
    print("\nTesting library capabilities...")
    
    try:
        import fitz  # PyMuPDF
        print("PyMuPDF imported successfully")
        
        # Test PyMuPDF capabilities
        doc = fitz.open()
        page = doc.new_page()
        
        # Check available methods
        page_methods = [method for method in dir(page) if any(keyword in method.lower() for keyword in ['table', 'image'])]
        print(f"PyMuPDF page methods for tables/images: {page_methods}")
        
        doc.close()
        
    except ImportError as e:
        print(f"PyMuPDF import error: {e}")
    
    try:
        import pdfplumber
        print("pdfplumber imported successfully")
        
        # Check pdfplumber capabilities
        page_methods = [method for method in dir(pdfplumber.page.Page) if 'table' in method.lower()]
        print(f"pdfplumber table methods: {page_methods}")
        
    except ImportError as e:
        print(f"pdfplumber import error: {e}")

def main():
    """Main test function"""
    print("Testing Visual Content Extraction for RailGPT")
    print("=" * 50)
    
    # Test 1: Check implementation
    implementation_ok = check_visual_extraction_implementation()
    
    # Test 2: Check library capabilities
    test_library_capabilities()
    
    print("\nSummary:")
    if not implementation_ok:
        print("ISSUE: Visual content extraction is not implemented")
        print("- Tables from PDFs are not being extracted as structured data")
        print("- Images from PDFs are not being extracted")
        print("- Charts and diagrams are not being processed")

if __name__ == "__main__":
    main() 