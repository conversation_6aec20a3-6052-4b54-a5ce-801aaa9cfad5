"""
<PERSON><PERSON><PERSON> to fix the embeddings and SQL functions in the database for RailGPT.
This script will:
1. Recreate the SQL functions with proper similarity calculation
2. Regenerate embeddings for all chunks
3. Test the search functionality with specific queries
"""
import os
import logging
import json
import time
from typing import List, Dict, Any
import numpy as np
from dotenv import load_dotenv
from supabase_client import supabase
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def generate_embedding(text: str) -> List[float]:
    """Generate embedding for text using the LLM router."""
    try:
        return llm_router.generate_embedding(text)
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Try with default model
        try:
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Use a deterministic random embedding as last resort
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def recreate_sql_functions():
    """Recreate the SQL functions with proper similarity calculation."""
    logger.info("Recreating SQL functions with proper similarity calculation...")
    
    # Drop existing functions
    drop_functions = [
        "DROP FUNCTION IF EXISTS direct_search_document_chunks(vector, float, int);",
        "DROP FUNCTION IF EXISTS hybrid_search_document_chunks(text, vector, float, int);",
        "DROP FUNCTION IF EXISTS direct_search_website_chunks(vector, float, int);",
        "DROP FUNCTION IF EXISTS hybrid_search_website_chunks(text, vector, float, int);"
    ]
    
    for drop_query in drop_functions:
        try:
            result = supabase.execute_query(drop_query)
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error dropping function: {result['error']}")
            else:
                logger.info(f"Successfully dropped function: {drop_query}")
        except Exception as e:
            logger.error(f"Error dropping function: {str(e)}")
    
    # Create new functions
    # Direct search for document chunks
    direct_search_doc_function = """
    CREATE OR REPLACE FUNCTION direct_search_document_chunks(
        query_embedding vector,
        match_threshold float DEFAULT 0.01,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        document_id uuid,
        chunk_index int,
        page_number int,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.embedding,
            dc.metadata,
            dc.created_at,
            dc.updated_at,
            CASE 
                WHEN dc.embedding IS NULL THEN 0.01
                ELSE 1 - (dc.embedding <=> query_embedding)
            END AS similarity
        FROM
            document_chunks dc
        WHERE
            CASE 
                WHEN dc.embedding IS NULL THEN TRUE
                ELSE 1 - (dc.embedding <=> query_embedding) > match_threshold
            END
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Hybrid search for document chunks
    hybrid_search_doc_function = """
    CREATE OR REPLACE FUNCTION hybrid_search_document_chunks(
        query_text text,
        query_embedding vector,
        match_threshold float DEFAULT 0.01,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        document_id uuid,
        chunk_index int,
        page_number int,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.embedding,
            dc.metadata,
            dc.created_at,
            dc.updated_at,
            CASE 
                WHEN dc.embedding IS NULL THEN 0.01 + (ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text)) * 0.2)
                ELSE (1 - (dc.embedding <=> query_embedding)) * 0.8 +
                     (ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text)) * 0.2)
            END AS similarity
        FROM
            document_chunks dc
        WHERE
            CASE 
                WHEN dc.embedding IS NULL THEN TRUE
                ELSE 1 - (dc.embedding <=> query_embedding) > match_threshold
            END
            OR to_tsvector('english', dc.text) @@ plainto_tsquery('english', query_text)
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Direct search for website chunks
    direct_search_web_function = """
    CREATE OR REPLACE FUNCTION direct_search_website_chunks(
        query_embedding vector,
        match_threshold float DEFAULT 0.01,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        website_id uuid,
        url text,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            wc.id,
            wc.website_id,
            wc.url,
            wc.text,
            wc.embedding,
            wc.metadata,
            wc.created_at,
            wc.updated_at,
            CASE 
                WHEN wc.embedding IS NULL THEN 0.01
                ELSE 1 - (wc.embedding <=> query_embedding)
            END AS similarity
        FROM
            website_chunks wc
        WHERE
            CASE 
                WHEN wc.embedding IS NULL THEN TRUE
                ELSE 1 - (wc.embedding <=> query_embedding) > match_threshold
            END
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Hybrid search for website chunks
    hybrid_search_web_function = """
    CREATE OR REPLACE FUNCTION hybrid_search_website_chunks(
        query_text text,
        query_embedding vector,
        match_threshold float DEFAULT 0.01,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        website_id uuid,
        url text,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            wc.id,
            wc.website_id,
            wc.url,
            wc.text,
            wc.embedding,
            wc.metadata,
            wc.created_at,
            wc.updated_at,
            CASE 
                WHEN wc.embedding IS NULL THEN 0.01 + (ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', query_text)) * 0.2)
                ELSE (1 - (wc.embedding <=> query_embedding)) * 0.8 +
                     (ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', query_text)) * 0.2)
            END AS similarity
        FROM
            website_chunks wc
        WHERE
            CASE 
                WHEN wc.embedding IS NULL THEN TRUE
                ELSE 1 - (wc.embedding <=> query_embedding) > match_threshold
            END
            OR to_tsvector('english', wc.text) @@ plainto_tsquery('english', query_text)
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Execute the function creation queries
    functions = [
        ("direct_search_document_chunks", direct_search_doc_function),
        ("hybrid_search_document_chunks", hybrid_search_doc_function),
        ("direct_search_website_chunks", direct_search_web_function),
        ("hybrid_search_website_chunks", hybrid_search_web_function)
    ]
    
    for name, query in functions:
        try:
            result = supabase.execute_query(query)
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error creating function {name}: {result['error']}")
            else:
                logger.info(f"Successfully created function {name}")
        except Exception as e:
            logger.error(f"Error creating function {name}: {str(e)}")

def regenerate_embeddings():
    """Regenerate embeddings for all chunks."""
    logger.info("Regenerating embeddings for all chunks...")
    
    # Get all document chunks
    doc_query = "SELECT id, text FROM document_chunks"
    doc_result = supabase.execute_query(doc_query)
    
    if isinstance(doc_result, dict) and "error" in doc_result:
        logger.error(f"Error getting document chunks: {doc_result['error']}")
    elif isinstance(doc_result, list):
        logger.info(f"Found {len(doc_result)} document chunks")
        
        # Regenerate embeddings for each chunk
        for i, chunk in enumerate(doc_result):
            try:
                chunk_id = chunk.get("id")
                chunk_text = chunk.get("text", "")
                
                if not chunk_text:
                    logger.warning(f"Empty text for chunk {chunk_id}, skipping")
                    continue
                
                # Generate embedding
                logger.info(f"Generating embedding for document chunk {i+1}/{len(doc_result)}")
                embedding = generate_embedding(chunk_text)
                
                # Update chunk with embedding
                update_query = f"""
                UPDATE document_chunks
                SET embedding = '{json.dumps(embedding)}'::vector
                WHERE id = '{chunk_id}'
                """
                
                update_result = supabase.execute_query(update_query)
                
                if isinstance(update_result, dict) and "error" in update_result:
                    logger.error(f"Error updating embedding for chunk {chunk_id}: {update_result['error']}")
                else:
                    logger.info(f"Updated embedding for document chunk {chunk_id}")
                
                # Sleep to avoid rate limiting
                time.sleep(0.5)
            except Exception as e:
                logger.error(f"Error regenerating embedding for document chunk: {str(e)}")
    
    # Get all website chunks
    web_query = "SELECT id, text FROM website_chunks"
    web_result = supabase.execute_query(web_query)
    
    if isinstance(web_result, dict) and "error" in web_result:
        logger.error(f"Error getting website chunks: {web_result['error']}")
    elif isinstance(web_result, list):
        logger.info(f"Found {len(web_result)} website chunks")
        
        # Regenerate embeddings for each chunk
        for i, chunk in enumerate(web_result):
            try:
                chunk_id = chunk.get("id")
                chunk_text = chunk.get("text", "")
                
                if not chunk_text:
                    logger.warning(f"Empty text for chunk {chunk_id}, skipping")
                    continue
                
                # Generate embedding
                logger.info(f"Generating embedding for website chunk {i+1}/{len(web_result)}")
                embedding = generate_embedding(chunk_text)
                
                # Update chunk with embedding
                update_query = f"""
                UPDATE website_chunks
                SET embedding = '{json.dumps(embedding)}'::vector
                WHERE id = '{chunk_id}'
                """
                
                update_result = supabase.execute_query(update_query)
                
                if isinstance(update_result, dict) and "error" in update_result:
                    logger.error(f"Error updating embedding for chunk {chunk_id}: {update_result['error']}")
                else:
                    logger.info(f"Updated embedding for website chunk {chunk_id}")
                
                # Sleep to avoid rate limiting
                time.sleep(0.5)
            except Exception as e:
                logger.error(f"Error regenerating embedding for website chunk: {str(e)}")

def test_search_functionality():
    """Test search functionality with specific queries."""
    logger.info("Testing search functionality with specific queries...")
    
    # Test queries
    test_queries = [
        "What is the full form of ACP?",
        "What is the full form of FSDS?",
        "What is the Rapid Response app?",
        "What is VASP and who developed it?"
    ]
    
    for query in test_queries:
        logger.info(f"\n=== Testing query: '{query}' ===\n")
        
        # Generate embedding for the query
        query_embedding = generate_embedding(query)
        
        # Test document search
        logger.info("Testing document search...")
        
        # Direct document search query
        doc_query = f"""
        SELECT * FROM direct_search_document_chunks(
            '{json.dumps(query_embedding)}'::vector,
            0.01,
            5
        )
        """
        
        doc_result = supabase.execute_query(doc_query)
        
        if isinstance(doc_result, dict) and "error" in doc_result:
            logger.error(f"Error in document search: {doc_result['error']}")
        else:
            logger.info(f"Document search found {len(doc_result)} results")
            for i, item in enumerate(doc_result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
                logger.info(f"Text snippet: {text_snippet}")
        
        # Test website search
        logger.info("Testing website search...")
        
        # Direct website search query
        web_query = f"""
        SELECT * FROM direct_search_website_chunks(
            '{json.dumps(query_embedding)}'::vector,
            0.01,
            5
        )
        """
        
        web_result = supabase.execute_query(web_query)
        
        if isinstance(web_result, dict) and "error" in web_result:
            logger.error(f"Error in website search: {web_result['error']}")
        else:
            logger.info(f"Website search found {len(web_result)} results")
            for i, item in enumerate(web_result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
                logger.info(f"Text snippet: {text_snippet}")

def main():
    """Main function to fix embeddings and SQL functions."""
    # Recreate SQL functions
    recreate_sql_functions()
    
    # Regenerate embeddings
    regenerate_embeddings()
    
    # Test search functionality
    test_search_functionality()

if __name__ == "__main__":
    main()
