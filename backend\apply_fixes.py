"""
Apply fixes for RailGPT document processing and Supabase integration issues.

This script:
1. Fixes the Supabase vector search functions
2. Verifies the document and website chunks tables
3. Tests the search functionality with the new fixes

Usage:
    python apply_fixes.py
"""

import os
import sys
import logging
import json
import time
from typing import List, Dict, Any
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from supabase_client import supabase

def fix_vector_search_functions():
    """Fix the Supabase vector search functions."""
    logger.info("Fixing vector search functions...")
    
    try:
        # Read the SQL script
        with open("fix_vector_search.sql", "r") as f:
            sql = f.read()
        
        # Execute the SQL script
        result = supabase.execute_sql(sql)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fixing vector search functions: {result['error']}")
            return False
        
        logger.info("Successfully fixed vector search functions")
        return True
    
    except Exception as e:
        logger.error(f"Error fixing vector search functions: {str(e)}")
        return False

def verify_tables():
    """Verify that the document and website chunks tables exist and have data."""
    logger.info("Verifying tables...")
    
    try:
        # Check document_chunks table
        doc_count_query = "SELECT COUNT(*) as count FROM document_chunks"
        doc_count_result = supabase.execute_query(doc_count_query)
        
        if isinstance(doc_count_result, dict) and "error" in doc_count_result:
            logger.error(f"Error checking document_chunks table: {doc_count_result['error']}")
            return False
        
        doc_count = doc_count_result[0]["count"] if doc_count_result and "count" in doc_count_result[0] else 0
        logger.info(f"Found {doc_count} document chunks")
        
        # Check website_chunks table
        web_count_query = "SELECT COUNT(*) as count FROM website_chunks"
        web_count_result = supabase.execute_query(web_count_query)
        
        if isinstance(web_count_result, dict) and "error" in web_count_result:
            logger.error(f"Error checking website_chunks table: {web_count_result['error']}")
            return False
        
        web_count = web_count_result[0]["count"] if web_count_result and "count" in web_count_result[0] else 0
        logger.info(f"Found {web_count} website chunks")
        
        return True
    
    except Exception as e:
        logger.error(f"Error verifying tables: {str(e)}")
        return False

def test_search_functions():
    """Test the search functions with a sample query."""
    logger.info("Testing search functions...")
    
    try:
        # Create a mock embedding
        import numpy as np
        np.random.seed(42)  # For reproducibility
        mock_embedding = list(np.random.rand(768))
        
        # Test direct_search_document_chunks
        logger.info("Testing direct_search_document_chunks...")
        direct_doc_query = f"""
        SELECT * FROM direct_search_document_chunks(
            '{json.dumps(mock_embedding)}'::vector,
            0.0,
            5
        )
        """
        direct_doc_result = supabase.execute_sql(direct_doc_query)
        
        if isinstance(direct_doc_result, dict) and "error" in direct_doc_result:
            logger.error(f"Error testing direct_search_document_chunks: {direct_doc_result['error']}")
        else:
            logger.info(f"direct_search_document_chunks returned {len(direct_doc_result)} results")
        
        # Test search_document_chunks
        logger.info("Testing search_document_chunks...")
        doc_query = f"""
        SELECT * FROM search_document_chunks(
            '{json.dumps(mock_embedding)}'::vector,
            0.3,
            5
        )
        """
        doc_result = supabase.execute_sql(doc_query)
        
        if isinstance(doc_result, dict) and "error" in doc_result:
            logger.error(f"Error testing search_document_chunks: {doc_result['error']}")
        else:
            logger.info(f"search_document_chunks returned {len(doc_result)} results")
        
        # Test hybrid_search_document_chunks
        logger.info("Testing hybrid_search_document_chunks...")
        hybrid_doc_query = f"""
        SELECT * FROM hybrid_search_document_chunks(
            'test query',
            '{json.dumps(mock_embedding)}'::vector,
            0.3,
            5
        )
        """
        hybrid_doc_result = supabase.execute_sql(hybrid_doc_query)
        
        if isinstance(hybrid_doc_result, dict) and "error" in hybrid_doc_result:
            logger.error(f"Error testing hybrid_search_document_chunks: {hybrid_doc_result['error']}")
        else:
            logger.info(f"hybrid_search_document_chunks returned {len(hybrid_doc_result)} results")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing search functions: {str(e)}")
        return False

def main():
    """Main function to apply all fixes."""
    logger.info("Starting fix application...")
    
    # Fix vector search functions
    if fix_vector_search_functions():
        logger.info("Vector search functions fixed successfully")
    else:
        logger.error("Failed to fix vector search functions")
    
    # Verify tables
    if verify_tables():
        logger.info("Tables verified successfully")
    else:
        logger.error("Failed to verify tables")
    
    # Test search functions
    if test_search_functions():
        logger.info("Search functions tested successfully")
    else:
        logger.error("Failed to test search functions")
    
    logger.info("Fix application completed")

if __name__ == "__main__":
    main()
