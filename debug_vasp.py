#!/usr/bin/env python3

import requests
import json

def debug_vasp_chunks():
    """Debug VASP document chunks"""
    print("🔍 Debugging VASP Document Chunks")
    print("=" * 40)
    
    try:
        # Check document chunks
        print("1. Checking document chunks...")
        response = requests.get("http://localhost:8000/api/debug/document-chunks", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            chunks = data.get('document_chunks', [])
            
            # Filter for VASP chunks
            vasp_chunks = [chunk for chunk in chunks if 'vasp' in chunk.get('filename', '').lower()]
            
            print(f"Total document chunks: {len(chunks)}")
            print(f"VASP chunks found: {len(vasp_chunks)}")
            
            for i, chunk in enumerate(vasp_chunks[:3]):  # Show first 3
                print(f"\nVASP Chunk {i+1}:")
                print(f"  Filename: {chunk.get('filename', 'Unknown')}")
                print(f"  Page: {chunk.get('page_number', 'Unknown')}")
                print(f"  Text preview: {chunk.get('text', '')[:100]}...")
                
                # Check if it has visual content metadata
                metadata = chunk.get('metadata', {})
                if metadata:
                    content_type = metadata.get('content_type', 'text')
                    print(f"  Content type: {content_type}")
                    if content_type != 'text':
                        print(f"  🎯 Visual content detected!")
        else:
            print(f"❌ Error getting document chunks: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_simple_query():
    """Test a very simple query"""
    print("\n🔍 Testing Simple Query")
    print("=" * 40)
    
    try:
        payload = {
            "query": "test",
            "model": "gemini-2.0-flash",
            "fallback_enabled": True
        }
        
        print("Sending simple test query...")
        response = requests.post("http://localhost:8000/api/query", json=payload, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Simple query works! Answer length: {len(data.get('answer', ''))}")
            print(f"LLM fallback used: {data.get('llm_fallback', False)}")
        else:
            print(f"❌ Simple query failed: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("❌ Simple query timed out")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    debug_vasp_chunks()
    test_simple_query() 