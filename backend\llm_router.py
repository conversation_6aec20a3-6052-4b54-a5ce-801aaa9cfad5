"""
LLM Router - Central module for handling different LLM models and embedding services

This module routes requests to the appropriate LLM model based on user selection
and provides unified interfaces for:
- Text generation
- Embedding generation
- Document chunking strategies
"""

import os
import logging
import json
from typing import List, Dict, Any, Optional, Tuple, Union
from dotenv import load_dotenv
import google.generativeai as genai
import numpy as np
import requests
from functools import lru_cache

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Load API keys from environment
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
QWEN_API_KEY = os.getenv("QWEN_API_KEY")
HF_API_KEY = os.getenv("HF_API_KEY")
OLLAMA_API_ENDPOINT = os.getenv("OLLAMA_API_ENDPOINT", "http://localhost:11434/api")

# Configure Gemini API if key is available
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    logger.info("Gemini API configured successfully")
else:
    logger.warning("GEMINI_API_KEY not found in environment variables")

# Model configuration - Updated to match frontend model IDs
MODEL_CONFIG = {
    # Gemini models
    "gemini-2.0-flash": {
        "name": "Gemini 2.0 Flash",
        "provider": "google",
        "embedding_model": "models/embedding-001",
        "max_tokens": 4096,
        "supports_embedding": True,
        "supports_chunking": True,
        "supports_summarization": True,
        "chunk_strategy": "standard"
    },
    "gemini": {
        "name": "Google Gemini",
        "provider": "google",
        "actual_model": "gemini-2.0-flash",  # Use gemini-2.0-flash as the actual model
        "embedding_model": "models/embedding-001",
        "max_tokens": 8192,
        "supports_embedding": True,
        "supports_chunking": True,
        "supports_summarization": True,
        "chunk_strategy": "standard"
    },
    # OpenAI models
    "chatgpt": {
        "name": "OpenAI ChatGPT",
        "provider": "openai",
        "embedding_model": "text-embedding-3-small",
        "max_tokens": 4096,
        "supports_embedding": True,
        "supports_chunking": True,
        "supports_summarization": True,
        "chunk_strategy": "token_aware"
    },
    # Groq models - Frontend shows groq-* but we map them to groq backend
    "groq": {
        "name": "Groq",
        "provider": "groq",
        "embedding_model": None,  # Uses Gemini for embeddings
        "max_tokens": 4096,
        "supports_embedding": False,
        "supports_chunking": False,
        "supports_summarization": True,
        "chunk_strategy": "standard"
    },
    "groq-llama3-70b": {
        "name": "Groq LLaMA3 70B",
        "provider": "groq",
        "actual_model": "llama3-70b-8192",
        "embedding_model": None,  # Uses Gemini for embeddings
        "max_tokens": 8192,
        "supports_embedding": False,
        "supports_chunking": False,
        "supports_summarization": True,
        "chunk_strategy": "standard"
    },
    "groq-llama3-8b": {
        "name": "Groq LLaMA3 8B",
        "provider": "groq", 
        "actual_model": "llama3-8b-8192",
        "embedding_model": None,  # Uses Gemini for embeddings
        "max_tokens": 8192,
        "supports_embedding": False,
        "supports_chunking": False,
        "supports_summarization": True,
        "chunk_strategy": "standard"
    },
    # DeepSeek models
    "deepseek": {
        "name": "DeepSeek",
        "provider": "deepseek",
        "embedding_model": None,  # Uses Gemini for embeddings
        "max_tokens": 8192,
        "supports_embedding": False,
        "supports_chunking": True,
        "supports_summarization": True,
        "chunk_strategy": "structured"
    },
    "deepseek-coder": {
        "name": "DeepSeek Coder",
        "provider": "deepseek",
        "actual_model": "deepseek-coder",
        "embedding_model": None,  # Uses Gemini for embeddings
        "max_tokens": 8192,
        "supports_embedding": False,
        "supports_chunking": True,
        "supports_summarization": True,
        "chunk_strategy": "structured"
    },
    # Qwen models
    "qwen": {
        "name": "Qwen",
        "provider": "qwen",
        "embedding_model": None,  # Uses Gemini for embeddings
        "max_tokens": 4096,
        "supports_embedding": False,
        "supports_chunking": False,
        "supports_summarization": True,
        "chunk_strategy": "standard"
    },
    # Hugging Face models
    "huggingface": {
        "name": "Hugging Face",
        "provider": "huggingface",
        "embedding_model": "sentence-transformers/all-mpnet-base-v2",
        "max_tokens": 2048,
        "supports_embedding": True,
        "supports_chunking": False,
        "supports_summarization": True,
        "chunk_strategy": "standard"
    },
    # Ollama models (local)
    "ollama": {
        "name": "Ollama",
        "provider": "ollama",
        "embedding_model": "nomic-embed-text",
        "embedding_model_id": "nomic-embed-text",
        "model_id": "llama3",
        "max_tokens": 4096,
        "supports_embedding": True,
        "supports_chunking": False,
        "supports_summarization": True,
        "chunk_strategy": "standard"
    }
}

# Only supporting gemini-2.0-flash
DEFAULT_MODEL = "gemini-2.0-flash"  # Only using gemini-2.0-flash as the standard model

# Cache for embeddings to improve performance and reduce API calls
EMBEDDING_CACHE = {}

def get_model_config(model_id: str) -> Dict[str, Any]:
    """
    Get the configuration for a specific model.
    Falls back to default model if the specified model is not available.
    """
    if model_id in MODEL_CONFIG:
        return MODEL_CONFIG[model_id]
    else:
        logger.warning(f"Model {model_id} not found in config, using {DEFAULT_MODEL} instead")
        return MODEL_CONFIG[DEFAULT_MODEL]

def is_model_available(model_id: str) -> bool:
    """Check if a model is available in the configuration."""
    # First check if the model exists in our config
    if model_id not in MODEL_CONFIG:
        return False
        
    # Check if necessary API keys exist for each provider
    provider = MODEL_CONFIG[model_id]["provider"]
    
    if provider == "google" and GEMINI_API_KEY:
        return True
    elif provider == "openai" and OPENAI_API_KEY:
        return True
    elif provider == "groq" and GROQ_API_KEY:
        return True
    elif provider == "deepseek" and DEEPSEEK_API_KEY:
        return True
    elif provider == "qwen" and QWEN_API_KEY:
        return True
    elif provider == "hf" and HF_API_KEY:
        return True
    elif provider == "ollama" and OLLAMA_API_ENDPOINT:
        return True
        
    # Default to using Gemini if available
    if model_id == DEFAULT_MODEL and GEMINI_API_KEY:
        return True
        
    return False

def get_available_models() -> List[Dict[str, Any]]:
    """Get a list of all available models with their configurations."""
    available_models = []

    for model_id, config in MODEL_CONFIG.items():
        if is_model_available(model_id):
            available_models.append({
                "id": model_id,
                "name": config.get("name", model_id),  # Use model_id as fallback if name missing
                "provider": config["provider"],
                "max_tokens": config["max_tokens"],
                "supports_embedding": config["supports_embedding"],
                "supports_chunking": config["supports_chunking"],
                "supports_summarization": config["supports_summarization"],
            })

    return available_models

@lru_cache(maxsize=100)
def generate_embedding(text: str, model_id: str = DEFAULT_MODEL) -> List[float]:
    """
    Generate embedding vector for text using the specified model.
    """
    # Check if we have this embedding cached
    cache_key = f"{model_id}:{hash(text)}"
    if cache_key in EMBEDDING_CACHE:
        return EMBEDDING_CACHE[cache_key]

    model_config = get_model_config(model_id)

    try:
        # Generate embedding based on provider
        if model_config["provider"] == "google":
            if not GEMINI_API_KEY:
                logger.warning("No Gemini API key, returning random embedding")
                return list(np.random.rand(768))

            response = genai.embed_content(
                model=model_config["embedding_model"],
                content=text,
                task_type="retrieval_document"
            )
            embedding = list(response['embedding'])

            # Cache the embedding
            EMBEDDING_CACHE[cache_key] = embedding
            return embedding

    except Exception as e:
        logger.error(f"Error generating embedding with {model_id}: {str(e)}")
        # Return random embedding in case of errors
        return list(np.random.rand(768))


def prioritize_document_context(context, query):
    """
    Analyze and prepare the context to ensure document sources are prioritized.
    """
    if not context:
        return context

    # Split into individual sources
    context_parts = context.split("\n\n")

    # Group sources by type
    doc_parts = [part for part in context_parts if "From '" in part and ".pdf" in part.lower()]
    web_parts = [part for part in context_parts if "From '" in part and ("http" in part.lower() or "www" in part.lower())]
    other_parts = [part for part in context_parts if part not in doc_parts and part not in web_parts]

    # Prioritize document parts first, then websites, then others
    prioritized_parts = doc_parts + web_parts + other_parts

    # Rejoin with more emphasis on the importance of document content
    if doc_parts:
        prioritized_context = "DOCUMENT SOURCES (PRIORITIZE THESE):\n" + "\n\n".join(doc_parts)
        if web_parts:
            prioritized_context += "\n\nWEBSITE SOURCES:\n" + "\n\n".join(web_parts)
        if other_parts:
            prioritized_context += "\n\nOTHER SOURCES:\n" + "\n\n".join(other_parts)
        return prioritized_context
    else:
        return context


def generate_answer(
    query: str,
    context: str,
    system_prompt: Optional[str] = None,
    model_id: str = DEFAULT_MODEL,
    use_documents_only: bool = True
) -> str:
    """
    Generate an answer to a question based on context.
    
    Args:
        query: The user's question
        context: Context information to help answer the question
        system_prompt: Custom system prompt to guide the model's response
        model_id: Which LLM model to use (defaults to Gemini)
        use_documents_only: If True, restrict to context only. If False, allow general knowledge.
    """
    # Check if the requested model is available and fallback if needed
    if not is_model_available(model_id):
        logger.warning(f"Model '{model_id}' is not available. Falling back to {DEFAULT_MODEL}")
        model_id = DEFAULT_MODEL
    
    model_config = get_model_config(model_id)
    provider = model_config["provider"]
    
    # Use default system prompt if none provided
    if not system_prompt:
        if context and context.strip():
            # If context is provided, focus on answering from it
            if use_documents_only:
                system_prompt = """You are RailGPT, an AI assistant specializing in Indian Railways.

CRITICAL INSTRUCTIONS:
1. You MUST ONLY use the information provided in the context below to answer the question.
2. If the context does not contain relevant information to answer the question, clearly state: 
   "I couldn't find any valid information to answer your question."
3. DO NOT use your general knowledge under any circumstances.
4. Format your response in a clear, readable manner with proper paragraphs and bullet points where appropriate.

Your goal is to provide accurate information based ONLY on the provided context, not to make up information."""
            else:
                system_prompt = """You are RailGPT, an AI assistant specializing in Indian Railways.

INSTRUCTIONS:
1. If the provided context contains relevant information, use ONLY that information to answer.
2. If the context does NOT contain relevant information, you SHOULD use your general knowledge to provide an accurate and helpful response.
3. Format your response in a clear, readable manner with proper paragraphs and bullet points where appropriate.
4. Be factual, accurate and detailed in your response.

Your goal is to be as helpful as possible, using context when available but falling back to your general knowledge when needed."""
        else:
            # If no context is provided, always use general knowledge
            system_prompt = """You are RailGPT, an AI assistant specializing in Indian Railways.

INSTRUCTIONS:
1. Use your general knowledge to provide a detailed, accurate answer to the user's question.
2. Format your response clearly with proper structure, using paragraphs, bullet points, or lists as appropriate.
3. If the question is about Indian Railways, provide specific and factual information.
4. If you don't know the answer, be honest and say so, but try to provide related information that might be helpful.

Your goal is to give the most accurate and comprehensive answer possible."""
            
        # Add a note to the model about which model is being used    
        system_prompt += f"\n\nNOTE: You are running as the '{model_id}' model."

    # Create combined prompt with context
    if context and context.strip():
        combined_prompt = f"""CONTEXT INFORMATION:
{context}

USER QUESTION:
{query}

IMPORTANT INSTRUCTIONS:
1. You MUST answer the question using ONLY the information provided in the CONTEXT INFORMATION above.
2. If the context doesn't contain enough information to fully answer the question, say "Based on the available document information..." and then answer with what you can find in the context.
3. DO NOT use your general knowledge unless absolutely necessary to interpret the context.
4. DO NOT make up information that isn't in the context.
5. Format your answer with clear paragraphs, bullet points for lists, and markdown formatting where appropriate.
6. If you find conflicting information in the context, prioritize information from document sources over website sources."""
    else:
        combined_prompt = f"""USER QUESTION:
{query}

Please answer the user's question based on your knowledge. Format your answer with clear paragraphs, bullet points for lists, and markdown formatting where appropriate."""

    try:
        # Generate answer based on provider
        if provider == "google":
            if not GEMINI_API_KEY:
                return "Unable to generate answer: No Gemini API key configured."

            generation_config = {
                "temperature": 0.2,
                "top_p": 0.8,
                "top_k": 40,
                "max_output_tokens": 1024,
            }

            # Check if there's an actual_model specified, otherwise use model_id
            actual_model_name = model_config.get("actual_model", model_id)
            logger.info(f"Using Google API model: {actual_model_name}")
            
            # Use the actual model name for API calls
            model = genai.GenerativeModel(
                model_name=actual_model_name,
                generation_config=generation_config
            )

            # Simple request with no retry logic
            try:
                logger.info("Sending request to Gemini API")
                convo = model.start_chat(history=[])

                # Send the prompt to the model with simplified handling
                if system_prompt:
                    # Start with system prompt to set context
                    response = convo.send_message(system_prompt + "\n\n" + combined_prompt)
                else:
                    # Send the combined prompt directly
                    response = convo.send_message(combined_prompt)

                # Get the answer from the response
                answer = response.text
                
                # Return the answer
                return answer
            except Exception as e:
                logger.error(f"Error with Gemini API: {str(e)}")
                raise

        elif provider == "groq":
            if not GROQ_API_KEY:
                logger.warning("No Groq API key, falling back to Gemini")
                return generate_answer(query, context, system_prompt, "gemini-2.0-flash")

            try:
                headers = {
                    "Authorization": f"Bearer {GROQ_API_KEY}",
                    "Content-Type": "application/json"
                }

                # Get the actual model name for Groq API
                actual_model = model_config.get("actual_model", "llama3-70b-8192")

                payload = {
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": combined_prompt}
                    ],
                    "model": actual_model,
                    "temperature": 0.2,
                    "max_tokens": 1024
                }

                response = requests.post(
                    "https://api.groq.com/openai/v1/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=60  # Increased timeout for Groq's large models
                )

                if response.status_code != 200:
                    logger.error(f"Error generating answer with Groq: {response.text}")
                    # Fall back to Gemini instead of raising an exception
                    logger.warning("Falling back to Gemini due to Groq API error")
                    return generate_answer(query, context, system_prompt, "gemini-2.0-flash")

                resp_json = response.json()
                return resp_json['choices'][0]['message']['content']

            except Exception as e:
                logger.error(f"Exception when using Groq API: {str(e)}")
                # Fall back to Gemini on any exception
                logger.warning("Falling back to Gemini due to Groq API error.")
                return generate_answer(query, context, system_prompt, "gemini-2.0-flash")

        elif provider == "deepseek":
            if not DEEPSEEK_API_KEY:
                logger.warning("No DeepSeek API key, falling back to Gemini")
                return generate_answer(query, context, system_prompt, "gemini-2.0-flash")

            headers = {
                "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
                "Content-Type": "application/json"
            }

            payload = {
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": combined_prompt}
                ],
                "model": "deepseek-coder",
                "temperature": 0.2,
                "max_tokens": 1024
            }

            response = requests.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=payload
            )

            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"]
            else:
                logger.error(f"DeepSeek API error: {response.text}")
                return f"Error generating answer with DeepSeek: {response.text}"

        elif provider == "qwen":
            if not QWEN_API_KEY:
                logger.warning("No Qwen API key, falling back to Gemini")
                return generate_answer(query, context, system_prompt, "gemini-2.0-flash")

            headers = {
                "Authorization": f"Bearer {QWEN_API_KEY}",
                "Content-Type": "application/json"
            }

            payload = {
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": combined_prompt}
                ],
                "model": "qwen-max",
                "temperature": 0.2,
                "max_tokens": 1024
            }

            response = requests.post(
                "https://api.qwen.ai/v1/chat/completions",
                headers=headers,
                json=payload
            )

            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"]
            else:
                logger.error(f"Qwen API error: {response.text}")
                return f"Error generating answer with Qwen: {response.text}"

        elif provider == "huggingface":
            if not HF_API_KEY:
                logger.warning("No Hugging Face API key, falling back to Gemini")
                return generate_answer(query, context, system_prompt, "gemini-2.0-flash")

            api_url = "https://api-inference.huggingface.co/models/mistralai/Mistral-7B-Instruct-v0.2"
            headers = {"Authorization": f"Bearer {HF_API_KEY}"}

            payload = {
                "inputs": f"{system_prompt}\n\n{combined_prompt}",
                "parameters": {
                    "temperature": 0.2,
                    "max_new_tokens": 1024
                }
            }

            response = requests.post(api_url, headers=headers, json=payload)

            if response.status_code == 200:
                return response.json()[0]["generated_text"]
            else:
                logger.error(f"Hugging Face API error: {response.text}")
                return f"Error generating answer with Hugging Face: {response.text}"

        elif provider == "ollama":
            # Use Ollama's completions endpoint
            payload = {
                "model": model_config.get("model_id", "llama3"),
                "prompt": f"{system_prompt}\n\n{combined_prompt}",
                "temperature": 0.2,
                "max_tokens": 1024
            }

            response = requests.post(f"{OLLAMA_API_ENDPOINT}/generate", json=payload)

            if response.status_code == 200:
                return response.json().get("response", "No response from Ollama")
            else:
                logger.error(f"Ollama API error: {response.text}")
                return f"Error generating answer with Ollama: {response.text}"

        else:
            logger.warning(f"Unknown provider {provider}, falling back to Gemini")
            return generate_answer(query, context, system_prompt, "gemini-2.0-flash")

    except Exception as e:
        logger.error(f"Error generating answer with {model_id}: {str(e)}")
        return f"Error generating answer: {str(e)}"

def create_chunks(
    text: str,
    filename: str,
    page_num: int,
    model_id: str = DEFAULT_MODEL,
    target_size: int = 400,
    overlap: int = 50
) -> List[Dict[str, Any]]:
    """
    Split text into chunks based on the specified model's chunking strategy.

    Args:
        text: The text to chunk
        filename: Source filename
        page_num: Page number in the source
        model_id: Model ID to determine chunking strategy
        target_size: Target chunk size in tokens
        overlap: Amount of overlap between chunks

    Returns:
        List of chunk objects with text and metadata
    """
    model_config = get_model_config(model_id)
    chunk_strategy = model_config["chunk_strategy"]

    # Clean text before chunking
    text = text.strip()
    if not text:
        return []

    chunks = []

    if chunk_strategy == "token_aware":
        # Token-aware chunking for OpenAI models
        words = text.split()

        # Use a sliding window approach with token estimation
        # Rough estimate: 1 token ≈ 4 characters in English
        chunk_size = target_size * 4  # characters approx
        stride = (target_size - overlap) * 4  # characters approx

        for i in range(0, len(text), stride):
            chunk_text = text[i:i + chunk_size]

            # Ensure chunk isn't just whitespace
            if chunk_text.strip():
                chunk_id = f"{filename}-p{page_num}-c{len(chunks)}"
                chunks.append({
                    "filename": filename,
                    "page": page_num,
                    "chunk_id": chunk_id,
                    "text": chunk_text,
                    "source_type": "document"
                })

    elif chunk_strategy == "structured":
        # Structured chunking that respects document sections
        # Look for section boundaries like headings
        import re

        # Split on headings or paragraph breaks
        section_pattern = r'(?:^|\n)(?:#{1,6}\s+[^\n]+|\d+\.\s+[^\n]+)|\n\s*\n'
        sections = re.split(section_pattern, text)

        # Process each section
        for i, section in enumerate(sections):
            section = section.strip()
            if not section:
                continue

            # For large sections, further split into paragraphs
            if len(section) > target_size * 4:
                paragraphs = re.split(r'\n\s*\n', section)

                # Group paragraphs to form chunks of appropriate size
                current_chunk = ""
                for para in paragraphs:
                    if len(current_chunk) + len(para) > target_size * 4 and current_chunk:
                        # Save current chunk and start a new one
                        chunk_id = f"{filename}-p{page_num}-s{i}-c{len(chunks)}"
                        chunks.append({
                            "filename": filename,
                            "page": page_num,
                            "chunk_id": chunk_id,
                            "text": current_chunk,
                            "source_type": "document"
                        })
                        current_chunk = para
                    else:
                        # Add to current chunk
                        current_chunk += ("\n\n" if current_chunk else "") + para

                # Add the last chunk if not empty
                if current_chunk:
                    chunk_id = f"{filename}-p{page_num}-s{i}-c{len(chunks)}"
                    chunks.append({
                        "filename": filename,
                        "page": page_num,
                        "chunk_id": chunk_id,
                        "text": current_chunk,
                        "source_type": "document"
                    })
            else:
                # Small enough section, use as a single chunk
                chunk_id = f"{filename}-p{page_num}-s{i}"
                chunks.append({
                    "filename": filename,
                    "page": page_num,
                    "chunk_id": chunk_id,
                    "text": section,
                    "source_type": "document"
                })

    else:
        # Standard chunking (default)
        words = text.split()
        chunk_size = target_size
        stride = target_size - overlap

        for i in range(0, len(words), stride):
            chunk_words = words[i:i + chunk_size]
            chunk_text = " ".join(chunk_words)

            if chunk_text.strip():
                chunk_id = f"{filename}-p{page_num}-c{len(chunks)}"
                chunks.append({
                    "filename": filename,
                    "page": page_num,
                    "chunk_id": chunk_id,
                    "text": chunk_text,
                    "source_type": "document"
                })

    # Generate embeddings for the chunks
    for chunk in chunks:
        try:
            chunk["embedding"] = generate_embedding(chunk["text"], model_id)
        except Exception as e:
            logger.error(f"Error generating embedding for chunk: {str(e)}")
            # Use a random embedding as fallback
            chunk["embedding"] = list(np.random.rand(768))

    return chunks

def summarize_document(
    text: str,
    model_id: str = DEFAULT_MODEL,
    max_length: int = 1000
) -> str:
    """
    Generate a summary of a document.

    Args:
        text: The document text to summarize
        model_id: The model ID to use for summarization
        max_length: Maximum summary length

    Returns:
        The document summary
    """
    # Truncate text if it's too long to fit in context
    if len(text) > 15000:
        text = text[:15000] + "...[text truncated for summarization]"

    system_prompt = """You are an AI assistant specializing in document summarization.
Create a comprehensive summary of the document below. Include:
1. Main topics and key information
2. Important facts and figures
3. Conclusions or recommendations
Organize the summary with clear sections and bullet points where appropriate."""

    user_prompt = f"""DOCUMENT TEXT:
{text}

Please provide a concise summary of this document, highlighting the most important information."""

    try:
        return generate_answer(user_prompt, "", system_prompt, model_id)
    except Exception as e:
        logger.error(f"Error generating document summary: {str(e)}")
        return "Error generating summary."

def score_answer_quality(
    query: str,
    answer: str,
    model_id: str = DEFAULT_MODEL
) -> Dict[str, Any]:
    """
    Score the quality of an answer.

    Args:
        query: The original query
        answer: The generated answer
        model_id: The model ID to use for scoring

    Returns:
        Dictionary with quality scores and feedback
    """
    system_prompt = """You are an AI assistant specializing in evaluating answer quality.
Score the answer to the given query on the following criteria (1-10 scale):
1. Relevance: How well the answer addresses the specific query
2. Accuracy: Correctness of information provided
3. Completeness: How thoroughly the answer covers the topic
4. Clarity: How easy the answer is to understand

Also provide brief feedback on how the answer could be improved."""

    user_prompt = f"""QUERY: {query}

ANSWER: {answer}

Please evaluate this answer based on the criteria above. Format your response as JSON with the following structure:
{{
  "relevance": score,
  "accuracy": score,
  "completeness": score,
  "clarity": score,
  "overall": average_score,
  "feedback": "brief feedback"
}}"""

    try:
        response = generate_answer(user_prompt, "", system_prompt, model_id)

        # Try to parse JSON response
        try:
            # Extract JSON if it's within a code block
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
                return json.loads(json_str)
            else:
                return json.loads(response)
        except json.JSONDecodeError:
            # If parsing fails, return a basic score
            return {
                "relevance": 5,
                "accuracy": 5,
                "completeness": 5,
                "clarity": 5,
                "overall": 5,
                "feedback": "Could not parse evaluation."
            }
    except Exception as e:
        logger.error(f"Error scoring answer quality: {str(e)}")
        return {
            "relevance": 5,
            "accuracy": 5,
            "completeness": 5,
            "clarity": 5,
            "overall": 5,
            "feedback": f"Error during evaluation: {str(e)}"
        }

# Add utility function to check API key validity
def check_api_key_validity(provider: str) -> Dict[str, Any]:
    """
    Check if the API key for a given provider is valid.

    Args:
        provider: The provider name (google, openai, etc.)

    Returns:
        Dictionary with validity status and message
    """
    try:
        if provider == "google":
            if not GEMINI_API_KEY:
                return {"valid": False, "message": "No Gemini API key configured."}

            # Test the API with a simple request
            genai.configure(api_key=GEMINI_API_KEY)
            model = genai.GenerativeModel("gemini-1.5-flash")
            _ = model.generate_content("Test")
            return {"valid": True, "message": "Gemini API key is valid."}

        elif provider == "openai":
            if not OPENAI_API_KEY:
                return {"valid": False, "message": "No OpenAI API key configured."}

            import openai
            openai.api_key = OPENAI_API_KEY
            _ = openai.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Test"}],
                max_tokens=5
            )
            return {"valid": True, "message": "OpenAI API key is valid."}

        elif provider == "groq":
            if not GROQ_API_KEY:
                return {"valid": False, "message": "No Groq API key configured."}

            # Test the API with a simple request
            headers = {
                "Authorization": f"Bearer {GROQ_API_KEY}",
                "Content-Type": "application/json"
            }

            payload = {
                "messages": [
                    {"role": "user", "content": "Test"}
                ],
                "model": "llama3-70b-8192",
                "max_tokens": 5
            }

            response = requests.post(
                "https://api.groq.com/openai/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                return {"valid": True, "message": "Groq API key is valid."}
            else:
                return {"valid": False, "message": f"Groq API error: {response.text}"}

        # Add other providers as needed
        else:
            return {"valid": False, "message": f"Unknown provider: {provider}"}

    except Exception as e:
        return {"valid": False, "message": f"Error checking API key: {str(e)}"}

    except Exception as e:
        return {"valid": False, "message": f"Error checking API key: {str(e)}"}
