#!/usr/bin/env python3
"""
Test script to debug document viewer path resolution
"""

import os
import requests

def test_server_document_viewer():
    """Test document viewer exactly like the server does"""
    print("=== Testing Document Viewer Path Resolution ===")
    
    # Get the script directory (like the server does)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Current script directory: {script_dir}")
    
    # Test files
    test_files = ["SampleRailwayDoc.pdf", "ACP 110V.docx"]
    
    for filename in test_files:
        print(f"\nTesting {filename}:")
        
        # Exact same path logic as in the server
        possible_paths = [
            os.path.join(script_dir, "backend", "data", "uploads", filename),
            os.path.join(script_dir, "backend", "data", filename),
            f"./backend/data/uploads/{filename}",
            f"./backend/data/{filename}",
            f"./data/uploads/{filename}",
            f"./data/{filename}",
            f"./uploads/{filename}",
            f"./documents/{filename}",
            f"./{filename}"
        ]
        
        file_path = None
        for i, path in enumerate(possible_paths):
            exists = os.path.exists(path)
            print(f"  Path {i+1}: {path} - {'EXISTS' if exists else 'NOT FOUND'}")
            if exists and file_path is None:
                file_path = path
        
        if file_path:
            print(f"  ✅ WOULD SERVE: {file_path}")
        else:
            print(f"  ❌ FILE NOT FOUND")

def test_backend_server_paths():
    """Test paths assuming server runs from backend directory"""
    print("\n=== Testing Backend Server Paths ===")
    
    # Simulate script_dir as backend directory
    script_dir = os.path.join(os.getcwd(), "backend")
    print(f"Simulated backend script directory: {script_dir}")
    
    test_files = ["SampleRailwayDoc.pdf", "ACP 110V.docx"]
    
    for filename in test_files:
        print(f"\nTesting {filename} from backend:")
        
        # Path logic when server runs from backend directory
        possible_paths = [
            os.path.join(script_dir, "data", "uploads", filename),
            os.path.join(script_dir, "data", filename)
        ]
        
        file_path = None
        for i, path in enumerate(possible_paths):
            exists = os.path.exists(path)
            print(f"  Path {i+1}: {path} - {'EXISTS' if exists else 'NOT FOUND'}")
            if exists and file_path is None:
                file_path = path
        
        if file_path:
            print(f"  ✅ WOULD SERVE: {file_path}")
        else:
            print(f"  ❌ FILE NOT FOUND")

def test_server_endpoint_with_debug():
    """Test the actual server endpoint with debug"""
    print("\n=== Testing Server Endpoint ===")
    
    try:
        # Test a simple endpoint first
        response = requests.get("http://localhost:8000/api/health")
        print(f"Health check: {response.status_code}")
        
        # Test document viewer with more detailed error info
        response = requests.get("http://localhost:8000/api/documents/view/SampleRailwayDoc.pdf")
        print(f"Document viewer response: {response.status_code}")
        if response.status_code != 200:
            print(f"Error details: {response.text}")
            
    except Exception as e:
        print(f"Server test error: {str(e)}")

if __name__ == "__main__":
    test_server_document_viewer()
    test_backend_server_paths()
    test_server_endpoint_with_debug() 