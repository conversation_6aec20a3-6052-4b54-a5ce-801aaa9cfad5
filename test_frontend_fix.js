// Simple test to verify API response structure
// Run this in browser console when on the RailGPT frontend

async function testAPIResponse() {
  console.log('🧪 Testing API Response Structure...');
  
  try {
    const response = await fetch('http://localhost:8000/api/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: 'FSDS system',
        model: 'gemini-2.0-flash'
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    console.log('✅ API Response received');
    console.log('📊 Response Analysis:', {
      hasAnswer: !!data.answer,
      answerLength: data.answer?.length || 0,
      hasDocumentAnswer: !!data.document_answer,
      documentAnswerLength: data.document_answer?.length || 0,
      hasWebsiteAnswer: !!data.website_answer,
      websiteAnswerLength: data.website_answer?.length || 0,
      documentSourcesCount: data.document_sources?.length || 0,
      websiteSourcesCount: data.website_sources?.length || 0,
      totalSourcesCount: data.sources?.length || 0,
      llmFallback: data.llm_fallback
    });
    
    console.log('📄 Document Answer Preview:', 
      data.document_answer ? data.document_answer.substring(0, 100) + '...' : 'NONE'
    );
    
    console.log('🌐 Website Answer Preview:', 
      data.website_answer ? data.website_answer.substring(0, 100) + '...' : 'NONE'
    );
    
    console.log('📚 Document Sources:', data.document_sources);
    console.log('🔗 Website Sources:', data.website_sources);
    
    // Test frontend rendering conditions
    const hasDocumentContent = !!(data.document_answer && data.document_answer.trim() !== "");
    const hasWebsiteContent = !!(data.website_answer && data.website_answer.trim() !== "");
    
    console.log('🎯 Frontend Display Logic:');
    console.log('  hasDocumentContent:', hasDocumentContent);
    console.log('  hasWebsiteContent:', hasWebsiteContent);
    
    if (hasDocumentContent && hasWebsiteContent) {
      console.log('  📋 Should show: BOTH document and website cards');
    } else if (hasDocumentContent) {
      console.log('  📋 Should show: DOCUMENT card only');
    } else if (hasWebsiteContent) {
      console.log('  📋 Should show: WEBSITE card only');
    } else {
      console.log('  📋 Should show: LLM FALLBACK card');
    }
    
    return data;
    
  } catch (error) {
    console.error('❌ API Test Failed:', error);
    return null;
  }
}

// Run the test
testAPIResponse(); 