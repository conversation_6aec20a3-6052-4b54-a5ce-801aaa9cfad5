#!/usr/bin/env python3
"""
Test script to upload a document and verify the complete flow.
"""

import requests
import json
import os

# Configuration
API_URL = "http://localhost:8000"

def test_upload_document():
    """Test uploading a document."""
    print("🧪 Testing document upload...")
    
    # Create a simple test file
    test_content = """Railway Safety Test Document

This is a test document for RailGPT.

Safety Guidelines:
1. Always check signals before proceeding
2. Maintain proper communication
3. Follow speed restrictions
4. Use protective equipment

This document is for testing the upload and viewing functionality."""

    # Write test file
    test_filename = "test-railway-safety.txt"
    with open(test_filename, 'w') as f:
        f.write(test_content)
    
    try:
        # Upload the document
        with open(test_filename, 'rb') as f:
            files = {'file': (test_filename, f, 'text/plain')}
            data = {'uploaded_by': 'test_user'}
            
            response = requests.post(
                f"{API_URL}/api/upload-document",
                files=files,
                data=data,
                timeout=30
            )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Upload successful!")
            print(f"   Message: {result.get('message', 'No message')}")
            print(f"   Chunks extracted: {result.get('chunks_extracted', 0)}")
            
            # Test fetching documents list
            docs_response = requests.get(f"{API_URL}/api/documents")
            if docs_response.status_code == 200:
                docs = docs_response.json()
                print(f"✅ Documents list retrieved: {len(docs)} documents found")
                
                # Look for our test document
                test_doc = None
                for doc in docs:
                    if test_filename in doc.get('name', ''):
                        test_doc = doc
                        break
                
                if test_doc:
                    print(f"✅ Test document found in list: {test_doc['name']}")
                    return test_doc
                else:
                    print("⚠️  Test document not found in list")
            else:
                print(f"❌ Failed to fetch documents: {docs_response.status_code}")
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
    finally:
        # Clean up test file
        if os.path.exists(test_filename):
            os.remove(test_filename)
    
    return None

def test_website_add():
    """Test adding a website."""
    print("\n🧪 Testing website addition...")
    
    try:
        data = {
            "url": "https://indianrailways.gov.in/railwayboard/",
            "submitted_by": "test_user"
        }
        
        response = requests.post(
            f"{API_URL}/api/add-website",
            json=data,
            timeout=60  # Website extraction can take longer
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Website add successful!")
            print(f"   Message: {result.get('message', 'No message')}")
            print(f"   Chunks extracted: {result.get('chunks_extracted', 0)}")
            
            # Test fetching websites list
            sites_response = requests.get(f"{API_URL}/api/websites")
            if sites_response.status_code == 200:
                sites = sites_response.json()
                print(f"✅ Websites list retrieved: {len(sites)} websites found")
                return True
            else:
                print(f"❌ Failed to fetch websites: {sites_response.status_code}")
        else:
            print(f"❌ Website add failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error during website test: {str(e)}")
    
    return False

if __name__ == "__main__":
    print("🚀 Starting RailGPT integration tests...")
    
    # Test document upload
    test_doc = test_upload_document()
    
    # Test website addition (this might fail due to network/extraction issues, but that's OK)
    test_website_add()
    
    print("\n✅ Tests completed!")
    print("\nNow you can:")
    print("1. Navigate to http://localhost:3000/documents to see uploaded documents")
    print("2. Navigate to http://localhost:3000/websites to see extracted websites") 
    print("3. Click on any source link in chat to test the document viewer")
