"""
Vector database implementation for efficient document and website search.
Supports both local FAISS-based search and Supabase pgvector search.
"""
import os
import json
import logging
import numpy as np
import faiss
from typing import List, Dict, Any
from dotenv import load_dotenv

# Import Supabase client
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Check if we should use Supabase for vector storage
USE_SUPABASE = os.getenv("USE_SUPABASE", "true").lower() == "true"

class VectorDB:
    """Vector database for efficient semantic search with Supabase pgvector support."""

    def __init__(self, dimension=768, index_file="data/faiss_index.bin", metadata_file="data/metadata.json"):
        """
        Initialize vector database.

        Args:
            dimension: Dimension of the embedding vectors
            index_file: Path to save/load the FAISS index (for local mode)
            metadata_file: Path to save/load the chunk metadata (for local mode)
        """
        self.dimension = dimension
        self.index_file = index_file
        self.metadata_file = metadata_file
        self.metadata = []  # Store chunk metadata (everything except embeddings)
        self.use_supabase = USE_SUPABASE

        if not self.use_supabase:
            # Local FAISS mode
            # Create a flat index for exact search
            self.index = faiss.IndexFlatIP(dimension)  # Inner product = cosine similarity for normalized vectors

            # Create directories if they don't exist
            os.makedirs(os.path.dirname(index_file), exist_ok=True)

            # Load existing index and metadata if they exist
            self._load_if_exists()

            logger.info("Using local FAISS vector database")
        else:
            # Supabase pgvector mode
            logger.info("Using Supabase pgvector for vector storage")

    def _load_if_exists(self):
        """Load index and metadata from disk if they exist (local mode only)."""
        if self.use_supabase:
            return

        try:
            if os.path.exists(self.index_file):
                logger.info(f"Loading vector index from {self.index_file}")
                self.index = faiss.read_index(self.index_file)

            if os.path.exists(self.metadata_file):
                logger.info(f"Loading metadata from {self.metadata_file}")
                with open(self.metadata_file, 'r') as f:
                    self.metadata = json.load(f)

            logger.info(f"Loaded {len(self.metadata)} chunks into memory")
        except Exception as e:
            logger.error(f"Error loading index or metadata: {str(e)}")
            # If loading fails, we'll start with empty index and metadata
            self.metadata = []
            self.index = faiss.IndexFlatIP(self.dimension)

    def save(self):
        """Save index and metadata to disk (local mode only)."""
        if self.use_supabase:
            return

        try:
            logger.info(f"Saving vector index to {self.index_file}")
            faiss.write_index(self.index, self.index_file)

            logger.info(f"Saving metadata to {self.metadata_file}")
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f)

            logger.info(f"Saved {len(self.metadata)} chunks")
        except Exception as e:
            logger.error(f"Error saving index or metadata: {str(e)}")

    def add_chunks(self, chunks: List[Dict[str, Any]], source_type: str = "document") -> None:
        """
        Add chunks to the vector database.

        Args:
            chunks: List of chunks to add
            source_type: Type of source ("document" or "website")
        """
        if not chunks:
            return

        if self.use_supabase:
            try:
                # In Supabase mode, we assume the chunks have already been stored in the database
                logger.info(f"Added {len(chunks)} {source_type} chunks via Supabase")
            except Exception as e:
                logger.error(f"Error adding chunks to Supabase: {str(e)}")
        else:
            # Local FAISS mode
            logger.info(f"Adding {len(chunks)} {source_type} chunks to local FAISS index")

            try:
                # Extract embeddings and prepare metadata
                embeddings = []
                chunk_metadata = []

                for i, chunk in enumerate(chunks):
                    if "embedding" in chunk:
                        # Ensure embedding is a valid numpy array
                        if isinstance(chunk["embedding"], str):
                            try:
                                # Parse string embedding (from JSON)
                                embedding = json.loads(chunk["embedding"])
                                chunk["embedding"] = embedding
                            except:
                                logger.warning(f"Failed to parse string embedding, creating dummy")
                                embedding = [0.01] * self.dimension
                        else:
                            embedding = chunk["embedding"]

                        embeddings.append(embedding)

                        # Create metadata without embedding (to save space)
                        metadata = {
                            k: v for k, v in chunk.items() if k != "embedding"
                        }
                        metadata["source_type"] = source_type  # Add source type for filtering
                        chunk_metadata.append(metadata)
                    else:
                        logger.warning(f"Chunk {i} has no embedding, skipping")

                if embeddings:
                    # Convert to numpy array
                    embeddings_array = np.array(embeddings).astype('float32')

                    # Add to FAISS index
                    self.index.add(embeddings_array)

                    # Add metadata
                    self.metadata.extend(chunk_metadata)

                    # Save to disk
                    self.save()

                logger.info(f"Added {len(embeddings)} embeddings to FAISS index")
            except Exception as e:
                logger.error(f"Error adding chunks to FAISS: {str(e)}")

    def search(self, query_embedding: List[float], top_k: int = 50, threshold: float = 0.18):
        """
        Search for chunks similar to the query embedding.

        Args:
            query_embedding: Query embedding vector
            top_k: Number of most similar chunks to return
            threshold: Minimum similarity threshold (default lowered to 0.3)

        Returns:
            List of metadata for most similar chunks
        """
        # Ensure query_embedding is a numpy array of floats
        if not isinstance(query_embedding, np.ndarray):
            try:
                if isinstance(query_embedding, str):
                    try:
                        query_embedding = json.loads(query_embedding)
                    except:
                        logger.error("Failed to parse string query_embedding")
                        return []
                query_embedding = np.array(query_embedding, dtype=np.float32)
            except Exception as e:
                logger.error(f"Error converting query_embedding: {str(e)}")
                return []

        if self.use_supabase:
            # The threshold is 1-cosine_distance for pgvector, so lower means more similar
            adjusted_threshold = threshold  # We want cosine similarity > threshold
            
            try:
                # Query both document and website chunks
                # Use a very low threshold to prioritize documents over accuracy
                # Search document chunks first
                logger.info(f"Searching document chunks in Supabase with threshold {adjusted_threshold}")
                doc_query = """
                    SELECT 
                        dc.id,
                        dc.document_id, 
                        dc.content, 
                        dc.metadata,
                        dc.chunk_index,
                        d.title,
                        d.file_path,
                        1 - (dc.embedding <=> $1) as similarity
                    FROM 
                        document_chunks dc
                    JOIN 
                        documents d ON dc.document_id = d.id
                    WHERE 
                        1 - (dc.embedding <=> $1) > $2
                    ORDER BY 
                        similarity DESC
                    LIMIT $3
                """
                
                doc_params = {
                    "1": query_embedding.tolist(),
                    "2": 0.01,  # Set extremely low threshold to find any matches
                    "3": top_k
                }
                
                doc_results = supabase.execute_query(doc_query, doc_params)
                
                if isinstance(doc_results, dict) and "error" in doc_results:
                    logger.error(f"Error in Supabase document search: {doc_results['error']}")
                    doc_results = []
                
                # Process document results
                for chunk in doc_results:
                    # Set source type for downstream processing
                    chunk["source_type"] = "document"
                    
                    # Boost document similarity scores
                    if "similarity" in chunk:
                        # Force document chunks to have very high similarity (0.99)
                        chunk["similarity"] = 0.99
                    
                    # Add source title based on document title
                    if "title" in chunk:
                        chunk["source_title"] = chunk["title"]
                
                # Then search website chunks
                logger.info(f"Searching website chunks in Supabase with threshold {adjusted_threshold}")
                web_query = """
                    SELECT 
                        wc.id,
                        wc.website_id, 
                        wc.content, 
                        wc.metadata,
                        wc.chunk_index,
                        w.title,
                        w.url,
                        1 - (wc.embedding <=> $1) as similarity
                    FROM 
                        website_chunks wc
                    JOIN 
                        websites w ON wc.website_id = w.id
                    WHERE 
                        1 - (wc.embedding <=> $1) > $2
                    ORDER BY 
                        similarity DESC
                    LIMIT $3
                """
                
                web_params = {
                    "1": query_embedding.tolist(),
                    "2": adjusted_threshold,
                    "3": top_k
                }
                
                web_results = supabase.execute_query(web_query, web_params)
                
                if isinstance(web_results, dict) and "error" in web_results:
                    logger.error(f"Error in Supabase website search: {web_results['error']}")
                    web_results = []
                
                # Process website results
                for chunk in web_results:
                    # Set source type for downstream processing
                    chunk["source_type"] = "website"
                    
                    # Add source title based on website title
                    if "title" in chunk:
                        chunk["source_title"] = chunk["title"]
                
                # Combine results
                all_results = doc_results + web_results
                
                # Sort by similarity score (descending)
                all_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                
                # Limit to top_k results
                all_results = all_results[:top_k]
                
                logger.info(f"Found {len(doc_results)} document chunks and {len(web_results)} website chunks in Supabase search")
                return all_results
            except Exception as e:
                logger.error(f"Error in Supabase search: {str(e)}")
                return []
        else:
            # Local FAISS mode
            # Ensure the query embedding is normalized and in the right format
            if len(query_embedding.shape) == 1:
                query_embedding = query_embedding.reshape(1, -1)
            
            # Normalize the query vector for cosine similarity
            faiss.normalize_L2(query_embedding)
            
            try:
                # Search the index
                if self.index.ntotal == 0:
                    logger.warning("FAISS index is empty, no results to return")
                    return []
                
                # Get similarity scores and indices
                similarities, indices = self.index.search(query_embedding, min(top_k, self.index.ntotal))
                
                # Flatten results
                similarities = similarities[0]
                indices = indices[0]
                
                # Filter by threshold and prepare results
                results = []
                for i, (similarity, idx) in enumerate(zip(similarities, indices)):
                    if similarity >= threshold and idx < len(self.metadata):
                        # Add metadata and similarity score
                        result = {**self.metadata[idx], "similarity": float(similarity)}
                        results.append(result)
                
                logger.info(f"Found {len(results)} chunks in local FAISS search")
                return results
            except Exception as e:
                logger.error(f"Error in FAISS search: {str(e)}")
                return []

    def hybrid_search(self, query_embedding: List[float], query_text: str = None, top_k: int = 50, threshold: float = 0.15):
        """
        Search for chunks with hybrid method (combine vector similarity with text matching).
        
        Args:
            query_embedding: Query embedding vector
            query_text: Raw query text for text matching
            top_k: Number of most similar chunks to return
            threshold: Minimum similarity threshold
            
        Returns:
            List of metadata for most similar chunks
        """
        # Ensure query_embedding is a numpy array of floats
        if not isinstance(query_embedding, np.ndarray):
            try:
                if isinstance(query_embedding, str):
                    import json
                    query_embedding = json.loads(query_embedding)
                query_embedding = np.array(query_embedding, dtype=np.float32)
            except Exception as e:
                logger.error(f"Error converting query_embedding: {str(e)}")
                # Create a default embedding as fallback
                query_embedding = np.array([0.01] * 768, dtype=np.float32)
                
        if self.use_supabase:
            # Supabase pgvector mode with hybrid search - search both documents and websites
            
            # If query_text is not provided, extract it from the first few chunks
            if query_text is None:
                # Fall back to regular semantic search
                logger.info("No query_text provided for hybrid search, falling back to semantic search")
                return self.search(query_embedding, top_k, threshold)
            
            # Define source priority weights
            SOURCE_PRIORITY = {
                "document": 1.5,  # Higher priority for documents
                "website": 1.2    # Medium priority for websites
            }
            
            try:
                # First search documents
                logger.info(f"Searching document chunks in Supabase with threshold {threshold}")
                doc_query = """
                    SELECT 
                        dc.id,
                        dc.document_id, 
                        dc.content, 
                        dc.metadata,
                        dc.chunk_index,
                        d.title,
                        d.file_path,
                        1 - (dc.embedding <=> $1) as similarity
                    FROM 
                        document_chunks dc
                    JOIN 
                        documents d ON dc.document_id = d.id
                    WHERE 
                        1 - (dc.embedding <=> $1) > $2
                    ORDER BY 
                        similarity DESC
                    LIMIT $3
                """
                
                doc_params = {
                    "1": query_embedding.tolist(),
                    "2": 0.01,  # Extremely low threshold to find any matches
                    "3": top_k
                }
                
                doc_results = supabase.execute_query(doc_query, doc_params)
                
                if isinstance(doc_results, dict) and "error" in doc_results:
                    logger.error(f"Error in Supabase document search: {doc_results['error']}")
                    doc_results = []
                
                # Process document results
                for chunk in doc_results:
                    # Set source type for downstream processing
                    chunk["source_type"] = "document"
                    
                    # Add source title based on document title
                    if "title" in chunk:
                        chunk["source_title"] = chunk["title"]
                    
                    # Boost document similarity scores
                    if "similarity" in chunk:
                        # Ensure document chunks get higher scores
                        chunk["similarity"] = min(0.99, chunk["similarity"])
                
                # Then search websites
                logger.info(f"Searching website chunks in Supabase with threshold {threshold}")
                web_query = """
                    SELECT 
                        wc.id,
                        wc.website_id, 
                        wc.content, 
                        wc.metadata,
                        wc.chunk_index,
                        w.title,
                        w.url,
                        1 - (wc.embedding <=> $1) as similarity
                    FROM 
                        website_chunks wc
                    JOIN 
                        websites w ON wc.website_id = w.id
                    WHERE 
                        1 - (wc.embedding <=> $1) > $2
                    ORDER BY 
                        similarity DESC
                    LIMIT $3
                """
                
                web_params = {
                    "1": query_embedding.tolist(),
                    "2": threshold,
                    "3": top_k
                }
                
                web_results = supabase.execute_query(web_query, web_params)
                
                if isinstance(web_results, dict) and "error" in web_results:
                    logger.error(f"Error in Supabase website search: {web_results['error']}")
                    web_results = []
                
                # Process website results
                for chunk in web_results:
                    # Set source type for downstream processing
                    chunk["source_type"] = "website"
                    
                    # Add source title based on website title
                    if "title" in chunk:
                        chunk["source_title"] = chunk["title"]
                
                # Apply priority weights to document results (force high similarity)
                for chunk in doc_results:
                    if "similarity" in chunk:
                        # Force document chunks to have very high similarity
                        chunk["similarity"] = min(1.0, chunk["similarity"] * SOURCE_PRIORITY.get("document", 1.2))
                
                # Apply priority weights to website results
                for chunk in web_results:
                    if "similarity" in chunk:
                        # Apply website priority weight (capped at 1.0)
                        chunk["similarity"] = min(1.0, chunk["similarity"] * SOURCE_PRIORITY.get("website", 1.0))
                
                # Combine results
                all_results = doc_results + web_results
                
                # Sort by similarity score (descending)
                all_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                
                # Limit to top_k results
                all_results = all_results[:top_k]
                
                logger.info(f"Found {len(doc_results)} document chunks and {len(web_results)} website chunks in Supabase hybrid search")
                return all_results
            except Exception as e:
                logger.error(f"Error in Supabase hybrid search: {str(e)}")
                # Fall back to regular search
                return self.search(query_embedding, top_k, threshold)
        else:
            # For local mode, just use regular search as hybrid search is not implemented
            return self.search(query_embedding, top_k, threshold)

    def clear(self):
        """Clear the index and metadata (for testing)."""
        if self.use_supabase:
            logger.warning("Clear operation not supported in Supabase mode")
            return

        self.index = faiss.IndexFlatIP(self.dimension)
        self.metadata = []

        # Delete files if they exist
        if os.path.exists(self.index_file):
            os.remove(self.index_file)
        if os.path.exists(self.metadata_file):
            os.remove(self.metadata_file)

        logger.info("Cleared local vector database")

    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector database."""
        if self.use_supabase:
            try:
                # Get document chunks count
                doc_count_query = "SELECT COUNT(*) as count FROM document_chunks"
                doc_count_result = supabase.execute_query(doc_count_query)
                doc_count = doc_count_result[0]["count"] if doc_count_result and "count" in doc_count_result[0] else 0

                # Get website chunks count
                web_count_query = "SELECT COUNT(*) as count FROM website_chunks"
                web_count_result = supabase.execute_query(web_count_query)
                web_count = web_count_result[0]["count"] if web_count_result and "count" in web_count_result[0] else 0

                # Calculate total
                total_count = doc_count + web_count

                return {
                    "storage_type": "supabase_pgvector",
                    "total_chunks": total_count,
                    "document_chunks": doc_count,
                    "website_chunks": web_count
                }
            except Exception as e:
                logger.error(f"Error getting Supabase stats: {str(e)}")
                return {
                    "storage_type": "supabase_pgvector",
                    "total_chunks": "unknown",
                    "document_chunks": "unknown",
                    "website_chunks": "unknown",
                    "error": str(e)
                }
        else:
            return {
                "storage_type": "local_faiss",
                "total_chunks": len(self.metadata),
                "index_size": self.index.ntotal,
                "document_chunks": sum(1 for item in self.metadata if item.get("source_type") == "document"),
                "website_chunks": sum(1 for item in self.metadata if item.get("source_type") == "website")
            }

    def is_initialized(self) -> bool:
        """Check if the vector database is initialized and ready to use."""
        if self.use_supabase:
            # For Supabase, we assume it's always initialized
            return True
        else:
            # For local mode, check if we have any vectors
            return self.index.ntotal > 0

# Create global instance to use throughout the application
vector_db = VectorDB()
