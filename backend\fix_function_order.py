#!/usr/bin/env python3
"""
Quick fix to move text-based search functions to the correct position in server.py
"""

def fix_function_order():
    # Read the server file
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the text-based search functions at the end
    start_marker = 'def text_based_document_search(query: str, top_k: int = 10):'
    start_idx = content.find(start_marker)
    
    if start_idx == -1:
        print("❌ Could not find text_based_document_search function")
        return False
    
    # Extract everything from the start of the first function to the end
    functions_section = content[start_idx:]
    
    # Remove the functions from the end
    content_without_functions = content[:start_idx].rstrip()
    
    # Find where to insert the functions (after search_for_acronym, before "# Load environment variables")
    insert_marker = '# Load environment variables'
    insert_idx = content_without_functions.find(insert_marker)
    
    if insert_idx == -1:
        print("❌ Could not find insertion point")
        return False
    
    # Insert the functions at the correct position
    new_content = (
        content_without_functions[:insert_idx] + 
        functions_section + 
        '\n\n' + 
        content_without_functions[insert_idx:]
    )
    
    # Write the fixed content back
    with open('server.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ Successfully moved text-based search functions to correct position")
    return True

if __name__ == "__main__":
    fix_function_order() 