import os
import logging
import re
import uuid
from typing import List, Dict, Any, Optional
import fitz  # PyMuPDF
import pdfplumber
import pytesseract
from pdf2image import convert_from_path
import docx
import openpyxl
import textract
from PIL import Image
from dotenv import load_dotenv

# Import Supabase client
from supabase_client import supabase

# Import visual content extractor
from visual_extractor import extract_visual_content_from_file

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Check if we should use Supabase
USE_SUPABASE = os.getenv("USE_SUPABASE", "true").lower() == "true"

# Tesseract config - update path if needed for Windows
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Text cleanup function
def clean_text(text: str) -> str:
    """Clean and normalize text from document extraction."""
    if not text:
        return ""
    
    # Fix character duplication issues (common with certain PDFs)
    # Remove repeated characters (e.g., "TTrraannssppoortr" -> "Transport")
    text = re.sub(r'(.)\1{2,}', r'\1\1', text)  # Reduce 3+ repeated chars to 2
    
    # Fix duplicated spaces and words
    text = re.sub(r'(\w+)\s+\1', r'\1', text)  # Remove duplicated words
    
    # Clean up spacing
    text = re.sub(r'\s+', ' ', text)  # Replace multiple whitespace with single space
    text = re.sub(r'\n\s*\n', '\n', text)  # Clean up multiple newlines
    
    # Fix common OCR/extraction issues
    text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)  # Add space between lowercase and uppercase
    text = re.sub(r'([0-9])([A-Za-z])', r'\1 \2', text)  # Add space between numbers and letters
    text = re.sub(r'([A-Za-z])([0-9])', r'\1 \2', text)  # Add space between letters and numbers
    
    # Clean up punctuation spacing
    text = re.sub(r'([.!?])+', r'\1', text)  # Remove multiple punctuation
    text = re.sub(r'\s+([.!?])', r'\1', text)  # Remove space before punctuation
    text = re.sub(r'([.!?])([A-Z])', r'\1 \2', text)  # Add space after punctuation before capital letter
    
    return text.strip()

def extract_from_pdf_fitz(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from PDF using PyMuPDF (fitz)."""
    try:
        logger.info(f"Extracting text from PDF using PyMuPDF: {file_path}")
        result = []

        # Open the PDF with PyMuPDF
        with fitz.open(file_path) as pdf:
            filename = os.path.basename(file_path)

            # Extract text from each page
            for page_num, page in enumerate(pdf):
                text = page.get_text()
                cleaned_text = clean_text(text)

                # Skip empty pages
                if not cleaned_text:
                    logger.warning(f"Empty page {page_num+1} in {filename} using PyMuPDF")
                    continue

                result.append({
                    "filename": filename,
                    "page": page_num + 1,
                    "text": cleaned_text,
                    "extraction_method": "pymupdf"
                })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using PyMuPDF: {str(e)}")
        return []

def extract_from_pdf_pdfplumber(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from PDF using pdfplumber (better layout handling)."""
    try:
        logger.info(f"Extracting text from PDF using pdfplumber: {file_path}")
        result = []

        # Open the PDF with pdfplumber
        with pdfplumber.open(file_path) as pdf:
            filename = os.path.basename(file_path)

            # Extract text from each page
            for page_num, page in enumerate(pdf.pages):
                text = page.extract_text()
                cleaned_text = clean_text(text)

                # Skip empty pages
                if not cleaned_text:
                    logger.warning(f"Empty page {page_num+1} in {filename} using pdfplumber")
                    continue

                result.append({
                    "filename": filename,
                    "page": page_num + 1,
                    "text": cleaned_text,
                    "extraction_method": "pdfplumber"
                })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using pdfplumber: {str(e)}")
        return []

def extract_from_pdf_ocr(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from PDF using OCR (for scanned PDFs)."""
    try:
        logger.info(f"Extracting text from PDF using OCR: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Convert PDF to images
        logger.info(f"Converting PDF to images: {file_path}")
        images = convert_from_path(file_path)

        # Process each page/image
        for page_num, image in enumerate(images):
            logger.info(f"OCR processing page {page_num+1} of {filename}")

            # Perform OCR
            text = pytesseract.image_to_string(image)
            cleaned_text = clean_text(text)

            # Skip empty pages
            if not cleaned_text:
                logger.warning(f"Empty page {page_num+1} in {filename} after OCR")
                continue

            result.append({
                "filename": filename,
                "page": page_num + 1,
                "text": cleaned_text,
                "extraction_method": "ocr"
            })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using OCR: {str(e)}")
        return []

def extract_from_docx_python_docx(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from DOCX using python-docx."""
    try:
        logger.info(f"Extracting text from DOCX using python-docx: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Open the document
        doc = docx.Document(file_path)

        # Extract all paragraphs
        all_paragraphs = [para.text for para in doc.paragraphs if para.text.strip()]

        # Combine paragraphs into a single text
        text = "\n".join(all_paragraphs)
        cleaned_text = clean_text(text)

        if cleaned_text:
            result.append({
                "filename": filename,
                "page": 1,  # DOCX doesn't have pages, so use 1
                "text": cleaned_text,
                "extraction_method": "python-docx"
            })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using python-docx: {str(e)}")
        return []

def extract_from_docx_textract(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from DOCX using textract (fallback)."""
    try:
        logger.info(f"Extracting text from DOCX using textract: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Extract text using textract
        text = textract.process(file_path).decode('utf-8')
        cleaned_text = clean_text(text)

        if cleaned_text:
            result.append({
                "filename": filename,
                "page": 1,  # DOCX doesn't have pages, so use 1
                "text": cleaned_text,
                "extraction_method": "textract"
            })

        return result

    except Exception as e:
        logger.error(f"Error extracting text using textract: {str(e)}")
        return []

def extract_from_xlsx(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from Excel files."""
    try:
        logger.info(f"Extracting text from Excel: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Open the workbook
        workbook = openpyxl.load_workbook(file_path, data_only=True)

        # Process each sheet
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            sheet_text = []

            # Process each row
            for row in sheet.iter_rows(values_only=True):
                # Skip empty rows
                if any(cell is not None and str(cell).strip() for cell in row):
                    row_text = " | ".join([str(cell) if cell is not None else "" for cell in row])
                    sheet_text.append(row_text)

            # Combine all rows for this sheet
            if sheet_text:
                combined_text = f"[Sheet: {sheet_name}]\n" + "\n".join(sheet_text)

                result.append({
                    "filename": filename,
                    "page": sheet_name,  # Use sheet name as page
                    "text": combined_text,
                    "extraction_method": "openpyxl"
                })

        return result

    except Exception as e:
        logger.error(f"Error extracting text from Excel: {str(e)}")
        return []

def extract_from_image(file_path: str) -> List[Dict[str, Any]]:
    """Extract text from image files using OCR."""
    try:
        logger.info(f"Extracting text from image using OCR: {file_path}")
        result = []
        filename = os.path.basename(file_path)

        # Open the image and perform OCR
        image = Image.open(file_path)
        text = pytesseract.image_to_string(image)
        cleaned_text = clean_text(text)

        if cleaned_text:
            result.append({
                "filename": filename,
                "page": 1,  # Image has one page
                "text": cleaned_text,
                "extraction_method": "image-ocr"
            })

        return result

    except Exception as e:
        logger.error(f"Error extracting text from image: {str(e)}")
        return []

def create_chunks(extracted_items: List[Dict[str, Any]], target_size: int = 400, overlap: int = 100) -> List[Dict[str, Any]]:
    """
    Split extracted text into chunks with metadata, preserving context.
    """
    chunks = []

    for item in extracted_items:
        text = item.get("text", "")
        if not text:
            continue

        # Get metadata from extraction
        filename = item.get("filename", "unknown")
        page = item.get("page", 1)
        extraction_method = item.get("extraction_method", "unknown")

        # Add document title as context prefix
        doc_title = os.path.splitext(filename)[0]
        context_prefix = f"Document: {doc_title}, Page: {page}\n\n"

        # Split text into sentences first
        sentences = re.split(r'(?<=[.!?])\s+', text)

        # Group sentences into chunks
        current_chunk = []
        current_word_count = 0
        chunk_index = 0

        for sentence in sentences:
            # Count words in this sentence
            sentence_words = sentence.split()
            sentence_word_count = len(sentence_words)

            # If adding this sentence would exceed the target size and we already have content,
            # create a chunk and start a new one
            if current_word_count + sentence_word_count > target_size and current_word_count > 0:
                # Create chunk from current sentences
                chunk_text = " ".join(current_chunk)

                # Determine chunk position (start, middle, end)
                position = "start" if chunk_index == 0 else "middle"

                # Create unique chunk ID
                chunk_id = f"{os.path.splitext(filename)[0]}_{page}_{chunk_index}"

                # Add context prefix to the chunk text
                full_chunk_text = context_prefix + chunk_text

                chunks.append({
                    "filename": filename,
                    "page": page,
                    "chunk_id": chunk_id,
                    "text": full_chunk_text,
                    "source_type": "document",
                    "extraction_method": extraction_method,
                    "position": position,
                    "word_count": current_word_count
                })

                # Start a new chunk with overlap
                # Take the last few sentences to maintain context
                overlap_sentences = []
                overlap_word_count = 0

                # Work backwards through current_chunk to get overlap sentences
                for s in reversed(current_chunk):
                    s_words = s.split()
                    if overlap_word_count + len(s_words) <= overlap:
                        overlap_sentences.insert(0, s)
                        overlap_word_count += len(s_words)
                    else:
                        break

                # Reset current chunk with overlap sentences
                current_chunk = overlap_sentences
                current_word_count = overlap_word_count
                chunk_index += 1

            # Add the current sentence to the chunk
            current_chunk.append(sentence)
            current_word_count += sentence_word_count

        # Don't forget the last chunk if there's anything left
        if current_chunk:
            chunk_text = " ".join(current_chunk)

            # Mark as the end chunk
            position = "end" if chunk_index > 0 else "single"

            # Create unique chunk ID
            chunk_id = f"{os.path.splitext(filename)[0]}_{page}_{chunk_index}"

            # Add context prefix to the chunk text
            full_chunk_text = context_prefix + chunk_text

            chunks.append({
                "filename": filename,
                "page": page,
                "chunk_id": chunk_id,
                "text": full_chunk_text,
                "source_type": "document",
                "extraction_method": extraction_method,
                "position": position,
                "word_count": current_word_count
            })

    return chunks

def extract_document(file_path: str, supabase_file_path: str = None, uploaded_by: str = None) -> List[Dict[str, Any]]:
    """
    Extract text from various document formats with fallback mechanisms.
    """
    logger.info(f"Extracting document: {file_path}")

    if not os.path.exists(file_path):
        logger.error(f"File does not exist: {file_path}")
        return []

    # Get file extension and size
    _, ext = os.path.splitext(file_path)
    ext = ext.lower()
    file_size = os.path.getsize(file_path)
    filename = os.path.basename(file_path)

    extracted_items = []

    # Track extraction method for metadata
    extraction_method = "unknown"

    # PDF documents
    if ext == '.pdf':
        # Try PyMuPDF first (fastest and most accurate for text PDFs)
        extracted_items = extract_from_pdf_fitz(file_path)
        if extracted_items and any(item.get("text", "") for item in extracted_items):
            extraction_method = "pymupdf"

        # If PyMuPDF failed to extract text, try pdfplumber
        if not extracted_items or all(not item.get("text", "") for item in extracted_items):
            logger.info(f"PyMuPDF extraction failed or empty, trying pdfplumber for {file_path}")
            extracted_items = extract_from_pdf_pdfplumber(file_path)
            if extracted_items and any(item.get("text", "") for item in extracted_items):
                extraction_method = "pdfplumber"

        # If pdfplumber failed, try OCR
        if not extracted_items or all(not item.get("text", "") for item in extracted_items):
            logger.info(f"Pdfplumber extraction failed or empty, trying OCR for {file_path}")
            extracted_items = extract_from_pdf_ocr(file_path)
            if extracted_items and any(item.get("text", "") for item in extracted_items):
                extraction_method = "ocr"

    # Word documents
    elif ext == '.docx':
        # Try python-docx first
        extracted_items = extract_from_docx_python_docx(file_path)
        if extracted_items and any(item.get("text", "") for item in extracted_items):
            extraction_method = "python-docx"

        # If python-docx failed, try textract
        if not extracted_items or all(not item.get("text", "") for item in extracted_items):
            logger.info(f"python-docx extraction failed or empty, trying textract for {file_path}")
            extracted_items = extract_from_docx_textract(file_path)
            if extracted_items and any(item.get("text", "") for item in extracted_items):
                extraction_method = "textract"

    # Excel files
    elif ext == '.xlsx':
        extracted_items = extract_from_xlsx(file_path)
        if extracted_items and any(item.get("text", "") for item in extracted_items):
            extraction_method = "openpyxl"

    # Image files
    elif ext in ['.jpg', '.jpeg', '.png', '.tiff', '.tif']:
        extracted_items = extract_from_image(file_path)
        if extracted_items and any(item.get("text", "") for item in extracted_items):
            extraction_method = "image-ocr"

    # Unsupported file type
    else:
        logger.warning(f"Unsupported file type: {ext}")
        return []

    if not extracted_items:
        logger.warning(f"Could not extract text from {file_path} using any method")
        return []

    # Create chunks from extracted items
    chunks = create_chunks(extracted_items)

    # Store document in Supabase if enabled
    document_id = None
    if USE_SUPABASE:
        try:
            # Upload file to Supabase Storage if we have a local file path
            supabase_storage_path = None
            if os.path.exists(file_path):
                # Create a storage path based on the file type and name
                storage_path = f"{ext.replace('.', '')}/{filename}"

                # Upload to Supabase Storage
                logger.info(f"Uploading file to Supabase Storage: {storage_path}")
                upload_result = supabase.upload_file("documents", file_path, storage_path)

                if "error" not in upload_result:
                    supabase_storage_path = storage_path
                    logger.info(f"File uploaded to Supabase Storage: {supabase_storage_path}")
                else:
                    logger.warning(f"Error uploading file to Supabase Storage: {upload_result.get('error')}")

            # Create document entry in Supabase
            document_data = {
                "filename": filename,
                "display_name": filename,
                "file_path": supabase_file_path or supabase_storage_path or file_path,
                "file_type": ext.replace('.', ''),
                "file_size": file_size,
                "uploaded_by": uploaded_by,
                "status": "processed",
                "extraction_method": extraction_method
            }

            # Store document metadata in Supabase
            result = supabase.store_document(
                filename=document_data["filename"],
                display_name=document_data["display_name"],
                file_path=document_data["file_path"],
                file_type=document_data["file_type"],
                file_size=document_data["file_size"],
                uploaded_by=document_data["uploaded_by"]
            )

            if "error" in result:
                logger.error(f"Error storing document in Supabase: {result['error']}")
            else:
                document_id = result.get("id")
                logger.info(f"Stored document in Supabase with ID: {document_id}")

                # Add document_id to all chunks
                for chunk in chunks:
                    chunk["document_id"] = document_id
                    chunk["chunk_index"] = chunks.index(chunk)
                    chunk["page_number"] = chunk.get("page", 1)
                    chunk["metadata"] = {
                        "extraction_method": extraction_method,
                        "filename": filename,
                        "file_type": ext.replace('.', ''),
                        "file_size": file_size
                    }

                # Store chunks in Supabase
                for chunk in chunks:
                    chunk_result = supabase.store_document_chunk(
                        document_id=document_id,
                        chunk_index=chunk["chunk_index"],
                        page_number=chunk["page_number"],
                        text=chunk["text"],
                        embedding=chunk.get("embedding", []),
                        metadata=chunk["metadata"],
                        source_type=chunk["source_type"]
                    )
                    if "error" in chunk_result:
                        logger.error(f"Error storing document chunk: {chunk_result['error']}")
                    else:
                        logger.info(f"Stored document chunk with ID: {chunk_result.get('id')}")

        except Exception as e:
            logger.error(f"Error storing document in Supabase: {str(e)}")

    logger.info(f"Extracted {len(chunks)} chunks from {file_path}")
    return chunks

def extract_document_with_visual_content(file_path: str, supabase_file_path: str = None, 
                                       uploaded_by: str = None, extract_tables: bool = True,
                                       extract_images: bool = True, extract_charts: bool = True) -> List[Dict[str, Any]]:
    """
    Extract both text and visual content from documents.
    
    Args:
        file_path: Path to the document file
        supabase_file_path: Path to the file in Supabase Storage (if applicable)
        uploaded_by: User ID of the uploader (if applicable)
        extract_tables: Whether to extract tables
        extract_images: Whether to extract images
        extract_charts: Whether to detect charts/diagrams
    
    Returns:
        List of document chunks including both text and visual content
    """
    logger.info(f"Extracting document with visual content: {file_path}")
    
    # First extract regular text content
    text_chunks = extract_document(file_path, supabase_file_path, uploaded_by)
    
    # Then extract visual content for PDFs
    _, ext = os.path.splitext(file_path)
    if ext.lower() == '.pdf' and (extract_tables or extract_images or extract_charts):
        try:
            logger.info(f"Extracting visual content from PDF: {file_path}")
            visual_content = extract_visual_content_from_file(
                file_path, 
                extract_tables=extract_tables,
                extract_images=extract_images, 
                extract_charts=extract_charts
            )
            
            # Store visual content in Supabase Storage if we have a document ID
            document_id = None
            if text_chunks and len(text_chunks) > 0:
                document_id = text_chunks[0].get("document_id")
                
            visual_storage_result = None
            if USE_SUPABASE and document_id and (visual_content.get("images") or visual_content.get("tables")):
                try:
                    logger.info(f"Storing visual content in Supabase Storage for document {document_id}")
                    visual_storage_result = supabase.store_visual_content(document_id, visual_content)
                    logger.info(f"Visual content storage result: {len(visual_storage_result.get('images', []))} images, {len(visual_storage_result.get('tables', []))} tables stored")
                except Exception as e:
                    logger.error(f"Error storing visual content in Supabase Storage: {str(e)}")
            
            # Convert visual content to chunks with enhanced metadata
            visual_chunks = create_visual_content_chunks(visual_content, visual_storage_result)
            
            # Add document metadata to visual chunks if we have it
            if document_id:
                for chunk in visual_chunks:
                    chunk["document_id"] = document_id
                    chunk["chunk_index"] = len(text_chunks) + visual_chunks.index(chunk)
                    chunk["page_number"] = chunk.get("page", 1)
                    
                    # Determine chunk type and content type
                    content_type = chunk.get("content_type", "text")
                    chunk_type = "text"
                    if content_type == "table":
                        chunk_type = "table"
                    elif content_type == "image":
                        chunk_type = "image"
                    elif content_type == "chart_diagram":
                        chunk_type = "diagram"
                    
                    # Store visual chunks in Supabase if enabled
                    if USE_SUPABASE:
                        try:
                            chunk_result = supabase.store_document_chunk(
                                document_id=document_id,
                                chunk_index=chunk["chunk_index"],
                                page_number=chunk["page_number"],
                                text=chunk["text"],
                                embedding=chunk.get("embedding", []),
                                metadata=chunk.get("metadata", {}),
                                source_type=chunk["source_type"],
                                chunk_type=chunk_type,
                                content_type=content_type
                            )
                            if "error" in chunk_result:
                                logger.error(f"Error storing visual chunk: {chunk_result['error']}")
                            else:
                                logger.info(f"Stored {content_type} chunk with ID: {chunk_result.get('id')}")
                        except Exception as e:
                            logger.error(f"Error storing visual chunk in Supabase: {str(e)}")
            
            # Combine text and visual chunks
            all_chunks = text_chunks + visual_chunks
            
            logger.info(f"Extracted {len(text_chunks)} text chunks and {len(visual_chunks)} visual chunks")
            return all_chunks
            
        except Exception as e:
            logger.error(f"Error extracting visual content: {str(e)}")
            # Return just text chunks if visual extraction fails
            return text_chunks
    
    # For non-PDF files or when visual extraction is disabled, return just text chunks
    return text_chunks

def create_visual_content_chunks(visual_content: Dict[str, Any], storage_result: Dict[str, Any] = None) -> List[Dict[str, Any]]:
    """
    Convert extracted visual content into searchable chunks with storage URLs.
    """
    chunks = []
    filename = visual_content["filename"]
    
    # Create mappings from storage result for URLs
    image_urls = {}
    table_urls = {}
    if storage_result:
        for img in storage_result.get("images", []):
            key = f"page_{img['page']}_image_{img['image_index']}"
            image_urls[key] = img.get("public_url")
        
        for table in storage_result.get("tables", []):
            key = f"page_{table['page']}_table_{table['table_index']}"
            table_urls[key] = table.get("public_url")
    
    # Process tables
    for table in visual_content["tables"]:
        # Get storage URL if available
        table_key = f"page_{table['page']}_table_{table['table_index']}"
        table_url = table_urls.get(table_key)
        
        # Enhanced metadata for tables
        metadata = {
            "visual_content_type": "table",
            "table_data": table["data"],
            "markdown_table": table["markdown_representation"],
            "page": table["page"],
            "table_index": table["table_index"],
            "extraction_method": table["extraction_method"]
        }
        
        if table_url:
            metadata["storage_url"] = table_url
            metadata["display_type"] = "html_table"
        
        chunk = {
            "filename": filename,
            "page": table["page"],
            "chunk_id": f"{os.path.splitext(filename)[0]}_table_{table['page']}_{table['table_index']}",
            "text": f"Table from page {table['page']} of {filename}:\n\n{table['text_representation']}",
            "source_type": "document",
            "content_type": "table",
            "extraction_method": table["extraction_method"],
            "metadata": metadata
        }
        chunks.append(chunk)
    
    # Process images
    for image in visual_content["images"]:
        # Get storage URL if available
        image_key = f"page_{image['page']}_image_{image['image_index']}"
        image_url = image_urls.get(image_key)
        
        # Try to find text content around this image to understand context
        page_context = ""
        project_context = ""
        ocr_text = ""
        detected_companies = []
        
        # Look for text on the same page that might indicate project number
        try:
            import fitz  # PyMuPDF
            doc = fitz.open(visual_content.get("file_path", ""))
            if image["page"] - 1 < len(doc):  # Page numbers are 0-indexed in PyMuPDF
                page = doc[image["page"] - 1]
                page_text = page.get_text()
                page_context = page_text[:500]  # First 500 chars of page text
                
                # Look for project references in the page text
                import re
                project_matches = re.findall(r'project\s*(\d+)', page_text.lower())
                if project_matches:
                    project_context = f"project {project_matches[0]}"
                    logger.info(f"Found project context for image on page {image['page']}: {project_context}")
                
                # Also look for other relevant context
                if "quotation" in page_text.lower():
                    quote_matches = re.findall(r'quotation\s*(\d+)', page_text.lower())
                    if quote_matches:
                        project_context += f" quotation {quote_matches[0]}"
                
                # Extract company names from page context for logo identification
                company_patterns = [
                    r'([A-Z][A-Za-z\s&]+?)\s+(?:Enterprises|Company|Corp|Ltd|Inc|Limited)',
                    r'([A-Z][A-Za-z\s&]{2,})\s+(?:logo|Logo|LOGO)',
                    r'(?:Company|COMPANY):\s*([A-Z][A-Za-z\s&]+)',
                    r'([A-Z][A-Z\s]{3,})',  # All caps company names
                ]
                
                for pattern in company_patterns:
                    matches = re.findall(pattern, page_text)
                    for match in matches:
                        company_name = match.strip()
                        if len(company_name) > 2 and company_name not in detected_companies:
                            detected_companies.append(company_name)
                            logger.info(f"Detected company name: {company_name} on page {image['page']}")
            
            doc.close()
        except Exception as e:
            logger.warning(f"Could not extract page context for image: {str(e)}")
        
        # Try OCR on the image itself to extract embedded text (for logos with text)
        try:
            if "base64_data" in image:
                import base64
                import io
                from PIL import Image
                import pytesseract
                
                # Decode base64 image data
                image_data = base64.b64decode(image["base64_data"])
                pil_image = Image.open(io.BytesIO(image_data))
                
                # Perform OCR to extract text from the image
                ocr_text = pytesseract.image_to_string(pil_image, config='--psm 6').strip()
                
                if ocr_text:
                    logger.info(f"OCR extracted text from image on page {image['page']}: {ocr_text[:100]}...")
                    
                    # Look for company names in OCR text
                    ocr_company_patterns = [
                        r'([A-Z][A-Za-z\s&]+?)\s*(?:Enterprises|Company|Corp|Ltd|Inc|Limited)',
                        r'([A-Z][A-Z\s]{3,})',  # All caps text (likely company names)
                        r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',  # Title case company names
                    ]
                    
                    for pattern in ocr_company_patterns:
                        matches = re.findall(pattern, ocr_text)
                        for match in matches:
                            company_name = match.strip()
                            if len(company_name) > 2 and company_name not in detected_companies:
                                detected_companies.append(company_name)
                                logger.info(f"OCR detected company name: {company_name} in image on page {image['page']}")
                else:
                    logger.info(f"No OCR text found in image on page {image['page']}")
                    
        except Exception as e:
            logger.warning(f"OCR extraction failed for image on page {image['page']}: {str(e)}")
        
        # Create enhanced descriptive text for the image with all context
        image_description = f"Image from page {image['page']} of {filename}: {image['width']}x{image['height']} pixels"
        
        # Add company information to description for better search matching
        if detected_companies:
            company_list = ", ".join(detected_companies)
            image_description += f"\nCompanies detected: {company_list}"
            
        if project_context:
            image_description += f"\nContext: {project_context}"
            
        if ocr_text:
            image_description += f"\nText in image: {ocr_text}"
            
        if page_context:
            image_description += f"\nPage content: {page_context[:200]}..."
        
        # Enhanced search text for better matching
        searchable_text = image_description
        if detected_companies:
            # Add variations of company names for better search matching
            for company in detected_companies:
                searchable_text += f"\n{company} logo {company} emblem {company} symbol"
                # Add common variations
                if "enterprises" in company.lower():
                    base_name = company.replace("Enterprises", "").replace("enterprises", "").strip()
                    searchable_text += f"\n{base_name} {base_name} logo"
        
        # Enhanced metadata for images
        metadata = {
            "visual_content_type": "image",
            "width": image["width"],
            "height": image["height"],
            "format": image["format"],
            "page": image["page"],
            "image_index": image["image_index"],
            "extraction_method": image["extraction_method"],
            "page_context": page_context,
            "project_context": project_context,
            "ocr_text": ocr_text,
            "detected_companies": detected_companies,
            "is_logo": len(detected_companies) > 0 or "logo" in page_context.lower()
        }
        
        if image_url:
            metadata["storage_url"] = image_url
            metadata["display_type"] = "image"
        elif "base64_data" in image:
            metadata["base64_data"] = image["base64_data"]
            metadata["display_type"] = "base64_image"
        
        chunk = {
            "filename": filename,
            "page": image["page"],
            "chunk_id": f"{os.path.splitext(filename)[0]}_image_{image['page']}_{image['image_index']}",
            "text": searchable_text,  # Use enhanced searchable text instead of simple description
            "source_type": "document",
            "content_type": "image",
            "extraction_method": image["extraction_method"],
            "metadata": metadata
        }
        chunks.append(chunk)
    
    # Process charts and diagrams
    for chart in visual_content["charts_diagrams"]:
        # Enhanced metadata for charts/diagrams
        metadata = {
            "visual_content_type": "chart_diagram",
            "drawing_count": chart["drawing_count"],
            "confidence": chart["confidence"],
            "description": chart["description"],
            "page": chart["page"],
            "extraction_method": chart["extraction_method"],
            "display_type": "text_description"
        }
        
        chunk = {
            "filename": filename,
            "page": chart["page"],
            "chunk_id": f"{os.path.splitext(filename)[0]}_chart_{chart['page']}",
            "text": f"Chart/Diagram from page {chart['page']} of {filename}: {chart['description']}",
            "source_type": "document",
            "content_type": "chart_diagram",
            "extraction_method": chart["extraction_method"],
            "metadata": metadata
        }
        chunks.append(chunk)
    
    return chunks 