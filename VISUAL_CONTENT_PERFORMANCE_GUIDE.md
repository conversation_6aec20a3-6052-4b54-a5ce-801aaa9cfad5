# Visual Content Performance Optimization Guide

## 🎯 Overview

This guide provides strategies for optimizing visual content extraction and display performance, especially for large documents and high-volume usage scenarios.

## 📊 Performance Metrics & Benchmarks

### Current Performance Baselines
- **Small Documents** (< 5MB): 2-5 seconds processing
- **Medium Documents** (5-20MB): 5-15 seconds processing  
- **Large Documents** (20-50MB): 15-45 seconds processing
- **Very Large Documents** (> 50MB): 45+ seconds processing

### Visual Content Processing Times
- **Table Extraction**: ~0.5-2 seconds per page
- **Image Extraction**: ~1-3 seconds per image
- **Chart Detection**: ~0.5-1 second per page
- **Storage Upload**: ~0.1-0.5 seconds per MB

## ⚡ Backend Optimization Strategies

### 1. Document Processing Optimization

#### Parallel Processing
```python
# Enable concurrent processing for multiple pages
import concurrent.futures
from functools import partial

def process_pages_parallel(pdf_path, max_workers=4):
    """Process PDF pages in parallel for faster extraction"""
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Process pages concurrently
        futures = []
        for page_num in range(total_pages):
            future = executor.submit(extract_page_content, pdf_path, page_num)
            futures.append(future)
        
        # Collect results
        results = []
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())
    
    return results
```

#### Memory Management
```python
# Optimize memory usage for large documents
def extract_with_memory_management(file_path, chunk_size=10):
    """Process documents in chunks to manage memory"""
    total_pages = get_page_count(file_path)
    
    for start_page in range(0, total_pages, chunk_size):
        end_page = min(start_page + chunk_size, total_pages)
        
        # Process chunk
        chunk_results = process_page_range(file_path, start_page, end_page)
        
        # Store results immediately
        store_chunk_results(chunk_results)
        
        # Clear memory
        del chunk_results
        gc.collect()
```

#### Selective Extraction
```python
# Only extract requested content types
def extract_selective_content(file_path, extract_tables=True, 
                            extract_images=True, extract_charts=True):
    """Extract only requested content types to save processing time"""
    results = {"tables": [], "images": [], "charts": []}
    
    if extract_tables:
        results["tables"] = extract_tables_optimized(file_path)
    
    if extract_images:
        results["images"] = extract_images_optimized(file_path)
    
    if extract_charts:
        results["charts"] = detect_charts_optimized(file_path)
    
    return results
```

### 2. Database Optimization

#### Batch Insertions
```python
# Insert multiple chunks in batches
def store_chunks_batch(chunks, batch_size=100):
    """Store chunks in batches for better performance"""
    for i in range(0, len(chunks), batch_size):
        batch = chunks[i:i + batch_size]
        supabase_client.table('chunks').insert(batch).execute()
```

#### Indexing Strategy
```sql
-- Optimize database queries with proper indexes
CREATE INDEX idx_chunks_content_type ON chunks(chunk_type);
CREATE INDEX idx_chunks_document_id ON chunks(document_id);
CREATE INDEX idx_chunks_page_number ON chunks(page_number);
CREATE INDEX idx_chunks_embedding_search ON chunks USING ivfflat (embedding vector_cosine_ops);
```

#### Connection Pooling
```python
# Use connection pooling for better database performance
from supabase import create_client
import asyncio

class OptimizedSupabaseClient:
    def __init__(self, url, key, pool_size=10):
        self.pool = asyncio.Queue(maxsize=pool_size)
        for _ in range(pool_size):
            client = create_client(url, key)
            self.pool.put_nowait(client)
    
    async def execute_query(self, query_func):
        client = await self.pool.get()
        try:
            result = await query_func(client)
            return result
        finally:
            await self.pool.put(client)
```

### 3. Storage Optimization

#### Image Compression
```python
# Compress images before storage
from PIL import Image
import io

def compress_image(image_data, quality=85, max_size=(1920, 1080)):
    """Compress images to reduce storage and transfer time"""
    img = Image.open(io.BytesIO(image_data))
    
    # Resize if too large
    img.thumbnail(max_size, Image.Resampling.LANCZOS)
    
    # Compress
    output = io.BytesIO()
    img.save(output, format='JPEG', quality=quality, optimize=True)
    
    return output.getvalue()
```

#### CDN Integration
```python
# Use CDN for faster image delivery
def upload_to_cdn(image_data, filename):
    """Upload images to CDN for faster access"""
    # Upload to Supabase Storage with CDN
    result = supabase.storage.from_('doc-images').upload(
        filename, 
        image_data,
        file_options={
            'cache-control': '3600',
            'content-type': 'image/jpeg'
        }
    )
    
    # Return CDN URL
    return supabase.storage.from_('doc-images').get_public_url(filename)
```

## 🌐 Frontend Optimization Strategies

### 1. Lazy Loading
```typescript
// Implement lazy loading for visual content
const VisualContent: React.FC<VisualContentProps> = ({ source }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div ref={ref}>
      {isVisible && <ActualVisualContent source={source} />}
    </div>
  );
};
```

### 2. Image Optimization
```typescript
// Progressive image loading with placeholders
const OptimizedImage: React.FC<ImageProps> = ({ src, alt }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageSrc, setImageSrc] = useState('');

  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setImageSrc(src);
      setImageLoaded(true);
    };
    img.src = src;
  }, [src]);

  return (
    <div className="relative">
      {!imageLoaded && (
        <div className="animate-pulse bg-gray-200 rounded h-48 flex items-center justify-center">
          <span className="text-gray-500">Loading image...</span>
        </div>
      )}
      {imageLoaded && (
        <img
          src={imageSrc}
          alt={alt}
          className="max-w-full h-auto rounded transition-opacity duration-300"
          loading="lazy"
        />
      )}
    </div>
  );
};
```

### 3. Virtual Scrolling for Large Tables
```typescript
// Virtual scrolling for large tables
import { FixedSizeList as List } from 'react-window';

const VirtualTable: React.FC<TableProps> = ({ data, headers }) => {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style} className="flex border-b">
      {data[index].map((cell: string, cellIndex: number) => (
        <div key={cellIndex} className="flex-1 p-2 border-r">
          {cell}
        </div>
      ))}
    </div>
  );

  return (
    <div className="border rounded">
      {/* Header */}
      <div className="flex bg-gray-50 font-semibold">
        {headers.map((header: string, index: number) => (
          <div key={index} className="flex-1 p-2 border-r">
            {header}
          </div>
        ))}
      </div>
      
      {/* Virtual scrolling body */}
      <List
        height={400}
        itemCount={data.length}
        itemSize={40}
        width="100%"
      >
        {Row}
      </List>
    </div>
  );
};
```

## 🔧 System-Level Optimizations

### 1. Caching Strategy

#### Redis Caching
```python
# Cache extraction results
import redis
import json
import hashlib

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def get_cached_extraction(file_hash):
    """Get cached extraction results"""
    cache_key = f"extraction:{file_hash}"
    cached_result = redis_client.get(cache_key)
    
    if cached_result:
        return json.loads(cached_result)
    return None

def cache_extraction_result(file_hash, result, ttl=3600):
    """Cache extraction results for 1 hour"""
    cache_key = f"extraction:{file_hash}"
    redis_client.setex(cache_key, ttl, json.dumps(result))
```

#### Browser Caching
```typescript
// Implement service worker for caching
const CACHE_NAME = 'railgpt-visual-content-v1';
const urlsToCache = [
  '/static/js/bundle.js',
  '/static/css/main.css',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  if (event.request.url.includes('/api/visual-content/')) {
    event.respondWith(
      caches.match(event.request)
        .then((response) => {
          // Return cached version or fetch from network
          return response || fetch(event.request);
        })
    );
  }
});
```

### 2. Load Balancing

#### Nginx Configuration
```nginx
# Load balance visual content processing
upstream visual_processors {
    server 127.0.0.1:8000 weight=3;
    server 127.0.0.1:8001 weight=2;
    server 127.0.0.1:8002 weight=1;
}

server {
    listen 80;
    
    location /api/upload-document {
        proxy_pass http://visual_processors;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # Increase timeouts for large documents
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
        
        # Increase body size limit
        client_max_body_size 100M;
    }
    
    location /api/visual-content/ {
        proxy_pass http://visual_processors;
        
        # Cache visual content responses
        proxy_cache visual_cache;
        proxy_cache_valid 200 1h;
        proxy_cache_key "$scheme$request_method$host$request_uri";
    }
}
```

### 3. Resource Monitoring

#### Performance Monitoring
```python
# Monitor performance metrics
import time
import psutil
import logging
from functools import wraps

def monitor_performance(func):
    """Decorator to monitor function performance"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        try:
            result = func(*args, **kwargs)
            
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            execution_time = end_time - start_time
            memory_used = end_memory - start_memory
            
            logging.info(f"{func.__name__} - Time: {execution_time:.2f}s, Memory: {memory_used:.2f}MB")
            
            return result
            
        except Exception as e:
            logging.error(f"{func.__name__} failed: {str(e)}")
            raise
    
    return wrapper

@monitor_performance
def extract_visual_content_monitored(file_path):
    """Monitored version of visual content extraction"""
    return extract_visual_content_from_file(file_path)
```

## 📈 Performance Testing & Benchmarking

### Load Testing Script
```python
# Load testing for visual content processing
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

async def upload_test_document(session, file_path, test_id):
    """Upload a test document and measure performance"""
    start_time = time.time()
    
    with open(file_path, 'rb') as f:
        data = aiohttp.FormData()
        data.add_field('file', f, filename=f'test_{test_id}.pdf')
        data.add_field('extract_tables', 'true')
        data.add_field('extract_images', 'true')
        data.add_field('extract_charts', 'true')
        
        async with session.post('http://localhost:8000/api/upload-document', data=data) as response:
            result = await response.json()
            end_time = time.time()
            
            return {
                'test_id': test_id,
                'status': response.status,
                'processing_time': end_time - start_time,
                'chunks_extracted': result.get('chunks_extracted', 0)
            }

async def run_load_test(file_path, concurrent_uploads=5):
    """Run load test with multiple concurrent uploads"""
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(concurrent_uploads):
            task = upload_test_document(session, file_path, i)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # Analyze results
        total_time = max(r['processing_time'] for r in results)
        avg_time = sum(r['processing_time'] for r in results) / len(results)
        success_rate = sum(1 for r in results if r['status'] == 200) / len(results)
        
        print(f"Load Test Results:")
        print(f"  Concurrent uploads: {concurrent_uploads}")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Average time: {avg_time:.2f}s")
        print(f"  Success rate: {success_rate:.2%}")

# Run the test
if __name__ == "__main__":
    asyncio.run(run_load_test('test_table.pdf', 10))
```

## 🎯 Optimization Checklist

### Backend Optimizations
- [ ] Enable parallel processing for multi-page documents
- [ ] Implement memory management for large files
- [ ] Add selective content extraction
- [ ] Use batch database insertions
- [ ] Implement connection pooling
- [ ] Add Redis caching for extraction results
- [ ] Compress images before storage
- [ ] Monitor performance metrics

### Frontend Optimizations
- [ ] Implement lazy loading for visual content
- [ ] Add progressive image loading
- [ ] Use virtual scrolling for large tables
- [ ] Enable browser caching with service workers
- [ ] Optimize bundle size and loading
- [ ] Add loading states and error handling
- [ ] Implement responsive design for mobile

### Infrastructure Optimizations
- [ ] Set up load balancing for processing servers
- [ ] Configure CDN for image delivery
- [ ] Implement database query optimization
- [ ] Add monitoring and alerting
- [ ] Set up auto-scaling for high load
- [ ] Configure proper caching headers
- [ ] Optimize network and storage performance

## 📊 Expected Performance Improvements

### After Optimization
- **Small Documents**: 1-3 seconds (50% improvement)
- **Medium Documents**: 3-8 seconds (60% improvement)
- **Large Documents**: 8-20 seconds (65% improvement)
- **Very Large Documents**: 20-35 seconds (70% improvement)

### Scalability Targets
- **Concurrent Users**: 50+ simultaneous uploads
- **Daily Volume**: 1000+ documents processed
- **Storage Efficiency**: 40% reduction in storage usage
- **Query Response**: <2 seconds for visual content queries

---

**Implement these optimizations gradually and monitor performance improvements at each step!** 🚀 