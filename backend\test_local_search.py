#!/usr/bin/env python3
"""
Test script to directly test local search functions.
"""

import requests
import json
import time

# Configuration
API_URL = "http://localhost:8000"

def test_local_search():
    """Test local search functionality directly."""
    print("🔬 Testing Local Search Functions")
    print("="*50)
    
    # First check how many chunks are loaded
    try:
        chunks_response = requests.get(f"{API_URL}/api/chunks", timeout=10)
        if chunks_response.status_code == 200:
            chunks = chunks_response.json()
            doc_count = sum(1 for chunk in chunks if chunk.get('source_type') == 'document')
            web_count = sum(1 for chunk in chunks if chunk.get('source_type') == 'website')
            print(f"📄 Document chunks in memory: {doc_count}")
            print(f"🌐 Website chunks in memory: {web_count}")
            print(f"📊 Total chunks: {len(chunks)}")
            
            # Show sample chunk structure
            if chunks:
                sample = chunks[0]
                print(f"📋 Sample chunk keys: {list(sample.keys())}")
                print(f"📋 Sample source_type: {sample.get('source_type', 'NOT SET')}")
                print(f"📋 Sample has embedding: {'embedding' in sample}")
                if 'text' in sample:
                    print(f"📋 Sample text preview: {sample['text'][:100]}...")
        else:
            print(f"❌ Failed to get chunks: {chunks_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting chunks: {str(e)}")
    
    print("\n" + "="*50)
    
    # Test a simple query
    test_query = "FSDS monitoring system"
    print(f"🔍 Testing query: '{test_query}'")
    
    try:
        query_data = {
            "query": test_query,
            "model": "gemini-2.0-flash",
            "fallback_enabled": True
        }
        
        response = requests.post(
            f"{API_URL}/api/query",
            json=query_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Query successful")
            print(f"📄 Document answer: {result.get('document_answer') is not None}")
            print(f"🌐 Website answer: {result.get('website_answer') is not None}")
            print(f"🧠 LLM fallback used: {result.get('llm_fallback', False)}")
            print(f"📊 Total sources: {len(result.get('sources', []))}")
            print(f"📄 Document sources: {len(result.get('document_sources', []))}")
            print(f"🌐 Website sources: {len(result.get('website_sources', []))}")
            
            if result.get('answer'):
                print(f"💬 Answer preview: {result['answer'][:200]}...")
        else:
            print(f"❌ Query failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Error with query: {str(e)}")

if __name__ == "__main__":
    test_local_search() 