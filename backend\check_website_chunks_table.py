"""
<PERSON><PERSON><PERSON> to check and fix the website_chunks table structure.

This script:
1. Checks if the website_chunks table has the required columns
2. Adds missing columns if needed
3. Creates necessary indexes for better performance
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Import Supabase client
try:
    from supabase_client import supabase
except ImportError as e:
    logger.error(f"Error importing Supabase client: {str(e)}")
    sys.exit(1)

def check_table_exists():
    """Check if the website_chunks table exists."""
    try:
        query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'website_chunks'
        );
        """
        
        result = supabase.execute_query(query)
        
        if result and result[0].get('exists'):
            logger.info("website_chunks table exists")
            return True
        else:
            logger.error("website_chunks table does not exist")
            return False
    except Exception as e:
        logger.error(f"Error checking if table exists: {str(e)}")
        return False

def check_columns():
    """Check if the website_chunks table has all required columns."""
    try:
        query = """
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'website_chunks';
        """
        
        result = supabase.execute_query(query)
        
        if not result:
            logger.error("Failed to get column information")
            return False
            
        # Convert to a dictionary for easier lookup
        columns = {row.get('column_name'): row.get('data_type') for row in result}
        
        # Check required columns
        required_columns = {
            'id': 'uuid',
            'website_id': 'uuid',
            'chunk_index': 'integer',
            'text': 'text',
            'embedding': 'USER-DEFINED',  # vector type
            'metadata': 'jsonb',
            'created_at': 'timestamp with time zone',
            'source_type': 'text'
        }
        
        missing_columns = []
        for col, data_type in required_columns.items():
            if col not in columns:
                missing_columns.append((col, data_type))
            elif col == 'embedding' and columns[col] != 'USER-DEFINED':
                logger.warning(f"Column {col} has incorrect data type: {columns[col]}, should be vector")
                
        if missing_columns:
            logger.warning(f"Missing columns in website_chunks table: {missing_columns}")
            return missing_columns
        else:
            logger.info("All required columns exist in website_chunks table")
            return []
    except Exception as e:
        logger.error(f"Error checking columns: {str(e)}")
        return []

def add_missing_columns(missing_columns):
    """Add missing columns to the website_chunks table."""
    try:
        for col, data_type in missing_columns:
            # Special handling for vector type
            if col == 'embedding' and data_type == 'USER-DEFINED':
                query = f"""
                ALTER TABLE website_chunks 
                ADD COLUMN IF NOT EXISTS {col} vector(768);
                """
            elif col == 'metadata' and data_type == 'jsonb':
                query = f"""
                ALTER TABLE website_chunks 
                ADD COLUMN IF NOT EXISTS {col} jsonb DEFAULT '{{}}'::jsonb;
                """
            elif col == 'created_at' and data_type == 'timestamp with time zone':
                query = f"""
                ALTER TABLE website_chunks 
                ADD COLUMN IF NOT EXISTS {col} timestamp with time zone DEFAULT CURRENT_TIMESTAMP;
                """
            elif col == 'source_type' and data_type == 'text':
                query = f"""
                ALTER TABLE website_chunks 
                ADD COLUMN IF NOT EXISTS {col} text DEFAULT 'website';
                """
            else:
                query = f"""
                ALTER TABLE website_chunks 
                ADD COLUMN IF NOT EXISTS {col} {data_type};
                """
                
            result = supabase.execute_sql(query)
            
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error adding column {col}: {result['error']}")
            else:
                logger.info(f"Added column {col} to website_chunks table")
    except Exception as e:
        logger.error(f"Error adding missing columns: {str(e)}")

def create_indexes():
    """Create necessary indexes for better performance."""
    try:
        # Check existing indexes
        query = """
        SELECT indexname 
        FROM pg_indexes 
        WHERE tablename = 'website_chunks';
        """
        
        result = supabase.execute_query(query)
        
        if not result:
            logger.warning("Failed to get index information")
            existing_indexes = []
        else:
            existing_indexes = [row.get('indexname') for row in result]
            
        # Define indexes to create
        indexes = [
            ("idx_website_chunks_website_id", "CREATE INDEX IF NOT EXISTS idx_website_chunks_website_id ON website_chunks(website_id);"),
            ("idx_website_chunks_text_gin", "CREATE INDEX IF NOT EXISTS idx_website_chunks_text_gin ON website_chunks USING GIN (to_tsvector('english', text));"),
            ("idx_website_chunks_embedding", "CREATE INDEX IF NOT EXISTS idx_website_chunks_embedding ON website_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);")
        ]
        
        # Create missing indexes
        for idx_name, idx_query in indexes:
            if idx_name not in existing_indexes:
                logger.info(f"Creating index {idx_name}...")
                result = supabase.execute_sql(idx_query)
                
                if isinstance(result, dict) and "error" in result:
                    logger.error(f"Error creating index {idx_name}: {result['error']}")
                else:
                    logger.info(f"Created index {idx_name}")
            else:
                logger.info(f"Index {idx_name} already exists")
    except Exception as e:
        logger.error(f"Error creating indexes: {str(e)}")

def main():
    """Main function to check and fix the website_chunks table."""
    logger.info("Checking website_chunks table...")
    
    # Check if table exists
    if not check_table_exists():
        logger.error("website_chunks table does not exist. Please run the database setup script first.")
        return
        
    # Check columns
    missing_columns = check_columns()
    
    # Add missing columns
    if missing_columns:
        logger.info("Adding missing columns...")
        add_missing_columns(missing_columns)
    
    # Create indexes
    logger.info("Creating indexes...")
    create_indexes()
    
    logger.info("Table check and fix completed")

if __name__ == "__main__":
    main()
