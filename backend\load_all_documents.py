'''
Load all documents from Supabase into the vector database.
Run this script before starting the server to ensure all documents are available.
'''
import logging
import json
from supabase_client import supabase
import vector_db

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_all_documents():
    '''Load all documents from Supabase into the vector database.'''
    try:
        # Initialize vector database
        if not vector_db.vector_db.is_initialized():
            vector_db.vector_db.initialize()
        
        # Get all documents from Supabase
        query = '''
        SELECT 
            d.id, 
            d.display_name, 
            d.file_path, 
            d.file_type,
            COUNT(dc.id) as chunk_count
        FROM 
            documents d
        LEFT JOIN 
            document_chunks dc ON d.id = dc.document_id
        GROUP BY 
            d.id, d.display_name, d.file_path, d.file_type
        ORDER BY 
            d.created_at DESC
        '''
        
        documents = supabase.execute_query(query)
        
        if isinstance(documents, dict) and "error" in documents:
            logger.error(f"Error retrieving documents: {documents['error']}")
            return False
        
        logger.info(f"Found {len(documents)} documents in Supabase")
        
        # Get all document chunks
        chunks_query = '''
        SELECT 
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.metadata,
            d.display_name as filename,
            d.file_path as file_path,
            d.file_type
        FROM 
            document_chunks dc
        JOIN 
            documents d ON dc.document_id = d.id
        ORDER BY 
            d.created_at DESC, dc.chunk_index ASC
        '''
        
        chunks = supabase.execute_query(chunks_query)
        
        if isinstance(chunks, dict) and "error" in chunks:
            logger.error(f"Error retrieving document chunks: {chunks['error']}")
            return False
        
        logger.info(f"Found {len(chunks)} document chunks in Supabase")
        
        # Store chunks in vector database
        stored_count = 0
        for chunk in chunks:
            # Add source_type to identify as document
            chunk["source_type"] = "document"
            
            # Ensure the chunk has an embedding
            if not chunk.get("embedding") or chunk["embedding"] == "[]":
                # Create a default embedding if one doesn't exist
                # This will be replaced with a real embedding when the server uses it
                logger.warning(f"Chunk {chunk.get('id')} has no embedding, creating a default one")
                chunk["embedding"] = [0.01] * 512  # Create a default embedding
            
            # Store in vector database
            try:
                # Add to in-memory document chunks list
                from server import DOCUMENT_CHUNKS
                chunk_exists = any(c.get('id') == chunk.get('id') for c in DOCUMENT_CHUNKS)
                if not chunk_exists:
                    DOCUMENT_CHUNKS.append(chunk)
                    stored_count += 1
            except Exception as e:
                logger.warning(f"Error adding chunk to in-memory storage: {str(e)}")
                # Can continue even if this fails
        
        logger.info(f"Loaded {stored_count} document chunks into memory")
        return True
        
    except Exception as e:
        logger.error(f"Error loading documents: {str(e)}")
        return False

if __name__ == "__main__":
    print("Loading all documents from Supabase...")
    if load_all_documents():
        print("✅ Successfully loaded all documents")
    else:
        print("❌ Failed to load documents")
