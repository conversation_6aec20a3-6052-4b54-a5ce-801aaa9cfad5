import sys
sys.path.append('backend')
from visual_extractor import extract_visual_content_from_file
import fitz
import os

# Create a simple test PDF with table content
doc = fitz.open()
page = doc.new_page()
page.insert_text((50, 50), 'Test Document with Table', fontsize=16)

table_text = '''
Railway Statistics 2023

Route          Distance    Trains/Day    Passengers
Delhi-Mumbai   1384 km     25           50000
Mumbai-Chennai 1279 km     18           35000
Delhi-Kolkata  1472 km     22           42000
Chennai-Bangalore 362 km   30           28000
'''

page.insert_text((50, 100), table_text, fontsize=12)
test_pdf_path = 'test_table.pdf'
doc.save(test_pdf_path)
doc.close()

print(f'Created test PDF: {test_pdf_path}')

# Test visual extraction
try:
    visual_content = extract_visual_content_from_file(
        test_pdf_path,
        extract_tables=True,
        extract_images=True,
        extract_charts=True
    )
    
    print('Visual content extraction results:')
    print(f'Tables: {len(visual_content.get("tables", []))}')
    print(f'Images: {len(visual_content.get("images", []))}')
    print(f'Charts: {len(visual_content.get("charts_diagrams", []))}')
    
    # Check if pdfplumber can detect tables
    import pdfplumber
    with pdfplumber.open(test_pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages):
            tables = page.extract_tables()
            print(f'pdfplumber found {len(tables) if tables else 0} tables on page {page_num + 1}')
            
    # Check if PyMuPDF can detect tables
    with fitz.open(test_pdf_path) as pdf:
        for page_num, page in enumerate(pdf):
            try:
                tables = page.find_tables()
                print(f'PyMuPDF found {len(tables) if tables else 0} tables on page {page_num + 1}')
            except AttributeError:
                print('PyMuPDF find_tables method not available')
                break
    
    # Clean up
    if os.path.exists(test_pdf_path):
        os.remove(test_pdf_path)
        
except Exception as e:
    print(f'Error: {e}')
    import traceback
    traceback.print_exc() 