"""
Vector database implementation for efficient document and website search.
Supports both local FAISS-based search and Supabase pgvector search.
"""
import os
import json
import logging
import numpy as np
import faiss
from typing import List, Dict, Any
from dotenv import load_dotenv

# Import Supabase client
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Check if we should use Supabase for vector storage
USE_SUPABASE = os.getenv("USE_SUPABASE", "true").lower() == "true"

class VectorDB:
    """Vector database for efficient semantic search with Supabase pgvector support."""

    def __init__(self, dimension=768, index_file="data/faiss_index.bin", metadata_file="data/metadata.json"):
        """
        Initialize vector database.

        Args:
            dimension: Dimension of the embedding vectors
            index_file: Path to save/load the FAISS index (for local mode)
            metadata_file: Path to save/load the chunk metadata (for local mode)
        """
        self.dimension = dimension
        self.index_file = index_file
        self.metadata_file = metadata_file
        self.metadata = []  # Store chunk metadata (everything except embeddings)
        self.use_supabase = USE_SUPABASE

        if not self.use_supabase:
            # Local FAISS mode
            # Create a flat index for exact search
            self.index = faiss.IndexFlatIP(dimension)  # Inner product = cosine similarity for normalized vectors

            # Create directories if they don't exist
            os.makedirs(os.path.dirname(index_file), exist_ok=True)

            # Load existing index and metadata if they exist
            self._load_if_exists()

            logger.info("Using local FAISS vector database")
        else:
            # Supabase pgvector mode
            logger.info("Using Supabase pgvector for vector storage")

    def _load_if_exists(self):
        """Load index and metadata from disk if they exist (local mode only)."""
        if self.use_supabase:
            return

        try:
            if os.path.exists(self.index_file):
                logger.info(f"Loading vector index from {self.index_file}")
                self.index = faiss.read_index(self.index_file)

            if os.path.exists(self.metadata_file):
                logger.info(f"Loading metadata from {self.metadata_file}")
                with open(self.metadata_file, 'r') as f:
                    self.metadata = json.load(f)

            logger.info(f"Loaded {len(self.metadata)} chunks into memory")
        except Exception as e:
            logger.error(f"Error loading index or metadata: {str(e)}")
            # If loading fails, we'll start with empty index and metadata
            self.metadata = []
            self.index = faiss.IndexFlatIP(self.dimension)

    def save(self):
        """Save index and metadata to disk (local mode only)."""
        if self.use_supabase:
            return

        try:
            logger.info(f"Saving vector index to {self.index_file}")
            faiss.write_index(self.index, self.index_file)

            logger.info(f"Saving metadata to {self.metadata_file}")
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f)

            logger.info(f"Saved {len(self.metadata)} chunks")
        except Exception as e:
            logger.error(f"Error saving index or metadata: {str(e)}")

    def add_chunks(self, chunks: List[Dict[str, Any]], source_type: str = "document") -> None:
        """
        Add chunks to the vector database.

        Args:
            chunks: List of chunks to add
            source_type: Type of source ("document" or "website")
        """
        if not chunks:
            return

        if self.use_supabase:
            try:
                # Process each chunk
                for chunk in chunks:
                    # Ensure chunk has an embedding
                    if "embedding" not in chunk:
                        logger.warning(f"Chunk missing embedding, skipping: {chunk.get('text', '')[:100]}...")
                        continue

                    # Get document/website ID
                    doc_id = chunk.get("document_id") if source_type == "document" else chunk.get("website_id")
                    if not doc_id:
                        logger.warning(f"Chunk missing {source_type}_id, skipping: {chunk.get('text', '')[:100]}...")
                        continue

                    # Store in Supabase
                    if source_type == "document":
                        # Store document metadata if not exists
                        doc_result = supabase.store_document(
                            filename=chunk.get("filename", ""),
                            display_name=chunk.get("display_name", ""),
                            file_path=chunk.get("file_path", ""),
                            file_type=chunk.get("file_type", ""),
                            file_size=chunk.get("file_size", 0),
                            uploaded_by=chunk.get("uploaded_by", "")
                        )
                        if "error" in doc_result:
                            logger.error(f"Error storing document metadata: {doc_result['error']}")
                            continue

                        # Store document chunk
                        chunk_result = supabase.store_document_chunk(
                            document_id=doc_id,
                            chunk_index=chunk.get("chunk_index", 0),
                            page_number=chunk.get("page_number", 1),
                            text=chunk["text"],
                            embedding=chunk["embedding"],
                            metadata=chunk.get("metadata", {})
                        )
                        if "error" in chunk_result:
                            logger.error(f"Error storing document chunk: {chunk_result['error']}")
                    else:
                        # Store website metadata if not exists
                        website_result = supabase.store_website(
                            url=chunk.get("url", ""),
                            title=chunk.get("title", ""),
                            description=chunk.get("description", ""),
                            domain=chunk.get("domain", "")
                        )
                        if "error" in website_result:
                            logger.error(f"Error storing website metadata: {website_result['error']}")
                            continue

                        # Store website chunk
                        chunk_result = supabase.store_website_chunk(
                            website_id=doc_id,
                            chunk_index=chunk.get("chunk_index", 0),
                            text=chunk["text"],
                            embedding=chunk["embedding"],
                            metadata=chunk.get("metadata", {})
                        )
                        if "error" in chunk_result:
                            logger.error(f"Error storing website chunk: {chunk_result['error']}")

            except Exception as e:
                logger.error(f"Error adding chunks to Supabase: {str(e)}")
        else:
            # Local FAISS mode
            try:
                # Add chunks to FAISS index
                texts = [chunk["text"] for chunk in chunks]
                embeddings = [chunk["embedding"] for chunk in chunks]
                self.index.add(np.array(embeddings))

                # Store chunks in memory
                for chunk in chunks:
                    if source_type == "document":
                        self.document_chunks.append(chunk)
                    else:
                        self.website_chunks.append(chunk)

            except Exception as e:
                logger.error(f"Error adding chunks to FAISS: {str(e)}")

    def search(self, query_embedding: List[float], top_k: int = 50, threshold: float = 0.18) -> List[Dict[str, Any]]:
        """
        Search for chunks similar to the query embedding.

        Args:
            query_embedding: Query embedding vector
            top_k: Number of most similar chunks to return
            threshold: Minimum similarity threshold (default lowered to 0.3)

        Returns:
            List of metadata for most similar chunks
        """
        if self.use_supabase:
            # Supabase pgvector mode - search both documents and websites
            # First search documents with higher match count to prioritize them
            doc_results = supabase.search_documents(
                query_embedding=query_embedding,
                match_threshold=threshold,
                match_count=top_k * 3  # Get many more document results  # Get more document results to ensure good coverage
            )

            # Then search websites with standard match count
            web_results = supabase.search_websites(
                query_embedding=query_embedding,
                match_threshold=threshold,
                match_count=top_k
            )

            # Apply priority weights to adjust similarity scores
            # Import SOURCE_PRIORITY from server.py if available, otherwise use default weights
            try:
                from server import SOURCE_PRIORITY
            except ImportError:
                SOURCE_PRIORITY = {"document": 1.2, "website": 1.0}

            # Apply priority weights to document results
            for chunk in doc_results:
                if "similarity" in chunk:
                    # Apply document priority weight (capped at 1.0)
                    chunk["similarity"] = min(1.0, chunk["similarity"] * SOURCE_PRIORITY.get("document", 1.2))

            # Apply priority weights to website results
            for chunk in web_results:
                if "similarity" in chunk:
                    # Apply website priority weight (capped at 1.0)
                    chunk["similarity"] = min(1.0, chunk["similarity"] * SOURCE_PRIORITY.get("website", 1.0))

            # Combine results
            all_results = doc_results + web_results

            # Sort by similarity score (descending)
            all_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)

            # Limit to top_k results
            all_results = all_results[:top_k]

            logger.info(f"Found {len(doc_results)} document chunks and {len(web_results)} website chunks in Supabase search")
            return all_results

        else:
            # Local FAISS mode
            if self.index.ntotal == 0:
                logger.warning("Empty index, no results to return")
                return []

            # Convert to numpy array and normalize
            query_np = np.array(query_embedding).astype(np.float32).reshape(1, -1)
            faiss.normalize_L2(query_np)

            # Search the index
            similarities, indices = self.index.search(query_np, min(top_k, self.index.ntotal))

            # Filter results by threshold and get metadata
            results = []

            for idx, (similarity, index) in enumerate(zip(similarities[0], indices[0])):
                if similarity < threshold:
                    continue  # Skip results below threshold

                if index >= len(self.metadata) or index < 0:
                    logger.warning(f"Invalid index {index}, metadata length: {len(self.metadata)}")
                    continue

                # Get metadata and add similarity score
                metadata = self.metadata[index].copy()
                metadata["similarity"] = float(similarity)
                results.append(metadata)

            logger.info(f"Found {len(results)} chunks above threshold {threshold}")
            return results

    def hybrid_search(self, query_embedding: List[float], query_text: str = None, top_k: int = 50, threshold: float = 0.15) -> List[Dict[str, Any]]:
        
                # Ensure query_embedding is a numpy array of floats
                if not isinstance(query_embedding, np.ndarray):

        # Ensure query_embedding is a numpy array of floats
        if not isinstance(query_embedding, np.ndarray):
            try:
                if isinstance(query_embedding, str):
                    import json
                    query_embedding = json.loads(query_embedding)
                query_embedding = np.array(query_embedding, dtype=np.float32)
            except Exception as e:
                logger.error(f"Error converting query_embedding: {str(e)}")
                # Create a default embedding as fallback
                query_embedding = np.array([0.01] * 768, dtype=np.float32)
                    try:
                        if isinstance(query_embedding, str):
                            import json
                            query_embedding = json.loads(query_embedding)
                        query_embedding = np.array(query_embedding, dtype=np.float32)
                    except Exception as e:
                        logger.error(f"Error converting query_embedding: {str(e)}")
                        # Create a default embedding as fallback
                        query_embedding = np.array([0.01] * 768, dtype=np.float32)
                """
            Search using both semantic similarity and keyword matching.

            Args:
            query_embedding: Query embedding vector for semantic search
            query_text: The text query for keyword matching (optional)
            top_k: Number of most similar chunks to return
            threshold: Minimum similarity threshold (default lowered to 0.3)

            Returns:
            List of metadata for most similar chunks
            """
            if self.use_supabase:
            # Supabase pgvector mode with hybrid search - search both documents and websites

            # If query_text is not provided, extract it from the first few chunks
            if query_text is None:
                # Fall back to regular semantic search
                logger.info("No query_text provided for hybrid search, falling back to semantic search")
                return self.search(query_embedding, top_k, threshold)

            # First search documents with higher match count to prioritize them
            doc_results = supabase.hybrid_search_documents(
                query_text=query_text,
                query_embedding=query_embedding,
                match_threshold=threshold,
                match_count=top_k * 3  # Get many more document results  # Get more document results to ensure good coverage
            )

            # Then search websites with standard match count
            web_results = supabase.hybrid_search_websites(
                query_text=query_text,
                query_embedding=query_embedding,
                match_threshold=threshold,
                match_count=top_k
            )

            # Apply priority weights to adjust similarity scores
            # Import SOURCE_PRIORITY from server.py if available, otherwise use default weights
            try:
                from server import SOURCE_PRIORITY
            except ImportError:
                SOURCE_PRIORITY = {"document": 1.2, "website": 1.0}

            # Apply priority weights to document results
            for chunk in doc_results:
                if "similarity" in chunk:
                    # Apply document priority weight (capped at 1.0)
                    chunk["similarity"] = min(1.0, chunk["similarity"] * SOURCE_PRIORITY.get("document", 1.2))

            # Apply priority weights to website results
            for chunk in web_results:
                if "similarity" in chunk:
                    # Apply website priority weight (capped at 1.0)
                    chunk["similarity"] = min(1.0, chunk["similarity"] * SOURCE_PRIORITY.get("website", 1.0))

            # Combine results
            all_results = doc_results + web_results

            # Sort by similarity score (descending)
            all_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)

            # Limit to top_k results
            all_results = all_results[:top_k]

            logger.info(f"Found {len(doc_results)} document chunks and {len(web_results)} website chunks in Supabase hybrid search")
            return all_results
            else:
            # For local mode, just use regular search as hybrid search is not implemented
            return self.search(query_embedding, top_k, threshold)

    def clear(self):
        """Clear the index and metadata (for testing)."""
        if self.use_supabase:
            logger.warning("Clear operation not supported in Supabase mode")
            return

        self.index = faiss.IndexFlatIP(self.dimension)
        self.metadata = []

        # Delete files if they exist
        if os.path.exists(self.index_file):
            os.remove(self.index_file)
        if os.path.exists(self.metadata_file):
            os.remove(self.metadata_file)

        logger.info("Cleared local vector database")

    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector database."""
        if self.use_supabase:
            try:
                # Get document chunks count
                doc_count_query = "SELECT COUNT(*) as count FROM document_chunks"
                doc_count_result = supabase.execute_query(doc_count_query)
                doc_count = doc_count_result[0]["count"] if doc_count_result and "count" in doc_count_result[0] else 0

                # Get website chunks count
                web_count_query = "SELECT COUNT(*) as count FROM website_chunks"
                web_count_result = supabase.execute_query(web_count_query)
                web_count = web_count_result[0]["count"] if web_count_result and "count" in web_count_result[0] else 0

                # Calculate total
                total_count = doc_count + web_count

                return {
                    "storage_type": "supabase_pgvector",
                    "total_chunks": total_count,
                    "document_chunks": doc_count,
                    "website_chunks": web_count
                }
            except Exception as e:
                logger.error(f"Error getting Supabase stats: {str(e)}")
                return {
                    "storage_type": "supabase_pgvector",
                    "total_chunks": "unknown",
                    "document_chunks": "unknown",
                    "website_chunks": "unknown",
                    "error": str(e)
                }
        else:
            return {
                "storage_type": "local_faiss",
                "total_chunks": len(self.metadata),
                "index_size": self.index.ntotal,
                "document_chunks": sum(1 for item in self.metadata if item.get("source_type") == "document"),
                "website_chunks": sum(1 for item in self.metadata if item.get("source_type") == "website")
            }

    def is_initialized(self) -> bool:
        """Check if the vector database is initialized and ready to use."""
        if self.use_supabase:
            # For Supabase, we assume it's always initialized
            return True
        else:
            # For local mode, check if we have any vectors
            return self.index.ntotal > 0

# Create global instance to use throughout the application
vector_db = VectorDB()
