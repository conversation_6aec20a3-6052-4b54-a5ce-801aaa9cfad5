#!/usr/bin/env python3
"""
Complete flow test for document viewer functionality
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def test_complete_flow():
    """Test the complete document viewer flow"""
    print("=== Testing Complete Document Viewer Flow ===")
    
    # 1. Test server health
    print("\n1. Testing server health...")
    try:
        response = requests.get(f"{API_BASE}/api/health")
        if response.status_code == 200:
            print("✅ Server is healthy")
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server not accessible: {str(e)}")
        return False
    
    # 2. Test documents API
    print("\n2. Testing documents API...")
    try:
        response = requests.get(f"{API_BASE}/api/documents")
        if response.status_code == 200:
            documents = response.json()
            print(f"✅ Found {len(documents)} documents")
            if documents:
                print(f"   Sample document: {documents[0].get('name', 'Unknown')}")
            else:
                print("⚠️  No documents found")
                return False
        else:
            print(f"❌ Documents API failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Documents API error: {str(e)}")
        return False
    
    # 3. Test document viewer API
    print("\n3. Testing document viewer API...")
    test_files = ["SampleRailwayDoc.pdf", "ACP 110V.docx"]
    
    for filename in test_files:
        try:
            response = requests.get(f"{API_BASE}/api/documents/view/{filename}")
            if response.status_code == 200:
                print(f"✅ {filename} - accessible (size: {len(response.content)} bytes)")
            else:
                print(f"❌ {filename} - failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {filename} - error: {str(e)}")
    
    # 4. Test query with document sources
    print("\n4. Testing query with document sources...")
    try:
        query_data = {
            "query": "railway document",
            "fallback_enabled": True
        }
        
        response = requests.post(
            f"{API_BASE}/api/query",
            headers={"Content-Type": "application/json"},
            data=json.dumps(query_data)
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Query successful")
            print(f"   Answer length: {len(result.get('answer', ''))}")
            print(f"   Document sources: {len(result.get('document_sources', []))}")
            print(f"   Website sources: {len(result.get('website_sources', []))}")
            print(f"   LLM fallback: {result.get('llm_fallback', False)}")
            
            # Check if document sources have correct link format
            doc_sources = result.get('document_sources', [])
            if doc_sources:
                sample_source = doc_sources[0]
                link = sample_source.get('link', '')
                if '/viewer?' in link:
                    print(f"✅ Document source links correctly formatted: {link}")
                else:
                    print(f"❌ Document source links incorrectly formatted: {link}")
            else:
                print("⚠️  No document sources returned")
                
        else:
            print(f"❌ Query failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Query error: {str(e)}")
    
    # 5. Test frontend accessibility (if running)
    print("\n5. Testing frontend accessibility...")
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            
            # Test document viewer route
            viewer_url = "http://localhost:3000/viewer?file=SampleRailwayDoc.pdf&page=1"
            print(f"   Document viewer URL: {viewer_url}")
            print("   (Open this URL in browser to test document viewer)")
        else:
            print(f"⚠️  Frontend not accessible: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Frontend not accessible: {str(e)}")
    
    print("\n=== Flow Test Complete ===")
    return True

def test_specific_document_query():
    """Test a query that should definitely return document sources"""
    print("\n=== Testing Specific Document Query ===")
    
    # Try different queries that might match document content
    test_queries = [
        "ACP",
        "railway",
        "document",
        "PDF",
        "sample"
    ]
    
    for query in test_queries:
        print(f"\nTesting query: '{query}'")
        try:
            query_data = {
                "query": query,
                "fallback_enabled": False  # Disable fallback to force document search
            }
            
            response = requests.post(
                f"{API_BASE}/api/query",
                headers={"Content-Type": "application/json"},
                data=json.dumps(query_data)
            )
            
            if response.status_code == 200:
                result = response.json()
                doc_sources = result.get('document_sources', [])
                web_sources = result.get('website_sources', [])
                
                print(f"   Document sources: {len(doc_sources)}")
                print(f"   Website sources: {len(web_sources)}")
                
                if doc_sources:
                    print(f"   ✅ Found document sources!")
                    for i, source in enumerate(doc_sources[:2]):  # Show first 2
                        print(f"      {i+1}. {source.get('filename', 'Unknown')} - Page {source.get('page', 'Unknown')}")
                        print(f"         Link: {source.get('link', 'No link')}")
                    break  # Found working query
                else:
                    print(f"   ⚠️  No document sources found")
            else:
                print(f"   ❌ Query failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Query error: {str(e)}")

if __name__ == "__main__":
    test_complete_flow()
    test_specific_document_query() 