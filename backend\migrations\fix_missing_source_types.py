"""
Migration script to fix missing source_type values in document_chunks and website_chunks tables.

This script will:
1. Update document_chunks where source_type is null or 'unknown'
2. Update website_chunks where source_type is null or 'unknown'
3. Print a summary of changes made
"""

import os
import sys
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from backend
sys.path.append(str(Path(__file__).parent.parent))

# Import necessary modules
from supabase_client import supabase
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def fix_missing_source_types():
    """Fix missing source_type values in all chunks."""
    logger.info("Starting migration to fix missing source types...")

    # Fix document chunks
    fix_document_chunks()
    
    # Fix website chunks
    fix_website_chunks()
    
    logger.info("Migration completed successfully.")

def fix_document_chunks():
    """Fix missing source_type values in document_chunks table."""
    logger.info("Fixing missing source types in document chunks...")
    
    # Count how many document chunks have missing or unknown source_type
    count_query = """
    SELECT COUNT(*) 
    FROM document_chunks 
    WHERE source_type IS NULL OR source_type = 'unknown'
    """
    
    count_result = supabase.execute_query(count_query)
    if "error" in count_result:
        logger.error(f"Error counting document chunks with missing source type: {count_result['error']}")
        return
    
    try:
        count = count_result[0]['count']
        logger.info(f"Found {count} document chunks with missing source type.")
        
        if count > 0:
            # Update document chunks with missing source_type
            update_query = """
            UPDATE document_chunks
            SET source_type = 'document'
            WHERE source_type IS NULL OR source_type = 'unknown'
            """
            
            update_result = supabase.execute_query(update_query)
            if "error" in update_result:
                logger.error(f"Error updating document chunks: {update_result['error']}")
            else:
                logger.info(f"Successfully updated {count} document chunks with source_type = 'document'")
    except (KeyError, IndexError, TypeError) as e:
        logger.error(f"Error processing count result: {e}")

def fix_website_chunks():
    """Fix missing source_type values in website_chunks table."""
    logger.info("Fixing missing source types in website chunks...")
    
    # Count how many website chunks have missing or unknown source_type
    count_query = """
    SELECT COUNT(*) 
    FROM website_chunks 
    WHERE source_type IS NULL OR source_type = 'unknown'
    """
    
    count_result = supabase.execute_query(count_query)
    if "error" in count_result:
        logger.error(f"Error counting website chunks with missing source type: {count_result['error']}")
        return
    
    try:
        count = count_result[0]['count']
        logger.info(f"Found {count} website chunks with missing source type.")
        
        if count > 0:
            # Update website chunks with missing source_type
            update_query = """
            UPDATE website_chunks
            SET source_type = 'website'
            WHERE source_type IS NULL OR source_type = 'unknown'
            """
            
            update_result = supabase.execute_query(update_query)
            if "error" in update_result:
                logger.error(f"Error updating website chunks: {update_result['error']}")
            else:
                logger.info(f"Successfully updated {count} website chunks with source_type = 'website'")
    except (KeyError, IndexError, TypeError) as e:
        logger.error(f"Error processing count result: {e}")

if __name__ == "__main__":
    fix_missing_source_types()
