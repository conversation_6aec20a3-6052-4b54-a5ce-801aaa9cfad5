{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\components\\\\documents\\\\BulkCategoryEditor.tsx\";\nimport React, { useState, useEffect } from 'react';\nimport { getCategories, bulkUpdateDocumentCategories } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BulkCategoryEditor = ({\n  documents,\n  isOpen,\n  onClose,\n  onSave\n}) => {\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Form state\n  const [categoryUpdate, setCategoryUpdate] = useState({\n    main_category: '',\n    category: '',\n    sub_category: '',\n    minor_category: ''\n  });\n\n  // Load categories when modal opens\n  useEffect(() => {\n    if (isOpen) {\n      loadCategories();\n    }\n  }, [isOpen]);\n  const loadCategories = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const data = await getCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setError('Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = (field, value) => {\n    setCategoryUpdate(prev => ({\n      ...prev,\n      [field]: value || undefined\n    }));\n  };\n  const handleSave = async () => {\n    if (documents.length === 0) return;\n\n    // Check if at least one field is filled\n    const hasChanges = Object.values(categoryUpdate).some(value => value && value.trim() !== '');\n    if (!hasChanges) {\n      setError('Please select at least one category to update');\n      return;\n    }\n    setSaving(true);\n    setError(null);\n    try {\n      const documentIds = documents.map(doc => doc.id);\n\n      // Clean up the category update object - remove empty strings\n      const cleanedUpdate = {};\n      Object.entries(categoryUpdate).forEach(([key, value]) => {\n        if (value && value.trim() !== '') {\n          cleanedUpdate[key] = value.trim();\n        }\n      });\n      await bulkUpdateDocumentCategories(documentIds, cleanedUpdate);\n\n      // Create updated documents for the callback\n      const updatedDocuments = documents.map(doc => ({\n        ...doc,\n        mainCategory: cleanedUpdate.main_category || doc.mainCategory,\n        category: cleanedUpdate.category || doc.category,\n        subCategory: cleanedUpdate.sub_category || doc.subCategory,\n        minorCategory: cleanedUpdate.minor_category || doc.minorCategory\n      }));\n      onSave(updatedDocuments);\n      handleClose();\n    } catch (error) {\n      console.error('Error updating categories:', error);\n      setError(error instanceof Error ? error.message : 'Failed to update categories');\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleClose = () => {\n    setCategoryUpdate({\n      main_category: '',\n      category: '',\n      sub_category: '',\n      minor_category: ''\n    });\n    setError(null);\n    onClose();\n  };\n\n  // Get unique category values for dropdowns\n  const getMainCategories = () => {\n    const mainCats = new Set();\n    categories.forEach(cat => {\n      if (cat.level === 0) {\n        mainCats.add(cat.name);\n      }\n    });\n    return Array.from(mainCats).sort();\n  };\n  const getSubCategories = () => {\n    const subCats = new Set();\n    categories.forEach(cat => {\n      if (cat.level === 1) {\n        subCats.add(cat.name);\n      }\n    });\n    return Array.from(subCats).sort();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: [\"Bulk Update Categories (\", documents.length, \" documents)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-600\",\n            children: \"Loading categories...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-4 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-800\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Note:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this), \" Only fill in the categories you want to update. Empty fields will leave the existing values unchanged for each document.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Main Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: categoryUpdate.main_category || '',\n                onChange: e => handleInputChange('main_category', e.target.value),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Keep existing values\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this), getMainCategories().map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: cat,\n                  children: cat\n                }, cat, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: categoryUpdate.category || '',\n                onChange: e => handleInputChange('category', e.target.value),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Keep existing values\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this), getSubCategories().map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: cat,\n                  children: cat\n                }, cat, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Sub Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: categoryUpdate.sub_category || '',\n                onChange: e => handleInputChange('sub_category', e.target.value),\n                placeholder: \"Keep existing values\",\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Minor Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: categoryUpdate.minor_category || '',\n                onChange: e => handleInputChange('minor_category', e.target.value),\n                placeholder: \"Keep existing values\",\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-700 mb-2\",\n              children: [\"Selected Documents (\", documents.length, \"):\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-h-32 overflow-y-auto\",\n              children: documents.map(doc => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 py-1\",\n                children: [\"\\u2022 \", doc.name]\n              }, doc.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3 mt-6 pt-4 border-t\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            disabled: saving,\n            className: \"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 disabled:opacity-50\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSave,\n            disabled: saving || loading,\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center\",\n            children: [saving && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), saving ? 'Updating...' : 'Update Categories']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\nexport default BulkCategoryEditor;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getCategories", "bulkUpdateDocumentCategories", "jsxDEV", "_jsxDEV", "BulkCategoryEditor", "documents", "isOpen", "onClose", "onSave", "categories", "setCategories", "loading", "setLoading", "saving", "setSaving", "error", "setError", "categoryUpdate", "setCategoryUpdate", "main_category", "category", "sub_category", "minor_category", "loadCategories", "data", "console", "handleInputChange", "field", "value", "prev", "undefined", "handleSave", "length", "has<PERSON><PERSON><PERSON>", "Object", "values", "some", "trim", "documentIds", "map", "doc", "id", "cleanedUpdate", "entries", "for<PERSON>ach", "key", "updatedDocuments", "mainCategory", "subCategory", "minorCategory", "handleClose", "Error", "message", "getMainCategories", "mainCats", "Set", "cat", "level", "add", "name", "Array", "from", "sort", "getSubCategories", "subCats", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onChange", "e", "target", "type", "placeholder", "disabled"], "sources": ["C:/IR App/frontend/src/components/documents/BulkCategoryEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Document } from '../../types/documents';\nimport { \n  getCategories, \n  bulkUpdateDocumentCategories,\n  CategoryHierarchy,\n  DocumentCategoryUpdate \n} from '../../services/api';\n\ninterface BulkCategoryEditorProps {\n  documents: Document[];\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (updatedDocuments: Document[]) => void;\n}\n\nconst BulkCategoryEditor: React.FC<BulkCategoryEditorProps> = ({\n  documents,\n  isOpen,\n  onClose,\n  onSave,\n}) => {\n  const [categories, setCategories] = useState<CategoryHierarchy[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Form state\n  const [categoryUpdate, setCategoryUpdate] = useState<DocumentCategoryUpdate>({\n    main_category: '',\n    category: '',\n    sub_category: '',\n    minor_category: '',\n  });\n\n  // Load categories when modal opens\n  useEffect(() => {\n    if (isOpen) {\n      loadCategories();\n    }\n  }, [isOpen]);\n\n  const loadCategories = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const data = await getCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setError('Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field: keyof DocumentCategoryUpdate, value: string) => {\n    setCategoryUpdate(prev => ({\n      ...prev,\n      [field]: value || undefined\n    }));\n  };\n\n  const handleSave = async () => {\n    if (documents.length === 0) return;\n\n    // Check if at least one field is filled\n    const hasChanges = Object.values(categoryUpdate).some(value => value && value.trim() !== '');\n    if (!hasChanges) {\n      setError('Please select at least one category to update');\n      return;\n    }\n\n    setSaving(true);\n    setError(null);\n\n    try {\n      const documentIds = documents.map(doc => doc.id);\n      \n      // Clean up the category update object - remove empty strings\n      const cleanedUpdate: DocumentCategoryUpdate = {};\n      Object.entries(categoryUpdate).forEach(([key, value]) => {\n        if (value && value.trim() !== '') {\n          cleanedUpdate[key as keyof DocumentCategoryUpdate] = value.trim();\n        }\n      });\n\n      await bulkUpdateDocumentCategories(documentIds, cleanedUpdate);\n\n      // Create updated documents for the callback\n      const updatedDocuments = documents.map(doc => ({\n        ...doc,\n        mainCategory: cleanedUpdate.main_category || doc.mainCategory,\n        category: cleanedUpdate.category || doc.category,\n        subCategory: cleanedUpdate.sub_category || doc.subCategory,\n        minorCategory: cleanedUpdate.minor_category || doc.minorCategory,\n      }));\n\n      onSave(updatedDocuments);\n      handleClose();\n    } catch (error) {\n      console.error('Error updating categories:', error);\n      setError(error instanceof Error ? error.message : 'Failed to update categories');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleClose = () => {\n    setCategoryUpdate({\n      main_category: '',\n      category: '',\n      sub_category: '',\n      minor_category: '',\n    });\n    setError(null);\n    onClose();\n  };\n\n  // Get unique category values for dropdowns\n  const getMainCategories = () => {\n    const mainCats = new Set<string>();\n    categories.forEach(cat => {\n      if (cat.level === 0) {\n        mainCats.add(cat.name);\n      }\n    });\n    return Array.from(mainCats).sort();\n  };\n\n  const getSubCategories = () => {\n    const subCats = new Set<string>();\n    categories.forEach(cat => {\n      if (cat.level === 1) {\n        subCats.add(cat.name);\n      }\n    });\n    return Array.from(subCats).sort();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              Bulk Update Categories ({documents.length} documents)\n            </h2>\n            <button\n              onClick={handleClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n              {error}\n            </div>\n          )}\n\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n              <p className=\"mt-2 text-gray-600\">Loading categories...</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <p className=\"text-sm text-blue-800\">\n                  <strong>Note:</strong> Only fill in the categories you want to update. \n                  Empty fields will leave the existing values unchanged for each document.\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Main Category\n                  </label>\n                  <select\n                    value={categoryUpdate.main_category || ''}\n                    onChange={(e) => handleInputChange('main_category', e.target.value)}\n                    className=\"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">Keep existing values</option>\n                    {getMainCategories().map((cat) => (\n                      <option key={cat} value={cat}>\n                        {cat}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Category\n                  </label>\n                  <select\n                    value={categoryUpdate.category || ''}\n                    onChange={(e) => handleInputChange('category', e.target.value)}\n                    className=\"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">Keep existing values</option>\n                    {getSubCategories().map((cat) => (\n                      <option key={cat} value={cat}>\n                        {cat}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Sub Category\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={categoryUpdate.sub_category || ''}\n                    onChange={(e) => handleInputChange('sub_category', e.target.value)}\n                    placeholder=\"Keep existing values\"\n                    className=\"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Minor Category\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={categoryUpdate.minor_category || ''}\n                    onChange={(e) => handleInputChange('minor_category', e.target.value)}\n                    placeholder=\"Keep existing values\"\n                    className=\"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\n                <h3 className=\"text-sm font-medium text-gray-700 mb-2\">\n                  Selected Documents ({documents.length}):\n                </h3>\n                <div className=\"max-h-32 overflow-y-auto\">\n                  {documents.map((doc) => (\n                    <div key={doc.id} className=\"text-sm text-gray-600 py-1\">\n                      • {doc.name}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          <div className=\"flex justify-end space-x-3 mt-6 pt-4 border-t\">\n            <button\n              onClick={handleClose}\n              disabled={saving}\n              className=\"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 disabled:opacity-50\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleSave}\n              disabled={saving || loading}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center\"\n            >\n              {saving && (\n                <div className=\"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n              )}\n              {saving ? 'Updating...' : 'Update Categories'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BulkCategoryEditor;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SACEC,aAAa,EACbC,4BAA4B,QAGvB,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS5B,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,SAAS;EACTC,MAAM;EACNC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAsB,EAAE,CAAC;EACrE,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAyB;IAC3EqB,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACAvB,SAAS,CAAC,MAAM;IACd,IAAIO,MAAM,EAAE;MACViB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACjB,MAAM,CAAC,CAAC;EAEZ,MAAMiB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCX,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAMQ,IAAI,GAAG,MAAMxB,aAAa,CAAC,CAAC;MAClCU,aAAa,CAACc,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDC,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,iBAAiB,GAAGA,CAACC,KAAmC,EAAEC,KAAa,KAAK;IAChFV,iBAAiB,CAACW,IAAI,KAAK;MACzB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC,KAAK,IAAIE;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI1B,SAAS,CAAC2B,MAAM,KAAK,CAAC,EAAE;;IAE5B;IACA,MAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAClB,cAAc,CAAC,CAACmB,IAAI,CAACR,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAACS,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAC5F,IAAI,CAACJ,UAAU,EAAE;MACfjB,QAAQ,CAAC,+CAA+C,CAAC;MACzD;IACF;IAEAF,SAAS,CAAC,IAAI,CAAC;IACfE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMsB,WAAW,GAAGjC,SAAS,CAACkC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,CAAC;;MAEhD;MACA,MAAMC,aAAqC,GAAG,CAAC,CAAC;MAChDR,MAAM,CAACS,OAAO,CAAC1B,cAAc,CAAC,CAAC2B,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEjB,KAAK,CAAC,KAAK;QACvD,IAAIA,KAAK,IAAIA,KAAK,CAACS,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAChCK,aAAa,CAACG,GAAG,CAAiC,GAAGjB,KAAK,CAACS,IAAI,CAAC,CAAC;QACnE;MACF,CAAC,CAAC;MAEF,MAAMpC,4BAA4B,CAACqC,WAAW,EAAEI,aAAa,CAAC;;MAE9D;MACA,MAAMI,gBAAgB,GAAGzC,SAAS,CAACkC,GAAG,CAACC,GAAG,KAAK;QAC7C,GAAGA,GAAG;QACNO,YAAY,EAAEL,aAAa,CAACvB,aAAa,IAAIqB,GAAG,CAACO,YAAY;QAC7D3B,QAAQ,EAAEsB,aAAa,CAACtB,QAAQ,IAAIoB,GAAG,CAACpB,QAAQ;QAChD4B,WAAW,EAAEN,aAAa,CAACrB,YAAY,IAAImB,GAAG,CAACQ,WAAW;QAC1DC,aAAa,EAAEP,aAAa,CAACpB,cAAc,IAAIkB,GAAG,CAACS;MACrD,CAAC,CAAC,CAAC;MAEHzC,MAAM,CAACsC,gBAAgB,CAAC;MACxBI,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAACD,KAAK,YAAYoC,KAAK,GAAGpC,KAAK,CAACqC,OAAO,GAAG,6BAA6B,CAAC;IAClF,CAAC,SAAS;MACRtC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMoC,WAAW,GAAGA,CAAA,KAAM;IACxBhC,iBAAiB,CAAC;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFN,QAAQ,CAAC,IAAI,CAAC;IACdT,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAM8C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAS,CAAC;IAClC9C,UAAU,CAACmC,OAAO,CAACY,GAAG,IAAI;MACxB,IAAIA,GAAG,CAACC,KAAK,KAAK,CAAC,EAAE;QACnBH,QAAQ,CAACI,GAAG,CAACF,GAAG,CAACG,IAAI,CAAC;MACxB;IACF,CAAC,CAAC;IACF,OAAOC,KAAK,CAACC,IAAI,CAACP,QAAQ,CAAC,CAACQ,IAAI,CAAC,CAAC;EACpC,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,OAAO,GAAG,IAAIT,GAAG,CAAS,CAAC;IACjC9C,UAAU,CAACmC,OAAO,CAACY,GAAG,IAAI;MACxB,IAAIA,GAAG,CAACC,KAAK,KAAK,CAAC,EAAE;QACnBO,OAAO,CAACN,GAAG,CAACF,GAAG,CAACG,IAAI,CAAC;MACvB;IACF,CAAC,CAAC;IACF,OAAOC,KAAK,CAACC,IAAI,CAACG,OAAO,CAAC,CAACF,IAAI,CAAC,CAAC;EACnC,CAAC;EAED,IAAI,CAACxD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEH,OAAA;IAAK8D,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzF/D,OAAA;MAAK8D,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAC/F/D,OAAA;QAAK8D,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClB/D,OAAA;UAAK8D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD/D,OAAA;YAAI8D,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,0BAC1B,EAAC7D,SAAS,CAAC2B,MAAM,EAAC,aAC5C;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnE,OAAA;YACEoE,OAAO,EAAErB,WAAY;YACrBe,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7C/D,OAAA;cAAK8D,SAAS,EAAC,SAAS;cAACO,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAR,QAAA,eAC5E/D,OAAA;gBAAMwE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELvD,KAAK,iBACJZ,OAAA;UAAK8D,SAAS,EAAC,gEAAgE;UAAAC,QAAA,EAC5EnD;QAAK;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA3D,OAAO,gBACNR,OAAA;UAAK8D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/D,OAAA;YAAK8D,SAAS,EAAC;UAA2E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjGnE,OAAA;YAAG8D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,gBAENnE,OAAA;UAAK8D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/D,OAAA;YAAK8D,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxC/D,OAAA;cAAG8D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBAClC/D,OAAA;gBAAA+D,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,6HAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENnE,OAAA;YAAK8D,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD/D,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAO8D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnE,OAAA;gBACEyB,KAAK,EAAEX,cAAc,CAACE,aAAa,IAAI,EAAG;gBAC1C4D,QAAQ,EAAGC,CAAC,IAAKtD,iBAAiB,CAAC,eAAe,EAAEsD,CAAC,CAACC,MAAM,CAACrD,KAAK,CAAE;gBACpEqC,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G/D,OAAA;kBAAQyB,KAAK,EAAC,EAAE;kBAAAsC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7CjB,iBAAiB,CAAC,CAAC,CAACd,GAAG,CAAEiB,GAAG,iBAC3BrD,OAAA;kBAAkByB,KAAK,EAAE4B,GAAI;kBAAAU,QAAA,EAC1BV;gBAAG,GADOA,GAAG;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnE,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAO8D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnE,OAAA;gBACEyB,KAAK,EAAEX,cAAc,CAACG,QAAQ,IAAI,EAAG;gBACrC2D,QAAQ,EAAGC,CAAC,IAAKtD,iBAAiB,CAAC,UAAU,EAAEsD,CAAC,CAACC,MAAM,CAACrD,KAAK,CAAE;gBAC/DqC,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G/D,OAAA;kBAAQyB,KAAK,EAAC,EAAE;kBAAAsC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7CP,gBAAgB,CAAC,CAAC,CAACxB,GAAG,CAAEiB,GAAG,iBAC1BrD,OAAA;kBAAkByB,KAAK,EAAE4B,GAAI;kBAAAU,QAAA,EAC1BV;gBAAG,GADOA,GAAG;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnE,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAO8D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnE,OAAA;gBACE+E,IAAI,EAAC,MAAM;gBACXtD,KAAK,EAAEX,cAAc,CAACI,YAAY,IAAI,EAAG;gBACzC0D,QAAQ,EAAGC,CAAC,IAAKtD,iBAAiB,CAAC,cAAc,EAAEsD,CAAC,CAACC,MAAM,CAACrD,KAAK,CAAE;gBACnEuD,WAAW,EAAC,sBAAsB;gBAClClB,SAAS,EAAC;cAAkG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnE,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAO8D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnE,OAAA;gBACE+E,IAAI,EAAC,MAAM;gBACXtD,KAAK,EAAEX,cAAc,CAACK,cAAc,IAAI,EAAG;gBAC3CyD,QAAQ,EAAGC,CAAC,IAAKtD,iBAAiB,CAAC,gBAAgB,EAAEsD,CAAC,CAACC,MAAM,CAACrD,KAAK,CAAE;gBACrEuD,WAAW,EAAC,sBAAsB;gBAClClB,SAAS,EAAC;cAAkG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnE,OAAA;YAAK8D,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxC/D,OAAA;cAAI8D,SAAS,EAAC,wCAAwC;cAAAC,QAAA,GAAC,sBACjC,EAAC7D,SAAS,CAAC2B,MAAM,EAAC,IACxC;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnE,OAAA;cAAK8D,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtC7D,SAAS,CAACkC,GAAG,CAAEC,GAAG,iBACjBrC,OAAA;gBAAkB8D,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,SACrD,EAAC1B,GAAG,CAACmB,IAAI;cAAA,GADHnB,GAAG,CAACC,EAAE;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEX,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDnE,OAAA;UAAK8D,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5D/D,OAAA;YACEoE,OAAO,EAAErB,WAAY;YACrBkC,QAAQ,EAAEvE,MAAO;YACjBoD,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnE,OAAA;YACEoE,OAAO,EAAExC,UAAW;YACpBqD,QAAQ,EAAEvE,MAAM,IAAIF,OAAQ;YAC5BsD,SAAS,EAAC,qGAAqG;YAAAC,QAAA,GAE9GrD,MAAM,iBACLV,OAAA;cAAK8D,SAAS,EAAC;YAA6E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACnG,EACAzD,MAAM,GAAG,aAAa,GAAG,mBAAmB;UAAA;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAED,eAAelE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}