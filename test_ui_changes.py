#!/usr/bin/env python3
"""
Test script to verify UI display changes for visual content
"""

import requests
import time
import json

def test_ui_changes():
    """Test specific queries to verify UI changes"""
    print("🔍 Testing Visual Content UI Changes")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # Test queries to verify UI changes
    test_queries = [
        {"query": "Give me table of Quotation 1", "expect_table_in_answer": True, "expect_visual": False},
        {"query": "Show me Project 1 image", "expect_image_in_answer": True, "expect_visual": False},
        {"query": "What are the commercial terms", "expect_text": True, "expect_visual": True}
    ]
    
    for test_case in test_queries:
        query = test_case["query"]
        print(f"\n📝 Testing: {query}")
        
        try:
            # Send the query to the backend
            response = requests.post(
                f"{base_url}/api/query",
                json={'query': query, 'model': 'gemini-2.0-flash'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API Request successful")
                
                # Check document answer content
                doc_answer = result.get('document_answer', '')
                print(f"📄 Document answer length: {len(doc_answer)}")
                
                # Check if answer contains tables when expected
                if test_case.get("expect_table_in_answer"):
                    has_table = "table" in doc_answer.lower()
                    print(f"📊 Table in answer: {'✅ Yes' if has_table else '❌ No'}")
                
                # Check if answer contains images when expected
                if test_case.get("expect_image_in_answer"):
                    has_image = "image" in doc_answer.lower() or "picture" in doc_answer.lower()
                    print(f"🖼️ Image in answer: {'✅ Yes' if has_image else '❌ No'}")
                
                # Check visual content in document sources
                doc_sources = result.get('document_sources', [])
                visual_sources = [s for s in doc_sources if isinstance(s, dict) and s.get('visual_content')]
                
                print(f"🔎 Found {len(visual_sources)} visual content sources")
                for i, source in enumerate(visual_sources):
                    content_type = source.get('content_type', 'unknown')
                    print(f"   • Source {i+1}: {content_type}")
                    
                    if content_type == 'table':
                        table_data = source.get('visual_content', {}).get('table_data')
                        if table_data:
                            rows = len(table_data)
                            cols = len(table_data[0]) if rows > 0 and table_data[0] else 0
                            print(f"     Table size: {rows}x{cols}")
                    
                    if content_type == 'image':
                        print(f"     Image source: {source.get('filename', 'unknown')}")
                        has_url = bool(source.get('storage_url'))
                        has_base64 = bool(source.get('visual_content', {}).get('base64_data'))
                        print(f"     Has URL: {'✅' if has_url else '❌'}, Has base64: {'✅' if has_base64 else '❌'}")
            else:
                print(f"❌ API Request failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    print("\n✨ Test complete! Please check the frontend to verify the visual display")

if __name__ == "__main__":
    test_ui_changes() 