-- Optimizations for RailGPT Supabase integration
-- Run this script in the Supabase SQL Editor

-- 1. Create GIN indexes for text search
CREATE INDEX IF NOT EXISTS idx_document_chunks_text_gin ON document_chunks USING GIN (to_tsvector('english', text));
CREATE INDEX IF NOT EXISTS idx_website_chunks_text_gin ON website_chunks USING GIN (to_tsvector('english', text));

-- 2. Create optimized vector indexes
CREATE INDEX IF NOT EXISTS idx_document_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_website_chunks_embedding ON website_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- 3. Create partitioning for large tables
CREATE TABLE IF NOT EXISTS document_chunks_partitioned (
    id UUID PRIMARY KEY,
    document_id UUID NOT NULL,
    chunk_index INTEGER NOT NULL,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    embedding vector(768),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    partition_key INTEGER
) PARTITION BY RANGE (partition_key);

-- Create partitions for document chunks
CREATE TABLE IF NOT EXISTS document_chunks_p0 PARTITION OF document_chunks_partitioned
    FOR VALUES FROM (0) TO (1000);
CREATE TABLE IF NOT EXISTS document_chunks_p1 PARTITION OF document_chunks_partitioned
    FOR VALUES FROM (1000) TO (2000);
-- Add more partitions as needed

-- 4. Create optimized search function with caching
CREATE OR REPLACE FUNCTION optimized_search_documents(
    query_embedding vector(768),
    query_text text,
    match_threshold float,
    match_count int,
    cache_key text DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    cache_result jsonb;
BEGIN
    -- Check cache if cache_key is provided
    IF cache_key IS NOT NULL THEN
        SELECT result INTO cache_result
        FROM search_cache
        WHERE key = cache_key
        AND created_at > NOW() - INTERVAL '1 hour';
        
        IF cache_result IS NOT NULL THEN
            RETURN QUERY
            SELECT * FROM jsonb_to_recordset(cache_result) AS t(
                id UUID,
                document_id UUID,
                chunk_index INTEGER,
                text TEXT,
                metadata JSONB,
                url TEXT,
                domain TEXT,
                title TEXT,
                similarity float,
                source_type TEXT
            );
            RETURN;
        END IF;
    END IF;

    -- Perform optimized search
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.text,
        dc.metadata,
        d.file_path as url,
        d.main_category as domain,
        d.display_name as title,
        -- Optimized scoring: 70% vector, 30% text
        (0.7 * (1 - (dc.embedding <=> query_embedding)) + 
         0.3 * ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text))
        ) as similarity,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE 
        -- Use GIN index for text search
        to_tsvector('english', dc.text) @@ plainto_tsquery('english', query_text)
        OR (1 - (dc.embedding <=> query_embedding) > match_threshold)
    ORDER BY similarity DESC
    LIMIT match_count;

    -- Cache results if cache_key is provided
    IF cache_key IS NOT NULL THEN
        INSERT INTO search_cache (key, result, created_at)
        VALUES (
            cache_key,
            (SELECT jsonb_agg(t) FROM (
                SELECT * FROM optimized_search_documents(
                    query_embedding,
                    query_text,
                    match_threshold,
                    match_count,
                    NULL
                )
            ) t),
            NOW()
        )
        ON CONFLICT (key) DO UPDATE
        SET result = EXCLUDED.result,
            created_at = NOW();
    END IF;
END;
$$;

-- 5. Create search cache table
CREATE TABLE IF NOT EXISTS search_cache (
    key TEXT PRIMARY KEY,
    result JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL
);

-- 6. Create function to clean old cache entries
CREATE OR REPLACE FUNCTION clean_search_cache()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    DELETE FROM search_cache
    WHERE created_at < NOW() - INTERVAL '1 hour';
END;
$$;

-- 7. Create scheduled job to clean cache
SELECT cron.schedule(
    'clean-search-cache',
    '0 * * * *',  -- Every hour
    'SELECT clean_search_cache()'
); 