"""
<PERSON><PERSON><PERSON> to fix the hybrid search SQL functions in Supabase.

This script:
1. Creates simplified versions of the hybrid search functions that don't use ts_rank
2. Tests the functions to make sure they work
"""

import os
import logging
import json
from typing import List, Dict, Any
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import required modules
from supabase_client import supabase

# Load environment variables
load_dotenv()

def fix_hybrid_search_functions():
    """Fix the hybrid search functions in Supabase."""
    try:
        logger.info("Fixing hybrid search functions in Supabase...")
        
        # Create simplified versions of the hybrid search functions
        sql = """
        -- Drop existing functions if they exist
        DROP FUNCTION IF EXISTS hybrid_search_document_chunks(text, vector, float, int);
        DROP FUNCTION IF EXISTS hybrid_search_website_chunks(text, vector, float, int);

        -- Create function for hybrid document search (using only vector similarity)
        CREATE OR REPLACE FUNCTION hybrid_search_document_chunks(
            query_text text,
            query_embedding vector(768),
            match_threshold float,
            match_count int
        )
        RETURNS TABLE (
            id UUID,
            document_id UUID,
            chunk_index INTEGER,
            page_number INTEGER,
            text TEXT,
            metadata JSONB,
            url TEXT,
            domain TEXT,
            title TEXT,
            similarity float,
            source_type TEXT
        )
        LANGUAGE plpgsql
        AS $$
        BEGIN
            RETURN QUERY
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                dc.metadata,
                d.file_path as url,
                d.main_category as domain,
                d.display_name as title,
                -- Use only vector similarity for now
                (1 - (dc.embedding <=> query_embedding)) as similarity,
                'document'::TEXT as source_type
            FROM document_chunks dc
            JOIN documents d ON dc.document_id = d.id
            WHERE 
                -- Vector similarity threshold
                (1 - (dc.embedding <=> query_embedding) > match_threshold)
                -- Text search condition (optional but helps with relevance)
                OR (dc.text ILIKE '%' || query_text || '%')
            ORDER BY (1 - (dc.embedding <=> query_embedding)) DESC
            LIMIT match_count;
        END;
        $$;

        -- Create function for hybrid website search (using only vector similarity)
        CREATE OR REPLACE FUNCTION hybrid_search_website_chunks(
            query_text text,
            query_embedding vector(768),
            match_threshold float,
            match_count int
        )
        RETURNS TABLE (
            id uuid,
            website_id uuid,
            chunk_index int,
            text text,
            metadata jsonb,
            url text,
            domain text,
            title text,
            similarity float,
            source_type TEXT
        )
        LANGUAGE plpgsql
        AS $$
        BEGIN
            RETURN QUERY
            SELECT
                wc.id,
                wc.website_id,
                wc.chunk_index,
                wc.text,
                wc.metadata,
                w.url,
                w.domain,
                w.title,
                -- Use only vector similarity for now
                (1 - (wc.embedding <=> query_embedding)) as similarity,
                'website'::TEXT as source_type
            FROM website_chunks wc
            JOIN websites w ON wc.website_id = w.id
            WHERE 
                -- Vector similarity threshold
                (1 - (wc.embedding <=> query_embedding) > match_threshold)
                -- Text search condition (optional but helps with relevance)
                OR (wc.text ILIKE '%' || query_text || '%')
            ORDER BY (1 - (wc.embedding <=> query_embedding)) DESC
            LIMIT match_count;
        END;
        $$;
        """
        
        # Execute the SQL
        result = supabase.execute_sql(sql)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fixing hybrid search functions: {result['error']}")
            return False
        
        logger.info("Successfully fixed hybrid search functions in Supabase")
        return True
    
    except Exception as e:
        logger.error(f"Error fixing hybrid search functions: {str(e)}")
        return False

def test_hybrid_search_functions():
    """Test the hybrid search functions in Supabase."""
    try:
        logger.info("Testing hybrid search functions...")
        
        # Check if the functions exist
        sql = """
        SELECT EXISTS (
            SELECT 1 
            FROM pg_proc 
            WHERE proname = 'hybrid_search_document_chunks'
        );
        """
        
        result = supabase.execute_sql(sql)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing hybrid search functions: {result['error']}")
            return False
        
        if result and result[0].get('exists'):
            logger.info("hybrid_search_document_chunks function exists")
        else:
            logger.warning("hybrid_search_document_chunks function does not exist")
            return False
        
        # Check if the website function exists
        sql = """
        SELECT EXISTS (
            SELECT 1 
            FROM pg_proc 
            WHERE proname = 'hybrid_search_website_chunks'
        );
        """
        
        result = supabase.execute_sql(sql)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing hybrid search functions: {result['error']}")
            return False
        
        if result and result[0].get('exists'):
            logger.info("hybrid_search_website_chunks function exists")
        else:
            logger.warning("hybrid_search_website_chunks function does not exist")
            return False
        
        logger.info("Successfully tested hybrid search functions")
        return True
    
    except Exception as e:
        logger.error(f"Error testing hybrid search functions: {str(e)}")
        return False

if __name__ == "__main__":
    if fix_hybrid_search_functions():
        if test_hybrid_search_functions():
            logger.info("Successfully fixed and tested hybrid search functions")
        else:
            logger.error("Failed to test hybrid search functions")
    else:
        logger.error("Failed to fix hybrid search functions")
