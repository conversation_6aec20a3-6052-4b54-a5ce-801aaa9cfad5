"""
<PERSON><PERSON><PERSON> to test the database connection and execute simple queries.
"""
import os
import logging
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def test_database_connection():
    """Test the database connection and execute simple queries."""
    logger.info("Testing database connection...")
    
    # Test simple query
    try:
        result = supabase.execute_query("SELECT 1 as test")
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error executing simple query: {result['error']}")
        else:
            logger.info(f"Simple query result: {result}")
            logger.info("Database connection successful!")
    except Exception as e:
        logger.error(f"Error executing simple query: {str(e)}")
    
    # Test document_chunks table
    try:
        result = supabase.execute_query("SELECT COUNT(*) FROM document_chunks")
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error counting document_chunks: {result['error']}")
        else:
            logger.info(f"Document chunks count: {result}")
    except Exception as e:
        logger.error(f"Error counting document_chunks: {str(e)}")
    
    # Test website_chunks table
    try:
        result = supabase.execute_query("SELECT COUNT(*) FROM website_chunks")
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error counting website_chunks: {result['error']}")
        else:
            logger.info(f"Website chunks count: {result}")
    except Exception as e:
        logger.error(f"Error counting website_chunks: {str(e)}")
    
    # Test document_chunks with embeddings
    try:
        result = supabase.execute_query("SELECT COUNT(*) FROM document_chunks WHERE embedding IS NOT NULL")
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error counting document_chunks with embeddings: {result['error']}")
        else:
            logger.info(f"Document chunks with embeddings count: {result}")
    except Exception as e:
        logger.error(f"Error counting document_chunks with embeddings: {str(e)}")
    
    # Test website_chunks with embeddings
    try:
        result = supabase.execute_query("SELECT COUNT(*) FROM website_chunks WHERE embedding IS NOT NULL")
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error counting website_chunks with embeddings: {result['error']}")
        else:
            logger.info(f"Website chunks with embeddings count: {result}")
    except Exception as e:
        logger.error(f"Error counting website_chunks with embeddings: {str(e)}")
    
    # Test if vector extension is installed
    try:
        result = supabase.execute_query("SELECT * FROM pg_extension WHERE extname = 'vector'")
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error checking vector extension: {result['error']}")
        else:
            if result:
                logger.info("Vector extension is installed!")
            else:
                logger.error("Vector extension is NOT installed!")
    except Exception as e:
        logger.error(f"Error checking vector extension: {str(e)}")
    
    # Test if functions exist
    try:
        result = supabase.execute_query("SELECT routine_name FROM information_schema.routines WHERE routine_schema = 'public' AND routine_type = 'FUNCTION' AND routine_name LIKE '%search%'")
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error checking search functions: {result['error']}")
        else:
            logger.info(f"Search functions: {result}")
    except Exception as e:
        logger.error(f"Error checking search functions: {str(e)}")

def main():
    """Main function to test database connection."""
    test_database_connection()

if __name__ == "__main__":
    main()
