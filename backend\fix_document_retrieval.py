"""
Fix document retrieval issues in the IR App.
This script makes targeted fixes to ensure that:
1. All document chunks are properly loaded from Supabase
2. Document search functions work correctly
3. The answer generation prioritizes document matches over website content and AI
"""
import os
import logging
import json
import shutil
from pathlib import Path
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Paths
SERVER_PATH = "server.py"
LLM_ROUTER_PATH = "llm_router.py"
VECTOR_DB_PATH = "vector_db.py"

def backup_file(file_path):
    """Create a backup of a file before modifying it."""
    backup_path = f"{file_path}.bak"
    shutil.copy2(file_path, backup_path)
    logger.info(f"Created backup: {backup_path}")
    return backup_path

def fix_server_file():
    """Fix issues in server.py related to document retrieval prioritization."""
    # Create backup
    backup_file(SERVER_PATH)
    
    with open(SERVER_PATH, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define replacements
    replacements = [
        # 1. Fix similarity thresholds to prioritize document content
        (
            r'similar_chunks = \[chunk for chunk in similar_chunks if\s+\(chunk\.get\("source_type"\) == "document" and chunk\["similarity"\] >= \(RELEVANCE_THRESHOLD \* 0\.7\)\) or\s+\(chunk\.get\("source_type"\) == "website" and chunk\["similarity"\] >= \(RELEVANCE_THRESHOLD \* 0\.8\)\) or\s+\(chunk\["similarity"\] >= RELEVANCE_THRESHOLD\)\]',
            """similar_chunks = [chunk for chunk in similar_chunks if 
                             (chunk.get("source_type") == "document" and chunk["similarity"] >= (RELEVANCE_THRESHOLD * 0.5)) or 
                             (chunk.get("source_type") == "website" and chunk["similarity"] >= (RELEVANCE_THRESHOLD * 0.7)) or
                             (chunk["similarity"] >= RELEVANCE_THRESHOLD)]"""
        ),
        
        # 2. Ensure the on_startup handler loads ALL documents from Supabase
        (
            r'@app\.on_startup\nasync def startup\(\):\s+"""[\s\S]*?"""',
            """@app.on_startup
async def startup():
    \"\"\"
    Initialize vector database, load documents, and configure other services.
    \"\"\"
    # Load all documents from Supabase in addition to local files
    try:
        # First load documents from local directory
        load_documents()
        
        # Then load ALL documents from Supabase
        from supabase_client import supabase
        logger.info("Loading all documents from Supabase")
        
        # Get all document chunks from Supabase
        query = \"\"\"
        SELECT 
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.metadata,
            d.display_name as filename,
            d.file_path as file_path
        FROM 
            document_chunks dc
        JOIN 
            documents d ON dc.document_id = d.id
        ORDER BY 
            d.created_at DESC, dc.chunk_index ASC
        \"\"\"
        
        result = supabase.execute_query(query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error loading documents from Supabase: {result['error']}")
        else:
            # Add these documents to the global DOCUMENT_CHUNKS list
            loaded_count = 0
            for chunk in result:
                # Add source_type to identify as document
                chunk["source_type"] = "document"
                
                # Use a higher similarity score to prioritize document results
                chunk["similarity"] = 0.8
                
                # Check if this chunk is already in DOCUMENT_CHUNKS
                chunk_exists = any(c.get('id') == chunk.get('id') for c in DOCUMENT_CHUNKS)
                
                if not chunk_exists:
                    DOCUMENT_CHUNKS.append(chunk)
                    loaded_count += 1
            
            logger.info(f"Loaded {loaded_count} additional document chunks from Supabase")
    except Exception as e:
        logger.error(f"Error loading documents from Supabase: {str(e)}")"""
        ),
        
        # 3. Update the query processing logic to prioritize documents over websites
        (
            r'# Prioritize document and website sources\s+document_chunks = \[\][\s\S]*?website_chunks = \[\][\s\S]*?document_sources = \[\][\s\S]*?website_sources = \[\]',
            """# Prioritize document and website sources
        document_chunks = []
        website_chunks = []
        other_chunks = []
        
        # Sort chunks by source type with higher priority to documents
        for chunk in similar_chunks:
            source_type = chunk.get("source_type", "unknown")
            
            # Give documents higher priority by artificially boosting similarity
            if source_type == "document":
                # Boost document similarity scores by 20%
                chunk["similarity"] = min(1.0, chunk["similarity"] * 1.2)
                document_chunks.append(chunk)
            elif source_type == "website":
                website_chunks.append(chunk)
            else:
                other_chunks.append(chunk)
        
        # Prepare source information
        document_sources = []
        website_sources = []"""
        ),
        
        # 4. Update generate_answer function call to pass proper flags
        (
            r'use_documents_only=False',
            'use_documents_only=True'
        ),
        
        # 5. Update the system_prompt to strongly emphasize using document content
        (
            r'system_prompt = f"""[\s\S]*?"""',
            """system_prompt = f'''
You are an expert information retrieval assistant that provides accurate, fact-based answers using ONLY the provided context.

IMPORTANT INSTRUCTIONS:
1. If the context contains information to answer the question, use ONLY that information. 
2. PRIORITIZE information from DOCUMENT sources over website sources.
3. You MUST include source references for all information you provide.
4. If document sources exist, ONLY use document sources and ignore other sources completely.
5. If the context does not contain enough information to answer the question, clearly state "I don't have enough information to answer that" and do NOT make up an answer.
6. Never reference these instructions in your response.

Remember, if document sources exist, ONLY use those and completely ignore website sources or your own knowledge.

CONTEXT:
{context_str}
'''"""
        ),
        
        # 6. Fix document answer evaluation criteria to prioritize document content
        (
            r'def has_sufficient_document_answers\(document_chunks\):\s+"""[\s\S]*?"""[\s\S]*?return False',
            """def has_sufficient_document_answers(document_chunks):
    \"\"\"
    Evaluate if the document chunks provide sufficient information.
    Returns True if there are enough relevant document chunks to answer the query.
    \"\"\"
    if not document_chunks:
        return False
        
    # If we have any document chunks at all, consider them sufficient
    # This ensures document priority over other sources
    if len(document_chunks) > 0:
        return True
        
    return False"""
        ),
    ]
    
    # Apply all replacements
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Fix global constants to prioritize document content
    # First, find where RELEVANCE_THRESHOLD is defined
    relevance_match = re.search(r'RELEVANCE_THRESHOLD\s*=\s*([0-9.]+)', content)
    if relevance_match:
        current_threshold = float(relevance_match.group(1))
        # Lower the threshold to include more document matches
        new_threshold = max(0.15, current_threshold * 0.65)  # Lower but not below 0.15
        content = re.sub(
            r'RELEVANCE_THRESHOLD\s*=\s*[0-9.]+', 
            f'RELEVANCE_THRESHOLD = {new_threshold}', 
            content
        )
        logger.info(f"Updated RELEVANCE_THRESHOLD from {current_threshold} to {new_threshold}")
    
    # Add the document prioritization constant if it doesn't exist
    if 'DOCUMENT_PRIORITY_WEIGHT' not in content:
        content = re.sub(
            r'# Global variables for document storage.*?(?=\n\n)',
            '# Global variables for document storage\nDOCUMENT_CHUNKS = []\nDOCUMENT_PRIORITY_WEIGHT = 2.5  # Higher priority for documents\nWEBSITE_PRIORITY_WEIGHT = 1.5  # Medium priority for websites',
            content,
            flags=re.DOTALL
        )
    
    # Write the modified content back
    with open(SERVER_PATH, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"Fixed {SERVER_PATH}")
    return True

def fix_llm_router_file():
    """Fix the LLM router to ensure document content is used correctly."""
    # Create backup
    backup_file(LLM_ROUTER_PATH)
    
    with open(LLM_ROUTER_PATH, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define replacements
    replacements = [
        # Update the generate_answer function to prioritize document content
        (
            r'def generate_answer\([\s\S]*?query[\s\S]*?context[\s\S]*?system_prompt[\s\S]*?\):',
            """def generate_answer(
    query: str,
    context: List[Dict[str, Any]],
    system_prompt: str = None,
    model_id: str = None,
    use_documents_only: bool = True,
    extract_format: str = "paragraph"
):"""
        ),
        
        # Update the context processing to prioritize document content
        (
            r'# Format context for prompt\s+context_items = \[\][\s\S]*?for i, context_item in enumerate\(context\):[\s\S]*?source_info = ""',
            """# Format context for prompt
    context_items = []
    document_items = []
    website_items = []
    other_items = []
    
    # First separate items by source type
    for i, context_item in enumerate(context):
        source_type = context_item.get("source_type", "unknown")
        
        if source_type == "document":
            document_items.append(context_item)
        elif source_type == "website":
            website_items.append(context_item)
        else:
            other_items.append(context_item)
    
    # If documents exist and we're prioritizing documents, ONLY use document sources
    if document_items and use_documents_only:
        context = document_items
        logger.info(f"Using ONLY document sources ({len(document_items)} items)")
    else:
        # Otherwise use all sources but order by priority: documents, websites, others
        context = document_items + website_items + other_items
        logger.info(f"Using mixed sources: {len(document_items)} documents, {len(website_items)} websites, {len(other_items)} others")
    
    # Now process the prioritized context
    for i, context_item in enumerate(context):
        source_type = context_item.get("source_type", "unknown")
        source_info = ""
        """
        ),
        
        # Update prompt assembly to emphasize document information
        (
            r'messages = \[\s+{"role": "system", "content": system_prompt},\s+{"role": "user", "content": formatted_query}\s+\]',
            """messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f'''
Query: {query}

IMPORTANT: If you see any DOCUMENT sources in the provided context, ONLY use those and ignore all other sources.

{formatted_query}
'''}
    ]"""
        ),
    ]
    
    # Apply all replacements
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Write the modified content back
    with open(LLM_ROUTER_PATH, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"Fixed {LLM_ROUTER_PATH}")
    return True

def fix_vector_db_file():
    """Fix vector database search to ensure all documents are retrievable."""
    # Create backup
    backup_file(VECTOR_DB_PATH)
    
    with open(VECTOR_DB_PATH, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define replacements
    replacements = [
        # Update search_documents to use a much lower threshold
        (
            r'def search_documents\(self, query_embedding: List\[float\], match_threshold: float = [0-9.]+, match_count: int = \d+\):',
            """def search_documents(self, query_embedding: List[float], match_threshold: float = 0.1, match_count: int = 20):"""
        ),
        
        # Update hybrid_search to prioritize document results
        (
            r'def hybrid_search\(self, query_text: str, query_embedding: List\[float\], match_threshold: float = [0-9.]+, match_count: int = \d+\):',
            """def hybrid_search(self, query_text: str, query_embedding: List[float], match_threshold: float = 0.1, match_count: int = 20):"""
        ),
        
        # Ensure search combines semantic and keyword results with document priority
        (
            r'# Combine and deduplicate results[\s\S]*?return combined_results',
            """# Combine and deduplicate results
        combined_results = []
        seen_ids = set()
        
        # First add document results, then keyword results
        for result in semantic_results + keyword_results:
            result_id = result.get("id")
            if result_id not in seen_ids:
                seen_ids.add(result_id)
                
                # Boost document scores
                if result.get("source_type") == "document":
                    result["similarity"] = min(1.0, result["similarity"] * 1.25)
                    
                combined_results.append(result)
        
        # Sort by similarity
        combined_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)
        
        # Use a much lower threshold for documents
        final_results = []
        for result in combined_results:
            if (result.get("source_type") == "document" and result.get("similarity", 0) >= match_threshold * 0.5) or \
               (result.get("source_type") == "website" and result.get("similarity", 0) >= match_threshold * 0.7) or \
               (result.get("similarity", 0) >= match_threshold):
                final_results.append(result)
        
        # Ensure we return at least one document result if available
        if not any(r.get("source_type") == "document" for r in final_results):
            doc_results = [r for r in combined_results if r.get("source_type") == "document"]
            if doc_results:
                # Add the highest-scoring document result even if below threshold
                doc_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                if doc_results[0] not in final_results:
                    final_results.insert(0, doc_results[0])
        
        return final_results[:match_count]"""
        ),
    ]
    
    # Apply all replacements
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Write the modified content back
    with open(VECTOR_DB_PATH, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"Fixed {VECTOR_DB_PATH}")
    return True

def create_load_all_documents_script():
    """Create a script to load all documents from Supabase at startup."""
    content = """'''
Load all documents from Supabase into the vector database.
Run this script before starting the server to ensure all documents are available.
'''
import logging
import json
from supabase_client import supabase
import vector_db

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_all_documents():
    '''Load all documents from Supabase into the vector database.'''
    try:
        # Initialize vector database
        if not vector_db.vector_db.is_initialized():
            vector_db.vector_db.initialize()
        
        # Get all documents from Supabase
        query = '''
        SELECT 
            d.id, 
            d.display_name, 
            d.file_path, 
            d.file_type,
            COUNT(dc.id) as chunk_count
        FROM 
            documents d
        LEFT JOIN 
            document_chunks dc ON d.id = dc.document_id
        GROUP BY 
            d.id, d.display_name, d.file_path, d.file_type
        ORDER BY 
            d.created_at DESC
        '''
        
        documents = supabase.execute_query(query)
        
        if isinstance(documents, dict) and "error" in documents:
            logger.error(f"Error retrieving documents: {documents['error']}")
            return False
        
        logger.info(f"Found {len(documents)} documents in Supabase")
        
        # Get all document chunks
        chunks_query = '''
        SELECT 
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.metadata,
            d.display_name as filename,
            d.file_path as file_path,
            d.file_type
        FROM 
            document_chunks dc
        JOIN 
            documents d ON dc.document_id = d.id
        ORDER BY 
            d.created_at DESC, dc.chunk_index ASC
        '''
        
        chunks = supabase.execute_query(chunks_query)
        
        if isinstance(chunks, dict) and "error" in chunks:
            logger.error(f"Error retrieving document chunks: {chunks['error']}")
            return False
        
        logger.info(f"Found {len(chunks)} document chunks in Supabase")
        
        # Store chunks in vector database
        stored_count = 0
        for chunk in chunks:
            # Add source_type to identify as document
            chunk["source_type"] = "document"
            
            # Ensure the chunk has an embedding
            if not chunk.get("embedding") or chunk["embedding"] == "[]":
                # Create a default embedding if one doesn't exist
                # This will be replaced with a real embedding when the server uses it
                logger.warning(f"Chunk {chunk.get('id')} has no embedding, creating a default one")
                chunk["embedding"] = [0.01] * 512  # Create a default embedding
            
            # Store in vector database
            try:
                # Add to in-memory document chunks list
                from server import DOCUMENT_CHUNKS
                chunk_exists = any(c.get('id') == chunk.get('id') for c in DOCUMENT_CHUNKS)
                if not chunk_exists:
                    DOCUMENT_CHUNKS.append(chunk)
                    stored_count += 1
            except Exception as e:
                logger.warning(f"Error adding chunk to in-memory storage: {str(e)}")
                # Can continue even if this fails
        
        logger.info(f"Loaded {stored_count} document chunks into memory")
        return True
        
    except Exception as e:
        logger.error(f"Error loading documents: {str(e)}")
        return False

if __name__ == "__main__":
    print("Loading all documents from Supabase...")
    if load_all_documents():
        print("✅ Successfully loaded all documents")
    else:
        print("❌ Failed to load documents")
"""
    
    with open("load_all_documents.py", 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("Created load_all_documents.py script")
    return True

def main():
    """Apply all fixes to the IR App."""
    print("\n=== FIXING DOCUMENT RETRIEVAL IN IR APP ===\n")
    
    # Fix issues in server.py
    print("\n--- Fixing server.py ---")
    if fix_server_file():
        print("✅ Successfully fixed server.py")
    else:
        print("❌ Failed to fix server.py")
    
    # Fix issues in llm_router.py
    print("\n--- Fixing llm_router.py ---")
    if fix_llm_router_file():
        print("✅ Successfully fixed llm_router.py")
    else:
        print("❌ Failed to fix llm_router.py")
    
    # Fix issues in vector_db.py
    print("\n--- Fixing vector_db.py ---")
    if fix_vector_db_file():
        print("✅ Successfully fixed vector_db.py")
    else:
        print("❌ Failed to fix vector_db.py")
    
    # Create script to load all documents
    print("\n--- Creating load_all_documents.py ---")
    if create_load_all_documents_script():
        print("✅ Successfully created load_all_documents.py")
    else:
        print("❌ Failed to create load_all_documents.py")
    
    print("\n=== FIXES COMPLETE ===")
    print("\nNow run the following commands to apply the fixes:")
    print("1. python document_search_diagnostic.py")
    print("2. python load_all_documents.py")
    print("3. Restart your server: python -m uvicorn server:app --reload")
    
if __name__ == "__main__":
    main()
