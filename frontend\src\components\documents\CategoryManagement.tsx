import React, { useState, useEffect } from 'react';
import { CategoryHierarchy, CategoryCreate, CategoryUpdate } from '../../types/documents';
import { 
  getCategories, 
  createCategory, 
  updateCategory, 
  deleteCategory,
  buildCategoryTree
} from '../../services/categoryApi';
import { 
  Plus, 
  Edit2, 
  Trash2, 
  ChevronRight, 
  ChevronDown, 
  Save, 
  X, 
  AlertCircle, 
  CheckCircle,
  FolderOpen,
  Folder
} from 'lucide-react';

interface CategoryManagementProps {
  isOpen: boolean;
  onClose: () => void;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({ isOpen, onClose }) => {
  const [categories, setCategories] = useState<CategoryHierarchy[]>([]);
  const [categoryTree, setCategoryTree] = useState<CategoryHierarchy[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // UI state
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Form state
  const [formData, setFormData] = useState<CategoryCreate>({
    name: '',
    type: 'main_category',
    description: '',
    sort_order: 0,
  });

  const [editFormData, setEditFormData] = useState<CategoryUpdate>({});

  useEffect(() => {
    if (isOpen) {
      loadCategories();
    }
  }, [isOpen]);

  useEffect(() => {
    if (categories.length > 0) {
      const tree = buildCategoryTree(categories);
      setCategoryTree(tree);
    }
  }, [categories]);

  const loadCategories = async () => {
    setLoading(true);
    setError(null);
    try {
      const categoryData = await getCategories();
      setCategories(categoryData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCategory = async () => {
    if (!formData.name.trim()) {
      setError('Category name is required');
      return;
    }

    try {
      setError(null);
      await createCategory(formData);
      setSuccess('Category created successfully!');
      setShowCreateForm(false);
      setFormData({
        name: '',
        type: 'main_category',
        description: '',
        sort_order: 0,
      });
      await loadCategories();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create category');
    }
  };

  const handleUpdateCategory = async (categoryId: string) => {
    try {
      setError(null);
      await updateCategory(categoryId, editFormData);
      setSuccess('Category updated successfully!');
      setEditingCategory(null);
      setEditFormData({});
      await loadCategories();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update category');
    }
  };

  const handleDeleteCategory = async (categoryId: string, categoryName: string) => {
    if (!window.confirm(`Are you sure you want to delete the category "${categoryName}"? This will deactivate it but preserve data integrity.`)) {
      return;
    }

    try {
      setError(null);
      await deleteCategory(categoryId);
      setSuccess('Category deleted successfully!');
      await loadCategories();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete category');
    }
  };

  const toggleExpanded = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const startEditing = (category: CategoryHierarchy) => {
    setEditingCategory(category.id);
    setEditFormData({
      name: category.name,
      description: category.description,
      sort_order: category.sort_order,
    });
  };

  const cancelEditing = () => {
    setEditingCategory(null);
    setEditFormData({});
  };

  const renderCategoryNode = (category: CategoryHierarchy, level: number = 0) => {
    const hasChildren = category.children && category.children.length > 0;
    const isExpanded = expandedCategories.has(category.id);
    const isEditing = editingCategory === category.id;

    return (
      <div key={category.id} className="border-b border-gray-100 last:border-b-0">
        <div 
          className={`flex items-center py-2 px-4 hover:bg-gray-50 ${level > 0 ? 'ml-' + (level * 4) : ''}`}
          style={{ paddingLeft: `${level * 20 + 16}px` }}
        >
          {/* Expand/Collapse Button */}
          <button
            onClick={() => toggleExpanded(category.id)}
            className="mr-2 p-1 hover:bg-gray-200 rounded"
            disabled={!hasChildren}
          >
            {hasChildren ? (
              isExpanded ? (
                <ChevronDown className="h-4 w-4 text-gray-600" />
              ) : (
                <ChevronRight className="h-4 w-4 text-gray-600" />
              )
            ) : (
              <div className="h-4 w-4" />
            )}
          </button>

          {/* Category Icon */}
          <div className="mr-3">
            {hasChildren ? (
              isExpanded ? (
                <FolderOpen className="h-4 w-4 text-blue-500" />
              ) : (
                <Folder className="h-4 w-4 text-blue-500" />
              )
            ) : (
              <div className="h-4 w-4 bg-gray-300 rounded-sm" />
            )}
          </div>

          {/* Category Content */}
          <div className="flex-1 min-w-0">
            {isEditing ? (
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={editFormData.name || ''}
                  onChange={(e) => setEditFormData({ ...editFormData, name: e.target.value })}
                  className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                  placeholder="Category name"
                />
                <input
                  type="text"
                  value={editFormData.description || ''}
                  onChange={(e) => setEditFormData({ ...editFormData, description: e.target.value })}
                  className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                  placeholder="Description"
                />
                <input
                  type="number"
                  value={editFormData.sort_order || 0}
                  onChange={(e) => setEditFormData({ ...editFormData, sort_order: parseInt(e.target.value) || 0 })}
                  className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                  placeholder="Order"
                />
                <button
                  onClick={() => handleUpdateCategory(category.id)}
                  className="p-1 text-green-600 hover:bg-green-100 rounded"
                >
                  <Save className="h-4 w-4" />
                </button>
                <button
                  onClick={cancelEditing}
                  className="p-1 text-gray-600 hover:bg-gray-100 rounded"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{category.name}</span>
                    <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                      {category.type.replace('_', ' ')}
                    </span>
                    <span className="text-xs text-gray-500">#{category.sort_order}</span>
                  </div>
                  {category.description && (
                    <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                  )}
                  <p className="text-xs text-gray-400 mt-1">{category.full_path}</p>
                </div>
                <div className="flex items-center space-x-1 ml-4">
                  <button
                    onClick={() => startEditing(category)}
                    className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                  >
                    <Edit2 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteCategory(category.id, category.name)}
                    className="p-1 text-red-600 hover:bg-red-100 rounded"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Render children */}
        {hasChildren && isExpanded && (
          <div>
            {category.children!.map(child => renderCategoryNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Category Management</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowCreateForm(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        )}

        {success && (
          <div className="mx-6 mt-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            <span className="text-green-700 text-sm">{success}</span>
          </div>
        )}

        {/* Create Form */}
        {showCreateForm && (
          <div className="mx-6 mt-4 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h3 className="font-medium text-gray-900 mb-3">Create New Category</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Category name"
              />
              <select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="main_category">Main Category</option>
                <option value="category">Category</option>
                <option value="sub_category">Sub Category</option>
                <option value="minor_category">Minor Category</option>
              </select>
              <input
                type="text"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Description"
              />
              <div className="flex space-x-2">
                <input
                  type="number"
                  value={formData.sort_order}
                  onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
                  className="w-20 px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Order"
                />
                <button
                  onClick={handleCreateCategory}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  Create
                </button>
                <button
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading categories...</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {categoryTree.map(category => renderCategoryNode(category))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            Total categories: {categories.length}
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default CategoryManagement;
