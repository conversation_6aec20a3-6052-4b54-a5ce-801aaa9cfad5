"""
LLM client for RailGPT fallback responses.
Handles interactions with various LLM providers.
"""
import logging
import os
from typing import Optional, Dict, Any
import google.generativeai as genai
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Configure Gemini API
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)

async def get_llm_response(
    query: str,
    model: str = "gemini",
    context: Optional[list] = None,
    max_tokens: int = 1000,
    temperature: float = 0.7
) -> Dict[str, Any]:
    """
    Get a response from the specified LLM model.
    
    Args:
        query: The user's query
        model: The LLM model to use (default: gemini)
        context: Optional list of relevant chunks for context
        max_tokens: Maximum number of tokens in response
        temperature: Response temperature (0.0 to 1.0)
        
    Returns:
        Dictionary containing the LLM response and metadata
    """
    try:
        if model.lower() == "gemini":
            return await _get_gemini_response(query, context, max_tokens, temperature)
        else:
            raise ValueError(f"Unsupported LLM model: {model}")
    except Exception as e:
        logger.error(f"Error getting LLM response: {str(e)}")
        raise

async def _get_gemini_response(
    query: str,
    context: Optional[list],
    max_tokens: int,
    temperature: float
) -> Dict[str, Any]:
    """Get a response from Gemini AI."""
    if not GEMINI_API_KEY:
        raise ValueError("Gemini API key not configured")
    
    try:
        # Initialize Gemini model
        model = genai.GenerativeModel('gemini-2.0-flash')
        
        # Prepare context if available
        context_text = ""
        if context:
            context_text = "\n\n".join([
                f"Context {i+1}:\n{chunk.get('text', '')}"
                for i, chunk in enumerate(context)
            ])
        
        # Prepare prompt
        prompt = f"""You are RailGPT, an AI assistant for Indian Railways information.
Please provide a helpful response to the following query.
If you have relevant context, use it to inform your response.

{context_text if context_text else "No specific context available."}

Query: {query}

Please provide a clear, accurate, and helpful response."""

        # Generate response
        response = await model.generate_content_async(
            prompt,
            generation_config={
                "max_output_tokens": max_tokens,
                "temperature": temperature
            }
        )
        
        return {
            "text": response.text,
            "model": "gemini",
            "tokens_used": response.usage.total_tokens if hasattr(response, "usage") else None
        }
        
    except Exception as e:
        logger.error(f"Error with Gemini API: {str(e)}")
        raise 