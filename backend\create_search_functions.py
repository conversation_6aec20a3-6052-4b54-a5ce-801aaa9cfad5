"""
<PERSON><PERSON><PERSON> to create search functions in the database for RailGPT.
This script will create SQL functions for vector search.
"""
import os
import logging
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def create_search_functions():
    """Create SQL functions for vector search."""
    logger.info("Creating SQL functions for vector search...")
    
    # Direct search for document chunks
    direct_search_doc_function = """
    CREATE OR REPLACE FUNCTION direct_search_document_chunks(
        query_embedding vector,
        match_threshold float DEFAULT 0.01,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        document_id uuid,
        chunk_index int,
        page_number int,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.embedding,
            dc.metadata,
            dc.created_at,
            dc.updated_at,
            CASE 
                WHEN dc.embedding IS NULL THEN 0.01
                ELSE 1 - (dc.embedding <=> query_embedding)
            END AS similarity
        FROM
            document_chunks dc
        WHERE
            CASE 
                WHEN dc.embedding IS NULL THEN TRUE
                ELSE 1 - (dc.embedding <=> query_embedding) > match_threshold
            END
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Hybrid search for document chunks
    hybrid_search_doc_function = """
    CREATE OR REPLACE FUNCTION hybrid_search_document_chunks(
        query_text text,
        query_embedding vector,
        match_threshold float DEFAULT 0.01,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        document_id uuid,
        chunk_index int,
        page_number int,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.embedding,
            dc.metadata,
            dc.created_at,
            dc.updated_at,
            CASE 
                WHEN dc.embedding IS NULL THEN 0.01 + (ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text)) * 0.2)
                ELSE (1 - (dc.embedding <=> query_embedding)) * 0.8 +
                     (ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text)) * 0.2)
            END AS similarity
        FROM
            document_chunks dc
        WHERE
            CASE 
                WHEN dc.embedding IS NULL THEN TRUE
                ELSE 1 - (dc.embedding <=> query_embedding) > match_threshold
            END
            OR to_tsvector('english', dc.text) @@ plainto_tsquery('english', query_text)
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Direct search for website chunks
    direct_search_web_function = """
    CREATE OR REPLACE FUNCTION direct_search_website_chunks(
        query_embedding vector,
        match_threshold float DEFAULT 0.01,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        website_id uuid,
        url text,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            wc.id,
            wc.website_id,
            wc.url,
            wc.text,
            wc.embedding,
            wc.metadata,
            wc.created_at,
            wc.updated_at,
            CASE 
                WHEN wc.embedding IS NULL THEN 0.01
                ELSE 1 - (wc.embedding <=> query_embedding)
            END AS similarity
        FROM
            website_chunks wc
        WHERE
            CASE 
                WHEN wc.embedding IS NULL THEN TRUE
                ELSE 1 - (wc.embedding <=> query_embedding) > match_threshold
            END
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Hybrid search for website chunks
    hybrid_search_web_function = """
    CREATE OR REPLACE FUNCTION hybrid_search_website_chunks(
        query_text text,
        query_embedding vector,
        match_threshold float DEFAULT 0.01,
        match_count int DEFAULT 30
    )
    RETURNS TABLE (
        id uuid,
        website_id uuid,
        url text,
        text text,
        embedding vector,
        metadata jsonb,
        created_at timestamptz,
        updated_at timestamptz,
        similarity float
    )
    LANGUAGE plpgsql
    AS $$
    BEGIN
        RETURN QUERY
        SELECT
            wc.id,
            wc.website_id,
            wc.url,
            wc.text,
            wc.embedding,
            wc.metadata,
            wc.created_at,
            wc.updated_at,
            CASE 
                WHEN wc.embedding IS NULL THEN 0.01 + (ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', query_text)) * 0.2)
                ELSE (1 - (wc.embedding <=> query_embedding)) * 0.8 +
                     (ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', query_text)) * 0.2)
            END AS similarity
        FROM
            website_chunks wc
        WHERE
            CASE 
                WHEN wc.embedding IS NULL THEN TRUE
                ELSE 1 - (wc.embedding <=> query_embedding) > match_threshold
            END
            OR to_tsvector('english', wc.text) @@ plainto_tsquery('english', query_text)
        ORDER BY
            similarity DESC
        LIMIT
            match_count;
    END;
    $$;
    """
    
    # Execute the function creation queries
    functions = [
        ("direct_search_document_chunks", direct_search_doc_function),
        ("hybrid_search_document_chunks", hybrid_search_doc_function),
        ("direct_search_website_chunks", direct_search_web_function),
        ("hybrid_search_website_chunks", hybrid_search_web_function)
    ]
    
    for name, query in functions:
        try:
            logger.info(f"Creating function {name}...")
            result = supabase.execute_query(query)
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error creating function {name}: {result['error']}")
            else:
                logger.info(f"Successfully created function {name}")
        except Exception as e:
            logger.error(f"Error creating function {name}: {str(e)}")

def main():
    """Main function to create search functions."""
    create_search_functions()
    
    logger.info("Search functions created successfully!")

if __name__ == "__main__":
    main()
