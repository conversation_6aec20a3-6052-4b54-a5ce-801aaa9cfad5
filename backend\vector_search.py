"""
Optimized vector search implementation for RailGPT.
This module provides concurrent and scalable search functions for document and website content.
"""
import logging
from typing import List, Dict, Any, Optional
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor
import hashlib
from functools import lru_cache
from supabase_client import supabase
from llm_client import get_llm_response  # Import LLM client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configure concurrency settings
MAX_CONCURRENT_SEARCHES = 10
SEARCH_TIMEOUT = 30  # seconds
CACHE_TTL = 3600  # 1 hour in seconds
MIN_SIMILARITY_THRESHOLD = 0.75  # Increased threshold to ensure only relevant results prevent fallback

# Create thread pool for concurrent searches
search_executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_SEARCHES)

def generate_cache_key(query_embedding: List[float], query_text: str, source_type: str) -> str:
    """Generate a cache key for search results."""
    key_data = f"{json.dumps(query_embedding)}_{query_text}_{source_type}"
    return hashlib.md5(key_data.encode()).hexdigest()

@lru_cache(maxsize=1000)
def get_cached_results(cache_key: str) -> Optional[List[Dict[str, Any]]]:
    """Get cached search results if available."""
    try:
        result = supabase.execute_query(
            "SELECT result FROM search_cache WHERE key = $1 AND created_at > NOW() - INTERVAL '1 hour'",
            {"1": cache_key}
        )
        if result and not isinstance(result, dict):
            return json.loads(result[0]["result"])
    except Exception as e:
        logger.warning(f"Cache retrieval failed: {str(e)}")
    return None

async def search_documents(
    query_embedding: List[float],
    query_text: Optional[str] = None,
    use_hybrid_search: bool = True,
    match_threshold: float = 0.0001,
    match_count: int = 30,
    document_filter: Optional[List[str]] = None,
    timeout: int = SEARCH_TIMEOUT,
    max_retries: int = 2
) -> List[Dict[str, Any]]:
    """
    Asynchronously search for document chunks using optimized vector similarity and text matching.
    
    Args:
        query_embedding: Vector embedding of the query
        query_text: Original query text for hybrid search
        use_hybrid_search: Whether to use hybrid search
        match_threshold: Minimum similarity threshold
        match_count: Maximum number of results
        document_filter: Optional list of document IDs to filter
        timeout: Search timeout in seconds
        max_retries: Maximum number of retry attempts
        
    Returns:
        List of document chunks with similarity scores
    """
    cache_key = generate_cache_key(query_embedding, query_text or "", "document")
    cached_results = get_cached_results(cache_key)
    if cached_results:
        logger.info("Returning cached document search results")
        return cached_results

    try:
        # Format the embedding as a PostgreSQL vector
        embedding_str = json.dumps(query_embedding)
        
        # Use the optimized search function
        query = f"""
        SELECT * FROM optimized_search_documents(
            '{embedding_str}'::vector,
            $1,
            {match_threshold},
            {match_count},
            $2
        )
        """
        
        params = {
            "1": query_text or "",
            "2": cache_key
        }
        
        # Execute search with timeout
        loop = asyncio.get_event_loop()
        result = await asyncio.wait_for(
            loop.run_in_executor(
                search_executor,
                lambda: supabase.execute_query(query, params)
            ),
            timeout=timeout
        )
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error in document search: {result['error']}")
            if max_retries > 0:
                logger.info(f"Retrying document search ({max_retries} attempts remaining)")
                return await search_documents(
                    query_embedding,
                    query_text,
                    use_hybrid_search,
                    match_threshold,
                    match_count,
                    document_filter,
                    timeout,
                    max_retries - 1
                )
            return []
        
        # Process and return results
        for item in result:
            item["source_type"] = "document"
            item.setdefault("url", "")
            item.setdefault("domain", "")
            item.setdefault("title", "")
            item.setdefault("similarity", 0.0)
        
        return result
        
    except asyncio.TimeoutError:
        logger.error("Document search timed out")
        return []
    except Exception as e:
        logger.error(f"Error in document search: {str(e)}")
        return []

async def search_websites(
    query_embedding: List[float],
    query_text: Optional[str] = None,
    use_hybrid_search: bool = True,
    match_threshold: float = 0.15,  # Higher threshold for websites
    match_count: int = 30,
    website_filter: Optional[List[str]] = None,
    timeout: int = SEARCH_TIMEOUT,
    max_retries: int = 2
) -> List[Dict[str, Any]]:
    """
    Asynchronously search for website chunks using optimized vector similarity and text matching.
    This is only called if no document chunks are found.
    
    Args:
        query_embedding: Vector embedding of the query
        query_text: Original query text for hybrid search
        use_hybrid_search: Whether to use hybrid search
        match_threshold: Minimum similarity threshold (higher than documents)
        match_count: Maximum number of results
        website_filter: Optional list of website IDs to filter
        timeout: Search timeout in seconds
        max_retries: Maximum number of retry attempts
        
    Returns:
        List of website chunks with similarity scores
    """
    cache_key = generate_cache_key(query_embedding, query_text or "", "website")
    cached_results = get_cached_results(cache_key)
    if cached_results:
        logger.info("Returning cached website search results")
        return cached_results

    try:
        # Format the embedding as a PostgreSQL vector
        embedding_str = json.dumps(query_embedding)
        
        # Use the optimized search function
        query = f"""
        SELECT * FROM optimized_search_websites(
            '{embedding_str}'::vector,
            $1,
            {match_threshold},
            {match_count},
            $2
        )
        """
        
        params = {
            "1": query_text or "",
            "2": cache_key
        }
        
        # Execute search with timeout
        loop = asyncio.get_event_loop()
        result = await asyncio.wait_for(
            loop.run_in_executor(
                search_executor,
                lambda: supabase.execute_query(query, params)
            ),
            timeout=timeout
        )
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error in website search: {result['error']}")
            if max_retries > 0:
                logger.info(f"Retrying website search ({max_retries} attempts remaining)")
                return await search_websites(
                    query_embedding,
                    query_text,
                    use_hybrid_search,
                    match_threshold,
                    match_count,
                    website_filter,
                    timeout,
                    max_retries - 1
                )
            return []
        
        # Process and return results
        for item in result:
            item["source_type"] = "website"
            item.setdefault("url", "")
            item.setdefault("domain", "")
            item.setdefault("title", "")
            item.setdefault("similarity", 0.0)
        
        return result
        
    except asyncio.TimeoutError:
        logger.error("Website search timed out")
        return []
    except Exception as e:
        logger.error(f"Error in website search: {str(e)}")
        return []

async def search_all(
    query_embedding: List[float],
    query_text: Optional[str] = None,
    use_hybrid_search: bool = True,
    document_threshold: float = 0.0001,
    website_threshold: float = 0.15,
    match_count: int = 30,
    timeout: int = SEARCH_TIMEOUT,
    llm_fallback: bool = False,  # Default to false, only enable if no relevant results found
    llm_model: str = "gemini"  # Default to Gemini
) -> Dict[str, Any]:
    """
    Search both documents and websites with proper priority handling and LLM fallback.
    
    Args:
        query_embedding: Vector embedding of the query
        query_text: Original query text for hybrid search
        use_hybrid_search: Whether to use hybrid search
        document_threshold: Minimum similarity threshold for documents
        website_threshold: Minimum similarity threshold for websites
        match_count: Maximum number of results
        timeout: Search timeout in seconds
        llm_fallback: Whether to use LLM fallback
        llm_model: LLM model to use for fallback (default: gemini)
        
    Returns:
        Dictionary containing search results and metadata
    """
    # Track whether we have relevant results from documents or websites
    has_document_results = False
    has_website_results = False
    
    # First search documents - STRICT PRIORITY #1
    logger.info(f"Searching documents with threshold: {document_threshold}")
    doc_results = await search_documents(
        query_embedding,
        query_text,
        use_hybrid_search,
        document_threshold,
        match_count,
        timeout=timeout
    )
    
    # Log document search results
    logger.info(f"Document search returned {len(doc_results)} results")
    if doc_results:
        max_similarity = max([r.get("similarity", 0) for r in doc_results]) if doc_results else 0
        logger.info(f"Maximum document similarity score: {max_similarity}")
        has_document_results = max_similarity >= MIN_SIMILARITY_THRESHOLD
        logger.info(f"Has relevant document results: {has_document_results}")
    
    # If we have relevant document results, use them and don't proceed to websites
    if has_document_results:
        logger.info("Using document results only (priority #1)")
        return {
            "results": doc_results[:match_count],
            "source": "documents",
            "used_llm": False,
            "llm_fallback": False
        }
    
    # If no relevant document results, search websites - STRICT PRIORITY #2
    logger.info(f"No relevant document results found, searching websites with threshold: {website_threshold}")
    web_results = await search_websites(
        query_embedding,
        query_text,
        use_hybrid_search,
        website_threshold,
        match_count,
        timeout=timeout
    )
    
    # Log website search results
    logger.info(f"Website search returned {len(web_results)} results")
    if web_results:
        max_similarity = max([r.get("similarity", 0) for r in web_results]) if web_results else 0
        logger.info(f"Maximum website similarity score: {max_similarity}")
        has_website_results = max_similarity >= MIN_SIMILARITY_THRESHOLD
        logger.info(f"Has relevant website results: {has_website_results}")
    
    # If we have relevant website results, use them
    if has_website_results:
        logger.info("Using website results (priority #2)")
        return {
            "results": web_results[:match_count],
            "source": "websites",
            "used_llm": False,
            "llm_fallback": False
        }
    
    # STRICT PRIORITY #3: Only fallback to LLM if no relevant results from either source
    if not has_document_results and not has_website_results and query_text:
        logger.info("No relevant results from documents or websites, enabling LLM fallback")
        llm_fallback = True
    else:
        logger.info("Found relevant results, disabling LLM fallback")
        llm_fallback = False
    
    # Combine and sort results to return best available, even if below threshold
    all_results = doc_results + web_results
    all_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)
    
    # If both document and website searches yield insufficient results, use LLM fallback
    if llm_fallback and query_text:
        try:
            logger.info(f"Using LLM fallback with model: {llm_model}")
            llm_response = await get_llm_response(
                query_text,
                model=llm_model,
                context=all_results[:5] if all_results else None  # Provide top results as context
            )
            
            return {
                "results": all_results[:match_count],  # Include any found results
                "llm_response": llm_response,
                "source": "llm",
                "used_llm": True,
                "llm_model": llm_model,
                "llm_fallback": True,  # Explicitly mark as using fallback
                "document_count": len(doc_results),
                "website_count": len(web_results),
                "similarity_scores": {
                    "document_max": max([r.get("similarity", 0) for r in doc_results]) if doc_results else 0,
                    "website_max": max([r.get("similarity", 0) for r in web_results]) if web_results else 0
                }
            }
        except Exception as e:
            logger.error(f"LLM fallback failed: {str(e)}")
            # Return whatever results we have, even if they're not great
            return {
                "results": all_results[:match_count],
                "source": "documents_and_websites",
                "used_llm": False,
                "llm_fallback": False,
                "error": "LLM fallback failed"
            }
    
    # If no LLM fallback and results are insufficient, return what we have
    return {
        "results": all_results[:match_count],
        "source": "documents_and_websites",
        "used_llm": False,
        "llm_fallback": False,
        "warning": "Results may not be relevant",
        "document_count": len(doc_results),
        "website_count": len(web_results),
        "similarity_scores": {
            "document_max": max([r.get("similarity", 0) for r in doc_results]) if doc_results else 0,
            "website_max": max([r.get("similarity", 0) for r in web_results]) if web_results else 0
        }
    }
