import React, { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

// Configure PDF.js worker with the correct version for react-pdf 5.7.2
// Use the exact version that matches the react-pdf library
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js`;

interface PDFViewerProps {
  url: string;
  fileName: string;
}

const PDFViewer: React.FC<PDFViewerProps> = ({ url, fileName }) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setIsLoading(false);
    setError(null);
  }

  function onDocumentLoadError(error: Error) {
    console.error('Error loading PDF:', error);
    const errorMessage = error.message || 'Unknown error';

    // Check for common PDF.js version issues
    if (errorMessage.includes('version') || errorMessage.includes('worker')) {
      setError('PDF viewer configuration issue. The document is available but the viewer needs updating.');
    } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      setError('Network error loading PDF. Please check your connection.');
    } else {
      setError(`Failed to load PDF: ${errorMessage}`);
    }
    setIsLoading(false);
  }

  function changePage(offset: number) {
    if (!numPages) return;
    const newPage = pageNumber + offset;
    if (newPage >= 1 && newPage <= numPages) {
      setPageNumber(newPage);
    }
  }

  function changeScale(newScale: number) {
    setScale(newScale);
  }

  return (
    <div className="flex flex-col items-center">
      <div className="mb-4 w-full flex justify-between items-center bg-gray-100 p-2 rounded-lg">
        <div className="flex space-x-2">
          <button
            onClick={() => changePage(-1)}
            disabled={pageNumber <= 1}
            className="px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => changePage(1)}
            disabled={!numPages || pageNumber >= numPages}
            className="px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
        <div className="flex items-center">
          <span className="text-sm">
            Page {pageNumber} of {numPages || '--'}
          </span>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => changeScale(scale - 0.2)}
            disabled={scale <= 0.6}
            className="px-2 py-1 bg-gray-200 rounded disabled:bg-gray-100 disabled:text-gray-400"
          >
            -
          </button>
          <span className="text-sm">{Math.round(scale * 100)}%</span>
          <button
            onClick={() => changeScale(scale + 0.2)}
            disabled={scale >= 2.0}
            className="px-2 py-1 bg-gray-200 rounded disabled:bg-gray-100 disabled:text-gray-400"
          >
            +
          </button>
        </div>
      </div>

      <div className="border border-gray-300 rounded-lg overflow-auto w-full max-h-[60vh] bg-gray-50">
        {isLoading && (
          <div className="flex items-center justify-center h-60">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center h-60 text-red-600 text-center p-4">
            <div>
              <p className="font-semibold">Failed to load PDF</p>
              <p className="text-sm mt-2">{error}</p>
              <p className="text-sm mt-4">Filename: {fileName}</p>

              <div className="mt-4 space-y-2">
                <button
                  onClick={() => window.open(url, '_blank')}
                  className="block w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Open in New Tab
                </button>

                <button
                  onClick={() => {
                    // Try to reload with different worker
                    console.log('Retrying PDF load with different worker...');
                    pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.worker.min.js`;
                    window.location.reload();
                  }}
                  className="block w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  Retry with Different Viewer
                </button>

                <a
                  href={url}
                  download={fileName}
                  className="block w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-center"
                >
                  Download PDF
                </a>
              </div>
            </div>
          </div>
        )}

        <Document
          file={url}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading={
            <div className="flex items-center justify-center h-60">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
          }
          error={
            <div className="flex items-center justify-center h-60 text-red-600">
              <p>Failed to load PDF</p>
            </div>
          }
        >
          <Page
            pageNumber={pageNumber}
            scale={scale}
            renderTextLayer={false}
            renderAnnotationLayer={false}
            error="Failed to load page"
          />
        </Document>
      </div>
    </div>
  );
};

export default PDFViewer;
