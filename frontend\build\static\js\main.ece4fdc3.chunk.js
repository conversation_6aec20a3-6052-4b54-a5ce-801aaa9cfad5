(this.webpackJsonpfrontend=this.webpackJsonpfrontend||[]).push([[0],{16:function(e,t,s){"use strict";s.d(t,"h",(function(){return r})),s.d(t,"g",(function(){return n})),s.d(t,"b",(function(){return c})),s.d(t,"e",(function(){return o})),s.d(t,"d",(function(){return i})),s.d(t,"i",(function(){return l})),s.d(t,"c",(function(){return d})),s.d(t,"f",(function(){return u})),s.d(t,"a",(function(){return m}));var a=s(56);const r=Object(a.a)("https://rkllidjktazafeinezgo.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA"),n=async e=>{try{const{data:t,error:s}=await r.from("queries").insert([e]).select().single();return s?(console.error("Error saving query:",s),null):t}catch(t){return console.error("Error in saveQuery:",t),null}},c=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"New Chat",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"gemini-2.0-flash";try{const{data:s,error:a}=await r.from("chat_sessions").insert([{title:e,messages:[],model_used:t,has_document:!1,has_website:!1}]).select().single();return a?(console.error("Error creating chat session:",a),null):{...s,messages:s.messages||[]}}catch(s){return console.error("Error in createChatSession:",s),null}},o=async e=>{try{let t=r.from("chat_sessions").select("*").order("updated_at",{ascending:!1});e&&(t=t.eq("user_id",e));const{data:s,error:a}=await t;return a?(console.error("Error fetching chat sessions:",a),[]):(s||[]).map((e=>({...e,messages:e.messages||[]})))}catch(t){return console.error("Error in getChatSessions:",t),[]}},i=async e=>{try{const{data:t,error:s}=await r.from("chat_sessions").select("*").eq("id",e).single();return s?(console.error("Error fetching chat session:",s),null):{...t,messages:t.messages||[]}}catch(t){return console.error("Error in getChatSessionById:",t),null}},l=async(e,t)=>{try{const{error:s}=await r.from("chat_sessions").update({title:t}).eq("id",e);return!s||(console.error("Error updating chat title:",s),!1)}catch(s){return console.error("Error in updateChatTitle:",s),!1}},d=async e=>{try{const{error:t}=await r.from("chat_sessions").delete().eq("id",e);return!t||(console.error("Error deleting chat session:",t),!1)}catch(t){return console.error("Error in deleteChatSession:",t),!1}},u=async(e,t)=>{try{const s=t.map((e=>({id:e.id,content:e.content,document_answer:e.document_answer||void 0,website_answer:e.website_answer||void 0,llm_model:e.llm_model,sender:e.sender,loading:e.loading,sources:e.sources||void 0,document_sources:e.document_sources||void 0,website_sources:e.website_sources||void 0,timestamp:e.timestamp,chatId:e.chatId,llm_fallback:e.llm_fallback}))),a=s.some((e=>e.document_answer||e.document_sources&&e.document_sources.length>0||e.sources&&e.sources.some((e=>"document"===e.source_type)))),n=s.some((e=>e.website_answer||e.website_sources&&e.website_sources.length>0||e.sources&&e.sources.some((e=>"website"===e.source_type))));console.log("Saving chat messages:",{messageCount:s.length,hasDocument:a,hasWebsite:n,messagesWithDocAnswer:s.filter((e=>e.document_answer)).length,messagesWithWebAnswer:s.filter((e=>e.website_answer)).length});const{error:c}=await r.from("chat_sessions").update({messages:s,has_document:a,has_website:n}).eq("id",e);return!c||(console.error("Error saving chat messages:",c),!1)}catch(s){return console.error("Error in saveChatMessages:",s),!1}},m=async e=>{try{let t=r.from("chat_sessions").delete();e&&(t=t.eq("user_id",e));const{error:s}=await t;return!s||(console.error("Error clearing chat sessions:",s),!1)}catch(t){return console.error("Error in clearAllChatSessions:",t),!1}}},47:function(e,t){},67:function(e,t,s){},68:function(e,t,s){},8:function(e,t,s){"use strict";s.r(t),s.d(t,"API_URL",(function(){return r})),s.d(t,"sendQuery",(function(){return n})),s.d(t,"uploadDocument",(function(){return c})),s.d(t,"addWebsite",(function(){return o})),s.d(t,"getDocumentExtractionDetails",(function(){return i})),s.d(t,"getDocumentContent",(function(){return l})),s.d(t,"getWebsiteExtractionDetails",(function(){return d})),s.d(t,"getWebsiteContent",(function(){return u})),s.d(t,"submitFeedback",(function(){return m})),s.d(t,"getFeedbackEmails",(function(){return b})),s.d(t,"updateFeedbackEmails",(function(){return g})),s.d(t,"getDocuments",(function(){return x})),s.d(t,"getWebsites",(function(){return j})),s.d(t,"getCategories",(function(){return h})),s.d(t,"createCategory",(function(){return p})),s.d(t,"updateCategory",(function(){return f})),s.d(t,"deleteCategory",(function(){return y})),s.d(t,"updateDocumentCategories",(function(){return O})),s.d(t,"bulkUpdateDocumentCategories",(function(){return w})),s.d(t,"getWebsiteCategories",(function(){return v})),s.d(t,"createWebsiteCategory",(function(){return N})),s.d(t,"updateWebsiteCategories",(function(){return k})),s.d(t,"bulkUpdateWebsiteCategories",(function(){return C}));var a=s(16);const r="http://localhost:8000",n=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"gemini-2.0-flash",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"paragraph",n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];console.log("Processing query:",e,"using model:",t);const c={answer:`No meaningful answer found for '${e}'. The system encountered an error processing your request.`,document_answer:"",website_answer:"",sources:[],document_sources:[],website_sources:[]},o={answer:`Backend server at ${r} is not available. Please ensure the server is running with 'uvicorn server:app --reload'.`,document_answer:"",website_answer:"",sources:[],document_sources:[],website_sources:[]};try{console.log("Connecting to backend at:",r);try{const o=2;let d=0,u=t,m=null;for(;d<=o;){console.log(`Attempt ${d+1}/${o+1}: Sending query to: ${r}/api/query with model ${u}`);const t=new AbortController,a=setTimeout((()=>t.abort()),6e4);try{if(console.log(`Sending query to main server: ${r}/api/query`),m=await fetch(`${r}/api/query`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({query:e,model:u,fallback_enabled:!0,extract_format:s,use_hybrid_search:n,retry_on_timeout:!0}),signal:t.signal}),clearTimeout(a),m.ok)break}catch(i){if(clearTimeout(a),i instanceof DOMException&&"AbortError"===i.name?console.warn(`Query timed out with model ${u}, retrying with fallback model...`):console.error(`Fetch error with model ${u}:`,i),("gemini-2.0-flash"===u||"gemini-2.0-flash"===u||"gemini-2.0-flash"!==u)&&(u="gemini-2.0-flash"),d===o)throw i}d++}if(m&&m.ok){const s=await m.json();if(s&&!s.llm_model&&(s.llm_model=t),!s.answer)return console.error("Invalid response format from server:",s),{...c,answer:"The server returned an invalid response format. Please try again later."};try{{const r=performance.now();await Object(a.g)({query_text:e,answer_text:s.answer,llm_model:t,sources:s.sources||[],processing_time:(performance.now()-r)/1e3})}}catch(i){console.error("Error saving query to Supabase:",i)}return s}if(m){let e;console.error("Error from backend:",m.status,m.statusText);try{e=await m.text()}catch(l){e="Unable to parse error details"}return console.error("Error details:",e),{...c,answer:`Error ${m.status}: ${m.statusText}. ${e}`}}return{...c,answer:"The server did not respond. Please try again later."}}catch(i){if(i instanceof DOMException&&"AbortError"===i.name){console.error("Query request timed out");let e="Query timed out after 60 seconds.";return e+="gemini-2.0-flash"===t||"gemini-2.0-flash"===t?" Try a faster model like 'gemini-2.0-flash' or simplify your query.":"gemini-2.0-flash"===t?" Try simplifying your query.":" Try a faster model or simplify your query.",{...c,answer:e}}return console.error("Error querying backend:",i),o}}catch(i){return console.error("Error connecting to backend:",i),o}},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";console.log("Uploading document:",e.name);try{let n=null,c=null;try{const s=`${t}/${Date.now()}_${e.name}`,{data:r,error:o}=await a.h.storage.from("documents").upload(s,e,{cacheControl:"3600",upsert:!1});if(o)console.error("Error uploading to Supabase Storage:",o);else{n=r.path;const{data:{publicUrl:e}}=a.h.storage.from("documents").getPublicUrl(n);c=e,console.log("Uploaded to Supabase Storage:",n,c)}}catch(s){console.error("Error in Supabase Storage upload:",s)}const o=new FormData;o.append("file",e),o.append("uploaded_by",t),n&&(o.append("supabase_file_path",n),o.append("supabase_file_url",c||""));const i=await fetch(`${r}/api/upload-document`,{method:"POST",body:o});if(!i.ok){return{success:!1,message:(await i.json().catch((()=>({detail:i.statusText})))).detail||`Upload failed: ${i.status} ${i.statusText}`}}const l=await i.json();return console.log("Upload response:",l),{success:!0,message:l.message,chunks_extracted:l.chunks_extracted,chunks:l.chunks,data:{id:l.document_id||`doc-${Date.now()}`,path:n||`/documents/${e.name}`,originalResponse:l}}}catch(n){return console.error("Error uploading document:",n),{success:!1,message:n instanceof Error?n.message:"Unknown error occurred during upload"}}},o=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",s=arguments.length>2?arguments[2]:void 0;console.log("Adding website:",e,"with options:",s);try{const a={url:e,submitted_by:t,...s},n=await fetch(`${r}/api/add-website`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!n.ok){return{success:!1,message:(await n.json().catch((()=>({detail:n.statusText})))).detail||`Website add failed: ${n.status} ${n.statusText}`}}const c=await n.json();return console.log("Website add response:",c),{success:!0,message:c.message,chunks_extracted:c.chunks_extracted,chunks:c.chunks,data:{id:`web-${Date.now()}`,path:e,originalResponse:c}}}catch(a){return console.error("Error adding website:",a),{success:!1,message:a instanceof Error?a.message:"Unknown error occurred while adding website"}}},i=async e=>{console.log("Getting extraction details for document:",e);try{const t=await fetch(`${r}/api/documents/${e}/extraction-details`);if(!t.ok)throw console.error(`Failed to get document extraction details: ${t.status} ${t.statusText}`),new Error(`Failed to get document extraction details: ${t.statusText}`);return await t.json()}catch(t){return console.error("Error getting document extraction details:",t),{extractedContent:`Sample extracted content for ${e}. This is placeholder text because the actual content could not be retrieved from the server.`,extractionMethod:"Unknown (error occurred)",qualityScore:0,processingTime:0,chunks:0,warnings:["Failed to retrieve extraction details from server"],fallbackReason:t instanceof Error?t.message:"Unknown error"}}},l=async e=>{console.log("Getting content for document:",e);try{const t=await fetch(`${r}/api/documents/${e}/content`);if(!t.ok)throw console.error(`Failed to get document content: ${t.status} ${t.statusText}`),new Error(`Failed to get document content: ${t.statusText}`);return await t.json()}catch(t){return console.error("Error getting document content:",t),{content:`# Document Content Unavailable\n\nThe content for document ${e} could not be retrieved from the server.\n\n**Error:** ${t instanceof Error?t.message:"Unknown error"}\n\nPlease try again later or contact support if the problem persists.`,extraction_method:"Unknown (error occurred)",quality_score:0,processing_time:0,chunks_count:0}}},d=async e=>{console.log("Getting extraction details for website:",e);try{const t=await fetch(`${r}/api/websites/${e}/extraction-details`);if(!t.ok)throw console.error(`Failed to get website extraction details: ${t.status} ${t.statusText}`),new Error(`Failed to get website extraction details: ${t.statusText}`);return await t.json()}catch(t){return console.error("Error getting website extraction details:",t),{extractedContent:`Sample extracted content for ${e}. This is placeholder text because the actual content could not be retrieved from the server.`,extractionMethod:"Unknown (error occurred)",fallbackHistory:[],contentQuality:0,warnings:["Failed to retrieve extraction details from server"],processingTime:0,chunks:0}}},u=async e=>{console.log("Getting content for website:",e);try{const t=await fetch(`${r}/api/websites/${e}/content`);if(!t.ok)throw console.error(`Failed to get website content: ${t.status} ${t.statusText}`),new Error(`Failed to get website content: ${t.statusText}`);return await t.json()}catch(t){return console.error("Error getting website content:",t),{content:`# Website Content Unavailable\n\nThe content for website ${e} could not be retrieved from the server.\n\n**Error:** ${t instanceof Error?t.message:"Unknown error"}\n\nPlease try again later or contact support if the problem persists.`,extraction_method:"Unknown (error occurred)",quality_score:0,processing_time:0,pages_processed:0,total_links:0}}},m=async e=>{console.log("Submitting feedback:",e);try{const t=await fetch(`${r}/api/feedback`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(e)});if(!t.ok)throw console.error(`Failed to submit feedback: ${t.status} ${t.statusText}`),new Error(`Failed to submit feedback: ${t.statusText}`);return await t.json()}catch(t){return console.error("Error submitting feedback:",t),{success:!1,message:`Failed to submit feedback: ${t instanceof Error?t.message:"Unknown error"}`}}},b=async()=>{try{const e=await fetch(`${r}/api/feedback/emails`);if(!e.ok)throw console.error(`Failed to get feedback emails: ${e.status} ${e.statusText}`),new Error(`Failed to get feedback emails: ${e.statusText}`);return await e.json()}catch(e){return console.error("Error getting feedback emails:",e),{emails:[]}}},g=async e=>{try{const t=await fetch(`${r}/api/feedback/emails`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({emails:e})});if(!t.ok)throw console.error(`Failed to update feedback emails: ${t.status} ${t.statusText}`),new Error(`Failed to update feedback emails: ${t.statusText}`);return await t.json()}catch(t){return console.error("Error updating feedback emails:",t),{success:!1,message:`Failed to update feedback emails: ${t instanceof Error?t.message:"Unknown error"}`}}},x=async()=>{console.log("Fetching documents from backend...");try{const e=await fetch(`${r}/api/documents`);if(!e.ok)throw console.error(`Failed to get documents: ${e.status} ${e.statusText}`),new Error(`Failed to get documents: ${e.statusText}`);return await e.json()}catch(e){return console.error("Error getting documents:",e),[]}},j=async()=>{console.log("Fetching websites from backend...");try{const e=await fetch(`${r}/api/websites`);if(!e.ok)throw console.error(`Failed to get websites: ${e.status} ${e.statusText}`),new Error(`Failed to get websites: ${e.statusText}`);return await e.json()}catch(e){return console.error("Error getting websites:",e),[]}},h=async()=>{console.log("Fetching categories from backend...");try{const e=await fetch(`${r}/api/categories/`);if(!e.ok)throw console.error(`Failed to get categories: ${e.status} ${e.statusText}`),new Error(`Failed to get categories: ${e.statusText}`);return await e.json()}catch(e){return console.error("Error getting categories:",e),[]}},p=async e=>{console.log("Creating category:",e);try{const t=await fetch(`${r}/api/categories/`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){const e=await t.json().catch((()=>({detail:t.statusText})));throw new Error(e.detail||`Failed to create category: ${t.statusText}`)}return await t.json()}catch(t){throw console.error("Error creating category:",t),t}},f=async(e,t)=>{console.log("Updating category:",e,t);try{const s=await fetch(`${r}/api/categories/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok){const e=await s.json().catch((()=>({detail:s.statusText})));throw new Error(e.detail||`Failed to update category: ${s.statusText}`)}return await s.json()}catch(s){throw console.error("Error updating category:",s),s}},y=async e=>{console.log("Deleting category:",e);try{const t=await fetch(`${r}/api/categories/${e}`,{method:"DELETE"});if(!t.ok){const e=await t.json().catch((()=>({detail:t.statusText})));throw new Error(e.detail||`Failed to delete category: ${t.statusText}`)}return await t.json()}catch(t){throw console.error("Error deleting category:",t),t}},O=async(e,t)=>{console.log("Updating document categories:",e,t);try{const s=await fetch(`${r}/api/categories/documents/${e}/categories`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok){const e=await s.json().catch((()=>({detail:s.statusText})));throw new Error(e.detail||`Failed to update document categories: ${s.statusText}`)}return await s.json()}catch(s){throw console.error("Error updating document categories:",s),s}},w=async(e,t)=>{console.log("Bulk updating document categories:",e,t);try{const s=await fetch(`${r}/api/categories/documents/bulk-update-categories`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({document_ids:e,...t})});if(!s.ok){const e=await s.json().catch((()=>({detail:s.statusText})));throw new Error(e.detail||`Failed to bulk update document categories: ${s.statusText}`)}return await s.json()}catch(s){throw console.error("Error bulk updating document categories:",s),s}},v=async()=>{console.log("Fetching website categories from backend...");try{const e=await fetch(`${r}/api/categories/website-categories/`);if(!e.ok)throw console.error(`Failed to get website categories: ${e.status} ${e.statusText}`),new Error(`Failed to get website categories: ${e.statusText}`);return await e.json()}catch(e){return console.error("Error getting website categories:",e),[]}},N=async e=>{console.log("Creating website category:",e);try{const t=await fetch(`${r}/api/categories/website-categories/`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){const e=await t.json().catch((()=>({detail:t.statusText})));throw new Error(e.detail||`Failed to create website category: ${t.statusText}`)}return await t.json()}catch(t){throw console.error("Error creating website category:",t),t}},k=async(e,t)=>{console.log("Updating website categories:",e,t);try{const s=await fetch(`${r}/api/categories/websites/${e}/categories`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok){const e=await s.json().catch((()=>({detail:s.statusText})));throw new Error(e.detail||`Failed to update website categories: ${s.statusText}`)}return await s.json()}catch(s){throw console.error("Error updating website categories:",s),s}},C=async(e,t)=>{console.log("Bulk updating website categories:",e,t);try{const s=await fetch(`${r}/api/categories/websites/bulk-update-categories`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({website_ids:e,...t})});if(!s.ok){const e=await s.json().catch((()=>({detail:s.statusText})));throw new Error(e.detail||`Failed to bulk update website categories: ${s.statusText}`)}return await s.json()}catch(s){throw console.error("Error bulk updating website categories:",s),s}}},80:function(e,t,s){},82:function(e,t){},83:function(e,t){},84:function(e,t){},85:function(e,t){},86:function(e,t){},92:function(e,t,s){"use strict";s.r(t);var a=s(1),r=s.n(a),n=s(48),c=s.n(n),o=(s(67),s(22)),i=s(12),l=(s(68),s(8)),d=s(0);const u=[{id:"gemini-2.0-flash",name:"Gemini 2.0 Flash",logo:"\u26a1",provider:"Google",enabled:!0,tooltip:"Fast and accurate general-purpose AI model",features:{embedding:!0,chunking:!0,summarization:!0,scoring:!0}},{id:"gemini",name:"Google Gemini",logo:"\ud83e\udde0",provider:"Google",enabled:!0,tooltip:"Balanced performance across most tasks",features:{embedding:!0,chunking:!0,summarization:!0,scoring:!0}},{id:"chatgpt",name:"OpenAI ChatGPT",logo:"\ud83e\udd16",provider:"OpenAI",enabled:!0,tooltip:"Strong writing and creative tasks",features:{embedding:!0,chunking:!0,summarization:!0,scoring:!0}},{id:"groq",name:"Groq",logo:"\u26a1",provider:"Groq",enabled:!0,tooltip:"Ultra-fast response times",features:{embedding:!1,chunking:!1,summarization:!0,scoring:!1}},{id:"groq-llama3-70b",name:"Groq LLaMA3 70B",logo:"\u26a1",provider:"Groq",enabled:!0,tooltip:"Ultra-fast LLaMA3 70B model",features:{embedding:!1,chunking:!1,summarization:!0,scoring:!1}},{id:"groq-llama3-8b",name:"Groq LLaMA3 8B",logo:"\u26a1",provider:"Groq",enabled:!0,tooltip:"Ultra-fast LLaMA3 8B model",features:{embedding:!1,chunking:!1,summarization:!0,scoring:!1}},{id:"deepseek",name:"DeepSeek",logo:"\ud83d\udd0d",provider:"DeepSeek",enabled:!0,tooltip:"Good at technical and analytical tasks",features:{embedding:!1,chunking:!0,summarization:!0,scoring:!0}},{id:"deepseek-coder",name:"DeepSeek Coder",logo:"\ud83d\udd0d",provider:"DeepSeek",enabled:!0,tooltip:"Specialized in coding and technical tasks",features:{embedding:!1,chunking:!0,summarization:!0,scoring:!0}},{id:"qwen",name:"Qwen",logo:"\ud83c\udf10",provider:"Alibaba",enabled:!0,tooltip:"Multilingual capabilities",features:{embedding:!1,chunking:!1,summarization:!0,scoring:!1}},{id:"huggingface",name:"Hugging Face",logo:"\ud83e\udd17",provider:"Hugging Face",enabled:!0,tooltip:"Open-source models with custom endpoints",features:{embedding:!0,chunking:!1,summarization:!0,scoring:!1}},{id:"ollama",name:"Ollama",logo:"\ud83e\udd99",provider:"Local",enabled:!0,tooltip:"Run models locally for privacy",features:{embedding:!0,chunking:!1,summarization:!0,scoring:!1}}];var m=e=>{let{onModelChange:t,currentModel:s="gemini-2.0-flash",isLoading:n=!1}=e;const[c,o]=Object(a.useState)(!1),[i,l]=Object(a.useState)(u),[m,b]=Object(a.useState)(),g=r.a.useRef(null),x=r.a.useCallback((e=>i.find((t=>t.id===e))||i[0]),[i]);Object(a.useEffect)((()=>{const e=e=>{g.current&&!g.current.contains(e.target)&&o(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)}),[]),Object(a.useEffect)((()=>{const e=localStorage.getItem("railGptModels"),t=localStorage.getItem("railGptVisibleCount")||"5";if(e)try{const s=JSON.parse(e);l(s.filter((e=>e.enabled)).slice(0,parseInt(t,10)))}catch(s){console.error("Error parsing saved models:",s),l(u.filter((e=>e.enabled)).slice(0,5))}}),[]),Object(a.useEffect)((()=>{b(x(s))}),[s,x]);return Object(d.jsxs)("div",{className:"relative",ref:g,children:[Object(d.jsx)("button",{type:"button",onClick:()=>o(!c),className:`flex items-center justify-center h-10 w-10 rounded-full ${n?"bg-blue-100 animate-pulse":"bg-gray-100 hover:bg-gray-200"} focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors`,title:`${(null===m||void 0===m?void 0:m.name)||"Select LLM Model"}${null!==m&&void 0!==m&&m.tooltip?` - ${m.tooltip}`:""}`,"aria-label":(null===m||void 0===m?void 0:m.name)||"Select LLM Model",disabled:n,children:Object(d.jsx)("span",{className:"text-xl",role:"img","aria-label":(null===m||void 0===m?void 0:m.name)||"AI Model",children:n?"\u23f3":(null===m||void 0===m?void 0:m.logo)||"\ud83e\udd16"})}),c&&Object(d.jsx)("div",{className:"absolute bottom-full mb-2 right-0 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50 overflow-hidden",children:Object(d.jsxs)("div",{className:"py-1",role:"menu","aria-orientation":"vertical",children:[Object(d.jsx)("div",{className:"px-3 py-2 text-xs font-medium text-gray-500 border-b bg-gray-50",children:"Select AI Model"}),i.map((e=>Object(d.jsxs)("button",{onClick:()=>(e=>{b(e),t(e.id),o(!1)})(e),className:`w-full flex items-center px-4 py-2 text-left text-sm ${(null===m||void 0===m?void 0:m.id)===e.id?"bg-blue-50 text-blue-700":"text-gray-700 hover:bg-gray-100"} transition-colors`,role:"menuitem",title:e.tooltip,children:[Object(d.jsx)("span",{className:"mr-3 text-xl flex-shrink-0",children:e.logo}),Object(d.jsxs)("div",{className:"flex-1 overflow-hidden",children:[Object(d.jsx)("div",{className:"font-medium truncate",children:e.name}),Object(d.jsxs)("div",{className:"text-xs text-gray-500 flex items-center justify-between",children:[Object(d.jsx)("span",{children:e.provider}),(null===m||void 0===m?void 0:m.id)===e.id&&Object(d.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5 rounded-full ml-1",children:"Active"})]}),e.tooltip&&Object(d.jsx)("div",{className:"text-xs italic text-gray-400 mt-0.5 truncate",children:e.tooltip})]})]},e.id))),Object(d.jsx)("div",{className:"px-3 py-2 text-xs text-gray-400 border-t bg-gray-50",children:"More options in Settings"})]})})]})},b=s(131),g=s(57);const x=[{id:"inaccurate",label:"Inaccurate"},{id:"incomplete",label:"Incomplete"},{id:"offensive",label:"Offensive or Inappropriate"},{id:"too_slow",label:"Too Slow"},{id:"other",label:"Other"}];var j=e=>{let{isOpen:t,onClose:s,queryText:r,answerText:n,model:c,chatId:o}=e;const[i,u]=Object(a.useState)(""),[m,b]=Object(a.useState)(""),[g,j]=Object(a.useState)(!1),[h,p]=Object(a.useState)(null),[f,y]=Object(a.useState)("");if(!t)return null;return Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:Object(d.jsx)("div",{className:"bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto",children:Object(d.jsxs)("div",{className:"p-6",children:[Object(d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[Object(d.jsx)("h3",{className:"text-lg font-medium",children:"Provide Feedback"}),Object(d.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors","aria-label":"Close",children:"\u2715"})]}),null===h?Object(d.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),j(!0);try{const e={query:r,answer:n,issue_type:i,comment:m.trim()?m:void 0,model:c,chat_id:o,timestamp:(new Date).toISOString()},t=await Object(l.submitFeedback)(e);p(t.success),y(t.message||(t.success?"Thank you for your feedback!":"Failed to submit feedback.")),t.success&&setTimeout((()=>{s(),u(""),b(""),p(null),y("")}),2e3)}catch(t){p(!1),y(`Error submitting feedback: ${t instanceof Error?t.message:"Unknown error"}`)}finally{j(!1)}},children:[Object(d.jsxs)("div",{className:"mb-4",children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"What was the issue?"}),Object(d.jsxs)("select",{value:i,onChange:e=>u(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",required:!0,children:[Object(d.jsx)("option",{value:"",children:"Select an issue type"}),x.map((e=>Object(d.jsx)("option",{value:e.id,children:e.label},e.id)))]})]}),Object(d.jsxs)("div",{className:"mb-4",children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Additional comments (optional)"}),Object(d.jsx)("textarea",{value:m,onChange:e=>b(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md h-24",placeholder:"Please add any additional details that might help us improve..."})]}),Object(d.jsxs)("div",{className:"flex justify-end space-x-2",children:[Object(d.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors",disabled:g,children:"Cancel"}),Object(d.jsx)("button",{type:"submit",className:"px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50",disabled:g||!i,children:g?"Submitting...":"Submit Feedback"})]})]}):Object(d.jsx)("div",{className:"p-4 rounded-md "+(h?"bg-green-50 text-green-800":"bg-red-50 text-red-800"),children:Object(d.jsxs)("p",{className:"font-medium",children:[h?"\u2713":"\u2717"," ",f]})})]})})})};var h=e=>{let{content:t,query:s,model:r,chatId:n,enableMarkdown:c=!0}=e;const[o,i]=Object(a.useState)(!1),[l,u]=Object(a.useState)(null),m={h1:e=>{let{children:t}=e;return Object(d.jsx)("h1",{className:"text-xl font-bold mb-2 text-gray-800",children:t})},h2:e=>{let{children:t}=e;return Object(d.jsx)("h2",{className:"text-lg font-semibold mb-2 text-gray-800",children:t})},h3:e=>{let{children:t}=e;return Object(d.jsx)("h3",{className:"text-md font-semibold mb-1 text-gray-800",children:t})},p:e=>{let{children:t}=e;return Object(d.jsx)("p",{className:"mb-2 text-gray-700 leading-relaxed",children:t})},ul:e=>{let{children:t}=e;return Object(d.jsx)("ul",{className:"list-disc list-inside mb-2 ml-2 text-gray-700",children:t})},ol:e=>{let{children:t}=e;return Object(d.jsx)("ol",{className:"list-decimal list-inside mb-2 ml-2 text-gray-700",children:t})},li:e=>{let{children:t}=e;return Object(d.jsx)("li",{className:"mb-1",children:t})},strong:e=>{let{children:t}=e;return Object(d.jsx)("strong",{className:"font-semibold text-gray-800",children:t})},em:e=>{let{children:t}=e;return Object(d.jsx)("em",{className:"italic text-gray-700",children:t})},code:e=>{let{children:t}=e;return Object(d.jsx)("code",{className:"bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-800",children:t})},pre:e=>{let{children:t}=e;return Object(d.jsx)("pre",{className:"bg-gray-100 p-3 rounded-lg overflow-x-auto mb-2",children:Object(d.jsx)("code",{className:"text-sm font-mono text-gray-800",children:t})})},blockquote:e=>{let{children:t}=e;return Object(d.jsx)("blockquote",{className:"border-l-4 border-blue-300 pl-3 italic text-gray-600 mb-2",children:t})},table:e=>{let{children:t}=e;return Object(d.jsx)("table",{className:"min-w-full border-collapse border border-gray-300 mb-2",children:t})},th:e=>{let{children:t}=e;return Object(d.jsx)("th",{className:"border border-gray-300 bg-gray-100 px-2 py-1 text-left font-semibold",children:t})},td:e=>{let{children:t}=e;return Object(d.jsx)("td",{className:"border border-gray-300 px-2 py-1",children:t})}};return Object(d.jsxs)("div",{className:"relative",children:[Object(d.jsx)("div",{className:"prose max-w-none",children:c?Object(d.jsx)(b.a,{remarkPlugins:[g.a],components:m,children:t}):Object(d.jsx)("div",{className:"whitespace-pre-wrap text-gray-700 leading-relaxed",children:t})}),Object(d.jsxs)("div",{className:"mt-3 flex justify-between items-center border-t border-gray-100 pt-2 text-gray-500",children:[Object(d.jsx)("div",{className:"flex items-center space-x-2",children:Object(d.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(t)},className:"flex items-center p-1 px-2 text-xs hover:bg-gray-100 rounded",title:"Copy answer to clipboard",children:[Object(d.jsx)("span",{className:"mr-1",children:"\ud83d\udcc4"})," Copy"]})}),Object(d.jsxs)("div",{className:"flex items-center space-x-2",children:[Object(d.jsxs)("button",{onClick:()=>{u("positive")},className:"flex items-center p-1 px-2 text-xs hover:bg-gray-100 rounded "+("positive"===l?"bg-green-50 text-green-700":""),title:"This answer was helpful",disabled:null!==l,children:[Object(d.jsx)("span",{className:"mr-1",children:"\ud83d\udc4d"})," ","positive"===l?"Thank you!":"Helpful"]}),Object(d.jsxs)("button",{onClick:()=>{u("negative"),i(!0)},className:"flex items-center p-1 px-2 text-xs hover:bg-gray-100 rounded "+("negative"===l?"bg-gray-100":""),title:"This answer needs improvement",disabled:null!==l,children:[Object(d.jsx)("span",{className:"mr-1",children:"\ud83d\udc4e"})," ","negative"===l?"Feedback sent":"Not helpful"]})]})]}),Object(d.jsx)(j,{isOpen:o,onClose:()=>i(!1),queryText:s,answerText:t,model:r,chatId:n})]})},p=s(16);var f=e=>{let{isOpen:t,onToggle:s,currentChatId:r,onChatSelect:n,onNewChat:c}=e;const[o,i]=Object(a.useState)([]),[l,u]=Object(a.useState)(!0),[m,b]=Object(a.useState)(""),[g,x]=Object(a.useState)(null),[j,h]=Object(a.useState)(""),[f,y]=Object(a.useState)(null),O=Object(a.useRef)(null);Object(a.useEffect)((()=>{w();const e=e=>{const t=e.detail;i((e=>[t,...e]))},t=e=>{const t=e.detail;i((e=>e.map((e=>e.id===t.id?{...e,...t}:e))))};return window.addEventListener("chatCreated",e),window.addEventListener("chatUpdated",t),()=>{window.removeEventListener("chatCreated",e),window.removeEventListener("chatUpdated",t)}}),[]),Object(a.useEffect)((()=>{g&&O.current&&(O.current.focus(),O.current.select())}),[g]);const w=async()=>{try{u(!0);const e=await Object(p.e)();i(e||[])}catch(e){console.error("Error loading chat sessions:",e),i([])}finally{u(!1)}},v=async()=>{if(g&&j.trim())try{await Object(p.i)(g,j.trim()),i((e=>e.map((e=>e.id===g?{...e,title:j.trim()}:e))))}catch(e){console.error("Error renaming chat:",e)}finally{N()}else N()},N=()=>{x(null),h("")},k=(e=>{const t={};e.forEach((e=>{const s=(e=>{const t=new Date(e),s=new Date,a=new Date(s.getFullYear(),s.getMonth(),s.getDate()),r=new Date(a.getTime()-864e5),n=new Date(t.getFullYear(),t.getMonth(),t.getDate());return n.getTime()===a.getTime()?"Today":n.getTime()===r.getTime()?"Yesterday":n>=new Date(s.getFullYear(),s.getMonth(),s.getDate()-7)?"Last 7 days":n>=new Date(s.getFullYear(),s.getMonth(),1)?"This month":t.toLocaleDateString("en-US",{year:"numeric",month:"long"})})(e.updated_at);t[s]||(t[s]=[]),t[s].push(e)}));const s=[];["Today","Yesterday","Last 7 days","This month"].forEach((e=>{t[e]&&(s.push({label:e,chats:t[e]}),delete t[e])}));return Object.entries(t).sort(((e,t)=>{let[s]=e,[a]=t;const r=new Date(s+" 1, 2024"),n=new Date(a+" 1, 2024");if(!isNaN(r.getTime())&&!isNaN(n.getTime()))return n.getTime()-r.getTime();const c=parseInt(s),o=parseInt(a);return isNaN(c)||isNaN(o)?a.localeCompare(s):o-c})).forEach((e=>{let[t,a]=e;s.push({label:t,chats:a})})),s})(o.filter((e=>e.title.toLowerCase().includes(m.toLowerCase()))));return t?Object(d.jsxs)(d.Fragment,{children:[Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-35 lg:hidden",onClick:s}),Object(d.jsxs)("div",{className:"fixed left-0 top-0 h-full w-80 bg-white border-r border-gray-200 shadow-lg z-40 flex flex-col transition-colors duration-300",children:[Object(d.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[Object(d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[Object(d.jsx)("h2",{className:"text-lg font-semibold text-gray-800",children:"Chat History"}),Object(d.jsx)("button",{onClick:s,className:"p-1 text-gray-500 hover:text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded",title:"Close sidebar",children:"\u2715"})]}),Object(d.jsx)("button",{onClick:async()=>{await c(),b("")},className:"w-full flex items-center justify-center gap-2 p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",children:"\u2795 New Chat"}),Object(d.jsx)("div",{className:"mt-3",children:Object(d.jsx)("input",{type:"text",placeholder:"Search chats...",value:m,onChange:e=>b(e.target.value),className:"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-colors duration-300"})})]}),Object(d.jsx)("div",{className:"flex-1 overflow-y-auto",children:l?Object(d.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Loading chats..."}):0===k.length?Object(d.jsx)("div",{className:"p-4 text-center text-gray-500",children:m?"No chats found":"No chat history yet"}):k.map((e=>Object(d.jsxs)("div",{className:"mb-4",children:[Object(d.jsx)("h3",{className:"px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide",children:e.label}),Object(d.jsx)("div",{className:"space-y-1",children:e.chats.map((e=>{return Object(d.jsx)("div",{className:"mx-2 p-3 rounded-lg cursor-pointer transition-all duration-200 group "+(r===e.id?"bg-blue-100 border border-blue-200":"hover:bg-gray-50"),onClick:()=>!g&&n(e),onMouseEnter:()=>y(e.id),onMouseLeave:()=>y(null),children:Object(d.jsxs)("div",{className:"flex items-start justify-between",children:[Object(d.jsxs)("div",{className:"flex-1 min-w-0",children:[Object(d.jsx)("div",{className:"flex items-center gap-2 mb-1",children:Object(d.jsx)("span",{className:"text-xs",children:(t=e.model_used,null!==t&&void 0!==t&&t.includes("gemini")?"\ud83e\udde0":null!==t&&void 0!==t&&t.includes("chatgpt")?"\ud83e\udd16":null!==t&&void 0!==t&&t.includes("groq")?"\u26a1":null!==t&&void 0!==t&&t.includes("deepseek")?"\ud83d\udd0d":null!==t&&void 0!==t&&t.includes("qwen")?"\ud83c\udf10":null!==t&&void 0!==t&&t.includes("ollama")?"\ud83c\udfe0":null!==t&&void 0!==t&&t.includes("huggingface")?"\ud83e\udd17":"\ud83e\udde0")})}),g===e.id?Object(d.jsx)("input",{ref:O,type:"text",value:j,onChange:e=>h(e.target.value),onKeyDown:e=>{"Enter"===e.key&&v(),"Escape"===e.key&&N()},onBlur:v,className:"w-full p-1 text-sm border border-blue-300 bg-white text-gray-900 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300"}):Object(d.jsx)("h4",{className:"text-sm font-medium text-gray-800 truncate",children:e.title}),Object(d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:new Date(e.updated_at).toLocaleDateString()})]}),f===e.id&&!g&&Object(d.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[Object(d.jsx)("button",{onClick:t=>{var s,a;t.stopPropagation(),s=e.id,a=e.title,x(s),h(a)},className:"p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded",title:"Rename chat",children:"\ud83d\udcdd"}),Object(d.jsx)("button",{onClick:t=>{t.stopPropagation(),(async e=>{if(window.confirm("Are you sure you want to delete this chat?"))try{await Object(p.c)(e),i((t=>t.filter((t=>t.id!==e)))),e===r&&await c()}catch(t){console.error("Error deleting chat:",t)}})(e.id)},className:"p-1 text-gray-400 hover:text-red-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 rounded",title:"Delete chat",children:"\ud83d\uddd1\ufe0f"})]})]})},e.id);var t}))})]},e.label)))}),Object(d.jsx)("div",{className:"p-4 border-t border-gray-200",children:Object(d.jsx)("button",{onClick:async()=>{if(window.confirm("Are you sure you want to clear all chat history? This action cannot be undone."))try{await Object(p.a)(),i([]),await c()}catch(e){console.error("Error clearing chats:",e)}},className:"w-full p-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50",children:"Clear All Chats"})})]})]}):null};s(80);var y=e=>{let{isVisible:t,message:s="RailGPT Generating Answer...",trainType:r="express",currentStage:n="initializing",onComplete:c,sidebarOpen:o=!1}=e;if(Object(a.useEffect)((()=>{if(t){const e=document.getElementById("train-audio");e&&e.play().catch((()=>{console.log("Audio autoplay blocked - user interaction required")}))}else{const e=document.getElementById("train-audio");e&&(e.pause(),e.currentTime=0)}return()=>{const e=document.getElementById("train-audio");e&&(e.pause(),e.currentTime=0)}}),[t]),!t)return null;const i=(()=>{switch(r){case"rajdhani":return{coach:"#8B0000",engine:"#FFD700"};case"shatabdi":return{coach:"#0066CC",engine:"#FFFFFF"};default:return{coach:"#DC143C",engine:"#1E3A8A"}}})();return Object(d.jsxs)(d.Fragment,{children:[Object(d.jsxs)("audio",{id:"train-audio",loop:!0,preload:"auto",style:{display:"none"},children:[Object(d.jsx)("source",{src:"/sounds/train-sound.mp3",type:"audio/mpeg"}),Object(d.jsx)("source",{src:"https://www.soundjay.com/misc/sounds/train-whistle-1.mp3",type:"audio/mpeg"})]}),Object(d.jsx)("div",{className:"train-text-trail "+(o?"sidebar-open":""),children:s}),Object(d.jsxs)("div",{className:"train-container-direct "+(o?"sidebar-open":""),children:[Object(d.jsxs)("div",{className:"train-engine",style:{backgroundColor:i.engine},children:[Object(d.jsx)("div",{className:"engine-front"}),Object(d.jsxs)("div",{className:"engine-chimney",children:[Object(d.jsx)("div",{className:"smoke smoke-1"}),Object(d.jsx)("div",{className:"smoke smoke-2"}),Object(d.jsx)("div",{className:"smoke smoke-3"}),Object(d.jsx)("div",{className:"smoke smoke-4"})]}),Object(d.jsxs)("div",{className:"engine-details",children:[Object(d.jsx)("div",{className:"engine-window"}),Object(d.jsx)("div",{className:"engine-light"})]}),Object(d.jsxs)("div",{className:"train-wheels",children:[Object(d.jsxs)("div",{className:"wheel wheel-1",children:[Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"})]}),Object(d.jsxs)("div",{className:"wheel wheel-2",children:[Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"})]})]})]}),Object(d.jsxs)("div",{className:"train-coach",style:{backgroundColor:i.coach},children:[Object(d.jsx)("div",{className:"ir-logo",children:Object(d.jsx)("div",{className:"ir-circle",children:Object(d.jsx)("span",{className:"ir-text",children:"IR"})})}),Object(d.jsxs)("div",{className:"coach-windows",children:[Object(d.jsx)("div",{className:"window"}),Object(d.jsx)("div",{className:"window"}),Object(d.jsx)("div",{className:"window"}),Object(d.jsx)("div",{className:"window"})]}),Object(d.jsxs)("div",{className:"coach-details",children:[Object(d.jsx)("div",{className:"coach-door"}),Object(d.jsx)("div",{className:"coach-number",children:"12345"})]}),Object(d.jsxs)("div",{className:"train-wheels",children:[Object(d.jsxs)("div",{className:"wheel wheel-3",children:[Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"})]}),Object(d.jsxs)("div",{className:"wheel wheel-4",children:[Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"})]})]})]}),Object(d.jsxs)("div",{className:"train-wagon",children:[Object(d.jsx)("div",{className:"wagon-logo",children:Object(d.jsx)("div",{className:"ir-circle small",children:Object(d.jsx)("span",{className:"ir-text small",children:"IR"})})}),Object(d.jsx)("div",{className:"wagon-details",children:Object(d.jsxs)("div",{className:"cargo-lines",children:[Object(d.jsx)("div",{className:"cargo-line"}),Object(d.jsx)("div",{className:"cargo-line"}),Object(d.jsx)("div",{className:"cargo-line"})]})}),Object(d.jsxs)("div",{className:"train-wheels",children:[Object(d.jsxs)("div",{className:"wheel wheel-5",children:[Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"})]}),Object(d.jsxs)("div",{className:"wheel wheel-6",children:[Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"}),Object(d.jsx)("div",{className:"wheel-spoke"})]})]})]})]})]})};const O=e=>{if(!e||"string"!==typeof e)return'<div class="p-4 text-gray-600">Invalid table data</div>';const t=e.split("\n").filter((e=>e.trim()));if(t.length<2)return'<div class="p-4 text-gray-600">Invalid table format</div>';let s='<table class="min-w-full border border-gray-200 rounded-lg">';try{const e=t[0],a=e.split("|").map((e=>e.trim())).filter((e=>""!==e));if(0===a.length){const a=e.split(/\s{2,}/).filter((e=>e.trim()));if(a.length>0){s+='<thead class="bg-gray-50"><tr>',a.forEach(((e,t)=>{const a=e.replace(/\*\*/g,"").trim();s+=`<th class="px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">${a||`Column ${t+1}`}</th>`})),s+="</tr></thead>",s+="<tbody>";for(let e=2;e<t.length;e++){const r=t[e].split(/\s{2,}/).filter((e=>e.trim()));if(0!==r.length){for(s+='<tr class="hover:bg-gray-50 transition-colors">';r.length<a.length;)r.push("");a.forEach(((e,t)=>{const a=r[t]||"";s+=`<td class="px-3 py-2 text-sm text-gray-700 border-b border-gray-100">${a.trim()||"-"}</td>`})),s+="</tr>"}}return s+="</tbody></table>",s}return'<div class="p-4 text-gray-600">No table headers found</div>'}s+='<thead class="bg-gray-50"><tr>',a.forEach(((e,t)=>{const a=e.replace(/\*\*/g,"").trim();s+=`<th class="px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">${a||`Column ${t+1}`}</th>`})),s+="</tr></thead>";const r=t.length>1&&t[1].includes("|")&&t[1].includes("-");s+="<tbody>";for(let n=r?2:1;n<t.length;n++){const e=t[n];if(!e.includes("|"))continue;const r=e.split("|").map((e=>e.trim())).filter(((e,t,s)=>0!==t&&t!==s.length-1||""!==e));for(;r.length<a.length;)r.push("");s+='<tr class="hover:bg-gray-50 transition-colors">',a.forEach(((e,t)=>{const a=(r[t]||"").replace(/\*\*/g,"").trim();s+=`<td class="px-3 py-2 text-sm text-gray-700 border-b border-gray-100">${a||"-"}</td>`})),s+="</tr>"}s+="</tbody></table>"}catch(a){console.error("Error parsing markdown table:",a);return`<div class="p-4 text-gray-600 bg-red-50 border border-red-200 rounded">Error parsing table: ${a instanceof Error?a.message:"Unknown error"}</div>`}return s};var w=e=>{var t;let{source:s}=e;const[r,n]=Object(a.useState)("content"),[c,o]=Object(a.useState)(!0),[i,l]=Object(a.useState)(!1),[u,m]=Object(a.useState)(0),[b,g]=Object(a.useState)(`cb-${Date.now()}`),[x,j]=Object(a.useState)(!1);if(Object(a.useEffect)((()=>{o(!0),l(!1),m(0),g(`cb-${Date.now()}-${Math.random().toString(36).substring(2,9)}`),console.log("\ud83d\udd04 Visual content source changed:",{content_type:s.content_type,filename:s.filename,page:s.page,has_visual_content:!!s.visual_content,storage_url:s.storage_url?`${s.storage_url.substring(0,30)}...`:"None"})}),[s.filename,s.page,s.storage_url]),!s.content_type||"text"===s.content_type)return null;return Object(d.jsxs)("div",{className:`bg-gradient-to-br ${(()=>{switch(s.content_type){case"table":return"from-green-50 to-emerald-50 border-green-200";case"image":return"from-blue-50 to-cyan-50 border-blue-200";case"chart_diagram":return"from-purple-50 to-indigo-50 border-purple-200";default:return"from-gray-50 to-slate-50 border-gray-200"}})()} rounded-lg border p-4 mt-3`,children:[Object(d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[Object(d.jsxs)("h4",{className:"font-semibold text-gray-800 text-sm flex items-center",children:[(()=>{switch(s.content_type){case"table":return"\ud83d\udccb Table";case"image":return"\ud83d\uddbc\ufe0f Image";case"chart_diagram":return"\ud83d\udcca Chart/Diagram";default:return"\ud83d\udcc4 Content"}})(),s.page&&Object(d.jsxs)("span",{className:"ml-2 text-xs bg-white bg-opacity-70 px-2 py-1 rounded",children:["Page ",s.page]})]}),"table"===s.content_type&&(null===(t=s.visual_content)||void 0===t?void 0:t.metadata)&&Object(d.jsxs)("div",{className:"flex text-xs bg-white bg-opacity-50 rounded",children:[Object(d.jsx)("button",{onClick:()=>n("content"),className:"px-2 py-1 rounded-l "+("content"===r?"bg-white text-gray-800":"text-gray-600"),children:"Table"}),Object(d.jsx)("button",{onClick:()=>n("metadata"),className:"px-2 py-1 rounded-r "+("metadata"===r?"bg-white text-gray-800":"text-gray-600"),children:"Info"})]})]}),"content"===r&&Object(d.jsxs)("div",{children:["table"===s.content_type&&(()=>{const e=s.visual_content;if(!e)return null;if(e.table_data&&Array.isArray(e.table_data)){const t=e.table_data;if(0===t.length)return null;const s=t[0]||[],a=t.slice(1);return Object(d.jsx)("div",{className:"overflow-x-auto w-full",children:Object(d.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg table-fixed",children:[Object(d.jsx)("thead",{className:"bg-gray-50",children:Object(d.jsx)("tr",{children:s.map(((e,t)=>Object(d.jsx)("th",{className:"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200",style:{minWidth:"100px",maxWidth:"200px"},children:String(e||"").trim()||`Column ${t+1}`},t)))})}),Object(d.jsx)("tbody",{children:a.map(((e,t)=>Object(d.jsx)("tr",{className:"hover:bg-gray-50 transition-colors",children:s.map(((t,s)=>Object(d.jsx)("td",{className:"px-3 py-2 text-sm text-gray-700 border-b border-gray-100 truncate",title:String(e[s]||""),children:String(e[s]||"").trim()||"-"},s)))},t)))})]})})}return e.markdown_table?Object(d.jsx)("div",{className:"overflow-x-auto w-full",children:Object(d.jsx)("div",{className:"bg-white rounded-lg border border-gray-200",children:Object(d.jsx)("div",{className:"prose prose-sm max-w-none p-0",dangerouslySetInnerHTML:{__html:O(e.markdown_table)}})})}):e.text_table?Object(d.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:Object(d.jsx)("pre",{className:"text-sm text-gray-700 whitespace-pre-wrap font-mono",children:e.text_table})}):Object(d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-sm text-gray-600",children:["Table data is not available in a displayable format.",e&&Object(d.jsxs)("div",{className:"mt-2 text-xs",children:["Available data: ",Object.keys(e).join(", ")]})]})})(),"image"===s.content_type&&(()=>{const e=s.visual_content;console.log("DEBUG: Rendering image for",s.filename,"page",s.page),console.log("DEBUG: storage_url =",s.storage_url),console.log("DEBUG: base64_data available =",!(null===e||void 0===e||!e.base64_data));const t=Object(d.jsxs)("div",{className:"bg-blue-50 p-2 mb-2 rounded text-xs",children:[Object(d.jsxs)("p",{children:[Object(d.jsx)("strong",{children:"File:"})," ",s.filename||"Unknown"]}),Object(d.jsxs)("p",{children:[Object(d.jsx)("strong",{children:"Page:"})," ",s.page||"Unknown"]}),Object(d.jsxs)("p",{children:[Object(d.jsx)("strong",{children:"URL:"})," ",s.storage_url?"Available":"Not available"]}),Object(d.jsxs)("p",{children:[Object(d.jsx)("strong",{children:"Time:"})," ",(new Date).toISOString()]})]});return"image"===s.content_type&&s.filename&&s.filename.includes("Project")?Object(d.jsxs)("div",{className:"relative",children:[t,c&&Object(d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg",children:Object(d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}),Object(d.jsx)("img",{src:s.storage_url||`/logo512.png?t=${Date.now()}`,alt:`Image from page ${s.page} of ${s.filename}`,className:"max-w-full h-auto rounded-lg border border-gray-200 shadow-md",onLoad:()=>{console.log("DEBUG: Image loaded successfully"),o(!1)},onError:e=>{console.error("DEBUG: Image failed to load:",e),o(!1),l(!0)}}),i&&Object(d.jsxs)("div",{className:"bg-red-50 rounded-lg p-4 text-center text-gray-600 mt-2",children:[Object(d.jsx)("p",{children:"Image could not be loaded"}),Object(d.jsx)("p",{className:"text-xs mt-1",children:"Please check the document and try again"}),Object(d.jsx)("button",{onClick:()=>{console.log("DEBUG: Retrying image load"),o(!0),l(!1)},className:"mt-2 px-2 py-1 bg-blue-100 rounded text-sm",children:"Retry"})]})]}):s.storage_url?Object(d.jsxs)("div",{className:"relative",children:[t,c&&Object(d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg",children:Object(d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}),Object(d.jsx)("img",{src:`${s.storage_url}?t=${Date.now()}`,alt:`Image from page ${s.page} of ${s.filename}`,className:"max-w-full h-auto rounded-lg border border-gray-200 "+(c?"opacity-0":"opacity-100"),onLoad:()=>o(!1),onError:()=>{console.error("DEBUG: Storage URL image failed to load:",s.storage_url),o(!1),l(!0)}}),i&&Object(d.jsxs)("div",{className:"bg-gray-100 rounded-lg p-4 text-center text-gray-600",children:[Object(d.jsx)("p",{children:"Image could not be loaded"}),Object(d.jsxs)("p",{className:"text-xs mt-1",children:["URL: ",null===(a=s.storage_url)||void 0===a?void 0:a.substring(0,30),"..."]}),Object(d.jsx)("button",{onClick:()=>{console.log("DEBUG: Retrying image load"),o(!0),l(!1)},className:"mt-2 px-2 py-1 bg-blue-100 rounded text-sm",children:"Retry"})]})]}):null!==e&&void 0!==e&&e.base64_data?Object(d.jsxs)("div",{children:[t,Object(d.jsx)("img",{src:`data:image/png;base64,${e.base64_data}`,alt:`Image from page ${s.page} of ${s.filename}`,className:"max-w-full h-auto rounded-lg border border-gray-200",onError:()=>{console.error("DEBUG: Base64 image failed to load"),l(!0)}})]}):Object(d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[t,Object(d.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"No image content available from the document"}),Object(d.jsx)("img",{src:`/logo512.png?t=${Date.now()}`,alt:"Default logo",className:"max-w-full h-auto rounded-lg border border-gray-200 mx-auto",style:{maxHeight:"200px"}})]});var a})(),"chart_diagram"===s.content_type&&(()=>{const e=s.visual_content;return e?Object(d.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200",children:[Object(d.jsx)("h4",{className:"font-semibold text-purple-800 mb-3 flex items-center",children:"\ud83d\udcca Chart/Diagram Detected"}),Object(d.jsxs)("div",{className:"space-y-3",children:[e.description&&Object(d.jsx)("p",{className:"text-sm text-gray-700",children:e.description}),Object(d.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs text-gray-600",children:[e.drawing_count&&Object(d.jsxs)("div",{children:[Object(d.jsx)("span",{className:"font-medium",children:"Drawing Elements:"})," ",e.drawing_count]}),e.confidence&&Object(d.jsxs)("div",{children:[Object(d.jsx)("span",{className:"font-medium",children:"Confidence:"})," ",e.confidence]}),e.has_lines&&Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsx)("span",{className:"w-2 h-2 bg-green-400 rounded-full mr-2"}),"Contains lines"]}),e.has_curves&&Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsx)("span",{className:"w-2 h-2 bg-blue-400 rounded-full mr-2"}),"Contains curves"]})]})]})]}):null})()]}),"metadata"===r&&s.visual_content&&Object(d.jsx)("div",{className:"bg-white bg-opacity-50 rounded p-3 text-xs text-gray-600",children:Object(d.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[Object(d.jsxs)("div",{children:[Object(d.jsx)("span",{className:"font-medium",children:"Extraction Method:"})," ",s.visual_content.extraction_method]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("span",{className:"font-medium",children:"Page:"})," ",s.page]}),void 0!==s.visual_content.table_index&&Object(d.jsxs)("div",{children:[Object(d.jsx)("span",{className:"font-medium",children:"Table Index:"})," ",s.visual_content.table_index]}),void 0!==s.visual_content.image_index&&Object(d.jsxs)("div",{children:[Object(d.jsx)("span",{className:"font-medium",children:"Image Index:"})," ",s.visual_content.image_index]})]})})]})};const v=Object(a.createContext)(void 0),N=e=>{let{children:t}=e;const[s,r]=Object(a.useState)(null),[n,c]=Object(a.useState)([]),[o,i]=Object(a.useState)(!1),[l,u]=Object(a.useState)(!0),m=Object(a.useCallback)((async()=>{if(s&&0!==n.length)try{await Object(p.f)(s.id,n),console.log("Chat saved successfully")}catch(e){console.error("Error saving chat:",e)}}),[s,n]);Object(a.useEffect)((()=>{if(l&&s&&n.length>0){const e=setTimeout((()=>{m()}),2e3);return()=>clearTimeout(e)}}),[n,s,l,m]);const b={currentSession:s,messages:n,isLoading:o,createNewChat:async()=>{i(!0);try{const e=await Object(p.b)("New Chat","gemini-2.0-flash");if(e){r(e),c([]),console.log("New chat session created:",e.id);const t=new CustomEvent("chatCreated",{detail:e});window.dispatchEvent(t)}}catch(e){console.error("Error creating new chat:",e)}finally{i(!1)}},loadChatSession:async e=>{if((null===s||void 0===s?void 0:s.id)!==e){i(!0);try{const t=await Object(p.d)(e);t&&(r(t),c(t.messages||[]))}catch(t){console.error("Error loading chat session:",t)}finally{i(!1)}}},addMessage:e=>{c((t=>{const a=[...t,e];if("user"===e.sender&&s&&"New Chat"===s.title&&0===t.length){const t=e.content.substring(0,50)+(e.content.length>50?"...":"");r((e=>e?{...e,title:t}:null)),Object(p.i)(s.id,t).then((()=>{const e=new CustomEvent("chatUpdated",{detail:{...s,title:t}});window.dispatchEvent(e)}))}return a}))},updateMessage:(e,t)=>{c((s=>-1===s.findIndex((t=>t.id===e))?(console.warn(`Message with id ${e} not found for update`),s):s.map((s=>s.id===e?{...s,...t}:s))))},saveCurrentChat:m,clearCurrentChat:()=>{r(null),c([])},enableAutoSave:l,setEnableAutoSave:u};return Object(d.jsx)(v.Provider,{value:b,children:t})};function k(e){var t;let{sidebarOpen:s,setSidebarOpen:r}=e;const{currentSession:n,messages:c,createNewChat:o,loadChatSession:i,addMessage:b,updateMessage:g,clearCurrentChat:x}=(()=>{const e=Object(a.useContext)(v);if(!e)throw new Error("useChatContext must be used within a ChatProvider");return e})(),[j,p]=Object(a.useState)(""),[O,N]=Object(a.useState)(!1),[k,C]=Object(a.useState)("gemini-2.0-flash"),[S,_]=Object(a.useState)(!1),[E,D]=Object(a.useState)("initializing"),T=Object(a.useRef)(null);Object(a.useEffect)((()=>{T.current&&T.current.scrollIntoView({behavior:"smooth"})}),[c]);const $=(e,t)=>{console.log("\ud83d\udd0d DEBUG shouldShowVisualContent called with:",{userQuestion:e,documentAnswerLength:null===t||void 0===t?void 0:t.length});const s=e.toLowerCase();if(s.includes("image"))return console.log('\ud83d\udd0d DEBUG: ALWAYS showing visual content because query contains "image"'),!0;const a=s.includes("image")||s.includes("picture")||s.includes("diagram")||s.includes("chart"),r=(s.includes("table")||s.includes("quotation"))&&(s.includes("give me")||s.includes("show me table")||s.includes("get")||s.includes("provide")),n=s.includes("show me image")||s.includes("display image")||s.includes("see image"),c=(null===t||void 0===t?void 0:t.toLowerCase())||"",o=c.includes("<table>")||c.includes("|")||c.includes("table")&&c.length>200,i=c.includes("image")||c.includes("figure")||c.includes("diagram"),l=c.length>50;if(n||a&&(s.includes("show")||s.includes("display")))return console.log("\ud83d\udd0d DEBUG: Visual content shown because user explicitly asked for images"),!0;if(r&&!a)return console.log("\ud83d\udd0d DEBUG: Visual content hidden because user asked for table data"),!1;const d=!o&&!i&&(a&&!l||!l&&c.length<50);return console.log("\ud83d\udd0d DEBUG: Visual content decision for regular query:",d),d},F=(e,t)=>{if("object"!==typeof e||!e.visual_content)return console.log("\ud83d\udd0d DEBUG: Filtering out - not object or no visual_content:",typeof e,!(null===e||void 0===e||!e.visual_content)),!1;const s=t.toLowerCase(),a=e;console.log("\ud83d\udd0d DEBUG: Checking visual content filter for:",{content_type:a.content_type,has_visual_content:!!a.visual_content,user_query:s,filename:a.filename,page:a.page,visual_content_sample:a.visual_content?JSON.stringify(a.visual_content).substring(0,200):"none"});const r=s.match(/project\s*(\d+)/),n=s.match(/quotation\s*(\d+)/),c=[/(?:logo\s+(?:of\s+)?|show\s+me\s+(?:the\s+)?logo\s+(?:of\s+)?)([A-Z][A-Za-z\s&]+?)(?:\s+logo|\s+enterprises|\s+company|\s+corp|\s+ltd|\s+inc|\s*$)/i,/([A-Z][A-Za-z\s&]+?)\s+(?:enterprises|company|corp|ltd|inc)\s+logo/i,/([A-Z][A-Za-z\s&]+?)\s+logo/i];let o=null;for(const p of c){const e=t.match(p);if(e){o=e[1].trim();break}}const i=JSON.stringify(a.visual_content||{}).toLowerCase();if(o&&s.includes("logo")){var l,d,u,m;const e=o.toLowerCase(),t="image"===a.content_type;if(console.log("\ud83d\udd0d DEBUG: Company logo search:",{company:o,isImage:t,has_detected_companies:!(null===(l=a.visual_content)||void 0===l||!l.detected_companies),detected_companies:(null===(d=a.visual_content)||void 0===d?void 0:d.detected_companies)||[],ocr_text:(null===(u=a.visual_content)||void 0===u?void 0:u.ocr_text)||"",is_logo:(null===(m=a.visual_content)||void 0===m?void 0:m.is_logo)||!1}),t){var b,g,x;const s=i.includes(e),r=a.filename&&a.filename.toLowerCase().includes(e),n=((null===(b=a.visual_content)||void 0===b?void 0:b.detected_companies)||[]).some((t=>t.toLowerCase().includes(e)||e.includes(t.toLowerCase()))),c=((null===(g=a.visual_content)||void 0===g?void 0:g.ocr_text)||"").toLowerCase().includes(e),l=(null===(x=a.visual_content)||void 0===x?void 0:x.is_logo)||!1,d=s||r||n||c||l;return console.log("\ud83d\udd0d DEBUG: Company logo filter result:",{company:o,hasCompanyInContent:s,hasCompanyInFilename:r,hasDetectedCompany:n,hasOcrMatch:c,isLogo:l,isRelevantLogo:d,should_show:t&&d}),d}return!1}if(r&&s.includes("image")){var j,h;const e=r[1],t="image"===a.content_type,s=i.includes(`project ${e}`)||i.includes(`project${e}`),n=a.filename&&a.filename.toLowerCase().includes(`project ${e}`),c=a.page&&Math.abs(a.page-parseInt(e))<=1,o=(null===(j=a.visual_content)||void 0===j?void 0:j.project_context)&&a.visual_content.project_context.includes(`project ${e}`),l=s||n||c||o;return console.log("\ud83d\udd0d DEBUG: Project",e,"image filter:",{isImage:t,hasProjectInContent:s,hasProjectInFilename:n,isOnProjectPage:c,hasProjectContext:o,isRelevantProject:l,visualContentStr_sample:i.substring(0,150),project_context:null===(h=a.visual_content)||void 0===h?void 0:h.project_context,page:a.page,should_show:t&&l}),t&&l}if(n&&s.includes("table")){const e=n[1],t="table"===a.content_type,s=i.includes(`quotation ${e}`)||i.includes(`quotation${e}`)||i.includes(`quote ${e}`);return console.log("\ud83d\udd0d DEBUG: Quotation",e,"table filter:",{isTable:t,isRelevantQuotation:s,should_show:t&&s}),t&&s}if(s.includes("image")&&!s.includes("table")){const e="image"===a.content_type;return console.log("\ud83d\udd0d DEBUG: User asked for images only, source is image:",e),e}if(s.includes("table")&&!s.includes("image")){const e="table"===a.content_type;return console.log("\ud83d\udd0d DEBUG: User asked for tables only, source is table:",e),e}return console.log("\ud83d\udd0d DEBUG: Showing all visual content by default"),!0},I=async e=>{var t;n||await o();const s={id:`user-${Date.now()}`,content:e,sender:"user",timestamp:(new Date).toISOString(),chatId:(null===n||void 0===n?void 0:n.id)||"temp"};b(s),p("");const a=Date.now(),r={id:`ai-${a}`,content:"",sender:"ai",loading:!0,timestamp:(new Date).toISOString(),llm_model:k,chatId:(null===n||void 0===n?void 0:n.id)||"temp"};b(r),null===(t=T.current)||void 0===t||t.scrollIntoView({behavior:"smooth"}),N(!0),_(!0),D("initializing");try{var c,i,d,u;null===(c=T.current)||void 0===c||c.scrollIntoView({behavior:"smooth"}),setTimeout((()=>D("searching_documents")),500),setTimeout((()=>D("searching_websites")),2e3),setTimeout((()=>D("generating_answer")),4e3);const t=await Object(l.sendQuery)(e,k),s={id:`ai-${a}`,content:t.answer,document_answer:t.document_answer,website_answer:t.website_answer,llm_model:t.llm_model||k,sender:"ai",sources:t.sources,document_sources:t.document_sources,website_sources:t.website_sources,chatId:(null===n||void 0===n?void 0:n.id)||"temp",llm_fallback:t.llm_fallback};console.log("AI Message created:",{document_answer:t.document_answer?"EXISTS":"MISSING",website_answer:t.website_answer?"EXISTS":"MISSING",document_sources_count:(null===(i=t.document_sources)||void 0===i?void 0:i.length)||0,website_sources_count:(null===(d=t.website_sources)||void 0===d?void 0:d.length)||0,llm_fallback:t.llm_fallback}),console.log("\ud83d\udd0d DEBUG: Document sources from API:",t.document_sources),t.document_sources&&t.document_sources.forEach(((e,t)=>{console.log(`\ud83d\udd0d DEBUG: Source ${t}:`,{content_type:e.content_type,has_visual_content:!!e.visual_content,storage_url:e.storage_url,display_type:e.display_type,visual_content_keys:e.visual_content?Object.keys(e.visual_content):[]})})),g(r.id,s),null===(u=T.current)||void 0===u||u.scrollIntoView({behavior:"smooth"})}catch(m){console.error("Error sending message:",m);const e={id:`ai-${a}`,content:"I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.",sender:"ai",chatId:(null===n||void 0===n?void 0:n.id)||"temp",llm_fallback:!0};g(r.id,e)}finally{N(!1),_(!1),D("complete")}},P=e=>{let{items:t,maxVisible:s=3}=e;const[r,n]=Object(a.useState)(t.length<=s);if(0===t.length)return null;const c=r?t:t.slice(0,s),o=t.length>s;return Object(d.jsxs)("ul",{className:"text-xs list-disc pl-4 mt-1 space-y-1",children:[c.map(((e,t)=>Object(d.jsx)("li",{children:e.link?Object(d.jsx)("a",{href:e.link,target:"_blank",rel:"noopener noreferrer",className:"hover:underline text-blue-600 transition-colors duration-200",title:e.isDocument?"Open document at this page":"Open website in new tab",onClick:e.isDocument?t=>{e.link}:void 0,children:e.text}):Object(d.jsx)("span",{className:"text-gray-700",children:e.text})},t))),o&&!r&&Object(d.jsx)("li",{className:"list-none",children:Object(d.jsxs)("button",{onClick:()=>n(!0),className:"text-blue-500 hover:underline text-xs transition-colors duration-200",children:["+ ",t.length-s," more sources"]})}),o&&r&&Object(d.jsx)("li",{className:"list-none",children:Object(d.jsx)("button",{onClick:()=>n(!1),className:"text-blue-500 hover:underline text-xs transition-colors duration-200",children:"Show less"})})]})};return Object(d.jsxs)("div",{className:"flex h-screen bg-gray-100 transition-colors duration-300",children:[Object(d.jsx)(y,{isVisible:S,message:(()=>{switch(E){case"searching_documents":return"RailGPT Searching in Documents...";case"searching_websites":return"RailGPT Searching in Websites...";case"generating_answer":return"RailGPT Generating Response...";default:return"RailGPT Processing Your Query..."}})(),trainType:"express",currentStage:E,sidebarOpen:s}),Object(d.jsx)(f,{isOpen:s,onToggle:()=>r(!s),currentChatId:(null===n||void 0===n?void 0:n.id)||"",onChatSelect:async e=>{await i(e.id),C(e.model_used||"gemini-2.0-flash"),r(!1)},onNewChat:async()=>{console.log("Creating new chat..."),await o(),r(!1)}}),Object(d.jsxs)("div",{className:"flex flex-col flex-1 transition-all duration-300 "+(s?"lg:ml-80":""),children:[Object(d.jsx)("div",{className:`flex-1 ${c.length>0?"overflow-y-auto":"overflow-hidden"} p-4 pb-32`,children:0===c.length?Object(d.jsx)("div",{className:"flex items-center justify-center h-full",children:Object(d.jsxs)("div",{className:"text-center text-gray-500",children:[Object(d.jsx)("p",{className:"text-xl font-semibold mb-3",children:"Welcome to RailGPT!"}),Object(d.jsx)("p",{children:"Ask questions about Indian Railways..."})]})}):Object(d.jsxs)("div",{children:[c.map((e=>Object(d.jsx)("div",{className:"mb-4 "+("user"===e.sender?"flex justify-end":"flex justify-start"),children:Object(d.jsxs)("div",{className:"max-w-4xl rounded-lg p-4 transition-colors duration-300 "+("user"===e.sender?"bg-blue-500 text-white":"bg-white text-gray-800 shadow-md"),children:[Object(d.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[Object(d.jsx)("span",{className:"font-semibold",children:"user"===e.sender?"You":"RailGPT"}),e.timestamp&&Object(d.jsx)("span",{className:"text-xs ml-2 "+("user"===e.sender?"text-blue-100":"text-gray-500"),children:new Date(e.timestamp).toLocaleTimeString()})]}),"user"===e.sender&&e.content&&Object(d.jsx)("div",{className:"mt-2 whitespace-pre-wrap",children:e.content}),"ai"===e.sender&&Object(d.jsx)("div",{children:((t,s,a,r)=>{if(e.loading&&S&&!e.content&&!e.document_answer&&!e.website_answer)return null;const n=(e=>{if(!e||0===e.length)return[];const t={};return e.forEach((e=>{let s,a;if("string"===typeof e){const t=e.match(/([^\u2013]+)(?:\s*\u2013\s*Page\s*(\d+))?/i);s=t?t[1].trim():e,a=t&&t[2]?parseInt(t[2]):1}else s=e.name||e.filename||"Unknown Document",a=e.page||1;t[s]||(t[s]={filename:s,pages:[]}),t[s].pages.includes(a)||t[s].pages.push(a)})),Object.values(t).map((e=>{const t=e.pages.sort(((e,t)=>e-t)),s=1===t.length?`Page ${t[0]}`:`Pages ${t.join(", ")}`;return{text:`${e.filename} \u2013 ${s}`,link:`/viewer?file=${encodeURIComponent(e.filename)}&page=${t[0]}`,isDocument:!0}}))})(e.document_sources),o=(e=>{if(!e||0===e.length)return[];const t=new Set,s=[];return e.forEach((e=>{let a,r;if("string"===typeof e){a=e.startsWith("http")?e:`https://${e}`;try{r=new URL(a).hostname.replace(/^www\./,"")}catch{r=e}}else{a=e.url||"https://railgpt.indianrailways.gov.in";try{r=new URL(a).hostname.replace(/^www\./,"")}catch{r=a}}t.has(a)||(t.add(a),s.push({text:r,link:a,isDocument:!1}))})),s})(e.website_sources),i=!(!e.document_answer||""===e.document_answer.trim()),l=!(!e.website_answer||""===e.website_answer.trim()),u=!i&&!l||e.llm_fallback;console.log(`\ud83d\udd0d Rendering message ${e.id}:`,{hasDocumentContent:i,hasWebsiteContent:l,hasLLMFallback:u,documentAnswerLength:(null===(t=e.document_answer)||void 0===t?void 0:t.length)||0,websiteAnswerLength:(null===(s=e.website_answer)||void 0===s?void 0:s.length)||0,documentSourcesCount:n.length,websiteSourcesCount:o.length,rawDocumentAnswer:e.document_answer?"EXISTS":"MISSING",rawWebsiteAnswer:e.website_answer?"EXISTS":"MISSING",rawDocumentSources:(null===(a=e.document_sources)||void 0===a?void 0:a.length)||0,rawWebsiteSources:(null===(r=e.website_sources)||void 0===r?void 0:r.length)||0});const m=c.findIndex((t=>t.id===e.id));let b="";for(let e=m-1;e>=0;e--)if("user"===c[e].sender&&c[e].content){b=c[e].content;break}console.log("\ud83d\udd0d DEBUG: Found user question for AI message:",{aiMessageId:e.id,userQuestion:b});const g=[];let x="";if(i&&l){x="document_and_website";const t=1===n.length?n[0].text.split(" \u2013 ")[0]:"Uploaded Documents";g.push(Object(d.jsxs)("div",{className:"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300",children:[Object(d.jsxs)("h4",{className:"font-semibold text-blue-800 text-sm mb-3 flex items-center",children:["\ud83d\udcc4 Answer from ",t]}),Object(d.jsx)(h,{content:e.document_answer||"",query:b,model:e.llm_model,chatId:e.chatId}),(t=>{const s=(null===(t=e.document_sources)||void 0===t?void 0:t.filter((e=>"object"===typeof e&&e.visual_content)))||[];let a=s.filter((e=>F(e,b)));return 0===a.length&&b.toLowerCase().includes("image")&&(a=s.filter((e=>"image"===e.content_type)),console.log("\ud83d\udd0d DEBUG: No specific project matches, showing all available images:",a.length)),a.length>0&&$(b,e.document_answer||"")?Object(d.jsxs)("div",{className:"mt-4",children:[Object(d.jsx)("h5",{className:"text-sm font-semibold text-blue-800 mb-3",children:"\ud83d\udcca Visual Content:"}),Object(d.jsx)("div",{className:"space-y-3",children:a.map(((e,t)=>Object(d.jsx)(w,{source:e},t)))})]}):null})(),Object(d.jsxs)("div",{className:"mt-3 pt-3 border-t border-blue-200",children:[Object(d.jsx)("p",{className:"text-xs text-blue-700 font-semibold mb-1",children:"Sources:"}),Object(d.jsx)(P,{items:n,maxVisible:5})]})]},"document-card"));const s=o.length>1?"Extracted Websites":"Extracted Website";g.push(Object(d.jsxs)("div",{className:"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300",children:[Object(d.jsxs)("h4",{className:"font-semibold text-green-800 text-sm mb-3 flex items-center",children:["\ud83c\udf10 Answer from ",s]}),Object(d.jsx)(h,{content:e.website_answer||"",query:b,model:e.llm_model,chatId:e.chatId}),Object(d.jsxs)("div",{className:"mt-3 pt-3 border-t border-green-200",children:[Object(d.jsx)("p",{className:"text-xs text-green-700 font-semibold mb-1",children:"Sources:"}),Object(d.jsx)(P,{items:o,maxVisible:3})]})]},"website-card"))}else if(i){x="document_only";1===n.length&&n[0].text.split(" \u2013 ")[0];g.push(Object(d.jsxs)("div",{className:"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300",children:[Object(d.jsx)("h4",{className:"font-semibold text-blue-800 text-sm mb-3 flex items-center",children:"\ud83d\udcc4 Answer from Uploaded Documents"}),Object(d.jsx)(h,{content:e.document_answer||"",query:b,model:e.llm_model,chatId:e.chatId}),(t=>{const s=(null===(t=e.document_sources)||void 0===t?void 0:t.filter((e=>"object"===typeof e&&e.visual_content)))||[];let a=s.filter((e=>F(e,b)));return 0===a.length&&b.toLowerCase().includes("image")&&(a=s.filter((e=>"image"===e.content_type)),console.log("\ud83d\udd0d DEBUG: No specific project matches for document-only case, showing all available images:",a.length)),a.length>0&&$(b,e.document_answer||"")?Object(d.jsxs)("div",{className:"mt-4",children:[Object(d.jsx)("h5",{className:"text-sm font-semibold text-blue-800 mb-3",children:"\ud83d\udcca Visual Content:"}),Object(d.jsx)("div",{className:"space-y-3",children:a.map(((e,t)=>Object(d.jsx)(w,{source:e},t)))})]}):null})(),Object(d.jsxs)("div",{className:"mt-3 pt-3 border-t border-blue-200",children:[Object(d.jsx)("p",{className:"text-xs text-blue-700 font-semibold mb-1",children:"Sources:"}),Object(d.jsx)(P,{items:n,maxVisible:5})]})]},"document-priority"))}else if(l){x="website_only";o.length;g.push(Object(d.jsxs)("div",{className:"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300",children:[Object(d.jsx)("h4",{className:"font-semibold text-green-800 text-sm mb-3 flex items-center",children:"\ud83c\udf10 Answer from Extracted Websites"}),Object(d.jsx)(h,{content:e.website_answer||"",query:b,model:e.llm_model,chatId:e.chatId}),Object(d.jsxs)("div",{className:"mt-3 pt-3 border-t border-green-200",children:[Object(d.jsx)("p",{className:"text-xs text-green-700 font-semibold mb-1",children:"Sources:"}),Object(d.jsx)(P,{items:o,maxVisible:3})]})]},"website-priority"))}else if(u){var j,p,f,y,O,v;x="llm";const t=e.llm_model||"Gemini",s=null!==(j=e.llm_model)&&void 0!==j&&j.includes("chatgpt")?"\ud83e\udd16":null!==(p=e.llm_model)&&void 0!==p&&p.includes("groq")?"\u26a1":null!==(f=e.llm_model)&&void 0!==f&&f.includes("deepseek")?"\ud83d\udd0d":null!==(y=e.llm_model)&&void 0!==y&&y.includes("qwen")?"\ud83c\udf10":null!==(O=e.llm_model)&&void 0!==O&&O.includes("ollama")?"\ud83c\udfe0":null!==(v=e.llm_model)&&void 0!==v&&v.includes("huggingface")?"\ud83e\udd17":"\ud83e\udde0";S&&e.loading||g.push(Object(d.jsxs)("div",{className:"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300",children:[Object(d.jsxs)("h4",{className:"font-semibold text-purple-800 text-sm mb-3 flex items-center",children:[s," Answer generated by ",t]}),Object(d.jsx)(h,{content:e.content||"I couldn't find any relevant information to answer your question.",query:b,model:e.llm_model||"Gemini",chatId:e.chatId}),Object(d.jsx)("div",{className:"mt-3 pt-3 border-t border-purple-200",children:Object(d.jsx)("p",{className:"text-xs text-purple-600 italic",children:"This answer was generated by an AI model as no relevant information was found in your documents or websites."})})]},"llm-fallback"))}else x="no_results",S&&e.loading||g.push(Object(d.jsxs)("div",{className:"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300",children:[Object(d.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"No sources found"}),Object(d.jsx)(h,{content:e.content||"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.",query:b,model:e.llm_model||"Gemini",chatId:e.chatId})]},"no-results"));return g.length>0?Object(d.jsx)("div",{className:"mt-3",children:g}):(console.warn("Frontend: Unhandled rendering case"),Object(d.jsxs)("div",{className:"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300",children:[Object(d.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Rendering Error"}),Object(d.jsx)(h,{content:e.content||"An error occurred while rendering the response.",query:b,model:e.llm_model||"Gemini",chatId:e.chatId})]}))})()})]})},e.id))),Object(d.jsx)("div",{ref:T,style:{float:"left",clear:"both"}})]})}),Object(d.jsxs)("div",{className:"border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 "+(s?"lg:left-80 left-0":"left-0"),children:[Object(d.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),j.trim()&&!O){if(j.startsWith("/")){const e=(e=>{if(e.startsWith("/")){const t=e.split(" ")[0].toLowerCase();if("/model"===t){const t=e.substring(7).trim(),s=u.find((e=>e.name.toLowerCase().includes(t.toLowerCase())||e.id.toLowerCase().includes(t.toLowerCase())));if(s&&s.enabled)return C(s.id),p(""),"processed"}else if("/reset"===t||"/clear"===t)return x(),p(""),"processed"}return"not_processed"})(j);if("processed"===e)return void p("")}return await I(j)}},className:"flex items-center space-x-2",children:[Object(d.jsx)("div",{className:"flex-1 relative flex",children:Object(d.jsx)("input",{type:"text",value:j,onChange:e=>{const t=e.target.value;p(t)},placeholder:"Type your message... (/model, /reset, /clear)",className:"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300",disabled:O,"aria-label":"Message input"})}),Object(d.jsxs)("div",{className:"flex items-center space-x-2",children:[Object(d.jsx)(m,{currentModel:k,onModelChange:e=>C(e),isLoading:O}),Object(d.jsx)("button",{type:"submit",disabled:O||!j.trim(),className:"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300",title:"Send message",children:Object(d.jsx)("span",{children:O?"Sending...":"Send"})})]})]}),Object(d.jsxs)("div",{className:"text-xs text-gray-400 mt-1 text-center",children:["Current model: ",(null===(t=u.find((e=>e.id===k)))||void 0===t?void 0:t.name)||k]})]}),c.length>0&&Object(d.jsx)("div",{className:"h-36"})]})]})}var C=function(e){let{sidebarOpen:t,setSidebarOpen:s}=e;return Object(d.jsx)(k,{sidebarOpen:t,setSidebarOpen:s})};const S=a.forwardRef(((e,t)=>{let{className:s,variant:a="default",...r}=e;return Object(d.jsx)("div",{ref:t,role:"alert",className:`relative w-full rounded-lg border p-4 ${{default:"bg-gray-100 text-gray-800 border-gray-200",destructive:"bg-red-50 text-red-600 border-red-200",success:"bg-green-50 text-green-600 border-green-200",warning:"bg-yellow-50 text-yellow-600 border-yellow-200"}[a]} ${s||""}`,...r})}));S.displayName="Alert";const _=a.forwardRef(((e,t)=>{let{className:s,children:a,...r}=e;return Object(d.jsx)("h5",{ref:t,className:`mb-1 font-medium leading-none tracking-tight ${s||""}`,...r,children:a})}));_.displayName="AlertTitle";const E=a.forwardRef(((e,t)=>{let{className:s,...a}=e;return Object(d.jsx)("div",{ref:t,className:`text-sm [&_p]:leading-relaxed ${s||""}`,...a})}));E.displayName="AlertDescription";var D=()=>{const[e,t]=Object(a.useState)(""),[s,r]=Object(a.useState)(""),[n,c]=Object(a.useState)(""),[o,i]=Object(a.useState)(""),[u,m]=Object(a.useState)(""),[b,g]=Object(a.useState)(null),[x,j]=Object(a.useState)(!1),[h,p]=Object(a.useState)(""),[f,y]=Object(a.useState)(!1),[O,w]=Object(a.useState)("Main"),[v,N]=Object(a.useState)("idle"),[k,C]=Object(a.useState)(0),[D,T]=Object(a.useState)([]),[$,F]=Object(a.useState)({}),[I,P]=Object(a.useState)(""),[L,A]=Object(a.useState)(""),[M,U]=Object(a.useState)({}),[R,q]=Object(a.useState)([{id:"1",name:"Operations",type:"Main"},{id:"2",name:"Technical",type:"Main"},{id:"3",name:"Administrative",type:"Main"},{id:"4",name:"Safety",type:"Main"}]),[G,B]=Object(a.useState)([{id:"1",name:"Manuals",type:"Category",parentId:"1"},{id:"2",name:"Circulars",type:"Category",parentId:"1"},{id:"3",name:"Guidelines",type:"Category",parentId:"2"},{id:"4",name:"Reports",type:"Category",parentId:"3"}]),[W,z]=Object(a.useState)([{id:"1",name:"Diesel Loco",type:"Sub",parentId:"1"},{id:"2",name:"Electric Loco",type:"Sub",parentId:"1"},{id:"3",name:"Passenger",type:"Sub",parentId:"2"},{id:"4",name:"Freight",type:"Sub",parentId:"2"}]);Object(a.useEffect)((()=>{let t=!0;const a={};if(M.documentName&&!e.trim()&&(a.documentName="Document name is required"),M.mainCategory&&!s&&(a.mainCategory="Main category is required"),M.category&&!n&&(a.category="Category is required"),M.file&&!b)a.file="Document file is required";else if(b){["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","image/jpeg","image/png","text/plain"].includes(b.type)||(a.file="File type not supported. Please upload PDF, DOCX, XLSX, JPG, PNG, or TXT");const e=209715200;b.size>e&&(a.file="File too large. Maximum size is 200MB")}return t&&F(a),()=>{t=!1}}),[e,s,n,b,M]);const V=e=>{U((t=>({...t,[e]:!0})))};Object(a.useEffect)((()=>{if(I){const e=setTimeout((()=>{P("")}),5e3);return()=>clearTimeout(e)}}),[I]),Object(a.useEffect)((()=>{if(L){const e=setTimeout((()=>{A("")}),5e3);return()=>clearTimeout(e)}}),[L]);return Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 transition-colors duration-300",children:[Object(d.jsx)("h2",{className:"text-lg font-semibold mb-4 text-gray-900",children:"Upload Document"}),I&&Object(d.jsx)("div",{className:"mb-4",children:Object(d.jsxs)(S,{variant:"success",children:[Object(d.jsx)(_,{children:"Success"}),Object(d.jsx)(E,{children:I})]})}),L&&Object(d.jsx)("div",{className:"mb-4",children:Object(d.jsxs)(S,{variant:"destructive",children:[Object(d.jsx)(_,{children:"Error"}),Object(d.jsx)(E,{children:L})]})}),Object(d.jsxs)("form",{onSubmit:async a=>{if(a.preventDefault(),V("documentName"),V("mainCategory"),V("category"),V("file"),Object.keys($).length>0||!b)return void A("Please correct the errors before submitting");let d=null;const x=e=>{e()};x((()=>{j(!0),N("uploading"),C(10),P(""),A(""),T([])}));try{d=setInterval((()=>{C((e=>{const t=e+5;return t>=90?(d&&clearInterval(d),90):t}))}),300),x((()=>N("uploading")));const a=await Object(l.uploadDocument)(b,"<EMAIL>");if(d&&(clearInterval(d),d=null),a.success){var h,p;C(100),N("success"),a.chunks&&a.chunks.length>0&&(T(a.chunks),console.log("Extracted chunks:",a.chunks));const l={detail:{documentName:e,mainCategory:s,category:n,subCategory:o,minorCategory:u,file:b,uploadedAt:(new Date).toISOString(),id:(null===(h=a.data)||void 0===h?void 0:h.id)||`doc-${Date.now()}`,status:"Extracted",fileType:null===(p=b.name.split(".").pop())||void 0===p?void 0:p.toLowerCase(),qualityScore:90,chunks:a.chunks||[],extractedContent:a.chunks?a.chunks.map((e=>e.text)).join("\n\n"):"",chunks_extracted:a.chunks_extracted||0}},d=new CustomEvent("documentUploaded",{detail:l.detail});window.dispatchEvent(d),A(""),P(`Document "${e}" uploaded successfully!`);setTimeout((()=>{P("")}),5e3);setTimeout((()=>{t(""),r(""),c(""),i(""),m(""),g(null),N("idle"),C(0),T([]),j(!1),U({})}),3e3)}else N("error"),A(a.message||"Failed to upload document")}catch(f){console.error("Upload failed:",f),N("error"),A(`Upload failed: ${f instanceof Error?f.message:"Unknown error"}`)}finally{j(!1)}},className:"space-y-4",children:[Object(d.jsxs)("div",{children:[Object(d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Document Name ",Object(d.jsx)("span",{className:"text-red-500",children:"*"})]}),Object(d.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),onBlur:()=>V("documentName"),className:`w-full p-2 border ${$.documentName?"border-red-500":"border-gray-300"} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`,required:!0}),$.documentName&&Object(d.jsx)("p",{className:"mt-1 text-sm text-red-500",children:$.documentName})]}),Object(d.jsxs)("div",{children:[Object(d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Main Category ",Object(d.jsx)("span",{className:"text-red-500",children:"*"})]}),Object(d.jsxs)("div",{className:"flex gap-2",children:[Object(d.jsxs)("select",{value:s,onChange:e=>r(e.target.value),onBlur:()=>V("mainCategory"),className:`flex-1 p-2 border ${$.mainCategory?"border-red-500":"border-gray-300"} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`,required:!0,children:[Object(d.jsx)("option",{value:"",children:"Select Main Category"}),R.map((e=>Object(d.jsx)("option",{value:e.name,children:e.name},e.id)))]}),Object(d.jsx)("button",{type:"button",onClick:()=>{w("Main"),y(!0)},className:"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300",children:"+ New"})]}),$.mainCategory&&Object(d.jsx)("p",{className:"mt-1 text-sm text-red-500",children:$.mainCategory})]}),Object(d.jsxs)("div",{children:[Object(d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Category ",Object(d.jsx)("span",{className:"text-red-500",children:"*"})]}),Object(d.jsxs)("div",{className:"flex gap-2",children:[Object(d.jsxs)("select",{value:n,onChange:e=>c(e.target.value),onBlur:()=>V("category"),className:`flex-1 p-2 border ${$.category?"border-red-500":"border-gray-300"} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`,required:!0,disabled:!s,children:[Object(d.jsx)("option",{value:"",children:"Select Category"}),G.filter((e=>{var t;return!s||e.parentId===(null===(t=R.find((e=>e.name===s)))||void 0===t?void 0:t.id)})).map((e=>Object(d.jsx)("option",{value:e.name,children:e.name},e.id)))]}),Object(d.jsx)("button",{type:"button",onClick:()=>{w("Category"),y(!0)},className:"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300",disabled:!s,children:"+ New"})]}),$.category&&Object(d.jsx)("p",{className:"mt-1 text-sm text-red-500",children:$.category})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sub Category"}),Object(d.jsxs)("div",{className:"flex gap-2",children:[Object(d.jsxs)("select",{value:o,onChange:e=>i(e.target.value),className:"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300",disabled:!n,children:[Object(d.jsx)("option",{value:"",children:"Select Sub Category"}),W.filter((e=>{var t;return!n||e.parentId===(null===(t=G.find((e=>e.name===n)))||void 0===t?void 0:t.id)})).map((e=>Object(d.jsx)("option",{value:e.name,children:e.name},e.id)))]}),Object(d.jsx)("button",{type:"button",onClick:()=>{w("Sub"),y(!0)},className:"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300",disabled:!n,children:"+ New"})]})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Minor Category"}),Object(d.jsxs)("div",{className:"flex gap-2",children:[Object(d.jsx)("input",{type:"text",value:u,onChange:e=>m(e.target.value),className:"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300",placeholder:"Enter Minor Category"}),Object(d.jsx)("button",{type:"button",onClick:()=>{w("Minor"),y(!0)},className:"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300",children:"+ New"})]})]}),Object(d.jsxs)("div",{children:[Object(d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Upload Document ",Object(d.jsx)("span",{className:"text-red-500",children:"*"})]}),Object(d.jsx)("div",{className:"flex items-center justify-center w-full",children:Object(d.jsxs)("label",{className:`flex flex-col w-full h-32 border-2 ${$.file?"border-red-500":"border-blue-300"} border-dashed hover:bg-gray-50 hover:border-blue-500 rounded-lg cursor-pointer`,children:[Object(d.jsxs)("div",{className:"flex flex-col items-center justify-center pt-7",children:[Object(d.jsx)("svg",{className:"w-8 h-8 text-gray-400 group-hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),Object(d.jsx)("p",{className:"pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600",children:b?b.name:"Attach document (PDF, DOCX, XLSX, etc.)"})]}),Object(d.jsx)("input",{type:"file",className:"opacity-0",accept:".pdf,.docx,.xlsx,.txt,.png,.jpg,.jpeg",onChange:s=>{if(s.target.files&&s.target.files[0]){const a=s.target.files[0];if(g(a),V("file"),!e.trim()){const e=a.name.replace(/\.[^/.]+$/,"").replace(/[_-]/g," ");t(e),V("documentName")}}},onBlur:()=>V("file"),required:!0})]})}),$.file&&Object(d.jsx)("p",{className:"mt-1 text-sm text-red-500",children:$.file})]}),Object(d.jsx)("div",{children:Object(d.jsx)("button",{type:"submit",disabled:x,className:`w-full px-4 py-2 text-white font-medium rounded-md ${x?"bg-blue-300":"bg-blue-600 hover:bg-blue-700"} focus:outline-none focus:ring-2 focus:ring-blue-500`,children:x?"Uploading...":"Upload Document"})}),"idle"!==v&&Object(d.jsxs)("div",{className:"mt-4",children:[Object(d.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[Object(d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"uploading"===v?"Uploading...":"processing"===v?"Processing...":"success"===v?"Upload Complete":"Upload Failed"}),Object(d.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[k,"%"]})]}),Object(d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:Object(d.jsx)("div",{className:"h-2.5 rounded-full "+("error"===v?"bg-red-600":"success"===v?"bg-green-600":"bg-blue-600"),style:{width:`${k}%`}})})]}),"success"===v&&D.length>0&&Object(d.jsxs)("div",{className:"mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50",children:[Object(d.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Extracted Content Preview:"}),Object(d.jsxs)("div",{className:"max-h-40 overflow-y-auto text-sm text-gray-600",children:[D.slice(0,3).map(((e,t)=>{var s;return Object(d.jsxs)("div",{className:"mb-2 p-2 bg-white rounded border border-gray-200",children:[Object(d.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"document"===e.source_type?`Page ${e.page||"N/A"}`:`Source: ${e.source||"Unknown"}`}),Object(d.jsxs)("p",{children:[null===(s=e.text)||void 0===s?void 0:s.substring(0,150),"..."]})]},t)})),D.length>3&&Object(d.jsxs)("p",{className:"text-xs text-gray-500 text-center mt-2",children:["+ ",D.length-3," more chunks not shown"]})]})]})]}),f&&Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:Object(d.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[Object(d.jsxs)("h3",{className:"text-lg font-medium mb-4",children:["Add New ",O," Category"]}),Object(d.jsx)("input",{type:"text",value:h,onChange:e=>p(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4",placeholder:`Enter ${O} Category name`,autoFocus:!0}),Object(d.jsxs)("div",{className:"flex justify-end gap-2",children:[Object(d.jsx)("button",{onClick:()=>y(!1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),Object(d.jsx)("button",{onClick:async()=>{if(h.trim()){j(!0);try{if(await new Promise((e=>setTimeout(e,500))),"Main"===O){const e={id:`new-${Date.now()}`,name:h,type:"Main"};q((t=>[...t,e])),r(h)}else if("Category"===O){const e={id:`new-${Date.now()}`,name:h,type:"Category",parentId:s};B((t=>[...t,e])),c(h)}else if("Sub"===O){const e={id:`new-${Date.now()}`,name:h,type:"Sub",parentId:n};z((t=>[...t,e])),i(h)}else m(h);P(`Created new ${O} category: ${h}`)}catch(e){A(`Failed to create category: ${e instanceof Error?e.message:"Unknown error"}`)}finally{j(!1),p(""),y(!1)}}},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Add Category"})]})]})})]})};const T="http://localhost:8000",$=async()=>{try{const e=await fetch(`${T}/api/categories/`);if(!e.ok)throw new Error(`Failed to fetch categories: ${e.statusText}`);return await e.json()}catch(e){throw console.error("Error fetching categories:",e),e}};var F=s(118),I=s(119),P=s(120),L=s(121);var A=e=>{var t;let{document:s,isOpen:r,onClose:n,onSave:c}=e;const[o,i]=Object(a.useState)([]),[l,u]=Object(a.useState)(!1),[m,b]=Object(a.useState)(!1),[g,x]=Object(a.useState)(null),[j,h]=Object(a.useState)(null),[p,f]=Object(a.useState)(s.mainCategory||""),[y,O]=Object(a.useState)(s.category||""),[w,v]=Object(a.useState)(s.subCategory||""),[N,k]=Object(a.useState)(s.minorCategory||""),[C,S]=Object(a.useState)([]),[_,E]=Object(a.useState)([]),[D,A]=Object(a.useState)([]),[M,U]=Object(a.useState)([]);Object(a.useEffect)((()=>{r&&R()}),[r]),Object(a.useEffect)((()=>{q()}),[o,p,y,w]);const R=async()=>{u(!0),x(null);try{const e=await $();i(e);const t=((e,t)=>{const s=[],a=e=>{e.forEach((e=>{e.type===t&&s.push(e),e.children&&a(e.children)}))};return a(e),s})(e,"main_category");S(t)}catch(e){x(e instanceof Error?e.message:"Failed to load categories")}finally{u(!1)}},q=()=>{const e=C.find((e=>e.name===p));e&&e.children?E(e.children.filter((e=>"category"===e.type))):(E([]),O(""),v(""),k(""));const t=_.find((e=>e.name===y));t&&t.children?A(t.children.filter((e=>"sub_category"===e.type))):(A([]),v(""),k(""));const s=D.find((e=>e.name===w));s&&s.children?U(s.children.filter((e=>"minor_category"===e.type))):(U([]),k(""))};return r?Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[Object(d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[Object(d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Edit Document Categories"}),Object(d.jsx)("button",{onClick:n,className:"text-gray-400 hover:text-gray-600 transition-colors",children:Object(d.jsx)(F.a,{className:"h-6 w-6"})})]}),Object(d.jsxs)("div",{className:"p-6",children:[Object(d.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Document Information"}),Object(d.jsxs)("p",{className:"text-sm text-gray-600",children:[Object(d.jsx)("strong",{children:"Name:"})," ",s.name]}),Object(d.jsxs)("p",{className:"text-sm text-gray-600",children:[Object(d.jsx)("strong",{children:"Type:"})," ",null===(t=s.fileType)||void 0===t?void 0:t.toUpperCase()]}),Object(d.jsxs)("p",{className:"text-sm text-gray-600",children:[Object(d.jsx)("strong",{children:"Current Categories:"})," ",[s.mainCategory,s.category,s.subCategory,s.minorCategory].filter(Boolean).join(" > ")||"None"]})]}),g&&Object(d.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center",children:[Object(d.jsx)(I.a,{className:"h-5 w-5 text-red-500 mr-2"}),Object(d.jsx)("span",{className:"text-red-700 text-sm",children:g})]}),j&&Object(d.jsxs)("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center",children:[Object(d.jsx)(P.a,{className:"h-5 w-5 text-green-500 mr-2"}),Object(d.jsx)("span",{className:"text-green-700 text-sm",children:j})]}),l?Object(d.jsxs)("div",{className:"text-center py-8",children:[Object(d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),Object(d.jsx)("p",{className:"text-gray-600 mt-2",children:"Loading categories..."})]}):Object(d.jsxs)("div",{className:"space-y-4",children:[Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Main Category *"}),Object(d.jsxs)("select",{value:p,onChange:e=>f(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:m,children:[Object(d.jsx)("option",{value:"",children:"Select Main Category"}),C.map((e=>Object(d.jsx)("option",{value:e.name,children:e.name},e.id)))]})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category"}),Object(d.jsxs)("select",{value:y,onChange:e=>O(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:!p||m,children:[Object(d.jsx)("option",{value:"",children:"Select Category"}),_.map((e=>Object(d.jsx)("option",{value:e.name,children:e.name},e.id)))]})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sub Category"}),Object(d.jsxs)("select",{value:w,onChange:e=>v(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:!y||m,children:[Object(d.jsx)("option",{value:"",children:"Select Sub Category"}),D.map((e=>Object(d.jsx)("option",{value:e.name,children:e.name},e.id)))]})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Minor Category"}),Object(d.jsxs)("select",{value:N,onChange:e=>k(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:!w||m,children:[Object(d.jsx)("option",{value:"",children:"Select Minor Category"}),M.map((e=>Object(d.jsx)("option",{value:e.name,children:e.name},e.id)))]})]})]})]}),Object(d.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200",children:[Object(d.jsx)("button",{onClick:()=>{f(s.mainCategory||""),O(s.category||""),v(s.subCategory||""),k(s.minorCategory||""),x(null),h(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",disabled:m,children:"Reset"}),Object(d.jsxs)("div",{className:"flex space-x-3",children:[Object(d.jsx)("button",{onClick:n,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors",disabled:m,children:"Cancel"}),Object(d.jsx)("button",{onClick:async()=>{b(!0),x(null),h(null);try{const e={main_category:p||void 0,category:y||void 0,sub_category:w||void 0,minor_category:N||void 0};await(async(e,t)=>{try{const s=await fetch(`${T}/api/categories/documents/${e}/categories`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok){const e=await s.json().catch((()=>({detail:s.statusText})));throw new Error(e.detail||`Failed to update document categories: ${s.statusText}`)}return await s.json()}catch(g){throw console.error("Error updating document categories:",g),g}})(s.id,e);const t={...s,mainCategory:p,category:y,subCategory:w,minorCategory:N};h("Document categories updated successfully!"),c(t),setTimeout((()=>{n()}),1500)}catch(e){x(e instanceof Error?e.message:"Failed to update document categories")}finally{b(!1)}},disabled:!(p!==(s.mainCategory||"")||y!==(s.category||"")||w!==(s.subCategory||"")||N!==(s.minorCategory||""))||m||!p,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center",children:m?Object(d.jsxs)(d.Fragment,{children:[Object(d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):Object(d.jsxs)(d.Fragment,{children:[Object(d.jsx)(L.a,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]})]})]})}):null};var M=e=>{let{documents:t,isOpen:s,onClose:r,onSave:n}=e;const[c,o]=Object(a.useState)([]),[i,u]=Object(a.useState)(!1),[m,b]=Object(a.useState)(!1),[g,x]=Object(a.useState)(null),[j,h]=Object(a.useState)({main_category:"",category:"",sub_category:"",minor_category:""});Object(a.useEffect)((()=>{s&&p()}),[s]);const p=async()=>{u(!0),x(null);try{const e=await Object(l.getCategories)();o(e)}catch(g){console.error("Error loading categories:",g),x("Failed to load categories")}finally{u(!1)}},f=(e,t)=>{h((s=>({...s,[e]:t||void 0})))},y=()=>{h({main_category:"",category:"",sub_category:"",minor_category:""}),x(null),r()};return s?Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:Object(d.jsx)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:Object(d.jsxs)("div",{className:"p-6",children:[Object(d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[Object(d.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Bulk Update Categories (",t.length," documents)"]}),Object(d.jsx)("button",{onClick:y,className:"text-gray-400 hover:text-gray-600",children:Object(d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),g&&Object(d.jsx)("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:g}),i?Object(d.jsxs)("div",{className:"text-center py-8",children:[Object(d.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),Object(d.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading categories..."})]}):Object(d.jsxs)("div",{className:"space-y-4",children:[Object(d.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg",children:Object(d.jsxs)("p",{className:"text-sm text-blue-800",children:[Object(d.jsx)("strong",{children:"Note:"})," Only fill in the categories you want to update. Empty fields will leave the existing values unchanged for each document."]})}),Object(d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Main Category"}),Object(d.jsxs)("select",{value:j.main_category||"",onChange:e=>f("main_category",e.target.value),className:"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"Keep existing values"}),(()=>{const e=new Set;return c.forEach((t=>{0===t.level&&e.add(t.name)})),Array.from(e).sort()})().map((e=>Object(d.jsx)("option",{value:e,children:e},e)))]})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),Object(d.jsxs)("select",{value:j.category||"",onChange:e=>f("category",e.target.value),className:"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"Keep existing values"}),(()=>{const e=new Set;return c.forEach((t=>{1===t.level&&e.add(t.name)})),Array.from(e).sort()})().map((e=>Object(d.jsx)("option",{value:e,children:e},e)))]})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sub Category"}),Object(d.jsx)("input",{type:"text",value:j.sub_category||"",onChange:e=>f("sub_category",e.target.value),placeholder:"Keep existing values",className:"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Minor Category"}),Object(d.jsx)("input",{type:"text",value:j.minor_category||"",onChange:e=>f("minor_category",e.target.value),placeholder:"Keep existing values",className:"w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),Object(d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[Object(d.jsxs)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:["Selected Documents (",t.length,"):"]}),Object(d.jsx)("div",{className:"max-h-32 overflow-y-auto",children:t.map((e=>Object(d.jsxs)("div",{className:"text-sm text-gray-600 py-1",children:["\u2022 ",e.name]},e.id)))})]})]}),Object(d.jsxs)("div",{className:"flex justify-end space-x-3 mt-6 pt-4 border-t",children:[Object(d.jsx)("button",{onClick:y,disabled:m,className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 disabled:opacity-50",children:"Cancel"}),Object(d.jsxs)("button",{onClick:async()=>{if(0===t.length)return;if(Object.values(j).some((e=>e&&""!==e.trim()))){b(!0),x(null);try{const e=t.map((e=>e.id)),s={};Object.entries(j).forEach((e=>{let[t,a]=e;a&&""!==a.trim()&&(s[t]=a.trim())})),await Object(l.bulkUpdateDocumentCategories)(e,s);const a=t.map((e=>({...e,mainCategory:s.main_category||e.mainCategory,category:s.category||e.category,subCategory:s.sub_category||e.subCategory,minorCategory:s.minor_category||e.minorCategory})));n(a),y()}catch(g){console.error("Error updating categories:",g),x(g instanceof Error?g.message:"Failed to update categories")}finally{b(!1)}}else x("Please select at least one category to update")},disabled:m||i,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center",children:[m&&Object(d.jsx)("div",{className:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),m?"Updating...":"Update Categories"]})]})]})})}):null};var U=e=>{let{documents:t,onView:s,onEdit:r,onDelete:n,onReprocess:c,onCategoryUpdate:o}=e;const[i,l]=Object(a.useState)("uploadedAt"),[u,m]=Object(a.useState)("desc"),[b,g]=Object(a.useState)(""),[x,j]=Object(a.useState)(""),[h,p]=Object(a.useState)(""),[f,y]=Object(a.useState)(""),[O,w]=Object(a.useState)(""),[v,N]=Object(a.useState)(""),[k,C]=Object(a.useState)([]),[S,_]=Object(a.useState)(!1),[E,D]=Object(a.useState)({startDate:"",endDate:""}),[T,$]=Object(a.useState)(!1),[F,I]=Object(a.useState)(null),[P,L]=Object(a.useState)(!1),U=e=>{i===e?m("asc"===u?"desc":"asc"):(l(e),m("asc"))},R=e=>{if(0===k.length)return;const s=t.filter((e=>k.includes(e.id)));"delete"===e?window.confirm(`Are you sure you want to delete ${k.length} selected document(s)?`)&&(s.forEach((e=>n(e))),C([])):"reprocess"===e?(s.forEach((e=>c(e))),C([])):"bulk-category"===e&&L(!0)},q=(e,t)=>{D((s=>({...s,[e]:t})))},G=t.filter((e=>{if(b){var t,s,a;const r=b.toLowerCase();if(!(e.name.toLowerCase().includes(r)||(null===(t=e.uploadedBy)||void 0===t?void 0:t.toLowerCase().includes(r))||(null===(s=e.category)||void 0===s?void 0:s.toLowerCase().includes(r))||(null===(a=e.mainCategory)||void 0===a?void 0:a.toLowerCase().includes(r))))return!1}if(f&&e.mainCategory!==f)return!1;if(x&&e.mainCategory!==x&&e.category!==x&&e.subCategory!==x&&e.minorCategory!==x)return!1;if(h&&e.status!==h)return!1;if(v&&e.fileType!==v)return!1;if(O){const t=new Date(e.uploadedAt),s=new Date;if("day"===O){if(t<new Date(s.getTime()-864e5))return!1}else if("week"===O){if(t<new Date(s.getTime()-6048e5))return!1}else if("month"===O){if(t<new Date(s.getTime()-2592e6))return!1}}if(E.startDate||E.endDate){const t=new Date(e.uploadedAt);if(E.startDate){const e=new Date(E.startDate);if(e.setHours(0,0,0,0),t<e)return!1}if(E.endDate){const e=new Date(E.endDate);if(e.setHours(23,59,59,999),t>e)return!1}}return!0})),B=[...G].sort(((e,t)=>{var s,a;const r=null!==(s=e[i])&&void 0!==s?s:"",n=null!==(a=t[i])&&void 0!==a?a:"";return r<n?"asc"===u?-1:1:r>n?"asc"===u?1:-1:0})),W=e=>{const t=new Date(e);return new Intl.DateTimeFormat("en-IN",{day:"2-digit",month:"short",year:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)},z=e=>{let t;switch(e){case"Extracted":t="bg-green-100 text-green-800";break;case"Pending":t="bg-yellow-100 text-yellow-800";break;case"Manual Review":t="bg-red-100 text-red-800";break;default:t="bg-gray-100 text-gray-800"}return Object(d.jsx)("span",{className:`${t} px-2 py-1 rounded-full text-xs font-medium`,children:e})},V=Array.from(new Set(t.map((e=>e.mainCategory)).filter(Boolean))),H=Array.from(new Set(t.flatMap((e=>[e.category,e.subCategory,e.minorCategory])).filter(Boolean))),J=Array.from(new Set(t.map((e=>e.fileType)).filter(Boolean)));return Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[Object(d.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[Object(d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[Object(d.jsx)("h2",{"aria-label":"Filter controls",className:"text-lg font-semibold",children:"Manage Documents"}),Object(d.jsxs)("div",{className:"flex space-x-2",children:[Object(d.jsxs)("button",{onClick:()=>_(!S),className:"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md flex items-center text-sm",children:[Object(d.jsx)("span",{children:S?"Hide Filters":"Show Filters"}),Object(d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})})]}),k.length>0&&Object(d.jsxs)("div",{className:"flex space-x-2",children:[Object(d.jsxs)("button",{onClick:()=>R("bulk-category"),className:"px-3 py-1 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-md text-sm",children:["Update Categories (",k.length,")"]}),Object(d.jsxs)("button",{onClick:()=>R("reprocess"),className:"px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 rounded-md text-sm",children:["Reprocess (",k.length,")"]}),Object(d.jsxs)("button",{onClick:()=>R("delete"),className:"px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm",children:["Delete (",k.length,")"]})]})]})]}),Object(d.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[Object(d.jsx)("div",{className:"md:w-1/2 lg:w-2/3",children:Object(d.jsx)("input",{type:"text",placeholder:"Search documents by name, category, or uploader...",value:b,onChange:e=>g(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})}),Object(d.jsx)("div",{className:"md:w-1/2 lg:w-1/3",children:Object(d.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"All Statuses"}),Object(d.jsx)("option",{value:"Extracted",children:"Extracted"}),Object(d.jsx)("option",{value:"Pending",children:"Pending"}),Object(d.jsx)("option",{value:"Processing",children:"Processing"}),Object(d.jsx)("option",{value:"Manual Review",children:"Manual Review"}),Object(d.jsx)("option",{value:"Failed",children:"Failed"})]})})]}),S&&Object(d.jsx)("div",{className:"p-4 bg-gray-100 rounded-lg mt-4 mb-6",children:Object(d.jsxs)("div",{className:"flex flex-col space-y-4",children:[Object(d.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[Object(d.jsxs)("div",{className:"md:w-1/3",children:[Object(d.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Main Category"}),Object(d.jsxs)("select",{value:f,onChange:e=>y(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"All Main Categories"}),V.map((e=>Object(d.jsx)("option",{value:e,children:e},e)))]})]}),Object(d.jsxs)("div",{className:"md:w-1/3",children:[Object(d.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Category"}),Object(d.jsxs)("select",{value:x,onChange:e=>j(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"All Categories"}),H.map((e=>Object(d.jsx)("option",{value:e,children:e},e)))]})]}),Object(d.jsxs)("div",{className:"md:w-1/3",children:[Object(d.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"File Type"}),Object(d.jsxs)("select",{value:v,onChange:e=>N(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"All File Types"}),J.map((e=>Object(d.jsx)("option",{value:e,children:e.toUpperCase()},e)))]})]})]}),Object(d.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[Object(d.jsxs)("div",{className:"md:w-1/3",children:[Object(d.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Date Added"}),Object(d.jsxs)("select",{value:O,onChange:e=>w(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"All Time"}),Object(d.jsx)("option",{value:"day",children:"Last 24 Hours"}),Object(d.jsx)("option",{value:"week",children:"Last 7 Days"}),Object(d.jsx)("option",{value:"month",children:"Last 30 Days"})]})]}),Object(d.jsxs)("div",{className:"md:w-1/3",children:[Object(d.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Date Range"}),Object(d.jsxs)("div",{className:"flex space-x-2",children:[Object(d.jsx)("div",{className:"w-1/2",children:Object(d.jsx)("input",{type:"date",id:"start-date",value:E.startDate,onChange:e=>q("startDate",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Start date"})}),Object(d.jsx)("div",{className:"w-1/2",children:Object(d.jsx)("input",{type:"date",id:"end-date",value:E.endDate,onChange:e=>q("endDate",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"End date"})})]})]}),Object(d.jsx)("div",{className:"md:w-1/3 flex items-end",children:Object(d.jsx)("button",{onClick:()=>{g(""),y(""),j(""),p(""),N(""),w(""),D({startDate:"",endDate:""})},className:"px-3 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md text-sm w-full",children:"Reset Filters"})})]})]})}),Object(d.jsxs)("div",{className:"text-sm text-gray-500 mb-2",children:["Showing ",G.length," of ",t.length," documents"]})]}),Object(d.jsx)("div",{className:"overflow-x-auto",children:Object(d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[Object(d.jsx)("thead",{className:"bg-gray-50",children:Object(d.jsxs)("tr",{children:[Object(d.jsx)("th",{scope:"col",className:"px-3 py-3 text-center",children:Object(d.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500",checked:k.length>0&&k.length===G.length,onChange:()=>{return e=G,void(k.length===e.length?C([]):C(e.map((e=>e.id))));var e}})}),Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>U("name"),children:["Name","name"===i&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===u?"\u2191":"\u2193"})]}),Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>U("uploadedAt"),children:["Date Uploaded","uploadedAt"===i&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===u?"\u2191":"\u2193"})]}),Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>U("mainCategory"),children:["Category","mainCategory"===i&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===u?"\u2191":"\u2193"})]}),Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>U("uploadedBy"),children:["Uploaded By","uploadedBy"===i&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===u?"\u2191":"\u2193"})]}),Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>U("qualityScore"),children:["Quality","qualityScore"===i&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===u?"\u2191":"\u2193"})]}),Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>U("status"),children:["Status","status"===i&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===u?"\u2191":"\u2193"})]}),Object(d.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),Object(d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:B.length>0?B.map((e=>Object(d.jsxs)("tr",{className:k.includes(e.id)?"bg-blue-50":void 0,children:[Object(d.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:Object(d.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500",checked:k.includes(e.id),onChange:()=>{return t=e.id,void(k.includes(t)?C(k.filter((e=>e!==t))):C([...k,t]));var t}})}),Object(d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[Object(d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),Object(d.jsx)("div",{className:"text-sm text-gray-500",children:e.fileType.toUpperCase()})]}),Object(d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:W(e.uploadedAt)}),Object(d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[Object(d.jsxs)("div",{className:"text-sm text-gray-900",children:[e.mainCategory||"N/A",e.category&&Object(d.jsxs)("span",{children:[" / ",e.category]})]}),e.subCategory&&Object(d.jsxs)("div",{className:"text-xs text-gray-500",children:[e.subCategory,e.minorCategory&&Object(d.jsxs)("span",{children:[" / ",e.minorCategory]})]})]}),Object(d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.uploadedBy||"Unknown"}),Object(d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:void 0!==e.qualityScore?Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsxs)("div",{className:"w-24 bg-gray-200 rounded-full h-2.5",children:[Object(d.jsx)("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:`${e.qualityScore}%`}}),Object(d.jsx)("div",{className:"bg-gray-300 h-2.5 rounded-r-full",style:{width:100-e.qualityScore+"%",float:"right"}})]}),Object(d.jsxs)("span",{className:"text-sm text-gray-700 ml-2",children:[e.qualityScore,"%"]})]}):Object(d.jsx)("span",{className:"text-sm text-gray-500",children:"N/A"})}),Object(d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:z(e.status)}),Object(d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:Object(d.jsxs)("div",{className:"flex space-x-2 justify-end",children:[Object(d.jsx)("button",{onClick:()=>s(e),className:"text-blue-600 hover:text-blue-900",title:"View document details",children:Object(d.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})}),Object(d.jsx)("button",{onClick:()=>r(e),className:"text-indigo-600 hover:text-indigo-900",title:"Edit document",children:Object(d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),Object(d.jsx)("button",{onClick:()=>(e=>{I(e),$(!0)})(e),className:"text-purple-600 hover:text-purple-900",title:"Edit categories",children:Object(d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})})}),Object(d.jsx)("button",{onClick:()=>c(e),className:"text-yellow-600 hover:text-yellow-900",title:"Reprocess document",children:Object(d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),Object(d.jsx)("button",{onClick:()=>n(e),className:"text-red-600 hover:text-red-900",title:"Delete document",children:Object(d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})})]},e.id))):Object(d.jsx)("tr",{children:Object(d.jsx)("td",{colSpan:8,className:"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500",children:"No documents found"})})})]})}),F&&Object(d.jsx)(A,{document:F,isOpen:T,onClose:()=>{$(!1),I(null)},onSave:e=>{o&&o(e)}}),Object(d.jsx)(M,{documents:t.filter((e=>k.includes(e.id))),isOpen:P,onClose:()=>{L(!1)},onSave:e=>{o&&e.forEach((e=>o(e))),C([]),L(!1)}})]})},R=s(30);R.c.GlobalWorkerOptions.workerSrc="//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js";var q=e=>{let{url:t,fileName:s}=e;const[r,n]=Object(a.useState)(null),[c,o]=Object(a.useState)(1),[i,l]=Object(a.useState)(1),[u,m]=Object(a.useState)(!0),[b,g]=Object(a.useState)(null);function x(e){if(!r)return;const t=c+e;t>=1&&t<=r&&o(t)}function j(e){l(e)}return Object(d.jsxs)("div",{className:"flex flex-col items-center",children:[Object(d.jsxs)("div",{className:"mb-4 w-full flex justify-between items-center bg-gray-100 p-2 rounded-lg",children:[Object(d.jsxs)("div",{className:"flex space-x-2",children:[Object(d.jsx)("button",{onClick:()=>x(-1),disabled:c<=1,className:"px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Previous"}),Object(d.jsx)("button",{onClick:()=>x(1),disabled:!r||c>=r,className:"px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Next"})]}),Object(d.jsx)("div",{className:"flex items-center",children:Object(d.jsxs)("span",{className:"text-sm",children:["Page ",c," of ",r||"--"]})}),Object(d.jsxs)("div",{className:"flex space-x-2",children:[Object(d.jsx)("button",{onClick:()=>j(i-.2),disabled:i<=.6,className:"px-2 py-1 bg-gray-200 rounded disabled:bg-gray-100 disabled:text-gray-400",children:"-"}),Object(d.jsxs)("span",{className:"text-sm",children:[Math.round(100*i),"%"]}),Object(d.jsx)("button",{onClick:()=>j(i+.2),disabled:i>=2,className:"px-2 py-1 bg-gray-200 rounded disabled:bg-gray-100 disabled:text-gray-400",children:"+"})]})]}),Object(d.jsxs)("div",{className:"border border-gray-300 rounded-lg overflow-auto w-full max-h-[60vh] bg-gray-50",children:[u&&Object(d.jsx)("div",{className:"flex items-center justify-center h-60",children:Object(d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"})}),b&&Object(d.jsx)("div",{className:"flex items-center justify-center h-60 text-red-600 text-center p-4",children:Object(d.jsxs)("div",{children:[Object(d.jsx)("p",{className:"font-semibold",children:"Failed to load PDF"}),Object(d.jsx)("p",{className:"text-sm mt-2",children:b}),Object(d.jsxs)("p",{className:"text-sm mt-4",children:["Filename: ",s]}),Object(d.jsxs)("div",{className:"mt-4 space-y-2",children:[Object(d.jsx)("button",{onClick:()=>window.open(t,"_blank"),className:"block w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Open in New Tab"}),Object(d.jsx)("button",{onClick:()=>{console.log("Retrying PDF load with different worker..."),R.c.GlobalWorkerOptions.workerSrc="//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.worker.min.js",window.location.reload()},className:"block w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600",children:"Retry with Different Viewer"}),Object(d.jsx)("a",{href:t,download:s,className:"block w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-center",children:"Download PDF"})]})]})}),Object(d.jsx)(R.a,{file:t,onLoadSuccess:function(e){let{numPages:t}=e;n(t),m(!1),g(null)},onLoadError:function(e){console.error("Error loading PDF:",e);const t=e.message||"Unknown error";t.includes("version")||t.includes("worker")?g("PDF viewer configuration issue. The document is available but the viewer needs updating."):t.includes("network")||t.includes("fetch")?g("Network error loading PDF. Please check your connection."):g(`Failed to load PDF: ${t}`),m(!1)},loading:Object(d.jsx)("div",{className:"flex items-center justify-center h-60",children:Object(d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"})}),error:Object(d.jsx)("div",{className:"flex items-center justify-center h-60 text-red-600",children:Object(d.jsx)("p",{children:"Failed to load PDF"})}),children:Object(d.jsx)(R.b,{pageNumber:c,scale:i,renderTextLayer:!1,renderAnnotationLayer:!1,error:"Failed to load page"})})]})]})},G=s(122);var B=e=>{let{document:t,extractionDetails:s,isOpen:r,onClose:n,onReprocess:c}=e;const[o,i]=Object(a.useState)("content"),[u,m]=Object(a.useState)("PyMuPDF"),[b,g]=Object(a.useState)(!1),[x,j]=Object(a.useState)(null),[h,p]=Object(a.useState)("pdf"===t.fileType.toLowerCase()),[f,y]=Object(a.useState)("");if(Object(a.useEffect)((()=>{if("pdf"===t.fileType.toLowerCase()){const e=`${l.API_URL}/api/documents/view/${encodeURIComponent(t.id)}`;y(e),p(!0)}else p(!1);j(null)}),[t]),!r)return null;return Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col",children:[Object(d.jsxs)("div",{className:"p-4 border-b border-gray-200 flex justify-between items-center",children:[Object(d.jsx)("h2",{className:"text-lg font-semibold",children:t.name}),Object(d.jsx)("button",{onClick:n,className:"text-gray-500 hover:text-gray-700 focus:outline-none",children:Object(d.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),Object(d.jsxs)("div",{className:"flex border-b border-gray-200",children:[Object(d.jsx)("button",{className:"px-4 py-2 font-medium "+("content"===o?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>i("content"),children:"Extracted Content"}),h&&Object(d.jsx)("button",{className:"px-4 py-2 font-medium "+("preview"===o?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>i("preview"),children:"PDF Preview"}),Object(d.jsx)("button",{className:"px-4 py-2 font-medium "+("details"===o?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>i("details"),children:"Processing Details"})]}),x&&Object(d.jsx)("div",{className:"p-4",children:Object(d.jsxs)(S,{variant:"destructive",children:[Object(d.jsx)(_,{children:"Error"}),Object(d.jsx)(E,{children:x})]})}),Object(d.jsx)("div",{className:"flex-1 overflow-auto p-4",children:"content"===o?Object(d.jsx)("div",{className:"prose max-w-none",children:Object(d.jsx)("div",{className:"whitespace-pre-wrap text-sm bg-gray-50 p-4 rounded-md overflow-auto h-[60vh]",children:t.extractedContent||s.extractedContent||(t.chunks&&t.chunks.length>0?t.chunks.map((e=>e.text)).join("\n\n"):"No extracted content available")})}):"preview"===o&&h?Object(d.jsx)("div",{className:"h-[60vh]",children:b?Object(d.jsxs)("div",{className:"flex justify-center items-center h-full",children:[Object(d.jsx)(G.a,{className:"h-8 w-8 animate-spin text-blue-500"}),Object(d.jsx)("span",{className:"ml-2",children:"Loading PDF preview..."})]}):Object(d.jsx)(q,{url:f,fileName:t.name})}):Object(d.jsxs)("div",{className:"space-y-4",children:[Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Extraction Method"}),Object(d.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:s.extractionMethod})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Quality Score"}),Object(d.jsx)("div",{className:"mt-1",children:(()=>{const{qualityScore:e}=s;let t;return t=e>=80?"bg-green-500":e>=60?"bg-green-400":e>=40?"bg-yellow-500":e>=20?"bg-orange-500":"bg-red-500",Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 mr-2",children:Object(d.jsx)("div",{className:`h-2.5 rounded-full ${t}`,style:{width:`${e}%`}})}),Object(d.jsxs)("span",{className:"text-sm font-medium",children:[e,"%"]})]})})()})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Processing Time"}),Object(d.jsxs)("p",{className:"mt-1 text-sm text-gray-600",children:[s.processingTime," ms (",(s.processingTime/1e3).toFixed(2)," seconds)"]})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Chunks Created"}),Object(d.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:s.chunks})]}),s.warnings.length>0&&Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Warnings"}),Object(d.jsx)("ul",{className:"mt-1 text-sm text-gray-600 list-disc pl-5",children:s.warnings.map(((e,t)=>Object(d.jsx)("li",{className:"text-yellow-600",children:e},t)))})]}),s.fallbackReason&&Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Fallback Reason"}),Object(d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.fallbackReason})]}),Object(d.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Retry Extraction"}),Object(d.jsxs)("div",{className:"mt-2 flex items-end gap-3",children:[Object(d.jsxs)("div",{className:"flex-1",children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Extraction Tool"}),Object(d.jsxs)("select",{value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:b,children:[Object(d.jsx)("option",{value:"PyMuPDF",children:"PyMuPDF"}),Object(d.jsx)("option",{value:"PDFPlumber",children:"PDF Plumber"}),Object(d.jsx)("option",{value:"Tesseract OCR",children:"Tesseract OCR"}),Object(d.jsx)("option",{value:"Textract",children:"AWS Textract"}),Object(d.jsx)("option",{value:"DocX Parser",children:"DocX Parser"})]})]}),Object(d.jsx)("button",{onClick:async()=>{g(!0),j(null);try{await c(t,u)}catch(x){console.error("Error reprocessing document:",x),j(x instanceof Error?x.message:"An unexpected error occurred during reprocessing")}finally{g(!1)}},disabled:b,className:"px-4 py-2 rounded-md "+(b?"bg-gray-300 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:b?"Processing...":"Reprocess Document"})]})]})]})})]})})},W=s(123),z=s(124),V=s(125),H=s(126),J=s(127),Q=s(128),Z=s(129);var X=e=>{let{isOpen:t,onClose:s}=e;const[r,n]=Object(a.useState)([]),[c,o]=Object(a.useState)([]),[i,l]=Object(a.useState)(!1),[u,m]=Object(a.useState)(null),[b,g]=Object(a.useState)(null),[x,j]=Object(a.useState)(new Set),[h,p]=Object(a.useState)(null),[f,y]=Object(a.useState)(!1),[O,w]=Object(a.useState)({name:"",type:"main_category",description:"",sort_order:0}),[v,N]=Object(a.useState)({});Object(a.useEffect)((()=>{t&&k()}),[t]),Object(a.useEffect)((()=>{if(r.length>0){const e=(e=>{const t=new Map,s=[];e.forEach((e=>{t.set(e.id,{...e,children:[]})})),e.forEach((e=>{const a=t.get(e.id);if(e.parent_id){const s=t.get(e.parent_id);s&&(s.children=s.children||[],s.children.push(a))}else s.push(a)}));const a=e=>{e.sort(((e,t)=>e.sort_order!==t.sort_order?e.sort_order-t.sort_order:e.name.localeCompare(t.name))),e.forEach((e=>{e.children&&e.children.length>0&&a(e.children)}))};return a(s),s})(r);o(e)}}),[r]);const k=async()=>{l(!0),m(null);try{const e=await $();n(e)}catch(e){m(e instanceof Error?e.message:"Failed to load categories")}finally{l(!1)}},C=async e=>{try{m(null),await(async(e,t)=>{try{const s=await fetch(`${T}/api/categories/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok){const e=await s.json().catch((()=>({detail:s.statusText})));throw new Error(e.detail||`Failed to update category: ${s.statusText}`)}return await s.json()}catch(u){throw console.error("Error updating category:",u),u}})(e,v),g("Category updated successfully!"),p(null),N({}),await k()}catch(t){m(t instanceof Error?t.message:"Failed to update category")}},S=async(e,t)=>{if(window.confirm(`Are you sure you want to delete the category "${t}"? This will deactivate it but preserve data integrity.`))try{m(null),await(async e=>{try{const t=await fetch(`${T}/api/categories/${e}`,{method:"DELETE"});if(!t.ok){const e=await t.json().catch((()=>({detail:t.statusText})));throw new Error(e.detail||`Failed to delete category: ${t.statusText}`)}return await t.json()}catch(u){throw console.error("Error deleting category:",u),u}})(e),g("Category deleted successfully!"),await k()}catch(s){m(s instanceof Error?s.message:"Failed to delete category")}},_=()=>{p(null),N({})},E=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const s=e.children&&e.children.length>0,a=x.has(e.id),r=h===e.id;return Object(d.jsxs)("div",{className:"border-b border-gray-100 last:border-b-0",children:[Object(d.jsxs)("div",{className:"flex items-center py-2 px-4 hover:bg-gray-50 "+(t>0?"ml-"+4*t:""),style:{paddingLeft:20*t+16+"px"},children:[Object(d.jsx)("button",{onClick:()=>(e=>{const t=new Set(x);t.has(e)?t.delete(e):t.add(e),j(t)})(e.id),className:"mr-2 p-1 hover:bg-gray-200 rounded",disabled:!s,children:s?a?Object(d.jsx)(W.a,{className:"h-4 w-4 text-gray-600"}):Object(d.jsx)(z.a,{className:"h-4 w-4 text-gray-600"}):Object(d.jsx)("div",{className:"h-4 w-4"})}),Object(d.jsx)("div",{className:"mr-3",children:s?a?Object(d.jsx)(V.a,{className:"h-4 w-4 text-blue-500"}):Object(d.jsx)(H.a,{className:"h-4 w-4 text-blue-500"}):Object(d.jsx)("div",{className:"h-4 w-4 bg-gray-300 rounded-sm"})}),Object(d.jsx)("div",{className:"flex-1 min-w-0",children:r?Object(d.jsxs)("div",{className:"flex items-center space-x-2",children:[Object(d.jsx)("input",{type:"text",value:v.name||"",onChange:e=>N({...v,name:e.target.value}),className:"flex-1 px-2 py-1 border border-gray-300 rounded text-sm",placeholder:"Category name"}),Object(d.jsx)("input",{type:"text",value:v.description||"",onChange:e=>N({...v,description:e.target.value}),className:"flex-1 px-2 py-1 border border-gray-300 rounded text-sm",placeholder:"Description"}),Object(d.jsx)("input",{type:"number",value:v.sort_order||0,onChange:e=>N({...v,sort_order:parseInt(e.target.value)||0}),className:"w-16 px-2 py-1 border border-gray-300 rounded text-sm",placeholder:"Order"}),Object(d.jsx)("button",{onClick:()=>C(e.id),className:"p-1 text-green-600 hover:bg-green-100 rounded",children:Object(d.jsx)(L.a,{className:"h-4 w-4"})}),Object(d.jsx)("button",{onClick:_,className:"p-1 text-gray-600 hover:bg-gray-100 rounded",children:Object(d.jsx)(F.a,{className:"h-4 w-4"})})]}):Object(d.jsxs)("div",{className:"flex items-center justify-between",children:[Object(d.jsxs)("div",{className:"flex-1 min-w-0",children:[Object(d.jsxs)("div",{className:"flex items-center space-x-2",children:[Object(d.jsx)("span",{className:"font-medium text-gray-900",children:e.name}),Object(d.jsx)("span",{className:"text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded",children:e.type.replace("_"," ")}),Object(d.jsxs)("span",{className:"text-xs text-gray-500",children:["#",e.sort_order]})]}),e.description&&Object(d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),Object(d.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:e.full_path})]}),Object(d.jsxs)("div",{className:"flex items-center space-x-1 ml-4",children:[Object(d.jsx)("button",{onClick:()=>(e=>{p(e.id),N({name:e.name,description:e.description,sort_order:e.sort_order})})(e),className:"p-1 text-blue-600 hover:bg-blue-100 rounded",children:Object(d.jsx)(J.a,{className:"h-4 w-4"})}),Object(d.jsx)("button",{onClick:()=>S(e.id,e.name),className:"p-1 text-red-600 hover:bg-red-100 rounded",children:Object(d.jsx)(Q.a,{className:"h-4 w-4"})})]})]})})]}),s&&a&&Object(d.jsx)("div",{children:e.children.map((e=>E(e,t+1)))})]},e.id)};return t?Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col",children:[Object(d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[Object(d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Category Management"}),Object(d.jsxs)("div",{className:"flex items-center space-x-3",children:[Object(d.jsxs)("button",{onClick:()=>y(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center",children:[Object(d.jsx)(Z.a,{className:"h-4 w-4 mr-2"}),"Add Category"]}),Object(d.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:Object(d.jsx)(F.a,{className:"h-6 w-6"})})]})]}),u&&Object(d.jsxs)("div",{className:"mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center",children:[Object(d.jsx)(I.a,{className:"h-5 w-5 text-red-500 mr-2"}),Object(d.jsx)("span",{className:"text-red-700 text-sm",children:u})]}),b&&Object(d.jsxs)("div",{className:"mx-6 mt-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center",children:[Object(d.jsx)(P.a,{className:"h-5 w-5 text-green-500 mr-2"}),Object(d.jsx)("span",{className:"text-green-700 text-sm",children:b})]}),f&&Object(d.jsxs)("div",{className:"mx-6 mt-4 p-4 bg-gray-50 border border-gray-200 rounded-md",children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"Create New Category"}),Object(d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-3",children:[Object(d.jsx)("input",{type:"text",value:O.name,onChange:e=>w({...O,name:e.target.value}),className:"px-3 py-2 border border-gray-300 rounded-md",placeholder:"Category name"}),Object(d.jsxs)("select",{value:O.type,onChange:e=>w({...O,type:e.target.value}),className:"px-3 py-2 border border-gray-300 rounded-md",children:[Object(d.jsx)("option",{value:"main_category",children:"Main Category"}),Object(d.jsx)("option",{value:"category",children:"Category"}),Object(d.jsx)("option",{value:"sub_category",children:"Sub Category"}),Object(d.jsx)("option",{value:"minor_category",children:"Minor Category"})]}),Object(d.jsx)("input",{type:"text",value:O.description,onChange:e=>w({...O,description:e.target.value}),className:"px-3 py-2 border border-gray-300 rounded-md",placeholder:"Description"}),Object(d.jsxs)("div",{className:"flex space-x-2",children:[Object(d.jsx)("input",{type:"number",value:O.sort_order,onChange:e=>w({...O,sort_order:parseInt(e.target.value)||0}),className:"w-20 px-3 py-2 border border-gray-300 rounded-md",placeholder:"Order"}),Object(d.jsx)("button",{onClick:async()=>{if(O.name.trim())try{m(null),await(async e=>{try{const t=await fetch(`${T}/api/categories/`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){const e=await t.json().catch((()=>({detail:t.statusText})));throw new Error(e.detail||`Failed to create category: ${t.statusText}`)}return await t.json()}catch(u){throw console.error("Error creating category:",u),u}})(O),g("Category created successfully!"),y(!1),w({name:"",type:"main_category",description:"",sort_order:0}),await k()}catch(e){m(e instanceof Error?e.message:"Failed to create category")}else m("Category name is required")},className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:"Create"}),Object(d.jsx)("button",{onClick:()=>y(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"})]})]})]}),Object(d.jsx)("div",{className:"flex-1 overflow-y-auto",children:i?Object(d.jsxs)("div",{className:"text-center py-8",children:[Object(d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),Object(d.jsx)("p",{className:"text-gray-600 mt-2",children:"Loading categories..."})]}):Object(d.jsx)("div",{className:"divide-y divide-gray-100",children:c.map((e=>E(e)))})}),Object(d.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200",children:[Object(d.jsxs)("div",{className:"text-sm text-gray-600",children:["Total categories: ",r.length]}),Object(d.jsx)("button",{onClick:s,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors",children:"Close"})]})]})}):null},Y=s(130);const K=[{id:"1",name:"Railway Safety Guidelines 2025",uploadedAt:"2025-05-01T10:30:00Z",mainCategory:"Safety",category:"Guidelines",subCategory:"General",uploadedBy:"<EMAIL>",qualityScore:92,status:"Extracted",filePath:"/documents/railway-safety-guidelines-2025.pdf",fileType:"pdf",fileSize:2457600},{id:"2",name:"Engine Maintenance Manual",uploadedAt:"2025-04-28T14:15:00Z",mainCategory:"Technical",category:"Manuals",subCategory:"Diesel Loco",uploadedBy:"<EMAIL>",qualityScore:87,status:"Extracted",filePath:"/documents/engine-maintenance-manual.pdf",fileType:"pdf",fileSize:15728640},{id:"3",name:"Schedule of Train Operations - Q2 2025",uploadedAt:"2025-04-15T09:45:00Z",mainCategory:"Operations",category:"Schedules",uploadedBy:"<EMAIL>",qualityScore:75,status:"Extracted",filePath:"/documents/train-schedule-q2-2025.xlsx",fileType:"xlsx",fileSize:1048576},{id:"4",name:"Station Master Handbook",uploadedAt:"2025-03-20T11:00:00Z",mainCategory:"Administrative",category:"Handbooks",subCategory:"Station Operations",minorCategory:"Management",uploadedBy:"<EMAIL>",qualityScore:95,status:"Extracted",filePath:"/documents/station-master-handbook.docx",fileType:"docx",fileSize:5242880},{id:"5",name:"Railway Board Meeting Minutes - April 2025",uploadedAt:"2025-05-02T16:20:00Z",mainCategory:"Administrative",category:"Meeting Minutes",uploadedBy:"<EMAIL>",status:"Pending",filePath:"/documents/railway-board-minutes-apr-2025.pdf",fileType:"pdf",fileSize:524288}],ee={extractionMethod:"PyMuPDF",qualityScore:92,warnings:["Some tables may not be properly extracted"],extractedContent:"\n# Railway Safety Guidelines 2025\n\n## 1. Introduction\n\nThis document outlines the comprehensive safety guidelines for all railway operations in 2025. These guidelines are mandated by the Railway Safety Commission and must be followed by all railway personnel.\n\n## 2. General Safety Protocols\n\n### 2.1 Personal Protective Equipment (PPE)\n\nAll railway staff working on or near the tracks must wear:\n- High-visibility vest or clothing\n- Safety helmet\n- Safety boots with ankle support\n- Hearing protection when working near operating machinery\n\n### 2.2 Communication Protocols\n\nClear communication is essential for railway safety:\n- Use standard radio communication protocols\n- Confirm all instructions with a repeat-back\n- Use established hand signals when radio communication is not possible\n- Report any communication equipment failures immediately\n\n## 3. Track Maintenance Safety\n\n### 3.1 Track Inspection\n\nRegular track inspections must be conducted:\n- Visual inspections daily\n- Ultrasonic testing monthly\n- Comprehensive structural assessment quarterly\n\n### 3.2 Work Zone Safety\n\nFor maintenance work on active tracks:\n- Establish clear work zone boundaries\n- Assign a dedicated lookout person\n- Use track circuit operating devices where available\n- Implement temporary speed restrictions on adjacent tracks\n\n## 4. Train Operation Safety\n\n### 4.1 Pre-departure Checks\n\nBefore any train departure, complete the following safety checks:\n- Brake system functionality\n- Signal system responsiveness\n- Communication equipment testing\n- Door operation verification\n\n### 4.2 Speed Restrictions\n\nAdhere to all speed restrictions, especially in:\n- Curves and bends\n- Bridges and tunnels\n- Areas with ongoing maintenance\n- Bad weather conditions\n\n## 5. Emergency Procedures\n\n### 5.1 Accident Response\n\nIn case of an accident:\n- Immediately secure the site\n- Notify central control\n- Provide first aid as necessary\n- Document all relevant details\n\n### 5.2 Evacuation Protocols\n\nStandard evacuation procedures for railway emergencies:\n- Identify safe exit routes\n- Guide passengers to designated assembly points\n- Account for all passengers and staff\n- Provide regular updates to emergency services\n\n## 6. Compliance and Reporting\n\nAll safety incidents, near misses, and potential hazards must be reported through the official Railway Safety Management System within 24 hours of occurrence.\n\nRegular safety audits will be conducted to ensure compliance with these guidelines.\n  ",processingTime:3450,chunks:12};var te=()=>{const[e,t]=Object(a.useState)(K),[r,n]=Object(a.useState)(null),[c,o]=Object(a.useState)(!1),[i,u]=Object(a.useState)(ee),[m,b]=Object(a.useState)(!1);Object(a.useEffect)((()=>{g();const e=e=>{const s=e.detail;t((e=>[s,...e])),setTimeout((()=>g()),1e3)};return window.addEventListener("documentUploaded",e),()=>{window.removeEventListener("documentUploaded",e)}}),[]);const g=async()=>{try{const e=await Object(l.getDocuments)();e&&e.length>0?(t(e),console.log(`Loaded ${e.length} documents from Supabase`)):(console.log("No documents found from API, using sample data"),t(K))}catch(e){console.error("Error fetching documents:",e),t(K)}};return Object(d.jsxs)("div",{className:"h-full flex flex-col bg-gray-50 transition-colors duration-300",children:[Object(d.jsx)("div",{className:"bg-white p-4 shadow-sm z-10 transition-colors duration-300",children:Object(d.jsx)("div",{className:"container mx-auto",children:Object(d.jsxs)("div",{className:"flex items-center justify-between",children:[Object(d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Document Management"}),Object(d.jsxs)("button",{onClick:()=>b(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",children:[Object(d.jsx)(Y.a,{className:"h-4 w-4 mr-2"}),"Manage Categories"]})]})})}),Object(d.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:Object(d.jsx)("div",{className:"container mx-auto",children:Object(d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[Object(d.jsx)("div",{className:"lg:col-span-1",children:Object(d.jsx)(D,{})}),Object(d.jsx)("div",{className:"lg:col-span-2",children:Object(d.jsx)(U,{documents:e,onView:e=>{n(e),async function(){try{const{getDocumentExtractionDetails:r,getDocumentContent:n}=await Promise.resolve().then(s.bind(null,8));try{const t=await r(e.id);return console.log("Extraction details response:",t),void u(t)}catch(t){console.warn("Failed to get extraction details, trying content endpoint:",t)}try{const t=await n(e.id);console.log("Document content response:",t);const s={extractedContent:t.content||"No content available",extractionMethod:t.extraction_method||"Unknown",qualityScore:t.quality_score||75,processingTime:t.processing_time||1250,chunks:t.chunks_count||5,warnings:[],fallbackReason:""};return void u(s)}catch(a){throw console.error("Failed to get document content:",a),a}}catch(r){console.error("Error fetching extraction details:",r);const t={extractedContent:`Unable to retrieve content for ${e.name} due to an error: ${r instanceof Error?r.message:"Unknown error"}. Please try again later.`,extractionMethod:"Error Fallback",qualityScore:30,processingTime:0,chunks:0,warnings:["Error retrieving content",r instanceof Error?r.message:"Unknown error"],fallbackReason:"Error occurred while fetching content"};u(t)}}(),o(!0)},onEdit:e=>{alert(`Edit document: ${e.name}`)},onDelete:s=>{window.confirm(`Are you sure you want to delete "${s.name}"?`)&&t(e.filter((e=>e.id!==s.id)))},onReprocess:e=>{alert(`Reprocess document: ${e.name}`)},onCategoryUpdate:e=>{t((t=>t.map((t=>t.id===e.id?e:t))))}})})]})})}),r&&Object(d.jsx)(B,{document:r,extractionDetails:i,isOpen:c,onClose:()=>o(!1),onReprocess:async(e,t)=>(alert(`Reprocessing ${e.name} with ${t}`),await new Promise((e=>setTimeout(e,2e3))),!0)}),Object(d.jsx)(X,{isOpen:m,onClose:()=>b(!1)})]})};var se=()=>{const[e,t]=Object(a.useState)(""),[s,r]=Object(a.useState)(""),[n,c]=Object(a.useState)(!1),[o,i]=Object(a.useState)(!1),[u,m]=Object(a.useState)(""),[b,g]=Object(a.useState)(!1),[x,j]=Object(a.useState)(1),[h,p]=Object(a.useState)(!0),[f,y]=Object(a.useState)(!1),[O,w]=Object(a.useState)(!0),[v,N]=Object(a.useState)(10),[k,C]=Object(a.useState)("auto"),[D,T]=Object(a.useState)("idle"),[$,F]=Object(a.useState)(0),[I,P]=Object(a.useState)([]),[L,A]=Object(a.useState)({}),[M,U]=Object(a.useState)(""),[R,q]=Object(a.useState)(""),[G,B]=Object(a.useState)({}),[W,z]=Object(a.useState)(["Official Railways","News Portals","Travel Guides","Government Sites","Educational Resources"]);Object(a.useEffect)((()=>{const t={};G.websiteUrl&&(e.trim()?H(e)||(t.websiteUrl="Please enter a valid URL"):t.websiteUrl="Website URL is required"),A(t)}),[e,G]);const V=e=>{B((t=>({...t,[e]:!0})))};Object(a.useEffect)((()=>{if(M){const e=setTimeout((()=>{U("")}),5e3);return()=>clearTimeout(e)}}),[M]),Object(a.useEffect)((()=>{if(R){const e=setTimeout((()=>{q("")}),5e3);return()=>clearTimeout(e)}}),[R]);const H=e=>{try{return new URL(e),!0}catch(t){return!1}};return Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 transition-colors duration-300",children:[Object(d.jsx)("h2",{className:"text-lg font-semibold mb-4 text-gray-900",children:"Extract Website Data"}),M&&Object(d.jsx)("div",{className:"mb-4",children:Object(d.jsxs)(S,{variant:"success",children:[Object(d.jsx)(_,{children:"Success"}),Object(d.jsx)(E,{children:M})]})}),R&&Object(d.jsx)("div",{className:"mb-4",children:Object(d.jsxs)(S,{variant:"destructive",children:[Object(d.jsx)(_,{children:"Error"}),Object(d.jsx)(E,{children:R})]})}),Object(d.jsxs)("form",{onSubmit:a=>{if(a.preventDefault(),V("websiteUrl"),Object.keys(L).length>0)return;c(!0),T("extracting"),F(10),U(""),q(""),P([]);const n=setInterval((()=>{F((e=>{const t=e+5;return t>=90?(clearInterval(n),90):t}))}),300),o={followLinks:h,extractionDepth:x,extractImages:f,extractTables:O,maxPages:v,extractorType:k,domainCategory:s||void 0};T("extracting"),Object(l.addWebsite)(e,"<EMAIL>",o).then((a=>{if(clearInterval(n),a.success){var c;F(100),T("success"),a.chunks&&a.chunks.length>0&&(P(a.chunks),console.log("Extracted chunks:",a.chunks));const n={detail:{url:e,domain:new URL(e).hostname,extractedAt:(new Date).toISOString(),id:(null===(c=a.data)||void 0===c?void 0:c.id)||`web-${Date.now()}`,status:"Success",domainCategory:s,chunks:a.chunks||[],extractedContent:a.chunks?a.chunks.map((e=>e.text)).join("\n\n"):"",chunks_extracted:a.chunks_extracted||0}},o=new CustomEvent("websiteExtracted",n);window.dispatchEvent(o),U(`Website ${e} added successfully. ${a.chunks_extracted||0} chunks extracted.`),setTimeout((()=>{t(""),r(""),B({}),A({}),T("idle"),F(0)}),5e3)}else T("error"),q(`Extraction failed: ${a.message}`)})).catch((e=>{clearInterval(n),console.error("Extraction failed:",e),U(""),q(`Extraction failed: ${e instanceof Error?e.message:"Unknown error"}`),T("error")})).finally((()=>{c(!1)}))},className:"space-y-4",children:[Object(d.jsxs)("div",{children:[Object(d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Website URL ",Object(d.jsx)("span",{className:"text-red-500",children:"*"})]}),Object(d.jsx)("input",{type:"url",value:e,onChange:e=>t(e.target.value),onBlur:()=>V("websiteUrl"),className:`w-full p-2 border ${L.websiteUrl?"border-red-500":"border-gray-300"} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"https://example.com",required:!0}),L.websiteUrl&&Object(d.jsx)("p",{className:"mt-1 text-sm text-red-500",children:L.websiteUrl})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Domain Category"}),Object(d.jsxs)("div",{className:"flex gap-2",children:[Object(d.jsxs)("select",{value:s,onChange:e=>r(e.target.value),className:"flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"Select Domain Category"}),W.map((e=>Object(d.jsx)("option",{value:e,children:e},e)))]}),Object(d.jsx)("button",{type:"button",onClick:()=>i(!0),className:"px-3 py-2 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none",children:"+ New"})]}),Object(d.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Optional. Helps organize extracted content by domain type."})]}),Object(d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-md",children:[Object(d.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[Object(d.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Extraction Options"}),Object(d.jsx)("button",{type:"button",onClick:()=>g(!b),className:"text-blue-600 hover:text-blue-800 text-sm font-medium focus:outline-none",children:b?"Hide Advanced Options":"Show Advanced Options"})]}),Object(d.jsxs)("div",{className:"space-y-2",children:[Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsx)("input",{type:"checkbox",id:"follow-links",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:h,onChange:e=>p(e.target.checked)}),Object(d.jsx)("label",{htmlFor:"follow-links",className:"ml-2 block text-sm text-gray-700",children:"Follow and extract linked pages"})]}),Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsx)("input",{type:"checkbox",id:"extract-images",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:f,onChange:e=>y(e.target.checked)}),Object(d.jsx)("label",{htmlFor:"extract-images",className:"ml-2 block text-sm text-gray-700",children:"Extract image alt text"})]}),Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsx)("input",{type:"checkbox",id:"extract-tables",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:O,onChange:e=>w(e.target.checked)}),Object(d.jsx)("label",{htmlFor:"extract-tables",className:"ml-2 block text-sm text-gray-700",children:"Extract tables as structured data"})]}),b&&Object(d.jsxs)("div",{className:"mt-3 space-y-4 pt-3 border-t border-gray-200",children:[Object(d.jsxs)("div",{children:[Object(d.jsxs)("label",{htmlFor:"extraction-depth",className:"block text-sm font-medium text-gray-700 mb-1",children:["Extraction Depth: ",x]}),Object(d.jsx)("input",{type:"range",id:"extraction-depth",min:"1",max:"5",value:x,onChange:e=>j(parseInt(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),Object(d.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[Object(d.jsx)("span",{children:"1 (Current page only)"}),Object(d.jsx)("span",{children:"5 (Deep crawl)"})]})]}),Object(d.jsxs)("div",{children:[Object(d.jsxs)("label",{htmlFor:"max-pages",className:"block text-sm font-medium text-gray-700 mb-1",children:["Maximum Pages: ",v]}),Object(d.jsx)("input",{type:"number",id:"max-pages",min:"1",max:"50",value:v,onChange:e=>N(parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),Object(d.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Maximum number of pages to extract (1-50)"})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{htmlFor:"extractor-type",className:"block text-sm font-medium text-gray-700 mb-1",children:"Extraction Method"}),Object(d.jsxs)("select",{id:"extractor-type",value:k,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[Object(d.jsx)("option",{value:"auto",children:"Auto (Recommended)"}),Object(d.jsx)("option",{value:"html",children:"HTML Only"}),Object(d.jsx)("option",{value:"browser",children:"Browser Rendering"}),Object(d.jsx)("option",{value:"api",children:"API-Based (If Available)"})]}),Object(d.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Method used to extract content from the website"})]})]})]})]}),Object(d.jsx)("div",{children:Object(d.jsx)("button",{type:"submit",disabled:n,className:`w-full px-4 py-2 text-white font-medium rounded-md ${n?"bg-green-300":"bg-green-600 hover:bg-green-700"} focus:outline-none focus:ring-2 focus:ring-green-500`,children:n?"Extracting...":"Extract Website"})}),"idle"!==D&&Object(d.jsxs)("div",{className:"mt-4",children:[Object(d.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[Object(d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"extracting"===D?"Extracting website content...":"processing"===D?"Processing extracted content...":"success"===D?"Extraction Complete":"Extraction Failed"}),Object(d.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[$,"%"]})]}),Object(d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:Object(d.jsx)("div",{className:"h-2.5 rounded-full "+("error"===D?"bg-red-600":"bg-green-600"),style:{width:`${$}%`}})})]}),"success"===D&&I.length>0&&Object(d.jsxs)("div",{className:"mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50",children:[Object(d.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Extracted Content Preview:"}),Object(d.jsxs)("div",{className:"max-h-40 overflow-y-auto text-sm text-gray-600",children:[I.slice(0,3).map(((e,t)=>{var s;return Object(d.jsxs)("div",{className:"mb-2 p-2 bg-white rounded border border-gray-200",children:[Object(d.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"website"===e.source_type?`URL: ${e.url||e.source||"Unknown"}`:`Source: ${e.source||"Unknown"}`}),Object(d.jsxs)("p",{children:[null===(s=e.text)||void 0===s?void 0:s.substring(0,150),"..."]})]},t)})),I.length>3&&Object(d.jsxs)("p",{className:"text-xs text-gray-500 text-center mt-2",children:["+ ",I.length-3," more chunks not shown"]})]})]})]}),o&&Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:Object(d.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[Object(d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Add New Domain Category"}),Object(d.jsx)("input",{type:"text",value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4",placeholder:"Enter Domain Category name",autoFocus:!0}),Object(d.jsxs)("div",{className:"flex justify-end gap-2",children:[Object(d.jsx)("button",{onClick:()=>i(!1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),Object(d.jsx)("button",{onClick:async()=>{if(u.trim()){c(!0);try{await new Promise((e=>setTimeout(e,500))),z((e=>[...e,u])),r(u),U(`Created new domain category: ${u}`)}catch(e){q(`Failed to create category: ${e instanceof Error?e.message:"Unknown error"}`)}finally{c(!1),m(""),i(!1)}}},className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:"Add Category"})]})]})})]})};var ae=e=>{let{websites:t,onView:s,onRetry:r,onDelete:n,onCategoryUpdate:c}=e;const[o,i]=Object(a.useState)("extractedAt"),[l,u]=Object(a.useState)("desc"),[m,b]=Object(a.useState)(""),[g,x]=Object(a.useState)(""),[j,h]=Object(a.useState)(""),p=e=>{o===e?u("asc"===l?"desc":"asc"):(i(e),u("asc"))},f=[...t.filter((e=>!(m&&!e.url.toLowerCase().includes(m.toLowerCase())&&!e.domain.toLowerCase().includes(m.toLowerCase()))&&((!g||e.domain===g||e.domainCategory===g)&&(!j||e.status===j))))].sort(((e,t)=>{var s,a;const r=null!==(s=e[o])&&void 0!==s?s:"",n=null!==(a=t[o])&&void 0!==a?a:"";return r<n?"asc"===l?-1:1:r>n?"asc"===l?1:-1:0})),y=e=>{if(!e)return"N/A";try{const t=new Date(e);return isNaN(t.getTime())?"Invalid date":new Intl.DateTimeFormat("en-IN",{day:"2-digit",month:"short",year:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}catch(t){return console.error("Error formatting date:",t),"Invalid date"}},O=e=>{let t;switch(e){case"Success":t="bg-green-100 text-green-800";break;case"Failed":t="bg-red-100 text-red-800";break;case"Manual Required":t="bg-yellow-100 text-yellow-800";break;default:t="bg-gray-100 text-gray-800"}return Object(d.jsx)("span",{className:`${t} px-2 py-1 rounded-full text-xs font-medium`,children:e})},w=Array.from(new Set(t.flatMap((e=>[e.domain,e.domainCategory||""])).filter(Boolean)));return Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[Object(d.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[Object(d.jsx)("h2",{className:"text-lg font-semibold",children:"Manage Websites"}),Object(d.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mt-4",children:[Object(d.jsx)("div",{className:"md:w-1/3",children:Object(d.jsx)("input",{type:"text",placeholder:"Search URLs or domains...",value:m,onChange:e=>b(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})}),Object(d.jsx)("div",{className:"md:w-1/3",children:Object(d.jsxs)("select",{value:g,onChange:e=>x(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"All Domains"}),w.map((e=>Object(d.jsx)("option",{value:e,children:e},e)))]})}),Object(d.jsx)("div",{className:"md:w-1/3",children:Object(d.jsxs)("select",{value:j,onChange:e=>h(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[Object(d.jsx)("option",{value:"",children:"All Statuses"}),Object(d.jsx)("option",{value:"Success",children:"Success"}),Object(d.jsx)("option",{value:"Failed",children:"Failed"}),Object(d.jsx)("option",{value:"Manual Required",children:"Manual Required"})]})})]})]}),Object(d.jsx)("div",{className:"overflow-x-auto",children:Object(d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[Object(d.jsx)("thead",{className:"bg-gray-50",children:Object(d.jsxs)("tr",{children:[Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>p("url"),children:["URL","url"===o&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===l?"\u2191":"\u2193"})]}),Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>p("domain"),children:["Domain","domain"===o&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===l?"\u2191":"\u2193"})]}),Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>p("extractedAt"),children:["Extracted At","extractedAt"===o&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===l?"\u2191":"\u2193"})]}),Object(d.jsxs)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>p("status"),children:["Status","status"===o&&Object(d.jsx)("span",{className:"ml-1",children:"asc"===l?"\u2191":"\u2193"})]}),Object(d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),Object(d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:f.length>0?f.map((e=>Object(d.jsxs)("tr",{className:"hover:bg-gray-50",children:[Object(d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:Object(d.jsx)("div",{className:"text-sm font-medium text-blue-600 hover:underline",children:Object(d.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",children:e.url.length>50?`${e.url.substring(0,50)}...`:e.url})})}),Object(d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[Object(d.jsx)("div",{className:"text-sm text-gray-900",children:e.domain}),e.domainCategory&&Object(d.jsx)("div",{className:"text-xs text-gray-500",children:e.domainCategory})]}),Object(d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:y(e.extractedAt)}),Object(d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:O(e.status)}),Object(d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:Object(d.jsxs)("div",{className:"flex space-x-3",children:[Object(d.jsx)("button",{onClick:()=>s(e),className:"text-blue-600 hover:text-blue-900",children:"View"}),Object(d.jsx)("button",{onClick:()=>r(e),className:"text-yellow-600 hover:text-yellow-900",children:"Retry"}),Object(d.jsx)("button",{onClick:()=>n(e),className:"text-red-600 hover:text-red-900",children:"Delete"})]})})]},e.id))):Object(d.jsx)("tr",{children:Object(d.jsx)("td",{colSpan:5,className:"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500",children:"No websites found"})})})]})})]})};var re=e=>{let{website:t,extractionDetails:s,isOpen:r,onClose:n,onRetry:c}=e;const[o,i]=Object(a.useState)("content"),[l,u]=Object(a.useState)("Trafilatura"),[m,b]=Object(a.useState)(!1);if(!r)return null;return Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col",children:[Object(d.jsxs)("div",{className:"p-4 border-b border-gray-200 flex justify-between items-center",children:[Object(d.jsxs)("div",{children:[Object(d.jsx)("h2",{className:"text-lg font-semibold",children:Object(d.jsx)("a",{href:t.url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:t.url})}),Object(d.jsx)("p",{className:"text-sm text-gray-500",children:t.domain})]}),Object(d.jsx)("button",{onClick:n,className:"text-gray-500 hover:text-gray-700 focus:outline-none",children:Object(d.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),Object(d.jsxs)("div",{className:"flex border-b border-gray-200",children:[Object(d.jsx)("button",{className:"px-4 py-2 font-medium "+("content"===o?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"),onClick:()=>i("content"),children:"Extracted Content"}),Object(d.jsx)("button",{className:"px-4 py-2 font-medium "+("details"===o?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"),onClick:()=>i("details"),children:"Processing Details"})]}),Object(d.jsx)("div",{className:"flex-1 overflow-auto p-4",children:"content"===o?Object(d.jsx)("div",{className:"prose max-w-none",children:Object(d.jsx)("div",{className:"whitespace-pre-wrap text-sm bg-gray-50 p-4 rounded-md overflow-auto h-[60vh]",children:t.extractedContent||s.extractedContent||(t.chunks&&t.chunks.length>0?t.chunks.map((e=>e.text)).join("\n\n"):"No extracted content available")})}):Object(d.jsxs)("div",{className:"space-y-4",children:[Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Extraction Method"}),Object(d.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:s.extractionMethod})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Content Quality"}),Object(d.jsx)("div",{className:"mt-1",children:(()=>{const{contentQuality:e}=s;let t;return t=e>=80?"bg-green-500":e>=60?"bg-green-400":e>=40?"bg-yellow-500":e>=20?"bg-orange-500":"bg-red-500",Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 mr-2",children:Object(d.jsx)("div",{className:`h-2.5 rounded-full ${t}`,style:{width:`${e}%`}})}),Object(d.jsxs)("span",{className:"text-sm font-medium",children:[e,"%"]})]})})()})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Processing Time"}),Object(d.jsxs)("p",{className:"mt-1 text-sm text-gray-600",children:[s.processingTime," ms (",(s.processingTime/1e3).toFixed(2)," seconds)"]})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Chunks Created"}),Object(d.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:s.chunks})]}),s.fallbackHistory.length>0&&Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Fallback History"}),Object(d.jsx)("ol",{className:"mt-1 text-sm text-gray-600 list-decimal pl-5",children:s.fallbackHistory.map(((e,t)=>Object(d.jsxs)("li",{children:[e,t===s.fallbackHistory.length-1&&" (used)"]},t)))})]}),s.warnings.length>0&&Object(d.jsxs)("div",{children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Warnings"}),Object(d.jsx)("ul",{className:"mt-1 text-sm text-gray-600 list-disc pl-5",children:s.warnings.map(((e,t)=>Object(d.jsx)("li",{className:"text-yellow-600",children:e},t)))})]}),Object(d.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:"Retry Extraction"}),Object(d.jsxs)("div",{className:"mt-2 flex items-end gap-3",children:[Object(d.jsxs)("div",{className:"flex-1",children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Extraction Parser"}),Object(d.jsxs)("select",{value:l,onChange:e=>u(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",disabled:m,children:[Object(d.jsx)("option",{value:"Trafilatura",children:"Trafilatura"}),Object(d.jsx)("option",{value:"BeautifulSoup",children:"BeautifulSoup"}),Object(d.jsx)("option",{value:"Scrapy",children:"Scrapy"}),Object(d.jsx)("option",{value:"Puppeteer",children:"Puppeteer"})]})]}),Object(d.jsx)("button",{onClick:async()=>{b(!0);try{await c(t,l)}catch(e){console.error("Error re-extracting website:",e)}finally{b(!1)}},disabled:m,className:"px-4 py-2 rounded-md "+(m?"bg-gray-300 cursor-not-allowed":"bg-green-600 text-white hover:bg-green-700"),children:m?"Processing...":"Reprocess Website"})]})]})]})})]})})};var ne=e=>{let{isOpen:t,onClose:s}=e;const[r,n]=Object(a.useState)([]),[c,o]=Object(a.useState)(!1),[i,u]=Object(a.useState)(null),[m,b]=Object(a.useState)(null),[g,x]=Object(a.useState)(!1),[j,h]=Object(a.useState)({name:"",description:"",sort_order:0});Object(a.useEffect)((()=>{t&&p()}),[t]);const p=async()=>{o(!0),u(null);try{const e=await Object(l.getWebsiteCategories)();n(e)}catch(i){console.error("Error loading website categories:",i),u("Failed to load website categories")}finally{o(!1)}},f=()=>{u(null),b(null)},y=()=>{x(!1),h({name:"",description:"",sort_order:0}),f(),s()};return t?Object(d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:Object(d.jsx)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:Object(d.jsxs)("div",{className:"p-6",children:[Object(d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[Object(d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Website Category Management"}),Object(d.jsx)("button",{onClick:y,className:"text-gray-400 hover:text-gray-600",children:Object(d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:Object(d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),i&&Object(d.jsxs)("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex justify-between items-center",children:[Object(d.jsx)("span",{children:i}),Object(d.jsx)("button",{onClick:f,className:"text-red-500 hover:text-red-700",children:Object(d.jsx)(F.a,{className:"h-4 w-4"})})]}),m&&Object(d.jsxs)("div",{className:"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded flex justify-between items-center",children:[Object(d.jsx)("span",{children:m}),Object(d.jsx)("button",{onClick:f,className:"text-green-500 hover:text-green-700",children:Object(d.jsx)(F.a,{className:"h-4 w-4"})})]}),Object(d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[Object(d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Website Categories"}),Object(d.jsxs)("button",{onClick:()=>x(!g),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center",children:[Object(d.jsx)(Z.a,{className:"h-4 w-4 mr-2"}),"Add Category"]})]}),g&&Object(d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg space-y-4 mb-6",children:[Object(d.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Add Website Category"}),Object(d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name *"}),Object(d.jsx)("input",{type:"text",value:j.name,onChange:e=>h((t=>({...t,name:e.target.value}))),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Category name"})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort Order"}),Object(d.jsx)("input",{type:"number",value:j.sort_order||0,onChange:e=>h((t=>({...t,sort_order:parseInt(e.target.value)||0}))),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),Object(d.jsxs)("div",{className:"md:col-span-2",children:[Object(d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),Object(d.jsx)("textarea",{value:j.description||"",onChange:e=>h((t=>({...t,description:e.target.value}))),className:"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",rows:3,placeholder:"Category description"})]})]}),Object(d.jsxs)("div",{className:"flex space-x-2",children:[Object(d.jsxs)("button",{onClick:async()=>{try{u(null),await Object(l.createWebsiteCategory)(j),h({name:"",description:"",sort_order:0}),x(!1),b("Website category created successfully"),p()}catch(i){console.error("Error creating website category:",i),u(i instanceof Error?i.message:"Failed to create website category")}},disabled:!j.name.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center",children:[Object(d.jsx)(L.a,{className:"h-4 w-4 mr-2"}),"Create Category"]}),Object(d.jsxs)("button",{onClick:()=>x(!1),className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:[Object(d.jsx)(F.a,{className:"h-4 w-4 mr-2"}),"Cancel"]})]})]}),c?Object(d.jsxs)("div",{className:"text-center py-8",children:[Object(d.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),Object(d.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading categories..."})]}):Object(d.jsx)("div",{className:"space-y-2",children:r.length>0?r.map((e=>Object(d.jsx)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50",children:Object(d.jsxs)("div",{className:"flex-1",children:[Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsx)("span",{className:"font-medium text-gray-900",children:e.name}),Object(d.jsxs)("span",{className:"ml-2 text-xs text-gray-500",children:["Order: ",e.sort_order]}),e.is_active&&Object(d.jsx)("span",{className:"ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:"Active"})]}),e.description&&Object(d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),Object(d.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Created: ",new Date(e.created_at).toLocaleDateString()]})]})},e.id))):Object(d.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No website categories found"})}),Object(d.jsx)("div",{className:"flex justify-end mt-6 pt-4 border-t",children:Object(d.jsx)("button",{onClick:y,className:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400",children:"Close"})})]})})}):null};const ce=[{id:"1",url:"https://indianrailways.gov.in/railwayboard/",domain:"indianrailways.gov.in",extractedAt:"2025-05-01T14:30:00Z",status:"Success",submittedBy:"<EMAIL>",domainCategory:"Official Railways"},{id:"2",url:"https://irctc.co.in/nget/train-search",domain:"irctc.co.in",extractedAt:"2025-04-29T11:45:00Z",status:"Success",submittedBy:"<EMAIL>",domainCategory:"Official Railways"},{id:"3",url:"https://www.trainman.in/railway-stations",domain:"trainman.in",extractedAt:"2025-04-25T09:15:00Z",status:"Success",submittedBy:"<EMAIL>",domainCategory:"Travel Guides"},{id:"4",url:"https://rail-info.indianrailways.gov.in/mntes/",domain:"rail-info.indianrailways.gov.in",extractedAt:"2025-04-20T16:30:00Z",status:"Failed",submittedBy:"<EMAIL>",domainCategory:"Official Railways"},{id:"5",url:"https://www.ndtv.com/topic/indian-railways",domain:"ndtv.com",extractedAt:"2025-05-02T10:20:00Z",status:"Manual Required",submittedBy:"<EMAIL>",domainCategory:"News Portals"}],oe={extractionMethod:"Trafilatura",fallbackHistory:["Trafilatura","BeautifulSoup"],contentQuality:85,warnings:["Dynamic content may be missing","Some tables could not be extracted completely"],extractedContent:"\n# Indian Railways\n\nThe Indian Railways is a statutory body under the jurisdiction of Ministry of Railways, Government of India that operates India's national railway system. It manages the fourth-largest railway network in the world by size, with a route length of 67,956 km (42,226 mi) and total track length of 99,235 km (61,662 mi) as of March 2020. As of the end of 2019, 71.23% of its 121,407 kilometres of track is electrified.\n\n## Passenger Services\n\nIndian Railways operates more than 20,000 passenger trains daily, on both long-distance and suburban routes, from approximately 7,349 stations across India.\n\n### Train Categories\n\nIndian Railways operates different categories of trains:\n\n1. **Shatabdi Express**: 160 km/h (99 mph) trains that connect major cities and state capitals over short distances, typically within a day's journey.\n2. **Rajdhani Express**: 130 to 140 km/h (81 to 87 mph) trains that link major state capitals to New Delhi.\n3. **Duronto Express**: 130 km/h (81 mph) point-to-point non-stop trains designed to connect major cities.\n4. **Vande Bharat Express**: 180 km/h (110 mph) semi-high-speed train, currently running on select routes.\n5. **Jan Shatabdi Express**: 110 to 120 km/h (68 to 75 mph) intercity services serving middle-distance connectivity.\n6. **Garib Rath Express**: 130 km/h (81 mph) trains that aim to provide affordable air-conditioned travel.\n7. **Mail/Express Trains**: Regular long-distance train services operating throughout the country.\n8. **Passenger & Fast Passenger Trains**: Short-distance services connecting smaller towns and villages.\n9. **Suburban Trains**: High-frequency train services in major metropolitan areas.\n\n### Ticket Booking\n\nTicket booking is primarily done through the Indian Railway Catering and Tourism Corporation (IRCTC) portal. Tickets can be booked up to 120 days in advance.\n\n## Freight Services\n\nIndian Railways is one of the world's largest freight carriers, moving over 1.23 billion tonnes of freight annually in FY 2019-20.\n\nMajor categories of freight traffic:\n- Coal (and petroleum products): 49%\n- Raw materials: 17%\n- Iron and steel: 12%\n- Cement: the 10%\n- Food grains: 7%\n- Fertilizers: 5%\n\n## Current Projects\n\n### High-Speed Rail Projects\n\n1. **Mumbai\u2013Ahmedabad Corridor**: India's first high-speed rail line (bullet train) with operational speeds of 320 km/h. Currently under construction.\n2. **National High Speed Rail Corporation Limited (NHSRCL)**: Responsible for implementing high-speed rail projects in the country.\n\n### Infrastructure Development\n\n1. **Dedicated Freight Corridors (DFCs)**\n   - Eastern DFC: 1,856 km from Ludhiana to Dankuni\n   - Western DFC: 1,504 km from Dadri to Jawaharlal Nehru Port Trust\n\n2. **Station Redevelopment Program**\n   - Modernizing and upgrading major railway stations across the country\n   - Public-private partnership model for development\n  ",processingTime:2850,chunks:8};var ie=()=>{const[e,t]=Object(a.useState)(ce),[r,n]=Object(a.useState)(null),[c,o]=Object(a.useState)(!1),[i,u]=Object(a.useState)(oe),[m,b]=Object(a.useState)(!1);Object(a.useEffect)((()=>{g();const e=e=>{const s=e.detail;t((e=>[s,...e])),setTimeout((()=>g()),2e3)};return window.addEventListener("websiteExtracted",e),()=>{window.removeEventListener("websiteExtracted",e)}}),[]);const g=async()=>{try{const e=await Object(l.getWebsites)();e&&e.length>0?(t(e),console.log(`Loaded ${e.length} websites from Supabase`)):(console.log("No websites found from API, using sample data"),t(ce))}catch(e){console.error("Error fetching websites:",e),t(ce)}};return Object(d.jsxs)("div",{className:"h-full flex flex-col bg-gray-50 transition-colors duration-300",children:[Object(d.jsx)("div",{className:"bg-white p-4 shadow-sm z-10 transition-colors duration-300",children:Object(d.jsx)("div",{className:"container mx-auto",children:Object(d.jsxs)("div",{className:"flex items-center justify-between",children:[Object(d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Website Management"}),Object(d.jsxs)("button",{onClick:()=>b(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",children:[Object(d.jsx)(Y.a,{className:"h-4 w-4 mr-2"}),"Manage Categories"]})]})})}),Object(d.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:Object(d.jsx)("div",{className:"container mx-auto",children:Object(d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[Object(d.jsx)("div",{className:"lg:col-span-1",children:Object(d.jsx)(se,{})}),Object(d.jsx)("div",{className:"lg:col-span-2",children:Object(d.jsx)(ae,{websites:e,onView:e=>{n(e),async function(){try{const{getWebsiteExtractionDetails:r,getWebsiteContent:n}=await Promise.resolve().then(s.bind(null,8));try{const t=await r(e.id);return console.log("Website extraction details response:",t),void u(t)}catch(t){console.warn("Failed to get website extraction details, trying content endpoint:",t)}try{const t=await n(e.id);console.log("Website content response:",t);const s={extractedContent:t.content||t.text||"No content available",extractionMethod:t.extraction_method||"Direct Scraping",processingTime:t.processing_time||850,warnings:t.warnings||[],fallbackHistory:t.fallback_history||["API Response"],contentQuality:t.quality_score||80,chunks:t.chunks||5};return void u(s)}catch(a){throw console.error("Failed to get website content:",a),a}}catch(r){console.error("All API attempts failed, using fallback data:",r);const t={extractedContent:`Unable to retrieve content for ${e.url} due to an error: ${r instanceof Error?r.message:"Unknown error"}. Please try again later.`,extractionMethod:"Error Fallback",processingTime:0,warnings:["Error retrieving content",r instanceof Error?r.message:"Unknown error"],fallbackHistory:["API Error","Error Handler"],contentQuality:30,chunks:0};u(t)}}(),o(!0)},onRetry:e=>{alert(`Retry extraction for: ${e.url}`)},onDelete:s=>{window.confirm(`Are you sure you want to delete "${s.url}"?`)&&t(e.filter((e=>e.id!==s.id)))},onCategoryUpdate:e=>{t((t=>t.map((t=>t.id===e.id?e:t))))}})})]})})}),r&&Object(d.jsx)(re,{website:r,extractionDetails:i,isOpen:c,onClose:()=>o(!1),onRetry:async(e,t)=>(alert(`Retrying extraction of ${e.url} with ${t}`),await new Promise((e=>setTimeout(e,1500))),!0)}),Object(d.jsx)(ne,{isOpen:m,onClose:()=>b(!1)})]})};var le=()=>{const[e,t]=Object(a.useState)([]),[s,r]=Object(a.useState)(""),[n,c]=Object(a.useState)(!0),[o,i]=Object(a.useState)(null);Object(a.useEffect)((()=>{(async()=>{try{const e=await Object(l.getFeedbackEmails)();t(e.emails||[])}catch(e){console.error("Error loading feedback emails:",e)}finally{c(!1)}})()}),[]);const u=async e=>{c(!0);try{const s=await Object(l.updateFeedbackEmails)(e);s.success?(t(e),i({success:!0,message:"Feedback notification emails updated successfully."})):i({success:!1,message:s.message||"Failed to update emails."})}catch(s){i({success:!1,message:`Error: ${s instanceof Error?s.message:"Unknown error"}`})}finally{c(!1),setTimeout((()=>i(null)),3e3)}};return Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6 transition-colors duration-300",children:[Object(d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Feedback Notification Settings"}),Object(d.jsx)("p",{className:"text-gray-600 mb-4",children:"Configure email addresses to receive notifications when users provide negative feedback."}),o&&Object(d.jsx)("div",{className:"p-3 mb-4 rounded-md transition-colors duration-300 "+(o.success?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"),children:o.message}),Object(d.jsxs)("div",{className:"mb-4",children:[Object(d.jsx)("h4",{className:"font-medium text-gray-700 mb-2",children:"Current Notification Emails"}),n?Object(d.jsx)("div",{className:"text-gray-500",children:"Loading..."}):0===e.length?Object(d.jsx)("div",{className:"text-gray-500",children:"No notification emails configured."}):Object(d.jsx)("ul",{className:"space-y-2",children:e.map(((t,s)=>Object(d.jsxs)("li",{className:"flex justify-between items-center p-2 bg-gray-50 rounded transition-colors duration-300",children:[Object(d.jsx)("span",{className:"text-gray-900",children:t}),Object(d.jsx)("button",{onClick:()=>(t=>{const s=e.filter((e=>e!==t));u(s)})(t),className:"text-red-600 hover:text-red-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 rounded",title:"Remove email",disabled:n,children:"\u2715"})]},s)))})]}),Object(d.jsxs)("div",{className:"mt-4",children:[Object(d.jsx)("h4",{className:"font-medium text-gray-700 mb-2",children:"Add New Email"}),Object(d.jsxs)("div",{className:"flex",children:[Object(d.jsx)("input",{type:"email",value:s,onChange:e=>r(e.target.value),placeholder:"<EMAIL>",className:"flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300",disabled:n||e.length>=5}),Object(d.jsx)("button",{onClick:()=>{if(!s||!s.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/))return void i({success:!1,message:"Please enter a valid email address."});if(e.includes(s))return void i({success:!1,message:"This email is already in the list."});if(e.length>=5)return void i({success:!1,message:"Maximum 5 email addresses allowed."});const t=[...e,s];u(t),r("")},className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",disabled:n||!s||e.length>=5,children:"Add"})]}),e.length>=5&&Object(d.jsx)("p",{className:"text-xs text-amber-600 mt-1",children:"Maximum 5 email addresses allowed."}),Object(d.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"These emails will receive notifications when users provide negative feedback on AI answers."})]})]})};const de=[{id:"gemini",name:"Google Gemini",logo:"\ud83e\udde0",provider:"Google",enabled:!0,usedFor:{answering:!0,embeddings:!0,chunkAnalysis:!0},isDefault:!0},{id:"chatgpt",name:"OpenAI ChatGPT",logo:"\ud83e\udd16",provider:"OpenAI",enabled:!0,usedFor:{answering:!0,embeddings:!0,chunkAnalysis:!1},isDefault:!1},{id:"groq",name:"Groq (Mixtral, LLaMA3)",logo:"\u26a1",provider:"Groq",enabled:!0,usedFor:{answering:!0,embeddings:!1,chunkAnalysis:!1},isDefault:!1},{id:"deepseek",name:"DeepSeek",logo:"\ud83d\udd0d",provider:"DeepSeek",enabled:!0,usedFor:{answering:!0,embeddings:!1,chunkAnalysis:!1},isDefault:!1},{id:"qwen",name:"Qwen",logo:"\ud83c\udf10",provider:"Alibaba",enabled:!1,usedFor:{answering:!0,embeddings:!1,chunkAnalysis:!1},isDefault:!1},{id:"huggingface",name:"HuggingFace Inference API",logo:"\ud83e\udd17",provider:"HuggingFace",enabled:!1,usedFor:{answering:!0,embeddings:!1,chunkAnalysis:!1},isDefault:!1},{id:"ollama",name:"Ollama (Local LLM runner)",logo:"\ud83d\udc11",provider:"Local",enabled:!1,usedFor:{answering:!0,embeddings:!1,chunkAnalysis:!1},isDefault:!1}],ue=e=>{let{model:t,index:s,totalModels:a,moveModelUp:r,moveModelDown:n,toggleModel:c,setDefaultModel:o,updateUsedFor:i}=e;return Object(d.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg mb-3 transition-colors duration-300 "+(t.enabled?"bg-white":"opacity-70 bg-gray-50"),children:[Object(d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[Object(d.jsxs)("div",{className:"flex items-center space-x-3",children:[Object(d.jsx)("span",{className:"text-2xl",children:t.logo}),Object(d.jsx)("h3",{className:"font-medium text-gray-900",children:t.name}),Object(d.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 rounded-full px-2 py-1",children:t.provider})]}),Object(d.jsxs)("div",{className:"flex space-x-2",children:[Object(d.jsx)("button",{onClick:()=>r(s),disabled:0===s,className:"p-1 rounded transition-colors duration-200 "+(0===s?"text-gray-300":"text-blue-600 hover:bg-blue-50"),"aria-label":"Move up",children:"\u2b06\ufe0f"}),Object(d.jsx)("button",{onClick:()=>n(s),disabled:s===a-1,className:"p-1 rounded transition-colors duration-200 "+(s===a-1?"text-gray-300":"text-blue-600 hover:bg-blue-50"),"aria-label":"Move down",children:"\u2b07\ufe0f"})]}),Object(d.jsxs)("div",{className:"flex items-center space-x-3",children:[Object(d.jsxs)("label",{className:"inline-flex items-center cursor-pointer",children:[Object(d.jsx)("input",{type:"checkbox",className:"sr-only peer",checked:t.enabled,onChange:()=>c(t.id,!t.enabled)}),Object(d.jsx)("div",{className:"relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"}),Object(d.jsx)("span",{className:"ms-2 text-sm font-medium text-gray-900",children:"Enabled"})]}),Object(d.jsxs)("label",{className:"inline-flex items-center cursor-pointer",children:[Object(d.jsx)("input",{type:"radio",name:"default-model",className:"form-radio h-4 w-4 text-blue-600",checked:t.isDefault,onChange:()=>o(t.id)}),Object(d.jsx)("span",{className:"ms-2 text-sm font-medium text-gray-900",children:"Default"})]})]})]}),Object(d.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-2 pt-2 border-t border-gray-100",children:[Object(d.jsxs)("label",{className:"flex items-center space-x-2",children:[Object(d.jsx)("input",{type:"checkbox",className:"form-checkbox h-4 w-4 text-blue-600",checked:t.usedFor.answering,onChange:e=>i(t.id,"answering",e.target.checked)}),Object(d.jsx)("span",{className:"ms-2 text-sm font-medium text-gray-700",children:"Answering"})]}),Object(d.jsxs)("label",{className:"flex items-center space-x-2",children:[Object(d.jsx)("input",{type:"checkbox",className:"form-checkbox h-4 w-4 text-blue-600",checked:t.usedFor.embeddings,onChange:e=>i(t.id,"embeddings",e.target.checked)}),Object(d.jsx)("span",{className:"ms-2 text-sm font-medium text-gray-700",children:"Embeddings"})]}),Object(d.jsxs)("label",{className:"flex items-center space-x-2",children:[Object(d.jsx)("input",{type:"checkbox",className:"form-checkbox h-4 w-4 text-blue-600",checked:t.usedFor.chunkAnalysis,onChange:e=>i(t.id,"chunkAnalysis",e.target.checked)}),Object(d.jsx)("span",{className:"ms-2 text-sm font-medium text-gray-700",children:"Chunk Analysis"})]})]})]})},me=()=>{const[e,t]=Object(a.useState)({ocrFallback:!0,multiStageFallback:!1,chunkSize:1e3,chunkOverlap:200}),s=e=>{const{name:s,type:a,checked:r,value:n}=e.target;t((e=>({...e,[s]:"checkbox"===a?r:parseInt(n)||0})))};return Object(d.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg bg-white mb-6 transition-colors duration-300",children:[Object(d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Document Processing Settings"}),Object(d.jsxs)("div",{className:"space-y-4",children:[Object(d.jsxs)("label",{className:"flex items-center space-x-2",children:[Object(d.jsx)("input",{type:"checkbox",name:"ocrFallback",checked:e.ocrFallback,onChange:s,className:"form-checkbox h-4 w-4 text-blue-600"}),Object(d.jsx)("span",{className:"text-sm text-gray-700",children:"OCR fallback when text extraction fails"})]}),Object(d.jsxs)("label",{className:"flex items-center space-x-2",children:[Object(d.jsx)("input",{type:"checkbox",name:"multiStageFallback",checked:e.multiStageFallback,onChange:s,className:"form-checkbox h-4 w-4 text-blue-600"}),Object(d.jsx)("span",{className:"text-sm text-gray-700",children:"Multi-stage fallback (PyMuPDF \u2192 PDF.js \u2192 OCR)"})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm text-gray-700 mb-1",children:"Chunk size (characters)"}),Object(d.jsx)("input",{type:"number",name:"chunkSize",value:e.chunkSize,onChange:s,min:"100",max:"5000",className:"form-input mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm text-gray-700 mb-1",children:"Chunk overlap (characters)"}),Object(d.jsx)("input",{type:"number",name:"chunkOverlap",value:e.chunkOverlap,onChange:s,min:"0",max:"1000",className:"form-input mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"})]})]})]})},be=()=>{const[e,t]=Object(a.useState)({compactView:!1,highlightSources:!0,maxSourcesShown:5,accentColor:"#3b82f6"}),s=e=>{const{name:s,type:a,value:r}=e.target,n=e.target.checked;t((e=>({...e,[s]:"checkbox"===a?n:r})))};return Object(d.jsxs)("div",{className:"p-4 border rounded-lg bg-white mb-6 transition-colors duration-300",children:[Object(d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Display Preferences"}),Object(d.jsx)("div",{className:"space-y-6",children:Object(d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[Object(d.jsxs)("label",{className:"flex items-center space-x-2",children:[Object(d.jsx)("input",{type:"checkbox",name:"compactView",checked:e.compactView,onChange:s,className:"form-checkbox h-4 w-4 text-blue-600"}),Object(d.jsx)("span",{className:"text-sm text-gray-700",children:"Compact view"})]}),Object(d.jsxs)("label",{className:"flex items-center space-x-2",children:[Object(d.jsx)("input",{type:"checkbox",name:"highlightSources",checked:e.highlightSources,onChange:s,className:"form-checkbox h-4 w-4 text-blue-600"}),Object(d.jsx)("span",{className:"text-sm text-gray-700",children:"Highlight source references"})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm text-gray-700 mb-1",children:"Max sources to display"}),Object(d.jsxs)("select",{name:"maxSourcesShown",value:e.maxSourcesShown,onChange:s,className:"form-select mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",children:[Object(d.jsx)("option",{value:1,children:"1 source"}),Object(d.jsx)("option",{value:3,children:"3 sources"}),Object(d.jsx)("option",{value:5,children:"5 sources"}),Object(d.jsx)("option",{value:10,children:"10 sources"}),Object(d.jsx)("option",{value:0,children:"All sources"})]})]}),Object(d.jsxs)("div",{children:[Object(d.jsx)("label",{className:"block text-sm text-gray-700 mb-1",children:"Accent color"}),Object(d.jsx)("input",{type:"color",name:"accentColor",value:e.accentColor,onChange:s,className:"form-input h-8 w-full rounded cursor-pointer border-gray-300"})]})]})})]})};var ge=()=>{const[e,t]=Object(a.useState)(de),[s,n]=Object(a.useState)(5);r.a.useEffect((()=>{const e=localStorage.getItem("railGptModels"),s=localStorage.getItem("railGptVisibleCount");e&&t(JSON.parse(e)),s&&n(parseInt(s,10))}),[]);const c=s=>{if(s<=0)return;const a=[...e],r=a[s];a[s]=a[s-1],a[s-1]=r,t(a)},o=s=>{if(s>=e.length-1)return;const a=[...e],r=a[s];a[s]=a[s+1],a[s+1]=r,t(a)},i=(s,a)=>{t(e.map((e=>e.id===s?{...e,enabled:a}:e)))},l=s=>{t(e.map((e=>({...e,isDefault:e.id===s}))))},u=(s,a,r)=>{t(e.map((e=>e.id===s?{...e,usedFor:{...e.usedFor,[a]:r}}:e)))};return Object(d.jsxs)("div",{className:"h-full flex flex-col bg-gray-50 transition-colors duration-300",children:[Object(d.jsx)("div",{className:"bg-white p-4 shadow-sm z-10 transition-colors duration-300",children:Object(d.jsx)("div",{className:"container mx-auto",children:Object(d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"RailGPT Settings"})})}),Object(d.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:Object(d.jsx)("div",{className:"container mx-auto",children:Object(d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[Object(d.jsxs)("div",{className:"lg:col-span-2",children:[Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6 transition-colors duration-300",children:[Object(d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[Object(d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"LLM Models Configuration"}),Object(d.jsxs)("div",{className:"flex items-center space-x-2",children:[Object(d.jsx)("label",{className:"text-sm text-gray-700",children:"Models in dropdown:"}),Object(d.jsxs)("select",{value:s,onChange:e=>{n(parseInt(e.target.value,10))},className:"form-select rounded border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",children:[Object(d.jsx)("option",{value:1,children:"1"}),Object(d.jsx)("option",{value:2,children:"2"}),Object(d.jsx)("option",{value:3,children:"3"}),Object(d.jsx)("option",{value:4,children:"4"}),Object(d.jsx)("option",{value:5,children:"5"})]})]})]}),Object(d.jsx)("p",{className:"text-gray-600 mb-4",children:"Configure which LLM models are available for use and how they're used in the application. Drag to reorder, and select which models should appear in the dropdown menu."}),Object(d.jsx)("div",{className:"space-y-3",children:e.map(((t,s)=>Object(d.jsx)(ue,{model:t,index:s,totalModels:e.length,moveModelUp:c,moveModelDown:o,toggleModel:i,setDefaultModel:l,updateUsedFor:u},t.id)))}),Object(d.jsxs)("p",{className:"text-sm text-gray-500 mt-4",children:["First ",s," enabled models will appear in the chat dropdown."]}),Object(d.jsx)("button",{onClick:()=>{localStorage.setItem("railGptModels",JSON.stringify(e)),localStorage.setItem("railGptVisibleCount",s.toString()),alert("Settings saved successfully!")},className:"mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",children:"Save Model Settings"})]}),Object(d.jsx)(me,{}),Object(d.jsx)(be,{})]}),Object(d.jsx)("div",{className:"lg:col-span-1",children:Object(d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 transition-colors duration-300",children:[Object(d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"About"}),Object(d.jsx)("p",{className:"text-gray-600 mb-4",children:"RailGPT is an intelligent document query system designed for the Indian Railway community."}),Object(d.jsx)("p",{className:"text-gray-600 mb-4",children:"Configure your preferred LLM models, processing settings, and display preferences to customize your experience."}),Object(d.jsx)(le,{})]})})]})})})]})};var xe=e=>{let{url:t,fileName:s}=e;const[r,n]=Object(a.useState)("iframe"),[c,o]=Object(a.useState)(!0),[i,l]=Object(a.useState)(null),u=()=>{o(!1),l(null)},m=()=>{o(!1),l("Failed to load PDF in viewer")};return Object(d.jsxs)("div",{className:"flex flex-col items-center",children:[Object(d.jsxs)("div",{className:"mb-4 w-full flex justify-between items-center bg-gray-100 p-2 rounded-lg",children:[Object(d.jsx)("div",{className:"text-sm font-medium text-gray-700",children:s}),Object(d.jsxs)("div",{className:"flex space-x-2",children:[Object(d.jsx)("button",{onClick:()=>n("iframe"),className:"px-3 py-1 rounded text-sm "+("iframe"===r?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"Viewer"}),Object(d.jsx)("button",{onClick:()=>n("embed"),className:"px-3 py-1 rounded text-sm "+("embed"===r?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"Embed"}),Object(d.jsx)("button",{onClick:()=>window.open(t,"_blank"),className:"px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600",children:"New Tab"}),Object(d.jsx)("a",{href:t,download:s,className:"px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700",children:"Download"})]})]}),Object(d.jsxs)("div",{className:"border border-gray-300 rounded-lg overflow-hidden w-full bg-gray-50 relative",children:[c&&Object(d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10",children:Object(d.jsxs)("div",{className:"flex items-center space-x-2",children:[Object(d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),Object(d.jsx)("span",{className:"text-gray-600",children:"Loading PDF..."})]})}),i&&Object(d.jsx)("div",{className:"flex items-center justify-center h-60 text-red-600 text-center p-4",children:Object(d.jsxs)("div",{children:[Object(d.jsx)("p",{className:"font-semibold",children:"Failed to load PDF"}),Object(d.jsx)("p",{className:"text-sm mt-2",children:i}),Object(d.jsxs)("div",{className:"mt-4 space-y-2",children:[Object(d.jsx)("button",{onClick:()=>window.open(t,"_blank"),className:"block w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Open in New Tab"}),Object(d.jsx)("a",{href:t,download:s,className:"block w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-center",children:"Download PDF"})]})]})}),"iframe"===r&&!i&&Object(d.jsx)("iframe",{src:`${t}#toolbar=1&navpanes=1&scrollbar=1`,width:"100%",height:"600px",onLoad:u,onError:m,title:`PDF Viewer - ${s}`,className:"border-0"}),"embed"===r&&!i&&Object(d.jsx)("embed",{src:t,type:"application/pdf",width:"100%",height:"600px",onLoad:u,onError:m,title:`PDF Embed - ${s}`})]}),Object(d.jsx)("div",{className:"mt-2 text-xs text-gray-500 text-center",children:"If the PDF doesn't load, try opening it in a new tab or downloading it directly."})]})};var je=()=>{const e=Object(i.f)(),[t,s]=Object(a.useState)(""),[r,n]=Object(a.useState)(""),[c,o]=Object(a.useState)(""),[u,m]=Object(a.useState)(""),[b,g]=Object(a.useState)(!0),[x,j]=Object(a.useState)(""),[h,p]=Object(a.useState)(!1);Object(a.useEffect)((()=>{const t=new URLSearchParams(e.search).get("file");if(t){var a;console.log(`DocumentViewer: Loading file ${t}`),n(t);const e=(null===(a=t.split(".").pop())||void 0===a?void 0:a.toLowerCase())||"";if(console.log(`DocumentViewer: Detected file extension: ${e}`),o(e),"pdf"===e){console.log("DocumentViewer: PDF file detected, setting up PDF viewer");const e=`${l.API_URL}/api/documents/view/${encodeURIComponent(t)}`;s(e),p(!1),f(t)}else console.log("DocumentViewer: Non-PDF file detected, showing extracted content"),p(!0),f(t);g(!1)}else m("No file specified in the URL"),g(!1)}),[e]);const f=async e=>{try{g(!0),console.log(`Fetching content for filename: ${e}`);const a=await fetch(`${l.API_URL}/api/documents`);if(a.ok){const r=await a.json();console.log(`Found ${r.length} documents in database`);let c=null;if(c=r.find((t=>t.name===e)),c||(c=r.find((t=>t.name&&t.name.includes(e.replace(/\.[^/.]+$/,""))))),c||(c=r.find((t=>t.name&&e.includes(t.name.replace(/\.[^/.]+$/,""))))),c||(c=r.find((t=>t.filePath&&t.filePath.includes(e)))),!c&&r.length>0){console.log("No exact match found, checking if filename matches any document pattern...");for(const t of r)if(console.log(`Checking document: ${t.name} against ${e}`),t.name&&(t.name.toLowerCase().includes(e.toLowerCase().split(".")[0])||e.toLowerCase().includes(t.name.toLowerCase().split(".")[0]))){c=t;break}}if(c){console.log(`Found matching document: ${c.name} (ID: ${c.id})`);try{var t;const e=await Object(l.getDocumentContent)(c.id),s=e.content||e.extractedContent||"No content available";j(s),n(c.name);const a=c.fileType||(null===(t=c.name.split(".").pop())||void 0===t?void 0:t.toLowerCase())||"";o(a)}catch(s){console.error("Error getting document content:",s),j(`Error loading content: ${s instanceof Error?s.message:"Unknown error"}`)}}else console.log("No matching document found, showing file not found message"),j(`Document "${e}" not found in the database.\n\nAvailable documents:\n${r.map((e=>`- ${e.name}`)).join("\n")}`)}else console.error("Failed to fetch documents list:",a.status),j(`Failed to fetch documents list from server (${a.status})`)}catch(u){console.error("Error fetching document content:",u),j(`Error loading content for "${e}": ${u instanceof Error?u.message:"Unknown error"}`)}finally{g(!1)}};if(u)return Object(d.jsx)("div",{className:"flex items-center justify-center h-full",children:Object(d.jsxs)("div",{className:"text-center p-8",children:[Object(d.jsx)("h2",{className:"text-2xl font-bold text-red-600 mb-4",children:"Document Viewer Error"}),Object(d.jsx)("p",{className:"text-gray-600 mb-4",children:u}),Object(d.jsx)("button",{onClick:()=>window.history.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Go Back"})]})});if(b)return Object(d.jsx)("div",{className:"flex items-center justify-center h-full",children:Object(d.jsxs)("div",{className:"text-center",children:[Object(d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),Object(d.jsx)("p",{children:"Loading document..."})]})});const y=(e=>{switch(e){case"pdf":return{icon:"\ud83d\udcc4",name:"PDF Document"};case"docx":case"doc":return{icon:"\ud83d\udcdd",name:"Word Document"};case"xlsx":case"xls":return{icon:"\ud83d\udcca",name:"Excel Spreadsheet"};case"pptx":case"ppt":return{icon:"\ud83d\udccb",name:"PowerPoint Presentation"};case"txt":return{icon:"\ud83d\udcc4",name:"Text Document"};default:return{icon:"\ud83d\udcc1",name:"Document"}}})(c);return Object(d.jsxs)("div",{className:"h-full flex flex-col bg-white",children:[Object(d.jsx)("div",{className:"bg-white border-b border-gray-200 p-4",children:Object(d.jsxs)("div",{className:"flex items-center justify-between",children:[Object(d.jsxs)("div",{className:"flex items-center",children:[Object(d.jsx)("span",{className:"text-2xl mr-3",children:y.icon}),Object(d.jsxs)("div",{children:[Object(d.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:r}),Object(d.jsx)("p",{className:"text-sm text-gray-500",children:y.name})]})]}),Object(d.jsxs)("div",{className:"flex items-center space-x-2",children:["pdf"===c&&Object(d.jsx)("button",{onClick:()=>{"pdf"===c&&(p(!h),h||x||f(r))},className:"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50",children:h?"Show PDF":"Show Text"}),Object(d.jsx)("button",{onClick:()=>window.open(t,"_blank"),className:"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50",children:"Open in New Tab"}),Object(d.jsx)("a",{href:t,download:r,className:"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50",children:"Download"}),Object(d.jsx)("button",{onClick:()=>window.close(),className:"px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50",children:"Close"})]})]})}),Object(d.jsx)("div",{className:"flex-1 overflow-hidden",children:"pdf"===c&&!h&&t?Object(d.jsx)("div",{className:"h-full",children:Object(d.jsx)(xe,{url:t,fileName:r})}):Object(d.jsx)("div",{className:"h-full p-4 overflow-auto",children:Object(d.jsx)("div",{className:"max-w-4xl mx-auto",children:Object(d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[Object(d.jsx)("h3",{className:"text-lg font-semibold mb-4 text-gray-800",children:"Extracted Content"}),Object(d.jsx)("div",{className:"prose max-w-none",children:Object(d.jsx)("pre",{className:"whitespace-pre-wrap text-sm bg-white p-4 rounded border overflow-auto max-h-[70vh]",children:x||"Loading content..."})}),"pdf"!==c&&Object(d.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded text-sm text-blue-700",children:Object(d.jsxs)("p",{children:[Object(d.jsx)("strong",{children:"Note:"})," This is the extracted text content from the ",y.name,". The original file formatting may not be preserved."]})})]})})})})]})};var he=e=>{let{title:t="RailGPT",onSidebarToggle:s,sidebarOpen:a=!1}=e;const r=Object(i.f)(),n=r.pathname.includes("/documents"),c=r.pathname.includes("/websites");return Object(d.jsx)("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50 transition-colors duration-300",children:Object(d.jsxs)("div",{className:"container mx-auto flex flex-col sm:flex-row justify-between items-center",children:[Object(d.jsxs)("div",{className:"flex items-center mb-4 sm:mb-0",children:["/"===r.pathname&&s&&Object(d.jsx)("button",{onClick:s,className:"p-2 mr-3 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50",title:a?"Close chat history":"Open chat history",children:"\u2630"}),Object(d.jsx)("h1",{className:"text-xl font-bold",children:t})]}),Object(d.jsxs)("nav",{className:"flex gap-4",children:[Object(d.jsx)(o.b,{to:"/",className:"px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 "+("/"===r.pathname?"bg-blue-700 font-medium":""),children:"Chat"}),Object(d.jsx)(o.b,{to:"/documents",className:"px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 "+(n?"bg-blue-700 font-medium":""),children:"Documents"}),Object(d.jsx)(o.b,{to:"/websites",className:"px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 "+(c?"bg-blue-700 font-medium":""),children:"Websites"}),Object(d.jsx)(o.b,{to:"/settings",className:"px-3 py-2 hover:bg-blue-700 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 "+("/settings"===r.pathname?"bg-blue-700 font-medium":""),children:"\u2699\ufe0f Settings"})]})]})})};var pe=()=>{const[e,t]=Object(a.useState)(!1);return Object(d.jsx)(N,{children:Object(d.jsx)(o.a,{children:Object(d.jsxs)("div",{className:"flex flex-col h-screen overflow-hidden bg-white transition-colors duration-300",children:[Object(d.jsx)(he,{onSidebarToggle:()=>t(!e),sidebarOpen:e}),Object(d.jsx)("main",{className:"flex-grow bg-gray-50 h-full pt-16 sm:pt-20 overflow-hidden transition-colors duration-300",children:Object(d.jsxs)(i.c,{children:[Object(d.jsx)(i.a,{exact:!0,path:"/",render:()=>Object(d.jsx)(C,{sidebarOpen:e,setSidebarOpen:t})}),Object(d.jsx)(i.a,{path:"/documents",component:te}),Object(d.jsx)(i.a,{path:"/websites",component:ie}),Object(d.jsx)(i.a,{path:"/settings",component:ge}),Object(d.jsx)(i.a,{path:"/viewer",component:je})]})})]})})})};var fe=e=>{e&&e instanceof Function&&s.e(4).then(s.bind(null,134)).then((t=>{let{getCLS:s,getFID:a,getFCP:r,getLCP:n,getTTFB:c}=t;s(e),a(e),r(e),n(e),c(e)}))};c.a.render(Object(d.jsx)(r.a.StrictMode,{children:Object(d.jsx)(pe,{})}),document.getElementById("root")),fe()}},[[92,1,2]]]);
//# sourceMappingURL=main.ece4fdc3.chunk.js.map