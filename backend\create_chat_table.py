#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from supabase_client import SupabaseClient

def create_chat_sessions_table():
    """Create the chat_sessions table in Supabase"""
    try:
        # Read the SQL schema
        with open('sql/chat_sessions_schema.sql', 'r') as f:
            sql_content = f.read()
        
        # Initialize Supabase client
        client = SupabaseClient()
        
        # Execute the SQL
        result = client.execute_sql(sql_content)
        
        print("✅ Chat sessions table created successfully!")
        print("Result:", result)
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating chat sessions table: {str(e)}")
        return False

if __name__ == "__main__":
    create_chat_sessions_table() 