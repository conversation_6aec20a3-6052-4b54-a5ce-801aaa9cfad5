import React, { useState, useEffect } from 'react';
import { Document, CategoryHierarchy, DocumentCategoryUpdate } from '../../types/documents';
import { getCategories, updateDocumentCategories, getCategoriesOfType } from '../../services/categoryApi';
import { X, Save, AlertCircle, CheckCircle } from 'lucide-react';

interface DocumentCategoryEditorProps {
  document: Document;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedDocument: Document) => void;
}

const DocumentCategoryEditor: React.FC<DocumentCategoryEditorProps> = ({
  document,
  isOpen,
  onClose,
  onSave,
}) => {
  const [categories, setCategories] = useState<CategoryHierarchy[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [selectedMainCategory, setSelectedMainCategory] = useState(document.mainCategory || '');
  const [selectedCategory, setSelectedCategory] = useState(document.category || '');
  const [selectedSubCategory, setSelectedSubCategory] = useState(document.subCategory || '');
  const [selectedMinorCategory, setSelectedMinorCategory] = useState(document.minorCategory || '');

  // Available options based on selections
  const [mainCategories, setMainCategories] = useState<CategoryHierarchy[]>([]);
  const [availableCategories, setAvailableCategories] = useState<CategoryHierarchy[]>([]);
  const [availableSubCategories, setAvailableSubCategories] = useState<CategoryHierarchy[]>([]);
  const [availableMinorCategories, setAvailableMinorCategories] = useState<CategoryHierarchy[]>([]);

  // Load categories on mount
  useEffect(() => {
    if (isOpen) {
      loadCategories();
    }
  }, [isOpen]);

  // Update available options when selections change
  useEffect(() => {
    updateAvailableOptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categories, selectedMainCategory, selectedCategory, selectedSubCategory]);

  const loadCategories = async () => {
    setLoading(true);
    setError(null);
    try {
      const categoryData = await getCategories();
      setCategories(categoryData);
      
      // Get main categories
      const mainCats = getCategoriesOfType(categoryData, 'main_category');
      setMainCategories(mainCats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const updateAvailableOptions = () => {
    // Find selected main category
    const mainCat = mainCategories.find(cat => cat.name === selectedMainCategory);
    if (mainCat && mainCat.children) {
      setAvailableCategories(mainCat.children.filter(cat => cat.type === 'category'));
    } else {
      setAvailableCategories([]);
      setSelectedCategory('');
      setSelectedSubCategory('');
      setSelectedMinorCategory('');
    }

    // Find selected category
    const cat = availableCategories.find(cat => cat.name === selectedCategory);
    if (cat && cat.children) {
      setAvailableSubCategories(cat.children.filter(cat => cat.type === 'sub_category'));
    } else {
      setAvailableSubCategories([]);
      setSelectedSubCategory('');
      setSelectedMinorCategory('');
    }

    // Find selected sub category
    const subCat = availableSubCategories.find(cat => cat.name === selectedSubCategory);
    if (subCat && subCat.children) {
      setAvailableMinorCategories(subCat.children.filter(cat => cat.type === 'minor_category'));
    } else {
      setAvailableMinorCategories([]);
      setSelectedMinorCategory('');
    }
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const categoryUpdate: DocumentCategoryUpdate = {
        main_category: selectedMainCategory || undefined,
        category: selectedCategory || undefined,
        sub_category: selectedSubCategory || undefined,
        minor_category: selectedMinorCategory || undefined,
      };

      await updateDocumentCategories(document.id, categoryUpdate);

      // Update the document object
      const updatedDocument: Document = {
        ...document,
        mainCategory: selectedMainCategory,
        category: selectedCategory,
        subCategory: selectedSubCategory,
        minorCategory: selectedMinorCategory,
      };

      setSuccess('Document categories updated successfully!');
      onSave(updatedDocument);
      
      // Close modal after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update document categories');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setSelectedMainCategory(document.mainCategory || '');
    setSelectedCategory(document.category || '');
    setSelectedSubCategory(document.subCategory || '');
    setSelectedMinorCategory(document.minorCategory || '');
    setError(null);
    setSuccess(null);
  };

  const hasChanges = () => {
    return (
      selectedMainCategory !== (document.mainCategory || '') ||
      selectedCategory !== (document.category || '') ||
      selectedSubCategory !== (document.subCategory || '') ||
      selectedMinorCategory !== (document.minorCategory || '')
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Edit Document Categories
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Document Info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Document Information</h3>
            <p className="text-sm text-gray-600">
              <strong>Name:</strong> {document.name}
            </p>
            <p className="text-sm text-gray-600">
              <strong>Type:</strong> {document.fileType?.toUpperCase()}
            </p>
            <p className="text-sm text-gray-600">
              <strong>Current Categories:</strong> {
                [document.mainCategory, document.category, document.subCategory, document.minorCategory]
                  .filter(Boolean)
                  .join(' > ') || 'None'
              }
            </p>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-700 text-sm">{error}</span>
            </div>
          )}

          {success && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-green-700 text-sm">{success}</span>
            </div>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading categories...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Main Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Main Category *
                </label>
                <select
                  value={selectedMainCategory}
                  onChange={(e) => setSelectedMainCategory(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={saving}
                >
                  <option value="">Select Main Category</option>
                  {mainCategories.map((cat) => (
                    <option key={cat.id} value={cat.name}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!selectedMainCategory || saving}
                >
                  <option value="">Select Category</option>
                  {availableCategories.map((cat) => (
                    <option key={cat.id} value={cat.name}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sub Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sub Category
                </label>
                <select
                  value={selectedSubCategory}
                  onChange={(e) => setSelectedSubCategory(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!selectedCategory || saving}
                >
                  <option value="">Select Sub Category</option>
                  {availableSubCategories.map((cat) => (
                    <option key={cat.id} value={cat.name}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Minor Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Minor Category
                </label>
                <select
                  value={selectedMinorCategory}
                  onChange={(e) => setSelectedMinorCategory(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!selectedSubCategory || saving}
                >
                  <option value="">Select Minor Category</option>
                  {availableMinorCategories.map((cat) => (
                    <option key={cat.id} value={cat.name}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={handleReset}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            disabled={saving}
          >
            Reset
          </button>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              disabled={saving}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={!hasChanges() || saving || !selectedMainCategory}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentCategoryEditor;
