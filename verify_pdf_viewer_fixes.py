#!/usr/bin/env python3
"""
Verification script for RailGPT PDF viewer fixes.
Run this to verify all PDF viewer functionality is working.
"""

import requests
import sys
import time

def check_backend():
    """Check if backend server is running."""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def check_frontend():
    """Check if frontend server is running."""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        return response.status_code == 200
    except:
        return False

def test_pdf_serving():
    """Test PDF file serving."""
    test_files = ["ACP%20110V.pdf", "Authority%20Transfer%20Declaration.pdf"]
    results = []
    
    for filename in test_files:
        try:
            url = f"http://localhost:8000/api/documents/view/{filename}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                is_pdf = response.content.startswith(b'%PDF')
                size = len(response.content)
                
                results.append({
                    'filename': filename,
                    'status': 'SUCCESS',
                    'content_type': content_type,
                    'is_pdf': is_pdf,
                    'size': size
                })
            else:
                results.append({
                    'filename': filename,
                    'status': 'FAILED',
                    'error': f"HTTP {response.status_code}"
                })
        except Exception as e:
            results.append({
                'filename': filename,
                'status': 'ERROR',
                'error': str(e)
            })
    
    return results

def test_document_search():
    """Test document search and source generation."""
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json={"query": "authority transfer", "model": "gemini-2.0-flash"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            document_sources = data.get('document_sources', [])
            llm_fallback = data.get('llm_fallback', True)
            
            return {
                'status': 'SUCCESS',
                'document_sources_count': len(document_sources),
                'llm_fallback': llm_fallback,
                'sources': document_sources[:2]  # First 2 sources
            }
        else:
            return {
                'status': 'FAILED',
                'error': f"HTTP {response.status_code}"
            }
    except Exception as e:
        return {
            'status': 'ERROR',
            'error': str(e)
        }

def main():
    """Run comprehensive verification."""
    print("🔧 RailGPT PDF Viewer Fixes Verification")
    print("=" * 60)
    
    # Check servers
    print("\n📡 Server Status Check:")
    backend_ok = check_backend()
    frontend_ok = check_frontend()
    
    print(f"   Backend (port 8000): {'✅ RUNNING' if backend_ok else '❌ NOT RUNNING'}")
    print(f"   Frontend (port 3000): {'✅ RUNNING' if frontend_ok else '❌ NOT RUNNING'}")
    
    if not backend_ok:
        print("\n❌ Backend server not running. Please start with:")
        print("   cd backend && python -m uvicorn server:app --reload --port 8000")
        return 1
    
    if not frontend_ok:
        print("\n❌ Frontend server not running. Please start with:")
        print("   cd frontend && npm start")
        return 1
    
    # Test PDF serving
    print("\n📄 PDF File Serving Test:")
    pdf_results = test_pdf_serving()
    
    for result in pdf_results:
        filename = result['filename'].replace('%20', ' ')
        if result['status'] == 'SUCCESS':
            print(f"   ✅ {filename}")
            print(f"      Content-Type: {result['content_type']}")
            print(f"      Valid PDF: {result['is_pdf']}")
            print(f"      Size: {result['size']:,} bytes")
        else:
            print(f"   ❌ {filename}: {result.get('error', 'Unknown error')}")
    
    # Test document search
    print("\n🔍 Document Search Test:")
    search_result = test_document_search()
    
    if search_result['status'] == 'SUCCESS':
        print(f"   ✅ Search successful")
        print(f"   📄 Document sources found: {search_result['document_sources_count']}")
        print(f"   🤖 LLM fallback: {search_result['llm_fallback']}")
        
        if search_result['document_sources_count'] > 0:
            print(f"   🎯 Source links generated:")
            for i, source in enumerate(search_result['sources']):
                if isinstance(source, dict):
                    filename = source.get('filename', 'Unknown')
                    link = source.get('link', 'No link')
                    print(f"      {i+1}. {filename} -> {link}")
        else:
            print(f"   ⚠️  No document sources found")
    else:
        print(f"   ❌ Search failed: {search_result.get('error', 'Unknown error')}")
    
    # Manual testing instructions
    print(f"\n🧪 Manual Testing Instructions:")
    print("=" * 60)
    print("1. Open: http://localhost:3000")
    print("2. Ask: 'What is authority transfer?' or 'What is ACP?'")
    print("3. Verify document source cards appear")
    print("4. Click on source links to test PDF viewer")
    print("5. Test viewer options: Pro PDF, React PDF, Simple")
    print("6. Test zoom, navigation, and download features")
    
    print(f"\n📋 Direct PDF Viewer URLs:")
    print("   http://localhost:3000/viewer?file=ACP%20110V.pdf&page=1")
    print("   http://localhost:3000/viewer?file=Authority%20Transfer%20Declaration.pdf&page=1")
    
    print(f"\n🎯 Expected Results:")
    print("   ✅ PDF viewer loads without version errors")
    print("   ✅ All three viewer options work")
    print("   ✅ Zoom and navigation controls functional")
    print("   ✅ Error recovery buttons available if needed")
    print("   ✅ Download functionality works")
    
    # Overall status
    all_pdf_ok = all(r['status'] == 'SUCCESS' for r in pdf_results)
    search_ok = search_result['status'] == 'SUCCESS'
    sources_found = search_result.get('document_sources_count', 0) > 0
    
    print(f"\n🏆 Overall Status:")
    if backend_ok and frontend_ok and all_pdf_ok and search_ok and sources_found:
        print("   ✅ ALL SYSTEMS OPERATIONAL")
        print("   🎉 PDF viewer fixes successfully implemented!")
        return 0
    else:
        print("   ⚠️  Some issues detected - check manual testing")
        return 1

if __name__ == "__main__":
    sys.exit(main())
