/* Train Container - Invisible wrapper, no background */
.train-container-direct {
  position: fixed;
  top: 50%;
  left: 0;
  display: flex;
  align-items: center;
  z-index: 9999;
  pointer-events: none;
  animation: trainMovement 15s linear infinite;
  animation-delay: 0s;
  will-change: transform;
  transform: translateY(-50%) scale(0.6) scaleX(-1);
  /* No background, no border, no shadow, no dimensions */
}

/* When sidebar is open, use different animation to avoid sidebar area */
.train-container-direct.sidebar-open {
  animation: trainMovementSidebarOpen 15s linear infinite;
}

/* Animated Text Trail */
.train-text-trail {
  position: fixed;
  top: 50%;
  left: 0;
  z-index: 9998;
  pointer-events: none;
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  color: #1E3A8A;
  font-size: 18px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: textTrail 20s linear infinite;
  animation-delay: 5s;
  transform: translateY(-50%);
  white-space: nowrap;
}

/* Text trail when sidebar is open */
.train-text-trail.sidebar-open {
  animation: textTrailSidebarOpen 20s linear infinite;
  animation-delay: 4s;
}

@keyframes textTrail {
  0% { 
    transform: translateY(-50%) translateX(-800px);
    opacity: 0;
  }
  5% { 
    transform: translateY(-50%) translateX(-300px);
    opacity: 1;
  }
  90% { 
    transform: translateY(-50%) translateX(calc(100vw - 200px));
    opacity: 1;
  }
  100% { 
    transform: translateY(-50%) translateX(calc(100vw + 200px));
    opacity: 0;
  }
}

/* Text trail animation when sidebar is open */
@keyframes textTrailSidebarOpen {
  0% { 
    transform: translateY(-50%) translateX(-800px);
    opacity: 0;
  }
  15% { 
    transform: translateY(-50%) translateX(-400px);
    opacity: 0;
  }
  20% { 
    transform: translateY(-50%) translateX(-200px);
    opacity: 1;
  }
  30% { 
    transform: translateY(-50%) translateX(calc(100vw - 300px));
    opacity: 1;
  }
  75% { 
    transform: translateY(-50%) translateX(400px);
    opacity: 1;
  }
  95% { 
    transform: translateY(-50%) translateX(-200px);
    opacity: 1;
  }
  100% { 
    transform: translateY(-50%) translateX(-800px);
    opacity: 0;
  }
}

@keyframes trainMovement {
  0% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(200vw); }
  100% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(-1200px); }
}

/* Train animation when sidebar is open - avoid sidebar area */
@keyframes trainMovementSidebarOpen {
  0% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(200vw); }
  10% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(calc(100vw - 100px)); }
  25% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(calc(100vw - 300px)); }
  75% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(400px); }
  100% { transform: translateY(-50%) scale(0.6) scaleX(-1) translateX(-1200px); }
}

/* Engine Styles */
.train-engine {
  position: relative;
  width: 120px;
  height: 80px;
  background: #1E3A8A;
  border-radius: 8px 15px 4px 4px;
  margin-right: 5px;
  border: none;
  box-shadow: none;
}

.engine-front {
  position: absolute;
  top: 10px;
  right: -10px;
  width: 15px;
  height: 60px;
  background: linear-gradient(90deg, #2563EB, #1E40AF);
  border-radius: 0 8px 8px 0;
  border: none;
  box-shadow: none;
}

/* Engine Chimney and Smoke */
.engine-chimney {
  position: absolute;
  top: -15px;
  left: 20px;
  width: 12px;
  height: 20px;
  background: #374151;
  border-radius: 2px 2px 0 0;
  border: none;
  box-shadow: none;
}

.smoke {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(64, 64, 64, 0.8);
  border-radius: 50%;
  animation: smokeRise 2s ease-out infinite;
  border: none;
  box-shadow: none;
}

.smoke-1 { 
  animation-delay: 0s; 
  left: 2px;
}
.smoke-2 { 
  animation-delay: 0.5s; 
  left: 6px;
}
.smoke-3 { 
  animation-delay: 1s; 
  left: 4px;
}
.smoke-4 { 
  animation-delay: 1.5s; 
  left: 8px;
}

@keyframes smokeRise {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) scale(1.5);
    opacity: 0.4;
  }
  100% {
    transform: translateY(-40px) scale(2);
    opacity: 0;
  }
}

/* Engine Details */
.engine-details {
  position: absolute;
  top: 15px;
  left: 15px;
}

.engine-window {
  width: 25px;
  height: 20px;
  background: linear-gradient(45deg, #87CEEB, #B0E0E6);
  border: 2px solid #2D3748;
  border-radius: 3px;
  box-shadow: none;
}

.engine-light {
  position: absolute;
  top: 25px;
  left: 5px;
  width: 15px;
  height: 15px;
  background: radial-gradient(circle, #FFF700, #FFD700);
  border-radius: 50%;
  box-shadow: none;
  animation: lightFlicker 2s ease-in-out infinite alternate;
}

@keyframes lightFlicker {
  0% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* Coach Styles */
.train-coach {
  position: relative;
  width: 140px;
  height: 85px;
  background: #DC143C;
  border-radius: 4px;
  margin-right: 5px;
  border: none;
  box-shadow: none;
}

/* Indian Railways Logo */
.ir-logo {
  position: absolute;
  top: 10px;
  left: 10px;
}

.ir-circle {
  width: 30px;
  height: 30px;
  background: #FFFFFF;
  border: 2px solid #FF6600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
}

.ir-circle.small {
  width: 20px;
  height: 20px;
}

.ir-text {
  font-weight: bold;
  font-size: 12px;
  color: #FF6600;
  font-family: 'Arial', sans-serif;
  text-shadow: none;
}

.ir-text.small {
  font-size: 8px;
}

/* Coach Windows */
.coach-windows {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  gap: 8px;
}

.window {
  width: 18px;
  height: 15px;
  background: linear-gradient(45deg, #87CEEB, #B0E0E6);
  border: 1px solid #2D3748;
  border-radius: 2px;
  box-shadow: none;
  animation: windowFlicker 3s ease-in-out infinite alternate;
}

@keyframes windowFlicker {
  0% { background: linear-gradient(45deg, #87CEEB, #B0E0E6); }
  50% { background: linear-gradient(45deg, #FFE4B5, #FFF8DC); }
  100% { background: linear-gradient(45deg, #87CEEB, #B0E0E6); }
}

/* Coach Details */
.coach-details {
  position: absolute;
  bottom: 35px;
  left: 15px;
}

.coach-door {
  width: 20px;
  height: 35px;
  background: linear-gradient(90deg, #8B0000, #A52A2A);
  border: 1px solid #2D3748;
  border-radius: 2px;
  box-shadow: none;
}

.coach-number {
  position: absolute;
  top: -20px;
  left: 0;
  font-size: 10px;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: none;
  font-family: 'Courier New', monospace;
}

/* Wagon Styles */
.train-wagon {
  position: relative;
  width: 120px;
  height: 80px;
  background: linear-gradient(180deg, #9CA3AF, #6B7280);
  border-radius: 4px;
  border: none;
  box-shadow: none;
}

.wagon-logo {
  position: absolute;
  top: 8px;
  right: 10px;
}

.wagon-details {
  position: absolute;
  top: 15px;
  left: 15px;
}

.cargo-lines {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cargo-line {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #4B5563, #6B7280);
  border-radius: 1px;
  box-shadow: none;
}

/* Train Wheels */
.train-wheels {
  position: absolute;
  bottom: -20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
}

.wheel {
  width: 35px;
  height: 35px;
  background: radial-gradient(circle, #2D3748 30%, #4A5568 70%, #1A202C 100%);
  border: 3px solid #E2E8F0;
  border-radius: 50%;
  position: relative;
  animation: wheelRotation 0.5s linear infinite;
  box-shadow: none;
}

@keyframes wheelRotation {
  0% { transform: rotate(360deg); }
  100% { transform: rotate(0deg); }
}

.wheel-spoke {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 60%;
  background: #E2E8F0;
  transform-origin: center top;
  border-radius: 1px;
}

.wheel-spoke:nth-child(1) { transform: translate(-50%, -50%) rotate(0deg); }
.wheel-spoke:nth-child(2) { transform: translate(-50%, -50%) rotate(60deg); }
.wheel-spoke:nth-child(3) { transform: translate(-50%, -50%) rotate(120deg); }

/* Responsive Design */
@media (max-width: 768px) {
  .train-container-direct {
    transform: translateY(-50%) scale(0.5) scaleX(-1);
    animation: trainMovement 15s linear infinite;
    animation-delay: 0s;
  }
  
  .train-container-direct.sidebar-open {
    animation: trainMovementSidebarOpen 15s linear infinite;
  }
  
  .train-text-trail {
    font-size: 16px;
    animation: textTrail 20s linear infinite;
    animation-delay: 5s;
  }
  
  .train-text-trail.sidebar-open {
    animation: textTrailSidebarOpen 20s linear infinite;
    animation-delay: 4s;
  }
  
  @keyframes trainMovement {
    0% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(200vw); }
    100% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(-1200px); }
  }
  
  @keyframes trainMovementSidebarOpen {
    0% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(200vw); }
    10% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(calc(100vw - 80px)); }
    25% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(calc(100vw - 250px)); }
    75% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(320px); }
    100% { transform: translateY(-50%) scale(0.5) scaleX(-1) translateX(-1200px); }
  }
  
  @keyframes textTrail {
    0% { 
      transform: translateY(-50%) translateX(-700px);
      opacity: 0;
    }
    5% { 
      transform: translateY(-50%) translateX(-250px);
      opacity: 1;
    }
    90% { 
      transform: translateY(-50%) translateX(calc(100vw - 150px));
      opacity: 1;
    }
    100% { 
      transform: translateY(-50%) translateX(calc(100vw + 150px));
      opacity: 0;
    }
  }
  
  @keyframes textTrailSidebarOpen {
    0% { 
      transform: translateY(-50%) translateX(-700px);
      opacity: 0;
    }
    15% { 
      transform: translateY(-50%) translateX(-350px);
      opacity: 0;
    }
    20% { 
      transform: translateY(-50%) translateX(-150px);
      opacity: 1;
    }
    30% { 
      transform: translateY(-50%) translateX(calc(100vw - 250px));
      opacity: 1;
    }
    75% { 
      transform: translateY(-50%) translateX(320px);
      opacity: 1;
    }
    95% { 
      transform: translateY(-50%) translateX(-150px);
      opacity: 1;
    }
    100% { 
      transform: translateY(-50%) translateX(-700px);
      opacity: 0;
    }
  }
}

@media (max-width: 480px) {
  .train-container-direct {
    transform: translateY(-50%) scale(0.4) scaleX(-1);
    animation: trainMovement 15s linear infinite;
    animation-delay: 0s;
  }
  
  .train-container-direct.sidebar-open {
    animation: trainMovementSidebarOpen 15s linear infinite;
  }
  
  .train-text-trail {
    font-size: 14px;
    animation: textTrail 20s linear infinite;
    animation-delay: 5s;
  }
  
  .train-text-trail.sidebar-open {
    animation: textTrailSidebarOpen 20s linear infinite;
    animation-delay: 4s;
  }
  
  @keyframes trainMovement {
    0% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(200vw); }
    100% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(-1200px); }
  }
  
  @keyframes trainMovementSidebarOpen {
    0% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(200vw); }
    10% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(calc(100vw - 60px)); }
    25% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(calc(100vw - 200px)); }
    75% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(280px); }
    100% { transform: translateY(-50%) scale(0.4) scaleX(-1) translateX(-1200px); }
  }
  
  @keyframes textTrail {
    0% { 
      transform: translateY(-50%) translateX(-600px);
      opacity: 0;
    }
    5% { 
      transform: translateY(-50%) translateX(-200px);
      opacity: 1;
    }
    90% { 
      transform: translateY(-50%) translateX(calc(100vw - 100px));
      opacity: 1;
    }
    100% { 
      transform: translateY(-50%) translateX(calc(100vw + 100px));
      opacity: 0;
    }
  }
  
  @keyframes textTrailSidebarOpen {
    0% { 
      transform: translateY(-50%) translateX(-600px);
      opacity: 0;
    }
    15% { 
      transform: translateY(-50%) translateX(-300px);
      opacity: 0;
    }
    20% { 
      transform: translateY(-50%) translateX(-100px);
      opacity: 1;
    }
    30% { 
      transform: translateY(-50%) translateX(calc(100vw - 200px));
      opacity: 1;
    }
    75% { 
      transform: translateY(-50%) translateX(280px);
      opacity: 1;
    }
    95% { 
      transform: translateY(-50%) translateX(-100px);
      opacity: 1;
    }
    100% { 
      transform: translateY(-50%) translateX(-600px);
      opacity: 0;
    }
  }
}

/* Performance Optimizations */
.train-container-direct * {
  will-change: transform;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .train-container-direct,
  .wheel,
  .smoke,
  .train-text-trail {
    animation: none;
  }
  
  .train-text-trail {
    display: none;
  }
  
  .window {
    background: linear-gradient(45deg, #87CEEB, #B0E0E6);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .train-engine {
    border: 2px solid #000;
  }
  
  .train-coach {
    border: 2px solid #000;
  }
  
  .train-wagon {
    border: 2px solid #000;
  }
} 