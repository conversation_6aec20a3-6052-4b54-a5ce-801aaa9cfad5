#!/usr/bin/env python3
"""
Simple test implementation of the user's priority system:
1. Document chunks (High Priority)
2. Website chunks (Medium Priority)  
3. Show both if both available
4. LLM fallback only if no relevant chunks found
"""

import requests
import json

# Configuration
API_URL = "http://localhost:8000"

def text_search_chunks(chunks, query, chunk_type="document"):
    """Simple text-based search for chunks."""
    query_lower = query.lower()
    query_words = set(query_lower.split())
    
    # Domain keywords
    if chunk_type == "document":
        domain_keywords = {
            'railway', 'train', 'rail', 'station', 'track', 'locomotive', 'coach',
            'signal', 'platform', 'passenger', 'freight', 'engine', 'diesel',
            'fsds', 'monitoring', 'system', 'safety', 'maintenance', 'inspection'
        }
    else:  # website
        domain_keywords = {
            'railway', 'train', 'rail', 'station', 'transport', 'transportation',
            'public', 'safety', 'travel', 'passenger', 'metro', 'services'
        }
    
    scored_chunks = []
    
    for chunk in chunks:
        if chunk.get('source_type') != chunk_type:
            continue
            
        chunk_text = chunk.get('text', '').lower()
        if not chunk_text:
            continue
        
        # Calculate relevance score
        score = 0
        
        # 1. Exact phrase match
        if query_lower in chunk_text:
            score += 1.0
        
        # 2. Keyword matching
        chunk_words = set(chunk_text.split())
        common_words = query_words & chunk_words
        if common_words:
            score += len(common_words) * 0.3
        
        # 3. Domain relevance
        domain_matches = domain_keywords & chunk_words
        if domain_matches:
            score += len(domain_matches) * 0.2
        
        # 4. Special boost for FSDS
        if 'fsds' in chunk_text:
            score += 0.5
        
        # Only include relevant chunks
        if score >= 0.8:  # Higher threshold to match server implementation
            chunk_copy = dict(chunk)
            chunk_copy['similarity'] = score
            scored_chunks.append(chunk_copy)
    
    # Sort by relevance
    scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
    return scored_chunks[:10]

def test_priority_system():
    """Test the priority system implementation."""
    print("🚀 Testing Priority System Implementation")
    print("="*50)
    
    # Get chunks from server
    try:
        chunks_response = requests.get(f"{API_URL}/api/chunks", timeout=10)
        if chunks_response.status_code != 200:
            print(f"❌ Failed to get chunks: {chunks_response.status_code}")
            return
        
        chunks = chunks_response.json()
        print(f"📊 Total chunks loaded: {len(chunks)}")
        
        # Filter by type
        doc_chunks = [c for c in chunks if c.get('source_type') == 'document']
        web_chunks = [c for c in chunks if c.get('source_type') == 'website']
        
        print(f"📄 Document chunks: {len(doc_chunks)}")
        print(f"🌐 Website chunks: {len(web_chunks)}")
        
    except Exception as e:
        print(f"❌ Error getting chunks: {str(e)}")
        return
    
    # Test queries
    test_queries = [
        {
            "query": "FSDS monitoring system",
            "description": "Railway-specific query",
            "expected_doc": True,
            "expected_web": False
        },
        {
            "query": "public transportation safety",
            "description": "Transport query",
            "expected_doc": False,
            "expected_web": True
        },
        {
            "query": "railway station safety",
            "description": "General railway query",
            "expected_doc": True,
            "expected_web": True
        },
        {
            "query": "chocolate cake recipe",
            "description": "Irrelevant query",
            "expected_doc": False,
            "expected_web": False
        }
    ]
    
    for test in test_queries:
        print(f"\n🔍 Testing: {test['description']}")
        print(f"Query: '{test['query']}'")
        
        # Search document chunks
        doc_results = text_search_chunks(chunks, test['query'], 'document')
        web_results = text_search_chunks(chunks, test['query'], 'website')
        
        print(f"📄 Document results: {len(doc_results)}")
        if doc_results:
            for i, result in enumerate(doc_results[:2]):
                print(f"   {i+1}. Score: {result['similarity']:.3f}")
        
        print(f"🌐 Website results: {len(web_results)}")
        if web_results:
            for i, result in enumerate(web_results[:2]):
                print(f"   {i+1}. Score: {result['similarity']:.3f}")
        
        # Determine expected behavior based on priority system
        if doc_results and web_results:
            expected_behavior = "Show both document and website answers"
            expected_llm_fallback = False
        elif doc_results:
            expected_behavior = "Show document answer only"
            expected_llm_fallback = False
        elif web_results:
            expected_behavior = "Show website answer only"
            expected_llm_fallback = False
        else:
            expected_behavior = "Use LLM fallback"
            expected_llm_fallback = True
        
        print(f"🎯 Expected: {expected_behavior}")
        print(f"🧠 LLM fallback should be: {expected_llm_fallback}")
        
        # Validate expectations
        doc_found = len(doc_results) > 0
        web_found = len(web_results) > 0
        
        if test['expected_doc'] == doc_found and test['expected_web'] == web_found:
            print("✅ SUCCESS: Results match expectations")
        else:
            print(f"❌ MISMATCH: Expected doc={test['expected_doc']}, web={test['expected_web']}, got doc={doc_found}, web={web_found}")

if __name__ == "__main__":
    test_priority_system() 