#!/usr/bin/env python3
"""
Simple working implementation of the user's priority system.
"""

import requests
import json
from flask import Flask, request, jsonify

app = Flask(__name__)

# Configuration
CHUNKS_API = "http://localhost:8000/api/chunks"

def text_search_chunks(chunks, query, chunk_type="document"):
    """Simple text-based search for chunks."""
    query_lower = query.lower()
    query_words = set(query_lower.split())
    
    # Domain keywords
    if chunk_type == "document":
        domain_keywords = {
            'railway', 'train', 'rail', 'station', 'track', 'locomotive', 'coach',
            'signal', 'platform', 'passenger', 'freight', 'engine', 'diesel',
            'fsds', 'monitoring', 'system', 'safety', 'maintenance', 'inspection'
        }
    else:  # website
        domain_keywords = {
            'railway', 'train', 'rail', 'station', 'transport', 'transportation',
            'public', 'safety', 'travel', 'passenger', 'metro', 'services'
        }
    
    scored_chunks = []
    
    for chunk in chunks:
        if chunk.get('source_type') != chunk_type:
            continue
            
        chunk_text = chunk.get('text', '').lower()
        if not chunk_text:
            continue
        
        # Calculate relevance score
        score = 0
        
        # 1. Exact phrase match
        if query_lower in chunk_text:
            score += 2.0
        
        # 2. Keyword matching
        chunk_words = set(chunk_text.split())
        common_words = query_words & chunk_words
        if common_words:
            min_matches = max(1, len(query_words) // 2)
            if len(common_words) >= min_matches:
                score += len(common_words) * 0.5
        
        # 3. Domain relevance
        domain_matches = domain_keywords & chunk_words
        if domain_matches:
            score += len(domain_matches) * 0.8
        
        # 4. Special boosts
        if chunk_type == "document":
            if 'fsds' in chunk_text:
                score += 1.5
            if 'monitoring' in chunk_text and 'system' in chunk_text:
                score += 1.0
        else:  # website
            if 'transportation' in chunk_text and 'safety' in chunk_text:
                score += 1.0
            if 'public' in chunk_text and ('transport' in chunk_text or 'railway' in chunk_text):
                score += 1.0
        
        # 5. Penalty for generic queries
        generic_terms = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'a', 'an', 'is', 'are'}
        if query_words.issubset(generic_terms):
            score *= 0.1
        
        # Only include relevant chunks
        if score >= 0.8:
            chunk_copy = dict(chunk)
            chunk_copy['similarity'] = score
            scored_chunks.append(chunk_copy)
    
    # Sort by relevance
    scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
    return scored_chunks[:10]

@app.route('/priority-query', methods=['POST'])
def priority_query():
    """Implement the user's priority system."""
    try:
        data = request.json
        query = data.get('query', '')
        
        if not query:
            return jsonify({"error": "Query is required"}), 400
        
        # Get chunks from main server
        chunks_response = requests.get(CHUNKS_API, timeout=10)
        if chunks_response.status_code != 200:
            return jsonify({"error": f"Failed to get chunks: {chunks_response.status_code}"}), 500
        
        chunks = chunks_response.json()
        
        # Search document chunks (HIGH PRIORITY)
        doc_chunks = text_search_chunks(chunks, query, 'document')
        
        # Search website chunks (MEDIUM PRIORITY)
        web_chunks = text_search_chunks(chunks, query, 'website')
        
        # Implement priority system
        if doc_chunks and web_chunks:
            # Both sources available - show both
            answer = "Information found in both documents and websites."
            document_answer = f"Based on {len(doc_chunks)} document sources, here is the document-based answer for '{query}'."
            website_answer = f"Based on {len(web_chunks)} website sources, here is the website-based answer for '{query}'."
            llm_fallback = False
            
        elif doc_chunks:
            # Only documents available (high priority)
            answer = f"Based on {len(doc_chunks)} document sources, here is the answer for '{query}'."
            document_answer = answer
            website_answer = None
            llm_fallback = False
            
        elif web_chunks:
            # Only websites available (medium priority)
            answer = f"Based on {len(web_chunks)} website sources, here is the answer for '{query}'."
            document_answer = None
            website_answer = answer
            llm_fallback = False
            
        else:
            # LLM fallback
            answer = f"No relevant information found for '{query}'. Using general knowledge fallback."
            document_answer = None
            website_answer = None
            llm_fallback = True
        
        # Create sources
        sources = []
        document_sources = []
        website_sources = []
        
        for chunk in doc_chunks:
            source = {
                "source_type": "document",
                "filename": chunk.get("filename", "Unknown"),
                "page": chunk.get("page", 1)
            }
            sources.append(source)
            document_sources.append(source)
        
        for chunk in web_chunks:
            source = {
                "source_type": "website", 
                "url": chunk.get("url", "Unknown")
            }
            sources.append(source)
            website_sources.append(source)
        
        return jsonify({
            "answer": answer,
            "document_answer": document_answer,
            "website_answer": website_answer,
            "sources": sources,
            "document_sources": document_sources,
            "website_sources": website_sources,
            "llm_model": "text-based-search",
            "llm_fallback": llm_fallback,
            "debug": {
                "query": query,
                "doc_chunks_found": len(doc_chunks),
                "web_chunks_found": len(web_chunks),
                "total_chunks": len(chunks)
            }
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    print("🚀 Starting Priority System Test Server on port 5000")
    app.run(host="0.0.0.0", port=5000, debug=True) 