#!/bin/bash

# RailGPT Backend Deployment to Google Cloud Run
# This script deploys the FastAPI backend to Cloud Run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
PROJECT_ID=${PROJECT_ID:-"railgpt-production"}
REGION=${REGION:-"us-central1"}
SERVICE_NAME=${SERVICE_NAME:-"railgpt-backend"}
IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE_NAME"

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v gcloud &> /dev/null; then
        print_error "Google Cloud SDK not found. Please install it first."
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker not found. Please install it first."
        exit 1
    fi
    
    # Check if logged in to gcloud
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        print_error "Not logged in to Google Cloud. Run 'gcloud auth login' first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Setup Google Cloud project
setup_project() {
    print_status "Setting up Google Cloud project..."
    
    # Set project
    gcloud config set project $PROJECT_ID
    
    # Enable required APIs
    print_status "Enabling required APIs..."
    gcloud services enable cloudbuild.googleapis.com
    gcloud services enable run.googleapis.com
    gcloud services enable containerregistry.googleapis.com
    
    print_success "Project setup completed"
}

# Build and push Docker image
build_and_push() {
    print_status "Building and pushing Docker image..."
    
    # Navigate to backend directory
    cd "$(dirname "$0")/../../backend"
    
    # Build image using Cloud Build (faster and more reliable)
    print_status "Building image with Cloud Build..."
    gcloud builds submit --tag $IMAGE_NAME .
    
    print_success "Image built and pushed successfully"
}

# Deploy to Cloud Run
deploy_to_cloud_run() {
    print_status "Deploying to Cloud Run..."
    
    # Check if environment file exists
    if [ -f "../.env" ]; then
        print_status "Loading environment variables from .env file..."
        source ../.env
    else
        print_warning "No .env file found. Using default values."
    fi
    
    # Deploy service
    gcloud run deploy $SERVICE_NAME \
        --image $IMAGE_NAME \
        --platform managed \
        --region $REGION \
        --allow-unauthenticated \
        --memory 2Gi \
        --cpu 2 \
        --max-instances 10 \
        --min-instances 0 \
        --concurrency 80 \
        --timeout 300 \
        --port 8000 \
        --set-env-vars ENVIRONMENT=production \
        --set-env-vars API_HOST=0.0.0.0 \
        --set-env-vars API_PORT=8000 \
        --set-env-vars LOG_LEVEL=INFO
    
    print_success "Service deployed to Cloud Run"
}

# Set environment variables
set_environment_variables() {
    print_status "Setting environment variables..."
    
    # Load from .env file if it exists
    if [ -f "../.env" ]; then
        source ../.env
        
        # Set environment variables
        gcloud run services update $SERVICE_NAME \
            --region $REGION \
            --set-env-vars SUPABASE_URL="$SUPABASE_URL" \
            --set-env-vars SUPABASE_KEY="$SUPABASE_KEY" \
            --set-env-vars SUPABASE_ANON_KEY="$SUPABASE_ANON_KEY" \
            --set-env-vars GEMINI_API_KEY="$GEMINI_API_KEY"
        
        # Set optional environment variables if they exist
        if [ ! -z "$OPENAI_API_KEY" ]; then
            gcloud run services update $SERVICE_NAME \
                --region $REGION \
                --set-env-vars OPENAI_API_KEY="$OPENAI_API_KEY"
        fi
        
        if [ ! -z "$GROQ_API_KEY" ]; then
            gcloud run services update $SERVICE_NAME \
                --region $REGION \
                --set-env-vars GROQ_API_KEY="$GROQ_API_KEY"
        fi
        
        print_success "Environment variables set"
    else
        print_warning "No .env file found. Please set environment variables manually:"
        print_warning "gcloud run services update $SERVICE_NAME --region $REGION --set-env-vars KEY=VALUE"
    fi
}

# Test deployment
test_deployment() {
    print_status "Testing deployment..."
    
    # Get service URL
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format="value(status.url)")
    
    print_status "Service URL: $SERVICE_URL"
    
    # Test health endpoint
    if curl -f "$SERVICE_URL/health" > /dev/null 2>&1; then
        print_success "Health check passed"
    else
        print_warning "Health check failed. Service might still be starting up."
    fi
    
    # Test API documentation
    print_status "API Documentation available at: $SERVICE_URL/docs"
}

# Setup custom domain (optional)
setup_domain() {
    if [ ! -z "$DOMAIN" ]; then
        print_status "Setting up custom domain: $DOMAIN"
        
        # Create domain mapping
        gcloud run domain-mappings create \
            --service $SERVICE_NAME \
            --domain $DOMAIN \
            --region $REGION
        
        print_success "Domain mapping created"
        print_status "Please configure your DNS to point $DOMAIN to:"
        gcloud run domain-mappings describe $DOMAIN --region $REGION --format="value(status.resourceRecords[0].rrdata)"
    fi
}

# Main execution
main() {
    echo "========================================="
    echo "   RailGPT Backend GCP Deployment       "
    echo "========================================="
    echo
    
    print_status "Project ID: $PROJECT_ID"
    print_status "Region: $REGION"
    print_status "Service Name: $SERVICE_NAME"
    echo
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --project)
                PROJECT_ID="$2"
                shift 2
                ;;
            --region)
                REGION="$2"
                shift 2
                ;;
            --service-name)
                SERVICE_NAME="$2"
                shift 2
                ;;
            --domain)
                DOMAIN="$2"
                shift 2
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --project PROJECT_ID    Google Cloud project ID"
                echo "  --region REGION         Deployment region (default: us-central1)"
                echo "  --service-name NAME     Cloud Run service name (default: railgpt-backend)"
                echo "  --domain DOMAIN         Custom domain to map"
                echo "  --help                  Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Update image name with new project ID
    IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE_NAME"
    
    # Run deployment steps
    check_prerequisites
    setup_project
    build_and_push
    deploy_to_cloud_run
    set_environment_variables
    test_deployment
    
    if [ ! -z "$DOMAIN" ]; then
        setup_domain
    fi
    
    echo
    print_success "Backend deployment completed successfully!"
    echo
    print_status "Service URL: $(gcloud run services describe $SERVICE_NAME --region $REGION --format="value(status.url)")"
    print_status "API Documentation: $(gcloud run services describe $SERVICE_NAME --region $REGION --format="value(status.url)")/docs"
    echo
    print_status "Next steps:"
    echo "  1. Test the API endpoints"
    echo "  2. Deploy the frontend"
    echo "  3. Set up load balancer and CDN"
    echo "  4. Configure custom domain"
    echo
    print_status "Useful commands:"
    echo "  View logs: gcloud logs read --service=$SERVICE_NAME --limit=50"
    echo "  Update service: gcloud run services update $SERVICE_NAME --region $REGION"
    echo "  Delete service: gcloud run services delete $SERVICE_NAME --region $REGION"
}

# Run main function with all arguments
main "$@"
