// Website types for RailGPT
export interface Website {
  id: string;
  url: string;
  domain: string;
  extractedAt: string; // ISO date string
  status: 'Success' | 'Failed' | 'Manual Required';
  submittedBy: string;
  domainCategory?: string;
  extractedContent?: string; // Extracted text content
  chunks?: any[]; // Array of text chunks with metadata
}

export interface WebsiteExtractionDetails {
  extractionMethod: string; // Trafilatura, BS4, Scrapy, Puppeteer
  fallbackHistory: string[]; // List of methods tried
  contentQuality: number; // 0-100
  warnings: string[];
  extractedContent: string;
  processingTime: number; // ms
  chunks: number; // Number of chunks created
}

export type ExtractionParser = 'Trafilatura' | 'BeautifulSoup' | 'Scrapy' | 'Puppeteer';
