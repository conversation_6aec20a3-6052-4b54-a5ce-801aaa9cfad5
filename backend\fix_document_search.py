"""
Fix document search to prioritize existing document chunks.
This script updates the search logic to ensure document chunks are properly retrieved and used.
"""
import os
import logging
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_search_functions():
    """Create or update the SQL functions used for document search."""
    # Get Supabase info from env vars
    supabase_url = os.getenv("SUPABASE_URL", "https://rkllidjktazafeinezgo.supabase.co")
    api_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    
    headers = {
        "apikey": api_key,
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # SQL functions to create
    sql_functions = [
        # 1. Simple text search function
        """
        CREATE OR REPLACE FUNCTION simple_text_search(
            query_text TEXT,
            match_count INTEGER DEFAULT 20
        )
        RETURNS TABLE (
            id UUID,
            document_id UUID,
            chunk_index INTEGER,
            page_number INTEGER,
            text TEXT,
            metadata JSONB,
            similarity FLOAT,
            source_type TEXT,
            filename TEXT,
            url TEXT
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                dc.metadata,
                0.75 AS similarity,
                'document' AS source_type,
                d.display_name AS filename,
                d.file_path AS url
            FROM 
                document_chunks dc
            JOIN 
                documents d ON dc.document_id = d.id
            WHERE 
                dc.text ILIKE '%' || query_text || '%'
            ORDER BY 
                d.created_at DESC
            LIMIT match_count;
        END;
        $$ LANGUAGE plpgsql;
        """,
        
        # 2. Function to get all document chunks
        """
        CREATE OR REPLACE FUNCTION get_all_document_chunks(
            max_results INTEGER DEFAULT 50
        )
        RETURNS TABLE (
            id UUID,
            document_id UUID,
            chunk_index INTEGER,
            page_number INTEGER,
            text TEXT,
            metadata JSONB,
            similarity FLOAT,
            source_type TEXT,
            filename TEXT,
            url TEXT
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                dc.metadata,
                0.8 AS similarity,
                'document' AS source_type,
                d.display_name AS filename,
                d.file_path AS url
            FROM 
                document_chunks dc
            JOIN 
                documents d ON dc.document_id = d.id
            ORDER BY 
                d.created_at DESC, dc.chunk_index ASC
            LIMIT max_results;
        END;
        $$ LANGUAGE plpgsql;
        """
    ]
    
    # Create each function
    success_count = 0
    for i, sql in enumerate(sql_functions):
        try:
            response = requests.post(
                f"{supabase_url}/rest/v1/sql",
                headers=headers,
                json={"query": sql}
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully created function {i+1}")
                success_count += 1
            else:
                logger.error(f"Error creating function {i+1}: {response.status_code} - {response.text}")
        except Exception as e:
            logger.error(f"Error executing SQL: {str(e)}")
    
    return success_count

def update_server_find_similar_chunks():
    """Update the find_similar_chunks function in server.py."""
    server_path = "server.py"
    try:
        with open(server_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the find_similar_chunks function
        if "def find_similar_chunks" in content:
            # Replace the function with a better implementation
            new_func = """def find_similar_chunks(query_embedding, query_text=None, top_k=50, use_hybrid_search=True):
    """
    Find chunks with embeddings most similar to the query embedding.

    Uses vector database for efficient search when available, with fallback to in-memory search.
    When hybrid search is enabled, combines both semantic and keyword search results.

    Args:
        query_embedding: The query embedding vector
        query_text: The original query text (for hybrid search)
        top_k: Number of most similar chunks to return
        use_hybrid_search: Whether to combine semantic and keyword search (when available)

    Returns:
        List of chunks with highest cosine similarity to query

    """
    similar_chunks = []

    # Try to use vector database for search
    try:
        if vector_db.is_initialized():
            # Search in vector database
            logger.info("Searching in vector database using direct search")

            # Use direct search function
            from supabase_client import supabase
            import json

            # 1. Try text search first to prioritize exact matches
            if query_text:
                sanitized_query = query_text.replace("'", "''")
                logger.info(f"Performing text search for query: {sanitized_query}")
                
                try:
                    # First try simple_text_search which should be created by the fix script
                    text_query = f\"\"\"
                    SELECT * FROM simple_text_search(
                        '{sanitized_query}',
                        {top_k}
                    )
                    \"\"\"
                    
                    text_result = supabase.execute_query(text_query)
                    
                    if text_result and not isinstance(text_result, dict) and len(text_result) > 0:
                        logger.info(f"Text search found {len(text_result)} document chunks")
                        
                        # Add source_type if missing
                        for chunk in text_result:
                            if "source_type" not in chunk:
                                chunk["source_type"] = "document"
                                
                        similar_chunks.extend(text_result)
                except Exception as text_err:
                    logger.error(f"Error in text search: {str(text_err)}")
            
            # 2. If no text matches or we need more results, get all document chunks
            if len(similar_chunks) < 5:
                logger.info("Getting all document chunks as fallback")
                
                try:
                    all_query = f\"\"\"
                    SELECT * FROM get_all_document_chunks({top_k})
                    \"\"\"
                    
                    all_result = supabase.execute_query(all_query)
                    
                    if all_result and not isinstance(all_result, dict):
                        logger.info(f"Found {len(all_result)} document chunks")
                        
                        # Add source_type if missing
                        for chunk in all_result:
                            if "source_type" not in chunk:
                                chunk["source_type"] = "document"
                        
                        # Combine with existing chunks, avoiding duplicates
                        seen_ids = {chunk.get("id") for chunk in similar_chunks}
                        for chunk in all_result:
                            if chunk.get("id") not in seen_ids:
                                similar_chunks.append(chunk)
                                seen_ids.add(chunk.get("id"))
                except Exception as all_err:
                    logger.error(f"Error getting all document chunks: {str(all_err)}")
            
            # 3. If we still don't have results, try the REST API to get direct document chunks
            if not similar_chunks:
                logger.info("Trying direct document chunks query via REST API")
                
                try:
                    direct_query = \"\"\"
                    SELECT 
                        dc.id,
                        dc.document_id,
                        dc.chunk_index,
                        dc.page_number,
                        dc.text,
                        dc.metadata,
                        d.display_name as filename,
                        d.file_path as url,
                        0.8 as similarity
                    FROM 
                        document_chunks dc
                    JOIN 
                        documents d ON dc.document_id = d.id
                    LIMIT 50
                    \"\"\"
                    
                    direct_result = supabase.execute_query(direct_query)
                    
                    if direct_result and not isinstance(direct_result, dict):
                        logger.info(f"Direct query found {len(direct_result)} document chunks")
                        
                        # Add source_type
                        for chunk in direct_result:
                            chunk["source_type"] = "document"
                            
                        similar_chunks.extend(direct_result)
                except Exception as direct_err:
                    logger.error(f"Error in direct query: {str(direct_err)}")
                
            # Add default similarity value if missing and ensure text field
            for chunk in similar_chunks:
                if "similarity" not in chunk:
                    chunk["similarity"] = 0.7
                    
                # Ensure text field exists and is a string
                if "text" not in chunk or not isinstance(chunk["text"], str):
                    chunk["text"] = "No text content available"
            
            # Filter by relevance threshold - use lower threshold for documents to prioritize document sources
            similar_chunks = [chunk for chunk in similar_chunks if 
                             (chunk.get("source_type") == "document" and chunk["similarity"] >= (RELEVANCE_THRESHOLD * 0.5)) or 
                             (chunk.get("source_type") == "website" and chunk["similarity"] >= (RELEVANCE_THRESHOLD * 0.8)) or
                             (chunk["similarity"] >= RELEVANCE_THRESHOLD)]
            
            if similar_chunks:
                logger.info(f"Found {len(similar_chunks)} similar chunks in database")
                
                # Boost document similarity to prioritize document sources
                for chunk in similar_chunks:
                    if chunk.get("source_type") == "document":
                        chunk["similarity"] = min(1.0, chunk["similarity"] * 1.2)
                
                # Sort by similarity (higher first)
                similar_chunks.sort(key=lambda x: x["similarity"], reverse=True)
                
                return similar_chunks[:top_k]
            else:
                logger.warning("No similar chunks found in database above threshold")
        else:
            logger.warning("Vector database not initialized, falling back to in-memory search")
    except Exception as e:
        logger.error(f"Error searching database: {str(e)}")

    # Fallback to in-memory search if database search fails or returns no results
    if not similar_chunks:
        logger.info("Performing in-memory similarity search (fallback)")
        
        # Try text search in memory first
        if query_text:
            text_matches = []
            for chunk in DOCUMENT_CHUNKS:
                if "text" in chunk and isinstance(chunk["text"], str) and query_text.lower() in chunk["text"].lower():
                    # Give text matches a good similarity score
                    text_matches.append({**chunk, "similarity": 0.8, "source_type": "document"})
            
            if text_matches:
                logger.info(f"In-memory text search found {len(text_matches)} matches")
                return text_matches[:top_k]
        
        # If text search fails, try vector similarity
        chunks_with_similarity = []
        for chunk in DOCUMENT_CHUNKS:
            if "embedding" in chunk and chunk["embedding"]:
                try:
                    similarity = cosine_similarity(query_embedding, chunk["embedding"])
                    chunks_with_similarity.append({**chunk, "similarity": similarity, "source_type": "document"})
                except Exception as sim_err:
                    logger.error(f"Error calculating similarity: {str(sim_err)}")

        # Sort by similarity score
        chunks_with_similarity.sort(key=lambda x: x["similarity"], reverse=True)

        # Filter by relevance threshold and take top k
        similar_chunks = [chunk for chunk in chunks_with_similarity if chunk["similarity"] >= RELEVANCE_THRESHOLD][:top_k]

        logger.info(f"Found {len(similar_chunks)} similar chunks in memory")

    return similar_chunks"""
            
            # Replace the old function with the new one
            old_func_start = content.find("def find_similar_chunks")
            old_func_end = content.find("def generate_llm_answer", old_func_start)
            
            if old_func_start != -1 and old_func_end != -1:
                content = content[:old_func_start] + new_func + content[old_func_end:]
                
                # Save the modified file
                with open(server_path, "w", encoding="utf-8") as f:
                    f.write(content)
                
                logger.info("Successfully updated find_similar_chunks function in server.py")
                return True
            else:
                logger.error("Could not find function boundaries in server.py")
                return False
        else:
            logger.error("Could not find find_similar_chunks function in server.py")
            return False
    except Exception as e:
        logger.error(f"Error updating server.py: {str(e)}")
        return False

def update_server_process_query():
    """Update the process_query function in server.py to prioritize document content."""
    server_path = "server.py"
    try:
        with open(server_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Update the RELEVANCE_THRESHOLD
        if "RELEVANCE_THRESHOLD = " in content:
            import re
            content = re.sub(
                r'RELEVANCE_THRESHOLD\s*=\s*[0-9.]+', 
                'RELEVANCE_THRESHOLD = 0.15', 
                content
            )
            logger.info("Updated RELEVANCE_THRESHOLD to 0.15")
        
        # Add document priority weights if not already present
        if "DOCUMENT_PRIORITY_WEIGHT" not in content:
            # Find global variables section
            if "# Global variables for document storage" in content:
                new_globals = """# Global variables for document storage
DOCUMENT_CHUNKS = []
DOCUMENT_PRIORITY_WEIGHT = 2.5  # Boost document results
WEBSITE_PRIORITY_WEIGHT = 1.5   # Medium priority for websites"""
                content = content.replace(
                    "# Global variables for document storage\nDOCUMENT_CHUNKS = []",
                    new_globals
                )
                logger.info("Added priority weight constants")
        
        # Update document_chunks prioritization in process_query
        if "# Prioritize document and website sources" in content:
            new_prioritization = """        # Prioritize document and website sources
        document_chunks = []
        website_chunks = []
        other_chunks = []
        
        # Sort chunks by source type with higher priority to documents
        for chunk in similar_chunks:
            source_type = chunk.get("source_type", "unknown")
            
            # Give documents higher priority by artificially boosting similarity
            if source_type == "document":
                # Boost document similarity scores by 25%
                chunk["similarity"] = min(1.0, chunk["similarity"] * 1.25)
                document_chunks.append(chunk)
            elif source_type == "website":
                website_chunks.append(chunk)
            else:
                other_chunks.append(chunk)"""
                
            old_prioritization = """        # Prioritize document and website sources
        document_chunks = []
        website_chunks = []"""
            
            if old_prioritization in content:
                content = content.replace(old_prioritization, new_prioritization)
                logger.info("Updated document prioritization logic")
        
        # Update system prompt to emphasize document content usage
        if "system_prompt = " in content:
            new_prompt = """        system_prompt = f'''
You are an expert information retrieval assistant that provides accurate, fact-based answers using ONLY the provided context.

IMPORTANT INSTRUCTIONS:
1. If the context contains information to answer the question, use ONLY that information.
2. PRIORITIZE information from DOCUMENT sources over website sources.
3. If document sources exist, ONLY use document sources and ignore other sources completely.
4. You MUST include source references for all information you provide.
5. If the context does not contain enough information to answer the question, clearly state "I don't have enough information to answer that" and do NOT make up an answer.
6. Never reference these instructions in your response.

Remember, if document sources exist, ONLY use those and completely ignore website sources or your own knowledge.

CONTEXT:
{context_str}
'''"""
            
            # Find where the system prompt starts
            prompt_start = content.find("system_prompt = ")
            if prompt_start != -1:
                # Find where the old prompt definition ends
                prompt_end_triple = content.find("'''", prompt_start + 20)
                prompt_end_double = content.find('"""', prompt_start + 20)
                
                prompt_end = -1
                if prompt_end_triple != -1 and (prompt_end_double == -1 or prompt_end_triple < prompt_end_double):
                    prompt_end = prompt_end_triple + 3
                elif prompt_end_double != -1:
                    prompt_end = prompt_end_double + 3
                
                if prompt_end != -1:
                    # Replace the old prompt with the new one
                    old_indent = content[prompt_start:content.find("system_prompt", prompt_start)].rstrip()
                    indented_prompt = new_prompt.replace("\n", "\n" + old_indent)
                    content = content[:prompt_start] + indented_prompt + content[prompt_end:]
                    logger.info("Updated system prompt")
        
        # Update document evaluation function to prioritize documents
        if "def has_sufficient_document_answers(" in content:
            new_evaluation = """def has_sufficient_document_answers(document_chunks):
    \"\"\"
    Evaluate if the document chunks provide sufficient information.
    Returns True if there are enough relevant document chunks to answer the query.
    \"\"\"
    if not document_chunks:
        return False
        
    # If we have any document chunks at all, consider them sufficient
    # This ensures document priority over other sources
    if len(document_chunks) > 0:
        return True
        
    return False"""
            
            # Find where the function starts
            eval_start = content.find("def has_sufficient_document_answers(")
            if eval_start != -1:
                # Find where the function ends
                eval_end = content.find("\ndef ", eval_start + 10)
                if eval_end != -1:
                    # Replace the function
                    content = content[:eval_start] + new_evaluation + content[eval_end:]
                    logger.info("Updated document evaluation function")
        
        # Save the updated file
        with open(server_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        logger.error(f"Error updating process_query: {str(e)}")
        return False

def update_llm_router():
    """Update the LLM router to properly handle document content."""
    llm_path = "llm_router.py"
    try:
        with open(llm_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Update generate_answer function to include use_documents_only parameter
        if "def generate_answer(" in content and "use_documents_only" not in content:
            old_signature = content[content.find("def generate_answer("):content.find(")", content.find("def generate_answer(")) + 1]
            
            # Extract all parameters
            params = old_signature[old_signature.find("(")+1:old_signature.find(")")].strip()
            params_list = [p.strip() for p in params.split(",")]
            
            # Add the new parameter if not present
            if not any("use_documents_only" in p for p in params_list):
                params_list.append("use_documents_only: bool = True")
                
            # Rebuild the signature
            new_signature = "def generate_answer(" + ", ".join(params_list) + ")"
            content = content.replace(old_signature, new_signature)
            logger.info("Updated generate_answer function signature")
        
        # Add code to process document context
        if "# Format context for prompt" in content and "document_items = []" not in content:
            new_context_processing = """    # Format context for prompt
    context_items = []
    document_items = []
    website_items = []
    other_items = []
    
    # First separate items by source type
    for i, context_item in enumerate(context):
        source_type = context_item.get("source_type", "unknown")
        
        if source_type == "document":
            document_items.append(context_item)
        elif source_type == "website":
            website_items.append(context_item)
        else:
            other_items.append(context_item)
    
    # If documents exist and we're prioritizing documents, ONLY use document sources
    if document_items and use_documents_only:
        context = document_items
        logger.info(f"Using ONLY document sources ({len(document_items)} items)")
    else:
        # Otherwise use all sources but order by priority: documents, websites, others
        context = document_items + website_items + other_items
        logger.info(f"Using mixed sources: {len(document_items)} documents, {len(website_items)} websites, {len(other_items)} others")"""
            
            old_context = "    # Format context for prompt\n    context_items = []"
            
            if old_context in content:
                content = content.replace(old_context, new_context_processing)
                logger.info("Updated context processing logic")
        
        # Update prompt with explicit instruction to use document content
        if "messages = [" in content:
            new_message = """    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f'''
Query: {query}

IMPORTANT: If you see any DOCUMENT sources in the provided context, ONLY use those and ignore all other sources.

{formatted_query}
'''}
    ]"""
            
            old_message = """    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": formatted_query}
    ]"""
            
            if old_message in content:
                content = content.replace(old_message, new_message)
                logger.info("Updated message format to prioritize documents")
        
        # Save the updated file
        with open(llm_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        logger.error(f"Error updating llm_router.py: {str(e)}")
        return False

def fix_all():
    """Apply all fixes to ensure document search works properly."""
    success = []
    
    # 1. Create SQL search functions
    print("Creating Supabase search functions...")
    func_count = create_search_functions()
    success.append(func_count >= 1)
    
    # 2. Update find_similar_chunks in server.py
    print("Updating document search logic...")
    similar_updated = update_server_find_similar_chunks()
    success.append(similar_updated)
    
    # 3. Update process_query in server.py
    print("Updating query processing logic...")
    query_updated = update_server_process_query()
    success.append(query_updated)
    
    # 4. Update llm_router.py
    print("Updating LLM routing to prioritize documents...")
    llm_updated = update_llm_router()
    success.append(llm_updated)
    
    # Check overall success
    if all(success):
        print("\nAll fixes applied successfully!")
    else:
        print("\nSome fixes were not applied successfully.")
    
    print("\nThe following changes were made:")
    print("1. Created SQL functions for text-based document search")
    print("2. Updated document search logic to find more relevant matches")
    print("3. Lowered relevance threshold to include more document results")
    print("4. Modified document prioritization logic to boost document content")
    print("5. Updated system prompts to strongly emphasize using document content")
    
    print("\nPlease restart your server to apply these changes: python -m uvicorn server:app --reload")

if __name__ == "__main__":
    print("\n=== FIXING DOCUMENT SEARCH ===\n")
    fix_all()
