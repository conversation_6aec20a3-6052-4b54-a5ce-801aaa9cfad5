# PowerShell script to start the React app with the OpenSSL legacy provider
Write-Host "IR App Frontend Fix & Startup Script" -ForegroundColor Green

# Set the environment variable for the current session
$env:NODE_OPTIONS = "--openssl-legacy-provider"

# Output the current settings for debugging
Write-Host "Using Node.js: $(node -v)"
Write-Host "NODE_OPTIONS: $env:NODE_OPTIONS"

# Ask user if they want to clean install dependencies
$cleanInstall = Read-Host "Would you like to clean and reinstall dependencies? (y/n)"
if ($cleanInstall -eq "y" -or $cleanInstall -eq "Y") {
    Write-Host "Removing node_modules folder..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue
    Remove-Item package-lock.json -ErrorAction SilentlyContinue
    
    Write-Host "Installing dependencies with legacy peer deps..." -ForegroundColor Yellow
    npm install --legacy-peer-deps
}

# Start the React app
Write-Host "Starting the application..." -ForegroundColor Cyan
npm start

# Keep the window open if there's an error
if ($LASTEXITCODE -ne 0) {
    Write-Host "Application exited with error code: $LASTEXITCODE" -ForegroundColor Red
    Write-Host "Press any key to continue..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
