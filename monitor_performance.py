#!/usr/bin/env python3
"""
Performance monitoring script for visual content system
"""

import requests
import time
import json
from datetime import datetime
import os

def monitor_system_performance():
    """Monitor visual content system performance"""
    print("📊 RailGPT Visual Content Performance Monitor")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    results = []
    
    # Test queries to monitor
    test_queries = [
        "Show me any tables in the document",
        "What specifications are available?", 
        "Display technical diagrams",
        "List component details from tables"
    ]
    
    print(f"\n🕐 Starting monitoring at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    for i in range(5):  # Run 5 test cycles
        print(f"\n--- Test Cycle {i+1}/5 ---")
        cycle_results = []
        
        for query in test_queries:
            try:
                start_time = time.time()
                response = requests.post(
                    f"{base_url}/api/query",
                    json={'query': query, 'model': 'gemini-2.0-flash'},
                    timeout=60
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    result = response.json()
                    query_time = end_time - start_time
                    
                    cycle_results.append({
                        'query': query,
                        'response_time': query_time,
                        'visual_found': result.get('visual_content_found', False),
                        'sources_count': len(result.get('sources', [])),
                        'answer_length': len(result.get('answer', '')),
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    print(f"   ⏱️  {query[:30]}... - {query_time:.1f}s")
                    
                else:
                    print(f"   ❌ Query failed: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        results.extend(cycle_results)
        
        # Brief pause between cycles
        if i < 4:
            time.sleep(2)
    
    # Analyze results
    print("\n📈 Performance Analysis:")
    
    if results:
        response_times = [r['response_time'] for r in results]
        avg_time = sum(response_times) / len(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        visual_detection_rate = sum(1 for r in results if r['visual_found']) / len(results)
        
        print(f"   📊 Total Queries: {len(results)}")
        print(f"   ⏱️  Average Response Time: {avg_time:.2f}s")
        print(f"   🚀 Fastest Response: {min_time:.2f}s")
        print(f"   🐌 Slowest Response: {max_time:.2f}s")
        print(f"   🎯 Visual Detection Rate: {visual_detection_rate:.1%}")
        
        # Performance rating
        if avg_time < 5:
            print("   🌟 Performance: Excellent")
        elif avg_time < 10:
            print("   ⚡ Performance: Good") 
        elif avg_time < 20:
            print("   ⚠️  Performance: Acceptable")
        else:
            print("   🔧 Performance: Needs Optimization")
        
        # Save detailed results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"performance_log_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_queries': len(results),
                    'avg_response_time': avg_time,
                    'min_response_time': min_time,
                    'max_response_time': max_time,
                    'visual_detection_rate': visual_detection_rate
                },
                'detailed_results': results
            }, f, indent=2)
        
        print(f"   💾 Detailed log saved: {filename}")
    
    # System recommendations
    print(f"\n🔧 Recommendations:")
    
    if avg_time > 15:
        print("   • Consider optimizing query processing")
        print("   • Check database indexing performance")
        print("   • Monitor server resources")
    
    if visual_detection_rate < 0.8:
        print("   • Review visual content extraction accuracy")
        print("   • Check document quality and formats")
        print("   • Verify visual query keywords")
    
    print("   • Monitor Supabase storage usage")
    print("   • Set up automated performance alerts")
    print("   • Create user feedback collection system")
    
    print(f"\n✅ Monitoring complete at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def check_system_health():
    """Quick system health check"""
    print("🏥 System Health Check")
    print("=" * 25)
    
    services = [
        ("Backend API", "http://localhost:8000/"),
        ("Frontend", "http://localhost:3000")
    ]
    
    for service_name, url in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {service_name}: Healthy")
            else:
                print(f"   ⚠️  {service_name}: Status {response.status_code}")
        except:
            print(f"   ❌ {service_name}: Unreachable")
    
    # Check backend endpoints
    backend_endpoints = [
        "/api/query",
        "/api/upload-document"
    ]
    
    print("\n🔍 Backend Endpoints:")
    for endpoint in backend_endpoints:
        try:
            # Just check if endpoint responds (may return error but that's OK)
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            print(f"   ✅ {endpoint}: Available")
        except:
            print(f"   ❌ {endpoint}: Unavailable")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "health":
        check_system_health()
    else:
        monitor_system_performance() 