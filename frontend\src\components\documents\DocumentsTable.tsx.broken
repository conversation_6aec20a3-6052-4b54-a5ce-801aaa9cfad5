import React, { useState, useEffect } from 'react';
import { Document } from '../../types/documents';
import { formatFileSize, formatDate } from '../../utils/format';
import DocumentFilterPanel from './DocumentFilterPanel';
import DocumentCategoryEditor from './DocumentCategoryEditor';

interface DocumentsTableProps {
  documents: Document[];
  onView: (document: Document) => void;
  onEdit: (document: Document) => void;
  onDelete: (document: Document) => void;
  onReprocess: (document: Document) => void;
  onCategoryUpdate: (document: Document) => void;
  onFilter?: (filteredDocuments: Document[]) => void;
}

const DocumentsTable: React.FC<DocumentsTableProps> = ({
  documents,
  onView,
  onEdit,
  onDelete,
  onReprocess,
  onCategoryUpdate,
  onFilter,
}) => {
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>(documents);
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  
  // Update filtered documents when the source documents change
  useEffect(() => {
    setFilteredDocuments(documents);
  }, [documents]);
  
  const handleFilter = (filters: any) => {
    // Apply filters to documents
    let result = [...documents];
    
    // Filter by document type/category if specified
    if (filters.documentType && filters.documentType !== 'all') {
      result = result.filter(doc => 
        doc.mainCategory === filters.documentType || 
        doc.category === filters.documentType ||
        doc.subCategory === filters.documentType
      );
    }
    
    // Filter by date range if specified
    if (filters.dateRange && filters.dateRange !== 'all') {
      const now = new Date();
      let cutoffDate = new Date();
      
      switch(filters.dateRange) {
        case 'today':
          cutoffDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          cutoffDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          cutoffDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      
      result = result.filter(doc => {
        const docDate = new Date(doc.uploadedAt);
        return docDate >= cutoffDate;
      });
    }
    
    // Filter by tags if specified
    if (filters.tags && filters.tags.length > 0) {
      result = result.filter(doc => {
        const docTags = doc.tags || [];
        return filters.tags.some((tag: string) => docTags.includes(tag));
      });
    }
    
    setFilteredDocuments(result);
    
    // Notify parent component about filtered results if callback exists
    if (onFilter) {
      onFilter(result);
    }
  };
  
  const toggleFilterPanel = () => {
    setIsFilterPanelOpen(!isFilterPanelOpen);
  };
  const [sortField, setSortField] = useState<keyof Document>('uploadedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [mainCategoryFilter, setMainCategoryFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [fileTypeFilter, setFileTypeFilter] = useState('');
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [dateRangeFilter, setDateRangeFilter] = useState<{startDate: string; endDate: string}>({startDate: '', endDate: ''});

  // Category editor state
  const [categoryEditorOpen, setCategoryEditorOpen] = useState(false);
  const [documentToEdit, setDocumentToEdit] = useState<Document | null>(null);

  // Handle sorting
  const handleSort = (field: keyof Document) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Toggle selection of all documents
  const toggleSelectAll = (docs: Document[]) => {
    if (selectedDocuments.length === docs.length) {
      setSelectedDocuments([]);
    } else {
      setSelectedDocuments(docs.map(doc => doc.id));
    }
  };

  // Toggle selection of a single document
  const toggleSelectDocument = (id: string) => {
    if (selectedDocuments.includes(id)) {
      setSelectedDocuments(selectedDocuments.filter(docId => docId !== id));
    } else {
      setSelectedDocuments([...selectedDocuments, id]);
    }
  };

  // Handle batch operations
  const handleBatchOperation = (operation: 'delete' | 'reprocess') => {
    if (selectedDocuments.length === 0) return;

    const selectedDocs = documents.filter(doc => selectedDocuments.includes(doc.id));

    if (operation === 'delete') {
      if (window.confirm(`Are you sure you want to delete ${selectedDocuments.length} selected document(s)?`)) {
        selectedDocs.forEach(doc => onDelete(doc));
        setSelectedDocuments([]);
      }
    } else if (operation === 'reprocess') {
      selectedDocs.forEach(doc => onReprocess(doc));
      setSelectedDocuments([]);
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setSearchQuery('');
    setMainCategoryFilter('');
    setCategoryFilter('');
    setStatusFilter('');
    setFileTypeFilter('');
    setDateFilter('');
    setDateRangeFilter({ startDate: '', endDate: '' });
  };

  // Handle date range filter changes
  const handleDateRangeChange = (field: 'startDate' | 'endDate', value: string) => {
    setDateRangeFilter(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle category editing
  const handleEditCategories = (document: Document) => {
    setDocumentToEdit(document);
    setCategoryEditorOpen(true);
  };

  const handleCategoryEditorClose = () => {
    setCategoryEditorOpen(false);
    setDocumentToEdit(null);
  };

  const handleCategoryUpdate = (updatedDocument: Document) => {
    if (onCategoryUpdate) {
      onCategoryUpdate(updatedDocument);
    }
  };

  // Filter documents based on all filters
  const applyFilters = () => documents.filter((doc) => {
    // Search query filter (check name and other fields)
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesQuery =
        doc.name.toLowerCase().includes(query) ||
        doc.uploadedBy?.toLowerCase().includes(query) ||
        doc.category?.toLowerCase().includes(query) ||
        doc.mainCategory?.toLowerCase().includes(query);

      if (!matchesQuery) return false;
    }

    // Main category filter
    if (mainCategoryFilter && doc.mainCategory !== mainCategoryFilter) {
      return false;
    }

    // Category filter
    if (
      categoryFilter &&
      !(
        doc.mainCategory === categoryFilter ||
        doc.category === categoryFilter ||
        doc.subCategory === categoryFilter ||
        doc.minorCategory === categoryFilter
      )
    ) {
      return false;
    }

    // Status filter
    if (statusFilter && doc.status !== statusFilter) {
      return false;
    }

    // File type filter
    if (fileTypeFilter && doc.fileType !== fileTypeFilter) {
      return false;
    }

    // Date filter (last day, week, month)
    if (dateFilter) {
      const docDate = new Date(doc.uploadedAt);
      const now = new Date();

      if (dateFilter === 'day') {
        // Last 24 hours
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        if (docDate < yesterday) return false;
      } else if (dateFilter === 'week') {
        // Last 7 days
        const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        if (docDate < lastWeek) return false;
      } else if (dateFilter === 'month') {
        // Last 30 days
        const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        if (docDate < lastMonth) return false;
      }
    }

    // Date range filter
    if (dateRangeFilter.startDate || dateRangeFilter.endDate) {
      const docDate = new Date(doc.uploadedAt);

      if (dateRangeFilter.startDate) {
        const startDate = new Date(dateRangeFilter.startDate);
        // Set to beginning of day
        startDate.setHours(0, 0, 0, 0);
        if (docDate < startDate) return false;
      }

      if (dateRangeFilter.endDate) {
        const endDate = new Date(dateRangeFilter.endDate);
        // Set to end of day
        endDate.setHours(23, 59, 59, 999);
        if (docDate > endDate) return false;
      }
    }

    return true;
  });

  // Sort documents
  const sortedDocuments = [...filteredDocuments].sort((a, b) => {
    // Use optional chaining and nullish coalescing to handle undefined
    const aValue = a[sortField] ?? '';
    const bValue = b[sortField] ?? '';

    if (aValue < bValue) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });

  // Format the date for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return new Intl.DateTimeFormat('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Render status badge
  const renderStatusBadge = (status: Document['status']) => {
    let bgColor;
    switch (status) {
      case 'Extracted':
        bgColor = 'bg-green-100 text-green-800';
        break;
      case 'Pending':
        bgColor = 'bg-yellow-100 text-yellow-800';
        break;
      case 'Manual Review':
        bgColor = 'bg-red-100 text-red-800';
        break;
      default:
        bgColor = 'bg-gray-100 text-gray-800';
    }

    return (
      <span className={`${bgColor} px-2 py-1 rounded-full text-xs font-medium`}>
        {status}
      </span>
    );
  };

  // Get unique categories and other filter options
  const uniqueMainCategories = Array.from(new Set(documents.map(doc => doc.mainCategory).filter(Boolean)));
  const uniqueCategories = Array.from(
    new Set(
      documents.flatMap((doc) => [
        doc.category,
        doc.subCategory,
        doc.minorCategory,
      ]).filter(Boolean)
    )
  );
  const uniqueFileTypes = Array.from(new Set(documents.map(doc => doc.fileType).filter(Boolean)));

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-lg font-semibold">Documents ({filteredDocuments.length})</h2>
        <button
          onClick={toggleFilterPanel}
          className={`px-3 py-1 rounded ${isFilterPanelOpen ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800'}`}
        >
          {isFilterPanelOpen ? 'Hide Filters' : 'Show Filters'}
        </button>
      </div>
      
      {isFilterPanelOpen && (
        <div className="p-4 border-b border-gray-200">
          <DocumentFilterPanel onFilter={handleFilter} />
        </div>
      )}
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <h2 aria-label="Filter controls" className="text-lg font-semibold">Manage Documents</h2>

          <div className="flex space-x-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md flex items-center text-sm"
            >
              <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
            </button>

            {selectedDocuments.length > 0 && (
              <div className="flex space-x-2">
                <button
                  onClick={() => handleBatchOperation('reprocess')}
                  className="px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 rounded-md text-sm"
                >
                  Reprocess ({selectedDocuments.length})
                </button>
                <button
                  onClick={() => handleBatchOperation('delete')}
                  className="px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm"
                >
                  Delete ({selectedDocuments.length})
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Basic Search */}
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="md:w-1/2 lg:w-2/3">
            <input
              type="text"
              placeholder="Search documents by name, category, or uploader..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="md:w-1/2 lg:w-1/3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="Extracted">Extracted</option>
              <option value="Pending">Pending</option>
              <option value="Processing">Processing</option>
              <option value="Manual Review">Manual Review</option>
              <option value="Failed">Failed</option>
            </select>
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="p-4 bg-gray-100 rounded-lg mt-4 mb-6">
            <div className="flex flex-col space-y-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="md:w-1/3">
                  <label className="block text-xs font-medium text-gray-700 mb-1">Main Category</label>
                  <select
                    value={mainCategoryFilter}
                    onChange={(e) => setMainCategoryFilter(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Main Categories</option>
                    {uniqueMainCategories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="md:w-1/3">
                  <label className="block text-xs font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Categories</option>
                    {uniqueCategories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="md:w-1/3">
                  <label className="block text-xs font-medium text-gray-700 mb-1">File Type</label>
                  <select
                    value={fileTypeFilter}
                    onChange={(e) => setFileTypeFilter(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All File Types</option>
                    {uniqueFileTypes.map((fileType) => (
                      <option key={fileType} value={fileType}>
                        {fileType.toUpperCase()}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Second row with date filters */}
              <div className="flex flex-col md:flex-row gap-4">
                <div className="md:w-1/3">
                  <label className="block text-xs font-medium text-gray-700 mb-1">Date Added</label>
                  <select
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Time</option>
                    <option value="day">Last 24 Hours</option>
                    <option value="week">Last 7 Days</option>
                    <option value="month">Last 30 Days</option>
                  </select>
                </div>

                <div className="md:w-1/3">
                  <label className="block text-xs font-medium text-gray-700 mb-1">Date Range</label>
                  <div className="flex space-x-2">
                    <div className="w-1/2">
                      <input
                        type="date"
                        id="start-date"
                        value={dateRangeFilter.startDate}
                        onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Start date"
                      />
                    </div>
                    <div className="w-1/2">
                      <input
                        type="date"
                        id="end-date"
                        value={dateRangeFilter.endDate}
                        onChange={(e) => handleDateRangeChange('endDate', e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="End date"
                      />
                    </div>
                  </div>
                </div>

                <div className="md:w-1/3 flex items-end">
                  <button
                    onClick={resetFilters}
                    className="px-3 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md text-sm w-full"
                  >
                    Reset Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="text-sm text-gray-500 mb-2">
          Showing {filteredDocuments.length} of {documents.length} documents
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-3 py-3 text-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                  checked={selectedDocuments.length > 0 && selectedDocuments.length === filteredDocuments.length}
                  onChange={() => toggleSelectAll(filteredDocuments)}
                />
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('name')}
              >
                Name
                {sortField === 'name' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('uploadedAt')}
              >
                Date Uploaded
                {sortField === 'uploadedAt' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('mainCategory')}
              >
                Category
                {sortField === 'mainCategory' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('uploadedBy')}
              >
                Uploaded By
                {sortField === 'uploadedBy' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('qualityScore')}
              >
                Quality
                {sortField === 'qualityScore' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('status')}
              >
                Status
                {sortField === 'status' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      onChange={() => toggleSelectDocument(document.id)}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {document.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {document.fileType.toUpperCase()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(document.uploadedAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {document.mainCategory || 'N/A'}
                      {document.category && <span> / {document.category}</span>}
                    </div>
                    {document.subCategory && (
                      <div className="text-xs text-gray-500">
                        {document.subCategory}
                        {document.minorCategory && <span> / {document.minorCategory}</span>}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {document.uploadedBy || 'Unknown'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {document.qualityScore !== undefined ? (
                      <div className="flex items-center">
                        <div className="w-24 bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-blue-600 h-2.5 rounded-full"
                            style={{ width: `${document.qualityScore}%` }}
                          ></div>
                          <div
                            className="bg-gray-300 h-2.5 rounded-r-full"
                            style={{
                              width: `${100 - document.qualityScore}%`,
                              float: 'right',
                            }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-700 ml-2">
                          {document.qualityScore}%
                        </span>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500">N/A</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderStatusBadge(document.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2 justify-end">
                      <button
                        onClick={() => onView(document)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View document details"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => onEdit(document)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Edit document"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => handleEditCategories(document)}
                        className="text-purple-600 hover:text-purple-900"
                        title="Edit categories"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => onReprocess(document)}
                        className="text-yellow-600 hover:text-yellow-900"
                        title="Reprocess document"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                      </button>
                      <button
                        onClick={() => onDelete(document)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete document"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                  No documents found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Category Editor Modal */}
      {documentToEdit && (
        <DocumentCategoryEditor
          document={documentToEdit}
          isOpen={categoryEditorOpen}
          onClose={handleCategoryEditorClose}
          onSave={handleCategoryUpdate}
        />
      )}
    </div>
  );
};

export default DocumentsTable;
