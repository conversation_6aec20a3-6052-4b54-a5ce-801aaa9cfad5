"""
Script to directly search for documents by title or content.
"""
import os
import logging
import json
from typing import List, Dict, Any
from dotenv import load_dotenv
from supabase_client import supabase
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def search_documents_by_title(query: str, limit: int = 5):
    """Search for documents by title."""
    logger.info(f"Searching for documents with title containing '{query}'...")
    
    # Prepare the query
    search_query = f"""
    SELECT
        d.id,
        d.display_name,
        d.file_path,
        d.file_type,
        d.created_at,
        d.updated_at
    FROM
        documents d
    WHERE
        LOWER(d.display_name) LIKE LOWER('%{query}%')
    LIMIT {limit}
    """
    
    try:
        result = supabase.execute_query(search_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by title: {result['error']}")
            return []
        
        logger.info(f"Found {len(result)} documents matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by title: {str(e)}")
        return []

def get_document_chunks(document_id: str, limit: int = 10):
    """Get chunks for a specific document."""
    logger.info(f"Getting chunks for document {document_id}...")
    
    # Prepare the query
    chunks_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.document_id = '{document_id}'
    ORDER BY
        dc.page_number, dc.chunk_index
    LIMIT {limit}
    """
    
    try:
        result = supabase.execute_query(chunks_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error getting document chunks: {result['error']}")
            return []
        
        logger.info(f"Found {len(result)} chunks for document {document_id}")
        return result
    except Exception as e:
        logger.error(f"Error getting document chunks: {str(e)}")
        return []

def search_documents_by_content(query: str, limit: int = 5):
    """Search for documents by content using text search."""
    logger.info(f"Searching for documents with content containing '{query}'...")
    
    # Prepare the query
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type,
        ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', '{query}')) AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        to_tsvector('english', dc.text) @@ plainto_tsquery('english', '{query}')
    ORDER BY
        similarity DESC
    LIMIT {limit}
    """
    
    try:
        result = supabase.execute_query(search_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by content: {result['error']}")
            return []
        
        logger.info(f"Found {len(result)} document chunks matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by content: {str(e)}")
        return []

def generate_answer(query: str, chunks: List[Dict[str, Any]]):
    """Generate an answer based on the document chunks."""
    logger.info(f"Generating answer for query '{query}' with {len(chunks)} chunks...")
    
    if not chunks:
        return "No relevant documents found."
    
    # Prepare context from chunks
    context_texts = []
    
    for chunk in chunks:
        # Skip if no text
        if not chunk.get("text"):
            continue
        
        # Format the context text
        document_name = chunk.get("filename", "Unknown Document")
        page_number = chunk.get("page_number", 0)
        
        # Add to context
        context_text = f"From '{document_name}' (Page {page_number}):\n{chunk.get('text', '')}"
        context_texts.append(context_text)
    
    # Combine context texts
    context = "\n\n".join(context_texts)
    
    # Generate answer
    system_prompt = f"""
You are RailGPT, an expert information retrieval assistant specializing in Indian Railways.

CRITICAL INSTRUCTIONS:
1. You MUST ONLY use the information provided in the context below to answer the question.
2. EXAMINE all provided context CAREFULLY and EXTRACT relevant information to answer the question.
3. You MUST include source references for ALL information you provide (document names, pages).
4. If the context DOES NOT contain relevant information to answer the question CLEARLY STATE: "I couldn't find any valid information to answer your question." DO NOT make up an answer.
5. DO NOT use your general knowledge under any circumstances.
6. Format your response in a clear, readable manner with proper paragraphs and bullet points where appropriate.

The context information below contains actual document content that has been retrieved based on the user's query.
DO NOT say things like "Based on the context provided" or "According to the information given" - just provide the answer directly with references.

CONTEXT:
{context}
"""
    
    try:
        answer = llm_router.generate_answer(
            query=query,
            context=context,
            model_id="gemini-2.0-flash",
            system_prompt=system_prompt
        )
        
        return answer
    except Exception as e:
        logger.error(f"Error generating answer: {str(e)}")
        return "Error generating answer."

def main():
    """Main function to test direct document search."""
    # Test queries
    test_queries = [
        "What is the full form of ACP?",
        "What is the full form of FSDS?",
        "What is the Rapid Response app?",
        "What is VASP and who developed it?"
    ]
    
    for query in test_queries:
        logger.info(f"\n=== Testing query: '{query}' ===\n")
        
        # Search for documents by title
        title_results = search_documents_by_title(query.split()[-1])
        
        # Search for documents by content
        content_results = search_documents_by_content(query)
        
        # Combine results
        all_chunks = []
        
        # Add chunks from title search
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id:
                chunks = get_document_chunks(doc_id)
                all_chunks.extend(chunks)
        
        # Add chunks from content search
        all_chunks.extend(content_results)
        
        # Generate answer
        if all_chunks:
            answer = generate_answer(query, all_chunks)
            logger.info(f"Answer: {answer}")
        else:
            logger.info("No relevant documents found.")

if __name__ == "__main__":
    main()
