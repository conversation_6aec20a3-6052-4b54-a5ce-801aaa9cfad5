-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create documents table
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename TEXT NOT NULL,
    display_name TEXT,
    file_path TEXT NOT NULL,
    file_type TEXT,
    file_size INTEGER,
    main_category TEXT,
    category TEXT,
    sub_category TEXT,
    minor_category TEXT,
    uploaded_by UUID,
    status TEXT DEFAULT 'processed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create document_chunks table with vector support
CREATE TABLE IF NOT EXISTS document_chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    embedding vector(768),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create websites table
CREATE TABLE IF NOT EXISTS websites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL UNIQUE,
    domain TEXT,
    title TEXT,
    description TEXT,
    category TEXT,
    website_category_id UUID REFERENCES website_categories(id),
    submitted_by UUID,
    status TEXT DEFAULT 'processed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create website_chunks table with vector support
CREATE TABLE IF NOT EXISTS website_chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    website_id UUID REFERENCES websites(id) ON DELETE CASCADE,
    chunk_index INTEGER,
    text TEXT,
    embedding vector(768),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create queries table
CREATE TABLE IF NOT EXISTS queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_text TEXT NOT NULL,
    answer_text TEXT,
    llm_model TEXT,
    sources JSONB,
    processing_time FLOAT,
    user_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create categories table for hierarchical categorization
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('main_category', 'category', 'sub_category', 'minor_category')),
    parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name, type, parent_id)
);

-- Create website_categories table for website domain categorization
CREATE TABLE IF NOT EXISTS website_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_website_chunks_website_id ON website_chunks(website_id);
CREATE INDEX IF NOT EXISTS idx_documents_uploaded_by ON documents(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_websites_submitted_by ON websites(submitted_by);
CREATE INDEX IF NOT EXISTS idx_queries_user_id ON queries(user_id);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);
CREATE INDEX IF NOT EXISTS idx_website_categories_name ON website_categories(name);

-- Create vector indexes for similarity search
CREATE INDEX IF NOT EXISTS idx_document_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_website_chunks_embedding ON website_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS search_document_chunks(vector, float, int);

-- Create function for document similarity search
CREATE OR REPLACE FUNCTION search_document_chunks(
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        dc.metadata,
        d.file_path as url,
        d.main_category as domain,
        d.display_name as title,
        1 - (dc.embedding <=> query_embedding) as similarity,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE 1 - (dc.embedding <=> query_embedding) > match_threshold
    ORDER BY dc.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS search_website_chunks(vector, float, int);

-- Function for searching website chunks using vector similarity
CREATE OR REPLACE FUNCTION search_website_chunks(
    query_embedding vector,
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id uuid,
    website_id uuid,
    chunk_index int,
    text text,
    metadata jsonb,
    url text,
    domain text,
    title text,
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id,
        wc.website_id,
        wc.chunk_index,
        wc.text,
        wc.metadata,
        w.url,
        w.domain,
        w.title,
        1 - (wc.embedding <=> query_embedding) as similarity
    FROM website_chunks wc
    JOIN websites w ON wc.website_id = w.id
    WHERE 1 - (wc.embedding <=> query_embedding) > match_threshold
    ORDER BY wc.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;