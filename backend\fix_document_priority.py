"""
Fix script to prioritize document and website content over AI model responses
in the IR App system. This script modifies key parameters to ensure uploaded
documents and extracted websites are properly used for answering queries.
"""
import os
import re
import shutil
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Files to modify
server_file = 'server.py'
vector_db_file = 'vector_db.py'
llm_router_file = 'llm_router.py'

def backup_file(filename):
    """Create a backup of the file before modifying it."""
    backup = f"{filename}.bak"
    if os.path.exists(filename):
        shutil.copy2(filename, backup)
        logger.info(f"Created backup of {filename} to {backup}")

def update_server_file():
    """Update server.py to prioritize document and website content."""
    logger.info("Fixing server.py for document priority...")
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Fix 1: Lower the relevance threshold to include more document matches
        content = re.sub(
            r'RELEVANCE_THRESHOLD = 0\.\d+',
            'RELEVANCE_THRESHOLD = 0.2  # Lowered to include more document matches',
            content
        )
        
        # Fix 2: Increase source priority weights for documents and websites
        content = re.sub(
            r'SOURCE_PRIORITY = \{[\s\n]*"document": \d+\.\d+,[\s\n]*"website": \d+\.\d+[\s\n]*\}',
            'SOURCE_PRIORITY = {\n    "document": 2.0,  # Higher priority for documents\n    "website": 1.5    # Higher priority for websites\n}',
            content
        )
        
        # Fix 3: Modify document answer evaluation to have stricter criteria for fallback
        # This makes it less likely to use AI when document content is available
        pattern = r'if\s+"\w+\'t\s+contain"\s+in\s+document_answer\.lower\(\)\s+or\s+"\w+\'t\s+contain"\s+in\s+document_answer\.lower\(\)\s+or\s+"\w+\s+not\s+contain"\s+in\s+document_answer\.lower\(\):'
        replacement = '''if (
                        "provided documents don't contain information about this topic" in document_answer.lower() or
                        "the documents don't contain any information about this" in document_answer.lower() or
                        "i couldn't find any information about this in the provided documents" in document_answer.lower() or
                        "i couldn't find any relevant information in the documents" in document_answer.lower()):'''
        content = re.sub(pattern, replacement, content)
        
        # Fix 4: Modify the system prompt to make the LLM prioritize document information
        system_prompt_pattern = r'''system_prompt = '''
        system_prompt_replacement = '''system_prompt = """You are RailGPT, an AI assistant that specializes in Indian Railways.
            CRITICAL INSTRUCTION: Your PRIMARY goal is to answer questions using ONLY the information from the provided context (documents and websites).
            
            Follow these steps strictly:
            1. Carefully analyze if the provided context contains ANY information relevant to the question, even partial matches.
            2. If there is ANY relevant information in the context, use ONLY that information to provide your answer. DO NOT add anything from your general knowledge.
            3. If using information from context, clearly cite the source (document name, page number, or website URL).
            4. ONLY if the context has ABSOLUTELY NO relevant information, state "I couldn't find information about this specific topic in the available documents or websites" and then provide a brief answer based on general knowledge.
            
            IMPORTANT: You must analyze the context thoroughly - even if it appears only partially relevant at first glance.
            Always prioritize document information over website information, and both over general knowledge."""'''
        content = re.sub(system_prompt_pattern, system_prompt_replacement, content)
        
        # Write changes back to the file
        with open(server_file, 'w', encoding='utf-8') as f:
            f.write(content)
            
        logger.info("Successfully updated server.py")
    except Exception as e:
        logger.error(f"Error updating server.py: {str(e)}")

def update_vector_db_file():
    """Update vector_db.py to enhance document retrieval."""
    logger.info("Fixing vector_db.py for improved document retrieval...")
    
    try:
        with open(vector_db_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix 1: Increase top_k parameter in search methods to return more results
        content = re.sub(
            r'def search\(self, query_embedding: List\[float\], top_k: int = \d+,\s+threshold: float = 0\.\d+\)',
            'def search(self, query_embedding: List[float], top_k: int = 50, threshold: float = 0.18)',
            content
        )
        
        # Fix 2: Increase top_k and lower threshold in hybrid_search method
        content = re.sub(
            r'def hybrid_search\(self, query_embedding: List\[float\], query_text: str = None, top_k: int = \d+,\s+threshold: float = 0\.\d+\)',
            'def hybrid_search(self, query_embedding: List[float], query_text: str = None, top_k: int = 50, threshold: float = 0.15)',
            content
        )
        
        # Fix 3: Increase document match count in hybrid search
        content = re.sub(
            r'match_count=top_k \* \d+',
            'match_count=top_k * 3  # Get many more document results',
            content
        )
        
        # Write changes back to the file
        with open(vector_db_file, 'w', encoding='utf-8') as f:
            f.write(content)
            
        logger.info("Successfully updated vector_db.py")
    except Exception as e:
        logger.error(f"Error updating vector_db.py: {str(e)}")

def update_llm_router_file():
    """Update llm_router.py to ensure document content is prioritized."""
    logger.info("Fixing llm_router.py...")
    
    try:
        with open(llm_router_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Modify generate_answer method to prioritize document context
        prompt_engineering_fix = '''
def prioritize_document_context(context, query):
    """
    Analyze and prepare the context to ensure document sources are prioritized.
    """
    if not context:
        return context
        
    # Split into individual sources
    context_parts = context.split("\n\n")
    
    # Group sources by type
    doc_parts = [part for part in context_parts if "From '" in part and ".pdf" in part.lower()]
    web_parts = [part for part in context_parts if "From '" in part and ("http" in part.lower() or "www" in part.lower())]
    other_parts = [part for part in context_parts if part not in doc_parts and part not in web_parts]
    
    # Prioritize document parts first, then websites, then others
    prioritized_parts = doc_parts + web_parts + other_parts
    
    # Rejoin with more emphasis on the importance of document content
    if doc_parts:
        prioritized_context = "DOCUMENT SOURCES (PRIORITIZE THESE):\n" + "\n\n".join(doc_parts)
        if web_parts:
            prioritized_context += "\n\nWEBSITE SOURCES:\n" + "\n\n".join(web_parts)
        if other_parts:
            prioritized_context += "\n\nOTHER SOURCES:\n" + "\n\n".join(other_parts)
        return prioritized_context
    else:
        return context
'''
        
        # Insert the new function before generate_answer
        pattern = r'def generate_answer\('
        content = re.sub(pattern, prompt_engineering_fix + '\n\ndef generate_answer(', content)
        
        # Modify the generate_answer function to use our new helper
        pattern = r'(def generate_answer.*?context: str,.*?\n\s+)(?=\s+system_prompt)'
        replacement = r'\1    # Prioritize document content in the context\n    context = prioritize_document_context(context, query)\n    \n    '
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # Write changes back to the file
        with open(llm_router_file, 'w', encoding='utf-8') as f:
            f.write(content)
            
        logger.info("Successfully updated llm_router.py")
    except Exception as e:
        logger.error(f"Error updating llm_router.py: {str(e)}")

def main():
    """Apply all fixes to prioritize document and website content."""
    logger.info("Starting document priority fixes...")
    
    # Create backups
    backup_file(server_file)
    backup_file(vector_db_file)
    backup_file(llm_router_file)
    
    # Apply fixes
    update_server_file()
    update_vector_db_file()
    update_llm_router_file()
    
    logger.info("All fixes have been applied. Your IR App should now prioritize document and website content over AI model responses.")
    logger.info("Restart the server for changes to take effect.")

if __name__ == "__main__":
    main()
