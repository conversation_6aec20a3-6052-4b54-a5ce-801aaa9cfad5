import React, { useState } from 'react';

interface InteractiveQueryProps {
  content: string;
  onEdit: (newContent: string) => void;
}

const InteractiveQuery: React.FC<InteractiveQueryProps> = ({ content, onEdit }) => {
  const [isHovering, setIsHovering] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(content);

  const handleCopy = () => {
    navigator.clipboard.writeText(content);
    // Optional: Show a toast/notification that text was copied
  };

  const handleEditClick = () => {
    setIsEditing(true);
    setEditedContent(content);
  };

  const handleSaveEdit = () => {
    if (editedContent.trim() !== '') {
      onEdit(editedContent);
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditedContent(content);
    }
  };

  return (
    <div 
      className="relative" 
      onMouseEnter={() => setIsHovering(true)} 
      onMouseLeave={() => setIsHovering(false)}
    >
      {isEditing ? (
        <div className="flex flex-col">
          <textarea
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full p-2 border border-gray-300 rounded-md mb-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            autoFocus
          />
          <div className="flex justify-end space-x-2">
            <button 
              onClick={() => {
                setIsEditing(false);
                setEditedContent(content);
              }}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
            >
              Cancel
            </button>
            <button 
              onClick={handleSaveEdit}
              className="px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded"
            >
              Save
            </button>
          </div>
        </div>
      ) : (
        <>
          <div className="whitespace-pre-wrap">{content}</div>
          {isHovering && (
            <div className="absolute right-0 bottom-0 flex space-x-2 text-xs bg-gray-100 bg-opacity-90 p-1 rounded">
              <button 
                onClick={handleEditClick} 
                className="text-blue-600 hover:text-blue-800" 
                title="Edit query"
              >
                🖉 Edit
              </button>
              <button 
                onClick={handleCopy} 
                className="text-green-600 hover:text-green-800" 
                title="Copy to clipboard"
              >
                📋 Copy
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default InteractiveQuery;
