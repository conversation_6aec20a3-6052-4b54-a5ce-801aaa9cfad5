import React, { useState, useEffect } from 'react';

interface VisualContentProps {
  source: {
    content_type?: string;
    visual_content?: Record<string, any>;
    storage_url?: string;
    display_type?: string;
    filename?: string;
    page?: number;
  };
}

const VisualContent: React.FC<VisualContentProps> = ({ source }) => {
  const [selectedTab, setSelectedTab] = useState<string>('content');
  const [imageLoading, setImageLoading] = useState<boolean>(true);
  const [imageError, setImageError] = useState<boolean>(false);
  const [imageTries, setImageTries] = useState<number>(0);
  const [cacheBuster, setCacheBuster] = useState<string>(`cb-${Date.now()}`);
  const [visibleDebug, setVisibleDebug] = useState<boolean>(false);
  
  // Refresh image on source change
  useEffect(() => {
    // Reset state when source changes
    setImageLoading(true);
    setImageError(false);
    setImageTries(0);
    setCacheBuster(`cb-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`);
    
    console.log('🔄 Visual content source changed:', {
      content_type: source.content_type,
      filename: source.filename,
      page: source.page,
      has_visual_content: !!source.visual_content,
      storage_url: source.storage_url ? `${source.storage_url.substring(0, 30)}...` : 'None',
    });
  }, [source.filename, source.page, source.storage_url]);

  if (!source.content_type || source.content_type === 'text') {
    return null; // No visual content to display
  }

  const renderTableContent = () => {
    const visualContent = source.visual_content;
    
    if (!visualContent) return null;

    // If we have table data, render it as a proper table
    if (visualContent.table_data && Array.isArray(visualContent.table_data)) {
      const tableData = visualContent.table_data;
      if (tableData.length === 0) return null;

      const headers = tableData[0] || [];
      const rows = tableData.slice(1);

      return (
        <div className="overflow-x-auto w-full">
          <table className="min-w-full bg-white border border-gray-200 rounded-lg table-fixed">
            <thead className="bg-gray-50">
              <tr>
                {headers.map((header: any, idx: number) => (
                  <th 
                    key={idx}
                    className="px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200"
                    style={{ minWidth: '100px', maxWidth: '200px' }}
                  >
                    {String(header || '').trim() || `Column ${idx + 1}`}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {rows.map((row: any[], rowIdx: number) => (
                <tr key={rowIdx} className="hover:bg-gray-50 transition-colors">
                  {headers.map((_: any, cellIdx: number) => (
                    <td 
                      key={cellIdx}
                      className="px-3 py-2 text-sm text-gray-700 border-b border-gray-100 truncate"
                      title={String(row[cellIdx] || '')}
                    >
                      {String(row[cellIdx] || '').trim() || '-'}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }
    
    // If we have markdown table, render it as HTML
    if (visualContent.markdown_table) {
      return (
        <div className="overflow-x-auto w-full">
          <div className="bg-white rounded-lg border border-gray-200">
            <div 
              className="prose prose-sm max-w-none p-0"
              dangerouslySetInnerHTML={{
                __html: markdownTableToHtml(visualContent.markdown_table)
              }}
            />
          </div>
        </div>
      );
    }

    // Fallback: Try to display as text table if available
    if (visualContent.text_table) {
      return (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
            {visualContent.text_table}
          </pre>
        </div>
      );
    }

    return (
      <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-600">
        Table data is not available in a displayable format.
        {visualContent && (
          <div className="mt-2 text-xs">
            Available data: {Object.keys(visualContent).join(', ')}
          </div>
        )}
      </div>
    );
  };

  const getImageSources = () => {
    const visualContent = source.visual_content;
    const sources = [];
    
    // 1. Try storage URL if available (with cache buster)
    if (source.storage_url) {
      const hasQueryParams = source.storage_url.includes('?');
      const cacheParam = hasQueryParams ? `&_cb=${cacheBuster}` : `?_cb=${cacheBuster}`;
      sources.push({
        url: `${source.storage_url}${cacheParam}`,
        type: 'Storage URL'
      });
    }
    
    // 2. Try base64 data if available
    if (visualContent?.base64_data) {
      sources.push({
        url: `data:image/png;base64,${visualContent.base64_data}`,
        type: 'Base64 Data'
      });
    }
    
    // 3. Generate path based on filename and page
    if (source.filename && source.page !== undefined) {
      const safeName = source.filename.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
      sources.push({
        url: `/images/${safeName}_page${source.page}.png?_cb=${cacheBuster}`,
        type: 'Generated Path'
      });
    }
    
    // 4. Logo as absolute fallback
    sources.push({
      url: `/logo512.png?_cb=${cacheBuster}`,
      type: 'Default Logo'
    });
    
    return sources;
  };

  const renderImageContent = () => {
    const visualContent = source.visual_content;
    
    console.log('DEBUG: Rendering image for', source.filename, 'page', source.page);
    console.log('DEBUG: storage_url =', source.storage_url);
    console.log('DEBUG: base64_data available =', !!visualContent?.base64_data);
    
    // Show debug info with image
    const debugInfo = (
      <div className="bg-blue-50 p-2 mb-2 rounded text-xs">
        <p><strong>File:</strong> {source.filename || 'Unknown'}</p>
        <p><strong>Page:</strong> {source.page || 'Unknown'}</p>
        <p><strong>URL:</strong> {source.storage_url ? 'Available' : 'Not available'}</p>
        <p><strong>Time:</strong> {new Date().toISOString()}</p>
      </div>
    );

    // Try to find actual image matching Project 1, Project 2, etc. in query response
    if (source.content_type === 'image' && source.filename && source.filename.includes('Project')) {
      return (
        <div className="relative">
          {debugInfo}
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          )}
          <img
            src={source.storage_url || `/logo512.png?t=${Date.now()}`}
            alt={`Image from page ${source.page} of ${source.filename}`}
            className="max-w-full h-auto rounded-lg border border-gray-200 shadow-md"
            onLoad={() => {
              console.log('DEBUG: Image loaded successfully');
              setImageLoading(false);
            }}
            onError={(e) => {
              console.error('DEBUG: Image failed to load:', e);
              setImageLoading(false);
              setImageError(true);
            }}
          />
          {imageError && (
            <div className="bg-red-50 rounded-lg p-4 text-center text-gray-600 mt-2">
              <p>Image could not be loaded</p>
              <p className="text-xs mt-1">Please check the document and try again</p>
              <button 
                onClick={() => {
                  console.log('DEBUG: Retrying image load');
                  setImageLoading(true);
                  setImageError(false);
                }}
                className="mt-2 px-2 py-1 bg-blue-100 rounded text-sm"
              >
                Retry
              </button>
            </div>
          )}
        </div>
      );
    }
    
    // Try storage URL first
    if (source.storage_url) {
      return (
        <div className="relative">
          {debugInfo}
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          )}
          <img
            src={`${source.storage_url}?t=${Date.now()}`}
            alt={`Image from page ${source.page} of ${source.filename}`}
            className={`max-w-full h-auto rounded-lg border border-gray-200 ${imageLoading ? 'opacity-0' : 'opacity-100'}`}
            onLoad={() => setImageLoading(false)}
            onError={() => {
              console.error('DEBUG: Storage URL image failed to load:', source.storage_url);
              setImageLoading(false);
              setImageError(true);
            }}
          />
          {imageError && (
            <div className="bg-gray-100 rounded-lg p-4 text-center text-gray-600">
              <p>Image could not be loaded</p>
              <p className="text-xs mt-1">URL: {source.storage_url?.substring(0, 30)}...</p>
              <button 
                onClick={() => {
                  console.log('DEBUG: Retrying image load');
                  setImageLoading(true);
                  setImageError(false);
                }}
                className="mt-2 px-2 py-1 bg-blue-100 rounded text-sm"
              >
                Retry
              </button>
            </div>
          )}
        </div>
      );
    }

    // Try base64 data if available
    if (visualContent?.base64_data) {
      return (
        <div>
          {debugInfo}
          <img
            src={`data:image/png;base64,${visualContent.base64_data}`}
            alt={`Image from page ${source.page} of ${source.filename}`}
            className="max-w-full h-auto rounded-lg border border-gray-200"
            onError={() => {
              console.error('DEBUG: Base64 image failed to load');
              setImageError(true);
            }}
          />
        </div>
      );
    }

    // Default to logo if no other image is available
    return (
      <div className="bg-gray-50 rounded-lg p-4 text-center">
        {debugInfo}
        <p className="text-sm text-gray-600 mb-2">No image content available from the document</p>
        <img 
          src={`/logo512.png?t=${Date.now()}`}
          alt="Default logo"
          className="max-w-full h-auto rounded-lg border border-gray-200 mx-auto"
          style={{ maxHeight: '200px' }}
        />
      </div>
    );
  };

  const renderChartContent = () => {
    const visualContent = source.visual_content;
    
    if (!visualContent) return null;

    return (
      <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200">
        <h4 className="font-semibold text-purple-800 mb-3 flex items-center">
          📊 Chart/Diagram Detected
        </h4>
        <div className="space-y-3">
          {visualContent.description && (
            <p className="text-sm text-gray-700">{visualContent.description}</p>
          )}
          <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
            {visualContent.drawing_count && (
              <div>
                <span className="font-medium">Drawing Elements:</span> {visualContent.drawing_count}
              </div>
            )}
            {visualContent.confidence && (
              <div>
                <span className="font-medium">Confidence:</span> {visualContent.confidence}
              </div>
            )}
            {visualContent.has_lines && (
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                Contains lines
              </div>
            )}
            {visualContent.has_curves && (
              <div className="flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                Contains curves
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const getContentTypeLabel = () => {
    switch (source.content_type) {
      case 'table': return '📋 Table';
      case 'image': return '🖼️ Image';
      case 'chart_diagram': return '📊 Chart/Diagram';
      default: return '📄 Content';
    }
  };

  const getContentTypeColor = () => {
    switch (source.content_type) {
      case 'table': return 'from-green-50 to-emerald-50 border-green-200';
      case 'image': return 'from-blue-50 to-cyan-50 border-blue-200';
      case 'chart_diagram': return 'from-purple-50 to-indigo-50 border-purple-200';
      default: return 'from-gray-50 to-slate-50 border-gray-200';
    }
  };

  return (
    <div className={`bg-gradient-to-br ${getContentTypeColor()} rounded-lg border p-4 mt-3`}>
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-semibold text-gray-800 text-sm flex items-center">
          {getContentTypeLabel()}
          {source.page && (
            <span className="ml-2 text-xs bg-white bg-opacity-70 px-2 py-1 rounded">
              Page {source.page}
            </span>
          )}
        </h4>
        
        {/* Tab selector for complex content */}
        {source.content_type === 'table' && source.visual_content?.metadata && (
          <div className="flex text-xs bg-white bg-opacity-50 rounded">
            <button
              onClick={() => setSelectedTab('content')}
              className={`px-2 py-1 rounded-l ${
                selectedTab === 'content' ? 'bg-white text-gray-800' : 'text-gray-600'
              }`}
            >
              Table
            </button>
            <button
              onClick={() => setSelectedTab('metadata')}
              className={`px-2 py-1 rounded-r ${
                selectedTab === 'metadata' ? 'bg-white text-gray-800' : 'text-gray-600'
              }`}
            >
              Info
            </button>
          </div>
        )}
      </div>

      {/* Content display */}
      {selectedTab === 'content' && (
        <div>
          {source.content_type === 'table' && renderTableContent()}
          {source.content_type === 'image' && renderImageContent()}
          {source.content_type === 'chart_diagram' && renderChartContent()}
        </div>
      )}

      {/* Metadata tab */}
      {selectedTab === 'metadata' && source.visual_content && (
        <div className="bg-white bg-opacity-50 rounded p-3 text-xs text-gray-600">
          <div className="grid grid-cols-2 gap-2">
            <div><span className="font-medium">Extraction Method:</span> {source.visual_content.extraction_method}</div>
            <div><span className="font-medium">Page:</span> {source.page}</div>
            {source.visual_content.table_index !== undefined && (
              <div><span className="font-medium">Table Index:</span> {source.visual_content.table_index}</div>
            )}
            {source.visual_content.image_index !== undefined && (
              <div><span className="font-medium">Image Index:</span> {source.visual_content.image_index}</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to convert markdown table to HTML
const markdownTableToHtml = (markdown: string): string => {
  if (!markdown || typeof markdown !== 'string') {
    return `<div class="p-4 text-gray-600">Invalid table data</div>`;
  }
  
  const lines = markdown.split('\n').filter(line => line.trim());
  if (lines.length < 2) return `<div class="p-4 text-gray-600">Invalid table format</div>`;

  let html = '<table class="min-w-full border border-gray-200 rounded-lg">';
  
  try {
    // Process header
    const headerLine = lines[0];
    const headerCells = headerLine.split('|')
      .map(cell => cell.trim())
      .filter(cell => cell !== ''); // Remove empty cells from start/end
    
    if (headerCells.length === 0) {
      // Try alternate format where cells are separated by multiple spaces
      const spaceSeparatedCells = headerLine.split(/\s{2,}/).filter(cell => cell.trim());
      if (spaceSeparatedCells.length > 0) {
        html += '<thead class="bg-gray-50"><tr>';
        spaceSeparatedCells.forEach((cell, index) => {
          const cleanCell = cell.replace(/\*\*/g, '').trim();
          html += `<th class="px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">${cleanCell || `Column ${index + 1}`}</th>`;
        });
        html += '</tr></thead>';
        
        // Process data rows (skip separator row)
        html += '<tbody>';
        for (let i = 2; i < lines.length; i++) {
          const rowCells = lines[i].split(/\s{2,}/).filter(cell => cell.trim());
          if (rowCells.length === 0) continue;
          
          html += '<tr class="hover:bg-gray-50 transition-colors">';
          // Make sure we have the right number of cells
          while (rowCells.length < spaceSeparatedCells.length) {
            rowCells.push('');
          }
          
          spaceSeparatedCells.forEach((_, index) => {
            const cellContent = rowCells[index] || '';
            html += `<td class="px-3 py-2 text-sm text-gray-700 border-b border-gray-100">${cellContent.trim() || '-'}</td>`;
          });
          html += '</tr>';
        }
        html += '</tbody></table>';
        return html;
      }
      
      return `<div class="p-4 text-gray-600">No table headers found</div>`;
    }
    
    html += '<thead class="bg-gray-50"><tr>';
    headerCells.forEach((cell, index) => {
      const cleanCell = cell.replace(/\*\*/g, '').trim(); // Remove markdown bold
      html += `<th class="px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">${cleanCell || `Column ${index + 1}`}</th>`;
    });
    html += '</tr></thead>';

    // Check if we have a separator row at index 1 (standard markdown table)
    const hasSeparator = lines.length > 1 && lines[1].includes('|') && lines[1].includes('-');
    const startRowIndex = hasSeparator ? 2 : 1;

    // Process data rows
    html += '<tbody>';
    for (let i = startRowIndex; i < lines.length; i++) {
      const rowLine = lines[i];
      if (!rowLine.includes('|')) continue; // Skip non-table lines
      
      const cells = rowLine.split('|')
        .map(cell => cell.trim())
        .filter((cell, index, array) => {
          // Keep all cells except first and last if they're empty (markdown format)
          if (index === 0 || index === array.length - 1) {
            return cell !== '';
          }
          return true;
        });
      
      // Ensure we have the right number of cells
      while (cells.length < headerCells.length) {
        cells.push('');
      }
      
      html += '<tr class="hover:bg-gray-50 transition-colors">';
      headerCells.forEach((_, cellIndex) => {
        const cellContent = cells[cellIndex] || '';
        const cleanCell = cellContent.replace(/\*\*/g, '').trim(); // Remove markdown bold
        html += `<td class="px-3 py-2 text-sm text-gray-700 border-b border-gray-100">${cleanCell || '-'}</td>`;
      });
      html += '</tr>';
    }
    html += '</tbody></table>';
  } catch (error) {
    console.error('Error parsing markdown table:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return `<div class="p-4 text-gray-600 bg-red-50 border border-red-200 rounded">Error parsing table: ${errorMessage}</div>`;
  }

  return html;
};

export default VisualContent; 