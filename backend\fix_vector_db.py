"""
Fix indentation issues in vector_db.py and ensure proper embedding type handling.
"""
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_indentation_issues():
    """Fix indentation errors in the vector_db.py file."""
    try:
        # Read the original file
        with open("vector_db.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # Create a backup
        with open("vector_db.py.bak", "w", encoding="utf-8") as f:
            f.writelines(lines)
        
        # Analyze indentation patterns
        fixed_lines = []
        in_hybrid_search = False
        current_indent = ""
        
        for i, line in enumerate(lines):
            # Track when we enter and exit the hybrid_search method
            if "def hybrid_search" in line:
                in_hybrid_search = True
                current_indent = line[:line.find("def")]
                fixed_lines.append(line)
                continue
            
            # Look for the next method to know when we exit hybrid_search
            if in_hybrid_search and "def " in line and line.startswith(current_indent):
                in_hybrid_search = False
            
            # Fix the indentation in the hybrid_search method
            if in_hybrid_search:
                # Special fix for the problematic line
                if "if self.use_supabase:" in line.strip():
                    # Ensure proper indentation (8 spaces for class methods)
                    fixed_line = current_indent + "        " + line.strip() + "\n"
                    fixed_lines.append(fixed_line)
                else:
                    # For all other lines in the method, maintain their relative indentation
                    # but ensure they're properly aligned
                    if line.strip():
                        # Count leading spaces
                        leading_spaces = len(line) - len(line.lstrip())
                        
                        # For indented lines, ensure at least 12 spaces (8+4)
                        if leading_spaces > len(current_indent) + 4:
                            # This is an indented line within the method
                            relative_indent = leading_spaces - len(current_indent)
                            fixed_line = current_indent + " " * relative_indent + line.strip() + "\n"
                            fixed_lines.append(fixed_line)
                        else:
                            # First level indentation within method
                            fixed_line = current_indent + "        " + line.strip() + "\n"
                            fixed_lines.append(fixed_line)
                    else:
                        # Keep empty lines
                        fixed_lines.append(line)
            else:
                # For lines outside the problematic method, keep them as they are
                fixed_lines.append(line)
        
        # Add embedding conversion code to hybrid_search method
        for i, line in enumerate(fixed_lines):
            if "def hybrid_search" in line:
                # Find the function signature end (end of line with closing parenthesis and colon)
                for j in range(i+1, len(fixed_lines)):
                    if "):" in fixed_lines[j]:
                        # Insert conversion code after function signature
                        indent = "        "  # 8 spaces for method body
                        conversion_code = f"\n{indent}# Ensure query_embedding is a numpy array of floats\n"
                        conversion_code += f"{indent}if not isinstance(query_embedding, np.ndarray):\n"
                        conversion_code += f"{indent}    try:\n"
                        conversion_code += f"{indent}        if isinstance(query_embedding, str):\n"
                        conversion_code += f"{indent}            import json\n"
                        conversion_code += f"{indent}            query_embedding = json.loads(query_embedding)\n"
                        conversion_code += f"{indent}        query_embedding = np.array(query_embedding, dtype=np.float32)\n"
                        conversion_code += f"{indent}    except Exception as e:\n"
                        conversion_code += f"{indent}        logger.error(f\"Error converting query_embedding: {{str(e)}}\")\n"
                        conversion_code += f"{indent}        # Create a default embedding as fallback\n"
                        conversion_code += f"{indent}        query_embedding = np.array([0.01] * 768, dtype=np.float32)\n"
                        
                        fixed_lines[j] = fixed_lines[j] + conversion_code
                        break
                break
        
        # Write the fixed content
        with open("vector_db.py", "w", encoding="utf-8") as f:
            f.writelines(fixed_lines)
        
        print("Successfully fixed indentation in vector_db.py")
        return True
    except Exception as e:
        print(f"ERROR fixing vector_db.py: {str(e)}")
        return False

if __name__ == "__main__":
    print("\n=== FIXING VECTOR_DB.PY INDENTATION ===\n")
    
    if fix_indentation_issues():
        print("\n✅ Fixed indentation issues in vector_db.py")
        print("✅ Added embedding type conversion code")
        print("\nPlease restart your server: python -m uvicorn server:app --reload")
    else:
        print("\n❌ Failed to fix vector_db.py")
        print("\nPlease manually check for indentation issues in the hybrid_search method.")
