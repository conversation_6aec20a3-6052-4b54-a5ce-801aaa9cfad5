"""
Simple test script for website search functionality.
"""
import logging
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_search_websites():
    """Test the search_websites function."""
    logger.info("Testing search_websites function...")
    
    # Create a simple embedding
    embedding = [0.1] * 768
    
    # Search for website chunks
    result = supabase.search_websites(
        query_embedding=embedding,
        match_threshold=0.0,
        match_count=5
    )
    
    logger.info(f"Found {len(result)} website chunks")
    
    # Print the results
    for i, chunk in enumerate(result):
        logger.info(f"Result {i+1}:")
        logger.info(f"  ID: {chunk.get('id')}")
        logger.info(f"  Website ID: {chunk.get('website_id')}")
        
        text = chunk.get('text')
        if text:
            logger.info(f"  Text: {text[:100]}...")
        else:
            logger.info(f"  Text: None")
            
        logger.info(f"  Similarity: {chunk.get('similarity')}")

def test_hybrid_search_websites():
    """Test the hybrid_search_websites function."""
    logger.info("Testing hybrid_search_websites function...")
    
    # Create a simple embedding
    embedding = [0.1] * 768
    
    # Search for website chunks
    result = supabase.hybrid_search_websites(
        query_text="Rapid Response",
        query_embedding=embedding,
        match_threshold=0.0,
        match_count=5
    )
    
    logger.info(f"Found {len(result)} website chunks")
    
    # Print the results
    for i, chunk in enumerate(result):
        logger.info(f"Result {i+1}:")
        logger.info(f"  ID: {chunk.get('id')}")
        logger.info(f"  Website ID: {chunk.get('website_id')}")
        
        text = chunk.get('text')
        if text:
            logger.info(f"  Text: {text[:100]}...")
        else:
            logger.info(f"  Text: None")
            
        logger.info(f"  Similarity: {chunk.get('similarity')}")

if __name__ == "__main__":
    # Test vector search
    logger.info("=== Testing vector search ===")
    test_search_websites()
    
    # Test hybrid search
    logger.info("\n=== Testing hybrid search ===")
    test_hybrid_search_websites()
