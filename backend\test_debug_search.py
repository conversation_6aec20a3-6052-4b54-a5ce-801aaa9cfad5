#!/usr/bin/env python3
"""
Debug script to understand why irrelevant queries are still finding chunks.
"""

import requests
import json

# Configuration
API_URL = "http://localhost:8000"

def debug_search(query):
    """Debug a search to see what chunks are found."""
    print(f"\n🔍 Debugging search for: '{query}'")
    
    try:
        # Use the debug search endpoint
        response = requests.get(
            f"{API_URL}/api/debug/search",
            params={"query": query},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"📄 Document search results:")
            doc_search = data.get("direct_document_search", {})
            print(f"  - Direct document count: {doc_search.get('direct_document_count', 0)}")
            print(f"  - Content count: {doc_search.get('content_count', 0)}")
            
            print(f"🌐 Website search results:")
            web_search = data.get("direct_website_search", {})
            print(f"  - Website count: {web_search.get('website_count', 0)}")
            
            # Show sample content
            if doc_search.get('content_results'):
                print(f"📄 Sample document content:")
                for i, result in enumerate(doc_search['content_results'][:2]):
                    print(f"  {i+1}. {result.get('text', '')[:100]}...")
            
            if web_search.get('website_chunks'):
                print(f"🌐 Sample website content:")
                for i, result in enumerate(web_search['website_chunks'][:2]):
                    print(f"  {i+1}. {result.get('text', '')[:100]}...")
                    
        else:
            print(f"❌ API Error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def main():
    print("🔍 Debug Search Analysis")
    print("="*50)
    
    # Test relevant query
    debug_search("FSDS monitoring system")
    
    # Test irrelevant queries
    debug_search("How to bake a chocolate cake")
    debug_search("Best programming languages")
    
    print("\n" + "="*50)
    print("🏁 Debug completed!")

if __name__ == "__main__":
    main() 
"""
Debug script to understand why irrelevant queries are still finding chunks.
"""

import requests
import json

# Configuration
API_URL = "http://localhost:8000"

def debug_search(query):
    """Debug a search to see what chunks are found."""
    print(f"\n🔍 Debugging search for: '{query}'")
    
    try:
        # Use the debug search endpoint
        response = requests.get(
            f"{API_URL}/api/debug/search",
            params={"query": query},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"📄 Document search results:")
            doc_search = data.get("direct_document_search", {})
            print(f"  - Direct document count: {doc_search.get('direct_document_count', 0)}")
            print(f"  - Content count: {doc_search.get('content_count', 0)}")
            
            print(f"🌐 Website search results:")
            web_search = data.get("direct_website_search", {})
            print(f"  - Website count: {web_search.get('website_count', 0)}")
            
            # Show sample content
            if doc_search.get('content_results'):
                print(f"📄 Sample document content:")
                for i, result in enumerate(doc_search['content_results'][:2]):
                    print(f"  {i+1}. {result.get('text', '')[:100]}...")
            
            if web_search.get('website_chunks'):
                print(f"🌐 Sample website content:")
                for i, result in enumerate(web_search['website_chunks'][:2]):
                    print(f"  {i+1}. {result.get('text', '')[:100]}...")
                    
        else:
            print(f"❌ API Error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def main():
    print("🔍 Debug Search Analysis")
    print("="*50)
    
    # Test relevant query
    debug_search("FSDS monitoring system")
    
    # Test irrelevant queries
    debug_search("How to bake a chocolate cake")
    debug_search("Best programming languages")
    
    print("\n" + "="*50)
    print("🏁 Debug completed!")

if __name__ == "__main__":
    main() 