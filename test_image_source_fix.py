import os
import sys
import json
import logging
from typing import Dict, Any, List

# Add backend to path
sys.path.append('./backend')

# Import backend modules
from supabase_client import supabase
import requests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_chunks():
    """Test what's actually stored in the database chunks."""
    print("=== TESTING DATABASE CHUNKS ===")
    
    try:
        # Query document chunks
        query = """
        SELECT id, document_id, chunk_index, page_number, text, metadata, 
               source_type, content_type, filename, created_at
        FROM document_chunks 
        WHERE content_type IN ('image', 'table', 'chart_diagram')
        ORDER BY created_at DESC 
        LIMIT 10
        """
        
        result = supabase.execute_query(query)
        
        if result:
            print(f"Found {len(result)} visual content chunks in database:")
            for i, chunk in enumerate(result):
                print(f"\n--- Chunk {i+1} ---")
                print(f"ID: {chunk.get('id')}")
                print(f"Document ID: {chunk.get('document_id')}")
                print(f"Filename: {chunk.get('filename', 'NOT SET')}")
                print(f"Content Type: {chunk.get('content_type')}")
                print(f"Page: {chunk.get('page_number')}")
                print(f"Metadata keys: {list(chunk.get('metadata', {}).keys()) if chunk.get('metadata') else 'None'}")
                
                # Check if we have base64 data
                metadata = chunk.get('metadata', {})
                if 'base64_data' in metadata:
                    data_length = len(metadata['base64_data'])
                    print(f"Base64 data length: {data_length}")
                
                # Check if we have storage URL
                if 'storage_url' in metadata:
                    print(f"Storage URL: {metadata['storage_url']}")
                    
                # Show text preview
                text = chunk.get('text', '')
                text_preview = text[:100] + '...' if len(text) > 100 else text
                print(f"Text preview: {text_preview}")
        else:
            print("No visual content chunks found in database")
            
    except Exception as e:
        print(f"Error querying database: {e}")

def test_documents_table():
    """Test what's in the documents table."""
    print("\n=== TESTING DOCUMENTS TABLE ===")
    
    try:
        query = """
        SELECT id, filename, display_name, file_path, file_type, status, created_at
        FROM documents 
        ORDER BY created_at DESC 
        LIMIT 10
        """
        
        result = supabase.execute_query(query)
        
        if result:
            print(f"Found {len(result)} documents in database:")
            for i, doc in enumerate(result):
                print(f"\n--- Document {i+1} ---")
                print(f"ID: {doc.get('id')}")
                print(f"Filename: {doc.get('filename')}")
                print(f"Display Name: {doc.get('display_name')}")
                print(f"File Path: {doc.get('file_path')}")
                print(f"File Type: {doc.get('file_type')}")
                print(f"Status: {doc.get('status')}")
        else:
            print("No documents found in database")
            
    except Exception as e:
        print(f"Error querying documents table: {e}")

def fix_missing_filenames():
    """Fix chunks that have missing filenames by looking up document info."""
    print("\n=== FIXING MISSING FILENAMES ===")
    
    try:
        # Get chunks with missing filenames
        query = """
        SELECT dc.id, dc.document_id, dc.filename, dc.content_type, dc.metadata,
               d.filename as doc_filename, d.display_name as doc_display_name
        FROM document_chunks dc
        LEFT JOIN documents d ON dc.document_id = d.id
        WHERE dc.content_type IN ('image', 'table', 'chart_diagram')
        AND (dc.filename IS NULL OR dc.filename = '' OR dc.filename = 'Unknown document')
        """
        
        result = supabase.execute_query(query)
        
        if result:
            print(f"Found {len(result)} chunks with missing filenames")
            
            fixed_count = 0
            for chunk in result:
                chunk_id = chunk.get('id')
                doc_filename = chunk.get('doc_filename')
                doc_display_name = chunk.get('doc_display_name')
                
                # Use display_name if available, otherwise filename
                correct_filename = doc_display_name or doc_filename
                
                if correct_filename and correct_filename != 'Unknown document':
                    print(f"Fixing chunk {chunk_id}: setting filename to '{correct_filename}'")
                    
                    # Update the chunk
                    update_query = """
                    UPDATE document_chunks 
                    SET filename = %s
                    WHERE id = %s
                    """
                    
                    try:
                        supabase.client.rpc('execute_sql', {
                            'query': update_query,
                            'params': [correct_filename, chunk_id]
                        }).execute()
                        fixed_count += 1
                    except Exception as e:
                        print(f"Error updating chunk {chunk_id}: {e}")
                
            print(f"Fixed {fixed_count} chunks with missing filenames")
        else:
            print("No chunks with missing filenames found")
            
    except Exception as e:
        print(f"Error fixing filenames: {e}")

def test_query_response():
    """Test the actual query response to see what's being returned."""
    print("\n=== TESTING QUERY RESPONSE ===")
    
    try:
        # Test with the VASP logo query
        response = requests.post(
            "http://localhost:8001/api/query",
            json={
                "query": "VASP Enterprises logo",
                "model": "gemini-2.0-flash"
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print("Response structure:")
            print(f"Answer length: {len(data.get('answer', ''))}")
            print(f"Document sources: {len(data.get('document_sources', []))}")
            print(f"Visual content found: {data.get('visual_content_found', False)}")
            print(f"Visual content types: {data.get('visual_content_types', [])}")
            
            # Check document sources
            doc_sources = data.get('document_sources', [])
            for i, source in enumerate(doc_sources):
                print(f"\n--- Document Source {i+1} ---")
                print(f"Filename: {source.get('filename', 'NOT SET')}")
                print(f"Content Type: {source.get('content_type', 'NOT SET')}")
                print(f"Page: {source.get('page', 'NOT SET')}")
                print(f"Storage URL: {source.get('storage_url', 'NOT SET')}")
                print(f"Display Type: {source.get('display_type', 'NOT SET')}")
                
                visual_content = source.get('visual_content', {})
                if visual_content:
                    print(f"Visual content keys: {list(visual_content.keys())}")
                    if 'base64_data' in visual_content:
                        data_length = len(visual_content['base64_data'])
                        print(f"Base64 data length: {data_length}")
        else:
            print(f"Query failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"Error testing query: {e}")

def update_chunk_metadata():
    """Update chunk metadata to include proper filenames in visual content."""
    print("\n=== UPDATING CHUNK METADATA ===")
    
    try:
        # Get visual content chunks with their document info
        query = """
        SELECT dc.id, dc.document_id, dc.metadata, dc.content_type,
               d.filename as doc_filename, d.display_name as doc_display_name
        FROM document_chunks dc
        LEFT JOIN documents d ON dc.document_id = d.id
        WHERE dc.content_type IN ('image', 'table', 'chart_diagram')
        AND dc.metadata IS NOT NULL
        """
        
        result = supabase.execute_query(query)
        
        if result:
            print(f"Found {len(result)} visual content chunks to update")
            
            updated_count = 0
            for chunk in result:
                chunk_id = chunk.get('id')
                metadata = chunk.get('metadata', {})
                doc_filename = chunk.get('doc_filename')
                doc_display_name = chunk.get('doc_display_name')
                
                # Use display_name if available, otherwise filename
                correct_filename = doc_display_name or doc_filename
                
                if correct_filename and correct_filename != 'Unknown document':
                    # Update the filename in metadata
                    if isinstance(metadata, dict):
                        metadata['filename'] = correct_filename
                        
                        print(f"Updating metadata for chunk {chunk_id}")
                        
                        # Update the chunk metadata
                        try:
                            supabase.client.table('document_chunks').update({
                                'metadata': metadata
                            }).eq('id', chunk_id).execute()
                            
                            updated_count += 1
                        except Exception as e:
                            print(f"Error updating chunk {chunk_id} metadata: {e}")
                
            print(f"Updated metadata for {updated_count} chunks")
        else:
            print("No visual content chunks found to update")
            
    except Exception as e:
        print(f"Error updating chunk metadata: {e}")

def main():
    """Run all tests and fixes."""
    print("RailGPT Image Source Fix Utility")
    print("=" * 50)
    
    # Test current state
    test_database_chunks()
    test_documents_table()
    
    # Try to fix the issues
    fix_missing_filenames()
    update_chunk_metadata()
    
    # Test the results
    print("\n=== TESTING AFTER FIXES ===")
    test_database_chunks()
    test_query_response()
    
    print("\n=== FIX COMPLETE ===")
    print("If the issue persists, the problem might be in the frontend rendering logic.")
    print("Check the VisualContent.tsx component for proper image source handling.")

if __name__ == "__main__":
    main() 