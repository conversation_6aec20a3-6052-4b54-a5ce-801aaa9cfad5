import React, { useState, useEffect } from 'react';

// LLM Model Type
export interface LLMModel {
  id: string;
  name: string;
  logo: string;
  provider: string;
  enabled: boolean;
  tooltip?: string;
  features?: {
    embedding?: boolean;
    chunking?: boolean;
    summarization?: boolean;
    scoring?: boolean;
  };
}

// Default list of supported LLM models
export const DEFAULT_LLM_MODELS: LLMModel[] = [
  { 
    id: 'gemini-2.0-flash', 
    name: 'Gemini 2.0 Flash', 
    logo: '⚡', 
    provider: 'Google',
    enabled: true, 
    tooltip: 'Fast and accurate general-purpose AI model',
    features: {
      embedding: true,
      chunking: true,
      summarization: true,
      scoring: true
    }
  },
  { 
    id: 'gemini', 
    name: 'Google Gemini', 
    logo: '🧠', 
    provider: 'Google',
    enabled: true, 
    tooltip: 'Balanced performance across most tasks',
    features: {
      embedding: true,
      chunking: true,
      summarization: true,
      scoring: true
    }
  },
  { 
    id: 'chatgpt', 
    name: 'OpenAI ChatGPT', 
    logo: '🤖', 
    provider: 'OpenAI', 
    enabled: true,
    tooltip: 'Strong writing and creative tasks',
    features: {
      embedding: true,
      chunking: true,
      summarization: true,
      scoring: true
    }
  },
  { 
    id: 'groq', 
    name: '<PERSON>ro<PERSON>', 
    logo: '⚡', 
    provider: 'Groq', 
    enabled: true,
    tooltip: 'Ultra-fast response times',
    features: {
      embedding: false,
      chunking: false,
      summarization: true,
      scoring: false
    }
  },
  { 
    id: 'groq-llama3-70b', 
    name: 'Groq LLaMA3 70B', 
    logo: '⚡', 
    provider: 'Groq', 
    enabled: true,
    tooltip: 'Ultra-fast LLaMA3 70B model',
    features: {
      embedding: false,
      chunking: false,
      summarization: true,
      scoring: false
    }
  },
  { 
    id: 'groq-llama3-8b', 
    name: 'Groq LLaMA3 8B', 
    logo: '⚡', 
    provider: 'Groq', 
    enabled: true,
    tooltip: 'Ultra-fast LLaMA3 8B model',
    features: {
      embedding: false,
      chunking: false,
      summarization: true,
      scoring: false
    }
  },
  { 
    id: 'deepseek', 
    name: 'DeepSeek', 
    logo: '🔍', 
    provider: 'DeepSeek', 
    enabled: true,
    tooltip: 'Good at technical and analytical tasks',
    features: {
      embedding: false,
      chunking: true,
      summarization: true,
      scoring: true
    }
  },
  { 
    id: 'deepseek-coder', 
    name: 'DeepSeek Coder', 
    logo: '🔍', 
    provider: 'DeepSeek', 
    enabled: true,
    tooltip: 'Specialized in coding and technical tasks',
    features: {
      embedding: false,
      chunking: true,
      summarization: true,
      scoring: true
    }
  },
  { 
    id: 'qwen', 
    name: 'Qwen', 
    logo: '🌐', 
    provider: 'Alibaba', 
    enabled: true,
    tooltip: 'Multilingual capabilities',
    features: {
      embedding: false,
      chunking: false,
      summarization: true,
      scoring: false
    }
  },
  { 
    id: 'huggingface', 
    name: 'Hugging Face', 
    logo: '🤗', 
    provider: 'Hugging Face', 
    enabled: true,
    tooltip: 'Open-source models with custom endpoints',
    features: {
      embedding: true,
      chunking: false,
      summarization: true,
      scoring: false
    }
  },
  { 
    id: 'ollama', 
    name: 'Ollama', 
    logo: '🦙', 
    provider: 'Local', 
    enabled: true,
    tooltip: 'Run models locally for privacy',
    features: {
      embedding: true,
      chunking: false,
      summarization: true,
      scoring: false
    }
  },
];

interface LLMSelectorProps {
  onModelChange: (modelId: string) => void;
  currentModel?: string;
  isLoading?: boolean; // Optional loading state when waiting for model response
}

const LLMSelector: React.FC<LLMSelectorProps> = ({ onModelChange, currentModel = 'gemini-2.0-flash', isLoading = false }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [models, setModels] = useState<LLMModel[]>(DEFAULT_LLM_MODELS);
  const [activeModel, setActiveModel] = useState<LLMModel | undefined>();
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // Find a model by ID in the models array
  const findModelById = React.useCallback((id: string): LLMModel | undefined => {
    return models.find(model => model.id === id) || models[0];
  }, [models]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Load model configuration from localStorage only once
  useEffect(() => {
    // Load available models from localStorage if available
    const savedModels = localStorage.getItem('railGptModels');
    const visibleCount = localStorage.getItem('railGptVisibleCount') || '5';
    
    if (savedModels) {
      try {
        const parsedModels = JSON.parse(savedModels) as LLMModel[];
        // Only show enabled models and limit to visibleCount
        setModels(
          parsedModels
            .filter(model => model.enabled)
            .slice(0, parseInt(visibleCount, 10))
        );
      } catch (error) {
        console.error('Error parsing saved models:', error);
        // Fall back to default models
        setModels(DEFAULT_LLM_MODELS.filter(model => model.enabled).slice(0, 5));
      }
    }
  }, []); // Run only once on mount

  // Update active model when currentModel prop changes
  useEffect(() => {
    setActiveModel(findModelById(currentModel));
  }, [currentModel, findModelById]);

  // Handle model selection
  const handleModelSelect = (model: LLMModel) => {
    setActiveModel(model);
    onModelChange(model.id);
    setShowDropdown(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setShowDropdown(!showDropdown)}
        className={`flex items-center justify-center h-10 w-10 rounded-full ${isLoading ? 'bg-blue-100 animate-pulse' : 'bg-gray-100 hover:bg-gray-200'} focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors`}
        title={`${activeModel?.name || 'Select LLM Model'}${activeModel?.tooltip ? ` - ${activeModel.tooltip}` : ''}`}
        aria-label={activeModel?.name || 'Select LLM Model'}
        disabled={isLoading}
      >
        <span className="text-xl" role="img" aria-label={activeModel?.name || 'AI Model'}>
          {isLoading ? '⏳' : activeModel?.logo || '🤖'}
        </span>
      </button>

      {showDropdown && (
        <div className="absolute bottom-full mb-2 right-0 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50 overflow-hidden">
          <div className="py-1" role="menu" aria-orientation="vertical">
            <div className="px-3 py-2 text-xs font-medium text-gray-500 border-b bg-gray-50">
              Select AI Model
            </div>
            {models.map((model) => (
              <button
                key={model.id}
                onClick={() => handleModelSelect(model)}
                className={`w-full flex items-center px-4 py-2 text-left text-sm ${
                  activeModel?.id === model.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                } transition-colors`}
                role="menuitem"
                title={model.tooltip}
              >
                <span className="mr-3 text-xl flex-shrink-0">{model.logo}</span>
                <div className="flex-1 overflow-hidden">
                  <div className="font-medium truncate">{model.name}</div>
                  <div className="text-xs text-gray-500 flex items-center justify-between">
                    <span>{model.provider}</span>
                    {activeModel?.id === model.id && (
                      <span className="bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5 rounded-full ml-1">Active</span>
                    )}
                  </div>
                  {model.tooltip && (
                    <div className="text-xs italic text-gray-400 mt-0.5 truncate">{model.tooltip}</div>
                  )}
                </div>
              </button>
            ))}
            <div className="px-3 py-2 text-xs text-gray-400 border-t bg-gray-50">
              More options in Settings
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LLMSelector;
