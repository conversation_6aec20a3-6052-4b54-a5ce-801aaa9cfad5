@echo off
REM Start RailGPT Visual Content System for Production

echo 🚂 Starting RailGPT Visual Content System...
echo ==========================================

REM Check if ports are available
echo Checking port availability...
netstat -an | findstr :8000 > nul
if %errorlevel% == 0 (
    echo ⚠️  Port 8000 is in use. Please stop the existing service or use a different port.
    pause
    exit /b 1
)

netstat -an | findstr :3000 > nul
if %errorlevel% == 0 (
    echo ⚠️  Port 3000 is in use. Please stop the existing service or use a different port.
    pause
    exit /b 1
)

REM Start Backend Server
echo 🔧 Starting Backend Server...
cd backend
start "RailGPT Backend" cmd /k "uvicorn server:app --reload --host 0.0.0.0 --port 8000"
cd ..

REM Wait for backend to start
echo ⏳ Waiting for backend to initialize...
timeout /t 10

REM Start Frontend Server  
echo 🌐 Starting Frontend Server...
cd frontend
start "RailGPT Frontend" cmd /k "npm start"
cd ..

REM Wait for services to start
echo ⏳ Starting services...
timeout /t 15

REM Open browser
echo 🌍 Opening browser...
start http://localhost:3000

echo ✅ RailGPT Visual Content System Started!
echo 📊 Backend: http://localhost:8000
echo 🌐 Frontend: http://localhost:3000
echo 📋 Documentation: VISUAL_CONTENT_USER_GUIDE.md
echo.
echo 🚀 Ready for visual content extraction and queries!
echo Press any key to run health check...
pause

REM Run health check
python monitor_performance.py health

echo.
echo System is ready for production use!
pause 