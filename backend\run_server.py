import uvicorn

if __name__ == "__main__":
    # Configure Uvicorn with increased limits for file uploads
    uvicorn.run(
        "server:app", 
        host="0.0.0.0", 
        port=8000,
        reload=True,
        limit_concurrency=10,
        limit_max_requests=100,
        timeout_keep_alive=120,
        # Increase the HTTP request body size limit to 200MB
        # This is needed for large file uploads
        http={
            "h11": {
                "max_incomplete_event_size": 200 * 1024 * 1024  # 200MB
            }
        }
    )
