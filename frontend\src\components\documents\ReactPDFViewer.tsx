import React, { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

// Set up PDF.js worker - use compatible version
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js`;

interface ReactPDFViewerProps {
  url: string;
  fileName: string;
  defaultScale?: number;
  initialPage?: number;
}

const ReactPDFViewer: React.FC<ReactPDFViewerProps> = ({
  url,
  fileName,
  defaultScale = 1.0,
  initialPage = 1
}) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(initialPage);
  const [scale, setScale] = useState<number>(defaultScale);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setLoading(false);
    setError('');
    console.log(`Enhanced PDF loaded: ${numPages} pages`);
  };

  const onDocumentLoadError = (error: any) => {
    console.error('Enhanced PDF load error:', error);
    const errorMessage = error.message || 'Unknown error';

    // Check for common PDF.js version issues
    if (errorMessage.includes('version') || errorMessage.includes('worker')) {
      setError('PDF viewer configuration issue. The document is available but the viewer needs updating.');
    } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      setError('Network error loading PDF. Please check your connection.');
    } else {
      setError(`Failed to load PDF: ${errorMessage}`);
    }
    setLoading(false);
  };

  const goToPrevPage = () => {
    setPageNumber(prev => Math.max(1, prev - 1));
  };

  const goToNextPage = () => {
    setPageNumber(prev => Math.min(numPages, prev + 1));
  };

  const zoomIn = () => {
    setScale(prev => Math.min(3.0, prev + 0.2));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(0.5, prev - 0.2));
  };

  const resetZoom = () => {
    setScale(1.0);
  };

  const downloadPDF = () => {
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
  };

  if (error) {
    return (
      <div className="border border-red-300 rounded-lg overflow-hidden bg-white">
        <div className="bg-red-100 p-2 text-sm font-medium text-red-700 border-b">
          ❌ Enhanced PDF Viewer Error
        </div>
        <div className="p-4 text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <div className="space-x-2">
            <button
              onClick={() => window.open(url, '_blank')}
              className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            >
              Open in New Tab
            </button>
            <button
              onClick={downloadPDF}
              className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
            >
              Download PDF
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
      {/* Enhanced Toolbar */}
      <div className="bg-gray-100 p-2 border-b flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">📄 {fileName}</span>
          {loading && <span className="text-xs text-blue-600">Loading...</span>}
        </div>

        <div className="flex items-center space-x-1">
          {/* Zoom Controls */}
          <button
            onClick={zoomOut}
            className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50"
            title="Zoom Out"
          >
            🔍-
          </button>
          <span className="text-xs px-2 py-1 bg-white border border-gray-300 rounded">
            {Math.round(scale * 100)}%
          </span>
          <button
            onClick={zoomIn}
            className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50"
            title="Zoom In"
          >
            🔍+
          </button>
          <button
            onClick={resetZoom}
            className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50"
            title="Reset Zoom"
          >
            Reset
          </button>

          {/* Page Navigation */}
          <div className="border-l border-gray-300 pl-2 ml-2">
            <button
              onClick={goToPrevPage}
              disabled={pageNumber <= 1}
              className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
              title="Previous Page"
            >
              ◀
            </button>
            <span className="text-xs px-2 py-1 bg-white border border-gray-300 rounded mx-1">
              {pageNumber} / {numPages}
            </span>
            <button
              onClick={goToNextPage}
              disabled={pageNumber >= numPages}
              className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
              title="Next Page"
            >
              ▶
            </button>
          </div>

          {/* Action Buttons */}
          <div className="border-l border-gray-300 pl-2 ml-2">
            <button
              onClick={() => window.open(url, '_blank')}
              className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50"
              title="Open in New Tab"
            >
              🔗
            </button>
            <button
              onClick={downloadPDF}
              className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 ml-1"
              title="Download PDF"
            >
              💾
            </button>
          </div>
        </div>
      </div>

      {/* PDF Content */}
      <div className="overflow-auto" style={{ height: '600px' }}>
        <div className="flex justify-center p-4">
          <Document
            file={url}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">Loading Enhanced PDF...</p>
              </div>
            }
          >
            {error ? (
              <div className="text-center py-8">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                  <p className="text-red-600 mb-4">{error}</p>
                  <div className="space-y-2">
                    <button
                      onClick={() => window.open(url, '_blank')}
                      className="block w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                      Open in New Tab
                    </button>
                    <button
                      onClick={() => {
                        // Try to reload with different worker
                        console.log('Retrying PDF load with different worker...');
                        pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.worker.min.js`;
                        window.location.reload();
                      }}
                      className="block w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                    >
                      Retry with Different Viewer
                    </button>
                    <a
                      href={url}
                      download={fileName}
                      className="block w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-center"
                    >
                      Download PDF
                    </a>
                  </div>
                </div>
              </div>
            ) : (
              <Page
                pageNumber={pageNumber}
                scale={scale}
                renderTextLayer={false}
                renderAnnotationLayer={false}
                error="Failed to load page"
              />
            )}
          </Document>
        </div>
      </div>
    </div>
  );
};

export default ReactPDFViewer;