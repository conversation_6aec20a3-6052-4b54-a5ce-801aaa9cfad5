#!/usr/bin/env python3

import requests
import json
import time

def test_vasp_logo():
    """Test VASP logo detection with timeout handling"""
    print("🔍 Testing VASP Logo Detection")
    print("=" * 40)
    
    url = "http://localhost:8000/api/query"
    
    # Test query
    query = "VASP Enterprises logo"
    
    payload = {
        "query": query,
        "model": "gemini-2.0-flash",
        "fallback_enabled": True
    }
    
    print(f"Query: {query}")
    print("Sending request...")
    
    try:
        # Send request with 30 second timeout
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Status: {response.status_code}")
            print(f"Answer: {data.get('answer', 'No answer')[:200]}...")
            print(f"Document sources: {len(data.get('document_sources', []))}")
            print(f"Website sources: {len(data.get('website_sources', []))}")
            print(f"LLM fallback used: {data.get('llm_fallback', False)}")
            
            # Check if visual content was found
            if data.get('visual_content_found'):
                print(f"🎯 Visual content found: {data.get('visual_content_types', [])}")
            
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out after 30 seconds")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    test_vasp_logo() 