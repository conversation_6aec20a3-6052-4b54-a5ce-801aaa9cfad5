#!/usr/bin/env python3
"""
Simple test to debug document source issues
"""

import requests
import json

def test_simple_query():
    """Test a simple query and debug the response"""
    print("=== Testing Simple Query ===")
    
    # Test with a very simple query
    query = "railway"
    
    headers = {"Content-Type": "application/json"}
    body = {"query": query}
    
    try:
        response = requests.post("http://localhost:8000/api/query", 
                               headers=headers, 
                               json=body)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"Query: {query}")
            print(f"Answer length: {len(data.get('answer', ''))}")
            print(f"Document answer: {data.get('document_answer', 'None')}")
            print(f"Website answer: {data.get('website_answer', 'None')}")
            print(f"LLM fallback: {data.get('llm_fallback', False)}")
            print(f"Document sources count: {len(data.get('document_sources', []))}")
            print(f"Website sources count: {len(data.get('website_sources', []))}")
            
            if data.get('document_sources'):
                print("\\nDocument sources:")
                for i, source in enumerate(data['document_sources']):
                    print(f"  {i+1}. {source.get('name', 'Unknown')} - Page {source.get('page', 'Unknown')}")
                    print(f"     Link: {source.get('link', 'No link')}")
            
            if data.get('website_sources'):
                print("\\nWebsite sources:")
                for i, source in enumerate(data['website_sources']):
                    print(f"  {i+1}. {source.get('url', 'Unknown')}")
            
            # Test with different queries
            test_queries = ["ACP", "authority", "transfer", "declaration", "sample"]
            
            print("\\n=== Testing Multiple Queries ===")
            for test_query in test_queries:
                test_response = requests.post("http://localhost:8000/api/query", 
                                            headers=headers, 
                                            json={"query": test_query})
                if test_response.status_code == 200:
                    test_data = test_response.json()
                    doc_count = len(test_data.get('document_sources', []))
                    web_count = len(test_data.get('website_sources', []))
                    fallback = test_data.get('llm_fallback', False)
                    print(f"  '{test_query}': doc={doc_count}, web={web_count}, fallback={fallback}")
                else:
                    print(f"  '{test_query}': ERROR {test_response.status_code}")
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_simple_query() 