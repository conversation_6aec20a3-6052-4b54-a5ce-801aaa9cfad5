#!/usr/bin/env python3

import requests
import time

def test_pdf_serving():
    """Test that PDF files are served correctly by the backend."""
    print("=== Testing PDF File Serving ===")
    
    test_files = [
        "ACP%20110V.pdf",
        "Authority%20Transfer%20Declaration.pdf", 
        "SampleRailwayDoc.pdf"
    ]
    
    for filename in test_files:
        try:
            url = f"http://localhost:8000/api/documents/view/{filename}"
            print(f"\nTesting: {filename}")
            print(f"URL: {url}")
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                content_length = len(response.content)
                
                print(f"✅ Status: {response.status_code}")
                print(f"✅ Content-Type: {content_type}")
                print(f"✅ File size: {content_length:,} bytes")
                
                if 'pdf' in content_type.lower():
                    print(f"✅ Correct PDF content type")
                else:
                    print(f"⚠️  Unexpected content type: {content_type}")
                    
                # Check if content looks like a PDF
                if response.content.startswith(b'%PDF'):
                    print(f"✅ Valid PDF file signature")
                else:
                    print(f"❌ Invalid PDF file signature")
                    
            else:
                print(f"❌ Failed with status {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {str(e)}")
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")

def test_frontend_access():
    """Test that the frontend document viewer page can be accessed."""
    print("\n=== Testing Frontend Document Viewer Access ===")
    
    test_urls = [
        "http://localhost:3000/viewer?file=ACP%20110V.pdf&page=1",
        "http://localhost:3000/viewer?file=Authority%20Transfer%20Declaration.pdf&page=1"
    ]
    
    for url in test_urls:
        try:
            print(f"\nTesting frontend URL: {url}")
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ Frontend accessible (status: {response.status_code})")
                
                # Check if it looks like a React app
                if 'document viewer' in response.text.lower() or 'react' in response.text.lower():
                    print(f"✅ Appears to be the correct React app")
                else:
                    print(f"⚠️  May not be the correct page content")
                    
            else:
                print(f"❌ Frontend not accessible (status: {response.status_code})")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Frontend request failed: {str(e)}")
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")

def test_backend_health():
    """Test that the backend is running and healthy."""
    print("\n=== Testing Backend Health ===")
    
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        
        if response.status_code == 200:
            print(f"✅ Backend is healthy (status: {response.status_code})")
            data = response.json()
            print(f"✅ Response: {data}")
        else:
            print(f"❌ Backend health check failed (status: {response.status_code})")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend health check failed: {str(e)}")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")

if __name__ == "__main__":
    print("🔧 Testing PDF Viewer Fixes")
    print("=" * 60)
    
    test_backend_health()
    test_pdf_serving()
    test_frontend_access()
    
    print("\n" + "=" * 60)
    print("🎯 Summary:")
    print("1. Backend should serve PDF files correctly with proper content-type")
    print("2. PDF files should have valid PDF signatures (%PDF)")  
    print("3. Frontend document viewer should be accessible")
    print("\n📝 Manual Testing Steps:")
    print("1. Open: http://localhost:3000/viewer?file=ACP%20110V.pdf&page=1")
    print("2. Try both 'React PDF' and 'Simple Viewer' buttons")
    print("3. Use 'Open in New Tab' if viewer fails")
    print("4. Check that source filtering shows only 2 sources max") 