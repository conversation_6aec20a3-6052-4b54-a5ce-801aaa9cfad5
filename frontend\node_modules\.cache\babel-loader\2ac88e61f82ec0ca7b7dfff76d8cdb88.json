{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\components\\\\websites\\\\WebsiteCategoryManagement.tsx\";\nimport React, { useState, useEffect } from 'react';\nimport { getWebsiteCategories, createWebsiteCategory } from '../../services/api';\nimport { Plus, Save, X } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WebsiteCategoryManagement = ({\n  isOpen,\n  onClose\n}) => {\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  // Form state\n  const [newCategory, setNewCategory] = useState({\n    name: '',\n    description: '',\n    sort_order: 0\n  });\n\n  // Load categories when modal opens\n  useEffect(() => {\n    if (isOpen) {\n      loadCategories();\n    }\n  }, [isOpen]);\n  const loadCategories = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const data = await getWebsiteCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('Error loading website categories:', error);\n      setError('Failed to load website categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateCategory = async () => {\n    try {\n      setError(null);\n      await createWebsiteCategory(newCategory);\n      setNewCategory({\n        name: '',\n        description: '',\n        sort_order: 0\n      });\n      setShowAddForm(false);\n      setSuccess('Website category created successfully');\n      loadCategories();\n    } catch (error) {\n      console.error('Error creating website category:', error);\n      setError(error instanceof Error ? error.message : 'Failed to create website category');\n    }\n  };\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  const handleClose = () => {\n    setShowAddForm(false);\n    setNewCategory({\n      name: '',\n      description: '',\n      sort_order: 0\n    });\n    clearMessages();\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: \"Website Category Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearMessages,\n            className: \"text-red-500 hover:text-red-700\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: success\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearMessages,\n            className: \"text-green-500 hover:text-green-700\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Website Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddForm(!showAddForm),\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), \"Add Category\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 p-4 rounded-lg space-y-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-md font-medium text-gray-900\",\n            children: \"Add Website Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newCategory.name,\n                onChange: e => setNewCategory(prev => ({\n                  ...prev,\n                  name: e.target.value\n                })),\n                className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"Category name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Sort Order\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: newCategory.sort_order || 0,\n                onChange: e => setNewCategory(prev => ({\n                  ...prev,\n                  sort_order: parseInt(e.target.value) || 0\n                })),\n                className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:col-span-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: newCategory.description || '',\n                onChange: e => setNewCategory(prev => ({\n                  ...prev,\n                  description: e.target.value\n                })),\n                className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                rows: 3,\n                placeholder: \"Category description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCreateCategory,\n              disabled: !newCategory.name.trim(),\n              className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Save, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), \"Create Category\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowAddForm(false),\n              className: \"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\",\n              children: [/*#__PURE__*/_jsxDEV(X, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this), \"Cancel\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-600\",\n            children: \"Loading categories...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: categories.length > 0 ? categories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-900\",\n                  children: category.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-xs text-gray-500\",\n                  children: [\"Order: \", category.sort_order]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 25\n                }, this), category.is_active && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 23\n              }, this), category.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: category.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: [\"Created: \", new Date(category.created_at).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 21\n            }, this)\n          }, category.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 19\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-center py-8\",\n            children: \"No website categories found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end mt-6 pt-4 border-t\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\nexport default WebsiteCategoryManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getWebsiteCategories", "createWebsiteCategory", "Plus", "Save", "X", "jsxDEV", "_jsxDEV", "WebsiteCategoryManagement", "isOpen", "onClose", "categories", "setCategories", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showAddForm", "setShowAddForm", "newCategory", "setNewCategory", "name", "description", "sort_order", "loadCategories", "data", "console", "handleCreateCategory", "Error", "message", "clearMessages", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "type", "value", "onChange", "e", "prev", "target", "placeholder", "parseInt", "rows", "disabled", "trim", "length", "map", "category", "is_active", "Date", "created_at", "toLocaleDateString", "id"], "sources": ["C:/IR App/frontend/src/components/websites/WebsiteCategoryManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  getWebsiteCategories,\n  createWebsiteCategory,\n  WebsiteCategory,\n  WebsiteCategoryCreateRequest\n} from '../../services/api';\nimport { Plus, Save, X } from 'lucide-react';\n\ninterface WebsiteCategoryManagementProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst WebsiteCategoryManagement: React.FC<WebsiteCategoryManagementProps> = ({\n  isOpen,\n  onClose,\n}) => {\n  const [categories, setCategories] = useState<WebsiteCategory[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  // Form state\n  const [newCategory, setNewCategory] = useState<WebsiteCategoryCreateRequest>({\n    name: '',\n    description: '',\n    sort_order: 0\n  });\n\n  // Load categories when modal opens\n  useEffect(() => {\n    if (isOpen) {\n      loadCategories();\n    }\n  }, [isOpen]);\n\n  const loadCategories = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const data = await getWebsiteCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('Error loading website categories:', error);\n      setError('Failed to load website categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateCategory = async () => {\n    try {\n      setError(null);\n      await createWebsiteCategory(newCategory);\n      setNewCategory({ name: '', description: '', sort_order: 0 });\n      setShowAddForm(false);\n      setSuccess('Website category created successfully');\n      loadCategories();\n    } catch (error) {\n      console.error('Error creating website category:', error);\n      setError(error instanceof Error ? error.message : 'Failed to create website category');\n    }\n  };\n\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  const handleClose = () => {\n    setShowAddForm(false);\n    setNewCategory({ name: '', description: '', sort_order: 0 });\n    clearMessages();\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              Website Category Management\n            </h2>\n            <button\n              onClick={handleClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          {/* Messages */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex justify-between items-center\">\n              <span>{error}</span>\n              <button onClick={clearMessages} className=\"text-red-500 hover:text-red-700\">\n                <X className=\"h-4 w-4\" />\n              </button>\n            </div>\n          )}\n\n          {success && (\n            <div className=\"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded flex justify-between items-center\">\n              <span>{success}</span>\n              <button onClick={clearMessages} className=\"text-green-500 hover:text-green-700\">\n                <X className=\"h-4 w-4\" />\n              </button>\n            </div>\n          )}\n\n          {/* Add Category Button */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Website Categories</h3>\n            <button\n              onClick={() => setShowAddForm(!showAddForm)}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Category\n            </button>\n          </div>\n\n          {/* Add Form */}\n          {showAddForm && (\n            <div className=\"bg-gray-50 p-4 rounded-lg space-y-4 mb-6\">\n              <h4 className=\"text-md font-medium text-gray-900\">Add Website Category</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name *</label>\n                  <input\n                    type=\"text\"\n                    value={newCategory.name}\n                    onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"Category name\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Sort Order</label>\n                  <input\n                    type=\"number\"\n                    value={newCategory.sort_order || 0}\n                    onChange={(e) => setNewCategory(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                <div className=\"md:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Description</label>\n                  <textarea\n                    value={newCategory.description || ''}\n                    onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    rows={3}\n                    placeholder=\"Category description\"\n                  />\n                </div>\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={handleCreateCategory}\n                  disabled={!newCategory.name.trim()}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center\"\n                >\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  Create Category\n                </button>\n                <button\n                  onClick={() => setShowAddForm(false)}\n                  className=\"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\"\n                >\n                  <X className=\"h-4 w-4 mr-2\" />\n                  Cancel\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Categories List */}\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n              <p className=\"mt-2 text-gray-600\">Loading categories...</p>\n            </div>\n          ) : (\n            <div className=\"space-y-2\">\n              {categories.length > 0 ? (\n                categories.map((category) => (\n                  <div key={category.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center\">\n                        <span className=\"font-medium text-gray-900\">{category.name}</span>\n                        <span className=\"ml-2 text-xs text-gray-500\">Order: {category.sort_order}</span>\n                        {category.is_active && (\n                          <span className=\"ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\n                            Active\n                          </span>\n                        )}\n                      </div>\n                      {category.description && (\n                        <p className=\"text-sm text-gray-600 mt-1\">{category.description}</p>\n                      )}\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        Created: {new Date(category.created_at).toLocaleDateString()}\n                      </p>\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <p className=\"text-gray-500 text-center py-8\">No website categories found</p>\n              )}\n            </div>\n          )}\n\n          <div className=\"flex justify-end mt-6 pt-4 border-t\">\n            <button\n              onClick={handleClose}\n              className=\"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\"\n            >\n              Close\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WebsiteCategoryManagement;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,oBAAoB,EACpBC,qBAAqB,QAGhB,oBAAoB;AAC3B,SAASC,IAAI,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO7C,MAAMC,yBAAmE,GAAGA,CAAC;EAC3EC,MAAM;EACNC;AACF,CAAC,KAAK;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAoB,EAAE,CAAC;EACnE,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAA+B;IAC3EwB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACAzB,SAAS,CAAC,MAAM;IACd,IAAIS,MAAM,EAAE;MACViB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACjB,MAAM,CAAC,CAAC;EAEZ,MAAMiB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCZ,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAMW,IAAI,GAAG,MAAM1B,oBAAoB,CAAC,CAAC;MACzCW,aAAa,CAACe,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDC,QAAQ,CAAC,mCAAmC,CAAC;IAC/C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFb,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMd,qBAAqB,CAACmB,WAAW,CAAC;MACxCC,cAAc,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,UAAU,EAAE;MAAE,CAAC,CAAC;MAC5DL,cAAc,CAAC,KAAK,CAAC;MACrBF,UAAU,CAAC,uCAAuC,CAAC;MACnDQ,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDC,QAAQ,CAACD,KAAK,YAAYe,KAAK,GAAGf,KAAK,CAACgB,OAAO,GAAG,mCAAmC,CAAC;IACxF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BhB,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAMe,WAAW,GAAGA,CAAA,KAAM;IACxBb,cAAc,CAAC,KAAK,CAAC;IACrBE,cAAc,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAE,CAAC,CAAC;IAC5DO,aAAa,CAAC,CAAC;IACftB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAK2B,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzF5B,OAAA;MAAK2B,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAC/F5B,OAAA;QAAK2B,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClB5B,OAAA;UAAK2B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5B,OAAA;YAAI2B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhC,OAAA;YACEiC,OAAO,EAAEP,WAAY;YACrBC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7C5B,OAAA;cAAK2B,SAAS,EAAC,SAAS;cAACO,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAR,QAAA,eAC5E5B,OAAA;gBAAMqC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLxB,KAAK,iBACJR,OAAA;UAAK2B,SAAS,EAAC,kGAAkG;UAAAC,QAAA,gBAC/G5B,OAAA;YAAA4B,QAAA,EAAOpB;UAAK;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpBhC,OAAA;YAAQiC,OAAO,EAAER,aAAc;YAACE,SAAS,EAAC,iCAAiC;YAAAC,QAAA,eACzE5B,OAAA,CAACF,CAAC;cAAC6B,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEAtB,OAAO,iBACNV,OAAA;UAAK2B,SAAS,EAAC,wGAAwG;UAAAC,QAAA,gBACrH5B,OAAA;YAAA4B,QAAA,EAAOlB;UAAO;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtBhC,OAAA;YAAQiC,OAAO,EAAER,aAAc;YAACE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAC7E5B,OAAA,CAACF,CAAC;cAAC6B,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAGDhC,OAAA;UAAK2B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5B,OAAA;YAAI2B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEhC,OAAA;YACEiC,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5Ce,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAE3F5B,OAAA,CAACJ,IAAI;cAAC+B,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLpB,WAAW,iBACVZ,OAAA;UAAK2B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBACvD5B,OAAA;YAAI2B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3EhC,OAAA;YAAK2B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5B,OAAA;cAAA4B,QAAA,gBACE5B,OAAA;gBAAO2B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9EhC,OAAA;gBACEyC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE5B,WAAW,CAACE,IAAK;gBACxB2B,QAAQ,EAAGC,CAAC,IAAK7B,cAAc,CAAC8B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE7B,IAAI,EAAE4B,CAAC,CAACE,MAAM,CAACJ;gBAAM,CAAC,CAAC,CAAE;gBAC7Ef,SAAS,EAAC,kGAAkG;gBAC5GoB,WAAW,EAAC;cAAe;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhC,OAAA;cAAA4B,QAAA,gBACE5B,OAAA;gBAAO2B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFhC,OAAA;gBACEyC,IAAI,EAAC,QAAQ;gBACbC,KAAK,EAAE5B,WAAW,CAACI,UAAU,IAAI,CAAE;gBACnCyB,QAAQ,EAAGC,CAAC,IAAK7B,cAAc,CAAC8B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE3B,UAAU,EAAE8B,QAAQ,CAACJ,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,IAAI;gBAAE,CAAC,CAAC,CAAE;gBAClGf,SAAS,EAAC;cAAkG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5B,OAAA;gBAAO2B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFhC,OAAA;gBACE0C,KAAK,EAAE5B,WAAW,CAACG,WAAW,IAAI,EAAG;gBACrC0B,QAAQ,EAAGC,CAAC,IAAK7B,cAAc,CAAC8B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE5B,WAAW,EAAE2B,CAAC,CAACE,MAAM,CAACJ;gBAAM,CAAC,CAAC,CAAE;gBACpFf,SAAS,EAAC,kGAAkG;gBAC5GsB,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC;cAAsB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhC,OAAA;YAAK2B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B5B,OAAA;cACEiC,OAAO,EAAEX,oBAAqB;cAC9B4B,QAAQ,EAAE,CAACpC,WAAW,CAACE,IAAI,CAACmC,IAAI,CAAC,CAAE;cACnCxB,SAAS,EAAC,qGAAqG;cAAAC,QAAA,gBAE/G5B,OAAA,CAACH,IAAI;gBAAC8B,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThC,OAAA;cACEiC,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAAC,KAAK,CAAE;cACrCc,SAAS,EAAC,kEAAkE;cAAAC,QAAA,gBAE5E5B,OAAA,CAACF,CAAC;gBAAC6B,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA1B,OAAO,gBACNN,OAAA;UAAK2B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B5B,OAAA;YAAK2B,SAAS,EAAC;UAA2E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjGhC,OAAA;YAAG2B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,gBAENhC,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBxB,UAAU,CAACgD,MAAM,GAAG,CAAC,GACpBhD,UAAU,CAACiD,GAAG,CAAEC,QAAQ,iBACtBtD,OAAA;YAAuB2B,SAAS,EAAC,0FAA0F;YAAAC,QAAA,eACzH5B,OAAA;cAAK2B,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5B,OAAA;gBAAK2B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC5B,OAAA;kBAAM2B,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAE0B,QAAQ,CAACtC;gBAAI;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClEhC,OAAA;kBAAM2B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,SAAO,EAAC0B,QAAQ,CAACpC,UAAU;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/EsB,QAAQ,CAACC,SAAS,iBACjBvD,OAAA;kBAAM2B,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,EAAC;gBAElF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EACLsB,QAAQ,CAACrC,WAAW,iBACnBjB,OAAA;gBAAG2B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAE0B,QAAQ,CAACrC;cAAW;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACpE,eACDhC,OAAA;gBAAG2B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,WAC/B,EAAC,IAAI4B,IAAI,CAACF,QAAQ,CAACG,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC,GAjBEsB,QAAQ,CAACK,EAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBhB,CACN,CAAC,gBAEFhC,OAAA;YAAG2B,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC7E;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAEDhC,OAAA;UAAK2B,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClD5B,OAAA;YACEiC,OAAO,EAAEP,WAAY;YACrBC,SAAS,EAAC,kEAAkE;YAAAC,QAAA,EAC7E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAED,eAAe/B,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}