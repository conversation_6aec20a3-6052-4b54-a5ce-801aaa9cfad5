// Supabase client for frontend
import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://rkllidjktazafeinezgo.supabase.co';
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Document types
export interface Document {
  id: string;
  filename: string;
  display_name?: string;
  file_path: string;
  file_type?: string;
  file_size?: number;
  main_category?: string;
  category?: string;
  sub_category?: string;
  minor_category?: string;
  uploaded_by?: string;
  created_at?: string;
  updated_at?: string;
  status?: string;
  quality_score?: number;
}

export interface DocumentChunk {
  id: string;
  document_id: string;
  chunk_index: number;
  page_number: number;
  text: string;
  metadata?: any;
  created_at?: string;
}

// Website types
export interface Website {
  id: string;
  url: string;
  domain?: string;
  title?: string;
  description?: string;
  category?: string;
  submitted_by?: string;
  created_at?: string;
  updated_at?: string;
  status?: string;
  quality_score?: number;
}

export interface WebsiteChunk {
  id: string;
  website_id: string;
  chunk_index: number;
  text: string;
  metadata?: any;
  created_at?: string;
}

// Query types
export interface Query {
  id: string;
  user_id?: string;
  query_text: string;
  answer_text?: string;
  llm_model?: string;
  sources?: any;
  created_at?: string;
  processing_time?: number;
}

// Document operations
export const getDocuments = async (): Promise<Document[]> => {
  try {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching documents:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getDocuments:', error);
    return [];
  }
};

export const getDocumentById = async (id: string): Promise<Document | null> => {
  try {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching document:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getDocumentById:', error);
    return null;
  }
};

export const getDocumentChunks = async (documentId: string): Promise<DocumentChunk[]> => {
  try {
    const { data, error } = await supabase
      .from('document_chunks')
      .select('*')
      .eq('document_id', documentId)
      .order('chunk_index', { ascending: true });

    if (error) {
      console.error('Error fetching document chunks:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getDocumentChunks:', error);
    return [];
  }
};

// Website operations
export const getWebsites = async (): Promise<Website[]> => {
  try {
    const { data, error } = await supabase
      .from('websites')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching websites:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getWebsites:', error);
    return [];
  }
};

export const getWebsiteById = async (id: string): Promise<Website | null> => {
  try {
    const { data, error } = await supabase
      .from('websites')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching website:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getWebsiteById:', error);
    return null;
  }
};

// Query operations
export const saveQuery = async (query: Omit<Query, 'id' | 'created_at'>): Promise<Query | null> => {
  try {
    const { data, error } = await supabase
      .from('queries')
      .insert([query])
      .select()
      .single();

    if (error) {
      console.error('Error saving query:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in saveQuery:', error);
    return null;
  }
};

export const getRecentQueries = async (limit: number = 10): Promise<Query[]> => {
  try {
    const { data, error } = await supabase
      .from('queries')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent queries:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getRecentQueries:', error);
    return [];
  }
};

// Storage operations
export const uploadFile = async (
  filePath: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<string | null> => {
  try {
    const { data, error } = await supabase.storage
      .from('documents')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Error uploading file:', error);
      return null;
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('documents')
      .getPublicUrl(data.path);

    return publicUrl;
  } catch (error) {
    console.error('Error in uploadFile:', error);
    return null;
  }
};

export const getFileUrl = (filePath: string): string => {
  const { data: { publicUrl } } = supabase.storage
    .from('documents')
    .getPublicUrl(filePath);

  return publicUrl;
};

// Chat Session types
export interface ChatMessage {
  id: string;
  content: string;
  document_answer?: string;
  website_answer?: string;
  llm_model?: string;
  sender: 'user' | 'ai';
  loading?: boolean;
  sources?: Array<any>;
  document_sources?: Array<any>;
  website_sources?: Array<any>;
  timestamp?: string;
  chatId?: string;
  llm_fallback?: boolean;
}

export interface ChatSession {
  id: string;
  user_id?: string;
  title: string;
  messages: ChatMessage[];
  model_used: string;
  created_at: string;
  updated_at: string;
  tags?: string[];
  has_document: boolean;
  has_website: boolean;
}

// Chat Session operations
export const createChatSession = async (title: string = 'New Chat', modelUsed: string = 'gemini-2.0-flash'): Promise<ChatSession | null> => {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .insert([{
        title,
        messages: [],
        model_used: modelUsed,
        has_document: false,
        has_website: false
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating chat session:', error);
      return null;
    }

    return {
      ...data,
      messages: data.messages || []
    };
  } catch (error) {
    console.error('Error in createChatSession:', error);
    return null;
  }
};

export const getChatSessions = async (userId?: string): Promise<ChatSession[]> => {
  try {
    // Add timeout and limit to prevent long-running queries
    let query = supabase
      .from('chat_sessions')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(50); // Limit to 50 most recent sessions

    if (userId) {
      query = query.eq('user_id', userId);
    }

    // Add timeout using AbortController
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const { data, error } = await query.abortSignal(controller.signal);

      clearTimeout(timeoutId);

      if (error) {
        // Handle specific timeout errors
        if (error.code === '57014' || error.message?.includes('timeout')) {
          console.warn('Chat sessions query timed out, returning empty array');
          return [];
        }
        console.error('Error fetching chat sessions:', error);
        return [];
      }

      return (data || []).map(session => ({
        ...session,
        messages: session.messages || []
      }));
    } catch (fetchError: any) {
      clearTimeout(timeoutId);

      // Handle abort/timeout errors gracefully
      if (fetchError.name === 'AbortError' || fetchError.code === '57014') {
        console.warn('Chat sessions fetch was aborted due to timeout');
        return [];
      }
      throw fetchError;
    }
  } catch (error: any) {
    console.error('Error in getChatSessions:', error);

    // Return empty array for timeout or connection issues
    if (error.code === '57014' || error.message?.includes('timeout') || error.name === 'AbortError') {
      console.warn('Returning empty chat sessions due to timeout/connection issue');
      return [];
    }

    return [];
  }
};

export const getChatSessionById = async (id: string): Promise<ChatSession | null> => {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching chat session:', error);
      return null;
    }

    return {
      ...data,
      messages: data.messages || []
    };
  } catch (error) {
    console.error('Error in getChatSessionById:', error);
    return null;
  }
};

export const updateChatSession = async (id: string, updates: Partial<ChatSession>): Promise<ChatSession | null> => {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating chat session:', error);
      return null;
    }

    return {
      ...data,
      messages: data.messages || []
    };
  } catch (error) {
    console.error('Error in updateChatSession:', error);
    return null;
  }
};

export const updateChatTitle = async (id: string, title: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('chat_sessions')
      .update({ title })
      .eq('id', id);

    if (error) {
      console.error('Error updating chat title:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in updateChatTitle:', error);
    return false;
  }
};

export const deleteChatSession = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('chat_sessions')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting chat session:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteChatSession:', error);
    return false;
  }
};

export const saveChatMessages = async (chatId: string, messages: ChatMessage[]): Promise<boolean> => {
  try {
    // Ensure all message fields are preserved when saving to Supabase
    const sanitizedMessages = messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      document_answer: msg.document_answer || undefined,
      website_answer: msg.website_answer || undefined,
      llm_model: msg.llm_model,
      sender: msg.sender,
      loading: msg.loading,
      sources: msg.sources || undefined,
      document_sources: msg.document_sources || undefined,
      website_sources: msg.website_sources || undefined,
      timestamp: msg.timestamp,
      chatId: msg.chatId,
      llm_fallback: msg.llm_fallback
    }));

    // Check if chat has documents or websites in the messages
    const hasDocument = sanitizedMessages.some(msg => 
      msg.document_answer || 
      (msg.document_sources && msg.document_sources.length > 0) ||
      (msg.sources && msg.sources.some((s: any) => s.source_type === 'document'))
    );
    
    const hasWebsite = sanitizedMessages.some(msg => 
      msg.website_answer || 
      (msg.website_sources && msg.website_sources.length > 0) ||
      (msg.sources && msg.sources.some((s: any) => s.source_type === 'website'))
    );

    console.log('Saving chat messages:', {
      messageCount: sanitizedMessages.length,
      hasDocument,
      hasWebsite,
      messagesWithDocAnswer: sanitizedMessages.filter(m => m.document_answer).length,
      messagesWithWebAnswer: sanitizedMessages.filter(m => m.website_answer).length
    });

    const { error } = await supabase
      .from('chat_sessions')
      .update({ 
        messages: sanitizedMessages,
        has_document: hasDocument,
        has_website: hasWebsite
      })
      .eq('id', chatId);

    if (error) {
      console.error('Error saving chat messages:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in saveChatMessages:', error);
    return false;
  }
};

export const clearAllChatSessions = async (userId?: string): Promise<boolean> => {
  try {
    let query = supabase.from('chat_sessions').delete();
    
    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { error } = await query;

    if (error) {
      console.error('Error clearing chat sessions:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in clearAllChatSessions:', error);
    return false;
  }
};

export default supabase;
