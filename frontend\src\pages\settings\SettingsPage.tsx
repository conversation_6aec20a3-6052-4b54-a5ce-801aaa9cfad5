import React, { useState } from 'react';
import FeedbackEmailConfig from '../../components/ui/FeedbackEmailConfig';

// LLM Model interfaces
interface LLMModel {
  id: string;
  name: string;
  logo: string;
  provider: string;
  enabled: boolean;
  usedFor: {
    answering: boolean;
    embeddings: boolean;
    chunkAnalysis: boolean;
  };
  isDefault: boolean;
}

// Initial supported LLMs
const initialModels: LLMModel[] = [
  {
    id: 'gemini',
    name: 'Google Gemini',
    logo: '🧠',
    provider: 'Google',
    enabled: true,
    usedFor: {
      answering: true,
      embeddings: true,
      chunkAnalysis: true,
    },
    isDefault: true,
  },
  {
    id: 'chatgpt',
    name: 'OpenAI ChatGPT',
    logo: '🤖',
    provider: 'OpenAI',
    enabled: true,
    usedFor: {
      answering: true,
      embeddings: true,
      chunkAnalysis: false,
    },
    isDefault: false,
  },
  {
    id: 'groq',
    name: '<PERSON><PERSON><PERSON> (Mixtral, LLaMA3)',
    logo: '⚡',
    provider: 'Groq',
    enabled: true,
    usedFor: {
      answering: true,
      embeddings: false,
      chunkAnalysis: false,
    },
    isDefault: false,
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    logo: '🔍',
    provider: 'DeepSeek',
    enabled: true,
    usedFor: {
      answering: true,
      embeddings: false,
      chunkAnalysis: false,
    },
    isDefault: false,
  },
  {
    id: 'qwen',
    name: 'Qwen',
    logo: '🌐',
    provider: 'Alibaba',
    enabled: false,
    usedFor: {
      answering: true,
      embeddings: false,
      chunkAnalysis: false,
    },
    isDefault: false,
  },
  {
    id: 'huggingface',
    name: 'HuggingFace Inference API',
    logo: '🤗',
    provider: 'HuggingFace',
    enabled: false,
    usedFor: {
      answering: true,
      embeddings: false,
      chunkAnalysis: false,
    },
    isDefault: false,
  },
  {
    id: 'ollama',
    name: 'Ollama (Local LLM runner)',
    logo: '🐑',
    provider: 'Local',
    enabled: false,
    usedFor: {
      answering: true,
      embeddings: false,
      chunkAnalysis: false,
    },
    isDefault: false,
  },
];

// Model item component with simple reordering buttons
interface ModelItemProps {
  model: LLMModel;
  index: number;
  totalModels: number;
  moveModelUp: (index: number) => void;
  moveModelDown: (index: number) => void;
  toggleModel: (id: string, enabled: boolean) => void;
  setDefaultModel: (id: string) => void;
  updateUsedFor: (id: string, field: 'answering' | 'embeddings' | 'chunkAnalysis', value: boolean) => void;
}

const ModelItem: React.FC<ModelItemProps> = ({
  model,
  index,
  totalModels,
  moveModelUp,
  moveModelDown,
  toggleModel,
  setDefaultModel,
  updateUsedFor
}) => {
  return (
    <div
      className={`p-4 border border-gray-200 rounded-lg mb-3 transition-colors duration-300 ${
        model.enabled
          ? 'bg-white'
          : 'opacity-70 bg-gray-50'
      }`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{model.logo}</span>
          <h3 className="font-medium text-gray-900">{model.name}</h3>
          <span className="text-xs bg-blue-100 text-blue-800 rounded-full px-2 py-1">{model.provider}</span>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => moveModelUp(index)}
            disabled={index === 0}
            className={`p-1 rounded transition-colors duration-200 ${
              index === 0
                ? 'text-gray-300'
                : 'text-blue-600 hover:bg-blue-50'
            }`}
            aria-label="Move up"
          >
            ⬆️
          </button>
          <button
            onClick={() => moveModelDown(index)}
            disabled={index === totalModels - 1}
            className={`p-1 rounded transition-colors duration-200 ${
              index === totalModels - 1
                ? 'text-gray-300'
                : 'text-blue-600 hover:bg-blue-50'
            }`}
            aria-label="Move down"
          >
            ⬇️
          </button>
        </div>
        <div className="flex items-center space-x-3">
          <label className="inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={model.enabled}
              onChange={() => toggleModel(model.id, !model.enabled)}
            />
            <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            <span className="ms-2 text-sm font-medium text-gray-900">Enabled</span>
          </label>

          <label className="inline-flex items-center cursor-pointer">
            <input
              type="radio"
              name="default-model"
              className="form-radio h-4 w-4 text-blue-600"
              checked={model.isDefault}
              onChange={() => setDefaultModel(model.id)}
            />
            <span className="ms-2 text-sm font-medium text-gray-900">Default</span>
          </label>
        </div>
      </div>

      {/* Model usage configuration */}
      <div className="grid grid-cols-3 gap-4 mt-2 pt-2 border-t border-gray-100">
        <label className="flex items-center space-x-2">
            <input
              type="checkbox"
            className="form-checkbox h-4 w-4 text-blue-600"
              checked={model.usedFor.answering}
            onChange={(e) => updateUsedFor(model.id, 'answering', e.target.checked)}
            />
          <span className="ms-2 text-sm font-medium text-gray-700">Answering</span>
          </label>
        
        <label className="flex items-center space-x-2">
            <input
              type="checkbox"
            className="form-checkbox h-4 w-4 text-blue-600"
              checked={model.usedFor.embeddings}
            onChange={(e) => updateUsedFor(model.id, 'embeddings', e.target.checked)}
            />
          <span className="ms-2 text-sm font-medium text-gray-700">Embeddings</span>
          </label>
        
        <label className="flex items-center space-x-2">
            <input
              type="checkbox"
            className="form-checkbox h-4 w-4 text-blue-600"
              checked={model.usedFor.chunkAnalysis}
            onChange={(e) => updateUsedFor(model.id, 'chunkAnalysis', e.target.checked)}
            />
          <span className="ms-2 text-sm font-medium text-gray-700">Chunk Analysis</span>
          </label>
        </div>
    </div>
  );
};

// Document Processing Settings Component
const DocumentSettings: React.FC = () => {
  const [settings, setSettings] = useState({
    ocrFallback: true,
    multiStageFallback: false,
    chunkSize: 1000,
    chunkOverlap: 200,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, type, checked, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : parseInt(value) || 0
    }));
  };

  return (
    <div className="p-4 border border-gray-200 rounded-lg bg-white mb-6 transition-colors duration-300">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Document Processing Settings</h3>

      <div className="space-y-4">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            name="ocrFallback"
            checked={settings.ocrFallback}
            onChange={handleChange}
            className="form-checkbox h-4 w-4 text-blue-600"
          />
          <span className="text-sm text-gray-700">OCR fallback when text extraction fails</span>
        </label>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            name="multiStageFallback"
            checked={settings.multiStageFallback}
            onChange={handleChange}
            className="form-checkbox h-4 w-4 text-blue-600"
          />
          <span className="text-sm text-gray-700">Multi-stage fallback (PyMuPDF → PDF.js → OCR)</span>
        </label>

        <div>
          <label className="block text-sm text-gray-700 mb-1">Chunk size (characters)</label>
          <input
            type="number"
            name="chunkSize"
            value={settings.chunkSize}
            onChange={handleChange}
            min="100"
            max="5000"
            className="form-input mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          />
        </div>

        <div>
          <label className="block text-sm text-gray-700 mb-1">Chunk overlap (characters)</label>
          <input
            type="number"
            name="chunkOverlap"
            value={settings.chunkOverlap}
            onChange={handleChange}
            min="0"
            max="1000"
            className="form-input mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          />
        </div>
      </div>
    </div>
  );
};

// Display Settings Component
const DisplaySettings: React.FC = () => {
  const [displaySettings, setDisplaySettings] = useState({
    compactView: false,
    highlightSources: true,
    maxSourcesShown: 5,
    accentColor: '#3b82f6',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, type, value } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setDisplaySettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  return (
    <div className="p-4 border rounded-lg bg-white mb-6 transition-colors duration-300">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Display Preferences</h3>

      <div className="space-y-6">
        {/* Other Display Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              name="compactView"
              checked={displaySettings.compactView}
              onChange={handleChange}
              className="form-checkbox h-4 w-4 text-blue-600"
            />
            <span className="text-sm text-gray-700">Compact view</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              name="highlightSources"
              checked={displaySettings.highlightSources}
              onChange={handleChange}
              className="form-checkbox h-4 w-4 text-blue-600"
            />
            <span className="text-sm text-gray-700">Highlight source references</span>
          </label>

          <div>
            <label className="block text-sm text-gray-700 mb-1">Max sources to display</label>
            <select
              name="maxSourcesShown"
              value={displaySettings.maxSourcesShown}
              onChange={handleChange}
              className="form-select mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            >
              <option value={1}>1 source</option>
              <option value={3}>3 sources</option>
              <option value={5}>5 sources</option>
              <option value={10}>10 sources</option>
              <option value={0}>All sources</option>
            </select>
          </div>

          <div>
            <label className="block text-sm text-gray-700 mb-1">Accent color</label>
            <input
              type="color"
              name="accentColor"
              value={displaySettings.accentColor}
              onChange={handleChange}
              className="form-input h-8 w-full rounded cursor-pointer border-gray-300"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Settings Page component
const SettingsPage: React.FC = () => {
  const [models, setModels] = useState<LLMModel[]>(initialModels);
  const [visibleCount, setVisibleCount] = useState(5);

  // Save settings to localStorage
  const saveSettings = () => {
    localStorage.setItem('railGptModels', JSON.stringify(models));
    localStorage.setItem('railGptVisibleCount', visibleCount.toString());
    alert('Settings saved successfully!');
  };

  // Load settings from localStorage on component mount
  React.useEffect(() => {
    const savedModels = localStorage.getItem('railGptModels');
    const savedVisibleCount = localStorage.getItem('railGptVisibleCount');

    if (savedModels) {
      setModels(JSON.parse(savedModels));
    }

    if (savedVisibleCount) {
      setVisibleCount(parseInt(savedVisibleCount, 10));
    }
  }, []);

  // Move model up in the list
  const moveModelUp = (index: number) => {
    if (index <= 0) return;
    const updatedModels = [...models];
    const temp = updatedModels[index];
    updatedModels[index] = updatedModels[index - 1];
    updatedModels[index - 1] = temp;
    setModels(updatedModels);
  };

  // Move model down in the list
  const moveModelDown = (index: number) => {
    if (index >= models.length - 1) return;
    const updatedModels = [...models];
    const temp = updatedModels[index];
    updatedModels[index] = updatedModels[index + 1];
    updatedModels[index + 1] = temp;
    setModels(updatedModels);
  };

  // Toggle model enabled status
  const toggleModel = (id: string, enabled: boolean) => {
    setModels(models.map(model =>
      model.id === id ? { ...model, enabled } : model
    ));
  };

  // Set default model
  const setDefaultModel = (id: string) => {
    setModels(models.map(model => ({
      ...model,
      isDefault: model.id === id
    })));
  };

  // Update model used for settings
  const updateUsedFor = (id: string, field: 'answering' | 'embeddings' | 'chunkAnalysis', value: boolean) => {
    setModels(models.map(model =>
      model.id === id
        ? {
            ...model,
            usedFor: {
              ...model.usedFor,
              [field]: value
            }
          }
        : model
    ));
  };

  // Handle visible count change
  const handleVisibleCountChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setVisibleCount(parseInt(e.target.value, 10));
  };

  return (
    <div className="h-full flex flex-col bg-gray-50 transition-colors duration-300">
      {/* Fixed header section */}
      <div className="bg-white p-4 shadow-sm z-10 transition-colors duration-300">
        <div className="container mx-auto">
          <h1 className="text-2xl font-bold text-gray-900">RailGPT Settings</h1>
        </div>
      </div>

      {/* Scrollable content section */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow p-6 mb-6 transition-colors duration-300">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">LLM Models Configuration</h2>
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-700">Models in dropdown:</label>
                    <select
                      value={visibleCount}
                      onChange={handleVisibleCountChange}
                      className="form-select rounded border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    >
                      <option value={1}>1</option>
                      <option value={2}>2</option>
                      <option value={3}>3</option>
                      <option value={4}>4</option>
                      <option value={5}>5</option>
                    </select>
                  </div>
                </div>

                <p className="text-gray-600 mb-4">
              Configure which LLM models are available for use and how they're used in the application.
              Drag to reorder, and select which models should appear in the dropdown menu.
            </p>

                <div className="space-y-3">
              {models.map((model, index) => (
                <ModelItem
                  key={model.id}
                  model={model}
                  index={index}
                  totalModels={models.length}
                  moveModelUp={moveModelUp}
                  moveModelDown={moveModelDown}
                  toggleModel={toggleModel}
                  setDefaultModel={setDefaultModel}
                  updateUsedFor={updateUsedFor}
                />
              ))}
            </div>

                <p className="text-sm text-gray-500 mt-4">
                  First {visibleCount} enabled models will appear in the chat dropdown.
                </p>

                <button
                  onClick={saveSettings}
                  className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                >
                  Save Model Settings
                </button>
          </div>

          <DocumentSettings />
              <DisplaySettings />
        </div>

        <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 transition-colors duration-300">
                <h3 className="text-lg font-medium text-gray-900 mb-4">About</h3>
                <p className="text-gray-600 mb-4">
                  RailGPT is an intelligent document query system designed for the Indian Railway community.
            </p>
                <p className="text-gray-600 mb-4">
                  Configure your preferred LLM models, processing settings, and display preferences to customize your experience.
                </p>
                <FeedbackEmailConfig />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
