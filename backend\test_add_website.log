2025-05-22 02:12:14,597 - INFO - supabase_client - Initialized Supabase client for https://rkllidjktazafeinezgo.supabase.co
2025-05-22 02:12:18,576 - INFO - __main__ - Testing with URL: https://indianrailways.gov.in
2025-05-22 02:12:18,579 - INFO - __main__ - Adding website: https://indianrailways.gov.in
2025-05-22 02:12:18,580 - WARNING - supabase_client - Invalid UUID for submitted_by: test_script. Using default user ID.
2025-05-22 02:12:18,581 - INFO - supabase_client - Storing website: https://indianrailways.gov.in
2025-05-22 02:12:19,008 - INFO - supabase_client - Successfully updated website with ID: b6cf6bd1-b15f-4eb2-a086-633a116250ec
2025-05-22 02:12:19,022 - INFO - __main__ - Successfully stored website with ID: b6cf6bd1-b15f-4eb2-a086-633a116250ec
2025-05-22 02:12:19,023 - INFO - website_scraper - Extracting website content from: https://indianrailways.gov.in
2025-05-22 02:12:19,025 - INFO - website_scraper - Extracting content from https://indianrailways.gov.in using Trafilatura
2025-05-22 02:12:20,454 - INFO - website_scraper - Extracted 5 chunks from https://indianrailways.gov.in using trafilatura
2025-05-22 02:12:20,456 - INFO - __main__ - Extracted 5 chunks from website
2025-05-22 02:12:20,995 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:12:21,866 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 400 Bad Request"
2025-05-22 02:12:21,869 - ERROR - supabase_client - Error storing website chunk: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:21,870 - ERROR - __main__ - Error storing chunk 0: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:22,518 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:12:22,644 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 400 Bad Request"
2025-05-22 02:12:22,650 - ERROR - supabase_client - Error storing website chunk: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:22,652 - ERROR - __main__ - Error storing chunk 1: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:23,199 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:12:23,340 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 400 Bad Request"
2025-05-22 02:12:23,352 - ERROR - supabase_client - Error storing website chunk: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:23,354 - ERROR - __main__ - Error storing chunk 2: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:23,822 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:12:23,950 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 400 Bad Request"
2025-05-22 02:12:23,953 - ERROR - supabase_client - Error storing website chunk: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:23,955 - ERROR - __main__ - Error storing chunk 3: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:24,354 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:12:24,487 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 400 Bad Request"
2025-05-22 02:12:24,490 - ERROR - supabase_client - Error storing website chunk: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:24,492 - ERROR - __main__ - Error storing chunk 4: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'source_type' column of 'website_chunks' in the schema cache"}
2025-05-22 02:12:24,496 - INFO - __main__ - Successfully stored 0 out of 5 chunks
2025-05-22 02:12:24,497 - ERROR - __main__ - Failed to add website
2025-05-22 02:14:49,308 - INFO - supabase_client - Initialized Supabase client for https://rkllidjktazafeinezgo.supabase.co
2025-05-22 02:14:53,828 - INFO - __main__ - Testing with URL: https://indianrailways.gov.in
2025-05-22 02:14:53,828 - INFO - __main__ - Adding website: https://indianrailways.gov.in
2025-05-22 02:14:53,830 - WARNING - supabase_client - Invalid UUID for submitted_by: test_script. Using default user ID.
2025-05-22 02:14:53,830 - INFO - supabase_client - Storing website: https://indianrailways.gov.in
2025-05-22 02:14:54,701 - INFO - supabase_client - Successfully updated website with ID: b6cf6bd1-b15f-4eb2-a086-633a116250ec
2025-05-22 02:14:54,712 - INFO - __main__ - Successfully stored website with ID: b6cf6bd1-b15f-4eb2-a086-633a116250ec
2025-05-22 02:14:54,714 - INFO - website_scraper - Extracting website content from: https://indianrailways.gov.in
2025-05-22 02:14:54,715 - INFO - website_scraper - Extracting content from https://indianrailways.gov.in using Trafilatura
2025-05-22 02:14:56,264 - INFO - website_scraper - Extracted 5 chunks from https://indianrailways.gov.in using trafilatura
2025-05-22 02:14:56,278 - INFO - __main__ - Extracted 5 chunks from website
2025-05-22 02:14:57,261 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:14:58,018 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 201 Created"
2025-05-22 02:14:58,085 - INFO - supabase_client - Successfully stored website chunk with ID: 21b77325-425b-4aca-87a8-c6ad14351e5a
2025-05-22 02:14:58,095 - INFO - __main__ - Successfully stored chunk 0 with ID: 21b77325-425b-4aca-87a8-c6ad14351e5a
2025-05-22 02:14:59,049 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:14:59,205 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 201 Created"
2025-05-22 02:14:59,214 - INFO - supabase_client - Successfully stored website chunk with ID: 13c739b7-d58f-4097-b8ae-20d86fdbf0b1
2025-05-22 02:14:59,214 - INFO - __main__ - Successfully stored chunk 1 with ID: 13c739b7-d58f-4097-b8ae-20d86fdbf0b1
2025-05-22 02:15:00,317 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:15:00,478 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 201 Created"
2025-05-22 02:15:00,483 - INFO - supabase_client - Successfully stored website chunk with ID: 7f01f653-9ccf-4299-9216-0ab99d1a33b3
2025-05-22 02:15:00,485 - INFO - __main__ - Successfully stored chunk 2 with ID: 7f01f653-9ccf-4299-9216-0ab99d1a33b3
2025-05-22 02:15:01,388 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:15:01,540 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 201 Created"
2025-05-22 02:15:01,545 - INFO - supabase_client - Successfully stored website chunk with ID: 4104a027-c522-4cee-ab64-fa4ad89afc07
2025-05-22 02:15:01,546 - INFO - __main__ - Successfully stored chunk 3 with ID: 4104a027-c522-4cee-ab64-fa4ad89afc07
2025-05-22 02:15:01,993 - ERROR - embeddings - Error in OpenAI API response: {'error': {'message': 'Incorrect API key provided: sk-your-***********here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-22 02:15:02,136 - INFO - httpx - HTTP Request: POST https://rkllidjktazafeinezgo.supabase.co/rest/v1/website_chunks "HTTP/1.1 201 Created"
2025-05-22 02:15:02,160 - INFO - supabase_client - Successfully stored website chunk with ID: ae66c7bd-7915-47ab-a649-b18cdf5e7d3f
2025-05-22 02:15:02,162 - INFO - __main__ - Successfully stored chunk 4 with ID: ae66c7bd-7915-47ab-a649-b18cdf5e7d3f
2025-05-22 02:15:02,168 - INFO - __main__ - Successfully stored 5 out of 5 chunks
2025-05-22 02:15:02,172 - INFO - __main__ - Website added successfully
2025-05-22 02:15:02,173 - INFO - __main__ - Checking website chunks for URL: https://indianrailways.gov.in
2025-05-22 02:15:02,174 - INFO - supabase_client - Executing SQL: 
        SELECT id FROM websites WHERE url = 'https://indianrailways.gov.in';
        ... with timeout 60s
2025-05-22 02:15:02,448 - INFO - __main__ - Found website with ID: None
2025-05-22 02:15:02,450 - INFO - supabase_client - Executing SQL: 
        SELECT COUNT(*) as count FROM website_chunks WHERE website_id = 'None';
        ... with timeout 60s
2025-05-22 02:15:02,675 - INFO - __main__ - Found 0 chunks for website
2025-05-22 02:15:02,676 - ERROR - __main__ - Website chunks not stored correctly
