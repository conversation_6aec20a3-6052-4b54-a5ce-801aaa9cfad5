# RailGPT Quick Deployment Guide

## 🚀 One-Click Deployment Options

### Option 1: Vercel + Railway (Recommended for beginners)

#### Step 1: Deploy Backend to Railway
1. Go to [Railway.app](https://railway.app)
2. Sign up/login with GitHub
3. Click "New Project" → "Deploy from GitHub repo"
4. Select your RailGPT repository
5. Choose the `backend` folder
6. Add environment variables:
   ```
   SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co
   SUPABASE_KEY=your_service_key_here
   SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA
   GEMINI_API_KEY=your_gemini_api_key_here
   ```
7. Deploy and note your Railway URL (e.g., `https://railgpt-backend-production.up.railway.app`)

#### Step 2: Deploy Frontend to Vercel
1. Go to [Vercel.com](https://vercel.com)
2. Sign up/login with GitHub
3. Click "New Project" → Import your repository
4. Set root directory to `frontend`
5. Add environment variable:
   ```
   REACT_APP_API_URL=https://your-railway-url.up.railway.app
   ```
6. Deploy

**Total time: 10-15 minutes**

### Option 2: Docker (Local/VPS)

#### Prerequisites
- Docker and Docker Compose installed
- Domain name (optional)

#### Quick Start
```bash
# Clone the repository
git clone https://github.com/yourusername/railgpt.git
cd railgpt

# Create environment file
cp deploy/.env.example .env
# Edit .env with your API keys

# Deploy with one command
./deploy/deploy.sh
```

**Access your app at: http://localhost**

### Option 3: DigitalOcean App Platform

#### One-Click Deploy Button
[![Deploy to DO](https://www.deploytodo.com/do-btn-blue.svg)](https://cloud.digitalocean.com/apps/new?repo=https://github.com/yourusername/railgpt/tree/main)

#### Manual Steps
1. Go to [DigitalOcean App Platform](https://cloud.digitalocean.com/apps)
2. Create new app from GitHub repository
3. Configure services:
   - **Backend**: Python app from `/backend` folder
   - **Frontend**: Static site from `/frontend` folder
4. Add environment variables
5. Deploy

## 🔧 Environment Variables Setup

### Required Variables
```env
# Supabase (Database)
SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co
SUPABASE_KEY=your_service_key_here
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA

# AI Models (at least one required)
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here (optional)
GROQ_API_KEY=your_groq_api_key_here (optional)
```

### How to Get API Keys

#### Gemini API Key (Required)
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with Google account
3. Click "Create API Key"
4. Copy the key

#### OpenAI API Key (Optional)
1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign up/login
3. Create new secret key
4. Copy the key

#### Groq API Key (Optional)
1. Go to [Groq Console](https://console.groq.com/keys)
2. Sign up/login
3. Create new API key
4. Copy the key

## 📱 Platform-Specific Instructions

### Vercel Deployment
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy frontend
cd frontend
vercel --prod

# Set environment variables in Vercel dashboard
```

### Netlify Deployment
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Deploy frontend
cd frontend
npm run build
netlify deploy --prod --dir=build
```

### Heroku Deployment
```bash
# Install Heroku CLI
# Create Heroku apps
heroku create railgpt-backend
heroku create railgpt-frontend

# Deploy backend
cd backend
git subtree push --prefix=backend heroku main

# Deploy frontend
cd frontend
# Add buildpack and deploy
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Frontend build issues
cd frontend
rm -rf node_modules package-lock.json
npm install
npm run build

# Backend dependency issues
cd backend
pip install --upgrade pip
pip install -r requirements.txt
```

#### 2. CORS Errors
- Ensure backend URL is correctly set in frontend environment variables
- Check that CORS is properly configured in backend

#### 3. Database Connection Issues
- Verify Supabase URL and keys are correct
- Check that Supabase project is active
- Ensure database tables are created (run the SQL scripts)

#### 4. API Key Issues
- Verify API keys are valid and have sufficient quota
- Check that environment variables are properly set
- Ensure no extra spaces or quotes in API keys

### Health Check Commands
```bash
# Check backend health
curl http://your-backend-url/health

# Check frontend
curl http://your-frontend-url/

# Check database connection
curl http://your-backend-url/api/health/db
```

## 🚀 Production Optimizations

### Performance
- Enable gzip compression
- Set up CDN for static assets
- Configure caching headers
- Use production builds

### Security
- Set up SSL certificates
- Configure security headers
- Enable rate limiting
- Use environment variables for secrets

### Monitoring
- Set up health checks
- Configure logging
- Monitor resource usage
- Set up alerts

## 💰 Cost Estimates

### Free Tier (Development)
- **Vercel**: Free for personal projects
- **Railway**: $5/month after free tier
- **Supabase**: Free tier (500MB database, 50MB file storage)
- **Total**: $0-5/month

### Production (Small Scale)
- **DigitalOcean App Platform**: $12/month
- **AWS/GCP/Azure**: $15-30/month
- **Domain + SSL**: $10-15/year
- **Total**: $15-45/month

### Enterprise (High Traffic)
- **Dedicated servers**: $50-200/month
- **CDN**: $10-50/month
- **Monitoring**: $20-100/month
- **Total**: $80-350/month

## 📞 Support

### Getting Help
1. Check the troubleshooting section above
2. Review application logs
3. Check GitHub issues
4. Contact support

### Useful Commands
```bash
# View application logs
docker-compose logs -f

# Restart services
docker-compose restart

# Update application
git pull
./deploy/deploy.sh

# Backup data
# (Supabase handles database backups automatically)
```

## 🎯 Next Steps After Deployment

1. **Test all functionality**
   - Upload documents
   - Add websites
   - Test chat functionality
   - Verify search results

2. **Configure domain**
   - Point domain to your hosting platform
   - Set up SSL certificate
   - Update environment variables

3. **Set up monitoring**
   - Configure health checks
   - Set up error tracking
   - Monitor performance

4. **Optimize for production**
   - Enable caching
   - Configure CDN
   - Set up backups

5. **Scale as needed**
   - Monitor resource usage
   - Upgrade hosting plan
   - Optimize database queries

Your RailGPT application should now be live and accessible to users!
