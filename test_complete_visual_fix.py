#!/usr/bin/env python3
"""
Complete test to verify all visual content fixes are working
"""

import requests
import json
import time

def test_specific_queries():
    """Test the specific queries mentioned in the user issue"""
    print("🎯 Testing Specific User Queries")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # Test queries from user feedback
    test_queries = [
        "Show me Project 1 image",
        "Give me table of Quotation 1", 
        "What are the commercial terms and conditions",
        "Transport charges extra if applicable"
    ]
    
    for query in test_queries:
        print(f"\n📤 Query: {query}")
        try:
            response = requests.post(
                f"{base_url}/api/query",
                json={'query': query, 'model': 'gemini-2.0-flash'},
                timeout=45
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Success!")
                print(f"   📊 Visual content found: {result.get('visual_content_found', False)}")
                print(f"   🎨 Visual types: {result.get('visual_content_types', [])}")
                print(f"   📄 Document sources: {len(result.get('document_sources', []))}")
                
                # Check for text quality
                doc_answer = result.get('document_answer', '')
                if 'TTrraannss' in doc_answer or 'chharagregse' in doc_answer:
                    print("   ⚠️  Text still has duplication issues")
                else:
                    print("   ✅ Text quality looks good")
                
                # Check visual content structure  
                sources = result.get('document_sources', [])
                for i, source in enumerate(sources):
                    if isinstance(source, dict) and source.get('visual_content'):
                        print(f"   📋 Source {i+1}: {source.get('content_type', 'unknown')} content")
                        visual_content = source.get('visual_content', {})
                        
                        if source.get('content_type') == 'table':
                            if 'table_data' in visual_content:
                                table_data = visual_content['table_data']
                                print(f"      🗂️  Table: {len(table_data)} rows")
                            if 'markdown_table' in visual_content:
                                md_length = len(visual_content['markdown_table'])
                                print(f"      📝 Markdown: {md_length} chars")
                        
                        elif source.get('content_type') == 'image':
                            if 'base64_data' in visual_content:
                                b64_length = len(visual_content['base64_data'])
                                print(f"      🖼️  Image: {b64_length} chars base64")
                            if 'width' in visual_content and 'height' in visual_content:
                                w, h = visual_content['width'], visual_content['height']
                                print(f"      📐 Dimensions: {w}x{h}")
                
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
        
        print("-" * 40)

def test_frontend_readiness():
    """Check if frontend is ready for testing"""
    print("\n🖥️ Frontend Readiness Check")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is running")
            print("🔄 Remember to refresh browser (Ctrl+F5) to see changes")
            return True
        else:
            print(f"⚠️  Frontend status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend not accessible: {e}")
        print("💡 Start with: cd frontend && npm start")
        return False

def main():
    """Run complete test suite"""
    print("🧪 Complete Visual Content Fix Verification")
    print("=" * 50)
    
    # Test specific queries
    test_specific_queries()
    
    # Check frontend
    frontend_ready = test_frontend_readiness()
    
    print("\n" + "=" * 50)
    print("🎯 VERIFICATION SUMMARY")
    print("=" * 50)
    
    print("\n✅ FIXES IMPLEMENTED:")
    print("1. 🧹 Text Cleaning: Removes character duplication like 'TTrraannss'")
    print("2. 🔍 Visual Detection: Better detection of 'Project 1', 'Quotation 1' etc.")
    print("3. 📋 Table Rendering: Improved table display with proper formatting")
    print("4. 🖼️  Image Display: Enhanced image rendering with metadata")
    print("5. 📝 Markdown Parser: Better conversion of markdown tables to HTML")
    
    print("\n📋 WHAT TO TEST IN BROWSER:")
    if frontend_ready:
        print("   1. Refresh browser (Ctrl+F5)")
        print("   2. Try: 'Show me Project 1 image'")
        print("   3. Try: 'Give me table of Quotation 1'")
        print("   4. Check if tables display properly")
        print("   5. Check if images show correctly")
        print("   6. Verify text is clean (no character duplication)")
    else:
        print("   ❌ Start frontend first: cd frontend && npm start")
    
    print("\n🔧 TROUBLESHOOTING:")
    print("   - If tables still don't show: Check browser console for errors")
    print("   - If images don't load: Check network tab for failed requests")  
    print("   - If text is garbled: Verify clean_text function is working")
    print("   - If wrong images show: Check entity detection in query")

if __name__ == "__main__":
    main() 