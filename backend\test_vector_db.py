"""
Test script for VectorDB.
"""
import logging
from vector_db import VectorDB

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_search():
    """Test the search method."""
    logger.info("Testing search method...")

    # Create a VectorDB instance
    db = VectorDB()

    # Search for "Rapid Response App"
    query = "Rapid Response App"
    logger.info(f"Searching for: {query}")

    # Get embedding for query
    from embeddings import get_embedding
    query_embedding = get_embedding(query)

    result = db.search(query_embedding=query_embedding, top_k=5)

    logger.info(f"Found {len(result)} results")

    # Print the results
    for i, item in enumerate(result):
        logger.info(f"Result {i+1}:")
        logger.info(f"  Source Type: {item.get('source_type')}")

        text = item.get('text')
        if text:
            logger.info(f"  Text: {text[:100]}...")
        else:
            logger.info(f"  Text: None")

        logger.info(f"  Similarity: {item.get('similarity')}")

def test_hybrid_search():
    """Test the hybrid_search method."""
    logger.info("Testing hybrid_search method...")

    # Create a VectorDB instance
    db = VectorDB()

    # Search for "Rapid Response App"
    query = "Rapid Response App"
    logger.info(f"Hybrid searching for: {query}")

    # Get embedding for query
    from embeddings import get_embedding
    query_embedding = get_embedding(query)

    result = db.hybrid_search(query_text=query, query_embedding=query_embedding, top_k=5)

    logger.info(f"Found {len(result)} results")

    # Print the results
    for i, item in enumerate(result):
        logger.info(f"Result {i+1}:")
        logger.info(f"  Source Type: {item.get('source_type')}")

        text = item.get('text')
        if text:
            logger.info(f"  Text: {text[:100]}...")
        else:
            logger.info(f"  Text: None")

        logger.info(f"  Similarity: {item.get('similarity')}")

if __name__ == "__main__":
    # Test search
    logger.info("=== Testing search ===")
    test_search()

    # Test hybrid search
    logger.info("\n=== Testing hybrid search ===")
    test_hybrid_search()
