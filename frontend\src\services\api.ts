// API endpoints and methods for interacting with the backend
import { supabase, saveQuery } from './supabase';

export const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export interface QueryRequest {
  query: string;
}

export interface Source {
  source_type: string;
  filename?: string;
  name?: string;  // For display name of the document
  page?: number;
  url?: string;
  link?: string;  // For document viewer links
  // Visual content fields
  content_type?: string;  // "text", "table", "image", "chart_diagram"
  visual_content?: Record<string, any>;  // Visual content metadata
  storage_url?: string;  // URL for stored visual content
  display_type?: string;  // "text", "html_table", "image", "base64_image"
}

export interface QueryResponse {
  answer: string;  // Combined answer
  document_answer?: string;  // Answer from document sources only
  website_answer?: string;  // Answer from website sources only
  sources: Array<Source>;  // All sources
  document_sources?: Array<Source>;  // Document sources only
  website_sources?: Array<Source>;  // Website sources only
  llm_model?: string;  // The LLM model used for generating the answer
  llm_fallback?: boolean;  // Whether the answer was generated using LLM fallback
  visual_content_found?: boolean;  // Whether visual content was found
  visual_content_types?: string[];  // Types of visual content found
}

export interface WebsiteAddRequest {
  url: string;
  submitted_by?: string;
  role?: string;
  follow_links?: boolean;
  extraction_depth?: number;
  extract_images?: boolean;
  extract_tables?: boolean;
  max_pages?: number;
  extractor_type?: string;
  domain_category?: string;
  [key: string]: any; // To allow for future extension options
}

export interface UploadResponse {
  success: boolean;
  message: string;
  chunks_extracted?: number;
  chunks?: Array<any>;
  data?: {
    id: string;
    path: string;
    [key: string]: any;
  };
}

export interface FeedbackData {
  query: string;  // The user's question
  answer: string; // The AI's answer
  issue_type: string; // Inaccurate, Incomplete, Offensive, Too Slow, Other
  comment?: string; // Optional user comment
  model?: string; // LLM model used
  chat_id?: string; // Unique identifier for this chat
  timestamp: string; // When the feedback was submitted
}

export interface FeedbackResponse {
  success: boolean;
  message: string;
}

/**
 * Attempts to use the actual backend API but gracefully falls back to a mock response
 * if the backend is not available
 *
 * @param query - The user's question
 * @param model - The LLM model to use (optional, defaults to 'gemini-2.0-flash')
 * @param extractFormat - Format preference for extraction (paragraph, bullet, table)
 * @param useHybridSearch - Whether to use hybrid search (semantic + keyword)
 * @returns Promise with the response containing answer and sources
 */
export const sendQuery = async (
  query: string,
  model: string = 'gemini-2.0-flash',
  extractFormat: string = 'paragraph',
  useHybridSearch: boolean = true
): Promise<QueryResponse> => {
  console.log('Processing query:', query, 'using model:', model);

  // Create an error-focused response with NO sources for when LLM/backend fails
  // This prevents showing misleading document references when actual query fails
  const errorResponse: QueryResponse = {
    answer: `No meaningful answer found for '${query}'. The system encountered an error processing your request.`,
    document_answer: "", // Empty string instead of null to satisfy TypeScript
    website_answer: "", // Empty string instead of null to satisfy TypeScript
    sources: [], // IMPORTANT: No sources when there's an error to avoid misleading references
    document_sources: [], // Empty sources array for documents
    website_sources: [] // Empty sources array for websites
  };

  // Simple informational response when backend is completely unreachable
  // This is clearly marked as informational and doesn't provide fake sources
  const connectionErrorResponse: QueryResponse = {
    answer: `Backend server at ${API_URL} is not available. Please ensure the server is running with 'uvicorn server:app --reload'.`,
    document_answer: "", // Empty string instead of null to satisfy TypeScript
    website_answer: "", // Empty string instead of null to satisfy TypeScript
    sources: [], // NO fabricated document sources
    document_sources: [],
    website_sources: []
  };

  // First, try to use the real backend
  try {
    // Check if the backend API is available
    console.log('Connecting to backend at:', API_URL);

    try {
      // Implement retry mechanism with model fallback
      const MAX_RETRIES = 2;
      let currentRetry = 0;
      let currentModel = model;
      let response = null;
      
      while (currentRetry <= MAX_RETRIES) {
        // Log the current attempt
        console.log(`Attempt ${currentRetry + 1}/${MAX_RETRIES + 1}: Sending query to: ${API_URL}/api/query with model ${currentModel}`);
        
        // Set up timeout control
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout - doubled for complex queries
        
        try {
          // Connect directly to the main backend server
          console.log(`Sending query to main server: ${API_URL}/api/query`);
          
          response = await fetch(`${API_URL}/api/query`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: JSON.stringify({
              query,
              model: currentModel,
              fallback_enabled: true,
              extract_format: extractFormat,
              use_hybrid_search: useHybridSearch,
              retry_on_timeout: true
            }),
            signal: controller.signal
          });
          
          // Clear the timeout to prevent memory leaks
          clearTimeout(timeoutId);
          
          // If we got a successful response, break out of the retry loop
          if (response.ok) {
            break;
          }
        } catch (error) {
          // Clear the timeout to prevent memory leaks
          clearTimeout(timeoutId);
          
          // Handle timeout or network error
          if (error instanceof DOMException && error.name === 'AbortError') {
            console.warn(`Query timed out with model ${currentModel}, retrying with fallback model...`);
          } else {
            console.error(`Fetch error with model ${currentModel}:`, error);
          }
          
          // Fall back to a faster model if available
          if (currentModel === 'gemini-2.0-flash' || currentModel === 'gemini-2.0-flash') {
            currentModel = 'gemini-2.0-flash'; // Fall back to faster model
          } else if (currentModel !== 'gemini-2.0-flash') {
            currentModel = 'gemini-2.0-flash'; // Default fallback
          }
          
          // If this is the last retry and it failed, throw the error to be caught by the outer try/catch
          if (currentRetry === MAX_RETRIES) {
            throw error;
          }
        }
        
        currentRetry++;
      }

      if (response && response.ok) {
        const data = await response.json();

        // Add model information for UI display if it's not present
        if (data && !data.llm_model) {
          data.llm_model = model;
        }

        // Check if response contains valid data
        if (!data.answer) {
          console.error('Invalid response format from server:', data);
          return {
            ...errorResponse,
            answer: `The server returned an invalid response format. Please try again later.`
          };
        }

        // Save query to Supabase if enabled
        try {
          if (process.env.REACT_APP_SAVE_QUERIES === 'true') {
            const startTime = performance.now();
            await saveQuery({
              query_text: query,
              answer_text: data.answer,
              llm_model: model,
              sources: data.sources || [],
              processing_time: (performance.now() - startTime) / 1000
            });
          }
        } catch (error) {
          console.error('Error saving query to Supabase:', error);
          // Continue even if saving fails
        }

        return data as QueryResponse;
      } else if (response) {
        // Non-OK response from backend
        console.error('Error from backend:', response.status, response.statusText);

        let errorDetails;
        try {
          errorDetails = await response.text();
        } catch (e) {
          errorDetails = 'Unable to parse error details';
        }

        console.error('Error details:', errorDetails);

        return {
          ...errorResponse,
          answer: `Error ${response.status}: ${response.statusText}. ${errorDetails}`
        };
      } else {
        // No response object at all (should never happen with our retry logic)
        return {
          ...errorResponse,
          answer: `The server did not respond. Please try again later.`
        };
      }
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.error('Query request timed out');

        // Provide a more helpful suggestion based on the current model
        let timeoutMessage = `Query timed out after 60 seconds.`;

        // Suggest a different model based on what's currently being used
        if (model === 'gemini-2.0-flash' || model === 'gemini-2.0-flash') {
          timeoutMessage += ` Try a faster model like 'gemini-2.0-flash' or simplify your query.`;
        } else if (model === 'gemini-2.0-flash') {
          timeoutMessage += ` Try simplifying your query.`;
        } else {
          timeoutMessage += ` Try a faster model or simplify your query.`;
        }

        return {
          ...errorResponse,
          answer: timeoutMessage
        };
      }

      // Other types of fetch errors (network issues, etc.)
      console.error('Error querying backend:', error);
      return connectionErrorResponse;
    }
  } catch (error: any) {
    console.error('Error connecting to backend:', error);
    return connectionErrorResponse;
  }
};

/**
 * Upload a document file to the backend
 *
 * @param file - The file to upload
 * @param uploadedBy - Name of the uploader (optional)
 * @returns Promise with the normalized upload response
 */
export const uploadDocument = async (
  file: File, 
  uploadedBy: string = 'default',
  extractTables: boolean = true,
  extractImages: boolean = true,
  extractCharts: boolean = true
): Promise<UploadResponse> => {
  console.log('Uploading document:', file.name);

  try {
    // First, upload to Supabase Storage if enabled
    let supabaseFilePath = null;
    let supabaseFileUrl = null;

    if (process.env.REACT_APP_USE_SUPABASE_STORAGE === 'true') {
      try {
        // Generate a unique file path to prevent collisions
        const timestamp = Date.now();
        const uniqueFilePath = `${uploadedBy}/${timestamp}_${file.name}`;

        // Upload to Supabase Storage
        const { data: storageData, error: storageError } = await supabase.storage
          .from('documents')
          .upload(uniqueFilePath, file, {
            cacheControl: '3600',
            upsert: false
          });

        if (storageError) {
          console.error('Error uploading to Supabase Storage:', storageError);
        } else {
          supabaseFilePath = storageData.path;

          // Get the public URL
          const { data: { publicUrl } } = supabase.storage
            .from('documents')
            .getPublicUrl(supabaseFilePath);

          supabaseFileUrl = publicUrl;
          console.log('Uploaded to Supabase Storage:', supabaseFilePath, supabaseFileUrl);
        }
      } catch (storageError) {
        console.error('Error in Supabase Storage upload:', storageError);
        // Continue with backend upload even if Supabase upload fails
      }
    }

    // Create form data for file upload to backend
    const formData = new FormData();
    formData.append('file', file);
    formData.append('uploaded_by', uploadedBy);

    // Add Supabase storage info if available
    if (supabaseFilePath) {
      formData.append('supabase_file_path', supabaseFilePath);
      formData.append('supabase_file_url', supabaseFileUrl || '');
    }

    // Send the request to the backend
    const response = await fetch(`${API_URL}/api/upload-document`, {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header - browser will set it with boundary for FormData
    });

    if (!response.ok) {
      // Format error response
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      return {
        success: false,
        message: errorData.detail || `Upload failed: ${response.status} ${response.statusText}`,
      };
    }

    // Process successful response
    const data = await response.json();
    console.log('Upload response:', data);

    // Normalize the response to match our interface
    return {
      success: true,
      message: data.message,
      chunks_extracted: data.chunks_extracted,
      chunks: data.chunks,
      data: {
        id: data.document_id || `doc-${Date.now()}`,
        path: supabaseFilePath || `/documents/${file.name}`,
        originalResponse: data
      }
    };
  } catch (error) {
    console.error('Error uploading document:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred during upload',
    };
  }
};

/**
 * Add a website URL to be scraped and indexed
 *
 * @param url - The website URL to add
 * @param submittedBy - Name of the submitter (optional)
 * @param extractionOptions - Advanced options for website extraction
 * @returns Promise with the normalized website add response
 */
export const addWebsite = async (
  url: string,
  submittedBy: string = 'default',
  extractionOptions?: Record<string, any>
): Promise<UploadResponse> => {
  console.log('Adding website:', url, 'with options:', extractionOptions);

  try {
    const request: WebsiteAddRequest = {
      url,
      submitted_by: submittedBy,
      ...extractionOptions
    };

    // Send the request to the backend
    const response = await fetch(`${API_URL}/api/add-website`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      // Format error response
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      return {
        success: false,
        message: errorData.detail || `Website add failed: ${response.status} ${response.statusText}`,
      };
    }

    // Process successful response
    const data = await response.json();
    console.log('Website add response:', data);

    // Normalize the response to match our interface
    return {
      success: true,
      message: data.message,
      chunks_extracted: data.chunks_extracted,
      chunks: data.chunks,
      data: {
        id: `web-${Date.now()}`,
        path: url,
        originalResponse: data
      }
    };
  } catch (error) {
    console.error('Error adding website:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred while adding website',
    };
  }
};

/**
 * Get document extraction details
 *
 * @param documentId - The ID of the document
 * @returns Promise with the extraction details
 */
export const getDocumentExtractionDetails = async (documentId: string): Promise<any> => {
  console.log('Getting extraction details for document:', documentId);

  try {
    const response = await fetch(`${API_URL}/api/documents/${documentId}/extraction-details`);

    if (!response.ok) {
      console.error(`Failed to get document extraction details: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get document extraction details: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting document extraction details:', error);

    // Return fallback data
    return {
      extractedContent: `Sample extracted content for ${documentId}. This is placeholder text because the actual content could not be retrieved from the server.`,
      extractionMethod: 'Unknown (error occurred)',
      qualityScore: 0,
      processingTime: 0,
      chunks: 0,
      warnings: ['Failed to retrieve extraction details from server'],
      fallbackReason: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Get document content
 *
 * @param documentId - The ID of the document
 * @returns Promise with the document content
 */
export const getDocumentContent = async (documentId: string): Promise<any> => {
  console.log('Getting content for document:', documentId);

  try {
    const response = await fetch(`${API_URL}/api/documents/${documentId}/content`);

    if (!response.ok) {
      console.error(`Failed to get document content: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get document content: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting document content:', error);

    // Return fallback data
    return {
      content: `# Document Content Unavailable\n\nThe content for document ${documentId} could not be retrieved from the server.\n\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\n\nPlease try again later or contact support if the problem persists.`,
      extraction_method: 'Unknown (error occurred)',
      quality_score: 0,
      processing_time: 0,
      chunks_count: 0
    };
  }
};

/**
 * Get website extraction details
 *
 * @param websiteId - The ID of the website
 * @returns Promise with the extraction details
 */
export const getWebsiteExtractionDetails = async (websiteId: string): Promise<any> => {
  console.log('Getting extraction details for website:', websiteId);

  try {
    const response = await fetch(`${API_URL}/api/websites/${websiteId}/extraction-details`);

    if (!response.ok) {
      console.error(`Failed to get website extraction details: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get website extraction details: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting website extraction details:', error);

    // Return fallback data
    return {
      extractedContent: `Sample extracted content for ${websiteId}. This is placeholder text because the actual content could not be retrieved from the server.`,
      extractionMethod: 'Unknown (error occurred)',
      fallbackHistory: [],
      contentQuality: 0,
      warnings: ['Failed to retrieve extraction details from server'],
      processingTime: 0,
      chunks: 0
    };
  }
};

/**
 * Get website content
 *
 * @param websiteId - The ID of the website
 * @returns Promise with the website content
 */
export const getWebsiteContent = async (websiteId: string): Promise<any> => {
  console.log('Getting content for website:', websiteId);

  try {
    const response = await fetch(`${API_URL}/api/websites/${websiteId}/content`);

    if (!response.ok) {
      console.error(`Failed to get website content: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get website content: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting website content:', error);

    // Return fallback data
    return {
      content: `# Website Content Unavailable\n\nThe content for website ${websiteId} could not be retrieved from the server.\n\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\n\nPlease try again later or contact support if the problem persists.`,
      extraction_method: 'Unknown (error occurred)',
      quality_score: 0,
      processing_time: 0,
      pages_processed: 0,
      total_links: 0
    };
  }
};

/**
 * Submit feedback for an AI answer
 *
 * @param feedbackData - The feedback data containing query, answer, issue type, etc.
 * @returns Promise with the response indicating success or failure
 */
export const submitFeedback = async (feedbackData: FeedbackData): Promise<FeedbackResponse> => {
  console.log('Submitting feedback:', feedbackData);

  try {
    const response = await fetch(`${API_URL}/api/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(feedbackData)
    });

    if (!response.ok) {
      console.error(`Failed to submit feedback: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to submit feedback: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error submitting feedback:', error);
    return {
      success: false,
      message: `Failed to submit feedback: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

/**
 * Get the configured feedback notification emails
 *
 * @returns Promise with the list of configured emails
 */
export const getFeedbackEmails = async (): Promise<{emails: string[]}> => {
  try {
    const response = await fetch(`${API_URL}/api/feedback/emails`);

    if (!response.ok) {
      console.error(`Failed to get feedback emails: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get feedback emails: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting feedback emails:', error);
    return {
      emails: []
    };
  }
};

/**
 * Update the configured feedback notification emails
 *
 * @param emails - The new list of emails to configure for feedback notifications
 * @returns Promise with the response indicating success or failure
 */
export const updateFeedbackEmails = async (emails: string[]): Promise<FeedbackResponse> => {
  try {
    const response = await fetch(`${API_URL}/api/feedback/emails`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({ emails })
    });

    if (!response.ok) {
      console.error(`Failed to update feedback emails: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to update feedback emails: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating feedback emails:', error);
    return {
      success: false,
      message: `Failed to update feedback emails: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

/**
 * Get all documents from the backend
 * @returns Promise with the list of documents
 */
export const getDocuments = async (): Promise<any[]> => {
  console.log('Fetching documents from backend...');

  try {
    const response = await fetch(`${API_URL}/api/documents`);

    if (!response.ok) {
      console.error(`Failed to get documents: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get documents: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting documents:', error);
    return []; // Return empty array on error
  }
};

/**
 * Get all websites from the backend
 * @returns Promise with the list of websites
 */
export const getWebsites = async (): Promise<any[]> => {
  console.log('Fetching websites from backend...');

  try {
    const response = await fetch(`${API_URL}/api/websites`);

    if (!response.ok) {
      console.error(`Failed to get websites: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to get websites: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting websites:', error);
    return []; // Return empty array on error
  }
};
