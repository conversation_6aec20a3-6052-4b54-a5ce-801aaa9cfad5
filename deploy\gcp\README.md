# RailGPT Google Cloud Platform Deployment

## Overview

This guide will help you deploy RailGPT on Google Cloud Platform using:
- **Cloud Run** for backend API (serverless, auto-scaling)
- **Cloud Storage** for static frontend hosting
- **Cloud Build** for CI/CD
- **Cloud CDN** for global content delivery
- **Cloud Load Balancer** for traffic management

## Prerequisites

1. **Google Cloud Account** with billing enabled
2. **Google Cloud SDK** installed locally
3. **Docker** installed (for local testing)
4. **Domain name** (optional, but recommended)

## Architecture

```
Internet → Cloud Load Balancer → Cloud CDN → Cloud Storage (Frontend)
                ↓
         Cloud Run (Backend API)
                ↓
         Supabase (Database)
```

## Cost Estimate

- **Cloud Run**: $0-20/month (based on usage)
- **Cloud Storage**: $1-5/month
- **Cloud CDN**: $1-10/month
- **Load Balancer**: $18/month
- **Total**: ~$20-55/month

## Quick Start

### 1. Setup Google Cloud Project

```bash
# Install Google Cloud SDK if not already installed
# https://cloud.google.com/sdk/docs/install

# Login to Google Cloud
gcloud auth login

# Create new project (or use existing)
gcloud projects create railgpt-production --name="RailGPT Production"

# Set project
gcloud config set project railgpt-production

# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable cdn.googleapis.com
gcloud services enable compute.googleapis.com
```

### 2. Deploy Backend to Cloud Run

```bash
# Navigate to project root
cd /path/to/railgpt

# Deploy backend
./deploy/gcp/deploy-backend.sh
```

### 3. Deploy Frontend to Cloud Storage

```bash
# Deploy frontend
./deploy/gcp/deploy-frontend.sh
```

### 4. Setup Load Balancer and CDN

```bash
# Setup load balancer
./deploy/gcp/setup-loadbalancer.sh
```

## Manual Deployment Steps

### Backend Deployment (Cloud Run)

1. **Build and push Docker image:**
```bash
# Set variables
export PROJECT_ID=railgpt-production
export REGION=us-central1
export SERVICE_NAME=railgpt-backend

# Build and push to Container Registry
gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME ./backend

# Deploy to Cloud Run
gcloud run deploy $SERVICE_NAME \
  --image gcr.io/$PROJECT_ID/$SERVICE_NAME \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 10 \
  --set-env-vars ENVIRONMENT=production \
  --set-env-vars API_HOST=0.0.0.0 \
  --set-env-vars API_PORT=8080
```

2. **Set environment variables:**
```bash
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --set-env-vars SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co \
  --set-env-vars SUPABASE_KEY=your_service_key_here \
  --set-env-vars SUPABASE_ANON_KEY=your_anon_key_here \
  --set-env-vars GEMINI_API_KEY=your_gemini_api_key_here
```

### Frontend Deployment (Cloud Storage + CDN)

1. **Create storage bucket:**
```bash
export BUCKET_NAME=railgpt-frontend-$(date +%s)

# Create bucket
gsutil mb gs://$BUCKET_NAME

# Enable website configuration
gsutil web set -m index.html -e index.html gs://$BUCKET_NAME

# Make bucket public
gsutil iam ch allUsers:objectViewer gs://$BUCKET_NAME
```

2. **Build and upload frontend:**
```bash
cd frontend

# Build production version
npm run build

# Upload to bucket
gsutil -m rsync -r -d build/ gs://$BUCKET_NAME/

# Set cache headers
gsutil -m setmeta -h "Cache-Control:public, max-age=31536000" gs://$BUCKET_NAME/static/**
gsutil -m setmeta -h "Cache-Control:public, max-age=0" gs://$BUCKET_NAME/index.html
```

### Load Balancer Setup

1. **Create backend services:**
```bash
# Create backend service for Cloud Run
gcloud compute backend-services create railgpt-backend-service \
  --global \
  --load-balancing-scheme=EXTERNAL

# Create backend service for Cloud Storage
gcloud compute backend-buckets create railgpt-frontend-bucket \
  --bucket-name=$BUCKET_NAME
```

2. **Create URL map:**
```bash
gcloud compute url-maps create railgpt-url-map \
  --default-backend-bucket=railgpt-frontend-bucket

# Add backend service for API routes
gcloud compute url-maps add-path-matcher railgpt-url-map \
  --path-matcher-name=api-matcher \
  --default-backend-service=railgpt-backend-service \
  --backend-service-path-rules="/api/*=railgpt-backend-service"
```

3. **Create HTTPS load balancer:**
```bash
# Create SSL certificate (replace with your domain)
gcloud compute ssl-certificates create railgpt-ssl-cert \
  --domains=your-domain.com,www.your-domain.com

# Create target HTTPS proxy
gcloud compute target-https-proxies create railgpt-https-proxy \
  --url-map=railgpt-url-map \
  --ssl-certificates=railgpt-ssl-cert

# Create forwarding rule
gcloud compute forwarding-rules create railgpt-https-rule \
  --global \
  --target-https-proxy=railgpt-https-proxy \
  --ports=443
```

## Environment Variables

Create a `gcp-env.yaml` file:

```yaml
# Backend environment variables for Cloud Run
SUPABASE_URL: "https://rkllidjktazafeinezgo.supabase.co"
SUPABASE_KEY: "your_service_key_here"
SUPABASE_ANON_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA"
GEMINI_API_KEY: "your_gemini_api_key_here"
OPENAI_API_KEY: "your_openai_api_key_here"
GROQ_API_KEY: "your_groq_api_key_here"
ENVIRONMENT: "production"
API_HOST: "0.0.0.0"
API_PORT: "8080"
LOG_LEVEL: "INFO"
```

## Security Configuration

### 1. IAM and Service Accounts

```bash
# Create service account for Cloud Run
gcloud iam service-accounts create railgpt-runner \
  --display-name="RailGPT Cloud Run Service Account"

# Grant necessary permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:railgpt-runner@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/storage.objectViewer"
```

### 2. VPC and Firewall

```bash
# Create VPC (optional, for advanced networking)
gcloud compute networks create railgpt-vpc --subnet-mode=regional

# Create subnet
gcloud compute networks subnets create railgpt-subnet \
  --network=railgpt-vpc \
  --range=10.0.0.0/24 \
  --region=$REGION
```

## Monitoring and Logging

### 1. Enable Cloud Monitoring

```bash
# Enable monitoring API
gcloud services enable monitoring.googleapis.com

# Create uptime check
gcloud alpha monitoring uptime create railgpt-uptime-check \
  --hostname=your-domain.com \
  --path=/health
```

### 2. Setup Alerts

```bash
# Create notification channel (email)
gcloud alpha monitoring channels create \
  --display-name="RailGPT Alerts" \
  --type=email \
  --channel-labels=email_address=<EMAIL>
```

## CI/CD with Cloud Build

Create `cloudbuild.yaml`:

```yaml
steps:
  # Build backend
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/railgpt-backend', './backend']
  
  # Push backend image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/railgpt-backend']
  
  # Deploy backend to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'railgpt-backend',
      '--image', 'gcr.io/$PROJECT_ID/railgpt-backend',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated'
    ]
  
  # Build frontend
  - name: 'node:18'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd frontend
        npm install
        npm run build
  
  # Deploy frontend to Cloud Storage
  - name: 'gcr.io/cloud-builders/gsutil'
    args: ['-m', 'rsync', '-r', '-d', 'frontend/build/', 'gs://your-bucket-name/']

substitutions:
  _REGION: us-central1

options:
  logging: CLOUD_LOGGING_ONLY
```

## Domain Configuration

### 1. DNS Setup

```bash
# Get load balancer IP
gcloud compute forwarding-rules describe railgpt-https-rule --global

# Point your domain to this IP address
# A record: your-domain.com → LOAD_BALANCER_IP
# CNAME record: www.your-domain.com → your-domain.com
```

### 2. SSL Certificate

```bash
# Check certificate status
gcloud compute ssl-certificates describe railgpt-ssl-cert --global

# Certificate will be automatically provisioned once DNS is configured
```

## Scaling and Performance

### 1. Cloud Run Scaling

```bash
# Update Cloud Run service with scaling parameters
gcloud run services update railgpt-backend \
  --region=$REGION \
  --min-instances=1 \
  --max-instances=100 \
  --concurrency=80 \
  --cpu=2 \
  --memory=2Gi
```

### 2. CDN Configuration

```bash
# Enable Cloud CDN for better performance
gcloud compute backend-buckets update railgpt-frontend-bucket \
  --enable-cdn \
  --cache-mode=CACHE_ALL_STATIC
```

## Backup and Disaster Recovery

### 1. Database Backup
- Supabase handles automatic backups
- Configure additional backup policies if needed

### 2. Code Backup
- Use Cloud Source Repositories
- Regular GitHub backups

### 3. Configuration Backup
```bash
# Export current configuration
gcloud config configurations export railgpt-config
```

## Troubleshooting

### Common Issues

1. **Cloud Run cold starts**
   - Set minimum instances to 1
   - Use Cloud Scheduler for keep-alive

2. **CORS issues**
   - Configure proper CORS in backend
   - Check load balancer configuration

3. **SSL certificate issues**
   - Verify DNS configuration
   - Wait for certificate provisioning (can take up to 24 hours)

### Debug Commands

```bash
# Check Cloud Run logs
gcloud logs read --service=railgpt-backend --limit=50

# Check load balancer status
gcloud compute url-maps describe railgpt-url-map

# Test backend directly
curl https://railgpt-backend-xxx-uc.a.run.app/health

# Test frontend
curl https://your-domain.com
```

## Cost Optimization

1. **Use Cloud Run minimum instances sparingly**
2. **Configure appropriate CPU and memory limits**
3. **Enable Cloud CDN caching**
4. **Use Cloud Storage lifecycle policies**
5. **Monitor usage with Cloud Billing alerts**

## Next Steps

1. Run the deployment scripts
2. Configure your domain
3. Set up monitoring and alerts
4. Test all functionality
5. Configure backup policies
6. Set up CI/CD pipeline

Your RailGPT application will be production-ready on Google Cloud Platform!
