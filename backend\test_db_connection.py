"""
Test script to check Supabase database connection and tables
"""

import os
import sys
import logging
import requests
import json
from dotenv import load_dotenv
from supabase_client import SupabaseClient, SUPABASE_URL, SUPABASE_KEY

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def direct_supabase_query(table_name, query_type="select"):
    """Query Supabase tables directly using REST API"""
    headers = {
        "apikey": SUPABASE_KEY,
        "Authorization": f"Bearer {SUPABASE_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=representation"
    }

    try:
        if query_type == "select":
            url = f"{SUPABASE_URL}/rest/v1/{table_name}?select=*&limit=5"
        elif query_type == "count":
            url = f"{SUPABASE_URL}/rest/v1/{table_name}?select=count"

        logger.info(f"Querying: {url}")
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Error {response.status_code}: {response.text}")
            return {"error": f"HTTP error {response.status_code}: {response.text}"}
    except Exception as e:
        logger.error(f"Exception: {str(e)}")
        return {"error": str(e)}

def test_database_connection():
    """Test connection to Supabase and check tables"""
    try:
        # Initialize Supabase client
        supabase = SupabaseClient()
        logger.info("Initialized Supabase client")

        # Check documents table
        logger.info("Checking documents table...")
        docs_result = direct_supabase_query("documents")
        logger.info(f"Documents: {json.dumps(docs_result, indent=2)}")

        # Check document_chunks table
        logger.info("Checking document_chunks table...")
        chunks_result = direct_supabase_query("document_chunks")
        logger.info(f"Document chunks: {json.dumps(chunks_result, indent=2)}")

        # Check websites table
        logger.info("Checking websites table...")
        websites_result = direct_supabase_query("websites")
        logger.info(f"Websites: {json.dumps(websites_result, indent=2)}")

        # Check website_chunks table
        logger.info("Checking website_chunks table...")
        web_chunks_result = direct_supabase_query("website_chunks")
        logger.info(f"Website chunks: {json.dumps(web_chunks_result, indent=2)}")

        # Check counts
        logger.info("Checking counts...")
        docs_count = direct_supabase_query("documents", "count")
        chunks_count = direct_supabase_query("document_chunks", "count")
        websites_count = direct_supabase_query("websites", "count")
        web_chunks_count = direct_supabase_query("website_chunks", "count")

        logger.info(f"Documents count: {docs_count}")
        logger.info(f"Document chunks count: {chunks_count}")
        logger.info(f"Websites count: {websites_count}")
        logger.info(f"Website chunks count: {web_chunks_count}")

        return True

    except Exception as e:
        logger.error(f"Error testing database connection: {str(e)}")
        return False

if __name__ == "__main__":
    test_database_connection()
