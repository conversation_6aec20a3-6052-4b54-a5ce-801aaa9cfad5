#!/usr/bin/env python3
"""
Test script to verify document viewer functionality
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_documents_api():
    """Test the documents API"""
    print("=== Testing Documents API ===")
    
    try:
        response = requests.get(f"{API_BASE}/api/documents")
        if response.status_code == 200:
            documents = response.json()
            print(f"✅ Documents API working - Found {len(documents)} documents")
            
            for i, doc in enumerate(documents[:3]):  # Show first 3
                print(f"  {i+1}. {doc.get('name', 'Unknown')} ({doc.get('fileType', 'unknown')})")
                print(f"     ID: {doc.get('id', 'unknown')}")
                print(f"     File Path: {doc.get('filePath', 'unknown')}")
                
            return documents
        else:
            print(f"❌ Documents API failed: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Documents API error: {str(e)}")
        return []

def test_websites_api():
    """Test the websites API"""
    print("\n=== Testing Websites API ===")
    
    try:
        response = requests.get(f"{API_BASE}/api/websites")
        if response.status_code == 200:
            websites = response.json()
            print(f"✅ Websites API working - Found {len(websites)} websites")
            
            for i, site in enumerate(websites[:3]):  # Show first 3
                print(f"  {i+1}. {site.get('name', 'Unknown')} ({site.get('url', 'unknown')})")
                print(f"     ID: {site.get('id', 'unknown')}")
                
            return websites
        else:
            print(f"❌ Websites API failed: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Websites API error: {str(e)}")
        return []

def test_document_viewer(documents):
    """Test the document viewer endpoint"""
    print("\n=== Testing Document Viewer ===")
    
    if not documents:
        print("❌ No documents to test viewer with")
        return
    
    for doc in documents[:2]:  # Test first 2 documents
        filename = doc.get('name', '')
        if filename:
            print(f"\nTesting viewer for: {filename}")
            
            try:
                # Test document viewer endpoint
                viewer_url = f"{API_BASE}/api/documents/view/{filename}"
                response = requests.head(viewer_url)  # Use HEAD to avoid downloading
                
                if response.status_code == 200:
                    print(f"  ✅ Document viewer accessible")
                    print(f"  Content-Type: {response.headers.get('content-type', 'unknown')}")
                elif response.status_code == 404:
                    print(f"  ❌ Document not found in storage")
                else:
                    print(f"  ❌ Document viewer failed: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Document viewer error: {str(e)}")

def test_document_content(documents):
    """Test the document content API"""
    print("\n=== Testing Document Content API ===")
    
    if not documents:
        print("❌ No documents to test content with")
        return
    
    for doc in documents[:2]:  # Test first 2 documents
        doc_id = doc.get('id', '')
        doc_name = doc.get('name', '')
        
        if doc_id:
            print(f"\nTesting content for: {doc_name} (ID: {doc_id})")
            
            try:
                content_url = f"{API_BASE}/api/documents/{doc_id}/content"
                response = requests.get(content_url)
                
                if response.status_code == 200:
                    content_data = response.json()
                    content = content_data.get('content', '')
                    print(f"  ✅ Document content accessible")
                    print(f"  Content length: {len(content)} characters")
                    print(f"  Content preview: {content[:100]}...")
                else:
                    print(f"  ❌ Document content failed: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Document content error: {str(e)}")

def main():
    """Run all tests"""
    print("🧪 Testing RailGPT Document System")
    print("=" * 50)
    
    # Test APIs
    documents = test_documents_api()
    websites = test_websites_api()
    
    # Test document functionality
    test_document_viewer(documents)
    test_document_content(documents)
    
    print("\n" + "=" * 50)
    print("🏁 Testing completed!")

if __name__ == "__main__":
    main() 