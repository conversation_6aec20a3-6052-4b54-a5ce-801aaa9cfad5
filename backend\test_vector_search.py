"""
<PERSON><PERSON><PERSON> to test the vector search functionality with text search fallback.
"""
import os
import logging
import json
from typing import List, Dict, Any
import numpy as np
from dotenv import load_dotenv
import llm_router
import vector_search

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def generate_embedding(text: str) -> List[float]:
    """Generate embedding for text using the LLM router."""
    try:
        return llm_router.generate_embedding(text)
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Try with default model
        try:
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Use a deterministic random embedding as last resort
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def test_vector_search():
    """Test the vector search functionality with text search fallback."""
    logger.info("Testing vector search functionality with text search fallback...")
    
    # Test queries
    test_queries = [
        "What is the Rapid Response app?",
        "What is VASP and who developed it?",
        "What is the full form of ACP?",
        "What is the full form of FSDS?"
    ]
    
    for query in test_queries:
        logger.info(f"\n=== Testing query: '{query}' ===\n")
        
        # Generate embedding for the query
        query_embedding = generate_embedding(query)
        
        # Test document search
        logger.info("Testing document search...")
        
        doc_results = vector_search.search_documents(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            match_threshold=0.0001,
            match_count=5
        )
        
        logger.info(f"Document search found {len(doc_results)} results")
        for i, item in enumerate(doc_results[:3]):  # Show first 3 results
            similarity = item.get("similarity", 0)
            text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
            logger.info(f"Result {i+1}: similarity={similarity:.4f}")
            logger.info(f"Text snippet: {text_snippet}")
        
        # Test website search
        logger.info("Testing website search...")
        
        web_results = vector_search.search_websites(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            match_threshold=0.0001,
            match_count=5
        )
        
        logger.info(f"Website search found {len(web_results)} results")
        for i, item in enumerate(web_results[:3]):  # Show first 3 results
            similarity = item.get("similarity", 0)
            text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
            logger.info(f"Result {i+1}: similarity={similarity:.4f}")
            logger.info(f"Text snippet: {text_snippet}")

def main():
    """Main function to test vector search functionality."""
    test_vector_search()

if __name__ == "__main__":
    main()
