{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\pages\\\\ManageCategoriesPage.tsx\";\nimport React, { useState, useEffect } from 'react';\nimport { getCategories, getWebsiteCategories, createCategory, createWebsiteCategory, deleteCategory } from '../services/api';\nimport { Plus, Edit2, Trash2, Save, X } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ManageCategoriesPage = () => {\n  const [activeTab, setActiveTab] = useState('documents');\n  const [documentCategories, setDocumentCategories] = useState([]);\n  const [websiteCategories, setWebsiteCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Form states\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [newCategory, setNewCategory] = useState({\n    name: '',\n    description: '',\n    parent_id: '',\n    sort_order: 0\n  });\n  const [newWebsiteCategory, setNewWebsiteCategory] = useState({\n    name: '',\n    description: '',\n    sort_order: 0\n  });\n  useEffect(() => {\n    loadCategories();\n  }, [activeTab]);\n  const loadCategories = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      if (activeTab === 'documents') {\n        const data = await getCategories();\n        setDocumentCategories(data);\n      } else {\n        const data = await getWebsiteCategories();\n        setWebsiteCategories(data);\n      }\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setError('Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateCategory = async () => {\n    try {\n      setError(null);\n      if (activeTab === 'documents') {\n        await createCategory(newCategory);\n        setNewCategory({\n          name: '',\n          description: '',\n          parent_id: '',\n          sort_order: 0\n        });\n      } else {\n        await createWebsiteCategory(newWebsiteCategory);\n        setNewWebsiteCategory({\n          name: '',\n          description: '',\n          sort_order: 0\n        });\n      }\n      setShowAddForm(false);\n      setSuccess('Category created successfully');\n      loadCategories();\n    } catch (error) {\n      console.error('Error creating category:', error);\n      setError(error instanceof Error ? error.message : 'Failed to create category');\n    }\n  };\n  const handleDeleteCategory = async categoryId => {\n    if (!window.confirm('Are you sure you want to delete this category?')) return;\n    try {\n      setError(null);\n      await deleteCategory(categoryId);\n      setSuccess('Category deleted successfully');\n      loadCategories();\n    } catch (error) {\n      console.error('Error deleting category:', error);\n      setError(error instanceof Error ? error.message : 'Failed to delete category');\n    }\n  };\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  const renderDocumentCategories = () => {\n    const renderCategoryTree = (categories, level = 0) => {\n      return categories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `ml-${level * 4}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-3 border-b border-gray-200 hover:bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-gray-900\",\n                children: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-xs text-gray-500\",\n                children: [\"Level \", category.level]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), category.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: category.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: category.full_path\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setEditingCategory(category.id),\n              className: \"p-1 text-blue-600 hover:text-blue-800\",\n              title: \"Edit category\",\n              children: /*#__PURE__*/_jsxDEV(Edit2, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleDeleteCategory(category.id),\n              className: \"p-1 text-red-600 hover:text-red-800\",\n              title: \"Delete category\",\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), category.children && category.children.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-4\",\n          children: renderCategoryTree(category.children, level + 1)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)]\n      }, category.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this));\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: documentCategories.length > 0 ? renderCategoryTree(documentCategories) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500 text-center py-8\",\n        children: \"No document categories found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this);\n  };\n  const renderWebsiteCategories = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: websiteCategories.length > 0 ? websiteCategories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-3 border-b border-gray-200 hover:bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-900\",\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 text-xs text-gray-500\",\n              children: [\"Order: \", category.sort_order]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 17\n          }, this), category.description && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mt-1\",\n            children: category.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setEditingCategory(category.id),\n            className: \"p-1 text-blue-600 hover:text-blue-800\",\n            title: \"Edit category\",\n            children: /*#__PURE__*/_jsxDEV(Edit2, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDeleteCategory(category.id),\n            className: \"p-1 text-red-600 hover:text-red-800\",\n            title: \"Delete category\",\n            children: /*#__PURE__*/_jsxDEV(Trash2, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 15\n        }, this)]\n      }, category.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500 text-center py-8\",\n        children: \"No website categories found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this);\n  };\n  const renderAddForm = () => {\n    if (activeTab === 'documents') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 p-4 rounded-lg space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"Add Document Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newCategory.name,\n              onChange: e => setNewCategory(prev => ({\n                ...prev,\n                name: e.target.value\n              })),\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"Category name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Parent Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: newCategory.parent_id || '',\n              onChange: e => setNewCategory(prev => ({\n                ...prev,\n                parent_id: e.target.value || undefined\n              })),\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"No parent (top level)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), documentCategories.map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cat.id,\n                children: cat.full_path\n              }, cat.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newCategory.description || '',\n              onChange: e => setNewCategory(prev => ({\n                ...prev,\n                description: e.target.value\n              })),\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              rows: 3,\n              placeholder: \"Category description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Sort Order\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: newCategory.sort_order || 0,\n              onChange: e => setNewCategory(prev => ({\n                ...prev,\n                sort_order: parseInt(e.target.value) || 0\n              })),\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCreateCategory,\n            disabled: !newCategory.name.trim(),\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), \"Create Category\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddForm(false),\n            className: \"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), \"Cancel\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 p-4 rounded-lg space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"Add Website Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newWebsiteCategory.name,\n              onChange: e => setNewWebsiteCategory(prev => ({\n                ...prev,\n                name: e.target.value\n              })),\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"Category name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Sort Order\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: newWebsiteCategory.sort_order || 0,\n              onChange: e => setNewWebsiteCategory(prev => ({\n                ...prev,\n                sort_order: parseInt(e.target.value) || 0\n              })),\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newWebsiteCategory.description || '',\n              onChange: e => setNewWebsiteCategory(prev => ({\n                ...prev,\n                description: e.target.value\n              })),\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              rows: 3,\n              placeholder: \"Category description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCreateCategory,\n            disabled: !newWebsiteCategory.name.trim(),\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), \"Create Category\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddForm(false),\n            className: \"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), \"Cancel\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full flex flex-col bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Manage Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-1\",\n          children: \"Organize your documents and websites with custom categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto max-w-4xl\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearMessages,\n            className: \"text-red-500 hover:text-red-700\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: success\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearMessages,\n            className: \"text-green-500 hover:text-green-700\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"flex\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('documents'),\n                className: `px-6 py-3 text-sm font-medium ${activeTab === 'documents' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`,\n                children: \"Document Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('websites'),\n                className: `px-6 py-3 text-sm font-medium ${activeTab === 'websites' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`,\n                children: \"Website Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: activeTab === 'documents' ? 'Document Categories' : 'Website Categories'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowAddForm(!showAddForm),\n                className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Plus, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this), \"Add Category\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), showAddForm && renderAddForm(), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: \"Loading categories...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: activeTab === 'documents' ? renderDocumentCategories() : renderWebsiteCategories()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 320,\n    columnNumber: 5\n  }, this);\n};\nexport default ManageCategoriesPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getCategories", "getWebsiteCategories", "createCategory", "createWebsiteCategory", "deleteCategory", "Plus", "Edit2", "Trash2", "Save", "X", "jsxDEV", "_jsxDEV", "ManageCategoriesPage", "activeTab", "setActiveTab", "documentCategories", "setDocumentCategories", "websiteCategories", "setWebsiteCategories", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showAddForm", "setShowAddForm", "editingCategory", "setEditingCategory", "newCategory", "setNewCategory", "name", "description", "parent_id", "sort_order", "newWebsiteCategory", "setNewWebsiteCategory", "loadCategories", "data", "console", "handleCreateCategory", "Error", "message", "handleDeleteCategory", "categoryId", "window", "confirm", "clearMessages", "renderDocumentCategories", "renderCategoryTree", "categories", "level", "map", "category", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "full_path", "onClick", "id", "title", "length", "renderWebsiteCategories", "renderAddForm", "type", "value", "onChange", "e", "prev", "target", "placeholder", "undefined", "cat", "rows", "parseInt", "disabled", "trim"], "sources": ["C:/IR App/frontend/src/pages/ManageCategoriesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  getCategories, \n  getWebsiteCategories,\n  createCategory,\n  createWebsiteCategory,\n  updateCategory,\n  deleteCategory,\n  CategoryHierarchy,\n  WebsiteCategory,\n  CategoryCreateRequest,\n  WebsiteCategoryCreateRequest\n} from '../services/api';\nimport { Plus, Edit2, Trash2, Save, X } from 'lucide-react';\n\nconst ManageCategoriesPage: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'documents' | 'websites'>('documents');\n  const [documentCategories, setDocumentCategories] = useState<CategoryHierarchy[]>([]);\n  const [websiteCategories, setWebsiteCategories] = useState<WebsiteCategory[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Form states\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingCategory, setEditingCategory] = useState<string | null>(null);\n  const [newCategory, setNewCategory] = useState<CategoryCreateRequest>({\n    name: '',\n    description: '',\n    parent_id: '',\n    sort_order: 0\n  });\n  const [newWebsiteCategory, setNewWebsiteCategory] = useState<WebsiteCategoryCreateRequest>({\n    name: '',\n    description: '',\n    sort_order: 0\n  });\n\n  useEffect(() => {\n    loadCategories();\n  }, [activeTab]);\n\n  const loadCategories = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      if (activeTab === 'documents') {\n        const data = await getCategories();\n        setDocumentCategories(data);\n      } else {\n        const data = await getWebsiteCategories();\n        setWebsiteCategories(data);\n      }\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setError('Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateCategory = async () => {\n    try {\n      setError(null);\n      if (activeTab === 'documents') {\n        await createCategory(newCategory);\n        setNewCategory({ name: '', description: '', parent_id: '', sort_order: 0 });\n      } else {\n        await createWebsiteCategory(newWebsiteCategory);\n        setNewWebsiteCategory({ name: '', description: '', sort_order: 0 });\n      }\n      setShowAddForm(false);\n      setSuccess('Category created successfully');\n      loadCategories();\n    } catch (error) {\n      console.error('Error creating category:', error);\n      setError(error instanceof Error ? error.message : 'Failed to create category');\n    }\n  };\n\n  const handleDeleteCategory = async (categoryId: string) => {\n    if (!window.confirm('Are you sure you want to delete this category?')) return;\n\n    try {\n      setError(null);\n      await deleteCategory(categoryId);\n      setSuccess('Category deleted successfully');\n      loadCategories();\n    } catch (error) {\n      console.error('Error deleting category:', error);\n      setError(error instanceof Error ? error.message : 'Failed to delete category');\n    }\n  };\n\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  const renderDocumentCategories = () => {\n    const renderCategoryTree = (categories: CategoryHierarchy[], level = 0) => {\n      return categories.map((category) => (\n        <div key={category.id} className={`ml-${level * 4}`}>\n          <div className=\"flex items-center justify-between p-3 border-b border-gray-200 hover:bg-gray-50\">\n            <div className=\"flex-1\">\n              <div className=\"flex items-center\">\n                <span className=\"font-medium text-gray-900\">{category.name}</span>\n                <span className=\"ml-2 text-xs text-gray-500\">Level {category.level}</span>\n              </div>\n              {category.description && (\n                <p className=\"text-sm text-gray-600 mt-1\">{category.description}</p>\n              )}\n              <p className=\"text-xs text-gray-500 mt-1\">{category.full_path}</p>\n            </div>\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={() => setEditingCategory(category.id)}\n                className=\"p-1 text-blue-600 hover:text-blue-800\"\n                title=\"Edit category\"\n              >\n                <Edit2 className=\"h-4 w-4\" />\n              </button>\n              <button\n                onClick={() => handleDeleteCategory(category.id)}\n                className=\"p-1 text-red-600 hover:text-red-800\"\n                title=\"Delete category\"\n              >\n                <Trash2 className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </div>\n          {category.children && category.children.length > 0 && (\n            <div className=\"ml-4\">\n              {renderCategoryTree(category.children, level + 1)}\n            </div>\n          )}\n        </div>\n      ));\n    };\n\n    return (\n      <div className=\"space-y-2\">\n        {documentCategories.length > 0 ? (\n          renderCategoryTree(documentCategories)\n        ) : (\n          <p className=\"text-gray-500 text-center py-8\">No document categories found</p>\n        )}\n      </div>\n    );\n  };\n\n  const renderWebsiteCategories = () => {\n    return (\n      <div className=\"space-y-2\">\n        {websiteCategories.length > 0 ? (\n          websiteCategories.map((category) => (\n            <div key={category.id} className=\"flex items-center justify-between p-3 border-b border-gray-200 hover:bg-gray-50\">\n              <div className=\"flex-1\">\n                <div className=\"flex items-center\">\n                  <span className=\"font-medium text-gray-900\">{category.name}</span>\n                  <span className=\"ml-2 text-xs text-gray-500\">Order: {category.sort_order}</span>\n                </div>\n                {category.description && (\n                  <p className=\"text-sm text-gray-600 mt-1\">{category.description}</p>\n                )}\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => setEditingCategory(category.id)}\n                  className=\"p-1 text-blue-600 hover:text-blue-800\"\n                  title=\"Edit category\"\n                >\n                  <Edit2 className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => handleDeleteCategory(category.id)}\n                  className=\"p-1 text-red-600 hover:text-red-800\"\n                  title=\"Delete category\"\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n          ))\n        ) : (\n          <p className=\"text-gray-500 text-center py-8\">No website categories found</p>\n        )}\n      </div>\n    );\n  };\n\n  const renderAddForm = () => {\n    if (activeTab === 'documents') {\n      return (\n        <div className=\"bg-gray-50 p-4 rounded-lg space-y-4\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Add Document Category</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name *</label>\n              <input\n                type=\"text\"\n                value={newCategory.name}\n                onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Category name\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Parent Category</label>\n              <select\n                value={newCategory.parent_id || ''}\n                onChange={(e) => setNewCategory(prev => ({ ...prev, parent_id: e.target.value || undefined }))}\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">No parent (top level)</option>\n                {documentCategories.map((cat) => (\n                  <option key={cat.id} value={cat.id}>\n                    {cat.full_path}\n                  </option>\n                ))}\n              </select>\n            </div>\n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Description</label>\n              <textarea\n                value={newCategory.description || ''}\n                onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                rows={3}\n                placeholder=\"Category description\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Sort Order</label>\n              <input\n                type=\"number\"\n                value={newCategory.sort_order || 0}\n                onChange={(e) => setNewCategory(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={handleCreateCategory}\n              disabled={!newCategory.name.trim()}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center\"\n            >\n              <Save className=\"h-4 w-4 mr-2\" />\n              Create Category\n            </button>\n            <button\n              onClick={() => setShowAddForm(false)}\n              className=\"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\"\n            >\n              <X className=\"h-4 w-4 mr-2\" />\n              Cancel\n            </button>\n          </div>\n        </div>\n      );\n    } else {\n      return (\n        <div className=\"bg-gray-50 p-4 rounded-lg space-y-4\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Add Website Category</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name *</label>\n              <input\n                type=\"text\"\n                value={newWebsiteCategory.name}\n                onChange={(e) => setNewWebsiteCategory(prev => ({ ...prev, name: e.target.value }))}\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Category name\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Sort Order</label>\n              <input\n                type=\"number\"\n                value={newWebsiteCategory.sort_order || 0}\n                onChange={(e) => setNewWebsiteCategory(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Description</label>\n              <textarea\n                value={newWebsiteCategory.description || ''}\n                onChange={(e) => setNewWebsiteCategory(prev => ({ ...prev, description: e.target.value }))}\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                rows={3}\n                placeholder=\"Category description\"\n              />\n            </div>\n          </div>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={handleCreateCategory}\n              disabled={!newWebsiteCategory.name.trim()}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center\"\n            >\n              <Save className=\"h-4 w-4 mr-2\" />\n              Create Category\n            </button>\n            <button\n              onClick={() => setShowAddForm(false)}\n              className=\"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400\"\n            >\n              <X className=\"h-4 w-4 mr-2\" />\n              Cancel\n            </button>\n          </div>\n        </div>\n      );\n    }\n  };\n\n  return (\n    <div className=\"h-full flex flex-col bg-gray-50\">\n      <div className=\"bg-white p-4 shadow-sm\">\n        <div className=\"container mx-auto\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Manage Categories</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Organize your documents and websites with custom categories\n          </p>\n        </div>\n      </div>\n\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <div className=\"container mx-auto max-w-4xl\">\n          {/* Messages */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex justify-between items-center\">\n              <span>{error}</span>\n              <button onClick={clearMessages} className=\"text-red-500 hover:text-red-700\">\n                <X className=\"h-4 w-4\" />\n              </button>\n            </div>\n          )}\n\n          {success && (\n            <div className=\"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded flex justify-between items-center\">\n              <span>{success}</span>\n              <button onClick={clearMessages} className=\"text-green-500 hover:text-green-700\">\n                <X className=\"h-4 w-4\" />\n              </button>\n            </div>\n          )}\n\n          {/* Tabs */}\n          <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n            <div className=\"border-b border-gray-200\">\n              <nav className=\"flex\">\n                <button\n                  onClick={() => setActiveTab('documents')}\n                  className={`px-6 py-3 text-sm font-medium ${\n                    activeTab === 'documents'\n                      ? 'border-b-2 border-blue-500 text-blue-600'\n                      : 'text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  Document Categories\n                </button>\n                <button\n                  onClick={() => setActiveTab('websites')}\n                  className={`px-6 py-3 text-sm font-medium ${\n                    activeTab === 'websites'\n                      ? 'border-b-2 border-blue-500 text-blue-600'\n                      : 'text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  Website Categories\n                </button>\n              </nav>\n            </div>\n\n            <div className=\"p-6\">\n              {/* Add Category Button */}\n              <div className=\"flex justify-between items-center mb-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">\n                  {activeTab === 'documents' ? 'Document Categories' : 'Website Categories'}\n                </h2>\n                <button\n                  onClick={() => setShowAddForm(!showAddForm)}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Category\n                </button>\n              </div>\n\n              {/* Add Form */}\n              {showAddForm && renderAddForm()}\n\n              {/* Categories List */}\n              {loading ? (\n                <div className=\"text-center py-8\">\n                  <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n                  <p className=\"mt-2 text-gray-600\">Loading categories...</p>\n                </div>\n              ) : (\n                <div className=\"mt-6\">\n                  {activeTab === 'documents' ? renderDocumentCategories() : renderWebsiteCategories()}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ManageCategoriesPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,aAAa,EACbC,oBAAoB,EACpBC,cAAc,EACdC,qBAAqB,EAErBC,cAAc,QAKT,iBAAiB;AACxB,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAC3C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAA2B,WAAW,CAAC;EACjF,MAAM,CAACiB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlB,QAAQ,CAAsB,EAAE,CAAC;EACrF,MAAM,CAACmB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpB,QAAQ,CAAoB,EAAE,CAAC;EACjF,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAwB;IACpEiC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtC,QAAQ,CAA+B;IACzFiC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfE,UAAU,EAAE;EACd,CAAC,CAAC;EAEFnC,SAAS,CAAC,MAAM;IACdsC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACxB,SAAS,CAAC,CAAC;EAEf,MAAMwB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCjB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,IAAIT,SAAS,KAAK,WAAW,EAAE;QAC7B,MAAMyB,IAAI,GAAG,MAAMtC,aAAa,CAAC,CAAC;QAClCgB,qBAAqB,CAACsB,IAAI,CAAC;MAC7B,CAAC,MAAM;QACL,MAAMA,IAAI,GAAG,MAAMrC,oBAAoB,CAAC,CAAC;QACzCiB,oBAAoB,CAACoB,IAAI,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDC,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFlB,QAAQ,CAAC,IAAI,CAAC;MACd,IAAIT,SAAS,KAAK,WAAW,EAAE;QAC7B,MAAMX,cAAc,CAAC2B,WAAW,CAAC;QACjCC,cAAc,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAE,CAAC,CAAC;MAC7E,CAAC,MAAM;QACL,MAAM/B,qBAAqB,CAACgC,kBAAkB,CAAC;QAC/CC,qBAAqB,CAAC;UAAEL,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEE,UAAU,EAAE;QAAE,CAAC,CAAC;MACrE;MACAR,cAAc,CAAC,KAAK,CAAC;MACrBF,UAAU,CAAC,+BAA+B,CAAC;MAC3Ca,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAACD,KAAK,YAAYoB,KAAK,GAAGpB,KAAK,CAACqB,OAAO,GAAG,2BAA2B,CAAC;IAChF;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAG,MAAOC,UAAkB,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;IAEvE,IAAI;MACFxB,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMlB,cAAc,CAACwC,UAAU,CAAC;MAChCpB,UAAU,CAAC,+BAA+B,CAAC;MAC3Ca,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAACD,KAAK,YAAYoB,KAAK,GAAGpB,KAAK,CAACqB,OAAO,GAAG,2BAA2B,CAAC;IAChF;EACF,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1BzB,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAMwB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,kBAAkB,GAAGA,CAACC,UAA+B,EAAEC,KAAK,GAAG,CAAC,KAAK;MACzE,OAAOD,UAAU,CAACE,GAAG,CAAEC,QAAQ,iBAC7B1C,OAAA;QAAuB2C,SAAS,EAAE,MAAMH,KAAK,GAAG,CAAC,EAAG;QAAAI,QAAA,gBAClD5C,OAAA;UAAK2C,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAC9F5C,OAAA;YAAK2C,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB5C,OAAA;cAAK2C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5C,OAAA;gBAAM2C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEF,QAAQ,CAACtB;cAAI;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClEhD,OAAA;gBAAM2C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,QAAM,EAACF,QAAQ,CAACF,KAAK;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,EACLN,QAAQ,CAACrB,WAAW,iBACnBrB,OAAA;cAAG2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAEF,QAAQ,CAACrB;YAAW;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACpE,eACDhD,OAAA;cAAG2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAEF,QAAQ,CAACO;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B5C,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAACyB,QAAQ,CAACS,EAAE,CAAE;cAC/CR,SAAS,EAAC,uCAAuC;cACjDS,KAAK,EAAC,eAAe;cAAAR,QAAA,eAErB5C,OAAA,CAACL,KAAK;gBAACgD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACThD,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAMlB,oBAAoB,CAACU,QAAQ,CAACS,EAAE,CAAE;cACjDR,SAAS,EAAC,qCAAqC;cAC/CS,KAAK,EAAC,iBAAiB;cAAAR,QAAA,eAEvB5C,OAAA,CAACJ,MAAM;gBAAC+C,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLN,QAAQ,CAACE,QAAQ,IAAIF,QAAQ,CAACE,QAAQ,CAACS,MAAM,GAAG,CAAC,iBAChDrD,OAAA;UAAK2C,SAAS,EAAC,MAAM;UAAAC,QAAA,EAClBN,kBAAkB,CAACI,QAAQ,CAACE,QAAQ,EAAEJ,KAAK,GAAG,CAAC;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CACN;MAAA,GAjCON,QAAQ,CAACS,EAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkChB,CACN,CAAC;IACJ,CAAC;IAED,oBACEhD,OAAA;MAAK2C,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBxC,kBAAkB,CAACiD,MAAM,GAAG,CAAC,GAC5Bf,kBAAkB,CAAClC,kBAAkB,CAAC,gBAEtCJ,OAAA;QAAG2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAAC;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAC9E;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAMM,uBAAuB,GAAGA,CAAA,KAAM;IACpC,oBACEtD,OAAA;MAAK2C,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBtC,iBAAiB,CAAC+C,MAAM,GAAG,CAAC,GAC3B/C,iBAAiB,CAACmC,GAAG,CAAEC,QAAQ,iBAC7B1C,OAAA;QAAuB2C,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAChH5C,OAAA;UAAK2C,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrB5C,OAAA;YAAK2C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC5C,OAAA;cAAM2C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEF,QAAQ,CAACtB;YAAI;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClEhD,OAAA;cAAM2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,SAAO,EAACF,QAAQ,CAACnB,UAAU;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,EACLN,QAAQ,CAACrB,WAAW,iBACnBrB,OAAA;YAAG2C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAEF,QAAQ,CAACrB;UAAW;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACpE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B5C,OAAA;YACEkD,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAACyB,QAAQ,CAACS,EAAE,CAAE;YAC/CR,SAAS,EAAC,uCAAuC;YACjDS,KAAK,EAAC,eAAe;YAAAR,QAAA,eAErB5C,OAAA,CAACL,KAAK;cAACgD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACThD,OAAA;YACEkD,OAAO,EAAEA,CAAA,KAAMlB,oBAAoB,CAACU,QAAQ,CAACS,EAAE,CAAE;YACjDR,SAAS,EAAC,qCAAqC;YAC/CS,KAAK,EAAC,iBAAiB;YAAAR,QAAA,eAEvB5C,OAAA,CAACJ,MAAM;cAAC+C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GAzBEN,QAAQ,CAACS,EAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0BhB,CACN,CAAC,gBAEFhD,OAAA;QAAG2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAC7E;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIrD,SAAS,KAAK,WAAW,EAAE;MAC7B,oBACEF,OAAA;QAAK2C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClD5C,OAAA;UAAI2C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EhD,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAO2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9EhD,OAAA;cACEwD,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEvC,WAAW,CAACE,IAAK;cACxBsC,QAAQ,EAAGC,CAAC,IAAKxC,cAAc,CAACyC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAExC,IAAI,EAAEuC,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cAC7Ed,SAAS,EAAC,kGAAkG;cAC5GmB,WAAW,EAAC;YAAe;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhD,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAO2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvFhD,OAAA;cACEyD,KAAK,EAAEvC,WAAW,CAACI,SAAS,IAAI,EAAG;cACnCoC,QAAQ,EAAGC,CAAC,IAAKxC,cAAc,CAACyC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEtC,SAAS,EAAEqC,CAAC,CAACE,MAAM,CAACJ,KAAK,IAAIM;cAAU,CAAC,CAAC,CAAE;cAC/FpB,SAAS,EAAC,kGAAkG;cAAAC,QAAA,gBAE5G5C,OAAA;gBAAQyD,KAAK,EAAC,EAAE;gBAAAb,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9C5C,kBAAkB,CAACqC,GAAG,CAAEuB,GAAG,iBAC1BhE,OAAA;gBAAqByD,KAAK,EAAEO,GAAG,CAACb,EAAG;gBAAAP,QAAA,EAChCoB,GAAG,CAACf;cAAS,GADHe,GAAG,CAACb,EAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEX,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5C,OAAA;cAAO2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnFhD,OAAA;cACEyD,KAAK,EAAEvC,WAAW,CAACG,WAAW,IAAI,EAAG;cACrCqC,QAAQ,EAAGC,CAAC,IAAKxC,cAAc,CAACyC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEvC,WAAW,EAAEsC,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cACpFd,SAAS,EAAC,kGAAkG;cAC5GsB,IAAI,EAAE,CAAE;cACRH,WAAW,EAAC;YAAsB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhD,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAO2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClFhD,OAAA;cACEwD,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAEvC,WAAW,CAACK,UAAU,IAAI,CAAE;cACnCmC,QAAQ,EAAGC,CAAC,IAAKxC,cAAc,CAACyC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAErC,UAAU,EAAE2C,QAAQ,CAACP,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,IAAI;cAAE,CAAC,CAAC,CAAE;cAClGd,SAAS,EAAC;YAAkG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B5C,OAAA;YACEkD,OAAO,EAAErB,oBAAqB;YAC9BsC,QAAQ,EAAE,CAACjD,WAAW,CAACE,IAAI,CAACgD,IAAI,CAAC,CAAE;YACnCzB,SAAS,EAAC,qGAAqG;YAAAC,QAAA,gBAE/G5C,OAAA,CAACH,IAAI;cAAC8C,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThD,OAAA;YACEkD,OAAO,EAAEA,CAAA,KAAMnC,cAAc,CAAC,KAAK,CAAE;YACrC4B,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAE5E5C,OAAA,CAACF,CAAC;cAAC6C,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEhC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV,CAAC,MAAM;MACL,oBACEhD,OAAA;QAAK2C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClD5C,OAAA;UAAI2C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EhD,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAO2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9EhD,OAAA;cACEwD,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEjC,kBAAkB,CAACJ,IAAK;cAC/BsC,QAAQ,EAAGC,CAAC,IAAKlC,qBAAqB,CAACmC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAExC,IAAI,EAAEuC,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cACpFd,SAAS,EAAC,kGAAkG;cAC5GmB,WAAW,EAAC;YAAe;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhD,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAO2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClFhD,OAAA;cACEwD,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAEjC,kBAAkB,CAACD,UAAU,IAAI,CAAE;cAC1CmC,QAAQ,EAAGC,CAAC,IAAKlC,qBAAqB,CAACmC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAErC,UAAU,EAAE2C,QAAQ,CAACP,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,IAAI;cAAE,CAAC,CAAC,CAAE;cACzGd,SAAS,EAAC;YAAkG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5C,OAAA;cAAO2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnFhD,OAAA;cACEyD,KAAK,EAAEjC,kBAAkB,CAACH,WAAW,IAAI,EAAG;cAC5CqC,QAAQ,EAAGC,CAAC,IAAKlC,qBAAqB,CAACmC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEvC,WAAW,EAAEsC,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cAC3Fd,SAAS,EAAC,kGAAkG;cAC5GsB,IAAI,EAAE,CAAE;cACRH,WAAW,EAAC;YAAsB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B5C,OAAA;YACEkD,OAAO,EAAErB,oBAAqB;YAC9BsC,QAAQ,EAAE,CAAC3C,kBAAkB,CAACJ,IAAI,CAACgD,IAAI,CAAC,CAAE;YAC1CzB,SAAS,EAAC,qGAAqG;YAAAC,QAAA,gBAE/G5C,OAAA,CAACH,IAAI;cAAC8C,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThD,OAAA;YACEkD,OAAO,EAAEA,CAAA,KAAMnC,cAAc,CAAC,KAAK,CAAE;YACrC4B,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAE5E5C,OAAA,CAACF,CAAC;cAAC6C,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEhC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;EACF,CAAC;EAED,oBACEhD,OAAA;IAAK2C,SAAS,EAAC,iCAAiC;IAAAC,QAAA,gBAC9C5C,OAAA;MAAK2C,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrC5C,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5C,OAAA;UAAI2C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEhD,OAAA;UAAG2C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhD,OAAA;MAAK2C,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzC5C,OAAA;QAAK2C,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GAEzClC,KAAK,iBACJV,OAAA;UAAK2C,SAAS,EAAC,kGAAkG;UAAAC,QAAA,gBAC/G5C,OAAA;YAAA4C,QAAA,EAAOlC;UAAK;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpBhD,OAAA;YAAQkD,OAAO,EAAEd,aAAc;YAACO,SAAS,EAAC,iCAAiC;YAAAC,QAAA,eACzE5C,OAAA,CAACF,CAAC;cAAC6C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEApC,OAAO,iBACNZ,OAAA;UAAK2C,SAAS,EAAC,wGAAwG;UAAAC,QAAA,gBACrH5C,OAAA;YAAA4C,QAAA,EAAOhC;UAAO;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtBhD,OAAA;YAAQkD,OAAO,EAAEd,aAAc;YAACO,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAC7E5C,OAAA,CAACF,CAAC;cAAC6C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAGDhD,OAAA;UAAK2C,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5D5C,OAAA;YAAK2C,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvC5C,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5C,OAAA;gBACEkD,OAAO,EAAEA,CAAA,KAAM/C,YAAY,CAAC,WAAW,CAAE;gBACzCwC,SAAS,EAAE,iCACTzC,SAAS,KAAK,WAAW,GACrB,0CAA0C,GAC1C,mCAAmC,EACtC;gBAAA0C,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThD,OAAA;gBACEkD,OAAO,EAAEA,CAAA,KAAM/C,YAAY,CAAC,UAAU,CAAE;gBACxCwC,SAAS,EAAE,iCACTzC,SAAS,KAAK,UAAU,GACpB,0CAA0C,GAC1C,mCAAmC,EACtC;gBAAA0C,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhD,OAAA;YAAK2C,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAElB5C,OAAA;cAAK2C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD5C,OAAA;gBAAI2C,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAChD1C,SAAS,KAAK,WAAW,GAAG,qBAAqB,GAAG;cAAoB;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,eACLhD,OAAA;gBACEkD,OAAO,EAAEA,CAAA,KAAMnC,cAAc,CAAC,CAACD,WAAW,CAAE;gBAC5C6B,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,gBAE3F5C,OAAA,CAACN,IAAI;kBAACiD,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGLlC,WAAW,IAAIyC,aAAa,CAAC,CAAC,EAG9B/C,OAAO,gBACNR,OAAA;cAAK2C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B5C,OAAA;gBAAK2C,SAAS,EAAC;cAA2E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjGhD,OAAA;gBAAG2C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,gBAENhD,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAC,QAAA,EAClB1C,SAAS,KAAK,WAAW,GAAGmC,wBAAwB,CAAC,CAAC,GAAGiB,uBAAuB,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAED,eAAe/C,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}