import React, { useState } from 'react';

interface SimplePDFViewerProps {
  url: string;
  fileName: string;
}

const SimplePDFViewer: React.FC<SimplePDFViewerProps> = ({ url, fileName }) => {
  const [viewMode, setViewMode] = useState<'iframe' | 'embed' | 'link'>('iframe');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const handleIframeLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setError('Failed to load PDF in viewer');
  };

  return (
    <div className="flex flex-col items-center">
      <div className="mb-4 w-full flex justify-between items-center bg-gray-100 p-2 rounded-lg">
        <div className="text-sm font-medium text-gray-700">
          {fileName}
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode('iframe')}
            className={`px-3 py-1 rounded text-sm ${
              viewMode === 'iframe' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Viewer
          </button>
          <button
            onClick={() => setViewMode('embed')}
            className={`px-3 py-1 rounded text-sm ${
              viewMode === 'embed' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Embed
          </button>
          <button
            onClick={() => window.open(url, '_blank')}
            className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
          >
            New Tab
          </button>
          <a
            href={url}
            download={fileName}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Download
          </a>
        </div>
      </div>

      <div className="border border-gray-300 rounded-lg overflow-hidden w-full bg-gray-50 relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="text-gray-600">Loading PDF...</span>
            </div>
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center h-60 text-red-600 text-center p-4">
            <div>
              <p className="font-semibold">Failed to load PDF</p>
              <p className="text-sm mt-2">{error}</p>
              <div className="mt-4 space-y-2">
                <button
                  onClick={() => window.open(url, '_blank')}
                  className="block w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Open in New Tab
                </button>
                <a
                  href={url}
                  download={fileName}
                  className="block w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-center"
                >
                  Download PDF
                </a>
              </div>
            </div>
          </div>
        )}

        {viewMode === 'iframe' && !error && (
          <iframe
            src={`${url}#toolbar=1&navpanes=1&scrollbar=1`}
            width="100%"
            height="600px"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            title={`PDF Viewer - ${fileName}`}
            className="border-0"
          />
        )}

        {viewMode === 'embed' && !error && (
          <embed
            src={url}
            type="application/pdf"
            width="100%"
            height="600px"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            title={`PDF Embed - ${fileName}`}
          />
        )}
      </div>

      <div className="mt-2 text-xs text-gray-500 text-center">
        If the PDF doesn't load, try opening it in a new tab or downloading it directly.
      </div>
    </div>
  );
};

export default SimplePDFViewer; 