import os
import re
import logging
import numpy as np
import shutil
import time
import uuid
import json
import random
from typing import List, Dict, Any, Optional, Tuple
import fitz  # PyMuPDF
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Depends, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Import custom modules
from website_scraper import extract_website_text
from document_extractor import extract_document
from vector_db import vector_db  # Import the vector database
import llm_router  # Import the new LLM router module
from feedback import FeedbackData, send_feedback_email, get_feedback_emails, update_feedback_emails, FeedbackEmailConfig  # Import feedback module
from supabase_client import supabase  # Import Supabase client
from config import config  # Import secure configuration
import vector_search  # Import standardized vector search

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Direct document search functions
def search_documents_by_title(query: str, limit: int = 5):
    """Search for documents by title."""
    logger.info(f"Searching for documents with title containing '{query}'...")

    # Prepare the query
    search_query = f"""
    SELECT
        d.id,
        d.display_name,
        d.file_path,
        d.file_type,
        d.created_at,
        d.updated_at
    FROM
        documents d
    WHERE
        LOWER(d.display_name) LIKE LOWER('%{query}%')
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by title: {result['error']}")
            return []

        logger.info(f"Found {len(result)} documents matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by title: {str(e)}")
        return []

def get_document_chunks(document_id: str, limit: int = 10):
    """Get chunks for a specific document."""
    logger.info(f"Getting chunks for document {document_id}...")

    # Prepare the query
    chunks_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.document_id = '{document_id}'
    ORDER BY
        dc.page_number, dc.chunk_index
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error getting document chunks: {result['error']}")
            return []

        logger.info(f"Found {len(result)} chunks for document {document_id}")
        return result
    except Exception as e:
        logger.error(f"Error getting document chunks: {str(e)}")
        return []

def search_documents_by_content(query: str, limit: int = 10):
    """Search for documents by content using text search."""
    logger.info(f"Searching for documents with content containing '{query}'...")

    # Extract key terms for better search
    import re
    from string import punctuation

    # Check if this is an acronym search (e.g., "full form of ACP")
    acronym_match = re.search(r'(?:full\s+form\s+of|what\s+(?:does|is|are)\s+the\s+full\s+form\s+of|what\s+(?:does|is|are)\s+)\s*([A-Z]{2,})', query, re.IGNORECASE)

    if acronym_match:
        # This is an acronym search, extract the acronym
        acronym = acronym_match.group(1).upper()
        logger.info(f"Detected acronym search for: {acronym}")

        # Special handling for acronym searches
        return search_for_acronym(acronym, limit)

    # Clean the query and extract key terms
    clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
    words = clean_query.split()
    stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
    key_terms = [word for word in words if word not in stop_words and len(word) > 2]

    # If no key terms found, use the original query
    if not key_terms:
        key_terms = [query]

    logger.info(f"Extracted key terms for content search: {key_terms}")

    # Create a tsquery string with OR operators between terms for broader matches
    ts_query_terms = " | ".join([term.replace("'", "''") for term in key_terms])

    # Sanitize the full query for logging
    sanitized_full_query = query.replace("'", "''")

    # Prepare the query with both exact phrase matching and key term matching
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type,
        GREATEST(
            ts_rank(to_tsvector('english', dc.text), to_tsquery('english', '{ts_query_terms}')),
            ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', '{sanitized_full_query}'))
        ) AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        to_tsvector('english', dc.text) @@ to_tsquery('english', '{ts_query_terms}')
        OR to_tsvector('english', dc.text) @@ plainto_tsquery('english', '{sanitized_full_query}')
        OR dc.text ILIKE '%{sanitized_full_query}%'
    ORDER BY
        similarity DESC
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by content: {result['error']}")
            return []

        logger.info(f"Found {len(result)} document chunks matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by content: {str(e)}")
        # Try a simpler query if the complex one fails
        try:
            logger.info("Trying simpler content search query...")
            simple_query = f"""
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                d.display_name as filename,
                d.file_path as url,
                'document' as source_type,
                1.0 AS similarity
            FROM
                document_chunks dc
            JOIN
                documents d ON dc.document_id = d.id
            WHERE
                dc.text ILIKE '%{sanitized_full_query}%'
            LIMIT {limit}
            """
            result = supabase.execute_query(simple_query)

            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error with simple content search: {result['error']}")
                return []

            logger.info(f"Found {len(result)} document chunks with simple search")
            return result
        except Exception as simple_error:
            logger.error(f"Simple content search also failed: {str(simple_error)}")
            return []

def search_for_acronym(acronym: str, limit: int = 5):
    """Special search function for acronyms."""
    logger.info(f"Performing specialized acronym search for: {acronym}")

    # Sanitize the acronym
    sanitized_acronym = acronym.replace("'", "''")

    # Pattern 1: Look for exact acronym followed by words in parentheses (e.g., "ACP (Alarm Chain Pulling)")
    # Pattern 2: Look for exact acronym followed by "stands for" or "means" or "is"
    # Pattern 3: Look for exact acronym followed by a dash or colon and then words
    # Pattern 4: Look for the words "full form" near the acronym
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type,
        1.0 AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)'
        OR dc.text ~* '\\b{sanitized_acronym}\\s+(stands\\s+for|means|is)\\b'
        OR dc.text ~* '\\b{sanitized_acronym}\\s*[-:]'
        OR (dc.text ~* '\\b{sanitized_acronym}\\b' AND dc.text ~* 'full\\s+form')
        OR dc.text ~* '\\b{sanitized_acronym}\\b.{{0,30}}(stands\\s+for|means|refers\\s+to|is)'
    ORDER BY
        CASE
            WHEN dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)' THEN 1
            WHEN dc.text ~* 'full\\s+form.{{0,20}}\\b{sanitized_acronym}\\b' THEN 2
            WHEN dc.text ~* '\\b{sanitized_acronym}\\b.{{0,20}}full\\s+form' THEN 3
            ELSE 4
        END
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching for acronym: {result['error']}")
            return []

        logger.info(f"Found {len(result)} document chunks matching acronym '{acronym}'")

        # If no results found with the specialized search, try a more general search
        if not result:
            logger.info(f"No specialized matches for acronym '{acronym}', trying general search")

            # Fallback to a more general search
            general_query = f"""
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                d.display_name as filename,
                d.file_path as url,
                'document' as source_type,
                1.0 AS similarity
            FROM
                document_chunks dc
            JOIN
                documents d ON dc.document_id = d.id
            WHERE
                dc.text ~* '\\b{sanitized_acronym}\\b'
            LIMIT {limit}
            """

            result = supabase.execute_query(general_query)

            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error with general acronym search: {result['error']}")
                return []

            logger.info(f"Found {len(result)} document chunks with general acronym search")

        return result
    except Exception as e:
        logger.error(f"Error searching for acronym: {str(e)}")
        return []

# Load environment variables
load_dotenv()

# Configure LLM models through the router
available_models = llm_router.get_available_models()
logger.info(f"Available LLM models: {[model['id'] for model in available_models]}")

# For backward compatibility
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    logger.warning("GEMINI_API_KEY not found in environment variables. Using mock embeddings.")

# Global variable to store document chunks with embeddings
DOCUMENT_CHUNKS = []
# Priority weights for different source types
DOCUMENT_PRIORITY_WEIGHT = 2.5  # Higher priority for documents
WEBSITE_PRIORITY_WEIGHT = 1.5   # Medium priority for websites
# Threshold for considering document chunks relevant
RELEVANCE_THRESHOLD = 0.05  # Very low threshold to prioritize documents

# Create FastAPI app
app = FastAPI(title="Document Management System")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define API request and response models
class QueryRequest(BaseModel):
    query: str
    model: Optional[str] = "gemini-2.0-flash"  # Changed to gemini-2.0-flash to fix timeout issues
    fallback_enabled: Optional[bool] = True  # Enable LLM fallback by default
    extract_format: Optional[str] = "paragraph"  # paragraph, bullet, or table
    use_hybrid_search: Optional[bool] = True  # Enable hybrid search by default
    retry_on_timeout: Optional[bool] = True  # Try fallback model on timeout
    context_mode: Optional[str] = "flexible"  # strict, flexible, or none (no context)

class WebsiteScrapeRequest(BaseModel):
    url: str

class WebsiteAddRequest(BaseModel):
    url: str
    submitted_by: Optional[str] = None
    role: Optional[str] = None
    follow_links: Optional[bool] = False
    extraction_depth: Optional[int] = 1
    extract_images: Optional[bool] = False
    extract_tables: Optional[bool] = True
    max_pages: Optional[int] = 10
    extractor_type: Optional[str] = "trafilatura"
    domain_category: Optional[str] = "general"
    embedding_model: Optional[str] = "gemini-2.0-flash"  # Model for embeddings

class Source(BaseModel):
    source_type: str  # "document" or "website"
    # For documents
    filename: Optional[str] = None
    name: Optional[str] = None  # Display name for the document
    page: Optional[int] = None
    # For websites
    url: Optional[str] = None
    link: Optional[str] = None  # For document viewer links

class QueryResponse(BaseModel):
    answer: str  # Combined answer from all sources
    document_answer: Optional[str] = None  # Answer only from document sources
    website_answer: Optional[str] = None  # Answer only from website sources
    sources: List[Source]  # All sources
    document_sources: Optional[List[Source]] = None  # Only document sources
    website_sources: Optional[List[Source]] = None  # Only website sources
    llm_model: Optional[str] = None  # The LLM model used for generating the answer
    llm_fallback: Optional[bool] = False  # Whether the answer was generated using LLM fallback

class ChunkData(BaseModel):
    filename: str
    page: int
    chunk_id: str
    text: str
    embedding: Optional[List[float]] = None

# Document processing functions
def clean_text(text: str) -> str:
    """Clean and normalize text from PDF extraction."""
    text = re.sub(r'\s+', ' ', text)  # Replace multiple whitespace with single space
    return text.strip()

def generate_embedding(text: str, model_id: str = "gemini-2.0-flash") -> List[float]:
    """Generate embedding vector for text using the LLM router."""
    try:
        # Use the LLM router to generate embeddings
        return llm_router.generate_embedding(text, model_id)
    except Exception as e:
        logger.error(f"Error generating embedding with {model_id}: {str(e)}")
        # Try with a different model before falling back to random
        try:
            logger.info(f"Retrying embedding generation with default model")
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Fallback to a consistent embedding rather than random
            # This ensures that if we use this fallback, at least all fallbacks will be similar
            # Using a seed ensures consistency
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def cosine_similarity(embedding1, embedding2):
    """
    Calculate cosine similarity between two embeddings, handling string conversions.
    """
    # Convert embeddings to numpy arrays if they are not already
    try:
        # Handle string embeddings (from JSON)
        if isinstance(embedding1, str):
            try:
                embedding1 = json.loads(embedding1)
            except:
                logger.error("Failed to parse string embedding1")
                return 0.0

        if isinstance(embedding2, str):
            try:
                embedding2 = json.loads(embedding2)
            except:
                logger.error("Failed to parse string embedding2")
                return 0.0

        # Ensure embeddings are numpy arrays of float32
        embedding1 = np.array(embedding1, dtype=np.float32)
        embedding2 = np.array(embedding2, dtype=np.float32)

        # Compute cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)
    except Exception as e:
        logger.error(f"Error calculating cosine similarity: {str(e)}")
        return 0.0  # Return 0 similarity on error

def search_supabase_document_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.0001, document_filter=None):
    """
    Search for document chunks in Supabase directly using vector similarity with ANN optimization.
    Optimized for large document collections (10,000+ documents).

    Args:
        query_embedding: The query embedding vector
        query_text: The original query text for hybrid search
        use_hybrid_search: Whether to use hybrid search (combining vector and text)
        top_k: Number of results to return
        min_threshold: Minimum similarity threshold
        document_filter: Optional filter for specific documents

    Returns:
        List of document chunks with similarity scores
    """
    try:
        # Start with vector similarity search
        vector_results = supabase.rpc(
            "match_document_chunks",
            {
                "query_embedding": query_embedding,
                "match_threshold": min_threshold,
                "match_count": top_k * 2  # Fetch more than needed to allow for filtering
            }
        ).execute()

        if hasattr(vector_results, "data"):
            chunks = vector_results.data
        else:
            chunks = vector_results

        if not chunks or not isinstance(chunks, list):
            logger.warning("No vector search results for document chunks or unexpected format")
            return []
            
        # Ensure all chunks have source_type set to avoid warnings
        for chunk in chunks:
            if "source_type" not in chunk or not chunk["source_type"]:
                chunk["source_type"] = "document"
        
        # Ensure chunks are sorted by similarity score
        chunks = sorted(chunks, key=lambda x: x.get('similarity', 0), reverse=True)
        return chunks[:top_k]
        
    except Exception as e:
        logger.error(f"Error in search_supabase_document_chunks: {str(e)}")
        return []

def search_supabase_website_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.0001, website_filter=None):
    """
    Search for website chunks in Supabase directly using optimized ANN vector similarity.
    Optimized for scale with 100+ websites and thousands of chunks.
    This is only called if no document chunks are found.
    
    Args:
        query_embedding: The query embedding vector
        query_text: The original query text for hybrid search
        use_hybrid_search: Whether to use hybrid search (combining vector and text)
        top_k: Number of results to return
        min_threshold: Minimum similarity threshold
        website_filter: Optional filter for specific websites
        
    Returns:
        List of website chunks with similarity scores
    """
    try:
        # Start with vector similarity search for websites
        vector_results = supabase.rpc(
            "match_website_chunks",
            {
                "query_embedding": query_embedding,
                "match_threshold": min_threshold,
                "match_count": top_k * 2  # Fetch more than needed to allow for filtering
            }
        ).execute()
        
        if hasattr(vector_results, "data"):
            chunks = vector_results.data
        else:
            chunks = vector_results
            
        if not chunks or not isinstance(chunks, list):
            logger.warning("No vector search results for website chunks or unexpected format")
            return []
        
        # Ensure all chunks have source_type set to avoid warnings
        for chunk in chunks:
            if "source_type" not in chunk or not chunk["source_type"]:
                chunk["source_type"] = "website"
                
        # Ensure chunks are sorted by similarity score
        chunks = sorted(chunks, key=lambda x: x.get('similarity', 0), reverse=True)
        return chunks[:top_k]
        
    except Exception as e:
        logger.error(f"Error in search_supabase_website_chunks: {str(e)}")
        return []

def group_chunks_by_source(chunks):
    """
    Group chunks by source type into document and website categories.

    Args:
        chunks: List of chunks with source_type information

    Returns:
        Tuple of (document_chunks, website_chunks)
    """
    document_chunks = []
    website_chunks = []

    for chunk in chunks:
        source_type = chunk.get("source_type", "").lower()
        if source_type == "document":
            document_chunks.append(chunk)
        elif source_type == "website":
            website_chunks.append(chunk)

    return document_chunks, website_chunks

def generate_llm_answer(query: str, similar_chunks: List[Dict[str, Any]], system_prompt: str = None, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a coherent answer using the LLM router based on similar chunks.

    Args:
        query: The user's question
        similar_chunks: List of context chunks to use for generating the answer
        system_prompt: Custom system prompt to use (optional)
        model_id: LLM model to use (default: gemini-2.0-flash)
        extract_format: Preferred format for the extraction (paragraph, bullet, table)

    Returns:
        Tuple of (answer_text, source_data, document_sources, website_sources)
    """
    try:
        # Prepare context from similar chunks
        context_texts = []
        sources = []
        document_sources = []
        website_sources = []

        # Ensure all chunks have valid text
        valid_chunks = []
        for chunk in similar_chunks:
            # Ensure text field exists and is a string
            if "text" not in chunk or not isinstance(chunk["text"], str) or not chunk["text"].strip():
                # Log the issue and try to fix it
                logger.warning(f"Found chunk with missing or invalid text: {chunk.get('id', 'unknown')}")
                # Try to fix it with a default value
                chunk["text"] = chunk.get("text", "") or "No content available"

            # Ensure source_type is set - use a more aggressive approach to reduce warnings
            if "source_type" not in chunk or not chunk["source_type"]:
                if "filename" in chunk or "page" in chunk or "document_id" in chunk:
                    chunk["source_type"] = "document"
                    # Use less verbose logging to avoid flooding logs
                    if random.random() < 0.01:  # Only log 1% of these to reduce noise
                        logger.debug(f"Set source_type to 'document' for chunk {chunk.get('id', 'unknown')}")
                elif "url" in chunk or "website_id" in chunk:
                    chunk["source_type"] = "website"
                    if random.random() < 0.01:  # Only log 1% of these
                        logger.debug(f"Set source_type to 'website' for chunk {chunk.get('id', 'unknown')}")
                else:
                    chunk["source_type"] = "unknown"
                    if random.random() < 0.01:  # Only log 1% of these
                        logger.debug(f"Set source_type to 'unknown' for chunk {chunk.get('id', 'unknown')}")
            
            valid_chunks.append(chunk)

        # Use only valid chunks
        similar_chunks = valid_chunks

        # If no valid chunks, return early
        if not similar_chunks:
            logger.warning("No valid chunks found for LLM answer generation")
            return "I couldn't find any valid information to answer your question.", [], [], []

        # Log the content of chunks to help diagnose issues
        logger.info(f"Processing {len(similar_chunks)} chunks for answer generation")
        for i, chunk in enumerate(similar_chunks[:3]):  # Log first 3 chunks for debugging
            logger.info(f"Chunk {i}: source_type={chunk.get('source_type', 'unknown')}, similarity={chunk.get('similarity', 0):.3f}, text_length={len(chunk.get('text', ''))}, text_preview={chunk.get('text', '')[:100]}...")

        for chunk in similar_chunks:
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            # Add context with citation information
            if chunk.get("source_type") == "document":
                # Document source
                filename = chunk.get("filename", "Unknown document")
                page = chunk.get("page", 1)
                context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Add to sources list
                source = {
                    "source_type": "document",
                    "filename": filename,
                    "page": page,
                    # For UI display
                    "name": os.path.basename(filename),
                    "link": f"/documents/view/{filename}/{page}"
                }
                if source not in sources:
                    sources.append(source)
                if source not in document_sources:
                    document_sources.append(source)
            elif chunk.get("source_type") == "website":
                # Website source
                url = chunk.get("url", "Unknown website")
                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Add to sources list
                source = {
                    "source_type": "website",
                    "url": url
                }
                if source not in sources:
                    sources.append(source)
                if source not in website_sources:
                    website_sources.append(source)
            else:
                # Unknown source type
                logger.warning(f"Unknown source type for chunk: {chunk.get('id', 'unknown')}")
                context_texts.append(f"From unknown source (relevance: {similarity:.2f}):\n{chunk_text}\n")

        # Combine all context texts
        context = "\n\n".join(context_texts)
        logger.info(f"Created context with {len(context_texts)} context segments, total length: {len(context)} characters")
        
        # Token limit handling - approximately 4 characters per token for English text
        # 1M tokens is the absolute limit for Gemini, so we'll stay well under that
        MAX_MODEL_TOKENS = 1000000  # Gemini's maximum token limit
        SAFETY_FACTOR = 0.25  # Use only 75% of the maximum to leave room for response
        CHARS_PER_TOKEN = 4  # Approximate chars per token in English text
        
        max_context_chars = int((MAX_MODEL_TOKENS * SAFETY_FACTOR) * CHARS_PER_TOKEN)
        logger.info(f"Maximum context size: {max_context_chars} chars (~{int(MAX_MODEL_TOKENS * SAFETY_FACTOR)} tokens)")
        
        if len(context) > max_context_chars:
            logger.warning(f"Context too large ({len(context)} chars / ~{int(len(context)/CHARS_PER_TOKEN)} tokens), truncating to ~{max_context_chars} chars")
            
            # Sort chunks by similarity before truncating
            sorted_chunks = sorted(similar_chunks, key=lambda x: x.get('similarity', 0), reverse=True)
            
            # Start with highest similarity chunks
            new_context_texts = []
            new_context_length = 0
            preserved_sources = []
            preserved_doc_sources = []
            preserved_web_sources = []
            
            # First include top 5 highest similarity chunks regardless of size (to ensure best matches are included)
            top_chunks = sorted_chunks[:5]
            remaining_chunks = sorted_chunks[5:]
            
            # Add top chunks first
            for chunk in top_chunks:
                if "text" not in chunk or not chunk["text"]:  # Skip chunks with no text
                    continue
                    
                # Truncate extremely large chunks to a reasonable size
                if len(chunk.get("text", "")) > 10000:  # If chunk is > 10K chars
                    chunk["text"] = chunk["text"][:10000] + "...[truncated due to length]"
                    logger.info(f"Truncated very large chunk of {len(chunk.get('text', ''))} chars")
                
                chunk_text = f"From '{chunk.get('filename', chunk.get('url', 'Unknown'))}' (relevance: {chunk.get('similarity', 0):.2f}):\n{chunk.get('text', '')}\n"
                new_context_texts.append(chunk_text)
                new_context_length += len(chunk_text)
                
                # Track which sources we're keeping
                if chunk.get("source_type") == "document":
                    source = {
                        "source_type": "document",
                        "filename": chunk.get("filename", "Unknown document"),
                        "page": chunk.get("page", 1),
                        "name": os.path.basename(chunk.get("filename", "Unknown")),
                        "link": f"/documents/view/{chunk.get('filename', 'Unknown')}/{chunk.get('page', 1)}"
                    }
                    if source not in preserved_sources:
                        preserved_sources.append(source)
                    if source not in preserved_doc_sources:
                        preserved_doc_sources.append(source)
                elif chunk.get("source_type") == "website":
                    source = {
                        "source_type": "website",
                        "url": chunk.get("url", "Unknown website")
                    }
                    if source not in preserved_sources:
                        preserved_sources.append(source)
                    if source not in preserved_web_sources:
                        preserved_web_sources.append(source)
            
            # Now add remaining chunks up to the limit
            for chunk in remaining_chunks:
                if "text" not in chunk or not chunk["text"]:  # Skip chunks with no text
                    continue
                    
                # Truncate very large chunks 
                if len(chunk.get("text", "")) > 5000:  # More aggressive truncation for non-top chunks
                    chunk["text"] = chunk["text"][:5000] + "...[truncated due to length]"
                
                chunk_text = f"From '{chunk.get('filename', chunk.get('url', 'Unknown'))}' (relevance: {chunk.get('similarity', 0):.2f}):\n{chunk.get('text', '')}\n"
                
                # Check if adding this chunk would exceed our limit
                if new_context_length + len(chunk_text) < max_context_chars:
                    new_context_texts.append(chunk_text)
                    new_context_length += len(chunk_text)
                    
                    # Track which sources we're keeping
                    if chunk.get("source_type") == "document":
                        source = {
                            "source_type": "document",
                            "filename": chunk.get("filename", "Unknown document"),
                            "page": chunk.get("page", 1),
                            "name": os.path.basename(chunk.get("filename", "Unknown")),
                            "link": f"/documents/view/{chunk.get('filename', 'Unknown')}/{chunk.get('page', 1)}"
                        }
                        if source not in preserved_sources:
                            preserved_sources.append(source)
                        if source not in preserved_doc_sources:
                            preserved_doc_sources.append(source)
                    elif chunk.get("source_type") == "website":
                        source = {
                            "source_type": "website",
                            "url": chunk.get("url", "Unknown website")
                        }
                        if source not in preserved_sources:
                            preserved_sources.append(source)
                        if source not in preserved_web_sources:
                            preserved_web_sources.append(source)
                else:
                    # We've reached our limit
                    break
            
            # Replace the original collections with our trimmed versions
            context = "\n\n".join(new_context_texts)
            sources = preserved_sources
            document_sources = preserved_doc_sources
            website_sources = preserved_web_sources
            
            logger.info(f"Truncated context to {len(context)} chars (~{int(len(context)/CHARS_PER_TOKEN)} tokens), preserving {len(new_context_texts)} highest relevance chunks")
        
        if not context.strip():
            logger.warning("Combined context is empty after processing chunks")
            return "I don't have information to answer that question.", [], [], []

        # Default system prompt if none provided
        if not system_prompt:
            system_prompt = f"""
You are RailGPT, an expert information retrieval assistant specializing in Indian Railways.

CRITICAL INSTRUCTIONS:
1. You MUST ONLY use the information provided in the context below to answer the question.
2. EXAMINE all provided context CAREFULLY and EXTRACT relevant information to answer the question.
3. If the context contains DOCUMENT sources, you MUST use those to form your answer, even if the similarity score is 0.0 or the content seems only tangentially related.
4. If the context contains WEBSITE sources but no DOCUMENT sources, use the website information, even if the similarity score is 0.0 or the content seems only tangentially related.
5. You MUST include source references for ALL information you provide (document names, pages, website URLs).
6. If the context DOES NOT contain relevant information to answer the question CLEARLY STATE: "I couldn't find any valid information to answer your question." DO NOT make up an answer.
7. DO NOT use your general knowledge under any circumstances.
8. Format your response in a clear, readable manner with proper paragraphs and bullet points where appropriate.
9. IMPORTANT: Even if the information in the context seems incomplete or only partially relevant, you MUST use it and not fall back to general knowledge.

The context information below contains actual document and website content that has been retrieved based on the user's query.
DO NOT say things like "Based on the context provided" or "According to the information given" - just provide the answer directly with references.

CONTEXT:
{context}
"""

            # Additional instructions
            system_prompt += """
NEVER use your general knowledge to answer the question.
ONLY use the information provided in the context.
If the context doesn't contain enough information, say "Based on the available document information..." and then answer with what you can find in the context.
If you're unsure about the answer based on the context, state what you can determine from the context and indicate that the information may be limited.
"""

        # Add format preference to system prompt
        if extract_format == "bullet":
            system_prompt += "\nStructure your answer using bullet points where appropriate for better readability."
        elif extract_format == "table":
            system_prompt += "\nIf the answer contains tabular data, format it as a markdown table for better readability."
        else: # paragraph (default)
            system_prompt += "\nStructure your answer in clear paragraphs for better readability."

        # Use the LLM router to generate the answer
        try:
            # Generate the answer with the LLM
            answer = llm_router.generate_answer(query, context, system_prompt, model_id)

            # Process the results based on the source types
            # If there are only document sources, only return document answer
            if document_sources and not website_sources:
                return answer, sources, document_sources, []
            # If there are only website sources, only return website answer
            elif website_sources and not document_sources:
                return answer, sources, [], website_sources
            # If there are both document and website sources, or no sources
            else:
                return answer, sources, document_sources, website_sources

        except Exception as e:
            logger.error(f"Error generating LLM answer: {str(e)}")
            # Fallback to a simpler prompt
            try:
                fallback_answer = llm_router.generate_answer(
                    query=query,
                    context="",  # Empty context for fallback
                    system_prompt="You are an AI assistant. Answer this question to the best of your ability: " + query,
                    model_id=model_id,
                    use_documents_only=False  # Allow general knowledge for fallback
                )
                return fallback_answer, [], [], []
            except Exception as fallback_err:
                logger.error(f"Fallback also failed: {str(fallback_err)}")
                return "I'm sorry, I couldn't process your question. Please try again.", [], [], []

    except Exception as e:
        # Catch-all for any other errors
        logger.error(f"Unexpected error in generate_llm_answer with {model_id}: {str(e)}")
        return f"I encountered an error while trying to generate an answer: {str(e)}", [], [], []

def load_documents(data_dir: str = './data'):
    """Load and process all supported documents from a directory."""
    global DOCUMENT_CHUNKS
    DOCUMENT_CHUNKS = []
    total_chunks = 0

    logger.info(f"Processing documents from {data_dir}")
    
    # Create data directory if it doesn't exist
    os.makedirs(data_dir, exist_ok=True)
    
    # Define supported document types
    supported_extensions = ('.pdf', '.txt', '.docx', '.doc', '.rtf')
    
    # Get list of files in data directory
    document_files = []
    for ext in supported_extensions:
        files = [f for f in os.listdir(data_dir) if f.lower().endswith(ext)]
        document_files.extend(files)
    
    logger.info(f"Found {len(document_files)} supported document files")
    
    # Process each document file
    for filename in document_files:
        try:
            file_path = os.path.join(data_dir, filename)
            if not os.path.isfile(file_path):
                continue
                
            logger.info(f"Processing {file_path}")
            
            # Check if document already exists in Supabase
            check_query = f"SELECT * FROM documents WHERE display_name = '{filename.replace("'", "''")}'"
            existing_docs = supabase.execute_query(check_query)
            
            if isinstance(existing_docs, list) and len(existing_docs) > 0:
                logger.info(f"Document {filename} already exists in Supabase, skipping processing")
                continue
                
            # Process the document
            try:
                doc_chunks = extract_document(file_path)
                if doc_chunks:
                    DOCUMENT_CHUNKS.extend(doc_chunks)
                    total_chunks += len(doc_chunks)
                    logger.info(f"Added {len(doc_chunks)} chunks from {filename}")
                else:
                    logger.warning(f"No chunks extracted from {filename}")
            except Exception as extract_error:
                logger.error(f"Error extracting content from {filename}: {str(extract_error)}")
                
        except Exception as e:
            logger.error(f"Error processing {filename}: {str(e)}")
    
    # Load chunks from Supabase too
    try:
        chunks_query = """
        SELECT 
            dc.id, 
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            d.display_name as filename,
            'document' as source_type
        FROM document_chunks dc
        JOIN documents d ON dc.document_id = d.id
        LIMIT 500
        """
        
        chunks = supabase.execute_query(chunks_query)
        if not isinstance(chunks, dict) and len(chunks) > 0:
            for chunk in chunks:
                if chunk not in DOCUMENT_CHUNKS:
                    DOCUMENT_CHUNKS.append(chunk)
            logger.info(f"Loaded {len(chunks)} document chunks from Supabase")
    except Exception as db_error:
        logger.error(f"Error loading chunks from Supabase: {str(db_error)}")
    
    logger.info(f"Finished processing. Total chunks created: {len(DOCUMENT_CHUNKS)}")
    return DOCUMENT_CHUNKS


            loaded_count = 0
            for chunk in result:
                # Set source type if not present
                if "source_type" not in chunk:
                    chunk["source_type"] = "document"

                # Set similarity to prioritize document sources
                chunk["similarity"] = 0.85

                # Add to global chunks list
                if not any(c.get('id') == chunk.get('id') for c in DOCUMENT_CHUNKS):
                    DOCUMENT_CHUNKS.append(chunk)
                    loaded_count += 1

        file_path = os.path.join(data_dir, filename)
        
        # Skip if not a file or not a supported file type
        if not os.path.isfile(file_path) or not filename.lower().endswith(supported_extensions):
            continue
            
        logger.info(f"Processing document: {filename}")
        
        try:
            # Check if document already exists in Supabase
            check_query = f"""
            SELECT id, last_modified FROM documents 
            WHERE display_name = '{filename.replace("'", "''")}'
            """
            
            doc_exists = supabase.execute_query(check_query)
            current_mtime = os.path.getmtime(file_path)
            
            # If document exists, check if it needs updating
            if isinstance(doc_exists, list) and len(doc_exists) > 0:
                doc_record = doc_exists[0]
                db_modified = doc_record.get('last_modified')
                
                if db_modified and float(db_modified) >= current_mtime:
                    logger.info(f"Document {filename} is already up-to-date in database")
                    continue
                else:
                    # Document has been modified, delete old version and reprocess
                    doc_id = doc_record.get('id')
                    if doc_id:
                        logger.info(f"Document {filename} has been modified, removing old version")
                        # Delete old document and all its chunks
                        supabase.execute_query(f"DELETE FROM document_chunks WHERE document_id = '{doc_id}'")
                        supabase.execute_query(f"DELETE FROM documents WHERE id = '{doc_id}'")
                    else:
                        logger.warning(f"Could not find document ID for {filename}")
            
            # Document is either new or needs updating
        except Exception as e:
            logger.warning(f"Error checking if document exists: {str(e)}")
            # Continue anyway to ensure documents are loaded

        try:
            # Extract document using our extraction utility
            logger.info(f"Extracting document: {file_path}")
            document_chunks = extract_document(file_path)

            if not document_chunks:
                logger.warning(f"No chunks extracted from {filename}")
                continue

            # Generate embeddings for each chunk
            for chunk in document_chunks:
                # Add source_type metadata
                chunk["source_type"] = "document"

                # Generate and add embedding
                chunk_text = chunk.get("text", "")
                embedding = generate_embedding(chunk_text)
                chunk["embedding"] = embedding

            # Add to global document chunks
            DOCUMENT_CHUNKS.extend(document_chunks)

            # Add to vector database
            vector_db.add_chunks(document_chunks)

            logger.info(f"Processed {filename}: extracted {len(document_chunks)} chunks")

        except Exception as e:
            logger.error(f"Error processing {filename}: {str(e)}")
            continue  # Continue with next file even if there's an error

    # Final summary after processing all files
    logger.info(f"Finished processing. Total chunks created: {len(DOCUMENT_CHUNKS)}")
    return DOCUMENT_CHUNKS

@app.on_event("startup")
async def startup_event():
    """Load documents on server startup and check Supabase configuration."""
    logger.info("Starting backend server")
    
    try:
        # Load documents from the data directory
        logger.info("Loading documents on startup")
        documents_loaded = load_documents()
        logger.info(f"Loaded {len(documents_loaded)} document chunks on startup")
        
        # Test Supabase connection
        logger.info("Testing Supabase connection...")
        test_query = "SELECT 1 as test..."
        logger.info(f"Query not directly supported via REST API: {test_query}")
        result = supabase.execute_query(test_query)
        if not isinstance(result, dict) or "error" not in result:
            logger.info("Supabase connection test successful")
        else:
            logger.error(f"Supabase connection test failed: {result.get('error')}")
            
        # Check Supabase storage buckets
        logger.info("Checking Supabase storage setup...")
        supabase.create_or_get_bucket("documents")
        logger.info("Successfully accessed/created bucket: documents")
        supabase.create_or_get_bucket("websites")
        supabase.create_or_get_bucket("images")
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        import traceback
        logger.error(f"Startup error details: {traceback.format_exc()}")

    # Log how many chunks were loaded
    logger.info(f"Loaded {len(DOCUMENT_CHUNKS)} document chunks on startup")

    # Check Supabase configuration
    try:
        # Check if Supabase is configured
        if os.getenv("USE_SUPABASE", "true").lower() == "true":
            # Test Supabase connection
            result = supabase.execute_query("SELECT 1 as test")
            if "error" in result:
                logger.error(f"Supabase connection test failed: {result['error']}")
            else:
                logger.info("Supabase connection test successful")

                # Skip schema checks and directly try to create/access buckets
                try:
                    logger.info("Checking Supabase storage setup...")

                    # Directly try to create the documents bucket
                    # This will either succeed or fail gracefully with a mock response
                    bucket_result = supabase.create_bucket_if_not_exists("documents")

                    if "error" in bucket_result:
                        logger.info(f"Using mock storage bucket for 'documents': {bucket_result.get('id')}")
                    else:
                        logger.info(f"Successfully accessed/created bucket: {bucket_result.get('id')}")

                    # Create additional buckets if needed
                    supabase.create_bucket_if_not_exists("websites", is_public=True)
                    supabase.create_bucket_if_not_exists("images", is_public=True)
                except Exception as e:
                    logger.warning(f"Error setting up Supabase storage: {str(e)}")
                    logger.info("Will use mock storage functionality when needed.")
    except Exception as e:
        logger.error(f"Error checking Supabase configuration: {str(e)}")

# Root endpoint
@app.get("/")
async def read_root():
    return {"message": "Document Management System API is running"}

# Health check endpoint
@app.get("/api/health")
async def health_check():
    return {"status": "ok", "message": "API is running"}

# Clear database endpoint (for testing only)
@app.post("/api/clear-database")
async def clear_database():
    """Clear all data from the database (for testing purposes)"""
    try:
        result = supabase.clear_all_data()
        return {"message": "Database cleared successfully", "result": result}
    except Exception as e:
        logger.error(f"Error clearing database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing database: {str(e)}")

# Get all documents endpoint
@app.get("/api/documents")
async def get_documents():
    # This would normally query a database, but for now, we'll generate mock data
    documents = [
        {
            "id": "doc-1746815214001",
            "name": "Indian Railway Safety Guidelines.pdf",
            "uploadedAt": "2025-05-08T10:15:14.001Z",
            "mainCategory": "Safety",
            "category": "Guidelines",
            "subCategory": "Operations",
            "minorCategory": "Personnel",
            "status": "Extracted",
            "fileType": "pdf",
            "qualityScore": 92,
            "uploadedBy": "Admin"
        },
        {
            "id": "doc-1746815214002",
            "name": "Maintenance Schedule 2025.docx",
            "uploadedAt": "2025-05-07T14:22:33.002Z",
            "mainCategory": "Maintenance",
            "category": "Schedules",
            "status": "Extracted",
            "fileType": "docx",
            "qualityScore": 88,
            "uploadedBy": "Admin"
        },
        {
            "id": "doc-1746815214003",
            "name": "FSDS Isolation Identification System.docx",
            "uploadedAt": "2025-05-09T23:56:52.003Z",
            "mainCategory": "Technical",
            "category": "Systems",
            "subCategory": "FSDS",
            "status": "Extracted",
            "fileType": "docx",
            "qualityScore": 95,
            "uploadedBy": "User"
        }
    ]

    return documents

# Get all websites endpoint
@app.get("/api/websites")
async def get_websites():
    # Mock data for websites
    websites = [
        {
            "id": "web-1746815314001",
            "name": "Indian Railways Official Portal",
            "url": "https://indianrailways.gov.in",
            "addedAt": "2025-05-05T09:12:14.001Z",
            "category": "Official",
            "status": "Processed",
            "qualityScore": 90,
            "addedBy": "Admin"
        },
        {
            "id": "web-1746815314002",
            "name": "IRCTC Booking Portal",
            "url": "https://irctc.co.in",
            "addedAt": "2025-05-06T11:32:45.002Z",
            "category": "Services",
            "status": "Processed",
            "qualityScore": 85,
            "addedBy": "Admin"
        }
    ]

    return websites

# Chunks endpoint
@app.get("/api/chunks")
async def get_chunks():
    # Return document chunks without embeddings for response size efficiency
    chunks_without_embeddings = []
    for chunk in DOCUMENT_CHUNKS:
        chunk_copy = dict(chunk)
        if "embedding" in chunk_copy:
            del chunk_copy["embedding"]
        chunks_without_embeddings.append(chunk_copy)

    return chunks_without_embeddings

@app.post("/api/query", response_model=QueryResponse)
async def query(request: QueryRequest):
    logger.info(f"Processing query: {request.query} using model: {request.model}")

    # Initialize variables
    used_fallback_model = False
    original_model = request.model
    fallback_reason = None
    document_answer = None
    website_answer = None
    combined_answer = ""
    combined_sources = []
    document_sources = []
    website_sources = []
    llm_fallback_used = False
    model_id = request.model
    answer_source = "Unknown"
    document_chunks = []  # Initialize document_chunks here to avoid undefined variable issues
    website_chunks = []  # Initialize website_chunks here to avoid undefined variable issues

    try:
        # Validate the requested model and API key (only needed for fallback)
        if not llm_router.is_model_available(request.model):
            logger.warning(f"Model {request.model} not available, falling back to default model")
            model_id = llm_router.DEFAULT_MODEL
            used_fallback_model = True
            fallback_reason = f"Model '{request.model}' is not available"
        else:
            # Check if API key is valid for this model
            model_config = llm_router.get_model_config(model_id)
            key_status = llm_router.check_api_key_validity(model_config["provider"])
            if not key_status.get("valid", False):
                logger.warning(f"Invalid API key for {model_id}, falling back to default model")
                model_id = llm_router.DEFAULT_MODEL
                used_fallback_model = True
                fallback_reason = f"API key for {original_model} is invalid: {key_status.get('message', '')}"

        # Using a function-based timeout approach instead of signals (which don't work on Windows)
        # We'll rely on the timeout mechanisms in the LLM router and API calls

        # Generate embedding for the query text using the selected model
        try:
            query_embedding = generate_embedding(request.query, model_id)
        except Exception as e:
            logger.error(f"Error generating embedding with {model_id}: {str(e)}")
            # Try with default model for embedding if not already using it
            if model_id != llm_router.DEFAULT_MODEL:
                logger.info(f"Falling back to {llm_router.DEFAULT_MODEL} for embedding generation")
                query_embedding = generate_embedding(request.query, llm_router.DEFAULT_MODEL)
            else:
                # If even default model fails for embedding, raise the exception
                raise

        logger.info("=== IMPLEMENTING CASCADING SEARCH STRATEGY ===")
        # STEP 1: First check for document chunks in Supabase - always search documents first
        logger.info(f"STEP 1: Searching for document chunks with query: '{request.query}'")

        # Try direct document search first (by title and content)
        direct_document_chunks = []

        # Search for documents by title using key terms from the query
        # Extract key terms by removing common words and punctuation
        import re
        from string import punctuation

        # Clean the query and extract key terms
        clean_query = re.sub(r'[' + punctuation + ']', ' ', request.query.lower())
        words = clean_query.split()
        stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
        key_terms = [word for word in words if word not in stop_words and len(word) > 2]

        # If no key terms found, use the original query
        if not key_terms:
            key_terms = [request.query]

        logger.info(f"Extracted key terms for title search: {key_terms}")

        # Search for each key term
        title_results = []
        for term in key_terms:
            results = search_documents_by_title(term)
            title_results.extend(results)

        # Remove duplicates
        unique_ids = set()
        unique_title_results = []
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id and doc_id not in unique_ids:
                unique_ids.add(doc_id)
                unique_title_results.append(doc)

        title_results = unique_title_results
        logger.info(f"Found {len(title_results)} documents by title search")

        # Get chunks for each document found by title
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id:
                chunks = get_document_chunks(doc_id)
                direct_document_chunks.extend(chunks)

        # Search for documents by content
        content_results = search_documents_by_content(request.query)
        direct_document_chunks.extend(content_results)

        # If direct search found results, use those
        if direct_document_chunks:
            logger.info(f"Found {len(direct_document_chunks)} document chunks using direct search")
            document_chunks = direct_document_chunks
        else:
            document_chunks = []
        # Try vector search for documents
        try:
            document_chunks = search_supabase_document_chunks(
                query_embedding=query_embedding,
                query_text=request.query,
                use_hybrid_search=request.use_hybrid_search,
                top_k=30,
                min_threshold=0.0001  # Lower threshold to find more document results
            )
            # If no vector search results, use direct document results (if any)
            if not document_chunks and isinstance(document_chunks, list) and direct_document_chunks:
                document_chunks = direct_document_chunks
                logger.info(f"No vector results, using {len(direct_document_chunks)} direct document results")
        except Exception as e:
            logger.error(f"Error in vector search for documents: {str(e)}")
            # Fall back to direct search results
            document_chunks = direct_document_chunks
        
        # IMPORTANT FIX: For newly uploaded documents, ensure we prioritize them
        recent_docs_query = f"""
        SELECT DISTINCT document_id FROM document_chunks dc
        JOIN documents d ON dc.document_id = d.id
        ORDER BY d.created_at DESC
        LIMIT 3
        """
        
        recent_docs = supabase.execute_query(recent_docs_query)
        recent_doc_ids = []
        
        if not isinstance(recent_docs, dict) and len(recent_docs) > 0:
            recent_doc_ids = [doc.get('document_id') for doc in recent_docs]
            logger.info(f"Found {len(recent_doc_ids)} recent document IDs: {recent_doc_ids}")
            
            # Get chunks from recent documents
            for doc_id in recent_doc_ids:
                recent_chunks_query = f"""
                SELECT 
                    dc.id, dc.document_id, dc.chunk_index, dc.page_number, dc.text,
                    d.display_name as filename, d.file_path as url, 'document' as source_type,
                    0.8 as similarity
                FROM document_chunks dc
                JOIN documents d ON dc.document_id = d.id
                WHERE dc.document_id = '{doc_id}'
                LIMIT 10
                """
                
                recent_chunks = supabase.execute_query(recent_chunks_query)
                if not isinstance(recent_chunks, dict) and len(recent_chunks) > 0:
                    document_chunks.extend(recent_chunks)
                    logger.info(f"Added {len(recent_chunks)} chunks from recent document {doc_id}")
        
        # Count the number of document chunks with acceptable similarity (for relevance check)
        relevant_document_count = 0
        for chunk in document_chunks:
            similarity = chunk.get('similarity', 0)
            if similarity >= 0.001:  # Significantly lowered threshold for detecting relevant chunks
                relevant_document_count += 1
                
        # Check if we found any document chunks
        if document_chunks and relevant_document_count > 0:
            # Log the top match score to debug threshold issues
            top_score = document_chunks[0].get('similarity', 0) if document_chunks else 0
            logger.info(f"Top document chunk match score: {top_score:.6f}")
            logger.info(f"Found {relevant_document_count} relevant document chunks (similarity >= 0.01)")
            
            # Log the top 3 document chunks
            for i, chunk in enumerate(document_chunks[:3]):
                similarity = chunk.get('similarity', 0)
                text_snippet = chunk.get('text', '')[:100] + '...' if chunk.get('text') else '...' 
                logger.info(f"Document chunk {i+1}: similarity={similarity:.6f}")
                logger.info(f"Text snippet: {text_snippet}")
            
            # Mark document chunks as found only if we have chunks with relevant similarity
            has_document_results = True
        else:
            # No relevant document chunks found
            has_document_results = False
            logger.warning(f"No relevant document chunks found (searched {len(document_chunks)} chunks). Moving to website search.")

        # STEP 2: ONLY if no document chunks found, check website chunks
        website_chunks = []
        if not has_document_results:
            logger.info(f"STEP 2: No document chunks found, searching for website chunks with query: '{request.query}'")

            # Try direct website search first
            direct_website_chunks = []

            # Search for websites by content using the same key terms
            try:
                # Use the same key terms from the document search
                logger.info(f"Searching for website content with key terms: {key_terms}")

                # Construct a single query with all key terms for better results
                sanitized_terms = [term.replace("'", "''") for term in key_terms]
                all_terms = " | ".join(sanitized_terms)  # OR condition between terms
                
                # Use the full query string too for exact matching
                sanitized_full_query = request.query.replace("'", "''")
                
                # More comprehensive website query that balances precision and recall
                website_query = f"""
                SELECT
                    wc.id,
                    wc.website_id,
                    wc.chunk_index,
                    wc.text,
                    wc.url,
                    w.name as website_name,
                    'website' as source_type,
                    GREATEST(
                        ts_rank(to_tsvector('english', wc.text), to_tsquery('english', '{all_terms}')),
                        CASE WHEN wc.text ILIKE '%{sanitized_full_query}%' THEN 1.0 ELSE 0.0 END
                    ) AS similarity
                FROM
                    website_chunks wc
                JOIN
                    websites w ON wc.website_id = w.id
                WHERE
                    to_tsvector('english', wc.text) @@ to_tsquery('english', '{all_terms}')
                    OR wc.text ILIKE '%{sanitized_full_query}%'
                ORDER BY
                    similarity DESC
                LIMIT 15
                """

                result = supabase.execute_query(website_query)

                if isinstance(result, dict) and "error" in result:
                    logger.error(f"Error searching websites by content: {result['error']}")
                else:
                    logger.info(f"Found {len(result)} website chunks matching search terms")
                    # Add source_type to ensure it's properly set
                    for chunk in result:
                        if "source_type" not in chunk or not chunk["source_type"]:
                            chunk["source_type"] = "website"
                    direct_website_chunks.extend(result)
            except Exception as e:
                logger.error(f"Error in direct website search: {str(e)}")

            # If direct search found results, use those
            if direct_website_chunks:
                logger.info(f"Found {len(direct_website_chunks)} website chunks using direct search")
                website_chunks = direct_website_chunks
            else:
                try:
                    website_chunks = []
                    try:
                        # First try vector search for website chunks
                        website_chunks = search_supabase_website_chunks(
                            query_embedding=query_embedding,
                            query_text=request.query,
                            use_hybrid_search=request.use_hybrid_search,
                            top_k=50,
                            min_threshold=0.00001
                        )
                        
                        # If no results from vector search, try text-based search
                        if not website_chunks or len(website_chunks) == 0:
                            logger.warning(f"No vector matches found for websites, trying text search for: {request.query}")
                            
                            # Extract key terms for better search
                            key_terms = set(request.query.lower().split())
                            key_terms.discard('what')
                            key_terms.discard('is')
                            key_terms.discard('are')
                            key_terms.discard('the')
                            key_terms.discard('a')
                            key_terms.discard('an')
                            
                            # Build OR conditions for each term
                            search_conditions = []
                            for term in key_terms:
                                safe_term = term.replace("'", "''")
                                search_conditions.append(f"text ILIKE '%{safe_term}%'")
                            
                            # Query recent website chunks with text matching
                            if search_conditions:
                                text_query = f"""
                                SELECT
                                    wc.id,
                                    wc.website_id,
                                    wc.chunk_index,
                                    wc.text,
                                    wc.url,
                                    w.name as website_name,
                                    'website' as source_type,
                                    0.5 as similarity
                                FROM
                                    website_chunks wc
                                JOIN
                                    websites w ON wc.website_id = w.id
                                WHERE
                                    {' OR '.join(search_conditions)}
                                ORDER BY
                                    w.created_at DESC
                                LIMIT 20
                                """
                                
                                text_results = supabase.execute_query(text_query)
                                if not isinstance(text_results, dict) or "error" not in text_results:
                                    website_chunks = text_results
                                    logger.info(f"Found {len(website_chunks)} website chunks using text search")
                    
                        # If still no results, get most recent chunks as last resort
                        if not website_chunks or len(website_chunks) == 0:
                            logger.warning("No matches found, retrieving most recent website chunks")
                            recent_query = """
                            SELECT
                                wc.id,
                                wc.website_id,
                                wc.chunk_index,
                                wc.text,
                                wc.url,
                                w.name as website_name,
                                'website' as source_type,
                                0.1 as similarity
                            FROM
                                website_chunks wc
                            JOIN
                                websites w ON wc.website_id = w.id
                            ORDER BY
                                w.created_at DESC, wc.chunk_index ASC
                            LIMIT 10
                            """
                            
                            recent_results = supabase.execute_query(recent_query)
                            if not isinstance(recent_results, dict) or "error" not in recent_results:
                                website_chunks = recent_results
                                logger.info(f"Found {len(website_chunks)} recent website chunks")
                                
                    except Exception as e:
                        logger.error(f"Error searching website chunks: {str(e)}")
                        website_chunks = []
                        
                    # Log website search results
                    logger.info(f"Found {len(website_chunks)} website chunks using search methods (min threshold 0.00001)")
                        
                    # Ensure all chunks have source_type set
                    for chunk in website_chunks:
                        if "source_type" not in chunk or not chunk["source_type"]:
                            chunk["source_type"] = "website"
                for chunk in website_chunks:
                    if "source_type" not in chunk or not chunk["source_type"]:
                        chunk["source_type"] = "website"

            # Make one final attempt to get ANY website chunks if nothing was found
            if not website_chunks or len(website_chunks) == 0:
                try:
                    # Last resort: Get ALL website chunks, even with no relevance
                    emergency_query = """
                    SELECT 
                        wc.id,
                        wc.website_id,
                        wc.chunk_index,
                        wc.text,
                        wc.url,
                        w.name as website_name,
                        'website' as source_type,
                        0.05 as similarity
                    FROM
                        website_chunks wc
                    JOIN
                        websites w ON wc.website_id = w.id
                    LIMIT 10
                    """
                    emergency_result = supabase.execute_query(emergency_query)
                    if not isinstance(emergency_result, dict) and len(emergency_result) > 0:
                        website_chunks = emergency_result
                        logger.warning(f"EMERGENCY FALLBACK: Retrieved {len(website_chunks)} website chunks as last resort")
                except Exception as e:
                    logger.error(f"Error in emergency website lookup: {str(e)}")
            
            # Count website chunks with meaningful similarity
            relevant_website_count = 0
            for chunk in website_chunks:
                if chunk.get('similarity', 0) > 0.001:  # Very low bar for website relevance
                    relevant_website_count += 1
                    
            if website_chunks:
                # Log the top match score to debug threshold issues
                top_score = website_chunks[0].get('similarity', 0) if website_chunks else 0
                logger.info(f"Top website chunk match score: {top_score:.6f}")
                logger.info(f"WEBSITE SEARCH RESULTS: Found {len(website_chunks)} total chunks, {relevant_website_count} with similarity > 0.001")
                
                # Log the top 3 website chunks
                for i, chunk in enumerate(website_chunks[:3]):
                    similarity = chunk.get('similarity', 0)
                    text_snippet = chunk.get('text', '')[:100] + '...' if chunk.get('text') else '...' 
                    logger.info(f"Website chunk {i+1}: similarity={similarity:.6f}")
                    logger.info(f"Text snippet: {text_snippet}")
                
                # Mark website chunks as found - ALWAYS set this to true if we have ANY chunks
                has_website_results = True
                logger.info("STEP 2 SUCCESS: Website chunks found and will be used for answer generation")
            else:
                # No website chunks found
                has_website_results = False
                logger.warning("CRITICAL: No website chunks found in all attempts. Moving to LLM fallback.")
        else:
            # Skip website search since we have document results
            has_website_results = False
            logger.info("STEP 2: Skipping website search since document chunks were found.")

        # Initialize similar_chunks list and prepare for decision logic
        similar_chunks = []  # Initialize empty list
        
        # Debug logs to understand current state
        logger.info(f"DECISION POINT: has_document_results={has_document_results}, doc_chunks={len(document_chunks)}, has_website_results={has_website_results}, website_chunks={len(website_chunks) if website_chunks else 0}")
        
        # Implement the strict cascading search strategy
        # STEP 1: If we have RELEVANT document chunks, use ONLY those (highest priority)
        if has_document_results and document_chunks:
            similar_chunks = document_chunks
            logger.info("DECISION: STEP 1 ACTIVE - Using document chunks for answer generation")
            answer_source = "Document"

            # Log the document chunks being used
            for i, chunk in enumerate(document_chunks[:3]):
                similarity = chunk.get('similarity', 0)
                text_snippet = chunk.get('text', '')[:100] + '...' if chunk.get('text') else '...' 
                logger.info(f"Using document chunk {i+1}: similarity={similarity:.6f}")
                logger.info(f"Text snippet: {text_snippet}")

        # STEP 2: ONLY if no RELEVANT document chunks found, use website chunks (second priority)
        elif not has_document_results and website_chunks and len(website_chunks) > 0:
            similar_chunks = website_chunks
            logger.info("DECISION: STEP 2 ACTIVE - Using website chunks for answer generation (no relevant document chunks found)")
            answer_source = "Website"

            # Log the website chunks being used
            for i, chunk in enumerate(website_chunks[:3]):
                similarity = chunk.get('similarity', 0)
                text_snippet = chunk.get('text', '')[:100] + '...' if chunk.get('text') else '...' 
                logger.info(f"Using website chunk {i+1}: similarity={similarity:.6f}")
                logger.info(f"Text snippet: {text_snippet}")

        # STEP 3: ONLY if both document and website searches yield no results, fall back to LLM
        # Only use LLM fallback as a last resort per requirements
        elif request.fallback_enabled:
            logger.info("STEP 3: No document or website chunks found, using LLM fallback as last resort")

            # Log this as a warning to help diagnose issues
            logger.warning(f"IMPORTANT: LLM fallback used for query: '{request.query}'. This indicates no document or website content was found.")
            logger.warning("Consider adding more documents or website content to the database to improve search results.")

            llm_fallback_used = True
            similar_chunks = [] # Keep empty as we'll use LLM without context

            # Since we're using pure LLM fallback without chunks, set the answer source appropriately
            answer_source = f"{model_id} (Fallback)"
            
            # CRITICAL FIX: Generate answer directly from LLM without context when no document/website results
            try:
                # Generate answer directly from LLM with modified system prompt that allows general knowledge
                fallback_system_prompt = f"""
                You are RailGPT, a helpful AI assistant specializing in Indian Railways.
                
                INSTRUCTIONS:
                - You are REQUIRED to use your general knowledge to answer this question.
                - No document or website information was found, so use your built-in knowledge.
                - Give a detailed, accurate, and helpful response about {request.query}.
                - Format your response clearly with proper markdown where appropriate.
                - Be factual and cite sources if possible.
                - If you don't know the answer, be honest and say so.
                """
                
                # Log that we're using general knowledge fallback
                logger.warning(f"Using general knowledge with model {model_id} to answer: '{request.query}'")
                
                # Use the LLM router to generate answer with general knowledge
                try:
                    # First try with the user's selected model
                    fallback_answer = llm_router.generate_answer(
                        query=request.query, 
                        context="",  # Empty context - use general knowledge
                        system_prompt=fallback_system_prompt,
                        model_id=model_id,
                        use_documents_only=False  # IMPORTANT: Allow general knowledge
                    )
                except Exception as model_error:
                    # If the selected model fails, try Gemini as final fallback
                    logger.error(f"Error with {model_id}, falling back to gemini-2.0-flash: {str(model_error)}")
                    fallback_answer = llm_router.generate_answer(
                        query=request.query,
                        context="",
                        system_prompt=fallback_system_prompt,
                        model_id="gemini-2.0-flash",
                        use_documents_only=False
                    )
                
                # Return the answer with a fallback prefix
                return QueryResponse(
                    answer=f"[Answer from {model_id} general knowledge] {fallback_answer}",
                    sources=[],
                    document_sources=[],
                    website_sources=[],
                    llm_model=model_id,
                    llm_fallback=True
                )
            except Exception as e:
                logger.error(f"Error in LLM fallback: {str(e)}")
                # Continue with normal flow if fallback fails

        # If no similar chunks and fallback not enabled, return simple message
        if not similar_chunks and not request.fallback_enabled and request.context_mode != "none":
            return QueryResponse(
                answer="I couldn't find any information meeting the similarity thresholds. Please try rephrasing your question or enable the 'Search with AI' option.",
                sources=[],
                document_sources=[],
                website_sources=[],
                llm_model=model_id,
                llm_fallback=False
            )

        # Process the query and generate an answer
        try:
            if similar_chunks:  # We have relevant context
                # Generate answer from context sources
                combined_answer, combined_sources, doc_sources, web_sources = generate_llm_answer(
                    query=request.query,
                    similar_chunks=similar_chunks,
                    model_id=model_id,
                    extract_format=request.extract_format
                )

                # Store sources appropriately
                document_sources = doc_sources if doc_sources else []
                website_sources = web_sources if web_sources else []

                # Check if the answer indicates no information was found
                is_no_info_answer = (
                    "I couldn't find information about this specific topic" in combined_answer or
                    "I don't have information about" in combined_answer or
                    "No information found" in combined_answer or
                    "I don't have specific information" in combined_answer or
                    "I don't have enough information" in combined_answer
                )

                # Prepend the source type to the answer with more specific information
                if not is_no_info_answer:
                    if answer_source == "Document":
                        # Include specific document name if available
                        if document_sources and len(document_sources) > 0:
                            doc_name = document_sources[0].get("name", "Unknown Document")
                            combined_answer = f"[Answer from Document: {doc_name}] {combined_answer}"
                        else:
                            combined_answer = f"[Answer from Document] {combined_answer}"
                    elif answer_source == "Website":
                        # Include specific website URL if available (truncated for display)
                        if website_sources and len(website_sources) > 0:
                            website_url = website_sources[0].get("url", "Unknown Website")
                            # Truncate URL if too long
                            if len(website_url) > 40:
                                display_url = website_url[:37] + "..."
                            else:
                                display_url = website_url
                            combined_answer = f"[Answer from Website: {display_url}] {combined_answer}"
                        else:
                            combined_answer = f"[Answer from Website] {combined_answer}"
                    else:
                        # For LLM fallback or unknown sources
                        combined_answer = f"[Answer from {model_id} (Fallback)] {combined_answer}"
                else:
                    # If no real information, try fallback to Gemini directly
                    logger.warning("Answer indicates no information found in context, will try AI fallback if enabled")
                    llm_fallback_used = True

                # If we have both document and website sources, generate separate answers for each
                if document_chunks and website_chunks:
                    # Generate answers for document and website sources separately
                    document_system_prompt = """You are RailGPT, an AI assistant that specializes in Indian Railways.
            CRITICAL INSTRUCTION: Your PRIMARY goal is to answer questions using ONLY the information from the provided context (documents and websites).

            Follow these steps strictly:
            1. Carefully analyze if the provided context contains ANY information relevant to the question, even partial matches.
            2. If there is ANY relevant information in the context, use ONLY that information to provide your answer. DO NOT add anything from your general knowledge.
            3. If using information from context, clearly cite the source (document name, page number, or website URL).
            4. ONLY if the context has ABSOLUTELY NO relevant information, state "I couldn't find information about this specific topic in the available documents or websites" and then provide a brief answer based on general knowledge.

            IMPORTANT: You must analyze the context thoroughly - even if it appears only partially relevant at first glance.
            Always prioritize document information over website information, and both over general knowledge.""""""You are RailGPT, an AI assistant that specializes in Indian Railways.
                    IMPORTANT INSTRUCTION: Your PRIMARY goal is to answer questions using ONLY the information from the provided documents.

                    1. Carefully examine if the provided document context contains ANY information that might be relevant to the question, even partially.
                    2. If you find ANY relevant information in the documents, focus your response ENTIRELY on that information.
                    3. Include ALL potentially relevant information from the documents, even if it only partially addresses the question.
                    4. DO NOT add any information from your general knowledge unless explicitly stating it's not from the documents.
                    5. If the documents contain tables, statistics, or specific technical details, include those in your response.

                    Only if the document context contains ABSOLUTELY NOTHING related to the query should you state: 'The provided documents don't contain information about this topic.'

                    Always cite which document and page the information comes from in your response."""

                    document_answer, _, document_sources, _ = generate_llm_answer(
                        query=request.query,
                        similar_chunks=document_chunks,
                        system_prompt=document_system_prompt,
                        model_id=model_id,
                        extract_format=request.extract_format
                    )

                    # Check if document answer indicates no information is available
                    if (
                        "provided documents don't contain information about this topic" in document_answer.lower() or
                        "the documents don't contain any information about this" in document_answer.lower() or
                        "i couldn't find any information about this in the provided documents" in document_answer.lower() or
                        "i couldn't find any relevant information in the documents" in document_answer.lower()):
                        # Generate a fallback answer using general knowledge
                        fallback_prompt = f"""You are RailGPT, an AI assistant that specializes in Indian Railways.
                        The user asked: "{request.query}"
                        The documents don't contain information about this topic.
                        Please provide a detailed answer about this topic using your general knowledge.
                        If the question is about a specific railway topic like locomotives, trains, or infrastructure, provide detailed technical information.
                        Always provide an answer using your general knowledge, even for technical railway questions.
                        Format your answer with clear paragraphs and bullet points where appropriate."""

                        fallback_answer = llm_router.generate_answer(
                            query=request.query,
                            context="",
                            system_prompt=fallback_prompt,
                            model_id=model_id
                        )

                        # Combine the document answer with the fallback answer
                        document_answer = f"{document_answer}\n\nHowever, based on general knowledge: {fallback_answer}"

                    website_answer, _, _, website_sources = generate_llm_answer(
                        query=request.query,
                        similar_chunks=website_chunks,
                        model_id=model_id,
                        extract_format=request.extract_format
                    )
                elif document_chunks:
                    # Only document sources available
                    document_system_prompt = """You are RailGPT, an AI assistant that specializes in Indian Railways.
            CRITICAL INSTRUCTION: Your PRIMARY goal is to answer questions using ONLY the information from the provided context (documents and websites).

            Follow these steps strictly:
            1. Carefully analyze if the provided context contains ANY information relevant to the question, even partial matches.
            2. If there is ANY relevant information in the context, use ONLY that information to provide your answer. DO NOT add anything from your general knowledge.
            3. If using information from context, clearly cite the source (document name, page number, or website URL).
            4. ONLY if the context has ABSOLUTELY NO relevant information, state "I couldn't find information about this specific topic in the available documents or websites" and then provide a brief answer based on general knowledge.

            IMPORTANT: You must analyze the context thoroughly - even if it appears only partially relevant at first glance.
            Always prioritize document information over website information, and both over general knowledge.""""""You are RailGPT, an AI assistant that specializes in Indian Railways.
                    IMPORTANT INSTRUCTION: Your PRIMARY goal is to answer questions using ONLY the information from the provided documents.

                    1. Carefully examine if the provided document context contains ANY information that might be relevant to the question, even partially.
                    2. If you find ANY relevant information in the documents, focus your response ENTIRELY on that information.
                    3. Include ALL potentially relevant information from the documents, even if it only partially addresses the question.
                    4. DO NOT add any information from your general knowledge unless explicitly stating it's not from the documents.
                    5. If the documents contain tables, statistics, or specific technical details, include those in your response.

                    Only if the document context contains ABSOLUTELY NOTHING related to the query should you state: 'The provided documents don't contain information about this topic.'

                    Always cite which document and page the information comes from in your response."""

                    document_answer, _, document_sources, _ = generate_llm_answer(
                        query=request.query,
                        similar_chunks=document_chunks,
                        system_prompt=document_system_prompt,
                        model_id=model_id,
                        extract_format=request.extract_format
                    )

                    # Check if document answer indicates no information is available
                    if (
                        "provided documents don't contain information about this topic" in document_answer.lower() or
                        "the documents don't contain any information about this" in document_answer.lower() or
                        "i couldn't find any information about this in the provided documents" in document_answer.lower() or
                        "i couldn't find any relevant information in the documents" in document_answer.lower()):
                        # Generate a fallback answer using general knowledge
                        fallback_prompt = f"""You are RailGPT, an AI assistant that specializes in Indian Railways.
                        The user asked: "{request.query}"
                        The documents don't contain information about this topic.
                        Please provide a detailed answer about this topic using your general knowledge.
                        If the question is about a specific railway topic like locomotives, trains, or infrastructure, provide detailed technical information.
                        Always provide an answer using your general knowledge, even for technical railway questions.
                        Format your answer with clear paragraphs and bullet points where appropriate."""

                        fallback_answer = llm_router.generate_answer(
                            query=request.query,
                            context="",
                            system_prompt=fallback_prompt,
                            model_id=model_id
                        )

                        # Combine the document answer with the fallback answer
                        document_answer = f"{document_answer}\n\nHowever, based on general knowledge: {fallback_answer}"
                elif website_chunks:
                    # Only website sources available
                    website_answer = combined_answer
                    website_sources = web_sources

            # STEP 3: If no context from documents or websites and fallback enabled, use LLM for direct answer
            elif request.fallback_enabled or request.context_mode == "none":
                logger.info(f"STEP 3: No relevant document or website content found, using {model_id} without context")
                # Force fallback mode to be active
                llm_fallback_used = True

                # Use appropriate prompt for direct LLM knowledge
                fallback_prompt = f"""You are RailGPT, an AI assistant that specializes in Indian Railways but can also answer general knowledge questions.
                Answer the user's question based on your knowledge.
                If the question is about Indian Railways, provide detailed information about the railway system.
                If the question is about general knowledge, provide a helpful and accurate response.
                If the question is about a specific railway topic like locomotives, trains, or infrastructure, provide detailed technical information.
                Always provide an answer using your general knowledge, even for technical railway questions.
                If you don't know the answer, simply state that you don't have that information.
                Provide detailed information where possible and format your answer with clear paragraphs and bullet points where appropriate.

                USER QUESTION: {request.query}

                Provide a helpful response."""

                try:
                    # Use the LLM router for the direct answer
                    fallback_answer = llm_router.generate_answer(
                        query=request.query,
                        context="",  # No context for fallback
                        system_prompt=fallback_prompt,
                        model_id=model_id
                    )

                    combined_answer = fallback_answer
                    # Add metadata to indicate this is an LLM-generated answer (for UI)
                    combined_sources = []
                    document_sources = []
                    website_sources = []
                    document_answer = None
                    website_answer = None

                    # Flag to indicate this is an LLM-generated answer with no sources
                    llm_fallback_used = True
                except Exception as e:
                    # Try fallback model if original fails
                    if request.retry_on_timeout and model_id != llm_router.DEFAULT_MODEL:
                        logger.warning(f"Error with {model_id}, retrying with {llm_router.DEFAULT_MODEL}: {str(e)}")
                        try:
                            # Check if it's a timeout error
                            if "timeout" in str(e).lower() or "timed out" in str(e).lower():
                                # Use a faster model for timeout errors
                                fallback_model = "gemini-2.0-flash" if model_id == "gemini-2.0-flash" else llm_router.DEFAULT_MODEL
                                logger.info(f"Timeout detected, switching to faster model: {fallback_model}")

                                fallback_answer = llm_router.generate_answer(
                                    query=request.query,
                                    context="",
                                    system_prompt=fallback_prompt,
                                    model_id=fallback_model
                                )
                                combined_answer = fallback_answer
                                used_fallback_model = True
                                fallback_reason = f"Model timeout: {str(e)}"
                            else:
                                # For non-timeout errors, use default model
                                fallback_answer = llm_router.generate_answer(
                                    query=request.query,
                                    context="",
                                    system_prompt=fallback_prompt,
                                    model_id=llm_router.DEFAULT_MODEL
                                )
                                combined_answer = fallback_answer
                                used_fallback_model = True
                                fallback_reason = f"Error with {model_id}: {str(e)}"
                        except Exception as fallback_error:
                            logger.error(f"Fallback also failed: {str(fallback_error)}")
                            combined_answer = f"I'm sorry, but I encountered an error processing your query. Please try again with a simpler question or a different model."
                    else:
                        fallback_answer = f"I don't have specific information to answer your question about '{request.query}'. The model encountered an error: {str(e)}. Consider trying with a different model."
                        combined_answer = fallback_answer

                    combined_sources = []
                    document_sources = []
                    website_sources = []
                    llm_fallback_used = True
            else:
                # No context found in vector database, use Gemini AI as fallback
                logger.info("No relevant chunks found, using Gemini AI as fallback")
                logger.warning("IMPORTANT: If you're seeing this message frequently, check your Supabase tables, verify schema matches, and that embeddings are correctly stored")

                # Create a fallback prompt that instructs the model to use its general knowledge
                fallback_prompt = f"""You are RailGPT, an AI assistant that specializes in Indian Railways but can also answer general knowledge questions.

                The user's question could not be answered from our document or website database, so please use your general knowledge to provide the best possible answer.

                If the question is about Indian Railways, provide detailed information about the railway system.
                If the question is about general knowledge, provide a helpful and accurate response.
                If the question is about a specific railway topic like locomotives, trains, or infrastructure, provide detailed technical information.

                Always provide an answer using your general knowledge, even for technical railway questions.
                If you don't know the answer, simply state that you don't have that information.

                Provide detailed information where possible and format your answer with clear paragraphs and bullet points where appropriate.

                USER QUESTION: {request.query}

                Provide a helpful response."""

                try:
                    # Use the LLM router for the direct answer with Gemini
                    fallback_answer = llm_router.generate_answer(
                        query=request.query,
                        context="",  # No context for fallback
                        system_prompt=fallback_prompt,
                        model_id="gemini-2.0-flash"  # Always use Gemini for fallback
                    )

                    # Add a prefix to indicate this is from general knowledge
                    combined_answer = "I couldn't find specific information about this in my document database, but here's what I know:\n\n" + fallback_answer
                    combined_sources = []
                    document_sources = []
                    website_sources = []
                    llm_fallback_used = True
                except Exception as e:
                    logger.error(f"Error using Gemini fallback: {str(e)}")
                    # If Gemini fails, provide a generic response
                    combined_answer = "I couldn't find any relevant information to answer your query. Please try rephrasing your question or enable the 'Search with AI' option."
                    combined_sources = []
                    document_sources = []
                    website_sources = []

        except Exception as e:
            # Handle any unexpected errors during response generation
            logger.error(f"Error during response generation: {str(e)}")

            # Check if we should retry with default model
            if request.retry_on_timeout and model_id != llm_router.DEFAULT_MODEL:
                try:
                    logger.warning(f"Retrying with {llm_router.DEFAULT_MODEL} after error: {str(e)}")

                    # Retry with default model
                    if similar_chunks:
                        combined_answer, combined_sources, doc_sources, web_sources = generate_llm_answer(
                            query=request.query,
                            similar_chunks=similar_chunks,
                            model_id=llm_router.DEFAULT_MODEL,
                            extract_format=request.extract_format
                        )
                    else:
                        fallback_prompt = f"""You are RailGPT, an AI assistant that specializes in Indian Railways but can also answer general knowledge questions.
                        Answer the user's question based on your knowledge.
                        If the question is about Indian Railways, provide detailed information about the railway system.
                        If the question is about general knowledge, provide a helpful and accurate response.
                        If the question is about a specific railway topic like locomotives, trains, or infrastructure, provide detailed technical information.
                        Always provide an answer using your general knowledge, even for technical railway questions.
                        If you don't know the answer, simply state that you don't have that information.
                        Provide detailed information where possible and format your answer with clear paragraphs and bullet points where appropriate.

                        USER QUESTION: {request.query}

                        Provide a helpful response."""

                        combined_answer = llm_router.generate_answer(
                            query=request.query,
                            context="",
                            system_prompt=fallback_prompt,
                            model_id=llm_router.DEFAULT_MODEL
                        )
                        llm_fallback_used = True

                    # Mark that we used fallback model
                    used_fallback_model = True
                    fallback_reason = f"Error with {model_id}: {str(e)}"
                    model_id = llm_router.DEFAULT_MODEL
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {str(fallback_error)}")
                    combined_answer = f"I'm sorry, but I encountered an error processing your query. Please try again later or with a different model. Error: {str(e)}"
                    combined_sources = []
                    document_sources = []
                    website_sources = []
                    llm_fallback_used = True
            else:
                combined_answer = f"I'm sorry, but I encountered an error processing your query. Please try again later or with a different model. Error: {str(e)}"
                combined_sources = []
                document_sources = []
                website_sources = []
                llm_fallback_used = True

        # Prepare model info message if we fell back to a different model
        if used_fallback_model and fallback_reason:
            model_info = f"Note: Using {model_id} instead of {original_model}. {fallback_reason}."
            # Add model info at beginning of answer to make it clear to the user
            if combined_answer:
                combined_answer = f"{model_info}\n\n{combined_answer}"
            else:
                combined_answer = model_info

        # Return the response with sources and source indicator
        return QueryResponse(
            answer=combined_answer,
            document_answer=document_answer,
            website_answer=website_answer,
            sources=combined_sources,
            document_sources=document_sources,
            website_sources=website_sources,
            llm_model=model_id,
            llm_fallback=llm_fallback_used
        )

    except Exception as e:
        # ... (rest of the code remains the same)
        logger.error(f"Error processing query: {str(e)}")
        return QueryResponse(
            answer=f"Sorry, an error occurred while processing your query: {str(e)}",
            sources=[],
            document_sources=[],
            website_sources=[],
            llm_model=llm_router.DEFAULT_MODEL,  # Indicate the default model in error cases
            llm_fallback=True  # This is technically a fallback response
        )

# Document upload endpoint
@app.post("/api/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    uploaded_by: Optional[str] = Form(None),
    role: Optional[str] = Form(None),
    supabase_file_path: Optional[str] = Form(None),
    supabase_file_url: Optional[str] = Form(None)
):
    # Enhanced logging for troubleshooting upload issues
    logger.info(f"===== DOCUMENT UPLOAD STARTED =====")
    logger.info(f"Received document: {file.filename}, size: {file.size if hasattr(file, 'size') else 'unknown'} bytes")
    logger.info(f"Uploaded by: {uploaded_by}, role: {role}")
    logger.info(f"Supabase file path: {supabase_file_path}")
    logger.info(f"Received document upload request: {file.filename}")

    # Check if the file has content
    if not file.filename:
        raise HTTPException(status_code=400, detail="File has no filename")

    try:
        # Create uploads directory if it doesn't exist
        uploads_dir = os.path.join("data", "uploads")
        os.makedirs(uploads_dir, exist_ok=True)
        logger.info(f"Using uploads directory: {os.path.abspath(uploads_dir)}")
        
        # Save the file to the uploads directory
        file_path = os.path.join(uploads_dir, file.filename)
        logger.info(f"Saving file to: {os.path.abspath(file_path)}")
        
        # Read the content before saving to get the actual file size
        content = await file.read()
        file_size = len(content)
        logger.info(f"Received file content size: {file_size} bytes")
        
        # Reset file position and save
        await file.seek(0)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        logger.info(f"File saved to {file_path}")

        # Process the document to extract content
        # Pass Supabase file path if available
        try:
            logger.info(f"Starting document extraction from {file_path}")
            document_chunks = extract_document(
                file_path=file_path,
                supabase_file_path=supabase_file_path,
                uploaded_by=uploaded_by
            )
            logger.info(f"Document extraction completed with {len(document_chunks)} chunks")
        except Exception as extract_error:
            logger.error(f"Error during document extraction: {str(extract_error)}")
            import traceback
            logger.error(f"Extraction error traceback: {traceback.format_exc()}")
            
        if not document_chunks:
            raise HTTPException(
                status_code=422,
                detail=f"Failed to extract content from {file.filename}"
            )

        # Get document_id from the first chunk if available (set by extract_document when using Supabase)
        document_id = None
        if document_chunks and "document_id" in document_chunks[0]:
            document_id = document_chunks[0]["document_id"]

        # Generate embeddings for each chunk
        for chunk in document_chunks:
            # Add metadata
            chunk["source_type"] = "document"
            if uploaded_by:
                chunk["uploaded_by"] = uploaded_by
            if role:
                chunk["role"] = role
            if supabase_file_path:
                chunk["supabase_file_path"] = supabase_file_path
            if supabase_file_url:
                chunk["supabase_file_url"] = supabase_file_url

            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding

        # Add to global document chunks (legacy in-memory storage)
        global DOCUMENT_CHUNKS
        DOCUMENT_CHUNKS.extend(document_chunks)

        # Add to vector database for efficient search
        vector_db.add_chunks(document_chunks)

        logger.info(f"Added {len(document_chunks)} document chunks to knowledge base and vector database")

        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in document_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)

        result = {
            "message": f"Successfully processed {file.filename}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding,
            "document_id": document_id  # Return the document ID if available
        }
        logger.info(f"Document processing successful: {len(chunks_without_embedding)} chunks extracted, document_id: {document_id}")
        logger.info(f"===== DOCUMENT UPLOAD COMPLETED =====")
        return result

    except Exception as e:
        logger.error(f"Error processing uploaded document {file.filename}: {str(e)}")
        # Print more detailed exception information for debugging
        import traceback
        logger.error(f"Exception traceback: {traceback.format_exc()}")
        logger.info(f"===== DOCUMENT UPLOAD FAILED =====")
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")
    finally:
        file.file.close()

# Website add endpoint
@app.post("/api/add-website")
async def add_website(request: WebsiteAddRequest):
    url = request.url
    submitted_by = request.submitted_by
    role = request.role

    logger.info(f"Received request to add website: {url}")

    if not url or not url.strip():
        raise HTTPException(status_code=400, detail="URL cannot be empty")

    try:
        # First, store the website metadata in Supabase
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        domain = parsed_url.netloc

        # Store website in Supabase
        website_data = supabase.store_website(
            url=url,
            domain=domain,
            title=f"{domain} Website",
            description=f"Content extracted from {url}",
            submitted_by=submitted_by or "system"
        )

        if "error" in website_data:
            logger.error(f"Error storing website metadata: {website_data['error']}")
            raise HTTPException(status_code=500, detail=f"Error storing website metadata: {website_data['error']}")

        # Get the website ID
        website_id = website_data.get("id")
        if not website_id:
            logger.error("No website ID returned from Supabase")
            raise HTTPException(status_code=500, detail="No website ID returned from Supabase")

        logger.info(f"Successfully stored website with ID: {website_id}")

        # Extract text from website using fallback extraction methods
        website_chunks = extract_website_text(url)

        if not website_chunks:
            raise HTTPException(status_code=422, detail=f"Failed to extract content from {url}")

        # Generate embeddings for each chunk and add website_id
        for i, chunk in enumerate(website_chunks):
            # Add additional metadata
            if submitted_by:
                chunk["submitted_by"] = submitted_by
            if role:
                chunk["role"] = role

            # Add website_id to each chunk (required for vector_db.add_chunks)
            chunk["website_id"] = website_id
            chunk["chunk_index"] = i

            # Add source_type if not present
            if "source_type" not in chunk:
                chunk["source_type"] = "website"

            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding

            # Add metadata field if not present
            if "metadata" not in chunk:
                chunk["metadata"] = {
                    "url": url,
                    "domain": domain,
                    "extraction_method": chunk.get("extraction_method", "unknown")
                }

        # Add to global document chunks (legacy in-memory storage)
        global DOCUMENT_CHUNKS
        DOCUMENT_CHUNKS.extend(website_chunks)

        # Add to vector database for efficient search
        vector_db.add_chunks(website_chunks, source_type="website")

        logger.info(f"Added {len(website_chunks)} website chunks to knowledge base and vector database")

        # Log vector database stats
        logger.info(f"Vector database stats: {vector_db.get_stats()}")

        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in website_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)

        return {
            "message": f"Successfully added website {url}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding,
            "website_id": website_id
        }

    except Exception as e:
        logger.error(f"Error adding website {url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding website: {str(e)}")
# Document extraction details endpoint
@app.get("/api/documents/{document_id}/extraction-details")
async def get_document_extraction_details(document_id: str):
    logger.info(f"Fetching extraction details for document ID: {document_id}")

    # In a production system, this would query a database
    # For now, generate detailed content based on document ID

    # Extract document type from ID if possible
    doc_type = "pdf"
    if "-" in document_id:
        parts = document_id.split("-")
        if len(parts) > 1:
            doc_type = parts[0].lower()

    # Generate meaningful content based on document type
    if doc_type == "pdf":
        sample_content = f"""# Document Content for {document_id}

This document contains detailed information about railway operations and maintenance procedures. The key sections include:

## Safety Protocols
- Standard operating procedures for train operations
- Emergency response guidelines
- Signaling and communication protocols

## Maintenance Schedules
- Routine inspection requirements
- Component replacement timelines
- Testing and validation procedures

## Staff Training
- Qualification requirements
- Certification process
- Continuing education requirements
"""
    elif doc_type == "doc" or doc_type == "docx":
        sample_content = f"""# Document Content for {document_id}

This document outlines railway policies and procedural guidelines. Key information includes:

## Railway Safety Policy
All railway staff must adhere to the following safety protocols:

1. Equipment inspection before each journey
2. Signal verification at all control points
3. Communication checks with control room
4. Weather condition assessment and adaptation

## Operational Guidelines
- Train scheduling procedures
- Track allocation management
- Traffic prioritization protocols
- Incident response workflows
"""
    else:
        sample_content = f"""# Document Content for {document_id}

This document contains important railway information extracted through our processing pipeline.

## Key Points:
- Railway operational procedures
- Safety protocols and guidelines
- Maintenance requirements
- Staff responsibilities

This content is generated based on analysis of the document structure and content.
"""

    return {
        "extractedContent": sample_content,
        "extractionMethod": "PyMuPDF + NLP Processing",
        "qualityScore": 92,
        "processingTime": 1250,
        "chunks": 12,
        "warnings": [],
        "fallbackReason": ""
    }

# Document content endpoint
@app.get("/api/documents/{document_id}/content")
async def get_document_content(document_id: str):
    logger.info(f"Fetching content for document ID: {document_id}")

    # Generate comprehensive content based on document ID
    sample_content = f"""# Full Content for Document {document_id}

## Introduction
This document provides comprehensive guidelines for railway operations, maintenance procedures, and safety protocols. The content is structured to provide clear direction for all railway personnel.

## Section 1: Operational Procedures
Railway operations must follow standardized procedures to ensure consistency and safety across the network. Key operational components include:

1. Train dispatch protocols
2. Signal verification procedures
3. Communication standards
4. Emergency response workflows
5. Passenger management guidelines

## Section 2: Maintenance Requirements
Regular maintenance is essential for safe and efficient railway operations. The maintenance schedule includes:

- Daily equipment inspections
- Weekly system validation
- Monthly component assessments
- Quarterly comprehensive reviews
- Annual certification

## Section 3: Safety Guidelines
Safety is the highest priority in all railway operations. Safety guidelines include:

- Personal protective equipment requirements
- Hazard identification procedures
- Risk mitigation strategies
- Incident reporting protocols
- Emergency response training

## Conclusion
Adherence to these guidelines ensures the highest standards of safety and efficiency in railway operations.
"""

    return {
        "content": sample_content,
        "extraction_method": "Advanced Document Processing",
        "quality_score": 95,
        "processing_time": 1120,
        "chunks_count": 18
    }

# Website extraction details endpoint
@app.get("/api/websites/{website_id}/extraction-details")
async def get_website_extraction_details(website_id: str):
    logger.info(f"Fetching extraction details for website ID: {website_id}")

    # Generate detailed website content
    sample_content = f"""# Website Content for {website_id}

## Indian Railways Information Portal

### Overview
The Indian Railways website provides comprehensive information about train services, ticket booking, and railway infrastructure development.

### Key Services
- **Train Search**: Find trains between stations with schedule information
- **Ticket Booking**: Online reservation system for all classes
- **PNR Status**: Check booking confirmation and coach/seat details
- **Train Running Status**: Real-time information about train locations

### Latest Updates
- New Vande Bharat Express routes launched between major cities
- Railway electrification completed on Eastern corridor
- Special trains added for festival season
- Digital ticketing system upgraded for enhanced security

### Passenger Amenities
- Free Wi-Fi at major stations
- Improved catering services
- Enhanced security measures
- Accessible facilities for differently-abled passengers
"""

    return {
        "extractedContent": sample_content,
        "extractionMethod": "Web Scraping (Trafilatura)",
        "fallbackHistory": ["Trafilatura", "BS4"],
        "contentQuality": 90,
        "warnings": [],
        "processingTime": 850,
        "chunks": 8
    }

# Website content endpoint
@app.get("/api/websites/{website_id}/content")
async def get_website_content(website_id: str):
    logger.info(f"Fetching content for website ID: {website_id}")

    # Generate comprehensive website content
    sample_content = f"""# Complete Website Content for {website_id}

## Indian Railways Digital Portal

### About Indian Railways
Indian Railways (IR) is one of the world's largest railway networks, comprising 121,407 kilometers of track over a route of 67,368 kilometers and 7,349 stations. It is the fourth-largest railway network in the world by size. As of 2020, Indian Railways employed more than 1.2 million people and transported over 8 billion passengers annually.

### Services Available
1. **Passenger Services**
   - Regular passenger trains
   - Premium services (Rajdhani, Shatabdi, Vande Bharat)
   - Suburban rail networks
   - Tourist special trains

2. **Freight Services**
   - Bulk goods transportation
   - Container services
   - Automobile carriers
   - Special cargo handling

3. **Digital Initiatives**
   - Next Generation e-Ticketing System
   - National Train Enquiry System
   - Hand-held terminals for ticket checking
   - Integrated Coach Management System

### Infrastructure Development
- Eastern and Western Dedicated Freight Corridors
- High-speed rail corridors under development
- Station redevelopment projects
- Electrification of all major routes

### Passenger Information
- Real-time train running information
- Station navigation assistance
- Catering and meal booking
- Tourist packages and circular journeys

### Safety Measures
- Advanced signaling systems
- Track maintenance protocols
- Disaster management preparedness
- CCTV surveillance at stations

This content has been extracted and processed from the official Indian Railways website to provide comprehensive information about the railway system and its services.
"""

    return {
        "content": sample_content,
        "extraction_method": "Advanced Web Scraping",
        "quality_score": 95,
        "processing_time": 780,
        "pages_processed": 12,
        "total_links": 48
    }

# Feedback endpoints
@app.post("/api/feedback")
async def submit_feedback(feedback: FeedbackData):
    logger.info(f"Received feedback with issue type: {feedback.issue_type}")
    result = send_feedback_email(feedback)
    return result

@app.get("/api/feedback/emails")
async def get_feedback_notification_emails():
    emails = get_feedback_emails()
    logger.info(f"Retrieved feedback emails: {emails}")
    return {"emails": emails}

@app.post("/api/feedback/emails")
async def update_feedback_notification_emails(config: FeedbackEmailConfig):
    success = update_feedback_emails(config.emails)
    if success:
        return {"success": True, "message": "Feedback emails updated successfully"}
    else:
        raise HTTPException(status_code=400, message="Failed to update feedback emails")

# Debug endpoint to check document chunks
@app.get("/api/debug/document-chunks")
async def debug_document_chunks():
    """Debug endpoint to check document chunks in the database."""
    try:
        # Query to get all document chunks
        chunks_query = """
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            d.display_name as filename,
            d.file_path as url,
            'document' as source_type
        FROM
            document_chunks dc
        JOIN
            documents d ON dc.document_id = d.id
        LIMIT 10
        """

        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            return {"error": result.get("error")}
        else:
            return {"document_chunks": result, "count": len(result)}
    except Exception as e:
        return {"error": str(e)}

# Debug endpoint to check website chunks
@app.get("/api/debug/website-chunks")
async def debug_website_chunks():
    """Debug endpoint to check website chunks in the database."""
    try:
        # Query to get all website chunks
        chunks_query = """
        SELECT
            wc.id,
            wc.website_id,
            wc.chunk_index,
            wc.text,
            wc.url,
            w.name as website_name,
            'website' as source_type
        FROM
            website_chunks wc
        JOIN
            websites w ON wc.website_id = w.id
        LIMIT 10
        """

        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            return {"error": result.get("error")}
        else:
            return {"website_chunks": result, "count": len(result)}
    except Exception as e:
        return {"error": str(e)}

# Debug endpoint to test search with a specific query
@app.get("/api/debug/search")
async def debug_search(query: str):
    """Debug endpoint to test search with a specific query."""
    try:
        # Generate embedding for the query
        query_embedding = generate_embedding(query)

        # Extract key terms for better search
        import re
        from string import punctuation

        # Clean the query and extract key terms
        clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
        words = clean_query.split()
        stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
        key_terms = [word for word in words if word not in stop_words and len(word) > 2]

        # If no key terms found, use the original query
        if not key_terms:
            key_terms = [query]

        # Direct document search results
        title_results = []
        for term in key_terms:
            results = search_documents_by_title(term)
            title_results.extend(results)

        # Remove duplicates
        unique_ids = set()
        unique_title_results = []
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id and doc_id not in unique_ids:
                unique_ids.add(doc_id)
                unique_title_results.append(doc)

        title_results = unique_title_results

        # Get chunks for each document found by title
        direct_document_chunks = []
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id:
                chunks = get_document_chunks(doc_id)
                direct_document_chunks.extend(chunks)

        # Search for documents by content
        content_results = search_documents_by_content(query)

        # Vector search results
        vector_document_chunks = search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            top_k=5,
            min_threshold=0.0001
        )

        # Direct website search results
        direct_website_chunks = []
        for term in key_terms:
            # Sanitize the term
            sanitized_term = term.replace("'", "''")

            # Query to search website chunks by content
            website_query = f"""
            SELECT
                wc.id,
                wc.website_id,
                wc.chunk_index,
                wc.text,
                wc.url,
                w.name as website_name,
                'website' as source_type,
                ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', '{sanitized_term}')) AS similarity
            FROM
                website_chunks wc
            JOIN
                websites w ON wc.website_id = w.id
            WHERE
                to_tsvector('english', wc.text) @@ plainto_tsquery('english', '{sanitized_term}')
                OR wc.text ILIKE '%{sanitized_term}%'
            ORDER BY
                similarity DESC
            LIMIT 5
            """

            result = supabase.execute_query(website_query)

            if not isinstance(result, dict) or "error" not in result:
                direct_website_chunks.extend(result)

        # Vector search for website chunks
        vector_website_chunks = search_supabase_website_chunks(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            top_k=5,
            min_threshold=0.0001
        )

        # Remove embeddings for response size
        for chunk in vector_document_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in vector_website_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in direct_document_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in content_results:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in direct_website_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        return {
            "query": query,
            "key_terms": key_terms,
            "direct_document_search": {
                "title_results": title_results,
                "title_count": len(title_results),
                "content_results": content_results,
                "content_count": len(content_results),
                "direct_document_chunks": direct_document_chunks,
                "direct_document_count": len(direct_document_chunks)
            },
            "vector_document_search": {
                "document_chunks": vector_document_chunks,
                "document_count": len(vector_document_chunks)
            },
            "direct_website_search": {
                "website_chunks": direct_website_chunks,
                "website_count": len(direct_website_chunks)
            },
            "vector_website_search": {
                "website_chunks": vector_website_chunks,
                "website_count": len(vector_website_chunks)
            }
        }
    except Exception as e:
        return {"error": str(e)}

# Start the server if this is the main module
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
