import os
import sys
import logging

# Add backend to path
sys.path.append('./backend')

# Import backend modules
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_missing_filenames():
    """Fix chunks that have missing filenames by updating them with document info."""
    logger.info("=== FIXING MISSING FILENAMES IN CHUNKS ===")
    
    try:
        # Direct SQL approach using the execute_sql method
        update_query = """
        UPDATE document_chunks 
        SET filename = d.display_name
        FROM documents d 
        WHERE document_chunks.document_id = d.id 
        AND (document_chunks.filename IS NULL OR document_chunks.filename = '' OR document_chunks.filename = 'Unknown document')
        RETURNING document_chunks.id, document_chunks.filename;
        """
        
        # Try using the execute_sql method 
        try:
            result = supabase.execute_sql(update_query)
            if result and not isinstance(result, dict) or not result.get('error'):
                logger.info(f"Successfully updated filenames using execute_sql")
            else:
                logger.warning(f"execute_sql result: {result}")
        except Exception as e:
            logger.warning(f"execute_sql failed: {e}")
            # Continue with alternative approach
        
        # Alternative approach: Use Supabase table operations  
        # Get all chunks without proper filenames
        chunks_query = """
        SELECT 
            dc.id,
            dc.document_id,
            dc.filename,
            d.display_name
        FROM document_chunks dc
        JOIN documents d ON dc.document_id = d.id
        WHERE dc.filename IS NULL OR dc.filename = '' OR dc.filename = 'Unknown document'
        """
        
        chunks_result = supabase.execute_query(chunks_query)
        
        if chunks_result and not isinstance(chunks_result, dict):
            logger.info(f"Found {len(chunks_result)} chunks to update")
            
            updated_count = 0
            for chunk in chunks_result:
                chunk_id = chunk.get('id')
                display_name = chunk.get('display_name')
                
                if chunk_id and display_name:
                    try:
                        # Update individual chunk using Supabase client
                        update_result = supabase.supabase.table('document_chunks').update({
                            'filename': display_name
                        }).eq('id', chunk_id).execute()
                        
                        if update_result.data:
                            updated_count += 1
                            if updated_count <= 5:  # Only log first 5 for brevity
                                logger.info(f"Updated chunk {chunk_id} with filename: {display_name}")
                    except Exception as e:
                        logger.error(f"Failed to update chunk {chunk_id}: {e}")
            
            logger.info(f"Successfully updated {updated_count} chunks with proper filenames")
        else:
            logger.warning("Could not retrieve chunks to update")
            
        # Verify the fix
        verification_query = """
        SELECT 
            dc.id,
            dc.filename,
            d.display_name,
            COALESCE(dc.metadata->>'content_type', 'text') as content_type
        FROM document_chunks dc
        JOIN documents d ON dc.document_id = d.id
        WHERE COALESCE(dc.metadata->>'content_type', 'text') IN ('image', 'table', 'chart_diagram')
        ORDER BY d.display_name, dc.page_number
        LIMIT 10
        """
        
        verification_result = supabase.execute_query(verification_query)
        
        if verification_result and not isinstance(verification_result, dict):
            logger.info(f"Verification: Found {len(verification_result)} visual chunks after fix:")
            for i, chunk in enumerate(verification_result):
                logger.info(f"  {i+1}. Filename: {chunk.get('filename', 'NOT SET')}")
                logger.info(f"      Content Type: {chunk.get('content_type', 'text')}")
                
        return True
        
    except Exception as e:
        logger.error(f"Error fixing filenames: {e}")
        return False

def update_chunk_metadata_filenames():
    """Update chunk metadata to include proper filenames in visual content."""
    logger.info("=== UPDATING CHUNK METADATA WITH FILENAMES ===")
    
    try:
        # Get visual content chunks with their document info
        query = """
        SELECT 
            dc.id,
            dc.metadata,
            d.display_name
        FROM document_chunks dc
        JOIN documents d ON dc.document_id = d.id
        WHERE COALESCE(dc.metadata->>'content_type', 'text') IN ('image', 'table', 'chart_diagram')
        AND dc.metadata IS NOT NULL
        """
        
        result = supabase.execute_query(query)
        
        if result and not isinstance(result, dict):
            logger.info(f"Found {len(result)} visual content chunks to update metadata")
            
            updated_count = 0
            for chunk in result:
                chunk_id = chunk.get('id')
                metadata = chunk.get('metadata', {})
                display_name = chunk.get('display_name')
                
                if display_name and isinstance(metadata, dict):
                    # Update the filename in metadata
                    metadata['filename'] = display_name
                    
                    # Update the chunk metadata
                    try:
                        update_result = supabase.supabase.table('document_chunks').update({
                            'metadata': metadata
                        }).eq('id', chunk_id).execute()
                        
                        if update_result.data:
                            updated_count += 1
                            if updated_count <= 5:  # Only log first 5 for brevity
                                logger.info(f"Updated metadata for chunk {chunk_id}")
                    except Exception as e:
                        logger.error(f"Error updating chunk {chunk_id} metadata: {e}")
                
            logger.info(f"Updated metadata for {updated_count} chunks")
            return True
        else:
            logger.info("No visual content chunks found to update")
            return True
            
    except Exception as e:
        logger.error(f"Error updating chunk metadata: {e}")
        return False

def test_query_after_fix():
    """Test a query to see if images are now working correctly."""
    logger.info("=== TESTING QUERY AFTER FIX ===")
    
    try:
        import requests
        
        # Test with a specific image query
        response = requests.post(
            "http://localhost:8000/api/query",
            json={
                "query": "VASP Enterprises project image",
                "model": "gemini-2.0-flash"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            logger.info("Query successful!")
            logger.info(f"Document sources: {len(data.get('document_sources', []))}")
            logger.info(f"Visual content found: {data.get('visual_content_found', False)}")
            logger.info(f"Visual content types: {data.get('visual_content_types', [])}")
            
            # Check document sources for visual content
            doc_sources = data.get('document_sources', [])
            for i, source in enumerate(doc_sources):
                logger.info(f"Source {i+1}:")
                logger.info(f"  Filename: {source.get('filename', 'NOT SET')}")
                logger.info(f"  Content Type: {source.get('content_type', 'text')}")
                logger.info(f"  Visual Content Available: {bool(source.get('visual_content'))}")
                
                if source.get('visual_content'):
                    visual = source.get('visual_content')
                    base64_data = visual.get('base64_data', '')
                    logger.info(f"  Base64 Data Length: {len(base64_data)}")
                    logger.info(f"  Storage URL: {visual.get('storage_url', 'None')}")
            
            return True
        else:
            logger.error(f"Query failed with status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error testing query: {e}")
        return False

def main():
    """Run all fixes and tests."""
    logger.info("COMPREHENSIVE FILENAME FIX UTILITY")
    logger.info("=" * 50)
    
    # Step 1: Fix missing filenames in chunks table
    if fix_missing_filenames():
        logger.info("✅ Step 1: Fixed missing filenames")
    else:
        logger.error("❌ Step 1: Failed to fix missing filenames")
        return False
    
    # Step 2: Update chunk metadata 
    if update_chunk_metadata_filenames():
        logger.info("✅ Step 2: Updated chunk metadata")
    else:
        logger.error("❌ Step 2: Failed to update chunk metadata")
        return False
    
    # Step 3: Test the query
    if test_query_after_fix():
        logger.info("✅ Step 3: Query test successful")
    else:
        logger.error("❌ Step 3: Query test failed")
        return False
    
    logger.info("\n🎉 ALL FIXES COMPLETED SUCCESSFULLY!")
    logger.info("The image source issue should now be resolved.")
    return True

if __name__ == "__main__":
    main() 