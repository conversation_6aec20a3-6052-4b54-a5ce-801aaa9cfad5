"""
<PERSON><PERSON><PERSON> to test the search functions in the database for RailGPT.
This script will test the search functions with a sample query.
"""
import os
import logging
import json
from typing import List, Dict, Any
import numpy as np
from dotenv import load_dotenv
from supabase_client import supabase
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def generate_embedding(text: str) -> List[float]:
    """Generate embedding for text using the LLM router."""
    try:
        return llm_router.generate_embedding(text)
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Try with default model
        try:
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Use a deterministic random embedding as last resort
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def test_search_functions():
    """Test the search functions with a sample query."""
    logger.info("Testing search functions with a sample query...")
    
    # Sample query
    query = "What is the Rapid Response app?"
    
    # Generate embedding for the query
    query_embedding = generate_embedding(query)
    
    # Test direct search for document chunks
    logger.info("Testing direct_search_document_chunks...")
    
    direct_doc_query = f"""
    SELECT * FROM direct_search_document_chunks(
        '{json.dumps(query_embedding)}'::vector,
        0.01,
        5
    );
    """
    
    try:
        result = supabase.execute_query(direct_doc_query)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing direct_search_document_chunks: {result['error']}")
        else:
            logger.info(f"direct_search_document_chunks returned {len(result)} results")
            for i, item in enumerate(result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
                logger.info(f"Text snippet: {text_snippet}")
    except Exception as e:
        logger.error(f"Error testing direct_search_document_chunks: {str(e)}")
    
    # Test hybrid search for document chunks
    logger.info("Testing hybrid_search_document_chunks...")
    
    hybrid_doc_query = f"""
    SELECT * FROM hybrid_search_document_chunks(
        '{query}',
        '{json.dumps(query_embedding)}'::vector,
        0.01,
        5
    );
    """
    
    try:
        result = supabase.execute_query(hybrid_doc_query)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing hybrid_search_document_chunks: {result['error']}")
        else:
            logger.info(f"hybrid_search_document_chunks returned {len(result)} results")
            for i, item in enumerate(result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
                logger.info(f"Text snippet: {text_snippet}")
    except Exception as e:
        logger.error(f"Error testing hybrid_search_document_chunks: {str(e)}")
    
    # Test direct search for website chunks
    logger.info("Testing direct_search_website_chunks...")
    
    direct_web_query = f"""
    SELECT * FROM direct_search_website_chunks(
        '{json.dumps(query_embedding)}'::vector,
        0.01,
        5
    );
    """
    
    try:
        result = supabase.execute_query(direct_web_query)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing direct_search_website_chunks: {result['error']}")
        else:
            logger.info(f"direct_search_website_chunks returned {len(result)} results")
            for i, item in enumerate(result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
                logger.info(f"Text snippet: {text_snippet}")
    except Exception as e:
        logger.error(f"Error testing direct_search_website_chunks: {str(e)}")
    
    # Test hybrid search for website chunks
    logger.info("Testing hybrid_search_website_chunks...")
    
    hybrid_web_query = f"""
    SELECT * FROM hybrid_search_website_chunks(
        '{query}',
        '{json.dumps(query_embedding)}'::vector,
        0.01,
        5
    );
    """
    
    try:
        result = supabase.execute_query(hybrid_web_query)
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error testing hybrid_search_website_chunks: {result['error']}")
        else:
            logger.info(f"hybrid_search_website_chunks returned {len(result)} results")
            for i, item in enumerate(result[:3]):  # Show first 3 results
                similarity = item.get("similarity", 0)
                text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
                logger.info(f"Result {i+1}: similarity={similarity:.4f}")
                logger.info(f"Text snippet: {text_snippet}")
    except Exception as e:
        logger.error(f"Error testing hybrid_search_website_chunks: {str(e)}")

def main():
    """Main function to test search functions."""
    test_search_functions()

if __name__ == "__main__":
    main()
