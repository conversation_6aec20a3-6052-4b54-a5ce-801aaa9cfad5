#!/usr/bin/env python3
"""
Comprehensive test script for visual content extraction
"""

import os
import sys
import json
from pathlib import Path

# Add backend to path
sys.path.append('backend')

def test_visual_extraction_implementation():
    """Test if visual extraction is properly implemented"""
    print("🔍 Testing Visual Extraction Implementation")
    print("=" * 50)
    
    try:
        # Test imports
        from visual_extractor import (
            extract_visual_content_from_file, 
            extract_tables_from_pdf_pdfplumber,
            extract_images_from_pdf,
            detect_charts_and_diagrams
        )
        print("✅ Visual extractor imports successful")
        
        from document_extractor import extract_document_with_visual_content
        print("✅ Document extractor with visual content import successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_library_capabilities():
    """Test if required libraries support visual extraction"""
    print("\n🔍 Testing Library Capabilities")
    print("=" * 30)
    
    try:
        import fitz  # PyMuPDF
        print("✅ PyMuPDF imported successfully")
        
        # Test PyMuPDF table extraction capability
        doc = fitz.open()
        page = doc.new_page()
        
        # Check if find_tables method exists
        if hasattr(page, 'find_tables'):
            print("✅ PyMuPDF supports table extraction (find_tables)")
        else:
            print("❌ PyMuPDF does not support table extraction")
        
        # Check if get_images method exists
        if hasattr(page, 'get_images'):
            print("✅ PyMuPDF supports image extraction (get_images)")
        else:
            print("❌ PyMuPDF does not support image extraction")
        
        doc.close()
        
    except ImportError as e:
        print(f"❌ PyMuPDF import error: {e}")
    
    try:
        import pdfplumber
        print("✅ pdfplumber imported successfully")
        
        # Check pdfplumber table extraction methods
        page_methods = [method for method in dir(pdfplumber.page.Page) if 'table' in method.lower()]
        if 'extract_tables' in page_methods:
            print("✅ pdfplumber supports table extraction (extract_tables)")
        else:
            print("❌ pdfplumber does not support table extraction")
        
    except ImportError as e:
        print(f"❌ pdfplumber import error: {e}")

def create_test_pdf_with_table():
    """Create a test PDF with a proper table that can be detected by extraction libraries."""
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib import colors
        from reportlab.lib.units import inch
        
        filename = "test_railway_table.pdf"
        doc = SimpleDocTemplate(filename, pagesize=letter)
        story = []
        
        # Add title
        styles = getSampleStyleSheet()
        title = Paragraph("Railway Component Specifications", styles['Title'])
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Create table data with proper structure
        table_data = [
            ['Component', 'Part Number', 'Specification', 'Quantity'],
            ['Steel Socket Head Cap Screw', 'M8x25', 'Grade 8.8, Zinc Plated', '50'],
            ['Warning Horn', 'WH-12V-110dB', '12V DC, 110dB Output', '2'],
            ['Fuel Oil System Valve', 'FOV-25mm', '25mm Brass Ball Valve', '4'],
            ['Electrical Control Cabinet', 'ECC-480V-3P', '480V 3-Phase Panel', '1'],
            ['Signal Light Assembly', 'SLA-LED-RED', 'LED Red Signal Light', '8']
        ]
        
        # Create table with proper styling
        table = Table(table_data, colWidths=[2*inch, 1.5*inch, 2*inch, 1*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
        ]))
        
        story.append(table)
        story.append(Spacer(1, 12))
        
        # Add some descriptive text
        description = Paragraph(
            "This table contains specifications for various railway components used in locomotive maintenance and operation.",
            styles['Normal']
        )
        story.append(description)
        
        # Build PDF
        doc.build(story)
        print(f"✅ Created test PDF: {filename}")
        return filename
        
    except ImportError:
        print("⚠️  ReportLab not available, creating simple PDF with PyMuPDF")
        # Fallback to PyMuPDF for table creation
        import fitz
        
        filename = "test_railway_table.pdf"
        doc = fitz.open()
        page = doc.new_page()
        
        # Add title
        page.insert_text((50, 50), "Railway Component Specifications", fontsize=16, color=(0, 0, 0))
        
        # Create table manually with text positioning
        table_data = [
            ['Component', 'Part Number', 'Specification', 'Quantity'],
            ['Steel Socket Head Cap Screw', 'M8x25', 'Grade 8.8, Zinc Plated', '50'],
            ['Warning Horn', 'WH-12V-110dB', '12V DC, 110dB Output', '2'],
            ['Fuel Oil System Valve', 'FOV-25mm', '25mm Brass Ball Valve', '4'],
            ['Electrical Control Cabinet', 'ECC-480V-3P', '480V 3-Phase Panel', '1'],
            ['Signal Light Assembly', 'SLA-LED-RED', 'LED Red Signal Light', '8']
        ]
        
        # Draw table with proper spacing
        y_start = 100
        row_height = 25
        col_widths = [150, 100, 150, 60]
        x_positions = [50, 200, 300, 450]
        
        for row_idx, row in enumerate(table_data):
            y_pos = y_start + (row_idx * row_height)
            
            # Draw row background for header
            if row_idx == 0:
                rect = fitz.Rect(45, y_pos - 5, 520, y_pos + 15)
                page.draw_rect(rect, color=(0.8, 0.8, 0.8), fill=(0.8, 0.8, 0.8))
            
            # Draw cell text
            for col_idx, cell in enumerate(row):
                x_pos = x_positions[col_idx]
                fontsize = 10 if row_idx > 0 else 11
                weight = "bold" if row_idx == 0 else "normal"
                page.insert_text((x_pos, y_pos), str(cell), fontsize=fontsize)
            
            # Draw horizontal line
            page.draw_line((45, y_pos + 10), (520, y_pos + 10), color=(0, 0, 0), width=0.5)
        
        # Draw vertical lines
        for x_pos in [45, 195, 295, 445, 520]:
            page.draw_line((x_pos, y_start - 5), (x_pos, y_start + (len(table_data) * row_height) + 5), color=(0, 0, 0), width=0.5)
        
        # Add description
        desc_y = y_start + (len(table_data) * row_height) + 30
        page.insert_text((50, desc_y), "This table contains specifications for various railway components", fontsize=10)
        page.insert_text((50, desc_y + 15), "used in locomotive maintenance and operation.", fontsize=10)
        
        doc.save(filename)
        doc.close()
        print(f"✅ Created test PDF with table: {filename}")
        return filename
        
    except Exception as e:
        print(f"❌ Error creating test PDF: {str(e)}")
        return None

def test_visual_extraction_on_pdf(pdf_path):
    """Test visual extraction on a PDF file"""
    print(f"\n🔍 Testing Visual Extraction on {pdf_path}")
    print("=" * 50)
    
    if not os.path.exists(pdf_path):
        print(f"❌ Test PDF not found: {pdf_path}")
        return False
    
    try:
        from visual_extractor import extract_visual_content_from_file
        
        # Extract visual content
        visual_content = extract_visual_content_from_file(
            pdf_path,
            extract_tables=True,
            extract_images=True,
            extract_charts=True
        )
        
        print(f"📊 Extraction Summary:")
        summary = visual_content.get('extraction_summary', {})
        print(f"   Tables found: {summary.get('total_tables', 0)}")
        print(f"   Images found: {summary.get('total_images', 0)}")
        print(f"   Charts/Diagrams found: {summary.get('total_charts_diagrams', 0)}")
        
        # Test table extraction specifically
        tables = visual_content.get('tables', [])
        if tables:
            print(f"\n📋 Table Details:")
            for i, table in enumerate(tables):
                print(f"   Table {i+1} (Page {table['page']}):")
                print(f"   Method: {table['extraction_method']}")
                print(f"   Rows: {len(table['data']) if table['data'] else 0}")
                if table.get('text_representation'):
                    print(f"   Preview: {table['text_representation'][:100]}...")
        
        # Test image extraction
        images = visual_content.get('images', [])
        if images:
            print(f"\n🖼️  Image Details:")
            for i, image in enumerate(images):
                print(f"   Image {i+1} (Page {image['page']}):")
                print(f"   Size: {image['width']}x{image['height']}")
                print(f"   Format: {image['format']}")
        
        # Test chart detection
        charts = visual_content.get('charts_diagrams', [])
        if charts:
            print(f"\n📈 Chart/Diagram Details:")
            for i, chart in enumerate(charts):
                print(f"   Chart {i+1} (Page {chart['page']}):")
                print(f"   Description: {chart['description']}")
                print(f"   Confidence: {chart['confidence']}")
        
        return len(tables) > 0 or len(images) > 0 or len(charts) > 0
        
    except Exception as e:
        print(f"❌ Visual extraction failed: {e}")
        import traceback
        print(f"Error details: {traceback.format_exc()}")
        return False

def test_document_extractor_integration():
    """Test the integration with document extractor"""
    print(f"\n🔍 Testing Document Extractor Integration")
    print("=" * 40)
    
    try:
        from document_extractor import extract_document_with_visual_content
        
        # Create a simple test PDF
        test_pdf = create_test_pdf_with_table()
        if not test_pdf:
            return False
        
        # Test extraction with visual content
        chunks = extract_document_with_visual_content(
            test_pdf,
            extract_tables=True,
            extract_images=True,
            extract_charts=True
        )
        
        print(f"📄 Extracted {len(chunks)} total chunks")
        
        # Analyze chunk types
        text_chunks = [c for c in chunks if c.get('content_type') != 'table' and c.get('content_type') != 'image']
        table_chunks = [c for c in chunks if c.get('content_type') == 'table']
        image_chunks = [c for c in chunks if c.get('content_type') == 'image']
        
        print(f"   Text chunks: {len(text_chunks)}")
        print(f"   Table chunks: {len(table_chunks)}")
        print(f"   Image chunks: {len(image_chunks)}")
        
        # Show sample chunks
        if table_chunks:
            print(f"\n📋 Sample Table Chunk:")
            table_chunk = table_chunks[0]
            print(f"   ID: {table_chunk.get('chunk_id')}")
            print(f"   Text: {table_chunk.get('text', '')[:200]}...")
            if 'metadata' in table_chunk and 'markdown_table' in table_chunk['metadata']:
                print(f"   Has markdown table: Yes")
        
        # Clean up
        if os.path.exists(test_pdf):
            os.remove(test_pdf)
        
        return len(chunks) > 0
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        print(f"Error details: {traceback.format_exc()}")
        return False

def main():
    """Main test function"""
    print("🧪 Comprehensive Visual Content Extraction Test")
    print("=" * 60)
    
    results = {}
    
    # Test 1: Implementation check
    results['implementation'] = test_visual_extraction_implementation()
    
    # Test 2: Library capabilities
    test_library_capabilities()
    
    # Test 3: Visual extraction on PDF
    test_pdf = create_test_pdf_with_table()
    if test_pdf:
        results['pdf_extraction'] = test_visual_extraction_on_pdf(test_pdf)
        # Clean up
        if os.path.exists(test_pdf):
            os.remove(test_pdf)
    else:
        results['pdf_extraction'] = False
    
    # Test 4: Document extractor integration
    results['integration'] = test_document_extractor_integration()
    
    # Summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("\n🎉 All tests passed! Visual content extraction is working properly.")
        print("\nFeatures available:")
        print("- ✅ PDF table extraction using pdfplumber and PyMuPDF")
        print("- ✅ PDF image extraction using PyMuPDF")
        print("- ✅ Chart/diagram detection using PyMuPDF")
        print("- ✅ Integration with document upload endpoint")
        print("- ✅ Structured data storage in Supabase")
    else:
        print(f"\n⚠️  {total_tests - total_passed} test(s) failed. Visual extraction may not be fully functional.")

if __name__ == "__main__":
    main() 