"""
<PERSON><PERSON><PERSON> to test the text search functions in the database for RailGPT.
This script will test the text search functions with a sample query.
"""
import os
import logging
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def test_text_search_functions():
    """Test the text search functions with a sample query."""
    logger.info("Testing text search functions with a sample query...")
    
    # Sample queries
    test_queries = [
        "What is the Rapid Response app?",
        "What is VASP and who developed it?",
        "What is the full form of ACP?",
        "What is the full form of FSDS?"
    ]
    
    for query in test_queries:
        logger.info(f"\n=== Testing query: '{query}' ===\n")
        
        # Test text search for document chunks
        logger.info("Testing text_search_document_chunks...")
        
        text_doc_query = f"""
        SELECT * FROM text_search_document_chunks(
            '{query}',
            5
        );
        """
        
        try:
            result = supabase.execute_query(text_doc_query)
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error testing text_search_document_chunks: {result['error']}")
            else:
                logger.info(f"text_search_document_chunks returned {len(result)} results")
                for i, item in enumerate(result[:3]):  # Show first 3 results
                    similarity = item.get("similarity", 0)
                    text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
                    logger.info(f"Result {i+1}: similarity={similarity:.4f}")
                    logger.info(f"Text snippet: {text_snippet}")
        except Exception as e:
            logger.error(f"Error testing text_search_document_chunks: {str(e)}")
        
        # Test text search for website chunks
        logger.info("Testing text_search_website_chunks...")
        
        text_web_query = f"""
        SELECT * FROM text_search_website_chunks(
            '{query}',
            5
        );
        """
        
        try:
            result = supabase.execute_query(text_web_query)
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error testing text_search_website_chunks: {result['error']}")
            else:
                logger.info(f"text_search_website_chunks returned {len(result)} results")
                for i, item in enumerate(result[:3]):  # Show first 3 results
                    similarity = item.get("similarity", 0)
                    text_snippet = item.get("text", "")[:100] + "..." if item.get("text") else "..."
                    logger.info(f"Result {i+1}: similarity={similarity:.4f}")
                    logger.info(f"Text snippet: {text_snippet}")
        except Exception as e:
            logger.error(f"Error testing text_search_website_chunks: {str(e)}")

def main():
    """Main function to test text search functions."""
    test_text_search_functions()

if __name__ == "__main__":
    main()
