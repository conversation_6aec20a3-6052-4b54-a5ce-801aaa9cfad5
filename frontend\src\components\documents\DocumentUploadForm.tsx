import React, { useState, useEffect } from 'react';
import { uploadDocument } from '../../services/api';
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { DocumentCategory, CategoryHierarchy, CategoryCreate } from '../../types/documents';
import { getCategories, createCategory } from '../../services/categoryApi';

// Component for uploading documents with category selection
interface FormErrors {
  documentName?: string;
  mainCategory?: string;
  category?: string;
  file?: string;
}

const DocumentUploadForm: React.FC = (): React.ReactElement => {
  // Form state
  const [documentName, setDocumentName] = useState('');
  const [mainCategory, setMainCategory] = useState('');
  const [category, setCategory] = useState('');
  const [subCategory, setSubCategory] = useState('');
  const [minorCategory, setMinorCategory] = useState('');
  const [file, setFile] = useState<File | null>(null);

  // UI state
  const [isUploading, setIsUploading] = useState(false);
  const [newCategoryInput, setNewCategoryInput] = useState('');
  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);
  const [categoryType, setCategoryType] = useState<'Main' | 'Category' | 'Sub' | 'Minor'>('Main');

  // Upload status tracking
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'processing' | 'success' | 'error'>('idle');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [extractedChunks, setExtractedChunks] = useState<any[]>([]);

  // Form validation and feedback
  const [errors, setErrors] = useState<FormErrors>({});
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Categories from API
  const [allCategories, setAllCategories] = useState<CategoryHierarchy[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Helper functions to filter categories by type
  const getMainCategories = () => allCategories.filter(cat => cat.type === 'main_category');
  const getCategoriesByParent = (parentId: string) => allCategories.filter(cat => cat.parent_id === parentId);
  const getSubCategoriesByParent = (parentId: string) => allCategories.filter(cat => cat.parent_id === parentId);
  const getMinorCategoriesByParent = (parentId: string) => allCategories.filter(cat => cat.parent_id === parentId);

  // Load categories from API
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoadingCategories(true);
        const categories = await getCategories();
        setAllCategories(categories);
      } catch (error) {
        console.warn('Failed to load categories:', error);
        setErrorMessage('Failed to load categories. Category selection may be limited.');
      } finally {
        setLoadingCategories(false);
      }
    };

    loadCategories();
  }, []);

  // Effect to validate form
  useEffect(() => {
    let isMounted = true;

    const newErrors: FormErrors = {};

    if (touched.documentName && !documentName.trim()) {
      newErrors.documentName = 'Document name is required';
    }

    if (touched.mainCategory && !mainCategory) {
      newErrors.mainCategory = 'Main category is required';
    }

    if (touched.category && !category) {
      newErrors.category = 'Category is required';
    }

    if (touched.file && !file) {
      newErrors.file = 'Document file is required';
    } else if (file) {
      // Validate file type
      const allowedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png',
        'text/plain'
      ];

      if (!allowedTypes.includes(file.type)) {
        newErrors.file = 'File type not supported. Please upload PDF, DOCX, XLSX, JPG, PNG, or TXT';
      }

      // Validate file size (200MB max)
      const maxSize = 200 * 1024 * 1024; // 200MB
      if (file.size > maxSize) {
        newErrors.file = 'File too large. Maximum size is 200MB';
      }
    }

    // Only update state if component is still mounted
    if (isMounted) {
      setErrors(newErrors);
    }

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [documentName, mainCategory, category, file, touched]);

  // Mark fields as touched when user interacts with them
  const markAsTouched = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };



  // useEffect to clear success message after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  // useEffect to clear error message after 5 seconds
  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => {
        setErrorMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      markAsTouched('file');

      // Set document name from filename if empty
      if (!documentName.trim()) {
        // Remove extension and replace underscores/hyphens with spaces
        const nameFromFile = selectedFile.name.replace(/\.[^/.]+$/, '').replace(/[_-]/g, ' ');
        setDocumentName(nameFromFile);
        markAsTouched('documentName');
      }
    }
  };

  const handleNewCategorySubmit = async () => {
    if (!newCategoryInput.trim()) return;

    setIsUploading(true);
    try {
      // Determine parent ID based on category type
      let parentId = null;
      let categoryTypeForAPI: 'main_category' | 'category' | 'sub_category' | 'minor_category';

      if (categoryType === 'Main') {
        categoryTypeForAPI = 'main_category';
      } else if (categoryType === 'Category') {
        categoryTypeForAPI = 'category';
        parentId = mainCategory;
      } else if (categoryType === 'Sub') {
        categoryTypeForAPI = 'sub_category';
        parentId = category;
      } else if (categoryType === 'Minor') {
        categoryTypeForAPI = 'minor_category';
        parentId = subCategory;
      } else {
        // Default fallback
        categoryTypeForAPI = 'main_category';
      }

      // Create category using API
      const newCategoryData: CategoryCreate = {
        name: newCategoryInput,
        type: categoryTypeForAPI,
        parent_id: parentId,
        description: ''
      };

      const response = await createCategory(newCategoryData);

      // Reload categories to get the updated list
      const updatedCategories = await getCategories();
      setAllCategories(updatedCategories);

      // Set the newly created category as selected
      if (categoryType === 'Main') {
        setMainCategory(response.category?.id || '');
      } else if (categoryType === 'Category') {
        setCategory(response.category?.id || '');
      } else if (categoryType === 'Sub') {
        setSubCategory(response.category?.id || '');
      } else if (categoryType === 'Minor') {
        setMinorCategory(response.category?.id || '');
      }

      setSuccessMessage(`Created new ${categoryType} category: ${newCategoryInput}`);
    } catch (error) {
      setErrorMessage(`Failed to create category: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUploading(false);
      setNewCategoryInput('');
      setShowNewCategoryInput(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();

    // Mark all fields as touched to show any errors
    markAsTouched('documentName');
    markAsTouched('mainCategory');
    markAsTouched('category');
    markAsTouched('file');

    // Check if there are any errors
    if (Object.keys(errors).length > 0 || !file) {
      setErrorMessage('Please correct the errors before submitting');
      return;
    }

    // Track if component is still mounted
    let isMounted = true;
    let progressInterval: NodeJS.Timeout | null = null;

    // Update UI state
    const safeSetState = (updater: () => void) => {
      if (isMounted) {
        updater();
      }
    };

    safeSetState(() => {
      setIsUploading(true);
      setUploadStatus('uploading');
      setUploadProgress(10);
      setSuccessMessage('');
      setErrorMessage('');
      setExtractedChunks([]);
    });



    try {
      // Simulate upload progress updates (in a real app, use XHR or fetch with progress events)
      progressInterval = setInterval(() => {
        if (isMounted) {
          setUploadProgress(prev => {
            const newProgress = prev + 5;
            if (newProgress >= 90) {
              if (progressInterval) clearInterval(progressInterval);
              return 90; // Hold at 90% until processing is complete
            }
            return newProgress;
          });
        }
      }, 300);



      // Upload the document
      safeSetState(() => setUploadStatus('uploading'));

      // Upload the document using the API function
      const response = await uploadDocument(file, "<EMAIL>");

      // Clear the progress interval if it exists
      if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
      }

      // Only update state if component is still mounted
      if (!isMounted) return;

      if (response.success) {
        // Set progress to 100%
        setUploadProgress(100);
        setUploadStatus('success');

        // Store extracted chunks if available
        if (response.chunks && response.chunks.length > 0) {
          setExtractedChunks(response.chunks);
          console.log('Extracted chunks:', response.chunks);
        }

        // Create event data for custom event
        const eventData = {
          detail: {
            documentName,
            mainCategory,
            category,
            subCategory,
            minorCategory,
            file,
            uploadedAt: new Date().toISOString(),
            id: response.data?.id || `doc-${Date.now()}`,
            status: 'Extracted', // Update to real status from backend
            fileType: file.name.split('.').pop()?.toLowerCase(),
            qualityScore: 90, // This would come from backend in real app
            chunks: response.chunks || [],
            extractedContent: response.chunks ? response.chunks.map((chunk: any) => chunk.text).join('\n\n') : '',
            chunks_extracted: response.chunks_extracted || 0
          }
        };

        // Dispatch event to notify the DocumentsPage
        const event = new CustomEvent('documentUploaded', { detail: eventData.detail });
        window.dispatchEvent(event);

        // Set success message (ensure error is cleared)
        setErrorMessage('');
        setSuccessMessage(`Document "${documentName}" uploaded successfully!`);

        // Auto-clear success message after 5 seconds
        const clearSuccessMessage = () => {
          if (isMounted) {
            setSuccessMessage('');
          }
        };

        setTimeout(clearSuccessMessage, 5000);

        // Reset form after successful upload
        const resetForm = () => {
          if (isMounted) {
            setDocumentName('');
            setMainCategory('');
            setCategory('');
            setSubCategory('');
            setMinorCategory('');
            setFile(null);
            setUploadStatus('idle');
            setUploadProgress(0);
            setExtractedChunks([]);
            setIsUploading(false);
            setTouched({});
          }
        };

        setTimeout(resetForm, 3000);
      } else {
        // Handle upload error
        setUploadStatus('error');
        setErrorMessage(response.message || 'Failed to upload document');
      }
    } catch (error) {
      console.error('Upload failed:', error);
      if (isMounted) {
        setUploadStatus('error');
        setErrorMessage(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      if (isMounted) {
        setIsUploading(false);
      }
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 transition-colors duration-300">
      <h2 className="text-lg font-semibold mb-4 text-gray-900">Upload Document</h2>

      {/* Success message */}
      {successMessage && (
        <div className="mb-4">
          <Alert variant="success">
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{successMessage}</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Error message */}
      {errorMessage && (
        <div className="mb-4">
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Document Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Document Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={documentName}
            onChange={(e) => setDocumentName(e.target.value)}
            onBlur={() => markAsTouched('documentName')}
            className={`w-full p-2 border ${errors.documentName ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}
            required
          />
          {errors.documentName && (
            <p className="mt-1 text-sm text-red-500">{errors.documentName}</p>
          )}
        </div>

        {/* Main Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Main Category <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2">
            <select
              value={mainCategory}
              onChange={(e) => {
                setMainCategory(e.target.value);
                // Reset dependent categories when main category changes
                setCategory('');
                setSubCategory('');
                setMinorCategory('');
              }}
              onBlur={() => markAsTouched('mainCategory')}
              className={`flex-1 p-2 border ${errors.mainCategory ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}
              required
              disabled={loadingCategories}
            >
              <option value="">{loadingCategories ? 'Loading...' : 'Select Main Category'}</option>
              {getMainCategories().map((cat) => (
                <option key={cat.id} value={cat.id}>
                  {cat.name}
                </option>
              ))}
            </select>
            <button
              type="button"
              onClick={() => {
                setCategoryType('Main');
                setShowNewCategoryInput(true);
              }}
              className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300"
            >
              + New
            </button>
          </div>
          {errors.mainCategory && (
            <p className="mt-1 text-sm text-red-500">{errors.mainCategory}</p>
          )}
        </div>

        {/* Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2">
            <select
              value={category}
              onChange={(e) => {
                setCategory(e.target.value);
                // Reset dependent categories when category changes
                setSubCategory('');
                setMinorCategory('');
              }}
              onBlur={() => markAsTouched('category')}
              className={`flex-1 p-2 border ${errors.category ? 'border-red-500' : 'border-gray-300'} bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300`}
              required
              disabled={!mainCategory || loadingCategories}
            >
              <option value="">Select Category</option>
              {mainCategory && getCategoriesByParent(mainCategory)
                .filter(cat => cat.type === 'category')
                .map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.name}
                  </option>
                ))}
            </select>
            <button
              type="button"
              onClick={() => {
                setCategoryType('Category');
                setShowNewCategoryInput(true);
              }}
              className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300"
              disabled={!mainCategory}
            >
              + New
            </button>
          </div>
          {errors.category && (
            <p className="mt-1 text-sm text-red-500">{errors.category}</p>
          )}
        </div>

        {/* Sub Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sub Category
          </label>
          <div className="flex gap-2">
            <select
              value={subCategory}
              onChange={(e) => {
                setSubCategory(e.target.value);
                // Reset dependent categories when sub category changes
                setMinorCategory('');
              }}
              className="flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300"
              disabled={!category || loadingCategories}
            >
              <option value="">Select Sub Category</option>
              {category && getSubCategoriesByParent(category)
                .filter(cat => cat.type === 'sub_category')
                .map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.name}
                  </option>
                ))}
            </select>
            <button
              type="button"
              onClick={() => {
                setCategoryType('Sub');
                setShowNewCategoryInput(true);
              }}
              className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300"
              disabled={!category}
            >
              + New
            </button>
          </div>
        </div>

        {/* Minor Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Minor Category
          </label>
          <div className="flex gap-2">
            <select
              value={minorCategory}
              onChange={(e) => setMinorCategory(e.target.value)}
              className="flex-1 p-2 border border-gray-300 bg-white text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300"
              disabled={!subCategory || loadingCategories}
            >
              <option value="">Select Minor Category</option>
              {subCategory && getMinorCategoriesByParent(subCategory)
                .filter(cat => cat.type === 'minor_category')
                .map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.name}
                  </option>
                ))}
            </select>
            <button
              type="button"
              onClick={() => {
                setCategoryType('Minor');
                setShowNewCategoryInput(true);
              }}
              className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300"
              disabled={!subCategory}
            >
              + New
            </button>
          </div>
        </div>

        {/* File Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Upload Document <span className="text-red-500">*</span>
          </label>
          <div className="flex items-center justify-center w-full">
            <label
              className={`flex flex-col w-full h-32 border-2 ${errors.file ? 'border-red-500' : 'border-blue-300'} border-dashed hover:bg-gray-50 hover:border-blue-500 rounded-lg cursor-pointer`}
            >
              <div className="flex flex-col items-center justify-center pt-7">
                <svg
                  className="w-8 h-8 text-gray-400 group-hover:text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  ></path>
                </svg>
                <p className="pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600">
                  {file ? file.name : 'Attach document (PDF, DOCX, XLSX, etc.)'}
                </p>
              </div>
              <input
                type="file"
                className="opacity-0"
                accept=".pdf,.docx,.xlsx,.txt,.png,.jpg,.jpeg"
                onChange={handleFileChange}
                onBlur={() => markAsTouched('file')}
                required
              />
            </label>
          </div>
          {errors.file && (
            <p className="mt-1 text-sm text-red-500">{errors.file}</p>
          )}
        </div>

        {/* Submit Button */}
        <div>
          <button
            type="submit"
            disabled={isUploading}
            className={`w-full px-4 py-2 text-white font-medium rounded-md ${
              isUploading ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            {isUploading ? 'Uploading...' : 'Upload Document'}
          </button>
        </div>

        {/* Upload Progress */}
        {uploadStatus !== 'idle' && (
          <div className="mt-4">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">
                {uploadStatus === 'uploading' ? 'Uploading...' :
                 uploadStatus === 'processing' ? 'Processing...' :
                 uploadStatus === 'success' ? 'Upload Complete' :
                 'Upload Failed'}
              </span>
              <span className="text-sm font-medium text-gray-700">{uploadProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  uploadStatus === 'error' ? 'bg-red-600' :
                  uploadStatus === 'success' ? 'bg-green-600' : 'bg-blue-600'
                }`}
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Extracted Chunks Preview (shown after successful upload) */}
        {uploadStatus === 'success' && extractedChunks.length > 0 && (
          <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Extracted Content Preview:</h3>
            <div className="max-h-40 overflow-y-auto text-sm text-gray-600">
              {extractedChunks.slice(0, 3).map((chunk, index) => (
                <div key={index} className="mb-2 p-2 bg-white rounded border border-gray-200">
                  <p className="text-xs text-gray-500 mb-1">
                    {chunk.source_type === 'document' ?
                      `Page ${chunk.page || 'N/A'}` :
                      `Source: ${chunk.source || 'Unknown'}`}
                  </p>
                  <p>{chunk.text?.substring(0, 150)}...</p>
                </div>
              ))}
              {extractedChunks.length > 3 && (
                <p className="text-xs text-gray-500 text-center mt-2">
                  + {extractedChunks.length - 3} more chunks not shown
                </p>
              )}
            </div>
          </div>
        )}
      </form>

      {/* New Category Modal */}
      {showNewCategoryInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">Add New {categoryType} Category</h3>
            <input
              type="text"
              value={newCategoryInput}
              onChange={(e) => setNewCategoryInput(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
              placeholder={`Enter ${categoryType} Category name`}
              autoFocus
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowNewCategoryInput(false)}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
              >
                Cancel
              </button>
              <button
                onClick={handleNewCategorySubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Add Category
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentUploadForm;
