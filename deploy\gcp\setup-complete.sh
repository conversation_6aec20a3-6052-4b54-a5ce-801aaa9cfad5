#!/bin/bash

# Complete RailGPT GCP Setup Script
# This script sets up the entire RailGPT application on Google Cloud Platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
PROJECT_ID=${PROJECT_ID:-"railgpt-production"}
REGION=${REGION:-"us-central1"}
DOMAIN=${DOMAIN:-""}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command -v gcloud &> /dev/null; then
        missing_deps+=("gcloud")
    fi
    
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        print_status "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    # Check if logged in to gcloud
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        print_error "Not logged in to Google Cloud. Run 'gcloud auth login' first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Create or setup project
setup_project() {
    print_status "Setting up Google Cloud project: $PROJECT_ID"
    
    # Check if project exists
    if gcloud projects describe $PROJECT_ID &> /dev/null; then
        print_status "Project $PROJECT_ID already exists"
    else
        print_status "Creating new project: $PROJECT_ID"
        gcloud projects create $PROJECT_ID --name="RailGPT Production"
        
        print_warning "Please enable billing for the project in the Google Cloud Console:"
        print_warning "https://console.cloud.google.com/billing/linkedaccount?project=$PROJECT_ID"
        read -p "Press Enter after enabling billing..."
    fi
    
    # Set project
    gcloud config set project $PROJECT_ID
    
    # Enable required APIs
    print_status "Enabling required APIs..."
    gcloud services enable cloudbuild.googleapis.com
    gcloud services enable run.googleapis.com
    gcloud services enable storage.googleapis.com
    gcloud services enable compute.googleapis.com
    gcloud services enable containerregistry.googleapis.com
    gcloud services enable dns.googleapis.com
    
    print_success "Project setup completed"
}

# Check environment variables
check_environment() {
    print_status "Checking environment variables..."
    
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating template..."
        cat > .env << EOF
# Supabase Configuration
SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co
SUPABASE_KEY=your_service_key_here
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA

# API Keys (at least GEMINI_API_KEY is required)
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GROQ_API_KEY=your_groq_api_key_here

# GCP Configuration
PROJECT_ID=$PROJECT_ID
REGION=$REGION
DOMAIN=$DOMAIN
EOF
        print_warning "Please edit the .env file with your actual API keys"
        print_warning "Then run this script again"
        exit 1
    fi
    
    # Source the .env file
    source .env
    
    # Check required variables
    local required_vars=("SUPABASE_URL" "SUPABASE_KEY" "GEMINI_API_KEY")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ] || [ "${!var}" = "your_${var,,}_here" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_error "Missing or placeholder values for: ${missing_vars[*]}"
        print_status "Please update your .env file with actual values"
        exit 1
    fi
    
    print_success "Environment variables are configured"
}

# Deploy backend
deploy_backend() {
    print_status "Deploying backend to Cloud Run..."
    
    ./deploy/gcp/deploy-backend.sh \
        --project $PROJECT_ID \
        --region $REGION \
        --service-name railgpt-backend
    
    # Get backend URL
    BACKEND_URL=$(gcloud run services describe railgpt-backend --region $REGION --format="value(status.url)")
    
    print_success "Backend deployed successfully"
    print_status "Backend URL: $BACKEND_URL"
}

# Deploy frontend
deploy_frontend() {
    print_status "Deploying frontend to Cloud Storage..."
    
    if [ ! -z "$DOMAIN" ]; then
        ./deploy/gcp/deploy-frontend.sh \
            --project $PROJECT_ID \
            --region $REGION \
            --backend-url $BACKEND_URL \
            --domain $DOMAIN
    else
        ./deploy/gcp/deploy-frontend.sh \
            --project $PROJECT_ID \
            --region $REGION \
            --backend-url $BACKEND_URL
    fi
    
    print_success "Frontend deployed successfully"
}

# Setup monitoring
setup_monitoring() {
    print_status "Setting up monitoring and alerts..."
    
    # Enable monitoring API
    gcloud services enable monitoring.googleapis.com
    
    # Create uptime check for backend
    if [ ! -z "$BACKEND_URL" ]; then
        print_status "Creating uptime check for backend..."
        
        # Extract hostname from URL
        BACKEND_HOST=$(echo $BACKEND_URL | sed 's|https\?://||' | sed 's|/.*||')
        
        gcloud alpha monitoring uptime create \
            --display-name="RailGPT Backend Health Check" \
            --hostname=$BACKEND_HOST \
            --path=/health \
            --port=443 \
            --protocol=HTTPS \
            --timeout=10s \
            --period=60s || print_warning "Uptime check creation failed (might already exist)"
    fi
    
    # Create uptime check for frontend
    if [ ! -z "$DOMAIN" ]; then
        print_status "Creating uptime check for frontend..."
        
        gcloud alpha monitoring uptime create \
            --display-name="RailGPT Frontend Health Check" \
            --hostname=$DOMAIN \
            --path=/ \
            --port=443 \
            --protocol=HTTPS \
            --timeout=10s \
            --period=60s || print_warning "Uptime check creation failed (might already exist)"
    fi
    
    print_success "Monitoring setup completed"
}

# Setup backup and security
setup_security() {
    print_status "Setting up security and backup..."
    
    # Create service account for application
    SERVICE_ACCOUNT_NAME="railgpt-app"
    SERVICE_ACCOUNT_EMAIL="$SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com"
    
    if gcloud iam service-accounts describe $SERVICE_ACCOUNT_EMAIL &> /dev/null; then
        print_warning "Service account already exists"
    else
        gcloud iam service-accounts create $SERVICE_ACCOUNT_NAME \
            --display-name="RailGPT Application Service Account" \
            --description="Service account for RailGPT application"
        
        # Grant necessary permissions
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
            --role="roles/storage.objectViewer"
        
        print_success "Service account created and configured"
    fi
    
    # Setup Cloud Armor (DDoS protection) if using load balancer
    if [ ! -z "$DOMAIN" ]; then
        print_status "Setting up Cloud Armor security policy..."
        
        SECURITY_POLICY_NAME="railgpt-security-policy"
        
        if gcloud compute security-policies describe $SECURITY_POLICY_NAME --global &> /dev/null; then
            print_warning "Security policy already exists"
        else
            gcloud compute security-policies create $SECURITY_POLICY_NAME \
                --description="RailGPT security policy"
            
            # Add rate limiting rule
            gcloud compute security-policies rules create 1000 \
                --security-policy=$SECURITY_POLICY_NAME \
                --expression="true" \
                --action="rate-based-ban" \
                --rate-limit-threshold-count=100 \
                --rate-limit-threshold-interval-sec=60 \
                --ban-duration-sec=600 \
                --conform-action=allow \
                --exceed-action=deny-429 \
                --enforce-on-key=IP
            
            print_success "Security policy created"
        fi
    fi
}

# Run health checks
run_health_checks() {
    print_status "Running health checks..."
    
    # Check backend health
    if [ ! -z "$BACKEND_URL" ]; then
        if curl -f "$BACKEND_URL/health" > /dev/null 2>&1; then
            print_success "Backend health check passed"
        else
            print_warning "Backend health check failed"
        fi
    fi
    
    # Check frontend
    if [ ! -z "$DOMAIN" ]; then
        if curl -f "https://$DOMAIN" > /dev/null 2>&1; then
            print_success "Frontend health check passed"
        else
            print_warning "Frontend health check failed (DNS might still be propagating)"
        fi
    fi
}

# Display deployment summary
show_summary() {
    echo
    print_success "🎉 RailGPT deployment completed successfully!"
    echo
    print_status "📋 Deployment Summary:"
    echo "  Project ID: $PROJECT_ID"
    echo "  Region: $REGION"
    
    if [ ! -z "$BACKEND_URL" ]; then
        echo "  Backend URL: $BACKEND_URL"
        echo "  API Documentation: $BACKEND_URL/docs"
    fi
    
    if [ ! -z "$DOMAIN" ]; then
        echo "  Frontend URL: https://$DOMAIN"
        echo "  Load Balancer IP: $(gcloud compute forwarding-rules describe railgpt-https-rule --global --format="value(IPAddress)" 2>/dev/null || echo "Not available")"
    else
        BUCKET_NAME=$(gsutil ls -p $PROJECT_ID | grep railgpt-frontend | head -1 | sed 's|gs://||' | sed 's|/||')
        if [ ! -z "$BUCKET_NAME" ]; then
            echo "  Frontend URL: https://storage.googleapis.com/$BUCKET_NAME/index.html"
        fi
    fi
    
    echo
    print_status "🔧 Management Commands:"
    echo "  View backend logs: gcloud logs read --service=railgpt-backend --limit=50"
    echo "  Update backend: ./deploy/gcp/deploy-backend.sh"
    echo "  Update frontend: ./deploy/gcp/deploy-frontend.sh"
    echo "  Monitor resources: https://console.cloud.google.com/monitoring"
    
    echo
    print_status "📝 Next Steps:"
    echo "  1. Test all application functionality"
    
    if [ ! -z "$DOMAIN" ]; then
        echo "  2. Wait for SSL certificate provisioning (up to 24 hours)"
        echo "  3. Configure DNS if not already done"
    fi
    
    echo "  4. Set up monitoring alerts"
    echo "  5. Configure backup policies"
    echo "  6. Review security settings"
    
    echo
    print_status "💰 Cost Management:"
    echo "  Monitor costs: https://console.cloud.google.com/billing"
    echo "  Set up budget alerts: https://console.cloud.google.com/billing/budgets"
    
    if [ ! -z "$DOMAIN" ]; then
        echo
        print_warning "🔒 SSL Certificate:"
        print_warning "If using a custom domain, SSL certificate provisioning can take up to 24 hours."
        print_warning "Ensure your DNS is properly configured and pointing to the load balancer IP."
    fi
}

# Main execution
main() {
    echo "========================================="
    echo "     RailGPT Complete GCP Setup         "
    echo "========================================="
    echo
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --project)
                PROJECT_ID="$2"
                shift 2
                ;;
            --region)
                REGION="$2"
                shift 2
                ;;
            --domain)
                DOMAIN="$2"
                shift 2
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --project PROJECT_ID    Google Cloud project ID"
                echo "  --region REGION         Deployment region (default: us-central1)"
                echo "  --domain DOMAIN         Custom domain for HTTPS"
                echo "  --help                  Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    print_status "Configuration:"
    print_status "  Project ID: $PROJECT_ID"
    print_status "  Region: $REGION"
    print_status "  Domain: ${DOMAIN:-"Not specified (will use Cloud Storage URL)"}"
    echo
    
    # Change to script directory
    cd "$(dirname "$0")/../.."
    
    # Run setup steps
    check_prerequisites
    setup_project
    check_environment
    deploy_backend
    deploy_frontend
    setup_monitoring
    setup_security
    run_health_checks
    show_summary
}

# Run main function with all arguments
main "$@"
