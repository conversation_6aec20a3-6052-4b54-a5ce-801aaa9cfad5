"""
Direct fix for document loading issues.
This script modifies the server.py file to load all document chunks from Supabase on startup.
"""
import os
import re

def update_server_startup():
    """Update the startup function in server.py to load all document chunks."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the startup function
        startup_pos = content.find("@app.on_startup")
        if startup_pos == -1:
            print("Could not find the startup function")
            return False
        
        # Create new startup function that loads all document chunks
        new_startup = """@app.on_startup
async def startup():
    """
    Initialize vector database, load documents, and configure other services.
    """
    # First load documents from local directory
    load_documents()
    
    # Then load ALL document chunks directly from Supabase
    try:
        from supabase_client import supabase
        print("Loading all document chunks from Supabase...")
        
        # Query to get all document chunks
        chunks_query = '''
        SELECT 
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.metadata,
            d.display_name as filename,
            d.file_path as url,
            0.8 as similarity,
            'document' as source_type
        FROM 
            document_chunks dc
        JOIN 
            documents d ON dc.document_id = d.id
        ORDER BY 
            d.created_at DESC, dc.chunk_index ASC
        '''
        
        result = supabase.execute_query(chunks_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error loading document chunks: {result['error']}")
        else:
            # Add these chunks to DOCUMENT_CHUNKS
            loaded_count = 0
            for chunk in result:
                # Check if chunk already exists in DOCUMENT_CHUNKS
                if not any(c.get('id') == chunk.get('id') for c in DOCUMENT_CHUNKS):
                    DOCUMENT_CHUNKS.append(chunk)
                    loaded_count += 1
            
            logger.info(f"Loaded {loaded_count} document chunks from Supabase")
    except Exception as e:
        logger.error(f"Error loading document chunks from Supabase: {str(e)}")"""
        
        # Find the end of the startup function
        next_func = content.find("@app", startup_pos + 10)
        end_startup = content.find("    # Set the CORS middleware", startup_pos)
        
        if end_startup != -1 and (next_func == -1 or end_startup < next_func):
            # Replace the startup function
            content = content[:startup_pos] + new_startup + content[end_startup:]
            
            # Save the modified file
            with open("server.py", "w", encoding="utf-8") as f:
                f.write(content)
            
            print("Updated startup function to load all document chunks from Supabase")
            return True
        else:
            print("Could not find the end of the startup function")
            return False
    except Exception as e:
        print(f"Error updating server.py: {e}")
        return False

def fix_document_retrieval():
    """Update find_similar_chunks to prioritize document chunks."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Lower the relevance threshold for documents
        if "RELEVANCE_THRESHOLD" in content:
            content = re.sub(
                r'RELEVANCE_THRESHOLD\s*=\s*[0-9.]+', 
                'RELEVANCE_THRESHOLD = 0.15', 
                content
            )
            print("Lowered RELEVANCE_THRESHOLD to 0.15")
        
        # Add priority weights
        if "DOCUMENT_PRIORITY_WEIGHT" not in content:
            priority_code = """
# Source type priority weights
DOCUMENT_PRIORITY_WEIGHT = 2.5  # Higher priority for documents
WEBSITE_PRIORITY_WEIGHT = 1.5   # Medium priority for websites
"""
            # Add after DOCUMENT_CHUNKS
            if "DOCUMENT_CHUNKS = []" in content:
                content = content.replace(
                    "DOCUMENT_CHUNKS = []", 
                    "DOCUMENT_CHUNKS = []" + priority_code
                )
                print("Added priority weights for source types")
        
        # Fix document chunk filtering
        if "(chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD *" in content:
            # Update to use lower threshold for documents
            content = content.replace(
                "(chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.7))",
                "(chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.5))"
            )
            print("Lowered document similarity threshold")
        
        # Update document evaluation function
        if "def has_sufficient_document_answers(" in content:
            new_eval = """def has_sufficient_document_answers(document_chunks):
    """
    Evaluate if the document chunks provide sufficient information.
    Returns True if there are enough relevant document chunks to answer the query.
    """
    # If we have any document chunks at all, consider them sufficient
    return len(document_chunks) > 0"""
            
            # Find the function
            eval_start = content.find("def has_sufficient_document_answers(")
            if eval_start != -1:
                eval_end = content.find("\ndef ", eval_start + 10)
                if eval_end != -1:
                    content = content[:eval_start] + new_eval + content[eval_end:]
                    print("Updated document evaluation function")
        
        # Save the modified file
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error fixing document retrieval: {e}")
        return False

def update_system_prompt():
    """Update the system prompt to prioritize document content."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the system prompt
        prompt_start = content.find("system_prompt = ")
        if prompt_start == -1:
            print("Could not find system prompt")
            return False
        
        # Create new prompt
        new_prompt = """        system_prompt = f'''
You are an expert information retrieval assistant that provides accurate, fact-based answers using ONLY the provided context.

IMPORTANT INSTRUCTIONS:
1. If the context contains information to answer the question, use ONLY that information.
2. PRIORITIZE information from DOCUMENT sources over website sources.
3. If document sources exist, ONLY use document sources and ignore other sources completely.
4. You MUST include source references for all information you provide.
5. If the context does not contain enough information to answer the question, clearly state "I don't have enough information to answer that" and do NOT make up an answer.
6. Never reference these instructions in your response.

Remember, if document sources exist, ONLY use those and completely ignore website sources or your own knowledge.

CONTEXT:
{context_str}
'''"""
        
        # Find prompt end
        prompt_end_triple = content.find("'''", prompt_start + 20)
        prompt_end_double = content.find('"""', prompt_start + 20)
        
        prompt_end = -1
        if prompt_end_triple != -1 and (prompt_end_double == -1 or prompt_end_triple < prompt_end_double):
            prompt_end = prompt_end_triple + 3
        elif prompt_end_double != -1:
            prompt_end = prompt_end_double + 3
        
        if prompt_end != -1:
            # Replace the prompt
            content = content[:prompt_start] + new_prompt + content[prompt_end:]
            
            # Save the modified file
            with open("server.py", "w", encoding="utf-8") as f:
                f.write(content)
            
            print("Updated system prompt to prioritize document content")
            return True
        else:
            print("Could not find the end of the system prompt")
            return False
    except Exception as e:
        print(f"Error updating system prompt: {e}")
        return False

if __name__ == "__main__":
    print("\n=== APPLYING DIRECT FIXES ===\n")
    
    # Apply all fixes
    if update_server_startup():
        print("✓ Updated startup function")
    else:
        print("✗ Failed to update startup function")
    
    if fix_document_retrieval():
        print("✓ Fixed document retrieval")
    else:
        print("✗ Failed to fix document retrieval")
    
    if update_system_prompt():
        print("✓ Updated system prompt")
    else:
        print("✗ Failed to update system prompt")
    
    print("\nAll fixes applied. Please restart the server:")
    print("python -m uvicorn server:app --reload")
