"""
<PERSON><PERSON><PERSON> to run all fixes for the Supabase integration issues.

This script:
1. Runs fix_supabase_integration.py to fix the basic integration issues
2. Runs add_hybrid_search_functions.py to add the hybrid search functions
3. Runs test_vector_db.py to test the vector database functionality
"""

import os
import sys
import logging
import subprocess
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_script(script_name):
    """Run a Python script and return the result."""
    try:
        logger.info(f"Running {script_name}...")
        
        # Run the script
        result = subprocess.run(
            [sys.executable, script_name],
            capture_output=True,
            text=True,
            check=False
        )
        
        # Log the output
        if result.stdout:
            for line in result.stdout.splitlines():
                logger.info(f"{script_name} output: {line}")
        
        # Log any errors
        if result.stderr:
            for line in result.stderr.splitlines():
                logger.error(f"{script_name} error: {line}")
        
        # Return success if the script exited with code 0
        return result.returncode == 0
    
    except Exception as e:
        logger.error(f"Error running {script_name}: {str(e)}")
        return False

def run_all_fixes():
    """Run all fixes for the Supabase integration issues."""
    logger.info("Running all fixes for Supabase integration issues...")
    
    # Step 1: Run fix_supabase_integration.py
    logger.info("\n=== Step 1: Running fix_supabase_integration.py ===")
    if not run_script("fix_supabase_integration.py"):
        logger.warning("fix_supabase_integration.py failed, continuing with other fixes...")
    
    # Step 2: Run add_hybrid_search_functions.py
    logger.info("\n=== Step 2: Running add_hybrid_search_functions.py ===")
    if not run_script("add_hybrid_search_functions.py"):
        logger.warning("add_hybrid_search_functions.py failed, continuing with other fixes...")
    
    # Step 3: Run test_vector_db.py
    logger.info("\n=== Step 3: Running test_vector_db.py ===")
    if not run_script("test_vector_db.py"):
        logger.warning("test_vector_db.py failed, continuing...")
    
    # Step 4: Verify that the fixes worked
    logger.info("\n=== Step 4: Verifying that the fixes worked ===")
    
    # Import the necessary modules
    try:
        from supabase_client import supabase
        from vector_db import VectorDB
        
        # Check if the vector database is initialized
        vector_db = VectorDB()
        if vector_db.is_initialized():
            logger.info("Vector database is initialized")
        else:
            logger.warning("Vector database is not initialized")
        
        # Check if the hybrid search functions exist
        sql = """
        SELECT EXISTS (
            SELECT 1 
            FROM pg_proc 
            WHERE proname = 'hybrid_search_document_chunks'
        );
        """
        
        result = supabase.execute_sql(sql)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error checking hybrid search functions: {result['error']}")
        elif result and result[0].get('exists'):
            logger.info("hybrid_search_document_chunks function exists")
        else:
            logger.warning("hybrid_search_document_chunks function does not exist")
        
        # Check if the website function exists
        sql = """
        SELECT EXISTS (
            SELECT 1 
            FROM pg_proc 
            WHERE proname = 'hybrid_search_website_chunks'
        );
        """
        
        result = supabase.execute_sql(sql)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error checking hybrid search functions: {result['error']}")
        elif result and result[0].get('exists'):
            logger.info("hybrid_search_website_chunks function exists")
        else:
            logger.warning("hybrid_search_website_chunks function does not exist")
    
    except Exception as e:
        logger.error(f"Error verifying fixes: {str(e)}")
    
    logger.info("\n=== All fixes completed ===")
    logger.info("Please restart the backend server to apply all fixes.")

if __name__ == "__main__":
    run_all_fixes()
