"""
<PERSON><PERSON><PERSON> to check and fix the database content for RailGPT.
This script will:
1. Check if documents and websites exist in the database
2. Verify if embeddings are present and valid
3. Regenerate embeddings for chunks without valid embeddings
4. Test search functionality with specific queries
"""
import os
import logging
import json
from typing import List, Dict, Any
import numpy as np
from dotenv import load_dotenv
from supabase_client import supabase
import llm_router

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def check_database_content():
    """Check if documents and websites exist in the database."""
    logger.info("Checking database content...")
    
    # Helper function to extract count from result
    def extract_count(result):
        if not result or not isinstance(result, list) or len(result) == 0:
            return 0
        
        if "count" in result[0]:
            count_val = result[0]["count"]
        elif "COUNT" in result[0]:
            count_val = result[0]["COUNT"]
        else:
            # Try to get the first value regardless of key
            count_val = list(result[0].values())[0] if result[0] else 0
            
        # Convert to int if it's a string
        if isinstance(count_val, str):
            try:
                return int(count_val)
            except ValueError:
                return 0
        return count_val
    
    # Check documents table
    doc_query = "SELECT COUNT(*) FROM documents"
    doc_result = supabase.execute_query(doc_query)
    
    if isinstance(doc_result, dict) and "error" in doc_result:
        logger.error(f"Error checking documents: {doc_result['error']}")
        doc_count = 0
    else:
        doc_count = extract_count(doc_result)
        logger.info(f"Found {doc_count} documents in the database")
    
    # Check document_chunks table
    chunk_query = "SELECT COUNT(*) FROM document_chunks"
    chunk_result = supabase.execute_query(chunk_query)
    
    if isinstance(chunk_result, dict) and "error" in chunk_result:
        logger.error(f"Error checking document chunks: {chunk_result['error']}")
        chunk_count = 0
    else:
        chunk_count = extract_count(chunk_result)
        logger.info(f"Found {chunk_count} document chunks in the database")
    
    # Check document_chunks with embeddings
    embed_query = "SELECT COUNT(*) FROM document_chunks WHERE embedding IS NOT NULL"
    embed_result = supabase.execute_query(embed_query)
    
    if isinstance(embed_result, dict) and "error" in embed_result:
        logger.error(f"Error checking document chunks with embeddings: {embed_result['error']}")
        embed_count = 0
    else:
        embed_count = extract_count(embed_result)
        logger.info(f"Found {embed_count} document chunks with embeddings in the database")
    
    # Check websites table
    web_query = "SELECT COUNT(*) FROM websites"
    web_result = supabase.execute_query(web_query)
    
    if isinstance(web_result, dict) and "error" in web_result:
        logger.error(f"Error checking websites: {web_result['error']}")
        web_count = 0
    else:
        web_count = extract_count(web_result)
        logger.info(f"Found {web_count} websites in the database")
    
    # Check website_chunks table
    web_chunk_query = "SELECT COUNT(*) FROM website_chunks"
    web_chunk_result = supabase.execute_query(web_chunk_query)
    
    if isinstance(web_chunk_result, dict) and "error" in web_chunk_result:
        logger.error(f"Error checking website chunks: {web_chunk_result['error']}")
        web_chunk_count = 0
    else:
        web_chunk_count = extract_count(web_chunk_result)
        logger.info(f"Found {web_chunk_count} website chunks in the database")
    
    # Check website_chunks with embeddings
    web_embed_query = "SELECT COUNT(*) FROM website_chunks WHERE embedding IS NOT NULL"
    web_embed_result = supabase.execute_query(web_embed_query)
    
    if isinstance(web_embed_result, dict) and "error" in web_embed_result:
        logger.error(f"Error checking website chunks with embeddings: {web_embed_result['error']}")
        web_embed_count = 0
    else:
        web_embed_count = extract_count(web_embed_result)
        logger.info(f"Found {web_embed_count} website chunks with embeddings in the database")
    
    return {
        "documents": doc_count,
        "document_chunks": chunk_count,
        "document_chunks_with_embeddings": embed_count,
        "websites": web_count,
        "website_chunks": web_chunk_count,
        "website_chunks_with_embeddings": web_embed_count
    }

def regenerate_embeddings():
    """Regenerate embeddings for chunks without valid embeddings."""
    logger.info("Regenerating embeddings for chunks without valid embeddings...")
    
    # Check document chunks without embeddings
    doc_query = "SELECT id, text FROM document_chunks WHERE embedding IS NULL LIMIT 100"
    doc_result = supabase.execute_query(doc_query)
    
    if isinstance(doc_result, dict) and "error" in doc_result:
        logger.error(f"Error checking document chunks without embeddings: {doc_result['error']}")
    elif isinstance(doc_result, list):
        logger.info(f"Found {len(doc_result)} document chunks without embeddings")
        
        # Regenerate embeddings for each chunk
        for chunk in doc_result:
            try:
                chunk_id = chunk.get("id")
                chunk_text = chunk.get("text", "")
                
                if not chunk_text:
                    logger.warning(f"Empty text for chunk {chunk_id}, skipping")
                    continue
                
                # Generate embedding
                embedding = llm_router.generate_embedding(chunk_text)
                
                # Update chunk with embedding
                update_query = f"""
                UPDATE document_chunks
                SET embedding = '{json.dumps(embedding)}'::vector
                WHERE id = '{chunk_id}'
                """
                update_result = supabase.execute_query(update_query)
                
                if isinstance(update_result, dict) and "error" in update_result:
                    logger.error(f"Error updating embedding for chunk {chunk_id}: {update_result['error']}")
                else:
                    logger.info(f"Updated embedding for document chunk {chunk_id}")
            except Exception as e:
                logger.error(f"Error regenerating embedding for document chunk: {str(e)}")
    
    # Check website chunks without embeddings
    web_query = "SELECT id, text FROM website_chunks WHERE embedding IS NULL LIMIT 100"
    web_result = supabase.execute_query(web_query)
    
    if isinstance(web_result, dict) and "error" in web_result:
        logger.error(f"Error checking website chunks without embeddings: {web_result['error']}")
    elif isinstance(web_result, list):
        logger.info(f"Found {len(web_result)} website chunks without embeddings")
        
        # Regenerate embeddings for each chunk
        for chunk in web_result:
            try:
                chunk_id = chunk.get("id")
                chunk_text = chunk.get("text", "")
                
                if not chunk_text:
                    logger.warning(f"Empty text for chunk {chunk_id}, skipping")
                    continue
                
                # Generate embedding
                embedding = llm_router.generate_embedding(chunk_text)
                
                # Update chunk with embedding
                update_query = f"""
                UPDATE website_chunks
                SET embedding = '{json.dumps(embedding)}'::vector
                WHERE id = '{chunk_id}'
                """
                update_result = supabase.execute_query(update_query)
                
                if isinstance(update_result, dict) and "error" in update_result:
                    logger.error(f"Error updating embedding for chunk {chunk_id}: {update_result['error']}")
                else:
                    logger.info(f"Updated embedding for website chunk {chunk_id}")
            except Exception as e:
                logger.error(f"Error regenerating embedding for website chunk: {str(e)}")

def main():
    """Main function to check and fix the database."""
    # Check database content
    content_stats = check_database_content()
    
    # Check if we need to regenerate embeddings
    if (content_stats["document_chunks"] > content_stats["document_chunks_with_embeddings"] or
        content_stats["website_chunks"] > content_stats["website_chunks_with_embeddings"]):
        logger.info("Some chunks are missing embeddings, regenerating...")
        regenerate_embeddings()
    else:
        logger.info("All chunks have embeddings, no need to regenerate")
    
    # Final check
    logger.info("Final database check:")
    check_database_content()

if __name__ == "__main__":
    main()
