#!/usr/bin/env python3
"""
Test all endpoints to see which ones are working
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_endpoint(endpoint, method="GET"):
    """Test a single endpoint"""
    try:
        if method == "GET":
            response = requests.get(f"{API_BASE}{endpoint}", timeout=5)
        elif method == "HEAD":
            response = requests.head(f"{API_BASE}{endpoint}", timeout=5)
        
        print(f"  {method} {endpoint}: {response.status_code}")
        if response.status_code == 200 and method == "GET":
            try:
                data = response.json()
                if isinstance(data, list):
                    print(f"    → Returns list with {len(data)} items")
                elif isinstance(data, dict):
                    print(f"    → Returns dict with keys: {list(data.keys())[:3]}...")
            except:
                print(f"    → Returns non-JSON content")
        return response.status_code == 200
    except Exception as e:
        print(f"  {method} {endpoint}: ERROR - {str(e)}")
        return False

def main():
    """Test all endpoints"""
    print("🧪 Testing All Endpoints")
    print("=" * 50)
    
    endpoints = [
        "/api/health",
        "/api/documents", 
        "/api/websites",
        "/api/documents/view/SampleRailwayDoc.pdf",
        "/api/documents/view/ACP%20110V.docx",
        "/api/debug/paths"
    ]
    
    for endpoint in endpoints:
        if "view" in endpoint:
            test_endpoint(endpoint, "HEAD")
        else:
            test_endpoint(endpoint, "GET")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    main() 