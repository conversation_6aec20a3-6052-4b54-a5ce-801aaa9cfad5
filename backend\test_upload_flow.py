"""
Test script to verify the document and website upload flow.
"""

import os
import sys
import logging
import json
import uuid
import tempfile
from typing import List, Dict, Any
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import required modules
from supabase_client import supabase
from document_extractor import extract_document
from website_scraper import extract_website_text
from embeddings import get_embedding, get_mock_embedding
from vector_db import vector_db

# Load environment variables
load_dotenv()

def create_test_document():
    """Create a test document file."""
    try:
        # Create a temporary directory
        temp_dir = os.path.join("data", "uploads")
        os.makedirs(temp_dir, exist_ok=True)

        # Check if we have an existing PDF file to use
        sample_pdf = os.path.join("data", "SampleRailwayDoc.pdf")
        if os.path.exists(sample_pdf):
            logger.info(f"Using existing PDF file: {sample_pdf}")
            return sample_pdf

        # If no PDF file exists, create a mock document entry directly in Supabase
        logger.info("No PDF file found, creating mock document entry directly in Supabase")

        # Create a document entry
        document_data = {
            "filename": "test_document.pdf",
            "display_name": "Test Document",
            "file_path": "documents/test_document.pdf",
            "file_type": "pdf",
            "file_size": 1024,
            "status": "processed"
        }

        # Store document directly
        result = supabase.supabase.table("documents").insert(document_data).execute()

        if hasattr(result, 'data') and result.data:
            document_id = result.data[0].get('id')
            logger.info(f"Created mock document with ID: {document_id}")

            # Create a temporary file to return
            file_path = os.path.join(temp_dir, "test_document.pdf")
            with open(file_path, "w") as f:
                f.write("Mock PDF content")

            # Return both the file path and document ID
            return file_path, document_id
        else:
            logger.error(f"Failed to create mock document: {result}")
            return None
    except Exception as e:
        logger.error(f"Error creating test document: {str(e)}")
        return None

def test_document_upload_flow():
    """Test the document upload flow."""
    try:
        logger.info("Testing document upload flow...")

        # Create a test document
        result = create_test_document()

        # Handle different return types
        if not result:
            logger.error("Failed to create test document")
            return False

        if isinstance(result, tuple):
            file_path, document_id = result
            logger.info(f"Using mock document with ID: {document_id}")

            # Create mock document chunks directly
            document_chunks = [
                {
                    "document_id": document_id,
                    "chunk_index": 0,
                    "page_number": 1,
                    "text": "This is a test document chunk for upload flow verification.",
                    "source_type": "document",
                    "metadata": {
                        "filename": "test_document.pdf",
                        "file_type": "pdf",
                        "extraction_method": "mock"
                    }
                },
                {
                    "document_id": document_id,
                    "chunk_index": 1,
                    "page_number": 1,
                    "text": "It contains multiple chunks to test the document upload flow.",
                    "source_type": "document",
                    "metadata": {
                        "filename": "test_document.pdf",
                        "file_type": "pdf",
                        "extraction_method": "mock"
                    }
                }
            ]
        else:
            # We have a real PDF file
            file_path = result

            # Process the document
            logger.info(f"Processing document: {file_path}")
            document_chunks = extract_document(
                file_path=file_path,
                supabase_file_path=f"documents/{os.path.basename(file_path)}",
                uploaded_by="test_user"
            )

            if not document_chunks:
                logger.error("Failed to extract document chunks")
                return False

            logger.info(f"Extracted {len(document_chunks)} chunks from document")

            # Check if document_id is set in chunks
            if document_chunks and "document_id" in document_chunks[0]:
                document_id = document_chunks[0]["document_id"]
                logger.info(f"Document ID: {document_id}")
            else:
                logger.error("Document ID not set in chunks")
                return False

        # Generate embeddings for each chunk
        for chunk in document_chunks:
            # Add source_type if not already set
            if "source_type" not in chunk:
                chunk["source_type"] = "document"

            # Generate and add embedding
            embedding = get_mock_embedding()  # Use mock embedding for testing
            chunk["embedding"] = embedding

        # Add to vector database
        logger.info("Adding chunks to vector database...")
        vector_db.add_chunks(document_chunks)

        # Query the database to verify chunks were stored
        logger.info("Querying database to verify chunks were stored...")
        try:
            result = supabase.supabase.table("document_chunks").select("*").eq("document_id", document_id).execute()

            if hasattr(result, 'data') and result.data:
                logger.info(f"Found {len(result.data)} chunks in database")
                return True
            else:
                logger.error("No chunks found in database")
                return False
        except Exception as e:
            logger.error(f"Error querying database: {str(e)}")
            return False
    except Exception as e:
        logger.error(f"Error in test_document_upload_flow: {str(e)}")
        return False

def test_website_upload_flow():
    """Test the website upload flow."""
    try:
        logger.info("Testing website upload flow...")

        # Create a mock website URL
        url = "https://example.com/test"

        # Create mock website chunks
        website_chunks = [
            {
                "source_type": "website",
                "url": url,
                "domain": "example.com",
                "chunk_id": "example.com_test_0",
                "text": "This is a test website chunk for upload flow verification.",
                "extraction_method": "mock"
            },
            {
                "source_type": "website",
                "url": url,
                "domain": "example.com",
                "chunk_id": "example.com_test_1",
                "text": "It contains multiple chunks to test the website upload flow.",
                "extraction_method": "mock"
            }
        ]

        # Store website in Supabase
        website_data = supabase.store_website(
            url=url,
            domain="example.com",
            title="Test Website",
            description="Test website for upload flow verification"
        )

        if "error" in website_data:
            logger.error(f"Error storing website: {website_data['error']}")
            return False

        website_id = website_data.get("id")
        logger.info(f"Stored website with ID: {website_id}")

        # Generate embeddings for each chunk
        for i, chunk in enumerate(website_chunks):
            # Add website_id
            chunk["website_id"] = website_id
            chunk["chunk_index"] = i

            # Generate and add embedding
            embedding = get_mock_embedding()  # Use mock embedding for testing
            chunk["embedding"] = embedding

            # Add metadata
            chunk["metadata"] = {
                "url": url,
                "domain": "example.com",
                "chunk_id": f"example.com_test_{i}",
                "source_type": "website"
            }

        # Add to vector database
        logger.info("Adding chunks to vector database...")
        vector_db.add_chunks(website_chunks, source_type="website")

        # Query the database to verify chunks were stored
        logger.info("Querying database to verify chunks were stored...")
        try:
            result = supabase.supabase.table("website_chunks").select("*").eq("website_id", website_id).execute()

            if hasattr(result, 'data') and result.data:
                logger.info(f"Found {len(result.data)} chunks in database")
                return True
            else:
                logger.error("No chunks found in database")
                return False
        except Exception as e:
            logger.error(f"Error querying database: {str(e)}")
            return False
    except Exception as e:
        logger.error(f"Error in test_website_upload_flow: {str(e)}")
        return False

def run_tests():
    """Run all tests."""
    logger.info("Starting tests...")

    # Test document upload flow
    doc_result = test_document_upload_flow()
    logger.info(f"Document upload flow test: {'PASSED' if doc_result else 'FAILED'}")

    # Test website upload flow
    web_result = test_website_upload_flow()
    logger.info(f"Website upload flow test: {'PASSED' if web_result else 'FAILED'}")

    # Overall result
    if doc_result and web_result:
        logger.info("All tests PASSED")
        return True
    else:
        logger.error("Some tests FAILED")
        return False

if __name__ == "__main__":
    run_tests()
