import React, { useState, useRef, useEffect } from 'react';
import './App.css';
import { sendQuery } from './services/api';
import LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';
import InteractiveAnswer from './components/ui/InteractiveAnswer';
import ChatSidebar from './components/chat/ChatSidebar';
import TrainLoader from './components/ui/TrainLoader';
import VisualContent from './components/ui/VisualContent';
import { useChatContext } from './contexts/ChatContext';
import { ChatSession, ChatMessage } from './services/supabase';

interface Source {
  source_type: string;
  filename?: string;
  page?: number;
  url?: string;
  link?: string; // For document viewer links
  name?: string; // For display name
  // Visual content fields
  content_type?: string;  // "text", "table", "image", "chart_diagram"
  visual_content?: Record<string, any>;  // Visual content metadata
  storage_url?: string;  // URL for stored visual content
  display_type?: string;  // "text", "html_table", "image", "base64_image"
}

// Using ChatMessage interface from services/supabase.ts

interface ChatInterfaceProps {
  sidebarOpen: boolean;
  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

function ChatInterface({ sidebarOpen, setSidebarOpen }: ChatInterfaceProps) {
  const {
    currentSession,
    messages,
    createNewChat,
    loadChatSession,
    addMessage,
    updateMessage,
    clearCurrentChat
  } = useChatContext();

  const [input, setInput] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model
  const [showTrainLoader, setShowTrainLoader] = useState(false);
  const [currentSearchStage, setCurrentSearchStage] = useState<'initializing' | 'searching_documents' | 'searching_websites' | 'generating_answer' | 'complete'>('initializing');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of chat whenever messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Helper function to determine if visual content should be shown
  const shouldShowVisualContent = (userQuestion: string, documentAnswer: string): boolean => {
    const userQuery = userQuestion.toLowerCase();

    // Always show visual content when user asks for images
    if (userQuery.includes('image')) {
      return true;
    }
    
    // Detect different types of requests
    const askedForImages = userQuery.includes('image') || userQuery.includes('picture') || userQuery.includes('diagram') || userQuery.includes('chart');
    const askedForTableData = (userQuery.includes('table') || userQuery.includes('quotation')) && 
                             (userQuery.includes('give me') || userQuery.includes('show me table') || userQuery.includes('get') || userQuery.includes('provide'));
    const askedToShowImages = userQuery.includes('show me image') || userQuery.includes('display image') || userQuery.includes('see image');
    
    const answerLower = documentAnswer?.toLowerCase() || '';
    const hasTableInAnswer = answerLower.includes('<table>') || answerLower.includes('|') || 
                            (answerLower.includes('table') && answerLower.length > 200);
    const hasImageDescription = answerLower.includes('image') || answerLower.includes('figure') || answerLower.includes('diagram');
    const answerHasContent = answerLower.length > 50;
    
    // ALWAYS show visual content if user specifically asks to see images/visuals
    if (askedToShowImages || (askedForImages && (userQuery.includes('show') || userQuery.includes('display')))) {
      return true;
    }

    // NEVER show if user asked for table data (they want the actual data, not images)
    if (askedForTableData && !askedForImages) {
      return false;
    }
    
    // For other cases, show only if answer doesn't already provide adequate information
    const shouldShow = !hasTableInAnswer && // Don't show if answer already has table data
                      !hasImageDescription && // Don't show if answer already describes images well
                      (
                        (askedForImages && !answerHasContent) || // Show images if user asked and answer is short
                        (!answerHasContent && answerLower.length < 50) // Show for very short answers
                      );
    
    return shouldShow;
  };

  // Helper function to filter visual content types based on user query
  const filterVisualContent = (source: Source | string, userQuestion: string) => {
    if (typeof source !== 'object' || !source.visual_content) {
      return false;
    }

    const userQueryLower = userQuestion.toLowerCase();
    const sourceObj = source as Source;
    
    // Extract specific project/quotation numbers from user query
    const projectMatch = userQueryLower.match(/project\s*(\d+)/);
    const quotationMatch = userQueryLower.match(/quotation\s*(\d+)/);
    
    // Extract company names for logo queries
    const logoQueryPatterns = [
      /(?:logo\s+(?:of\s+)?|show\s+me\s+(?:the\s+)?logo\s+(?:of\s+)?)([A-Z][A-Za-z\s&]+?)(?:\s+logo|\s+enterprises|\s+company|\s+corp|\s+ltd|\s+inc|\s*$)/i,
      /([A-Z][A-Za-z\s&]+?)\s+(?:enterprises|company|corp|ltd|inc)\s+logo/i,
      /([A-Z][A-Za-z\s&]+?)\s+logo/i,
    ];
    
    let companyMatch = null;
    for (const pattern of logoQueryPatterns) {
      const match = userQuestion.match(pattern);
      if (match) {
        companyMatch = match[1].trim();
        break;
      }
    }
    
    // Check if the visual content contains relevant information for the specific request
    const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();
    
    // If user asked for a specific company logo
    if (companyMatch && userQueryLower.includes('logo')) {
      const companyNameLower = companyMatch.toLowerCase();
      const isImage = sourceObj.content_type === 'image';
      

      
      if (isImage) {
        // Check if this image contains the requested company
        const hasCompanyInContent = visualContentStr.includes(companyNameLower);
        const hasCompanyInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(companyNameLower);
        
        // Check detected companies from OCR
        const detectedCompanies: string[] = sourceObj.visual_content?.detected_companies || [];
        const hasDetectedCompany = detectedCompanies.some((company: string) =>
          company.toLowerCase().includes(companyNameLower) ||
          companyNameLower.includes(company.toLowerCase())
        );
        
        // Check OCR text
        const ocrText = sourceObj.visual_content?.ocr_text || '';
        const hasOcrMatch = ocrText.toLowerCase().includes(companyNameLower);
        
        // Check if marked as logo
        const isLogo = sourceObj.visual_content?.is_logo || false;
        
        const isRelevantLogo = hasCompanyInContent || hasCompanyInFilename || hasDetectedCompany || hasOcrMatch || isLogo;
        

        
        return isRelevantLogo;
      }
      
      return false; // Not an image for logo query
    }
    
    // If user asked for a specific project image
    if (projectMatch && userQueryLower.includes('image')) {
      const projectNumber = projectMatch[1];
      const isImage = sourceObj.content_type === 'image';
      
      // Check if this image is related to the specific project
      const hasProjectInContent = visualContentStr.includes(`project ${projectNumber}`) || 
                                  visualContentStr.includes(`project${projectNumber}`);
      const hasProjectInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(`project ${projectNumber}`);
      const isOnProjectPage = sourceObj.page && Math.abs(sourceObj.page - parseInt(projectNumber)) <= 1; // Allow adjacent pages
      const hasProjectContext = sourceObj.visual_content?.project_context && 
                               sourceObj.visual_content.project_context.includes(`project ${projectNumber}`);
      
      const isRelevantProject = hasProjectInContent || hasProjectInFilename || isOnProjectPage || hasProjectContext;
      

      
      // If this is an image and it's relevant to the project, show it
      // If no images are relevant, we'll fall back to showing all images
      return isImage && isRelevantProject;
    }
    
    // If user asked for a specific quotation table
    if (quotationMatch && userQueryLower.includes('table')) {
      const quotationNumber = quotationMatch[1];
      const isTable = sourceObj.content_type === 'table';
      
      // Check if this table is related to the specific quotation
      const isRelevantQuotation = visualContentStr.includes(`quotation ${quotationNumber}`) || 
                                  visualContentStr.includes(`quotation${quotationNumber}`) ||
                                  visualContentStr.includes(`quote ${quotationNumber}`);
      

      
      return isTable && isRelevantQuotation;
    }
    
    // If user specifically asked for images (but no specific project), only show images
    if (userQueryLower.includes('image') && !userQueryLower.includes('table')) {
      const isImage = sourceObj.content_type === 'image';
      return isImage;
    }

    // If user specifically asked for tables (but no specific quotation), only show tables
    if (userQueryLower.includes('table') && !userQueryLower.includes('image')) {
      const isTable = sourceObj.content_type === 'table';
      return isTable;
    }

    // Default: show all visual content
    return true;
  };

  // Handle command shortcuts in the textbox
  const handleCommandShortcut = (input: string) => {
    // Check if the input is a command
    if (input.startsWith('/')) {
      const command = input.split(' ')[0].toLowerCase();

      // Command: /model <model-name>
      if (command === '/model') {
        const modelArg = input.substring(7).trim();
        const matchedModel = DEFAULT_LLM_MODELS.find(m =>
          m.name.toLowerCase().includes(modelArg.toLowerCase()) ||
          m.id.toLowerCase().includes(modelArg.toLowerCase())
        );

        if (matchedModel && matchedModel.enabled) {
          setActiveLLMModel(matchedModel.id);
          setInput('');
          return 'processed';
        }
      }

      // Command: /reset or /clear - clear chat history
      else if (command === '/reset' || command === '/clear') {
        clearCurrentChat();
        setInput('');
        return 'processed';
      }
    }

    return 'not_processed';
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isSubmitting) return;

    // Handle command shortcuts like /reset, /model, etc.
    if (input.startsWith('/')) {
      const result = handleCommandShortcut(input);
      if (result === 'processed') {
        setInput('');
        return;
      }
      // If not processed as a command, continue as a regular message
    }

    return await sendUserMessage(input);
  };

  const sendUserMessage = async (messageText: string) => {
    // Create new chat session if none exists
    if (!currentSession) {
      await createNewChat();
    }

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      content: messageText,
      sender: 'user',
      timestamp: new Date().toISOString(),
      chatId: currentSession?.id || 'temp',
    };

    addMessage(userMessage);
    setInput('');

    const messageId = Date.now();
    const tempAiMessage: ChatMessage = {
      id: `ai-${messageId}`,
      content: '',
      sender: 'ai',
      loading: true,
      timestamp: new Date().toISOString(),
      llm_model: activeLLMModel,
      chatId: currentSession?.id || 'temp',
    };

    addMessage(tempAiMessage);
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    setIsSubmitting(true);
    setShowTrainLoader(true);
    setCurrentSearchStage('initializing');

    try {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

      // Simulate search progress updates with train loader
      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);
      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);
      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);

      const response = await sendQuery(messageText, activeLLMModel);

      // Create the AI message based on strict priority logic
      const aiMessage: ChatMessage = {
        id: `ai-${messageId}`,
        content: response.answer,
        document_answer: response.document_answer,
        website_answer: response.website_answer,
        llm_model: response.llm_model || activeLLMModel,
        sender: 'ai',
        sources: response.sources,
        document_sources: response.document_sources,
        website_sources: response.website_sources,
        chatId: currentSession?.id || 'temp',
        llm_fallback: response.llm_fallback,
      };



      updateMessage(tempAiMessage.id, aiMessage);

      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
      console.error('Error sending message:', error);

      // Provide more helpful error message without the specific query
      const errorMessage: ChatMessage = {
        id: `ai-${messageId}`,
        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,
        sender: 'ai',
        chatId: currentSession?.id || 'temp',
        llm_fallback: true,
      };

      updateMessage(tempAiMessage.id, errorMessage);
    } finally {
      setIsSubmitting(false);
      setShowTrainLoader(false);
      setCurrentSearchStage('complete');
    }
  };



  const processDocumentSources = (sources?: Array<Source | string>) => {
    if (!sources || sources.length === 0) return [];

    // Group by filename to avoid repetition
    const groupedSources: { [key: string]: { filename: string; pages: number[] } } = {};

    sources.forEach(source => {
      let filename: string;
      let page: number;

      if (typeof source === 'string') {
        // Parse string format like "MaintenanceManual.pdf – Page 3"
        const match = source.match(/([^–]+)(?:\s*–\s*Page\s*(\d+))?/i);
        filename = match ? match[1].trim() : source;
        page = match && match[2] ? parseInt(match[2]) : 1;
      } else {
        filename = source.name || source.filename || "Unknown Document";
        page = source.page || 1;
        }

      if (!groupedSources[filename]) {
        groupedSources[filename] = { filename, pages: [] };
      }

      if (!groupedSources[filename].pages.includes(page)) {
        groupedSources[filename].pages.push(page);
      }
    });

    // Convert to display format with viewer links
    return Object.values(groupedSources).map(group => {
      const sortedPages = group.pages.sort((a, b) => a - b);
      const pageText = sortedPages.length === 1
        ? `Page ${sortedPages[0]}`
        : `Pages ${sortedPages.join(', ')}`;

      // Create link for document viewer that opens at exact page number
      return {
        text: `${group.filename} – ${pageText}`,
        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,
        isDocument: true
      };
    });
  };

  const processWebsiteSources = (sources?: Array<Source | string>) => {
    if (!sources || sources.length === 0) return [];

    // Remove duplicates and format
    const uniqueUrls = new Set<string>();
    const processed: Array<{ text: string; link: string; isDocument?: boolean }> = [];

    sources.forEach(source => {
      let url: string;
      let displayText: string;

      if (typeof source === 'string') {
        url = source.startsWith('http') ? source : `https://${source}`;
          try {
          const urlObj = new URL(url);
          displayText = urlObj.hostname.replace(/^www\./, '');
          } catch {
          displayText = source;
        }
      } else {
        url = source.url || 'https://railgpt.indianrailways.gov.in';
        try {
          const urlObj = new URL(url);
          displayText = urlObj.hostname.replace(/^www\./, '');
        } catch {
          displayText = url;
        }
      }

      if (!uniqueUrls.has(url)) {
        uniqueUrls.add(url);
        processed.push({
          text: displayText,
          link: url,
          isDocument: false  // Mark as website source
        });
        }
    });

    return processed; // Return all website sources
  };

  // Component for expandable source list with appropriate click behaviors
  const SourceList = ({ items, maxVisible = 3 }: {
    items: Array<{ text: string; link?: string; isDocument?: boolean }>;
    maxVisible?: number
  }) => {
    const [expanded, setExpanded] = useState(items.length <= maxVisible);

    if (items.length === 0) return null;

    const visibleItems = expanded ? items : items.slice(0, maxVisible);
    const hasMore = items.length > maxVisible;

    const handleDocumentClick = (e: React.MouseEvent<HTMLAnchorElement>, link: string) => {
      // If we want to handle document links in a special way, we can do so here
      // For example, we could open a modal or new tab with the document viewer
      // Currently, just allowing regular link behavior
    };

    return (
      <ul className="text-xs list-disc pl-4 mt-1 space-y-1">
        {visibleItems.map((item, index) => (
              <li key={index}>
                {item.link ? (
              <a
                href={item.link}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline text-blue-600 transition-colors duration-200"
                title={item.isDocument ? "Open document at this page" : "Open website in new tab"}
                onClick={item.isDocument ? (e) => handleDocumentClick(e, item.link!) : undefined}
              >
                    {item.text}
                  </a>
                ) : (
              <span className="text-gray-700">{item.text}</span>
                )}
              </li>
            ))}
        {hasMore && !expanded && (
          <li className="list-none">
            <button
              onClick={() => setExpanded(true)}
              className="text-blue-500 hover:underline text-xs transition-colors duration-200"
            >
              + {items.length - maxVisible} more sources
            </button>
          </li>
        )}
        {hasMore && expanded && (
          <li className="list-none">
              <button
                onClick={() => setExpanded(false)}
              className="text-blue-500 hover:underline text-xs transition-colors duration-200"
              >
                Show less
              </button>
            </li>
                )}
      </ul>
    );
  };

  // Sidebar handlers
  const handleChatSelect = async (chatSession: ChatSession) => {
    // Load the selected chat session using context
    await loadChatSession(chatSession.id);
    setActiveLLMModel(chatSession.model_used || 'gemini-2.0-flash');
    setSidebarOpen(false); // Close sidebar on mobile after selection
  };

  const handleNewChat = async () => {
    console.log('Creating new chat...');
    await createNewChat();
    setSidebarOpen(false); // Close sidebar on mobile after creating new chat
  };

  return (
    <div className="flex h-screen bg-gray-100 transition-colors duration-300">
      {/* Train Loader Overlay */}
      <TrainLoader
        isVisible={showTrainLoader}
        message={(() => {
          switch (currentSearchStage) {
            case 'searching_documents':
              return "RailGPT Searching in Documents...";
            case 'searching_websites':
              return "RailGPT Searching in Websites...";
            case 'generating_answer':
              return "RailGPT Generating Response...";
            default:
              return "RailGPT Processing Your Query...";
          }
        })()}
        trainType="express"
        currentStage={currentSearchStage}
        sidebarOpen={sidebarOpen}
      />

      {/* Chat Sidebar */}
      <ChatSidebar
        isOpen={sidebarOpen}
        onToggle={() => setSidebarOpen(!sidebarOpen)}
        currentChatId={currentSession?.id || ''}
        onChatSelect={handleChatSelect}
        onNewChat={handleNewChat}
      />

      {/* Main Chat Area */}
      <div className={`flex flex-col flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-80' : ''}`}>
        {/* Message Area - only scrollable when messages exist */}
        <div className={`flex-1 ${messages.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-4 pb-32`}>
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-500">
                <p className="text-xl font-semibold mb-3">Welcome to RailGPT!</p>
                <p>Ask questions about Indian Railways...</p>
              </div>
            </div>
          ) : (
            <div>
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`mb-4 ${
                    message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'
                  }`}
                >
                  <div
                    className={`max-w-4xl rounded-lg p-4 transition-colors duration-300 ${
                      message.sender === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white text-gray-800 shadow-md'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <span className="font-semibold">
                        {message.sender === 'user' ? 'You' : 'RailGPT'}
                      </span>
                      {message.timestamp && (
                        <span className={`text-xs ml-2 ${
                          message.sender === 'user'
                            ? 'text-blue-100'
                            : 'text-gray-500'
                        }`}>
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </span>
                      )}
                    </div>

                    {/* Only show content directly for user messages */}
                    {message.sender === 'user' && message.content && (
                      <div className="mt-2 whitespace-pre-wrap">{message.content}</div>
                    )}

                    {/* AI messages with strict priority display logic */}
                    {message.sender === 'ai' && (
                      <div>
                        {(() => {
                          // Only hide if this specific message is loading AND has no content yet
                          if (message.loading && showTrainLoader && !message.content && !message.document_answer && !message.website_answer) {
                            return null;
                          }

                          // Process sources with improved deduplication
                          const documentSourceItems = processDocumentSources(message.document_sources);
                          const websiteSourceItems = processWebsiteSources(message.website_sources);

                          // Check what content is available for conditional display
                          const hasDocumentContent = !!(message.document_answer && message.document_answer.trim() !== "");
                          const hasWebsiteContent = !!(message.website_answer && message.website_answer.trim() !== "");
                          // LLM fallback happens if NEITHER document sources NOR website sources are found, or if explicitly set
                          const hasLLMFallback = (!hasDocumentContent && !hasWebsiteContent) || message.llm_fallback;

                          // Debug logging for rendering
                          console.log(`🔍 Rendering message ${message.id}:`, {
                            hasDocumentContent,
                            hasWebsiteContent,
                            hasLLMFallback,
                            documentAnswerLength: message.document_answer?.length || 0,
                            websiteAnswerLength: message.website_answer?.length || 0,
                            documentSourcesCount: documentSourceItems.length,
                            websiteSourcesCount: websiteSourceItems.length,
                            rawDocumentAnswer: message.document_answer ? 'EXISTS' : 'MISSING',
                            rawWebsiteAnswer: message.website_answer ? 'EXISTS' : 'MISSING',
                            rawDocumentSources: message.document_sources?.length || 0,
                            rawWebsiteSources: message.website_sources?.length || 0
                          });

                          // Get the user's question for context - find the most recent user message before this AI message
                          const currentMessageIndex = messages.findIndex(m => m.id === message.id);
                          let userQuestion = '';
                          
                          // Look backwards from current AI message to find the most recent user message
                          for (let i = currentMessageIndex - 1; i >= 0; i--) {
                            if (messages[i].sender === 'user' && messages[i].content) {
                              userQuestion = messages[i].content;
                              break;
                            }
                          }
                          
                          console.log('🔍 DEBUG: Found user question for AI message:', { aiMessageId: message.id, userQuestion });

                          // Conditional display logic based on answer sources
                          const components = [];
                          // eslint-disable-next-line @typescript-eslint/no-unused-vars
                          let answerSource = '';

                          // Case 1: Answer exists from both uploaded documents and websites
                          if (hasDocumentContent && hasWebsiteContent) {
                            answerSource = 'document_and_website';

                            // Document answer card
                            // eslint-disable-next-line @typescript-eslint/no-unused-vars
                            const documentName = documentSourceItems.length === 1
                              ? documentSourceItems[0].text.split(' – ')[0]
                              : 'Uploaded Documents';

                            components.push(
                              <div key="document-card" className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300">
                                <h4 className="font-semibold text-blue-800 text-sm mb-3 flex items-center">
                                  📄 Answer from {documentName}
                                </h4>
                                <InteractiveAnswer
                                  content={message.document_answer || ""}
                                  query={userQuestion}
                                  model={message.llm_model}
                                  chatId={message.chatId}
                                />
                                
                                {/* Display Visual Content with Smart Fallback */}
                                {(() => {
                                  const visualSources = message.document_sources?.filter(source => 
                                    typeof source === 'object' && source.visual_content
                                  ) || [];
                                  
                                  // First try to find sources that match the user's specific request
                                  let relevantSources = visualSources.filter(source => 
                                    filterVisualContent(source as Source, userQuestion)
                                  );
                                  
                                  // If no specific matches and user asked for images, show any available images
                                  if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {
                                    relevantSources = visualSources.filter(source => 
                                      (source as Source).content_type === 'image'
                                    );
                                    console.log('🔍 DEBUG: No specific project matches, showing all available images:', relevantSources.length);
                                  }
                                  
                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (
                                    <div className="mt-4">
                                      <h5 className="text-sm font-semibold text-blue-800 mb-3">📊 Visual Content:</h5>
                                      <div className="space-y-3">
                                        {relevantSources.map((source, index) => (
                                          <VisualContent key={index} source={source as Source} />
                                        ))}
                                      </div>
                                    </div>
                                  ) : null;
                                })()}
                                
                                <div className="mt-3 pt-3 border-t border-blue-200">
                                  <p className="text-xs text-blue-700 font-semibold mb-1">Sources:</p>
                                  <SourceList items={documentSourceItems} maxVisible={5} />
                                </div>
                              </div>
                            );

                            // Website answer card
                            // eslint-disable-next-line @typescript-eslint/no-unused-vars
                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';

                            components.push(
                              <div key="website-card" className="mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300">
                                <h4 className="font-semibold text-green-800 text-sm mb-3 flex items-center">
                                  🌐 Answer from {websiteLabel}
                                </h4>
                                <InteractiveAnswer
                                  content={message.website_answer || ""}
                                  query={userQuestion}
                                  model={message.llm_model}
                                  chatId={message.chatId}
                                />
                                <div className="mt-3 pt-3 border-t border-green-200">
                                  <p className="text-xs text-green-700 font-semibold mb-1">Sources:</p>
                                  <SourceList items={websiteSourceItems} maxVisible={3} />
                                </div>
                              </div>
                            );

                          // Case 2: Answer exists only from documents
                          } else if (hasDocumentContent) {
                            answerSource = 'document_only';

                            // eslint-disable-next-line @typescript-eslint/no-unused-vars
                            const documentName = documentSourceItems.length === 1
                              ? documentSourceItems[0].text.split(' – ')[0]
                              : 'Uploaded Documents';

                            components.push(
                              <div key="document-priority" className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300">
                                <h4 className="font-semibold text-blue-800 text-sm mb-3 flex items-center">
                                  📄 Answer from Uploaded Documents
                                </h4>
                                <InteractiveAnswer
                                  content={message.document_answer || ""}
                                  query={userQuestion}
                                  model={message.llm_model}
                                  chatId={message.chatId}
                                />
                                
                                {/* Display Visual Content with Smart Fallback */}
                                {(() => {
                                  const visualSources = message.document_sources?.filter(source => 
                                    typeof source === 'object' && source.visual_content
                                  ) || [];
                                  
                                  // First try to find sources that match the user's specific request
                                  let relevantSources = visualSources.filter(source => 
                                    filterVisualContent(source as Source, userQuestion)
                                  );
                                  
                                  // If no specific matches and user asked for images, show any available images
                                  if (relevantSources.length === 0 && userQuestion.toLowerCase().includes('image')) {
                                    relevantSources = visualSources.filter(source => 
                                      (source as Source).content_type === 'image'
                                    );
                                    console.log('🔍 DEBUG: No specific project matches for document-only case, showing all available images:', relevantSources.length);
                                  }
                                  
                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (
                                    <div className="mt-4">
                                      <h5 className="text-sm font-semibold text-blue-800 mb-3">📊 Visual Content:</h5>
                                      <div className="space-y-3">
                                        {relevantSources.map((source, index) => (
                                          <VisualContent key={index} source={source as Source} />
                                        ))}
                                      </div>
                                    </div>
                                  ) : null;
                                })()}
                                
                                <div className="mt-3 pt-3 border-t border-blue-200">
                                  <p className="text-xs text-blue-700 font-semibold mb-1">Sources:</p>
                                  <SourceList items={documentSourceItems} maxVisible={5} />
                                </div>
                              </div>
                            );

                          // Case 3: Answer exists only from websites
                          } else if (hasWebsiteContent) {
                            answerSource = 'website_only';

                            // eslint-disable-next-line @typescript-eslint/no-unused-vars
                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';

                            components.push(
                              <div key="website-priority" className="mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300">
                                <h4 className="font-semibold text-green-800 text-sm mb-3 flex items-center">
                                  🌐 Answer from Extracted Websites
                                </h4>
                                <InteractiveAnswer
                                  content={message.website_answer || ""}
                                  query={userQuestion}
                                  model={message.llm_model}
                                  chatId={message.chatId}
                                />
                                <div className="mt-3 pt-3 border-t border-green-200">
                                  <p className="text-xs text-green-700 font-semibold mb-1">Sources:</p>
                                  <SourceList items={websiteSourceItems} maxVisible={3} />
                                </div>
                              </div>
                            );

                          // Case 4: No answer from either - fallback to LLM
                          } else if (hasLLMFallback) {
                            answerSource = 'llm';

                            const modelName = message.llm_model || 'Gemini';
                            const modelLogo = message.llm_model?.includes('chatgpt') ? '🤖' :
                                            message.llm_model?.includes('groq') ? '⚡' :
                                            message.llm_model?.includes('deepseek') ? '🔍' :
                                            message.llm_model?.includes('qwen') ? '🌐' :
                                            message.llm_model?.includes('ollama') ? '🏠' :
                                            message.llm_model?.includes('huggingface') ? '🤗' : '🧠';

                            // Only show the LLM fallback card if not in loading state
                            if (!showTrainLoader || !message.loading) {
                            components.push(
                              <div key="llm-fallback" className="mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300">
                                <h4 className="font-semibold text-purple-800 text-sm mb-3 flex items-center">
                                  {modelLogo} Answer generated by {modelName}
                                </h4>
                                <InteractiveAnswer
                                  content={message.content || "I couldn't find any relevant information to answer your question."}
                                  query={userQuestion}
                                  model={message.llm_model || 'Gemini'}
                                  chatId={message.chatId}
                                />
                                <div className="mt-3 pt-3 border-t border-purple-200">
                                  <p className="text-xs text-purple-600 italic">
                                    This answer was generated by an AI model as no relevant information was found in your documents or websites.
                                  </p>
                                </div>
                              </div>
                            );
                            }

                          // Case 5: No sources found and fallback disabled (or similar edge case)
                          } else {
                            // eslint-disable-next-line @typescript-eslint/no-unused-vars
                            answerSource = 'no_results';

                            // Only show the "no results" card if not in loading state
                            if (!showTrainLoader || !message.loading) {
                            components.push(
                              <div key="no-results" className="mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300">
                                <p className="text-sm text-gray-600 mb-2">No sources found</p>
                                <InteractiveAnswer
                                  content={message.content || "I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded."}
                                  query={userQuestion}
                                  model={message.llm_model || 'Gemini'}
                                  chatId={message.chatId}
                                />
                              </div>
                            );
                            }
                          }

                          // If we have components to display, render them
                          if (components.length > 0) {
                            return (
                              <div className="mt-3">
                                {components}
                              </div>
                            );
                          }

                          // Fallback for any unhandled edge cases (should rarely happen)
                          console.warn("Frontend: Unhandled rendering case");
                          return (
                            <div className="mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300">
                              <p className="text-sm text-gray-600 mb-2">Rendering Error</p>
                              <InteractiveAnswer
                                content={message.content || "An error occurred while rendering the response."}
                                query={userQuestion}
                                model={message.llm_model || 'Gemini'}
                                chatId={message.chatId}
                              />
                            </div>
                          );
                        })()}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} style={{ float: 'left', clear: 'both' }} />
            </div>
          )}
        </div>

        {/* Fixed Chat Input Box at Bottom - using fixed positioning */}
        <div className={`border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen ? 'lg:left-80 left-0' : 'left-0'}`}>
          <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
            <div className="flex-1 relative flex">
              <input
                type="text"
                value={input}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setInput(newValue);
                  // Don't handle command shortcuts as you type, only on submit
                }}
                placeholder="Type your message... (/model, /reset, /clear)"
                className="w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300"
                disabled={isSubmitting}
                aria-label="Message input"
              />
            </div>

            <div className="flex items-center space-x-2">
              <LLMSelector
                currentModel={activeLLMModel}
                onModelChange={(modelId) => setActiveLLMModel(modelId)}
                isLoading={isSubmitting}
              />
              <button
                type="submit"
                disabled={isSubmitting || !input.trim()}
                className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300"
                title="Send message"
              >
                <span>{isSubmitting ? "Sending..." : "Send"}</span>
              </button>
            </div>
          </form>
                      <div className="text-xs text-gray-400 mt-1 text-center">
            Current model: {DEFAULT_LLM_MODELS.find(m => m.id === activeLLMModel)?.name || activeLLMModel}
          </div>
        </div>
        {/* Spacer div to push content up above the fixed input box - only needed when there are messages */}
        {messages.length > 0 && <div className="h-36"></div>}
      </div>
    </div>
  );
}

interface AppProps {
  sidebarOpen: boolean;
  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

function App({ sidebarOpen, setSidebarOpen }: AppProps) {
  return (
    <ChatInterface sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
  );
}

export default App;
