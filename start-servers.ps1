# PowerShell script to start both frontend and backend servers

Write-Host "Starting RailGPT Application" -ForegroundColor Green
Write-Host "==========================" -ForegroundColor Green

# Start backend server
Write-Host "Starting backend server..." -ForegroundColor Cyan
$backendProcess = Start-Process powershell -ArgumentList "-Command", "cd backend; python -m uvicorn server:app --reload" -PassThru -WindowStyle Normal

# Start frontend server
Write-Host "Starting frontend server..." -ForegroundColor Cyan
$frontendProcess = Start-Process powershell -ArgumentList "-Command", "cd frontend; set NODE_OPTIONS=--openssl-legacy-provider; npm start" -PassThru -WindowStyle Normal

Write-Host "RailGPT servers started!" -ForegroundColor Green
Write-Host "Backend running at: http://localhost:8000" -ForegroundColor Cyan
Write-Host "Frontend running at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Press Ctrl+C in the respective terminal windows to stop the servers." -ForegroundColor Yellow
