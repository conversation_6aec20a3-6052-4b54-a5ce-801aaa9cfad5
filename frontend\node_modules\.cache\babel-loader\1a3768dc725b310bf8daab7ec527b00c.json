{"ast": null, "code": "// Supabase client for frontend\nimport { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://rkllidjktazafeinezgo.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA';\n\n// Create Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Document types\n\n// Website types\n\n// Query types\n\n// Document operations\nexport const getDocuments = async () => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('documents').select('*').order('created_at', {\n      ascending: false\n    });\n    if (error) {\n      console.error('Error fetching documents:', error);\n      return [];\n    }\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocuments:', error);\n    return [];\n  }\n};\nexport const getDocumentById = async id => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('documents').select('*').eq('id', id).single();\n    if (error) {\n      console.error('Error fetching document:', error);\n      return null;\n    }\n    return data;\n  } catch (error) {\n    console.error('Error in getDocumentById:', error);\n    return null;\n  }\n};\nexport const getDocumentChunks = async documentId => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('document_chunks').select('*').eq('document_id', documentId).order('chunk_index', {\n      ascending: true\n    });\n    if (error) {\n      console.error('Error fetching document chunks:', error);\n      return [];\n    }\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocumentChunks:', error);\n    return [];\n  }\n};\n\n// Website operations\nexport const getWebsites = async () => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('websites').select('*').order('created_at', {\n      ascending: false\n    });\n    if (error) {\n      console.error('Error fetching websites:', error);\n      return [];\n    }\n    return data || [];\n  } catch (error) {\n    console.error('Error in getWebsites:', error);\n    return [];\n  }\n};\nexport const getWebsiteById = async id => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('websites').select('*').eq('id', id).single();\n    if (error) {\n      console.error('Error fetching website:', error);\n      return null;\n    }\n    return data;\n  } catch (error) {\n    console.error('Error in getWebsiteById:', error);\n    return null;\n  }\n};\n\n// Query operations\nexport const saveQuery = async query => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('queries').insert([query]).select().single();\n    if (error) {\n      console.error('Error saving query:', error);\n      return null;\n    }\n    return data;\n  } catch (error) {\n    console.error('Error in saveQuery:', error);\n    return null;\n  }\n};\nexport const getRecentQueries = async (limit = 10) => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('queries').select('*').order('created_at', {\n      ascending: false\n    }).limit(limit);\n    if (error) {\n      console.error('Error fetching recent queries:', error);\n      return [];\n    }\n    return data || [];\n  } catch (error) {\n    console.error('Error in getRecentQueries:', error);\n    return [];\n  }\n};\n\n// Storage operations\nexport const uploadFile = async (filePath, file, onProgress) => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.storage.from('documents').upload(filePath, file, {\n      cacheControl: '3600',\n      upsert: false\n    });\n    if (error) {\n      console.error('Error uploading file:', error);\n      return null;\n    }\n\n    // Get public URL\n    const {\n      data: {\n        publicUrl\n      }\n    } = supabase.storage.from('documents').getPublicUrl(data.path);\n    return publicUrl;\n  } catch (error) {\n    console.error('Error in uploadFile:', error);\n    return null;\n  }\n};\nexport const getFileUrl = filePath => {\n  const {\n    data: {\n      publicUrl\n    }\n  } = supabase.storage.from('documents').getPublicUrl(filePath);\n  return publicUrl;\n};\n\n// Chat Session types\n\n// Chat Session operations\nexport const createChatSession = async (title = 'New Chat', modelUsed = 'gemini-2.0-flash') => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('chat_sessions').insert([{\n      title,\n      messages: [],\n      model_used: modelUsed,\n      has_document: false,\n      has_website: false\n    }]).select().single();\n    if (error) {\n      console.error('Error creating chat session:', error);\n      return null;\n    }\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in createChatSession:', error);\n    return null;\n  }\n};\nexport const getChatSessions = async userId => {\n  try {\n    console.log('Attempting to fetch chat sessions...');\n\n    // First, try to check if the table exists with a simple query\n    const {\n      data: tableCheck,\n      error: tableError\n    } = await supabase.from('chat_sessions').select('id').limit(1);\n    if (tableError) {\n      console.warn('Chat sessions table not accessible:', tableError.message);\n      console.log('Returning empty chat sessions array');\n      return [];\n    }\n\n    // If table exists, try the full query with basic columns only\n    let query = supabase.from('chat_sessions').select('id, title, created_at, updated_at').order('updated_at', {\n      ascending: false\n    }).limit(10); // Reduced limit\n\n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n    const {\n      data,\n      error\n    } = await query;\n    if (error) {\n      console.warn('Chat sessions query failed:', error.message);\n      return [];\n    }\n\n    // Process and return data with safe defaults\n    const sessions = (data || []).map(session => ({\n      id: session.id,\n      title: session.title || 'Untitled Chat',\n      created_at: session.created_at,\n      updated_at: session.updated_at,\n      model_used: 'gemini-2.0-flash',\n      has_document: false,\n      has_website: false,\n      messages: []\n    }));\n    console.log(`Successfully loaded ${sessions.length} chat sessions`);\n    return sessions;\n  } catch (error) {\n    console.warn('Error in getChatSessions:', error.message || error);\n    return [];\n  }\n};\nexport const getChatSessionById = async id => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('chat_sessions').select('*').eq('id', id).single();\n    if (error) {\n      console.error('Error fetching chat session:', error);\n      return null;\n    }\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in getChatSessionById:', error);\n    return null;\n  }\n};\nexport const updateChatSession = async (id, updates) => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('chat_sessions').update(updates).eq('id', id).select().single();\n    if (error) {\n      console.error('Error updating chat session:', error);\n      return null;\n    }\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in updateChatSession:', error);\n    return null;\n  }\n};\nexport const updateChatTitle = async (id, title) => {\n  try {\n    const {\n      error\n    } = await supabase.from('chat_sessions').update({\n      title\n    }).eq('id', id);\n    if (error) {\n      console.error('Error updating chat title:', error);\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error in updateChatTitle:', error);\n    return false;\n  }\n};\nexport const deleteChatSession = async id => {\n  try {\n    const {\n      error\n    } = await supabase.from('chat_sessions').delete().eq('id', id);\n    if (error) {\n      console.error('Error deleting chat session:', error);\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error in deleteChatSession:', error);\n    return false;\n  }\n};\nexport const saveChatMessages = async (chatId, messages) => {\n  try {\n    // Ensure all message fields are preserved when saving to Supabase\n    const sanitizedMessages = messages.map(msg => ({\n      id: msg.id,\n      content: msg.content,\n      document_answer: msg.document_answer || undefined,\n      website_answer: msg.website_answer || undefined,\n      llm_model: msg.llm_model,\n      sender: msg.sender,\n      loading: msg.loading,\n      sources: msg.sources || undefined,\n      document_sources: msg.document_sources || undefined,\n      website_sources: msg.website_sources || undefined,\n      timestamp: msg.timestamp,\n      chatId: msg.chatId,\n      llm_fallback: msg.llm_fallback\n    }));\n\n    // Check if chat has documents or websites in the messages\n    const hasDocument = sanitizedMessages.some(msg => msg.document_answer || msg.document_sources && msg.document_sources.length > 0 || msg.sources && msg.sources.some(s => s.source_type === 'document'));\n    const hasWebsite = sanitizedMessages.some(msg => msg.website_answer || msg.website_sources && msg.website_sources.length > 0 || msg.sources && msg.sources.some(s => s.source_type === 'website'));\n    console.log('Saving chat messages:', {\n      messageCount: sanitizedMessages.length,\n      hasDocument,\n      hasWebsite,\n      messagesWithDocAnswer: sanitizedMessages.filter(m => m.document_answer).length,\n      messagesWithWebAnswer: sanitizedMessages.filter(m => m.website_answer).length\n    });\n    const {\n      error\n    } = await supabase.from('chat_sessions').update({\n      messages: sanitizedMessages,\n      has_document: hasDocument,\n      has_website: hasWebsite\n    }).eq('id', chatId);\n    if (error) {\n      console.error('Error saving chat messages:', error);\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error in saveChatMessages:', error);\n    return false;\n  }\n};\nexport const clearAllChatSessions = async userId => {\n  try {\n    let query = supabase.from('chat_sessions').delete();\n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n    const {\n      error\n    } = await query;\n    if (error) {\n      console.error('Error clearing chat sessions:', error);\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error in clearAllChatSessions:', error);\n    return false;\n  }\n};\nexport default supabase;", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "supabase", "getDocuments", "data", "error", "from", "select", "order", "ascending", "console", "getDocumentById", "id", "eq", "single", "getDocumentChunks", "documentId", "getWebsites", "getWebsiteById", "saveQuery", "query", "insert", "getRecentQueries", "limit", "uploadFile", "filePath", "file", "onProgress", "storage", "upload", "cacheControl", "upsert", "publicUrl", "getPublicUrl", "path", "getFileUrl", "createChatSession", "title", "modelUsed", "messages", "model_used", "has_document", "has_website", "getChatSessions", "userId", "log", "tableCheck", "tableError", "warn", "message", "sessions", "map", "session", "created_at", "updated_at", "length", "getChatSessionById", "updateChatSession", "updates", "update", "updateChatTitle", "deleteChatSession", "delete", "saveChatMessages", "chatId", "sanitizedMessages", "msg", "content", "document_answer", "undefined", "website_answer", "llm_model", "sender", "loading", "sources", "document_sources", "website_sources", "timestamp", "llm_fallback", "hasDocument", "some", "s", "source_type", "hasWebsite", "messageCount", "messagesWithDocAnswer", "filter", "m", "messagesWithWebAnswer", "clearAllChatSessions"], "sources": ["C:/IR App/frontend/src/services/supabase.ts"], "sourcesContent": ["// Supabase client for frontend\nimport { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://rkllidjktazafeinezgo.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA';\n\n// Create Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Document types\nexport interface Document {\n  id: string;\n  filename: string;\n  display_name?: string;\n  file_path: string;\n  file_type?: string;\n  file_size?: number;\n  main_category?: string;\n  category?: string;\n  sub_category?: string;\n  minor_category?: string;\n  uploaded_by?: string;\n  created_at?: string;\n  updated_at?: string;\n  status?: string;\n  quality_score?: number;\n}\n\nexport interface DocumentChunk {\n  id: string;\n  document_id: string;\n  chunk_index: number;\n  page_number: number;\n  text: string;\n  metadata?: any;\n  created_at?: string;\n}\n\n// Website types\nexport interface Website {\n  id: string;\n  url: string;\n  domain?: string;\n  title?: string;\n  description?: string;\n  category?: string;\n  submitted_by?: string;\n  created_at?: string;\n  updated_at?: string;\n  status?: string;\n  quality_score?: number;\n}\n\nexport interface WebsiteChunk {\n  id: string;\n  website_id: string;\n  chunk_index: number;\n  text: string;\n  metadata?: any;\n  created_at?: string;\n}\n\n// Query types\nexport interface Query {\n  id: string;\n  user_id?: string;\n  query_text: string;\n  answer_text?: string;\n  llm_model?: string;\n  sources?: any;\n  created_at?: string;\n  processing_time?: number;\n}\n\n// Document operations\nexport const getDocuments = async (): Promise<Document[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('documents')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching documents:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocuments:', error);\n    return [];\n  }\n};\n\nexport const getDocumentById = async (id: string): Promise<Document | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('documents')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching document:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in getDocumentById:', error);\n    return null;\n  }\n};\n\nexport const getDocumentChunks = async (documentId: string): Promise<DocumentChunk[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('document_chunks')\n      .select('*')\n      .eq('document_id', documentId)\n      .order('chunk_index', { ascending: true });\n\n    if (error) {\n      console.error('Error fetching document chunks:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocumentChunks:', error);\n    return [];\n  }\n};\n\n// Website operations\nexport const getWebsites = async (): Promise<Website[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('websites')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching websites:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getWebsites:', error);\n    return [];\n  }\n};\n\nexport const getWebsiteById = async (id: string): Promise<Website | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('websites')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching website:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in getWebsiteById:', error);\n    return null;\n  }\n};\n\n// Query operations\nexport const saveQuery = async (query: Omit<Query, 'id' | 'created_at'>): Promise<Query | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('queries')\n      .insert([query])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error saving query:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in saveQuery:', error);\n    return null;\n  }\n};\n\nexport const getRecentQueries = async (limit: number = 10): Promise<Query[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('queries')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(limit);\n\n    if (error) {\n      console.error('Error fetching recent queries:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getRecentQueries:', error);\n    return [];\n  }\n};\n\n// Storage operations\nexport const uploadFile = async (\n  filePath: string,\n  file: File,\n  onProgress?: (progress: number) => void\n): Promise<string | null> => {\n  try {\n    const { data, error } = await supabase.storage\n      .from('documents')\n      .upload(filePath, file, {\n        cacheControl: '3600',\n        upsert: false\n      });\n\n    if (error) {\n      console.error('Error uploading file:', error);\n      return null;\n    }\n\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage\n      .from('documents')\n      .getPublicUrl(data.path);\n\n    return publicUrl;\n  } catch (error) {\n    console.error('Error in uploadFile:', error);\n    return null;\n  }\n};\n\nexport const getFileUrl = (filePath: string): string => {\n  const { data: { publicUrl } } = supabase.storage\n    .from('documents')\n    .getPublicUrl(filePath);\n\n  return publicUrl;\n};\n\n// Chat Session types\nexport interface ChatMessage {\n  id: string;\n  content: string;\n  document_answer?: string;\n  website_answer?: string;\n  llm_model?: string;\n  sender: 'user' | 'ai';\n  loading?: boolean;\n  sources?: Array<any>;\n  document_sources?: Array<any>;\n  website_sources?: Array<any>;\n  timestamp?: string;\n  chatId?: string;\n  llm_fallback?: boolean;\n}\n\nexport interface ChatSession {\n  id: string;\n  user_id?: string;\n  title: string;\n  messages: ChatMessage[];\n  model_used: string;\n  created_at: string;\n  updated_at: string;\n  tags?: string[];\n  has_document: boolean;\n  has_website: boolean;\n}\n\n// Chat Session operations\nexport const createChatSession = async (title: string = 'New Chat', modelUsed: string = 'gemini-2.0-flash'): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .insert([{\n        title,\n        messages: [],\n        model_used: modelUsed,\n        has_document: false,\n        has_website: false\n      }])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error creating chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in createChatSession:', error);\n    return null;\n  }\n};\n\nexport const getChatSessions = async (userId?: string): Promise<ChatSession[]> => {\n  try {\n    console.log('Attempting to fetch chat sessions...');\n\n    // First, try to check if the table exists with a simple query\n    const { data: tableCheck, error: tableError } = await supabase\n      .from('chat_sessions')\n      .select('id')\n      .limit(1);\n\n    if (tableError) {\n      console.warn('Chat sessions table not accessible:', tableError.message);\n      console.log('Returning empty chat sessions array');\n      return [];\n    }\n\n    // If table exists, try the full query with basic columns only\n    let query = supabase\n      .from('chat_sessions')\n      .select('id, title, created_at, updated_at')\n      .order('updated_at', { ascending: false })\n      .limit(10); // Reduced limit\n\n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n\n    const { data, error } = await query;\n\n    if (error) {\n      console.warn('Chat sessions query failed:', error.message);\n      return [];\n    }\n\n    // Process and return data with safe defaults\n    const sessions = (data || []).map(session => ({\n      id: session.id,\n      title: session.title || 'Untitled Chat',\n      created_at: session.created_at,\n      updated_at: session.updated_at,\n      model_used: 'gemini-2.0-flash',\n      has_document: false,\n      has_website: false,\n      messages: []\n    }));\n\n    console.log(`Successfully loaded ${sessions.length} chat sessions`);\n    return sessions;\n\n  } catch (error: any) {\n    console.warn('Error in getChatSessions:', error.message || error);\n    return [];\n  }\n};\n\nexport const getChatSessionById = async (id: string): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in getChatSessionById:', error);\n    return null;\n  }\n};\n\nexport const updateChatSession = async (id: string, updates: Partial<ChatSession>): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error updating chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in updateChatSession:', error);\n    return null;\n  }\n};\n\nexport const updateChatTitle = async (id: string, title: string): Promise<boolean> => {\n  try {\n    const { error } = await supabase\n      .from('chat_sessions')\n      .update({ title })\n      .eq('id', id);\n\n    if (error) {\n      console.error('Error updating chat title:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in updateChatTitle:', error);\n    return false;\n  }\n};\n\nexport const deleteChatSession = async (id: string): Promise<boolean> => {\n  try {\n    const { error } = await supabase\n      .from('chat_sessions')\n      .delete()\n      .eq('id', id);\n\n    if (error) {\n      console.error('Error deleting chat session:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in deleteChatSession:', error);\n    return false;\n  }\n};\n\nexport const saveChatMessages = async (chatId: string, messages: ChatMessage[]): Promise<boolean> => {\n  try {\n    // Ensure all message fields are preserved when saving to Supabase\n    const sanitizedMessages = messages.map(msg => ({\n      id: msg.id,\n      content: msg.content,\n      document_answer: msg.document_answer || undefined,\n      website_answer: msg.website_answer || undefined,\n      llm_model: msg.llm_model,\n      sender: msg.sender,\n      loading: msg.loading,\n      sources: msg.sources || undefined,\n      document_sources: msg.document_sources || undefined,\n      website_sources: msg.website_sources || undefined,\n      timestamp: msg.timestamp,\n      chatId: msg.chatId,\n      llm_fallback: msg.llm_fallback\n    }));\n\n    // Check if chat has documents or websites in the messages\n    const hasDocument = sanitizedMessages.some(msg => \n      msg.document_answer || \n      (msg.document_sources && msg.document_sources.length > 0) ||\n      (msg.sources && msg.sources.some((s: any) => s.source_type === 'document'))\n    );\n    \n    const hasWebsite = sanitizedMessages.some(msg => \n      msg.website_answer || \n      (msg.website_sources && msg.website_sources.length > 0) ||\n      (msg.sources && msg.sources.some((s: any) => s.source_type === 'website'))\n    );\n\n    console.log('Saving chat messages:', {\n      messageCount: sanitizedMessages.length,\n      hasDocument,\n      hasWebsite,\n      messagesWithDocAnswer: sanitizedMessages.filter(m => m.document_answer).length,\n      messagesWithWebAnswer: sanitizedMessages.filter(m => m.website_answer).length\n    });\n\n    const { error } = await supabase\n      .from('chat_sessions')\n      .update({ \n        messages: sanitizedMessages,\n        has_document: hasDocument,\n        has_website: hasWebsite\n      })\n      .eq('id', chatId);\n\n    if (error) {\n      console.error('Error saving chat messages:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in saveChatMessages:', error);\n    return false;\n  }\n};\n\nexport const clearAllChatSessions = async (userId?: string): Promise<boolean> => {\n  try {\n    let query = supabase.from('chat_sessions').delete();\n    \n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n\n    const { error } = await query;\n\n    if (error) {\n      console.error('Error clearing chat sessions:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in clearAllChatSessions:', error);\n    return false;\n  }\n};\n\nexport default supabase;\n"], "mappings": "AAAA;AACA,SAASA,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,0CAA0C;AACpG,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,kNAAkN;;AAErR;AACA,OAAO,MAAMC,QAAQ,GAAGP,YAAY,CAACC,WAAW,EAAEI,eAAe,CAAC;;AAElE;;AA6BA;;AAwBA;;AAYA;AACA,OAAO,MAAMG,YAAY,GAAG,MAAAA,CAAA,KAAiC;EAC3D,IAAI;IACF,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC;IAE5C,IAAIJ,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,EAAE;IACX;IAEA,OAAOD,IAAI,IAAI,EAAE;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMM,eAAe,GAAG,MAAOC,EAAU,IAA+B;EAC7E,IAAI;IACF,MAAM;MAAER,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC,CACZE,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,IAAI;IACb;IAEA,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMU,iBAAiB,GAAG,MAAOC,UAAkB,IAA+B;EACvF,IAAI;IACF,MAAM;MAAEZ,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,iBAAiB,CAAC,CACvBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,aAAa,EAAEG,UAAU,CAAC,CAC7BR,KAAK,CAAC,aAAa,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAE5C,IAAIJ,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO,EAAE;IACX;IAEA,OAAOD,IAAI,IAAI,EAAE;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,WAAW,GAAG,MAAAA,CAAA,KAAgC;EACzD,IAAI;IACF,MAAM;MAAEb,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC;IAE5C,IAAIJ,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,EAAE;IACX;IAEA,OAAOD,IAAI,IAAI,EAAE;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMa,cAAc,GAAG,MAAON,EAAU,IAA8B;EAC3E,IAAI;IACF,MAAM;MAAER,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC,CACZE,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO,IAAI;IACb;IAEA,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,SAAS,GAAG,MAAOC,KAAuC,IAA4B;EACjG,IAAI;IACF,MAAM;MAAEhB,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,SAAS,CAAC,CACfe,MAAM,CAAC,CAACD,KAAK,CAAC,CAAC,CACfb,MAAM,CAAC,CAAC,CACRO,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO,IAAI;IACb;IAEA,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMiB,gBAAgB,GAAG,MAAAA,CAAOC,KAAa,GAAG,EAAE,KAAuB;EAC9E,IAAI;IACF,MAAM;MAAEnB,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC,CACzCc,KAAK,CAACA,KAAK,CAAC;IAEf,IAAIlB,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,EAAE;IACX;IAEA,OAAOD,IAAI,IAAI,EAAE;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMmB,UAAU,GAAG,MAAAA,CACxBC,QAAgB,EAChBC,IAAU,EACVC,UAAuC,KACZ;EAC3B,IAAI;IACF,MAAM;MAAEvB,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CAAC0B,OAAO,CAC3CtB,IAAI,CAAC,WAAW,CAAC,CACjBuB,MAAM,CAACJ,QAAQ,EAAEC,IAAI,EAAE;MACtBI,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE;IACV,CAAC,CAAC;IAEJ,IAAI1B,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO,IAAI;IACb;;IAEA;IACA,MAAM;MAAED,IAAI,EAAE;QAAE4B;MAAU;IAAE,CAAC,GAAG9B,QAAQ,CAAC0B,OAAO,CAC7CtB,IAAI,CAAC,WAAW,CAAC,CACjB2B,YAAY,CAAC7B,IAAI,CAAC8B,IAAI,CAAC;IAE1B,OAAOF,SAAS;EAClB,CAAC,CAAC,OAAO3B,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAM8B,UAAU,GAAIV,QAAgB,IAAa;EACtD,MAAM;IAAErB,IAAI,EAAE;MAAE4B;IAAU;EAAE,CAAC,GAAG9B,QAAQ,CAAC0B,OAAO,CAC7CtB,IAAI,CAAC,WAAW,CAAC,CACjB2B,YAAY,CAACR,QAAQ,CAAC;EAEzB,OAAOO,SAAS;AAClB,CAAC;;AAED;;AA8BA;AACA,OAAO,MAAMI,iBAAiB,GAAG,MAAAA,CAAOC,KAAa,GAAG,UAAU,EAAEC,SAAiB,GAAG,kBAAkB,KAAkC;EAC1I,IAAI;IACF,MAAM;MAAElC,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,eAAe,CAAC,CACrBe,MAAM,CAAC,CAAC;MACPgB,KAAK;MACLE,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAEF,SAAS;MACrBG,YAAY,EAAE,KAAK;MACnBC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC,CACFnC,MAAM,CAAC,CAAC,CACRO,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI;IACb;IAEA,OAAO;MACL,GAAGD,IAAI;MACPmC,QAAQ,EAAEnC,IAAI,CAACmC,QAAQ,IAAI;IAC7B,CAAC;EACH,CAAC,CAAC,OAAOlC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMsC,eAAe,GAAG,MAAOC,MAAe,IAA6B;EAChF,IAAI;IACFlC,OAAO,CAACmC,GAAG,CAAC,sCAAsC,CAAC;;IAEnD;IACA,MAAM;MAAEzC,IAAI,EAAE0C,UAAU;MAAEzC,KAAK,EAAE0C;IAAW,CAAC,GAAG,MAAM7C,QAAQ,CAC3DI,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,IAAI,CAAC,CACZgB,KAAK,CAAC,CAAC,CAAC;IAEX,IAAIwB,UAAU,EAAE;MACdrC,OAAO,CAACsC,IAAI,CAAC,qCAAqC,EAAED,UAAU,CAACE,OAAO,CAAC;MACvEvC,OAAO,CAACmC,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO,EAAE;IACX;;IAEA;IACA,IAAIzB,KAAK,GAAGlB,QAAQ,CACjBI,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,mCAAmC,CAAC,CAC3CC,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC,CACzCc,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd,IAAIqB,MAAM,EAAE;MACVxB,KAAK,GAAGA,KAAK,CAACP,EAAE,CAAC,SAAS,EAAE+B,MAAM,CAAC;IACrC;IAEA,MAAM;MAAExC,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMe,KAAK;IAEnC,IAAIf,KAAK,EAAE;MACTK,OAAO,CAACsC,IAAI,CAAC,6BAA6B,EAAE3C,KAAK,CAAC4C,OAAO,CAAC;MAC1D,OAAO,EAAE;IACX;;IAEA;IACA,MAAMC,QAAQ,GAAG,CAAC9C,IAAI,IAAI,EAAE,EAAE+C,GAAG,CAACC,OAAO,KAAK;MAC5CxC,EAAE,EAAEwC,OAAO,CAACxC,EAAE;MACdyB,KAAK,EAAEe,OAAO,CAACf,KAAK,IAAI,eAAe;MACvCgB,UAAU,EAAED,OAAO,CAACC,UAAU;MAC9BC,UAAU,EAAEF,OAAO,CAACE,UAAU;MAC9Bd,UAAU,EAAE,kBAAkB;MAC9BC,YAAY,EAAE,KAAK;MACnBC,WAAW,EAAE,KAAK;MAClBH,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;IAEH7B,OAAO,CAACmC,GAAG,CAAC,uBAAuBK,QAAQ,CAACK,MAAM,gBAAgB,CAAC;IACnE,OAAOL,QAAQ;EAEjB,CAAC,CAAC,OAAO7C,KAAU,EAAE;IACnBK,OAAO,CAACsC,IAAI,CAAC,2BAA2B,EAAE3C,KAAK,CAAC4C,OAAO,IAAI5C,KAAK,CAAC;IACjE,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMmD,kBAAkB,GAAG,MAAO5C,EAAU,IAAkC;EACnF,IAAI;IACF,MAAM;MAAER,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC,CACZE,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI;IACb;IAEA,OAAO;MACL,GAAGD,IAAI;MACPmC,QAAQ,EAAEnC,IAAI,CAACmC,QAAQ,IAAI;IAC7B,CAAC;EACH,CAAC,CAAC,OAAOlC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMoD,iBAAiB,GAAG,MAAAA,CAAO7C,EAAU,EAAE8C,OAA6B,KAAkC;EACjH,IAAI;IACF,MAAM;MAAEtD,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CACnCI,IAAI,CAAC,eAAe,CAAC,CACrBqD,MAAM,CAACD,OAAO,CAAC,CACf7C,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC,CACZL,MAAM,CAAC,CAAC,CACRO,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI;IACb;IAEA,OAAO;MACL,GAAGD,IAAI;MACPmC,QAAQ,EAAEnC,IAAI,CAACmC,QAAQ,IAAI;IAC7B,CAAC;EACH,CAAC,CAAC,OAAOlC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMuD,eAAe,GAAG,MAAAA,CAAOhD,EAAU,EAAEyB,KAAa,KAAuB;EACpF,IAAI;IACF,MAAM;MAAEhC;IAAM,CAAC,GAAG,MAAMH,QAAQ,CAC7BI,IAAI,CAAC,eAAe,CAAC,CACrBqD,MAAM,CAAC;MAAEtB;IAAM,CAAC,CAAC,CACjBxB,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC;IAEf,IAAIP,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAMwD,iBAAiB,GAAG,MAAOjD,EAAU,IAAuB;EACvE,IAAI;IACF,MAAM;MAAEP;IAAM,CAAC,GAAG,MAAMH,QAAQ,CAC7BI,IAAI,CAAC,eAAe,CAAC,CACrBwD,MAAM,CAAC,CAAC,CACRjD,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC;IAEf,IAAIP,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAM0D,gBAAgB,GAAG,MAAAA,CAAOC,MAAc,EAAEzB,QAAuB,KAAuB;EACnG,IAAI;IACF;IACA,MAAM0B,iBAAiB,GAAG1B,QAAQ,CAACY,GAAG,CAACe,GAAG,KAAK;MAC7CtD,EAAE,EAAEsD,GAAG,CAACtD,EAAE;MACVuD,OAAO,EAAED,GAAG,CAACC,OAAO;MACpBC,eAAe,EAAEF,GAAG,CAACE,eAAe,IAAIC,SAAS;MACjDC,cAAc,EAAEJ,GAAG,CAACI,cAAc,IAAID,SAAS;MAC/CE,SAAS,EAAEL,GAAG,CAACK,SAAS;MACxBC,MAAM,EAAEN,GAAG,CAACM,MAAM;MAClBC,OAAO,EAAEP,GAAG,CAACO,OAAO;MACpBC,OAAO,EAAER,GAAG,CAACQ,OAAO,IAAIL,SAAS;MACjCM,gBAAgB,EAAET,GAAG,CAACS,gBAAgB,IAAIN,SAAS;MACnDO,eAAe,EAAEV,GAAG,CAACU,eAAe,IAAIP,SAAS;MACjDQ,SAAS,EAAEX,GAAG,CAACW,SAAS;MACxBb,MAAM,EAAEE,GAAG,CAACF,MAAM;MAClBc,YAAY,EAAEZ,GAAG,CAACY;IACpB,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMC,WAAW,GAAGd,iBAAiB,CAACe,IAAI,CAACd,GAAG,IAC5CA,GAAG,CAACE,eAAe,IAClBF,GAAG,CAACS,gBAAgB,IAAIT,GAAG,CAACS,gBAAgB,CAACpB,MAAM,GAAG,CAAE,IACxDW,GAAG,CAACQ,OAAO,IAAIR,GAAG,CAACQ,OAAO,CAACM,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACC,WAAW,KAAK,UAAU,CAC3E,CAAC;IAED,MAAMC,UAAU,GAAGlB,iBAAiB,CAACe,IAAI,CAACd,GAAG,IAC3CA,GAAG,CAACI,cAAc,IACjBJ,GAAG,CAACU,eAAe,IAAIV,GAAG,CAACU,eAAe,CAACrB,MAAM,GAAG,CAAE,IACtDW,GAAG,CAACQ,OAAO,IAAIR,GAAG,CAACQ,OAAO,CAACM,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACC,WAAW,KAAK,SAAS,CAC1E,CAAC;IAEDxE,OAAO,CAACmC,GAAG,CAAC,uBAAuB,EAAE;MACnCuC,YAAY,EAAEnB,iBAAiB,CAACV,MAAM;MACtCwB,WAAW;MACXI,UAAU;MACVE,qBAAqB,EAAEpB,iBAAiB,CAACqB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnB,eAAe,CAAC,CAACb,MAAM;MAC9EiC,qBAAqB,EAAEvB,iBAAiB,CAACqB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjB,cAAc,CAAC,CAACf;IACzE,CAAC,CAAC;IAEF,MAAM;MAAElD;IAAM,CAAC,GAAG,MAAMH,QAAQ,CAC7BI,IAAI,CAAC,eAAe,CAAC,CACrBqD,MAAM,CAAC;MACNpB,QAAQ,EAAE0B,iBAAiB;MAC3BxB,YAAY,EAAEsC,WAAW;MACzBrC,WAAW,EAAEyC;IACf,CAAC,CAAC,CACDtE,EAAE,CAAC,IAAI,EAAEmD,MAAM,CAAC;IAEnB,IAAI3D,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAMoF,oBAAoB,GAAG,MAAO7C,MAAe,IAAuB;EAC/E,IAAI;IACF,IAAIxB,KAAK,GAAGlB,QAAQ,CAACI,IAAI,CAAC,eAAe,CAAC,CAACwD,MAAM,CAAC,CAAC;IAEnD,IAAIlB,MAAM,EAAE;MACVxB,KAAK,GAAGA,KAAK,CAACP,EAAE,CAAC,SAAS,EAAE+B,MAAM,CAAC;IACrC;IAEA,MAAM;MAAEvC;IAAM,CAAC,GAAG,MAAMe,KAAK;IAE7B,IAAIf,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,OAAO,KAAK;EACd;AACF,CAAC;AAED,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}