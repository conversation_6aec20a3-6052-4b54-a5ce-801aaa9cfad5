"""
Simple robust fix to force document prioritization in the IR App.
This script makes targeted changes to ensure answers come from documents,
not from Gemini AI unless no relevant documents are found.
"""
import os
import re

def fix_server_file():
    """Apply critical fixes to server.py"""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 1. Lower the relevance threshold dramatically
        if "RELEVANCE_THRESHOLD" in content:
            content = re.sub(
                r'RELEVANCE_THRESHOLD\s*=\s*[0-9.]+', 
                'RELEVANCE_THRESHOLD = 0.05', 
                content
            )
            print("Lowered relevance threshold to accept more document matches")
        
        # 2. Update the document filter to be much more permissive
        if "(chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >=" in content:
            content = content.replace(
                "(chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= (RELEVANCE_THRESHOLD * 0.5))",
                "(chunk.get(\"source_type\") == \"document\" and chunk[\"similarity\"] >= 0.01)"  # Almost no threshold
            )
            print("Made document filter extremely permissive")
        
        # 3. Replace the document evaluation function
        if "def has_sufficient_document_answers(document_chunks):" in content:
            old_func_start = content.find("def has_sufficient_document_answers(document_chunks):")
            
            if old_func_start != -1:
                # Find the next function
                next_func = content.find("def ", old_func_start + 10)
                
                if next_func != -1:
                    # Create new function
                    new_func = """def has_sufficient_document_answers(document_chunks):
    # Always return True if there are ANY document chunks
    # This ensures we always prioritize document content
    if document_chunks and len(document_chunks) > 0:
        logger.info(f"Found {len(document_chunks)} document chunks - using ONLY document content")
        return True
    
    logger.warning("No document chunks found for query")
    return False
"""
                    
                    # Replace the function
                    content = content[:old_func_start] + new_func + content[next_func:]
                    print("Replaced document evaluation function")
        
        # 4. Update process_query to boost document chunks
        if "# Prioritize document and website sources" in content:
            priority_start = content.find("# Prioritize document and website sources")
            
            if priority_start != -1:
                # Find the end of this section
                priority_end = content.find("document_sources =", priority_start)
                
                if priority_end != -1:
                    # Create new prioritization logic
                    new_priority = """        # Prioritize document and website sources
        document_chunks = []
        website_chunks = []
        other_chunks = []
        
        # Sort chunks by source type with extreme priority to documents
        for chunk in similar_chunks:
            source_type = chunk.get("source_type", "unknown")
            
            if source_type == "document":
                # Force extremely high similarity for documents
                chunk["similarity"] = 0.99
                document_chunks.append(chunk)
            elif source_type == "website":
                # Boost websites too
                chunk["similarity"] = min(0.9, chunk["similarity"] * 1.5)
                website_chunks.append(chunk)
            else:
                other_chunks.append(chunk)
        
        # CRITICAL: If ANY documents exist, use ONLY those
        if document_chunks:
            logger.info(f"FORCING document-only mode ({len(document_chunks)} document chunks)")
            similar_chunks = document_chunks
        elif website_chunks:
            logger.info(f"Using website sources ({len(website_chunks)} website chunks)")
            similar_chunks = website_chunks"""
                    
                    # Replace the prioritization logic
                    content = content[:priority_start] + new_priority + "\n\n        # Prepare source information\n        " + content[priority_end:]
                    print("Enhanced document prioritization in query processing")
        
        # 5. Override fallback logic to prefer documents
        if "fallback_to_ai =" in content:
            fallback_pos = content.find("fallback_to_ai =")
            
            if fallback_pos != -1:
                # Find end of line
                line_end = content.find("\n", fallback_pos)
                
                if line_end != -1:
                    # Replace with forced document priority
                    content = content[:fallback_pos] + "fallback_to_ai = False if document_chunks else (False if website_chunks else request.fallback_enabled)" + content[line_end:]
                    print("Disabled AI fallback when documents or websites are available")
        
        # 6. Update system prompt to emphasize document usage
        if "system_prompt = f'''" in content:
            prompt_start = content.find("system_prompt = f'''")
            
            if prompt_start != -1:
                # Find end of prompt
                prompt_end = content.find("'''", prompt_start + 20)
                
                if prompt_end != -1:
                    # Get indentation
                    indent = ""
                    for i in range(prompt_start-1, 0, -1):
                        if content[i] == "\n":
                            break
                        indent = content[i] + indent
                    
                    # Create new system prompt
                    new_prompt = indent + """system_prompt = f'''
You are an expert information retrieval assistant that ONLY uses provided information.

CRITICAL INSTRUCTIONS:
1. If the context contains DOCUMENT sources, you MUST use ONLY those and ignore everything else.
2. If the context contains WEBSITE sources but no DOCUMENT sources, use only the website information.
3. You MUST include source references for every piece of information.
4. NEVER use your general knowledge unless explicitly told there is no relevant information in the context.
5. If the context does not contain information to answer the question, say "I don't have specific information about this in the available documents."
6. NEVER make up information or hallucinate details not in the provided context.

Remember, DOCUMENT sources are the absolute highest priority. If they exist, use ONLY those.

CONTEXT:
{context_str}
'''"""
                    
                    # Replace the prompt
                    content = content[:prompt_start] + new_prompt + content[prompt_end+3:]
                    print("Updated system prompt to enforce document usage")
        
        # Write changes back
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error updating server.py: {str(e)}")
        return False

def fix_llm_router():
    """Apply critical fixes to llm_router.py"""
    try:
        with open("llm_router.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 1. Update generate_answer to force document sources
        if "def generate_answer(" in content:
            gen_pos = content.find("def generate_answer(")
            
            if gen_pos != -1:
                # Look for the context processing
                context_pos = content.find("# Format context for prompt", gen_pos)
                
                if context_pos != -1:
                    # Look for the context loop
                    for_loop = content.find("for i, context_item in enumerate(context):", context_pos)
                    
                    if for_loop != -1:
                        # Add document filtering before the loop
                        doc_filter = """    # CRITICAL: Filter for document sources only
    document_items = [item for item in context if item.get("source_type") == "document"]
    website_items = [item for item in context if item.get("source_type") == "website"]
    
    # If document sources exist, use ONLY those
    if document_items:
        logger.info(f"FORCING document-only mode in LLM ({len(document_items)} document items)")
        context = document_items
    elif website_items and use_documents_only:
        logger.info(f"Using website sources in LLM ({len(website_items)} website items)")
        context = website_items
    
    """
                        
                        # Insert before the context loop
                        content = content[:for_loop] + doc_filter + content[for_loop:]
                        print("Added document filtering in LLM router")
        
        # 2. Update user message to emphasize document usage
        if "messages = [" in content:
            msg_pos = content.find("messages = [")
            
            if msg_pos != -1:
                # Find user message
                user_msg = content.find('{"role": "user",', msg_pos)
                
                if user_msg != -1:
                    # Replace with enhanced message
                    new_msg = """        {"role": "user", "content": f'''
CRITICAL INSTRUCTION:
You MUST answer this question using ONLY the information provided in the context below.
If document sources exist in the context, you MUST use ONLY those and nothing else.
Never use information outside what is explicitly provided in the context.

Question: {query}

Context:
{formatted_query}

Remember: If you can't find relevant information in the context, say "I don't have specific information about this in the available documents."
'''}"""
                    
                    # Find end of user message
                    msg_end = content.find("}]", user_msg)
                    
                    if msg_end != -1:
                        # Replace user message
                        content = content[:user_msg] + new_msg + content[msg_end-3:]
                        print("Enhanced user message in LLM router")
        
        # Write changes back
        with open("llm_router.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error updating llm_router.py: {str(e)}")
        return False

def fix_vector_db():
    """Apply critical fixes to vector_db.py"""
    try:
        with open("vector_db.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 1. Lower search thresholds
        if "def search_documents" in content:
            content = re.sub(
                r'def search_documents\(self, query_embedding: List\[float\], match_threshold: float = [0-9.]+',
                'def search_documents(self, query_embedding: List[float], match_threshold: float = 0.01',
                content
            )
            print("Lowered search threshold dramatically")
        
        if "def hybrid_search" in content:
            content = re.sub(
                r'def hybrid_search\(self, query_text: str, query_embedding: List\[float\], match_threshold: float = [0-9.]+',
                'def hybrid_search(self, query_text: str, query_embedding: List[float], match_threshold: float = 0.01',
                content
            )
            print("Lowered hybrid search threshold dramatically")
        
        # 2. Force document inclusion in results
        if "# Return the top matches" in content:
            return_pos = content.find("# Return the top matches")
            
            if return_pos != -1:
                # Find return statement
                ret_stmt = content.find("return", return_pos)
                
                if ret_stmt != -1:
                    # Add document forcing logic
                    force_docs = """        # CRITICAL: Force include document results
        doc_results = [r for r in combined_results if r.get("source_type") == "document"]
        
        if not doc_results:
            # Look for any document in semantic or keyword results
            all_docs = [r for r in semantic_results + keyword_results if r.get("source_type") == "document"]
            
            if all_docs:
                # Sort by highest similarity
                all_docs.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                # Force high similarity
                all_docs[0]["similarity"] = 0.99
                # Insert at beginning
                logger.info("FORCING document inclusion in search results")
                combined_results.insert(0, all_docs[0])
        
        """
                    
                    # Insert before return statement
                    content = content[:ret_stmt] + force_docs + content[ret_stmt:]
                    print("Added forced document inclusion in search results")
        
        # Write changes back
        with open("vector_db.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error updating vector_db.py: {str(e)}")
        return False

def main():
    print("\n=== FORCING DOCUMENT ANSWERS ===\n")
    
    # 1. Fix server file
    print("Updating server.py...")
    if fix_server_file():
        print("+ Successfully updated server.py")
    else:
        print("- Failed to update server.py")
    
    # 2. Fix LLM router
    print("\nUpdating llm_router.py...")
    if fix_llm_router():
        print("+ Successfully updated llm_router.py")
    else:
        print("- Failed to update llm_router.py")
    
    # 3. Fix vector database
    print("\nUpdating vector_db.py...")
    if fix_vector_db():
        print("+ Successfully updated vector_db.py")
    else:
        print("- Failed to update vector_db.py")
    
    print("\n=== DOCUMENT PRIORITIZATION COMPLETE ===")
    print("\nThese changes will FORCE your system to use document and website content")
    print("instead of generating answers from Gemini AI.")
    print("\nRestart your server to apply these changes:")
    print("python -m uvicorn server:app --reload")

if __name__ == "__main__":
    main()
