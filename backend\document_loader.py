import os
import re
import fitz  # PyMuPDF
from typing import List, Dict, Any
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global variable to store document chunks
DOCUMENT_CHUNKS = []

def clean_text(text: str) -> str:
    """Clean and normalize text from PDF extraction."""
    # Replace multiple whitespace with single space
    text = re.sub(r'\s+', ' ', text)
    # Remove any leading/trailing whitespace
    text = text.strip()
    return text

def create_chunks(text: str, filename: str, page_num: int, target_size: int = 400, overlap: int = 50) -> List[Dict[str, Any]]:
    """
    Split text into chunks of approximately target_size words with overlap.
    
    Args:
        text: Text to split into chunks
        filename: Source PDF filename
        page_num: Page number in the PDF
        target_size: Target chunk size in words
        overlap: Number of words to overlap between chunks
        
    Returns:
        List of chunk dictionaries with metadata
    """
    words = text.split()
    total_words = len(words)
    chunks = []
    
    if total_words == 0:
        return chunks
    
    # If text is smaller than target size, return as a single chunk
    if total_words <= target_size:
        chunk_id = f"{os.path.splitext(filename)[0]}_{page_num}_0"
        chunks.append({
            "filename": filename,
            "page": page_num,
            "chunk_id": chunk_id,
            "text": text
        })
        return chunks
    
    # Create overlapping chunks
    chunk_index = 0
    start_idx = 0
    
    while start_idx < total_words:
        end_idx = min(start_idx + target_size, total_words)
        
        # If this is not the last chunk, adjust end_idx to include overlap in the next chunk
        if end_idx < total_words:
            # Create chunk
            chunk_text = " ".join(words[start_idx:end_idx])
            chunk_id = f"{os.path.splitext(filename)[0]}_{page_num}_{chunk_index}"
            chunks.append({
                "filename": filename,
                "page": page_num,
                "chunk_id": chunk_id,
                "text": chunk_text
            })
            
            # Move to next chunk start, accounting for overlap
            start_idx = end_idx - overlap
            chunk_index += 1
        else:
            # Last chunk
            chunk_text = " ".join(words[start_idx:end_idx])
            chunk_id = f"{os.path.splitext(filename)[0]}_{page_num}_{chunk_index}"
            chunks.append({
                "filename": filename,
                "page": page_num,
                "chunk_id": chunk_id,
                "text": chunk_text
            })
            break
    
    return chunks

def load_documents(data_dir: str = './data') -> List[Dict[str, Any]]:
    """
    Load and process all PDF documents from a directory.
    
    Args:
        data_dir: Directory containing PDF files
        
    Returns:
        List of text chunks with metadata
    """
    global DOCUMENT_CHUNKS
    DOCUMENT_CHUNKS = []
    
    # Ensure data directory exists
    if not os.path.exists(data_dir):
        logger.warning(f"Data directory {data_dir} does not exist")
        return DOCUMENT_CHUNKS
    
    # Get all PDF files in the directory
    pdf_files = [f for f in os.listdir(data_dir) if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        logger.warning(f"No PDF files found in {data_dir}")
        return DOCUMENT_CHUNKS
    
    logger.info(f"Found {len(pdf_files)} PDF files in {data_dir}")
    
    # Process each PDF file
    for filename in pdf_files:
        file_path = os.path.join(data_dir, filename)
        logger.info(f"Processing {file_path}")
        
        try:
            # Open PDF
            doc = fitz.open(file_path)
            
            # Process each page
            for page_num, page in enumerate(doc, 1):  # 1-based page numbering for readability
                text = page.get_text()
                cleaned_text = clean_text(text)
                
                if not cleaned_text:
                    logger.warning(f"No text extracted from page {page_num} in {filename}")
                    continue
                
                # Create chunks from page text
                page_chunks = create_chunks(cleaned_text, filename, page_num)
                DOCUMENT_CHUNKS.extend(page_chunks)
                
                logger.info(f"Processed page {page_num} of {filename}: created {len(page_chunks)} chunks")
            
            # Close the document
            doc.close()
            
        except Exception as e:
            logger.error(f"Error processing {filename}: {str(e)}")
    
    logger.info(f"Finished processing. Total chunks created: {len(DOCUMENT_CHUNKS)}")
    return DOCUMENT_CHUNKS

# If this script is run directly, load documents from the default location
if __name__ == "__main__":
    chunks = load_documents()
    print(f"Loaded {len(chunks)} chunks")
    
    # Print the first chunk for verification
    if chunks:
        print("\nSample chunk:")
        print(chunks[0])
