import os
import time
from dotenv import load_dotenv
from supabase_client import SupabaseClient
from document_extractor import extract_document
from website_scraper import extract_website_text
import google.generativeai as genai

def test_end_to_end():
    # Load environment variables
    load_dotenv()
    
    # Initialize Supabase client
    client = SupabaseClient()
    
    # Initialize Gemini
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        raise ValueError("GEMINI_API_KEY not found in environment variables")
    genai.configure(api_key=api_key)
    
    print("\n=== Starting End-to-End Test ===\n")
    
    try:
        # 1. Test Website Extraction
        print("1. Testing Website Extraction...")
        website_url = "https://www.rapidresponseapp.com"
        website_text = extract_website_text(website_url)
        print(f"Website text extracted (first 200 chars): {website_text[:200]}")
        
        # Store website
        website_result = client.store_website(
            url=website_url,
            domain="rapidresponseapp.com",
            title="Rapid Response App",
            description="Rapid Response App website"
        )
        print(f"Website stored: {website_result}")
        
        # Create website chunks
        website_chunks = [
            {
                "website_id": website_result.get("id"),
                "chunk_index": i,
                "text": chunk,
                "embedding": [0.1] * 768,  # Placeholder embedding
                "metadata": {"source": "website", "url": website_url}
            }
            for i, chunk in enumerate([website_text[i:i+500] for i in range(0, len(website_text), 500)])
        ]
        
        for chunk in website_chunks:
            chunk_result = client.store_website_chunk(**chunk)
            print(f"Website chunk stored: {chunk_result}")
        
        # 2. Test Document Processing
        print("\n2. Testing Document Processing...")
        
        # Create a test document
        test_doc_path = "test_document.txt"
        with open(test_doc_path, "w") as f:
            f.write("This is a test document for the Rapid Response App. It contains information about the application's features and capabilities.")
        
        # Store document
        doc_result = client.store_document(
            filename=test_doc_path,
            display_name="Test Document",
            file_path=test_doc_path,
            file_type="text/plain",
            file_size=os.path.getsize(test_doc_path)
        )
        print(f"Document stored: {doc_result}")
        
        # Create document chunks
        doc_chunks = [
            {
                "document_id": doc_result.get("id"),
                "chunk_index": 0,
                "page_number": 1,
                "text": "This is a test document for the Rapid Response App. It contains information about the application's features and capabilities.",
                "embedding": [0.1] * 768,  # Placeholder embedding
                "metadata": {"source": "document", "filename": test_doc_path}
            }
        ]
        
        for chunk in doc_chunks:
            chunk_result = client.store_document_chunk(**chunk)
            print(f"Document chunk stored: {chunk_result}")
        
        # 3. Test Query Processing
        print("\n3. Testing Query Processing...")
        
        # Generate embedding for query
        query_text = "What is Rapid Response App?"
        query_embedding = [0.1] * 768  # Placeholder embedding
        
        # Search documents and websites
        doc_results = client.search_documents(query_embedding, match_count=5)
        web_results = client.search_websites(query_embedding, match_count=5)
        
        print(f"Document search results: {doc_results}")
        print(f"Website search results: {web_results}")
        
        # Generate answer using Gemini
        model = genai.GenerativeModel('gemini-2.0-flash')
        context = "\n".join([
            f"Document: {doc.get('text', '')}" for doc in doc_results
        ] + [
            f"Website: {web.get('text', '')}" for web in web_results
        ])
        
        prompt = f"""Based on the following context, answer the question: {query_text}

Context:
{context}

Answer:"""
        
        response = model.generate_content(prompt)
        answer = response.text
        
        # Store query and answer
        query_result = client.store_query(
            query_text=query_text,
            answer_text=answer,
            llm_model="gemini-2.0-flash",
            sources=[
                {"type": "document", "id": doc.get("document_id")} for doc in doc_results
            ] + [
                {"type": "website", "id": web.get("website_id")} for web in web_results
            ],
            processing_time=0.5
        )
        
        print(f"\nQuery: {query_text}")
        print(f"Answer: {answer}")
        print(f"Sources: {query_result.get('sources', [])}")
        
        # Cleanup
        os.remove(test_doc_path)
        
        print("\n=== End-to-End Test Completed Successfully ===\n")
        return True
        
    except Exception as e:
        print(f"\nTest failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_end_to_end() 