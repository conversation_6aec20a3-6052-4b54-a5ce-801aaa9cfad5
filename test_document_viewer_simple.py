#!/usr/bin/env python3
"""
Simple test for document viewer endpoint
"""

import requests
import os

API_BASE = "http://localhost:8000"

def test_file_paths():
    """Test if files exist in expected locations"""
    print("=== Testing File Paths ===")
    
    test_files = [
        "SampleRailwayDoc.pdf",
        "ACP 110V.docx", 
        "Local component Declaration.pdf"
    ]
    
    possible_locations = [
        "backend/data/uploads/",
        "backend/data/",
        "data/uploads/",
        "data/",
        ""
    ]
    
    for filename in test_files:
        print(f"\nChecking {filename}:")
        found = False
        for location in possible_locations:
            full_path = os.path.join(location, filename)
            if os.path.exists(full_path):
                print(f"  ✅ Found at: {full_path}")
                found = True
                break
        if not found:
            print(f"  ❌ Not found in any location")

def test_document_viewer_endpoint():
    """Test the document viewer endpoint"""
    print("\n=== Testing Document Viewer Endpoint ===")
    
    test_files = [
        "SampleRailwayDoc.pdf",
        "ACP 110V.docx"
    ]
    
    for filename in test_files:
        print(f"\nTesting {filename}:")
        try:
            url = f"{API_BASE}/api/documents/view/{filename}"
            print(f"  URL: {url}")
            
            response = requests.head(url, timeout=10)
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  ✅ Success!")
                print(f"  Content-Type: {response.headers.get('content-type', 'unknown')}")
                print(f"  Content-Length: {response.headers.get('content-length', 'unknown')}")
            elif response.status_code == 404:
                print(f"  ❌ File not found")
            else:
                print(f"  ❌ Error: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Request failed: {str(e)}")

def main():
    """Run tests"""
    print("🧪 Simple Document Viewer Test")
    print("=" * 50)
    
    test_file_paths()
    test_document_viewer_endpoint()
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    main() 