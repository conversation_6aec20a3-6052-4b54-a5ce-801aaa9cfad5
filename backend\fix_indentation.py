#!/usr/bin/env python3
"""
Script to fix indentation errors in server.py
"""

import os

def fix_indentation():
    """Fix specific indentation issues in server.py"""
    
    # Read the file
    with open('server.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"Total lines in file: {len(lines)}")
    
    # Fix line 2268 (0-indexed 2267)
    if len(lines) > 2267:
        print(f"Line 2268 before: {repr(lines[2267])}")
        if lines[2267].strip().startswith('document_chunks = extract_document('):
            lines[2267] = '                document_chunks = extract_document(\n'
            print(f"Line 2268 after: {repr(lines[2267])}")
    
    # Fix the lines around 3430 (0-indexed 3429)
    if len(lines) > 3429:
        print(f"Line 3430 before: {repr(lines[3429])}")
        if 'context_texts.append' in lines[3429] and not lines[3429].startswith('                    context_texts'):
            lines[3429] = lines[3429].replace('context_texts.append', '                    context_texts.append')
            print(f"Line 3430 after: {repr(lines[3429])}")
    
    # Fix the source_key line
    for i, line in enumerate(lines):
        if 'source_key = f"{filename}"' in line and not line.startswith('                    source_key'):
            lines[i] = '                    source_key = f"{filename}"\n'
            print(f"Fixed source_key line {i+1}: {repr(lines[i])}")
            break
    
    # Write the fixed file
    with open('server.py', 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print("✅ Indentation fixes applied to server.py")

if __name__ == "__main__":
    fix_indentation() 