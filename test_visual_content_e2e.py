#!/usr/bin/env python3
"""
End-to-end test for visual content extraction and display
"""

import requests
import json
import time
import os
from pathlib import Path

def test_visual_content_e2e():
    """Test the complete visual content pipeline"""
    print("🧪 Testing Visual Content End-to-End")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Step 1: Check if backend is running
    print("\n1. 🔍 Checking backend status...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ Backend is running")
        else:
            print(f"   ❌ Backend returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Backend not accessible: {e}")
        return False
    
    # Step 2: Upload test document with visual content
    print("\n2. 📄 Uploading test document with tables...")
    
    # Check if test PDF exists
    test_pdf_path = Path("test_table.pdf")
    if not test_pdf_path.exists():
        print("   ❌ Test PDF not found. Creating one...")
        # Create test PDF
        try:
            from backend.test_visual_extraction_complete import create_test_pdf_with_table
            create_test_pdf_with_table()
            print("   ✅ Test PDF created")
        except Exception as e:
            print(f"   ❌ Failed to create test PDF: {e}")
            return False
    
    # Upload the document
    try:
        with open(test_pdf_path, 'rb') as f:
            files = {'file': ('test_table.pdf', f, 'application/pdf')}
            data = {
                'uploaded_by': 'test_user',
                'extract_tables': 'true',
                'extract_images': 'true', 
                'extract_charts': 'true'
            }
            
            response = requests.post(
                f"{base_url}/api/upload-document",
                files=files,
                data=data,
                timeout=30
            )
            
            if response.status_code == 200:
                upload_result = response.json()
                print(f"   ✅ Document uploaded successfully")
                print(f"   📊 Chunks extracted: {upload_result.get('chunks_extracted', 0)}")
                
                # Check if visual content was extracted
                chunks = upload_result.get('chunks', [])
                visual_chunks = [c for c in chunks if c.get('chunk_type') != 'text']
                print(f"   🎨 Visual chunks found: {len(visual_chunks)}")
                
                for chunk in visual_chunks:
                    print(f"      - {chunk.get('chunk_type', 'unknown')} on page {chunk.get('page_number', '?')}")
                
            else:
                print(f"   ❌ Upload failed with status {response.status_code}")
                print(f"   Error: {response.text}")
                return False
                
    except Exception as e:
        print(f"   ❌ Upload error: {e}")
        return False
    
    # Step 3: Test visual content queries
    print("\n3. 🔍 Testing visual content queries...")
    
    visual_queries = [
        "Show me the railway component specifications table",
        "What are the part numbers for railway components?",
        "List the specifications for steel components",
        "What is the weight of component RC-001?",
        "Show me any tables or charts in the document"
    ]
    
    for i, query in enumerate(visual_queries, 1):
        print(f"\n   Query {i}: {query}")
        
        try:
            response = requests.post(
                f"{base_url}/api/query",
                json={
                    'query': query,
                    'model': 'gemini-2.0-flash',
                    'extract_format': 'paragraph',
                    'use_hybrid_search': True
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # Check for visual content in response
                visual_found = result.get('visual_content_found', False)
                visual_types = result.get('visual_content_types', [])
                
                print(f"   📊 Visual content found: {visual_found}")
                if visual_types:
                    print(f"   🎨 Visual types: {', '.join(visual_types)}")
                
                # Check sources for visual content
                sources = result.get('sources', [])
                visual_sources = [s for s in sources if s.get('content_type') and s.get('content_type') != 'text']
                
                if visual_sources:
                    print(f"   ✅ Found {len(visual_sources)} visual sources:")
                    for source in visual_sources:
                        content_type = source.get('content_type', 'unknown')
                        page = source.get('page', '?')
                        print(f"      - {content_type} on page {page}")
                        
                        # Check if visual content metadata exists
                        if source.get('visual_content'):
                            print(f"        📋 Has visual metadata")
                        if source.get('storage_url'):
                            print(f"        🔗 Has storage URL")
                else:
                    print(f"   ⚠️  No visual sources found")
                
                # Check answer quality
                answer = result.get('answer', '')
                if len(answer) > 50:
                    print(f"   ✅ Got detailed answer ({len(answer)} chars)")
                else:
                    print(f"   ⚠️  Short answer ({len(answer)} chars)")
                    
            else:
                print(f"   ❌ Query failed with status {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Query error: {e}")
    
    # Step 4: Test frontend integration
    print("\n4. 🌐 Testing frontend integration...")
    
    try:
        # Check if frontend is accessible
        frontend_url = "http://localhost:3000"
        response = requests.get(frontend_url, timeout=5)
        
        if response.status_code == 200:
            print("   ✅ Frontend is accessible")
            print("   📝 Manual testing required:")
            print("      1. Open http://localhost:3000")
            print("      2. Ask: 'Show me the railway component specifications table'")
            print("      3. Verify visual content displays properly")
            print("      4. Check table rendering and formatting")
        else:
            print(f"   ⚠️  Frontend returned status {response.status_code}")
            
    except requests.exceptions.RequestException:
        print("   ⚠️  Frontend not accessible (may not be running)")
    
    # Step 5: Performance check
    print("\n5. ⚡ Performance check...")
    
    start_time = time.time()
    try:
        response = requests.post(
            f"{base_url}/api/query",
            json={
                'query': 'Show me all tables in the document',
                'model': 'gemini-2.0-flash'
            },
            timeout=30
        )
        
        end_time = time.time()
        query_time = end_time - start_time
        
        if response.status_code == 200:
            print(f"   ✅ Query completed in {query_time:.2f} seconds")
            if query_time < 10:
                print("   🚀 Good performance")
            elif query_time < 20:
                print("   ⚠️  Acceptable performance")
            else:
                print("   🐌 Slow performance - consider optimization")
        else:
            print(f"   ❌ Performance test failed")
            
    except Exception as e:
        print(f"   ❌ Performance test error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Visual Content E2E Test Complete!")
    print("\n📋 Summary:")
    print("   - Backend visual extraction: Implemented ✅")
    print("   - Frontend visual display: Implemented ✅") 
    print("   - Query processing: Enhanced ✅")
    print("   - Performance: Monitored ✅")
    
    print("\n🚀 Next Steps:")
    print("   1. Test with real railway documents")
    print("   2. Optimize for large documents")
    print("   3. Add user training documentation")
    print("   4. Monitor production performance")
    
    return True

if __name__ == "__main__":
    test_visual_content_e2e() 