# RailGPT Hosting Guide

## Application Overview

RailGPT is a full-stack application with:
- **Frontend**: React TypeScript application (port 3000)
- **Backend**: FastAPI Python server (port 8000)
- **Database**: Supabase (PostgreSQL with vector extensions)
- **File Storage**: Local file system for document uploads

## Prerequisites

### Environment Variables Required
Create a `.env` file in the backend directory:

```env
# Supabase Configuration
SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co
SUPABASE_KEY=your_service_key_here
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA

# API Keys
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GROQ_API_KEY=your_groq_api_key_here

# Application Settings
API_HOST=0.0.0.0
API_PORT=8000
ENVIRONMENT=production
LOG_LEVEL=INFO
```

## Hosting Options

### Option 1: Cloud Platform Deployment (Recommended)

#### 1.1 Vercel + Railway (Easiest)

**Frontend on Vercel:**
1. Push your code to GitHub
2. Connect Vercel to your repository
3. Set build settings:
   - Build Command: `cd frontend && npm run build`
   - Output Directory: `frontend/build`
   - Install Command: `cd frontend && npm install`

**Backend on Railway:**
1. Connect Railway to your GitHub repository
2. Create a new service from the backend folder
3. Set environment variables in Railway dashboard
4. Railway will auto-deploy on git push

#### 1.2 Netlify + Heroku

**Frontend on Netlify:**
1. Connect Netlify to your GitHub repository
2. Set build settings:
   - Build command: `cd frontend && npm run build`
   - Publish directory: `frontend/build`

**Backend on Heroku:**
1. Create a `Procfile` in the backend directory:
   ```
   web: uvicorn server:app --host 0.0.0.0 --port $PORT
   ```
2. Deploy to Heroku with environment variables

#### 1.3 AWS/GCP/Azure (Advanced)

**Frontend**: Deploy to S3/CloudFront, GCS/CDN, or Azure Static Web Apps
**Backend**: Deploy to EC2/ECS, Compute Engine/Cloud Run, or Azure Container Instances

### Option 2: VPS/Dedicated Server

#### 2.1 DigitalOcean Droplet / Linode / Vultr

**Server Requirements:**
- OS: Ubuntu 20.04+ or CentOS 8+
- RAM: 2GB minimum, 4GB recommended
- Storage: 20GB minimum, 50GB recommended
- CPU: 2 cores minimum

**Setup Steps:**

1. **Install Dependencies:**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python 3.9+
sudo apt install python3 python3-pip python3-venv -y

# Install Node.js 16+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Nginx
sudo apt install nginx -y

# Install PM2 for process management
sudo npm install -g pm2
```

2. **Setup Application:**
```bash
# Clone your repository
git clone https://github.com/yourusername/railgpt.git
cd railgpt

# Setup backend
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Setup frontend
cd ../frontend
npm install
npm run build
```

3. **Configure PM2:**
Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'railgpt-backend',
    script: 'venv/bin/uvicorn',
    args: 'server:app --host 0.0.0.0 --port 8000',
    cwd: './backend',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

4. **Configure Nginx:**
Create `/etc/nginx/sites-available/railgpt`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Frontend
    location / {
        root /path/to/railgpt/frontend/build;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # File uploads
    client_max_body_size 200M;
}
```

5. **Start Services:**
```bash
# Enable Nginx site
sudo ln -s /etc/nginx/sites-available/railgpt /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

# Start backend with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Option 3: Docker Deployment

#### 3.1 Create Docker Files

**Backend Dockerfile:**
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8000"]
```

**Frontend Dockerfile:**
```dockerfile
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

**Docker Compose:**
```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - ./backend/data:/app/data

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
```

#### 3.2 Deploy with Docker

**Local/VPS Deployment:**
```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f
```

**Cloud Deployment:**
- **AWS ECS/Fargate**
- **Google Cloud Run**
- **Azure Container Instances**
- **DigitalOcean App Platform**

### Option 4: Serverless Deployment

#### 4.1 Vercel (Full Stack)

Create `vercel.json`:
```json
{
  "builds": [
    {
      "src": "frontend/package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "build"
      }
    },
    {
      "src": "backend/server.py",
      "use": "@vercel/python"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/backend/server.py"
    },
    {
      "src": "/(.*)",
      "dest": "/frontend/$1"
    }
  ]
}
```

## Domain and SSL Setup

### 1. Domain Configuration
- Point your domain to your server's IP address
- Configure DNS A records
- Set up CNAME for www subdomain

### 2. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Environment-Specific Configurations

### Production Optimizations

**Backend:**
- Set `ENVIRONMENT=production`
- Use production-grade WSGI server (Gunicorn)
- Enable logging and monitoring
- Set up health checks

**Frontend:**
- Build with production optimizations
- Enable gzip compression
- Set up CDN for static assets
- Configure caching headers

### Security Considerations

1. **API Security:**
   - Use HTTPS only
   - Implement rate limiting
   - Validate all inputs
   - Use secure headers

2. **Database Security:**
   - Use Supabase RLS policies
   - Rotate API keys regularly
   - Monitor access logs

3. **File Upload Security:**
   - Validate file types
   - Scan for malware
   - Limit file sizes
   - Use secure storage

## Monitoring and Maintenance

### 1. Application Monitoring
- Set up health check endpoints
- Monitor server resources
- Track application logs
- Set up alerts for errors

### 2. Backup Strategy
- Regular database backups (Supabase handles this)
- Backup uploaded documents
- Version control for code
- Document recovery procedures

### 3. Updates and Maintenance
- Regular security updates
- Dependency updates
- Performance monitoring
- User feedback collection

## Cost Estimation

### Free Tier Options:
- **Vercel**: Free for personal projects
- **Netlify**: Free tier available
- **Railway**: $5/month for hobby projects
- **Supabase**: Free tier with limitations

### Paid Options:
- **VPS**: $5-20/month (DigitalOcean, Linode)
- **Cloud Platforms**: $10-50/month depending on usage
- **Enterprise**: $100+/month for high availability

## Troubleshooting

### Common Issues:
1. **CORS errors**: Configure proper CORS settings
2. **File upload failures**: Check file size limits
3. **Database connection issues**: Verify Supabase credentials
4. **Build failures**: Check Node.js/Python versions

### Debug Commands:
```bash
# Check backend logs
pm2 logs railgpt-backend

# Check frontend build
npm run build

# Test API endpoints
curl http://localhost:8000/health

# Check Nginx configuration
sudo nginx -t
```

## Next Steps

1. Choose your hosting option based on budget and requirements
2. Set up your environment variables
3. Deploy the application
4. Configure domain and SSL
5. Set up monitoring and backups
6. Test all functionality in production

For specific deployment help, refer to the detailed guides for each hosting platform.
