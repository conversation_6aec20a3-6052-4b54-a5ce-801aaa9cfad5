#!/bin/bash

# RailGPT Deployment Script
# This script automates the deployment process for RailGPT

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command_exists docker; then
        missing_deps+=("docker")
    fi
    
    if ! command_exists docker-compose; then
        missing_deps+=("docker-compose")
    fi
    
    if ! command_exists git; then
        missing_deps+=("git")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        print_status "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Function to check environment variables
check_environment() {
    print_status "Checking environment variables..."
    
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating template..."
        cat > .env << EOF
# Supabase Configuration
SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co
SUPABASE_KEY=your_service_key_here
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrbGxpZGprdGF6YWZlaW5lemdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5OTU5OTksImV4cCI6MjA2MjU3MTk5OX0.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA

# API Keys
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GROQ_API_KEY=your_groq_api_key_here

# Application Settings
API_HOST=0.0.0.0
API_PORT=8000
ENVIRONMENT=production
LOG_LEVEL=INFO
EOF
        print_warning "Please edit the .env file with your actual API keys and configuration"
        print_warning "Then run this script again"
        exit 1
    fi
    
    # Source the .env file
    source .env
    
    # Check required variables
    local required_vars=("SUPABASE_URL" "SUPABASE_KEY" "GEMINI_API_KEY")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ] || [ "${!var}" = "your_${var,,}_here" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_error "Missing or placeholder values for: ${missing_vars[*]}"
        print_status "Please update your .env file with actual values"
        exit 1
    fi
    
    print_success "Environment variables are configured"
}

# Function to build and deploy
deploy_application() {
    print_status "Starting deployment..."
    
    # Stop existing containers
    print_status "Stopping existing containers..."
    docker-compose down --remove-orphans || true
    
    # Remove old images (optional)
    if [ "$1" = "--clean" ]; then
        print_status "Removing old images..."
        docker system prune -f
    fi
    
    # Build and start containers
    print_status "Building and starting containers..."
    docker-compose up --build -d
    
    # Wait for services to be ready
    print_status "Waiting for services to start..."
    sleep 30
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        print_success "Services are running"
    else
        print_error "Some services failed to start"
        docker-compose logs
        exit 1
    fi
}

# Function to run health checks
health_check() {
    print_status "Running health checks..."
    
    # Check backend health
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        print_success "Backend is healthy"
    else
        print_error "Backend health check failed"
        return 1
    fi
    
    # Check frontend
    if curl -f http://localhost/ >/dev/null 2>&1; then
        print_success "Frontend is accessible"
    else
        print_error "Frontend health check failed"
        return 1
    fi
    
    print_success "All health checks passed"
}

# Function to show deployment info
show_deployment_info() {
    print_success "Deployment completed successfully!"
    echo
    print_status "Application URLs:"
    echo "  Frontend: http://localhost"
    echo "  Backend API: http://localhost:8000"
    echo "  API Documentation: http://localhost:8000/docs"
    echo
    print_status "Useful commands:"
    echo "  View logs: docker-compose logs -f"
    echo "  Stop services: docker-compose down"
    echo "  Restart services: docker-compose restart"
    echo "  Update application: git pull && ./deploy.sh"
    echo
    print_status "For production deployment, make sure to:"
    echo "  1. Configure your domain name"
    echo "  2. Set up SSL certificates"
    echo "  3. Configure firewall rules"
    echo "  4. Set up monitoring and backups"
}

# Function to setup SSL (optional)
setup_ssl() {
    if [ "$1" = "--ssl" ] && [ -n "$2" ]; then
        print_status "Setting up SSL for domain: $2"
        
        # Create SSL directory
        mkdir -p ssl
        
        # Generate self-signed certificate for testing
        # In production, use Let's Encrypt or proper certificates
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout ssl/key.pem \
            -out ssl/cert.pem \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=$2"
        
        print_success "SSL certificates generated"
        print_warning "These are self-signed certificates for testing only"
        print_warning "For production, use Let's Encrypt or proper CA certificates"
    fi
}

# Main execution
main() {
    echo "========================================="
    echo "       RailGPT Deployment Script        "
    echo "========================================="
    echo
    
    # Change to script directory
    cd "$(dirname "$0")"
    
    # Parse command line arguments
    CLEAN_BUILD=false
    SETUP_SSL=false
    DOMAIN=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean)
                CLEAN_BUILD=true
                shift
                ;;
            --ssl)
                SETUP_SSL=true
                DOMAIN="$2"
                shift 2
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --clean     Remove old Docker images before building"
                echo "  --ssl DOMAIN Setup SSL certificates for domain"
                echo "  --help      Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run deployment steps
    check_prerequisites
    check_environment
    
    if [ "$SETUP_SSL" = true ]; then
        setup_ssl --ssl "$DOMAIN"
    fi
    
    if [ "$CLEAN_BUILD" = true ]; then
        deploy_application --clean
    else
        deploy_application
    fi
    
    # Wait a bit for services to fully start
    sleep 10
    
    if health_check; then
        show_deployment_info
    else
        print_error "Deployment completed but health checks failed"
        print_status "Check logs with: docker-compose logs"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
