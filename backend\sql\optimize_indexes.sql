-- Optimize indexes for large-scale performance

-- Drop existing vector indexes
DROP INDEX IF EXISTS idx_document_chunks_embedding;
DROP INDEX IF EXISTS idx_website_chunks_embedding;

-- Create optimized IVF indexes with more lists for larger datasets
CREATE INDEX idx_document_chunks_embedding ON document_chunks 
USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 500);  -- Increased from 100 to 500 for 10,000+ documents

CREATE INDEX idx_website_chunks_embedding ON website_chunks 
USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 200);  -- Adjusted for website scale

-- Add text search indexes for hybrid search performance
CREATE INDEX idx_document_chunks_text_search ON document_chunks 
USING gin (to_tsvector('english', text));

CREATE INDEX idx_website_chunks_text_search ON website_chunks 
USING gin (to_tsvector('english', text));

-- Add composite indexes for common query patterns
CREATE INDEX idx_document_chunks_document_id_chunk_index ON document_chunks(document_id, chunk_index);
CREATE INDEX idx_website_chunks_website_id_chunk_index ON website_chunks(website_id, chunk_index);

-- Analyze tables to update statistics for query planner
ANALYZE document_chunks;
ANALYZE website_chunks;
