import requests
import json
import time

# Backend API URL
API_URL = "http://localhost:8000/api/query"

# Test queries from the user
test_queries = [
    "What is the full form of ACP?",
    "What is the full form of FSDS?",
    "Tell me about the Rapid response app",
    "What has VASP developed?"
]

def test_query(query_text):
    """Test a single query against the backend API"""
    
    payload = {
        "query": query_text,
        "model": "gemini-2.0-flash",
        "fallback_enabled": True,
        "extract_format": "paragraph",
        "use_hybrid_search": True,
        "retry_on_timeout": True,
        "context_mode": "flexible"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"\n\n--- TESTING QUERY: '{query_text}' ---")
    
    try:
        response = requests.post(API_URL, headers=headers, json=payload)
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"Status: SUCCESS (200)")
            print(f"Model used: {result.get('llm_model', 'Unknown')}")
            print(f"LLM Fallback used: {result.get('llm_fallback', False)}")
            
            # Print sources information
            if result.get('document_sources') and len(result['document_sources']) > 0:
                print(f"Document sources: {len(result['document_sources'])}")
                for idx, source in enumerate(result['document_sources']):
                    print(f"  Source {idx+1}: {source.get('filename', 'Unknown')} (page {source.get('page', 'N/A')})")
            
            if result.get('website_sources') and len(result['website_sources']) > 0:
                print(f"Website sources: {len(result['website_sources'])}")
                for idx, source in enumerate(result['website_sources']):
                    print(f"  Source {idx+1}: {source.get('url', 'Unknown')}")
            
            # Print answer
            print("\nANSWER:")
            print(result.get('answer', 'No answer provided'))
            
            return True
        else:
            print(f"Status: FAILED ({response.status_code})")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"Status: ERROR")
        print(f"Exception: {str(e)}")
        return False

# Test all queries
def run_all_tests():
    successful_tests = 0
    
    for query in test_queries:
        if test_query(query):
            successful_tests += 1
        
        # Add a short delay between tests
        time.sleep(1)
    
    print(f"\n--- TEST SUMMARY ---")
    print(f"Successful tests: {successful_tests}/{len(test_queries)}")
    
    if successful_tests == len(test_queries):
        print("All tests passed successfully!")
    else:
        print(f"Some tests failed. Check the logs above for details.")

if __name__ == "__main__":
    run_all_tests()
