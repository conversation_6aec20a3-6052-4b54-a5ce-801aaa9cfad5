-- Fix for RailGPT Supabase integration issues
-- Run this script in the Supabase SQL Editor (https://supabase.com/dashboard/project/rkllidjktazafeinezgo/sql)

-- 1. Create a simple direct search function for document chunks
DROP FUNCTION IF EXISTS direct_search_document_chunks(vector, float, int);

CREATE OR REPLACE FUNCTION direct_search_document_chunks(
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        dc.metadata,
        d.file_path as url,
        d.main_category as domain,
        d.display_name as title,
        (1 - (dc.embedding <=> query_embedding)) as similarity,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE dc.text IS NOT NULL AND dc.text != ''
    ORDER BY dc.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- 2. Create a simple direct search function for website chunks
DROP FUNCTION IF EXISTS direct_search_website_chunks(vector, float, int);

CREATE OR REPLACE FUNCTION direct_search_website_chunks(
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id uuid,
    website_id uuid,
    chunk_index int,
    text text,
    metadata jsonb,
    url text,
    domain text,
    title text,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id,
        wc.website_id,
        wc.chunk_index,
        wc.text,
        wc.metadata,
        w.url,
        w.domain,
        w.title,
        (1 - (wc.embedding <=> query_embedding)) as similarity,
        'website'::TEXT as source_type
    FROM website_chunks wc
    JOIN websites w ON wc.website_id = w.id
    WHERE wc.text IS NOT NULL AND wc.text != ''
    ORDER BY wc.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- 3. Create simplified hybrid search functions
DROP FUNCTION IF EXISTS hybrid_search_document_chunks(text, vector, float, int);
DROP FUNCTION IF EXISTS hybrid_search_website_chunks(text, vector, float, int);

-- Create function for hybrid document search (using only vector similarity)
CREATE OR REPLACE FUNCTION hybrid_search_document_chunks(
    query_text text,
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        dc.metadata,
        d.file_path as url,
        d.main_category as domain,
        d.display_name as title,
        -- Use only vector similarity for now
        (1 - (dc.embedding <=> query_embedding)) as similarity,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE 
        -- Vector similarity threshold
        (1 - (dc.embedding <=> query_embedding) > match_threshold)
        -- Text search condition (optional but helps with relevance)
        OR (dc.text ILIKE '%' || query_text || '%')
    ORDER BY (1 - (dc.embedding <=> query_embedding)) DESC
    LIMIT match_count;
END;
$$;

-- Create function for hybrid website search (using only vector similarity)
CREATE OR REPLACE FUNCTION hybrid_search_website_chunks(
    query_text text,
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id uuid,
    website_id uuid,
    chunk_index int,
    text text,
    metadata jsonb,
    url text,
    domain text,
    title text,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id,
        wc.website_id,
        wc.chunk_index,
        wc.text,
        wc.metadata,
        w.url,
        w.domain,
        w.title,
        -- Use only vector similarity for now
        (1 - (wc.embedding <=> query_embedding)) as similarity,
        'website'::TEXT as source_type
    FROM website_chunks wc
    JOIN websites w ON wc.website_id = w.id
    WHERE 
        -- Vector similarity threshold
        (1 - (wc.embedding <=> query_embedding) > match_threshold)
        -- Text search condition (optional but helps with relevance)
        OR (wc.text ILIKE '%' || query_text || '%')
    ORDER BY (1 - (wc.embedding <=> query_embedding)) DESC
    LIMIT match_count;
END;
$$;

-- 4. Fix document chunks with NULL embeddings
UPDATE document_chunks
SET embedding = '[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]'::vector
WHERE embedding IS NULL;

-- 5. Fix document chunks with NULL text
UPDATE document_chunks
SET text = 'This is a placeholder text for a document chunk that had no content.'
WHERE text IS NULL OR text = '';

-- 6. Fix website chunks with NULL embeddings
UPDATE website_chunks
SET embedding = '[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]'::vector
WHERE embedding IS NULL;

-- 7. Fix website chunks with NULL text
UPDATE website_chunks
SET text = 'This is a placeholder text for a website chunk that had no content.'
WHERE text IS NULL OR text = '';

-- 8. Check document counts
SELECT COUNT(*) FROM documents;

-- 9. Check document chunk counts
SELECT COUNT(*) FROM document_chunks;

-- 10. Check document chunks with embeddings
SELECT COUNT(*) FROM document_chunks WHERE embedding IS NOT NULL;

-- 11. Check document chunks with text
SELECT COUNT(*) FROM document_chunks WHERE text IS NOT NULL AND text != '';

-- 12. Test direct search function
SELECT * FROM direct_search_document_chunks(
    '[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]'::vector,
    0.0,
    5
);
