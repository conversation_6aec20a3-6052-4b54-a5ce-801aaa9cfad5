@echo off
echo Starting the React app with OpenSSL legacy provider...
echo.

rem Set the OpenSSL legacy provider environment variable
set NODE_OPTIONS=--openssl-legacy-provider

echo Using Node.js: 
node -v
echo NODE_OPTIONS: %NODE_OPTIONS%
echo.

echo Starting the application...
npm start

rem If there was an error, pause so we can see the output
if %ERRORLEVEL% neq 0 (
  echo Application exited with error code: %ERRORLEVEL%
  pause
)
