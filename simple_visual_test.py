#!/usr/bin/env python3
"""
Simple test script for visual content extraction using existing railway documents
"""

import requests
import json
import time
from pathlib import Path

def test_railway_visual_content():
    """Test visual content extraction with existing railway documents"""
    print("🚂 Testing RailGPT Visual Content")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # Step 1: Check backend status
    print("\n1. 🔍 Checking backend...")
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            print("   ✅ Backend running")
        else:
            print(f"   ❌ Backend error: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Backend connection failed: {e}")
        return False
    
    # Step 2: Use existing railway document
    print("\n2. 📄 Testing with existing document...")
    
    # Use a specific railway document we know exists
    test_files = [
        Path("backend/data/SampleRailwayDoc.pdf"),
        Path("test_table.pdf"),
        Path("backend/data/uploads/SampleRailwayDoc.pdf")
    ]
    
    test_file = None
    for file_path in test_files:
        if file_path.exists():
            test_file = file_path
            print(f"   📄 Using: {test_file}")
            break
    
    if not test_file:
        print("   📝 Creating simple test document...")
        test_file = create_simple_test_pdf()
    
    # Upload the document
    print(f"   📤 Uploading...")
    try:
        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'application/pdf')}
            data = {
                'uploaded_by': 'test_user',
                'extract_tables': 'true',
                'extract_images': 'true', 
                'extract_charts': 'true'
            }
            
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/upload-document",
                files=files,
                data=data,
                timeout=60
            )
            upload_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Upload successful ({upload_time:.1f}s)")
                print(f"   📊 Chunks: {result.get('chunks_extracted', 0)}")
                
                # Analyze visual content
                chunks = result.get('chunks', [])
                visual_chunks = [c for c in chunks if c.get('chunk_type') != 'text']
                print(f"   🎨 Visual chunks: {len(visual_chunks)}")
                
                for chunk in visual_chunks:
                    chunk_type = chunk.get('chunk_type', 'unknown')
                    page = chunk.get('page_number', '?')
                    print(f"      - {chunk_type} on page {page}")
                    
            else:
                print(f"   ❌ Upload failed: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"   ❌ Upload error: {e}")
        return False
    
    # Step 3: Test visual queries
    print("\n3. 🔍 Testing visual queries...")
    
    queries = [
        "Show me any tables",
        "What specifications are in the document?",
        "Display any images or diagrams"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n   Query {i}: {query}")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/query",
                json={
                    'query': query,
                    'model': 'gemini-2.0-flash'
                },
                timeout=30
            )
            query_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                visual_found = result.get('visual_content_found', False)
                sources = result.get('sources', [])
                answer_length = len(result.get('answer', ''))
                
                print(f"   ⏱️  Time: {query_time:.1f}s")
                print(f"   📊 Visual found: {visual_found}")
                print(f"   📄 Sources: {len(sources)}")
                print(f"   📝 Answer: {answer_length} chars")
                
            else:
                print(f"   ❌ Query failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Query error: {e}")
    
    # Step 4: Frontend check
    print("\n4. 🌐 Frontend check...")
    
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("   ✅ Frontend accessible at http://localhost:3000")
        else:
            print(f"   ⚠️  Frontend status: {response.status_code}")
    except:
        print("   ⚠️  Frontend not accessible")
    
    print("\n" + "=" * 40)
    print("🎉 Test Complete!")
    print("\n🚀 Next Steps:")
    print("   • Open http://localhost:3000 in browser")
    print("   • Upload railway documents with visual extraction")
    print("   • Test queries like 'Show me the specifications table'")
    print("   • Check visual content displays properly")
    
    return True

def create_simple_test_pdf():
    """Create a simple test PDF if none exists"""
    import io
    
    # Create a simple text file as fallback
    content = """Railway Component Test Document

Component Specifications:
ID | Type | Material | Weight
RC-001 | Rail Clamp | Steel | 2.5kg
RC-002 | Bolt M16 | Stainless | 0.3kg
RC-003 | Washer | Zinc | 0.1kg

Maintenance Schedule:
Regular inspection required every 30 days.
"""
    
    filename = "simple_test.txt"
    with open(filename, "w") as f:
        f.write(content)
    
    print(f"   ✅ Created {filename}")
    return Path(filename)

if __name__ == "__main__":
    test_railway_visual_content() 