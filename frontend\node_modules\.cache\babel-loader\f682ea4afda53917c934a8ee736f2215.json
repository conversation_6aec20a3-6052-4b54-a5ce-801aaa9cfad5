{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\pages\\\\websites\\\\WebsitesPage.tsx\";\nimport React, { useState, useEffect } from 'react';\nimport WebsiteExtractForm from '../../components/websites/WebsiteExtractForm';\nimport WebsitesTable from '../../components/websites/WebsitesTable';\nimport WebsiteViewModal from '../../components/websites/WebsiteViewModal';\nimport { getWebsites } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Sample data for demonstration\nconst SAMPLE_WEBSITES = [{\n  id: '1',\n  url: 'https://indianrailways.gov.in/railwayboard/',\n  domain: 'indianrailways.gov.in',\n  extractedAt: '2025-05-01T14:30:00Z',\n  status: 'Success',\n  submittedBy: '<EMAIL>',\n  domainCategory: 'Official Railways'\n}, {\n  id: '2',\n  url: 'https://irctc.co.in/nget/train-search',\n  domain: 'irctc.co.in',\n  extractedAt: '2025-04-29T11:45:00Z',\n  status: 'Success',\n  submittedBy: '<EMAIL>',\n  domainCategory: 'Official Railways'\n}, {\n  id: '3',\n  url: 'https://www.trainman.in/railway-stations',\n  domain: 'trainman.in',\n  extractedAt: '2025-04-25T09:15:00Z',\n  status: 'Success',\n  submittedBy: '<EMAIL>',\n  domainCategory: 'Travel Guides'\n}, {\n  id: '4',\n  url: 'https://rail-info.indianrailways.gov.in/mntes/',\n  domain: 'rail-info.indianrailways.gov.in',\n  extractedAt: '2025-04-20T16:30:00Z',\n  status: 'Failed',\n  submittedBy: '<EMAIL>',\n  domainCategory: 'Official Railways'\n}, {\n  id: '5',\n  url: 'https://www.ndtv.com/topic/indian-railways',\n  domain: 'ndtv.com',\n  extractedAt: '2025-05-02T10:20:00Z',\n  status: 'Manual Required',\n  submittedBy: '<EMAIL>',\n  domainCategory: 'News Portals'\n}];\n\n// Sample extraction details for demonstration\nconst SAMPLE_EXTRACTION_DETAILS = {\n  extractionMethod: 'Trafilatura',\n  fallbackHistory: ['Trafilatura', 'BeautifulSoup'],\n  contentQuality: 85,\n  warnings: ['Dynamic content may be missing', 'Some tables could not be extracted completely'],\n  extractedContent: `\n# Indian Railways\n\nThe Indian Railways is a statutory body under the jurisdiction of Ministry of Railways, Government of India that operates India's national railway system. It manages the fourth-largest railway network in the world by size, with a route length of 67,956 km (42,226 mi) and total track length of 99,235 km (61,662 mi) as of March 2020. As of the end of 2019, 71.23% of its 121,407 kilometres of track is electrified.\n\n## Passenger Services\n\nIndian Railways operates more than 20,000 passenger trains daily, on both long-distance and suburban routes, from approximately 7,349 stations across India.\n\n### Train Categories\n\nIndian Railways operates different categories of trains:\n\n1. **Shatabdi Express**: 160 km/h (99 mph) trains that connect major cities and state capitals over short distances, typically within a day's journey.\n2. **Rajdhani Express**: 130 to 140 km/h (81 to 87 mph) trains that link major state capitals to New Delhi.\n3. **Duronto Express**: 130 km/h (81 mph) point-to-point non-stop trains designed to connect major cities.\n4. **Vande Bharat Express**: 180 km/h (110 mph) semi-high-speed train, currently running on select routes.\n5. **Jan Shatabdi Express**: 110 to 120 km/h (68 to 75 mph) intercity services serving middle-distance connectivity.\n6. **Garib Rath Express**: 130 km/h (81 mph) trains that aim to provide affordable air-conditioned travel.\n7. **Mail/Express Trains**: Regular long-distance train services operating throughout the country.\n8. **Passenger & Fast Passenger Trains**: Short-distance services connecting smaller towns and villages.\n9. **Suburban Trains**: High-frequency train services in major metropolitan areas.\n\n### Ticket Booking\n\nTicket booking is primarily done through the Indian Railway Catering and Tourism Corporation (IRCTC) portal. Tickets can be booked up to 120 days in advance.\n\n## Freight Services\n\nIndian Railways is one of the world's largest freight carriers, moving over 1.23 billion tonnes of freight annually in FY 2019-20.\n\nMajor categories of freight traffic:\n- Coal (and petroleum products): 49%\n- Raw materials: 17%\n- Iron and steel: 12%\n- Cement: the 10%\n- Food grains: 7%\n- Fertilizers: 5%\n\n## Current Projects\n\n### High-Speed Rail Projects\n\n1. **Mumbai–Ahmedabad Corridor**: India's first high-speed rail line (bullet train) with operational speeds of 320 km/h. Currently under construction.\n2. **National High Speed Rail Corporation Limited (NHSRCL)**: Responsible for implementing high-speed rail projects in the country.\n\n### Infrastructure Development\n\n1. **Dedicated Freight Corridors (DFCs)**\n   - Eastern DFC: 1,856 km from Ludhiana to Dankuni\n   - Western DFC: 1,504 km from Dadri to Jawaharlal Nehru Port Trust\n\n2. **Station Redevelopment Program**\n   - Modernizing and upgrading major railway stations across the country\n   - Public-private partnership model for development\n  `,\n  processingTime: 2850,\n  chunks: 8\n};\nconst WebsitesPage = () => {\n  const [websites, setWebsites] = useState(SAMPLE_WEBSITES);\n  const [selectedWebsite, setSelectedWebsite] = useState(null);\n  const [isViewModalOpen, setIsViewModalOpen] = useState(false);\n  const [extractionDetails, setExtractionDetails] = useState(SAMPLE_EXTRACTION_DETAILS);\n  const [isCategoryManagementOpen, setIsCategoryManagementOpen] = useState(false);\n\n  // Fetch websites from the backend\n  useEffect(() => {\n    fetchWebsites();\n\n    // Set up website extraction event listener\n    const handleWebsiteExtracted = event => {\n      const newWebsite = event.detail;\n      setWebsites(prev => [newWebsite, ...prev]);\n      // Refresh the websites list to get the latest from Supabase\n      setTimeout(() => fetchWebsites(), 2000); // Small delay to allow backend processing\n    };\n\n    // Add event listener\n    window.addEventListener('websiteExtracted', handleWebsiteExtracted);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('websiteExtracted', handleWebsiteExtracted);\n    };\n  }, []);\n  const fetchWebsites = async () => {\n    try {\n      const data = await getWebsites();\n      if (data && data.length > 0) {\n        // Use real data from Supabase, don't mix with sample data\n        setWebsites(data);\n        console.log(`Loaded ${data.length} websites from Supabase`);\n      } else {\n        console.log('No websites found from API, using sample data');\n        // Only use sample data if no real data is available\n        setWebsites(SAMPLE_WEBSITES);\n      }\n    } catch (error) {\n      console.error('Error fetching websites:', error);\n      // Keep using sample data if API call fails\n      setWebsites(SAMPLE_WEBSITES);\n    }\n  };\n  const handleViewWebsite = website => {\n    setSelectedWebsite(website);\n\n    // Fetch actual extraction details from the backend\n    async function fetchExtractionDetails() {\n      try {\n        // Import our API functions\n        const {\n          getWebsiteExtractionDetails,\n          getWebsiteContent\n        } = await import('../../services/api');\n\n        // First try the specific extraction details endpoint\n        try {\n          const data = await getWebsiteExtractionDetails(website.id);\n          console.log('Website extraction details response:', data);\n          setExtractionDetails(data);\n          return;\n        } catch (extractionError) {\n          console.warn('Failed to get website extraction details, trying content endpoint:', extractionError);\n        }\n\n        // If that fails, try the general website content endpoint\n        try {\n          const contentData = await getWebsiteContent(website.id);\n          console.log('Website content response:', contentData);\n\n          // Create extraction details from content data\n          const details = {\n            extractedContent: contentData.content || contentData.text || 'No content available',\n            extractionMethod: contentData.extraction_method || 'Direct Scraping',\n            processingTime: contentData.processing_time || 850,\n            warnings: contentData.warnings || [],\n            // Required fields from WebsiteExtractionDetails interface\n            fallbackHistory: contentData.fallback_history || ['API Response'],\n            contentQuality: contentData.quality_score || 80,\n            chunks: contentData.chunks || 5\n          };\n          setExtractionDetails(details);\n          return;\n        } catch (contentError) {\n          console.error('Failed to get website content:', contentError);\n          throw contentError; // Re-throw to be caught by the outer try-catch\n        }\n      } catch (error) {\n        console.error('All API attempts failed, using fallback data:', error);\n\n        // Fallback data in case of error\n        const errorFallbackDetails = {\n          extractedContent: `Unable to retrieve content for ${website.url} due to an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again later.`,\n          extractionMethod: 'Error Fallback',\n          processingTime: 0,\n          warnings: ['Error retrieving content', error instanceof Error ? error.message : 'Unknown error'],\n          fallbackHistory: ['API Error', 'Error Handler'],\n          contentQuality: 30,\n          chunks: 0\n        };\n        setExtractionDetails(errorFallbackDetails);\n      }\n    }\n    fetchExtractionDetails();\n    setIsViewModalOpen(true);\n  };\n  const handleRetryWebsite = website => {\n    // In a real app, you would implement retry logic\n    alert(`Retry extraction for: ${website.url}`);\n  };\n  const handleDeleteWebsite = website => {\n    if (window.confirm(`Are you sure you want to delete \"${website.url}\"?`)) {\n      // In a real app, you would make an API call to delete the website\n      // async function deleteWebsite() {\n      //   try {\n      //     await fetch(`/api/websites/${website.id}`, { method: 'DELETE' });\n      //     setWebsites(websites.filter(site => site.id !== website.id));\n      //   } catch (error) {\n      //     console.error('Error deleting website:', error);\n      //   }\n      // }\n      // deleteWebsite();\n\n      // For the demo, just filter it out\n      setWebsites(websites.filter(site => site.id !== website.id));\n    }\n  };\n  const handleRetryWithParser = async (website, parser) => {\n    // In a real app, you would make an API call to retry with the selected parser\n    alert(`Retrying extraction of ${website.url} with ${parser}`);\n\n    // Simulate processing time\n    await new Promise(resolve => setTimeout(resolve, 1500));\n    return true;\n  };\n  const handleCategoryUpdate = updatedWebsite => {\n    // Update the website in the local state\n    setWebsites(prevWebsites => prevWebsites.map(site => site.id === updatedWebsite.id ? updatedWebsite : site));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full flex flex-col bg-gray-50 transition-colors duration-300\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 shadow-sm z-10 transition-colors duration-300\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Website Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:col-span-1\",\n            children: /*#__PURE__*/_jsxDEV(WebsiteExtractForm, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:col-span-2\",\n            children: /*#__PURE__*/_jsxDEV(WebsitesTable, {\n              websites: websites,\n              onView: handleViewWebsite,\n              onRetry: handleRetryWebsite,\n              onDelete: handleDeleteWebsite\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), selectedWebsite && /*#__PURE__*/_jsxDEV(WebsiteViewModal, {\n      website: selectedWebsite,\n      extractionDetails: extractionDetails,\n      isOpen: isViewModalOpen,\n      onClose: () => setIsViewModalOpen(false),\n      onRetry: handleRetryWithParser\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 279,\n    columnNumber: 5\n  }, this);\n};\nexport default WebsitesPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "WebsiteExtractForm", "WebsitesTable", "WebsiteViewModal", "getWebsites", "jsxDEV", "_jsxDEV", "SAMPLE_WEBSITES", "id", "url", "domain", "extractedAt", "status", "submittedBy", "domainCategory", "SAMPLE_EXTRACTION_DETAILS", "extractionMethod", "fallbackHistory", "contentQuality", "warnings", "extractedContent", "processingTime", "chunks", "WebsitesPage", "websites", "setWebsites", "selectedWebsite", "setSelectedWebsite", "isViewModalOpen", "setIsViewModalOpen", "extractionDetails", "setExtractionDetails", "isCategoryManagementOpen", "setIsCategoryManagementOpen", "fetchWebsites", "handleWebsiteExtracted", "event", "newWebsite", "detail", "prev", "setTimeout", "window", "addEventListener", "removeEventListener", "data", "length", "console", "log", "error", "handleViewWebsite", "website", "fetchExtractionDetails", "getWebsiteExtractionDetails", "getWebsiteContent", "extractionError", "warn", "contentData", "details", "content", "text", "extraction_method", "processing_time", "fallback_history", "quality_score", "contentError", "errorFallbackDetails", "Error", "message", "handleRetryWebsite", "alert", "handleDeleteWebsite", "confirm", "filter", "site", "handleRetryWithParser", "parser", "Promise", "resolve", "handleCategoryUpdate", "updatedWebsite", "prevWebsites", "map", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onView", "onRetry", "onDelete", "isOpen", "onClose"], "sources": ["C:/IR App/frontend/src/pages/websites/WebsitesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Website, WebsiteExtractionDetails, ExtractionParser } from '../../types/websites';\nimport WebsiteExtractForm from '../../components/websites/WebsiteExtractForm';\nimport WebsitesTable from '../../components/websites/WebsitesTable';\nimport WebsiteViewModal from '../../components/websites/WebsiteViewModal';\nimport WebsiteCategoryManagement from '../../components/websites/WebsiteCategoryManagement';\nimport { getWebsites } from '../../services/api';\nimport { Settings } from 'lucide-react';\n\n// Sample data for demonstration\nconst SAMPLE_WEBSITES: Website[] = [\n  {\n    id: '1',\n    url: 'https://indianrailways.gov.in/railwayboard/',\n    domain: 'indianrailways.gov.in',\n    extractedAt: '2025-05-01T14:30:00Z',\n    status: 'Success',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'Official Railways',\n  },\n  {\n    id: '2',\n    url: 'https://irctc.co.in/nget/train-search',\n    domain: 'irctc.co.in',\n    extractedAt: '2025-04-29T11:45:00Z',\n    status: 'Success',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'Official Railways',\n  },\n  {\n    id: '3',\n    url: 'https://www.trainman.in/railway-stations',\n    domain: 'trainman.in',\n    extractedAt: '2025-04-25T09:15:00Z',\n    status: 'Success',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'Travel Guides',\n  },\n  {\n    id: '4',\n    url: 'https://rail-info.indianrailways.gov.in/mntes/',\n    domain: 'rail-info.indianrailways.gov.in',\n    extractedAt: '2025-04-20T16:30:00Z',\n    status: 'Failed',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'Official Railways',\n  },\n  {\n    id: '5',\n    url: 'https://www.ndtv.com/topic/indian-railways',\n    domain: 'ndtv.com',\n    extractedAt: '2025-05-02T10:20:00Z',\n    status: 'Manual Required',\n    submittedBy: '<EMAIL>',\n    domainCategory: 'News Portals',\n  },\n];\n\n// Sample extraction details for demonstration\nconst SAMPLE_EXTRACTION_DETAILS: WebsiteExtractionDetails = {\n  extractionMethod: 'Trafilatura',\n  fallbackHistory: ['Trafilatura', 'BeautifulSoup'],\n  contentQuality: 85,\n  warnings: ['Dynamic content may be missing', 'Some tables could not be extracted completely'],\n  extractedContent: `\n# Indian Railways\n\nThe Indian Railways is a statutory body under the jurisdiction of Ministry of Railways, Government of India that operates India's national railway system. It manages the fourth-largest railway network in the world by size, with a route length of 67,956 km (42,226 mi) and total track length of 99,235 km (61,662 mi) as of March 2020. As of the end of 2019, 71.23% of its 121,407 kilometres of track is electrified.\n\n## Passenger Services\n\nIndian Railways operates more than 20,000 passenger trains daily, on both long-distance and suburban routes, from approximately 7,349 stations across India.\n\n### Train Categories\n\nIndian Railways operates different categories of trains:\n\n1. **Shatabdi Express**: 160 km/h (99 mph) trains that connect major cities and state capitals over short distances, typically within a day's journey.\n2. **Rajdhani Express**: 130 to 140 km/h (81 to 87 mph) trains that link major state capitals to New Delhi.\n3. **Duronto Express**: 130 km/h (81 mph) point-to-point non-stop trains designed to connect major cities.\n4. **Vande Bharat Express**: 180 km/h (110 mph) semi-high-speed train, currently running on select routes.\n5. **Jan Shatabdi Express**: 110 to 120 km/h (68 to 75 mph) intercity services serving middle-distance connectivity.\n6. **Garib Rath Express**: 130 km/h (81 mph) trains that aim to provide affordable air-conditioned travel.\n7. **Mail/Express Trains**: Regular long-distance train services operating throughout the country.\n8. **Passenger & Fast Passenger Trains**: Short-distance services connecting smaller towns and villages.\n9. **Suburban Trains**: High-frequency train services in major metropolitan areas.\n\n### Ticket Booking\n\nTicket booking is primarily done through the Indian Railway Catering and Tourism Corporation (IRCTC) portal. Tickets can be booked up to 120 days in advance.\n\n## Freight Services\n\nIndian Railways is one of the world's largest freight carriers, moving over 1.23 billion tonnes of freight annually in FY 2019-20.\n\nMajor categories of freight traffic:\n- Coal (and petroleum products): 49%\n- Raw materials: 17%\n- Iron and steel: 12%\n- Cement: the 10%\n- Food grains: 7%\n- Fertilizers: 5%\n\n## Current Projects\n\n### High-Speed Rail Projects\n\n1. **Mumbai–Ahmedabad Corridor**: India's first high-speed rail line (bullet train) with operational speeds of 320 km/h. Currently under construction.\n2. **National High Speed Rail Corporation Limited (NHSRCL)**: Responsible for implementing high-speed rail projects in the country.\n\n### Infrastructure Development\n\n1. **Dedicated Freight Corridors (DFCs)**\n   - Eastern DFC: 1,856 km from Ludhiana to Dankuni\n   - Western DFC: 1,504 km from Dadri to Jawaharlal Nehru Port Trust\n\n2. **Station Redevelopment Program**\n   - Modernizing and upgrading major railway stations across the country\n   - Public-private partnership model for development\n  `,\n  processingTime: 2850,\n  chunks: 8,\n};\n\nconst WebsitesPage: React.FC = () => {\n  const [websites, setWebsites] = useState<Website[]>(SAMPLE_WEBSITES);\n  const [selectedWebsite, setSelectedWebsite] = useState<Website | null>(null);\n  const [isViewModalOpen, setIsViewModalOpen] = useState(false);\n  const [extractionDetails, setExtractionDetails] = useState<WebsiteExtractionDetails>(SAMPLE_EXTRACTION_DETAILS);\n  const [isCategoryManagementOpen, setIsCategoryManagementOpen] = useState(false);\n\n  // Fetch websites from the backend\n  useEffect(() => {\n    fetchWebsites();\n\n    // Set up website extraction event listener\n    const handleWebsiteExtracted = (event: CustomEvent) => {\n      const newWebsite = event.detail;\n      setWebsites(prev => [newWebsite, ...prev]);\n      // Refresh the websites list to get the latest from Supabase\n      setTimeout(() => fetchWebsites(), 2000); // Small delay to allow backend processing\n    };\n\n    // Add event listener\n    window.addEventListener('websiteExtracted', handleWebsiteExtracted as EventListener);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('websiteExtracted', handleWebsiteExtracted as EventListener);\n    };\n  }, []);\n\n  const fetchWebsites = async () => {\n    try {\n      const data = await getWebsites();\n      if (data && data.length > 0) {\n        // Use real data from Supabase, don't mix with sample data\n        setWebsites(data);\n        console.log(`Loaded ${data.length} websites from Supabase`);\n      } else {\n        console.log('No websites found from API, using sample data');\n        // Only use sample data if no real data is available\n        setWebsites(SAMPLE_WEBSITES);\n      }\n    } catch (error) {\n      console.error('Error fetching websites:', error);\n      // Keep using sample data if API call fails\n      setWebsites(SAMPLE_WEBSITES);\n    }\n  };\n\n  const handleViewWebsite = (website: Website) => {\n    setSelectedWebsite(website);\n\n    // Fetch actual extraction details from the backend\n    async function fetchExtractionDetails() {\n      try {\n        // Import our API functions\n        const { getWebsiteExtractionDetails, getWebsiteContent } = await import('../../services/api');\n\n        // First try the specific extraction details endpoint\n        try {\n          const data = await getWebsiteExtractionDetails(website.id);\n          console.log('Website extraction details response:', data);\n          setExtractionDetails(data);\n          return;\n        } catch (extractionError) {\n          console.warn('Failed to get website extraction details, trying content endpoint:', extractionError);\n        }\n\n        // If that fails, try the general website content endpoint\n        try {\n          const contentData = await getWebsiteContent(website.id);\n          console.log('Website content response:', contentData);\n\n          // Create extraction details from content data\n          const details = {\n            extractedContent: contentData.content || contentData.text || 'No content available',\n            extractionMethod: contentData.extraction_method || 'Direct Scraping',\n            processingTime: contentData.processing_time || 850,\n            warnings: contentData.warnings || [],\n            // Required fields from WebsiteExtractionDetails interface\n            fallbackHistory: contentData.fallback_history || ['API Response'],\n            contentQuality: contentData.quality_score || 80,\n            chunks: contentData.chunks || 5\n          };\n\n          setExtractionDetails(details);\n          return;\n        } catch (contentError) {\n          console.error('Failed to get website content:', contentError);\n          throw contentError; // Re-throw to be caught by the outer try-catch\n        }\n      } catch (error) {\n        console.error('All API attempts failed, using fallback data:', error);\n\n        // Fallback data in case of error\n        const errorFallbackDetails: WebsiteExtractionDetails = {\n          extractedContent: `Unable to retrieve content for ${website.url} due to an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again later.`,\n          extractionMethod: 'Error Fallback',\n          processingTime: 0,\n          warnings: ['Error retrieving content', error instanceof Error ? error.message : 'Unknown error'],\n          fallbackHistory: ['API Error', 'Error Handler'],\n          contentQuality: 30,\n          chunks: 0\n        };\n\n        setExtractionDetails(errorFallbackDetails);\n      }\n    }\n\n    fetchExtractionDetails();\n    setIsViewModalOpen(true);\n  };\n\n  const handleRetryWebsite = (website: Website) => {\n    // In a real app, you would implement retry logic\n    alert(`Retry extraction for: ${website.url}`);\n  };\n\n  const handleDeleteWebsite = (website: Website) => {\n    if (window.confirm(`Are you sure you want to delete \"${website.url}\"?`)) {\n      // In a real app, you would make an API call to delete the website\n      // async function deleteWebsite() {\n      //   try {\n      //     await fetch(`/api/websites/${website.id}`, { method: 'DELETE' });\n      //     setWebsites(websites.filter(site => site.id !== website.id));\n      //   } catch (error) {\n      //     console.error('Error deleting website:', error);\n      //   }\n      // }\n      // deleteWebsite();\n\n      // For the demo, just filter it out\n      setWebsites(websites.filter(site => site.id !== website.id));\n    }\n  };\n\n  const handleRetryWithParser = async (website: Website, parser: ExtractionParser) => {\n    // In a real app, you would make an API call to retry with the selected parser\n    alert(`Retrying extraction of ${website.url} with ${parser}`);\n\n    // Simulate processing time\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    return true;\n  };\n\n  const handleCategoryUpdate = (updatedWebsite: Website) => {\n    // Update the website in the local state\n    setWebsites(prevWebsites =>\n      prevWebsites.map(site =>\n        site.id === updatedWebsite.id ? updatedWebsite : site\n      )\n    );\n  };\n\n  return (\n    <div className=\"h-full flex flex-col bg-gray-50 transition-colors duration-300\">\n      {/* Fixed header section */}\n      <div className=\"bg-white p-4 shadow-sm z-10 transition-colors duration-300\">\n        <div className=\"container mx-auto\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Website Management</h1>\n        </div>\n      </div>\n\n      {/* Scrollable content section */}\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <div className=\"container mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {/* Left side: Extract form */}\n            <div className=\"lg:col-span-1\">\n              <WebsiteExtractForm />\n            </div>\n\n            {/* Right side: Websites table */}\n            <div className=\"lg:col-span-2\">\n              <WebsitesTable\n                websites={websites}\n                onView={handleViewWebsite}\n                onRetry={handleRetryWebsite}\n                onDelete={handleDeleteWebsite}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Website View Modal */}\n      {selectedWebsite && (\n        <WebsiteViewModal\n          website={selectedWebsite}\n          extractionDetails={extractionDetails}\n          isOpen={isViewModalOpen}\n          onClose={() => setIsViewModalOpen(false)}\n          onRetry={handleRetryWithParser}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default WebsitesPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,gBAAgB,MAAM,4CAA4C;AAEzE,SAASC,WAAW,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGjD;AACA,MAAMC,eAA0B,GAAG,CACjC;EACEC,EAAE,EAAE,GAAG;EACPC,GAAG,EAAE,6CAA6C;EAClDC,MAAM,EAAE,uBAAuB;EAC/BC,WAAW,EAAE,sBAAsB;EACnCC,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE,mBAAmB;EAChCC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,EAAE,EAAE,GAAG;EACPC,GAAG,EAAE,uCAAuC;EAC5CC,MAAM,EAAE,aAAa;EACrBC,WAAW,EAAE,sBAAsB;EACnCC,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE,mBAAmB;EAChCC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,EAAE,EAAE,GAAG;EACPC,GAAG,EAAE,0CAA0C;EAC/CC,MAAM,EAAE,aAAa;EACrBC,WAAW,EAAE,sBAAsB;EACnCC,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE,wBAAwB;EACrCC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,EAAE,EAAE,GAAG;EACPC,GAAG,EAAE,gDAAgD;EACrDC,MAAM,EAAE,iCAAiC;EACzCC,WAAW,EAAE,sBAAsB;EACnCC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,wBAAwB;EACrCC,cAAc,EAAE;AAClB,CAAC,EACD;EACEN,EAAE,EAAE,GAAG;EACPC,GAAG,EAAE,4CAA4C;EACjDC,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,sBAAsB;EACnCC,MAAM,EAAE,iBAAiB;EACzBC,WAAW,EAAE,qBAAqB;EAClCC,cAAc,EAAE;AAClB,CAAC,CACF;;AAED;AACA,MAAMC,yBAAmD,GAAG;EAC1DC,gBAAgB,EAAE,aAAa;EAC/BC,eAAe,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;EACjDC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,CAAC,gCAAgC,EAAE,+CAA+C,CAAC;EAC7FC,gBAAgB,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EACDC,cAAc,EAAE,IAAI;EACpBC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EACnC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAYQ,eAAe,CAAC;EACpE,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAiB,IAAI,CAAC;EAC5E,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAA2BgB,yBAAyB,CAAC;EAC/G,MAAM,CAACiB,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;;EAE/E;EACAC,SAAS,CAAC,MAAM;IACdkC,aAAa,CAAC,CAAC;;IAEf;IACA,MAAMC,sBAAsB,GAAIC,KAAkB,IAAK;MACrD,MAAMC,UAAU,GAAGD,KAAK,CAACE,MAAM;MAC/Bb,WAAW,CAACc,IAAI,IAAI,CAACF,UAAU,EAAE,GAAGE,IAAI,CAAC,CAAC;MAC1C;MACAC,UAAU,CAAC,MAAMN,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3C,CAAC;;IAED;IACAO,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEP,sBAAuC,CAAC;;IAEpF;IACA,OAAO,MAAM;MACXM,MAAM,CAACE,mBAAmB,CAAC,kBAAkB,EAAER,sBAAuC,CAAC;IACzF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMU,IAAI,GAAG,MAAMxC,WAAW,CAAC,CAAC;MAChC,IAAIwC,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3B;QACApB,WAAW,CAACmB,IAAI,CAAC;QACjBE,OAAO,CAACC,GAAG,CAAC,UAAUH,IAAI,CAACC,MAAM,yBAAyB,CAAC;MAC7D,CAAC,MAAM;QACLC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D;QACAtB,WAAW,CAAClB,eAAe,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACAvB,WAAW,CAAClB,eAAe,CAAC;IAC9B;EACF,CAAC;EAED,MAAM0C,iBAAiB,GAAIC,OAAgB,IAAK;IAC9CvB,kBAAkB,CAACuB,OAAO,CAAC;;IAE3B;IACA,eAAeC,sBAAsBA,CAAA,EAAG;MACtC,IAAI;QACF;QACA,MAAM;UAAEC,2BAA2B;UAAEC;QAAkB,CAAC,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC;;QAE7F;QACA,IAAI;UACF,MAAMT,IAAI,GAAG,MAAMQ,2BAA2B,CAACF,OAAO,CAAC1C,EAAE,CAAC;UAC1DsC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEH,IAAI,CAAC;UACzDb,oBAAoB,CAACa,IAAI,CAAC;UAC1B;QACF,CAAC,CAAC,OAAOU,eAAe,EAAE;UACxBR,OAAO,CAACS,IAAI,CAAC,oEAAoE,EAAED,eAAe,CAAC;QACrG;;QAEA;QACA,IAAI;UACF,MAAME,WAAW,GAAG,MAAMH,iBAAiB,CAACH,OAAO,CAAC1C,EAAE,CAAC;UACvDsC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAES,WAAW,CAAC;;UAErD;UACA,MAAMC,OAAO,GAAG;YACdrC,gBAAgB,EAAEoC,WAAW,CAACE,OAAO,IAAIF,WAAW,CAACG,IAAI,IAAI,sBAAsB;YACnF3C,gBAAgB,EAAEwC,WAAW,CAACI,iBAAiB,IAAI,iBAAiB;YACpEvC,cAAc,EAAEmC,WAAW,CAACK,eAAe,IAAI,GAAG;YAClD1C,QAAQ,EAAEqC,WAAW,CAACrC,QAAQ,IAAI,EAAE;YACpC;YACAF,eAAe,EAAEuC,WAAW,CAACM,gBAAgB,IAAI,CAAC,cAAc,CAAC;YACjE5C,cAAc,EAAEsC,WAAW,CAACO,aAAa,IAAI,EAAE;YAC/CzC,MAAM,EAAEkC,WAAW,CAAClC,MAAM,IAAI;UAChC,CAAC;UAEDS,oBAAoB,CAAC0B,OAAO,CAAC;UAC7B;QACF,CAAC,CAAC,OAAOO,YAAY,EAAE;UACrBlB,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEgB,YAAY,CAAC;UAC7D,MAAMA,YAAY,CAAC,CAAC;QACtB;MACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;;QAErE;QACA,MAAMiB,oBAA8C,GAAG;UACrD7C,gBAAgB,EAAE,kCAAkC8B,OAAO,CAACzC,GAAG,qBAAqBuC,KAAK,YAAYkB,KAAK,GAAGlB,KAAK,CAACmB,OAAO,GAAG,eAAe,2BAA2B;UACvKnD,gBAAgB,EAAE,gBAAgB;UAClCK,cAAc,EAAE,CAAC;UACjBF,QAAQ,EAAE,CAAC,0BAA0B,EAAE6B,KAAK,YAAYkB,KAAK,GAAGlB,KAAK,CAACmB,OAAO,GAAG,eAAe,CAAC;UAChGlD,eAAe,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC;UAC/CC,cAAc,EAAE,EAAE;UAClBI,MAAM,EAAE;QACV,CAAC;QAEDS,oBAAoB,CAACkC,oBAAoB,CAAC;MAC5C;IACF;IAEAd,sBAAsB,CAAC,CAAC;IACxBtB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMuC,kBAAkB,GAAIlB,OAAgB,IAAK;IAC/C;IACAmB,KAAK,CAAC,yBAAyBnB,OAAO,CAACzC,GAAG,EAAE,CAAC;EAC/C,CAAC;EAED,MAAM6D,mBAAmB,GAAIpB,OAAgB,IAAK;IAChD,IAAIT,MAAM,CAAC8B,OAAO,CAAC,oCAAoCrB,OAAO,CAACzC,GAAG,IAAI,CAAC,EAAE;MACvE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACAgB,WAAW,CAACD,QAAQ,CAACgD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjE,EAAE,KAAK0C,OAAO,CAAC1C,EAAE,CAAC,CAAC;IAC9D;EACF,CAAC;EAED,MAAMkE,qBAAqB,GAAG,MAAAA,CAAOxB,OAAgB,EAAEyB,MAAwB,KAAK;IAClF;IACAN,KAAK,CAAC,0BAA0BnB,OAAO,CAACzC,GAAG,SAASkE,MAAM,EAAE,CAAC;;IAE7D;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIrC,UAAU,CAACqC,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,oBAAoB,GAAIC,cAAuB,IAAK;IACxD;IACAtD,WAAW,CAACuD,YAAY,IACtBA,YAAY,CAACC,GAAG,CAACR,IAAI,IACnBA,IAAI,CAACjE,EAAE,KAAKuE,cAAc,CAACvE,EAAE,GAAGuE,cAAc,GAAGN,IACnD,CACF,CAAC;EACH,CAAC;EAED,oBACEnE,OAAA;IAAK4E,SAAS,EAAC,gEAAgE;IAAAC,QAAA,gBAE7E7E,OAAA;MAAK4E,SAAS,EAAC,4DAA4D;MAAAC,QAAA,eACzE7E,OAAA;QAAK4E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChC7E,OAAA;UAAI4E,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA;MAAK4E,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzC7E,OAAA;QAAK4E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChC7E,OAAA;UAAK4E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpD7E,OAAA;YAAK4E,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B7E,OAAA,CAACL,kBAAkB;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAGNjF,OAAA;YAAK4E,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B7E,OAAA,CAACJ,aAAa;cACZsB,QAAQ,EAAEA,QAAS;cACnBgE,MAAM,EAAEvC,iBAAkB;cAC1BwC,OAAO,EAAErB,kBAAmB;cAC5BsB,QAAQ,EAAEpB;YAAoB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7D,eAAe,iBACdpB,OAAA,CAACH,gBAAgB;MACf+C,OAAO,EAAExB,eAAgB;MACzBI,iBAAiB,EAAEA,iBAAkB;MACrC6D,MAAM,EAAE/D,eAAgB;MACxBgE,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,KAAK,CAAE;MACzC4D,OAAO,EAAEf;IAAsB;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAED,eAAehE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}