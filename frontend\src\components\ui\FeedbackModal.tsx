import React, { useState } from 'react';
import { submitFeedback, FeedbackData } from '../../services/api';

interface FeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  queryText: string;
  answerText: string;
  model?: string;
  chatId?: string;
}

const ISSUE_TYPES = [
  { id: 'inaccurate', label: 'Inaccurate' },
  { id: 'incomplete', label: 'Incomplete' },
  { id: 'offensive', label: 'Offensive or Inappropriate' },
  { id: 'too_slow', label: 'Too Slow' },
  { id: 'other', label: 'Other' }
];

const FeedbackModal: React.FC<FeedbackModalProps> = ({ 
  isOpen, 
  onClose, 
  queryText, 
  answerText,
  model,
  chatId 
}) => {
  const [issueType, setIssueType] = useState<string>('');
  const [comment, setComment] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitSuccess, setSubmitSuccess] = useState<boolean | null>(null);
  const [submitMessage, setSubmitMessage] = useState<string>('');

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const feedbackData: FeedbackData = {
        query: queryText,
        answer: answerText,
        issue_type: issueType,
        comment: comment.trim() ? comment : undefined,
        model: model,
        chat_id: chatId,
        timestamp: new Date().toISOString()
      };
      
      const response = await submitFeedback(feedbackData);
      
      setSubmitSuccess(response.success);
      setSubmitMessage(response.message || (response.success ? 'Thank you for your feedback!' : 'Failed to submit feedback.'));
      
      if (response.success) {
        // Auto close after successful submission after 2 seconds
        setTimeout(() => {
          onClose();
          setIssueType('');
          setComment('');
          setSubmitSuccess(null);
          setSubmitMessage('');
        }, 2000);
      }
    } catch (error) {
      setSubmitSuccess(false);
      setSubmitMessage(`Error submitting feedback: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Provide Feedback</h3>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Close"
            >
              ✕
            </button>
          </div>
          
          {submitSuccess === null ? (
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  What was the issue?
                </label>
                <select
                  value={issueType}
                  onChange={(e) => setIssueType(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                >
                  <option value="">Select an issue type</option>
                  {ISSUE_TYPES.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Additional comments (optional)
                </label>
                <textarea
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md h-24"
                  placeholder="Please add any additional details that might help us improve..."
                />
              </div>
              
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50"
                  disabled={isSubmitting || !issueType}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
                </button>
              </div>
            </form>
          ) : (
            <div className={`p-4 rounded-md ${submitSuccess ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
              <p className="font-medium">
                {submitSuccess ? '✓' : '✗'} {submitMessage}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FeedbackModal;
