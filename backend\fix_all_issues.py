"""
Fix all document retrieval issues in the IR App in a simpler format.
"""
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_context_str_error():
    """Fix the context_str undefined error in server.py."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Fix the context_str variable error in system prompt
        if "{context_str}" in content:
            content = content.replace("{context_str}", "{context}")
            print("Fixed context_str reference in system prompt")
        
        # Write the fixed content
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error fixing context_str: {str(e)}")
        return False

def add_error_handling():
    """Add better error handling to LLM generation in server.py."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the generate_answer call
        generate_answer_pos = content.find("llm_router.generate_answer(")
        if generate_answer_pos > 0:
            # Find the line end
            line_end = content.find("\n", generate_answer_pos)
            
            # Check if already wrapped in try-except
            prev_lines = content[max(0, generate_answer_pos-50):generate_answer_pos]
            
            # Only add if not already wrapped
            if "try:" not in prev_lines[-10:]:
                # Extract the original line
                original_line = content[generate_answer_pos:line_end].strip()
                
                # Create replacement with try-except
                replacement = f"""try:
            {original_line}
        except Exception as e:
            logger.error(f"Error generating LLM answer: {{str(e)}}")
            # Fallback to a simpler prompt
            try:
                return llm_router.generate_answer(
                    query=query,
                    context="",  # Empty context for fallback
                    system_prompt="You are an AI assistant. Answer this question: " + query,
                    model_id=model_id,
                    use_documents_only=False
                ), document_sources, website_sources, False
            except Exception as fallback_err:
                logger.error(f"Fallback also failed: {{str(fallback_err)}}")
                return "I'm sorry, I couldn't process your question. Please try again.", [], [], True"""
                
                # Replace the original line with try-except block
                content = content[:generate_answer_pos] + replacement + content[line_end:]
                print("Added robust error handling around LLM generation")
        
        # Write the fixed content
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error adding error handling: {str(e)}")
        return False

def fix_duplicate_uploads():
    """Fix the issue with duplicate documents being uploaded on each restart."""
    try:
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the document processing loop
        process_loop = content.find("# Process each document file")
        if process_loop > 0:
            # Find the start of the loop
            loop_start = content.find("for filename in document_files:", process_loop)
            
            # Check if already fixed
            check_code = "# Check if this document already exists in Supabase"
            if check_code in content[loop_start:loop_start+500]:
                print("Duplicate upload check already implemented")
            else:
                # Find the point to insert the check
                insert_point = content.find("logger.info", loop_start)
                insert_point = content.find("\n", insert_point) + 1
                
                # Add check for existing documents
                check_code = """
        # Get the relative path for storage
        file_ext = os.path.splitext(filename)[1].lower()
        storage_path = f"{file_ext[1:]}/{filename}"
        
        # Check if this document already exists in Supabase
        try:
            # Query to check if document exists by file_path
            doc_query = f"SELECT * FROM documents WHERE file_path=eq.{storage_path}"
            existing_docs = supabase.execute_query(doc_query)
            
            if isinstance(existing_docs, list) and len(existing_docs) > 0:
                logger.info(f"Document {filename} already exists in Supabase, skipping processing")
                continue
        except Exception as e:
            logger.warning(f"Error checking if document exists: {str(e)}")
            # Continue anyway to ensure documents are loaded
"""
                
                # Insert the check code
                content = content[:insert_point] + check_code + content[insert_point:]
                print("Added check to prevent duplicate document uploads")
        
        # Write the fixed content
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Error fixing duplicate uploads: {str(e)}")
        return False

def main():
    """Apply all fixes."""
    print("\n=== FIXING DOCUMENT RETRIEVAL ISSUES ===\n")
    
    # Fix context_str error
    print("1. Fixing context_str error...")
    if fix_context_str_error():
        print("SUCCESS: Fixed context_str reference in system prompt")
    else:
        print("FAILED: Could not fix context_str error")
    
    # Add error handling
    print("\n2. Improving error handling...")
    if add_error_handling():
        print("SUCCESS: Added robust error handling for LLM generation")
    else:
        print("FAILED: Could not add error handling")
    
    # Fix duplicate uploads
    print("\n3. Preventing duplicate document uploads...")
    if fix_duplicate_uploads():
        print("SUCCESS: Added checks to prevent duplicate document uploads")
    else:
        print("FAILED: Could not fix duplicate uploads issue")
    
    print("\n=== FIXES COMPLETE ===")
    print("\nYour IR App should now:")
    print("- Properly handle document retrieval without the context_str error")
    print("- Have better error handling for LLM generation")
    print("- Only upload documents once (no duplicates)")
    print("\nRestart your server to apply these changes:")
    print("python -m uvicorn server:app --reload")

if __name__ == "__main__":
    main()
