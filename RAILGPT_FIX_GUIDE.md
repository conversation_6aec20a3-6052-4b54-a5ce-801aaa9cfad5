# RailGPT Fix Guide

This guide provides step-by-step instructions to fix the issues with RailGPT not returning answers from uploaded documents.

## Issue 1: Syntax Error in server.py

There's a syntax error in the server.py file that's preventing the backend from starting.

### Fix:

1. The error is in the `generate_llm_answer` function. The function has already been fixed in the server.py file.
2. If you still see the error, make sure there are no indentation issues or missing closing brackets.

## Issue 2: Supabase SQL Functions

The hybrid search functions in Supabase are missing or not working correctly.

### Fix:

1. Go to the Supabase dashboard: https://app.supabase.com/
2. Select your project: "RailGPT"
3. Go to the SQL Editor
4. Copy the contents of the `supabase_fix.sql` file
5. Paste the SQL into the editor and run it

This script:
- Creates direct search functions for document and website chunks
- Creates simplified hybrid search functions
- Fixes document and website chunks with NULL embeddings or text
- Checks document and chunk counts
- Tests the direct search function

## Issue 3: Supabase Access

If you can't access the Supabase dashboard directly, you can use the REST API:

1. Make sure you have the correct Supabase URL and API key in your .env file
2. Use the following curl command to execute the SQL:

```bash
curl -X POST 'https://rkllidjktazafeinezgo.supabase.co/rest/v1/rpc/exec_sql' \
-H "apikey: YOUR_SUPABASE_API_KEY" \
-H "Authorization: Bearer YOUR_SUPABASE_API_KEY" \
-H "Content-Type: application/json" \
-d '{"sql": "-- SQL from supabase_fix.sql"}'
```

Replace `YOUR_SUPABASE_API_KEY` with your actual API key and paste the SQL from `supabase_fix.sql`.

## Issue 4: Server Error Handling

The server needs better error handling for missing or invalid data.

### Fix:

The server.py file has already been patched to add error handling for:
- Missing or invalid 'similarity' values
- Missing or invalid 'text' values

## Complete Fix Process

1. **Fix the server.py syntax error**:
   - This has already been done

2. **Run the Supabase SQL fix**:
   - Go to the Supabase SQL Editor
   - Run the SQL from `supabase_fix.sql`

3. **Restart the backend server**:
   ```powershell
   cd backend
   uvicorn server:app --reload
   ```

4. **Start the frontend**:
   ```powershell
   cd frontend
   npm start
   ```

5. **Test the application**:
   - Open the RailGPT application in your browser
   - Try searching for "rapid response app" or other terms related to your uploaded documents

## Troubleshooting

If you still encounter issues:

### 1. Check the Logs

Look for specific error messages in the backend logs.

### 2. Verify Database Contents

Run these SQL queries in the Supabase SQL Editor:

```sql
-- Check documents
SELECT COUNT(*) FROM documents;

-- Check document chunks
SELECT COUNT(*) FROM document_chunks;

-- Check document chunks with embeddings
SELECT COUNT(*) FROM document_chunks WHERE embedding IS NOT NULL;

-- Check document chunks with text
SELECT COUNT(*) FROM document_chunks WHERE text IS NOT NULL AND text != '';
```

### 3. Test Direct Query

Run this SQL query to test a direct search:

```sql
-- Direct query for similar chunks
SELECT 
    dc.id, 
    dc.document_id, 
    dc.chunk_index, 
    dc.page_number, 
    dc.text, 
    d.file_path as url,
    d.display_name as title,
    1 - (dc.embedding <=> '[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]'::vector) as similarity,
    'document' as source_type
FROM 
    document_chunks dc
JOIN 
    documents d ON dc.document_id = d.id
WHERE 
    dc.text IS NOT NULL AND dc.text != ''
ORDER BY 
    dc.embedding <=> '[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]'::vector
LIMIT 5;
```

### 4. Check for Other Issues

If you're still having issues, check for:

1. Incorrect Supabase URL or API key
2. Missing tables or columns in the database
3. Issues with the LLM API (Gemini)
4. Network connectivity issues

## Final Notes

1. The direct search approach bypasses the hybrid search functions that were causing issues.
2. The patches add robust error handling for missing or invalid data.
3. The fixes ensure that only valid chunks are used for generating answers.

If you continue to experience issues, please provide the specific error messages from the logs for further assistance.
