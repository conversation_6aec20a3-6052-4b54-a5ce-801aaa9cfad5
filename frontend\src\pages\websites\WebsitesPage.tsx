import React, { useState, useEffect } from 'react';
import { Website, WebsiteExtractionDetails, ExtractionParser } from '../../types/websites';
import WebsiteExtractForm from '../../components/websites/WebsiteExtractForm';
import WebsitesTable from '../../components/websites/WebsitesTable';
import WebsiteViewModal from '../../components/websites/WebsiteViewModal';
import WebsiteCategoryManagement from '../../components/websites/WebsiteCategoryManagement';
import { getWebsites } from '../../services/api';
import { Settings } from 'lucide-react';

// Sample data for demonstration
const SAMPLE_WEBSITES: Website[] = [
  {
    id: '1',
    url: 'https://indianrailways.gov.in/railwayboard/',
    domain: 'indianrailways.gov.in',
    extractedAt: '2025-05-01T14:30:00Z',
    status: 'Success',
    submittedBy: '<EMAIL>',
    domainCategory: 'Official Railways',
  },
  {
    id: '2',
    url: 'https://irctc.co.in/nget/train-search',
    domain: 'irctc.co.in',
    extractedAt: '2025-04-29T11:45:00Z',
    status: 'Success',
    submittedBy: '<EMAIL>',
    domainCategory: 'Official Railways',
  },
  {
    id: '3',
    url: 'https://www.trainman.in/railway-stations',
    domain: 'trainman.in',
    extractedAt: '2025-04-25T09:15:00Z',
    status: 'Success',
    submittedBy: '<EMAIL>',
    domainCategory: 'Travel Guides',
  },
  {
    id: '4',
    url: 'https://rail-info.indianrailways.gov.in/mntes/',
    domain: 'rail-info.indianrailways.gov.in',
    extractedAt: '2025-04-20T16:30:00Z',
    status: 'Failed',
    submittedBy: '<EMAIL>',
    domainCategory: 'Official Railways',
  },
  {
    id: '5',
    url: 'https://www.ndtv.com/topic/indian-railways',
    domain: 'ndtv.com',
    extractedAt: '2025-05-02T10:20:00Z',
    status: 'Manual Required',
    submittedBy: '<EMAIL>',
    domainCategory: 'News Portals',
  },
];

// Sample extraction details for demonstration
const SAMPLE_EXTRACTION_DETAILS: WebsiteExtractionDetails = {
  extractionMethod: 'Trafilatura',
  fallbackHistory: ['Trafilatura', 'BeautifulSoup'],
  contentQuality: 85,
  warnings: ['Dynamic content may be missing', 'Some tables could not be extracted completely'],
  extractedContent: `
# Indian Railways

The Indian Railways is a statutory body under the jurisdiction of Ministry of Railways, Government of India that operates India's national railway system. It manages the fourth-largest railway network in the world by size, with a route length of 67,956 km (42,226 mi) and total track length of 99,235 km (61,662 mi) as of March 2020. As of the end of 2019, 71.23% of its 121,407 kilometres of track is electrified.

## Passenger Services

Indian Railways operates more than 20,000 passenger trains daily, on both long-distance and suburban routes, from approximately 7,349 stations across India.

### Train Categories

Indian Railways operates different categories of trains:

1. **Shatabdi Express**: 160 km/h (99 mph) trains that connect major cities and state capitals over short distances, typically within a day's journey.
2. **Rajdhani Express**: 130 to 140 km/h (81 to 87 mph) trains that link major state capitals to New Delhi.
3. **Duronto Express**: 130 km/h (81 mph) point-to-point non-stop trains designed to connect major cities.
4. **Vande Bharat Express**: 180 km/h (110 mph) semi-high-speed train, currently running on select routes.
5. **Jan Shatabdi Express**: 110 to 120 km/h (68 to 75 mph) intercity services serving middle-distance connectivity.
6. **Garib Rath Express**: 130 km/h (81 mph) trains that aim to provide affordable air-conditioned travel.
7. **Mail/Express Trains**: Regular long-distance train services operating throughout the country.
8. **Passenger & Fast Passenger Trains**: Short-distance services connecting smaller towns and villages.
9. **Suburban Trains**: High-frequency train services in major metropolitan areas.

### Ticket Booking

Ticket booking is primarily done through the Indian Railway Catering and Tourism Corporation (IRCTC) portal. Tickets can be booked up to 120 days in advance.

## Freight Services

Indian Railways is one of the world's largest freight carriers, moving over 1.23 billion tonnes of freight annually in FY 2019-20.

Major categories of freight traffic:
- Coal (and petroleum products): 49%
- Raw materials: 17%
- Iron and steel: 12%
- Cement: the 10%
- Food grains: 7%
- Fertilizers: 5%

## Current Projects

### High-Speed Rail Projects

1. **Mumbai–Ahmedabad Corridor**: India's first high-speed rail line (bullet train) with operational speeds of 320 km/h. Currently under construction.
2. **National High Speed Rail Corporation Limited (NHSRCL)**: Responsible for implementing high-speed rail projects in the country.

### Infrastructure Development

1. **Dedicated Freight Corridors (DFCs)**
   - Eastern DFC: 1,856 km from Ludhiana to Dankuni
   - Western DFC: 1,504 km from Dadri to Jawaharlal Nehru Port Trust

2. **Station Redevelopment Program**
   - Modernizing and upgrading major railway stations across the country
   - Public-private partnership model for development
  `,
  processingTime: 2850,
  chunks: 8,
};

const WebsitesPage: React.FC = () => {
  const [websites, setWebsites] = useState<Website[]>(SAMPLE_WEBSITES);
  const [selectedWebsite, setSelectedWebsite] = useState<Website | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [extractionDetails, setExtractionDetails] = useState<WebsiteExtractionDetails>(SAMPLE_EXTRACTION_DETAILS);
  const [isCategoryManagementOpen, setIsCategoryManagementOpen] = useState(false);

  // Fetch websites from the backend
  useEffect(() => {
    fetchWebsites();

    // Set up website extraction event listener
    const handleWebsiteExtracted = (event: CustomEvent) => {
      const newWebsite = event.detail;
      setWebsites(prev => [newWebsite, ...prev]);
      // Refresh the websites list to get the latest from Supabase
      setTimeout(() => fetchWebsites(), 2000); // Small delay to allow backend processing
    };

    // Add event listener
    window.addEventListener('websiteExtracted', handleWebsiteExtracted as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('websiteExtracted', handleWebsiteExtracted as EventListener);
    };
  }, []);

  const fetchWebsites = async () => {
    try {
      const data = await getWebsites();
      if (data && data.length > 0) {
        // Use real data from Supabase, don't mix with sample data
        setWebsites(data);
        console.log(`Loaded ${data.length} websites from Supabase`);
      } else {
        console.log('No websites found from API, using sample data');
        // Only use sample data if no real data is available
        setWebsites(SAMPLE_WEBSITES);
      }
    } catch (error) {
      console.error('Error fetching websites:', error);
      // Keep using sample data if API call fails
      setWebsites(SAMPLE_WEBSITES);
    }
  };

  const handleViewWebsite = (website: Website) => {
    setSelectedWebsite(website);

    // Fetch actual extraction details from the backend
    async function fetchExtractionDetails() {
      try {
        // Import our API functions
        const { getWebsiteExtractionDetails, getWebsiteContent } = await import('../../services/api');

        // First try the specific extraction details endpoint
        try {
          const data = await getWebsiteExtractionDetails(website.id);
          console.log('Website extraction details response:', data);
          setExtractionDetails(data);
          return;
        } catch (extractionError) {
          console.warn('Failed to get website extraction details, trying content endpoint:', extractionError);
        }

        // If that fails, try the general website content endpoint
        try {
          const contentData = await getWebsiteContent(website.id);
          console.log('Website content response:', contentData);

          // Create extraction details from content data
          const details = {
            extractedContent: contentData.content || contentData.text || 'No content available',
            extractionMethod: contentData.extraction_method || 'Direct Scraping',
            processingTime: contentData.processing_time || 850,
            warnings: contentData.warnings || [],
            // Required fields from WebsiteExtractionDetails interface
            fallbackHistory: contentData.fallback_history || ['API Response'],
            contentQuality: contentData.quality_score || 80,
            chunks: contentData.chunks || 5
          };

          setExtractionDetails(details);
          return;
        } catch (contentError) {
          console.error('Failed to get website content:', contentError);
          throw contentError; // Re-throw to be caught by the outer try-catch
        }
      } catch (error) {
        console.error('All API attempts failed, using fallback data:', error);

        // Fallback data in case of error
        const errorFallbackDetails: WebsiteExtractionDetails = {
          extractedContent: `Unable to retrieve content for ${website.url} due to an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again later.`,
          extractionMethod: 'Error Fallback',
          processingTime: 0,
          warnings: ['Error retrieving content', error instanceof Error ? error.message : 'Unknown error'],
          fallbackHistory: ['API Error', 'Error Handler'],
          contentQuality: 30,
          chunks: 0
        };

        setExtractionDetails(errorFallbackDetails);
      }
    }

    fetchExtractionDetails();
    setIsViewModalOpen(true);
  };

  const handleRetryWebsite = (website: Website) => {
    // In a real app, you would implement retry logic
    alert(`Retry extraction for: ${website.url}`);
  };

  const handleDeleteWebsite = (website: Website) => {
    if (window.confirm(`Are you sure you want to delete "${website.url}"?`)) {
      // In a real app, you would make an API call to delete the website
      // async function deleteWebsite() {
      //   try {
      //     await fetch(`/api/websites/${website.id}`, { method: 'DELETE' });
      //     setWebsites(websites.filter(site => site.id !== website.id));
      //   } catch (error) {
      //     console.error('Error deleting website:', error);
      //   }
      // }
      // deleteWebsite();

      // For the demo, just filter it out
      setWebsites(websites.filter(site => site.id !== website.id));
    }
  };

  const handleRetryWithParser = async (website: Website, parser: ExtractionParser) => {
    // In a real app, you would make an API call to retry with the selected parser
    alert(`Retrying extraction of ${website.url} with ${parser}`);

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1500));

    return true;
  };

  const handleCategoryUpdate = (updatedWebsite: Website) => {
    // Update the website in the local state
    setWebsites(prevWebsites =>
      prevWebsites.map(site =>
        site.id === updatedWebsite.id ? updatedWebsite : site
      )
    );
  };

  return (
    <div className="h-full flex flex-col bg-gray-50 transition-colors duration-300">
      {/* Fixed header section */}
      <div className="bg-white p-4 shadow-sm z-10 transition-colors duration-300">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Website Management</h1>
            <button
              onClick={() => setIsCategoryManagementOpen(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            >
              <Settings className="h-4 w-4 mr-2" />
              Manage Categories
            </button>
          </div>
        </div>
      </div>

      {/* Scrollable content section */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left side: Extract form */}
            <div className="lg:col-span-1">
              <WebsiteExtractForm />
            </div>

            {/* Right side: Websites table */}
            <div className="lg:col-span-2">
              <WebsitesTable
                websites={websites}
                onView={handleViewWebsite}
                onRetry={handleRetryWebsite}
                onDelete={handleDeleteWebsite}
                onCategoryUpdate={handleCategoryUpdate}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Website View Modal */}
      {selectedWebsite && (
        <WebsiteViewModal
          website={selectedWebsite}
          extractionDetails={extractionDetails}
          isOpen={isViewModalOpen}
          onClose={() => setIsViewModalOpen(false)}
          onRetry={handleRetryWithParser}
        />
      )}

      {/* Website Category Management Modal */}
      <WebsiteCategoryManagement
        isOpen={isCategoryManagementOpen}
        onClose={() => setIsCategoryManagementOpen(false)}
      />
    </div>
  );
};

export default WebsitesPage;
