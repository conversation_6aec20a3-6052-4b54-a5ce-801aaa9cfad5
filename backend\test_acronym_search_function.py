"""
Test script for the acronym search functionality.
"""
import os
import logging
import json
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def search_for_acronym(acronym: str, limit: int = 5):
    """Special search function for acronyms."""
    logger.info(f"Performing specialized acronym search for: {acronym}")
    
    # Sanitize the acronym
    sanitized_acronym = acronym.replace("'", "''")
    
    # Pattern 1: Look for exact acronym followed by words in parentheses (e.g., "ACP (Alarm Chain Pulling)")
    # Pattern 2: Look for exact acronym followed by "stands for" or "means" or "is"
    # Pattern 3: Look for exact acronym followed by a dash or colon and then words
    # Pattern 4: Look for the words "full form" near the acronym
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type,
        1.0 AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)'
        OR dc.text ~* '\\b{sanitized_acronym}\\s+(stands\\s+for|means|is)\\b'
        OR dc.text ~* '\\b{sanitized_acronym}\\s*[-:]'
        OR (dc.text ~* '\\b{sanitized_acronym}\\b' AND dc.text ~* 'full\\s+form')
        OR dc.text ~* '\\b{sanitized_acronym}\\b.{{0,30}}(stands\\s+for|means|refers\\s+to|is)'
    ORDER BY
        CASE
            WHEN dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)' THEN 1
            WHEN dc.text ~* 'full\\s+form.{{0,20}}\\b{sanitized_acronym}\\b' THEN 2
            WHEN dc.text ~* '\\b{sanitized_acronym}\\b.{{0,20}}full\\s+form' THEN 3
            ELSE 4
        END
    LIMIT {limit}
    """
    
    try:
        result = supabase.execute_query(search_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching for acronym: {result['error']}")
            return []
        
        logger.info(f"Found {len(result)} document chunks matching acronym '{acronym}'")
        
        # If no results found with the specialized search, try a more general search
        if not result:
            logger.info(f"No specialized matches for acronym '{acronym}', trying general search")
            
            # Fallback to a more general search
            general_query = f"""
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                d.display_name as filename,
                d.file_path as url,
                'document' as source_type,
                1.0 AS similarity
            FROM
                document_chunks dc
            JOIN
                documents d ON dc.document_id = d.id
            WHERE
                dc.text ~* '\\b{sanitized_acronym}\\b'
            LIMIT {limit}
            """
            
            result = supabase.execute_query(general_query)
            
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error with general acronym search: {result['error']}")
                return []
            
            logger.info(f"Found {len(result)} document chunks with general acronym search")
        
        return result
    except Exception as e:
        logger.error(f"Error searching for acronym: {str(e)}")
        return []

def test_acronym_search():
    """Test the acronym search functionality."""
    # Test acronyms
    acronyms = ["ACP", "FSDS", "VASP"]
    
    for acronym in acronyms:
        logger.info(f"\n=== Testing acronym search for: '{acronym}' ===\n")
        
        # Search for the acronym
        results = search_for_acronym(acronym)
        
        # Print the results
        logger.info(f"Found {len(results)} results for acronym '{acronym}'")
        
        for i, result in enumerate(results):
            logger.info(f"Result {i+1}:")
            logger.info(f"  Document: {result.get('filename', 'Unknown')}")
            logger.info(f"  Page: {result.get('page_number', 'Unknown')}")
            logger.info(f"  Text: {result.get('text', '')[:200]}...")
            logger.info("")

def main():
    """Main function."""
    test_acronym_search()

if __name__ == "__main__":
    main()
