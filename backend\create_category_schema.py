#!/usr/bin/env python3
"""
Create category management schema in Supabase using SQL execution
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supabase_client import SupabaseClient
import requests
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def execute_sql_via_rpc(client, sql_query):
    """Execute SQL using Supabase RPC if available"""
    try:
        # Try using the RPC endpoint for SQL execution
        response = requests.post(
            f"{client.url}/rest/v1/rpc/execute_sql",
            headers=client.headers,
            json={"query": sql_query},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"SQL execution failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"Error executing SQL via RPC: {str(e)}")
        return None

def create_category_schema():
    """Create the category management schema"""
    
    print("🏗️  Creating Category Management Schema")
    print("=" * 50)
    
    client = SupabaseClient()
    
    # SQL to create categories table
    categories_sql = """
    CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('main_category', 'category', 'sub_category', 'minor_category')),
        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(name, type, parent_id)
    );
    """
    
    # SQL to create website_categories table
    website_categories_sql = """
    CREATE TABLE IF NOT EXISTS website_categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    # SQL to create indexes
    indexes_sql = """
    CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
    CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);
    CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);
    CREATE INDEX IF NOT EXISTS idx_website_categories_name ON website_categories(name);
    """
    
    # SQL to create category hierarchy view
    hierarchy_view_sql = """
    CREATE OR REPLACE VIEW category_hierarchy AS
    WITH RECURSIVE category_tree AS (
        -- Base case: main categories
        SELECT 
            id,
            name,
            type,
            parent_id,
            description,
            is_active,
            sort_order,
            created_at,
            updated_at,
            name as full_path,
            1 as level
        FROM categories 
        WHERE type = 'main_category' AND is_active = true
        
        UNION ALL
        
        -- Recursive case: child categories
        SELECT 
            c.id,
            c.name,
            c.type,
            c.parent_id,
            c.description,
            c.is_active,
            c.sort_order,
            c.created_at,
            c.updated_at,
            ct.full_path || ' > ' || c.name as full_path,
            ct.level + 1 as level
        FROM categories c
        INNER JOIN category_tree ct ON c.parent_id = ct.id
        WHERE c.is_active = true
    )
    SELECT * FROM category_tree
    ORDER BY full_path;
    """
    
    # SQL to create utility function
    function_sql = """
    CREATE OR REPLACE FUNCTION get_category_path(category_id UUID)
    RETURNS TEXT
    LANGUAGE plpgsql
    AS $$
    DECLARE
        result TEXT := '';
        current_id UUID := category_id;
        current_name TEXT;
        parent_id UUID;
    BEGIN
        WHILE current_id IS NOT NULL LOOP
            SELECT name, parent_id INTO current_name, parent_id
            FROM categories 
            WHERE id = current_id;
            
            IF current_name IS NULL THEN
                EXIT;
            END IF;
            
            IF result = '' THEN
                result := current_name;
            ELSE
                result := current_name || ' > ' || result;
            END IF;
            
            current_id := parent_id;
        END LOOP;
        
        RETURN result;
    END;
    $$;
    """
    
    # Execute SQL statements
    sql_statements = [
        ("Categories table", categories_sql),
        ("Website categories table", website_categories_sql),
        ("Database indexes", indexes_sql),
        ("Category hierarchy view", hierarchy_view_sql),
        ("Utility function", function_sql)
    ]
    
    success_count = 0
    
    for name, sql in sql_statements:
        print(f"\n📝 Creating {name}...")
        
        result = execute_sql_via_rpc(client, sql)
        
        if result is not None:
            print(f"✅ {name} created successfully")
            success_count += 1
        else:
            print(f"❌ Failed to create {name}")
            # Try alternative method for simple table creation
            if "CREATE TABLE" in sql:
                print(f"   Attempting alternative method...")
                # For now, we'll note that manual creation might be needed
                print(f"   Manual creation may be required via Supabase dashboard")
    
    print(f"\n📊 Summary: {success_count}/{len(sql_statements)} operations completed")
    
    if success_count == len(sql_statements):
        print("🎉 Schema creation completed successfully!")
        return True
    else:
        print("⚠️  Some operations failed. Manual intervention may be required.")
        return False

def populate_initial_categories():
    """Populate the database with initial categories"""
    
    print("\n📋 Populating Initial Categories")
    print("=" * 50)
    
    client = SupabaseClient()
    
    # Sample main categories
    main_categories = [
        {"name": "Safety", "type": "main_category", "description": "Safety-related documents", "sort_order": 1},
        {"name": "Technical", "type": "main_category", "description": "Technical documentation", "sort_order": 2},
        {"name": "Operations", "type": "main_category", "description": "Operational documents", "sort_order": 3},
        {"name": "Administrative", "type": "main_category", "description": "Administrative documents", "sort_order": 4},
    ]
    
    # Sample website categories
    website_categories = [
        {"name": "Official Railways", "description": "Official railway websites", "sort_order": 1},
        {"name": "News Portals", "description": "Railway news websites", "sort_order": 2},
        {"name": "Technical Resources", "description": "Technical documentation sites", "sort_order": 3},
    ]
    
    try:
        # Insert main categories
        print("📝 Inserting main categories...")
        for cat in main_categories:
            try:
                result = client.supabase.table("categories").insert(cat).execute()
                if result.data:
                    print(f"   ✅ Created: {cat['name']}")
                else:
                    print(f"   ❌ Failed to create: {cat['name']}")
            except Exception as e:
                if "duplicate key" in str(e).lower():
                    print(f"   ℹ️  Already exists: {cat['name']}")
                else:
                    print(f"   ❌ Error creating {cat['name']}: {str(e)}")
        
        # Insert website categories
        print("\n📝 Inserting website categories...")
        for cat in website_categories:
            try:
                result = client.supabase.table("website_categories").insert(cat).execute()
                if result.data:
                    print(f"   ✅ Created: {cat['name']}")
                else:
                    print(f"   ❌ Failed to create: {cat['name']}")
            except Exception as e:
                if "duplicate key" in str(e).lower():
                    print(f"   ℹ️  Already exists: {cat['name']}")
                else:
                    print(f"   ❌ Error creating {cat['name']}: {str(e)}")
        
        print("✅ Initial categories populated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error populating categories: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Category Management Schema Setup")
    print("=" * 70)
    
    # Step 1: Create schema
    schema_success = create_category_schema()
    
    # Step 2: Populate initial data (only if schema creation was successful)
    if schema_success:
        populate_success = populate_initial_categories()
        
        if populate_success:
            print("\n🎉 Category management system is ready!")
            print("   You can now use the category management features in RailGPT.")
        else:
            print("\n⚠️  Schema created but initial data population failed.")
            print("   You may need to populate categories manually.")
    else:
        print("\n❌ Schema creation failed.")
        print("   Please create the tables manually via Supabase dashboard.")
        print("   Refer to the supabase_setup.sql file for the required schema.")
    
    print("\n✨ Setup completed!")
