2025-05-12 23:30:44,723 - INFO - Test started at 2025-05-12T23:30:44.723087
2025-05-12 23:30:44,727 - INFO - Starting server test...
2025-05-12 23:30:44,728 - INFO - Current directory: C:\IR App\backend
2025-05-12 23:30:44,729 - INFO - Importing server module...
2025-05-12 23:30:52,978 - INFO - NumExpr defaulting to 4 threads.
2025-05-12 23:30:55,638 - INFO - Initialized Supabase client for https://rkllidjktazafeinezgo.supabase.co
2025-05-12 23:30:55,914 - INFO - Loading faiss with AVX2 support.
2025-05-12 23:30:55,914 - INFO - Could not load library with AVX2 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx2'")
2025-05-12 23:30:55,930 - INFO - Loading faiss.
2025-05-12 23:30:56,001 - INFO - Successfully loaded faiss.
2025-05-12 23:30:56,027 - INFO - Using Supabase pgvector for vector storage
2025-05-12 23:30:58,732 - INFO - Gemini API configured successfully
2025-05-12 23:30:58,783 - INFO - Available LLM models: ['gemini-2.0-flash', 'gemini', 'chatgpt', 'groq', 'deepseek', 'qwen', 'huggingface', 'ollama']
2025-05-12 23:30:58,904 - INFO - Server module imported successfully
2025-05-12 23:30:58,904 - INFO - Server app is defined
2025-05-12 23:30:58,905 - INFO - Server routes:
2025-05-12 23:30:58,906 - INFO -   /openapi.json - {'GET', 'HEAD'}
2025-05-12 23:30:58,906 - INFO -   /docs - {'GET', 'HEAD'}
2025-05-12 23:30:58,907 - INFO -   /docs/oauth2-redirect - {'GET', 'HEAD'}
2025-05-12 23:30:58,908 - INFO -   /redoc - {'GET', 'HEAD'}
2025-05-12 23:30:58,908 - INFO -   / - {'GET'}
2025-05-12 23:30:58,909 - INFO -   /api/clear-database - {'POST'}
2025-05-12 23:30:58,910 - INFO -   /api/documents - {'GET'}
2025-05-12 23:30:58,911 - INFO -   /api/websites - {'GET'}
2025-05-12 23:30:58,912 - INFO -   /api/chunks - {'GET'}
2025-05-12 23:30:58,912 - INFO -   /api/query - {'POST'}
2025-05-12 23:30:58,913 - INFO -   /api/upload-document - {'POST'}
2025-05-12 23:30:58,914 - INFO -   /api/add-website - {'POST'}
2025-05-12 23:30:58,917 - INFO -   /api/documents/{document_id}/extraction-details - {'GET'}
2025-05-12 23:30:58,918 - INFO -   /api/documents/{document_id}/content - {'GET'}
2025-05-12 23:30:58,919 - INFO -   /api/websites/{website_id}/extraction-details - {'GET'}
2025-05-12 23:30:58,922 - INFO -   /api/websites/{website_id}/content - {'GET'}
2025-05-12 23:30:58,923 - INFO -   /api/feedback - {'POST'}
2025-05-12 23:30:58,923 - INFO -   /api/feedback/emails - {'GET'}
2025-05-12 23:30:58,930 - INFO -   /api/feedback/emails - {'POST'}
2025-05-12 23:30:58,930 - INFO - Initializing Supabase client...
2025-05-12 23:30:58,931 - INFO - Supabase client initialized successfully
2025-05-12 23:30:58,931 - INFO - Testing document query...
2025-05-12 23:31:03,254 - INFO - HTTP Request: GET https://rkllidjktazafeinezgo.supabase.co/rest/v1/documents?select=count "HTTP/1.1 200 OK"
2025-05-12 23:31:03,274 - INFO - Document count: [{'count': 8}]
2025-05-12 23:31:03,276 - INFO - Test result: PASSED
2025-05-12 23:31:03,279 - INFO - Test completed at 2025-05-12T23:31:03.279087
2025-05-12 23:31:03,280 - INFO - Log file: C:\IR App\backend\server_test.log
