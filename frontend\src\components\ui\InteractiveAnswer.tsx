import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import FeedbackModal from './FeedbackModal';

interface InteractiveAnswerProps {
  content: string;
  query: string;
  model?: string;
  chatId?: string;
  enableMarkdown?: boolean;
}

const InteractiveAnswer: React.FC<InteractiveAnswerProps> = ({
  content,
  query,
  model,
  chatId,
  enableMarkdown = true
}) => {
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [feedbackGiven, setFeedbackGiven] = useState<'positive' | 'negative' | null>(null);

  const handleCopy = () => {
    navigator.clipboard.writeText(content);
    // Optional: Show a toast notification
  };

  const handlePositiveFeedback = () => {
    setFeedbackGiven('positive');
    // Could implement analytics or backend call here if needed
  };

  const handleNegativeFeedback = () => {
    setFeedbackGiven('negative');
    setIsFeedbackModalOpen(true);
  };

  // Removed toggle functionality - always show formatted view

  // Custom markdown components for better styling
  const markdownComponents = {
    // Style headings
    h1: ({ children }: any) => <h1 className="text-xl font-bold mb-2 text-gray-800">{children}</h1>,
    h2: ({ children }: any) => <h2 className="text-lg font-semibold mb-2 text-gray-800">{children}</h2>,
    h3: ({ children }: any) => <h3 className="text-md font-semibold mb-1 text-gray-800">{children}</h3>,

    // Style paragraphs
    p: ({ children }: any) => <p className="mb-2 text-gray-700 leading-relaxed">{children}</p>,

    // Style lists
    ul: ({ children }: any) => <ul className="list-disc list-inside mb-2 ml-2 text-gray-700">{children}</ul>,
    ol: ({ children }: any) => <ol className="list-decimal list-inside mb-2 ml-2 text-gray-700">{children}</ol>,
    li: ({ children }: any) => <li className="mb-1">{children}</li>,

    // Style emphasis
    strong: ({ children }: any) => <strong className="font-semibold text-gray-800">{children}</strong>,
    em: ({ children }: any) => <em className="italic text-gray-700">{children}</em>,

    // Style code
    code: ({ children }: any) => (
      <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-800">
        {children}
      </code>
    ),
    pre: ({ children }: any) => (
      <pre className="bg-gray-100 p-3 rounded-lg overflow-x-auto mb-2">
        <code className="text-sm font-mono text-gray-800">{children}</code>
      </pre>
    ),

    // Style blockquotes
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-blue-300 pl-3 italic text-gray-600 mb-2">
        {children}
      </blockquote>
    ),

    // Style tables
    table: ({ children }: any) => (
      <table className="min-w-full border-collapse border border-gray-300 mb-2">
        {children}
      </table>
    ),
    th: ({ children }: any) => (
      <th className="border border-gray-300 bg-gray-100 px-2 py-1 text-left font-semibold">
        {children}
      </th>
    ),
    td: ({ children }: any) => (
      <td className="border border-gray-300 px-2 py-1">
        {children}
      </td>
    ),
  };

  return (
    <div className="relative">
      {/* Content Display - Always show formatted view */}
      <div className="prose max-w-none">
        {enableMarkdown ? (
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={markdownComponents}
          >
            {content}
          </ReactMarkdown>
        ) : (
          <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">{content}</div>
        )}
      </div>

      {/* Control Bar */}
      <div className="mt-3 flex justify-between items-center border-t border-gray-100 pt-2 text-gray-500">
        <div className="flex items-center space-x-2">
          {/* Format toggle removed - always show formatted view */}
          <button
            onClick={handleCopy}
            className="flex items-center p-1 px-2 text-xs hover:bg-gray-100 rounded"
            title="Copy answer to clipboard"
          >
            <span className="mr-1">📄</span> Copy
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handlePositiveFeedback}
            className={`flex items-center p-1 px-2 text-xs hover:bg-gray-100 rounded ${feedbackGiven === 'positive' ? 'bg-green-50 text-green-700' : ''}`}
            title="This answer was helpful"
            disabled={feedbackGiven !== null}
          >
            <span className="mr-1">👍</span> {feedbackGiven === 'positive' ? 'Thank you!' : 'Helpful'}
          </button>

          <button
            onClick={handleNegativeFeedback}
            className={`flex items-center p-1 px-2 text-xs hover:bg-gray-100 rounded ${feedbackGiven === 'negative' ? 'bg-gray-100' : ''}`}
            title="This answer needs improvement"
            disabled={feedbackGiven !== null}
          >
            <span className="mr-1">👎</span> {feedbackGiven === 'negative' ? 'Feedback sent' : 'Not helpful'}
          </button>
        </div>
      </div>

      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        queryText={query}
        answerText={content}
        model={model}
        chatId={chatId}
      />
    </div>
  );
};

export default InteractiveAnswer;
