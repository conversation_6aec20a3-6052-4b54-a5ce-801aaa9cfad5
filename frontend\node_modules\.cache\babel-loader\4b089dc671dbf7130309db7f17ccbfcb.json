{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\components\\\\documents\\\\DocumentsTable.tsx\";\nimport React, { useState } from 'react';\nimport DocumentCategoryEditor from './DocumentCategoryEditor';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DocumentsTable = ({\n  documents,\n  onView,\n  onEdit,\n  onDelete,\n  onReprocess,\n  onCategoryUpdate\n}) => {\n  const [sortField, setSortField] = useState('uploadedAt');\n  const [sortDirection, setSortDirection] = useState('desc');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [mainCategoryFilter, setMainCategoryFilter] = useState('');\n  const [dateFilter, setDateFilter] = useState('');\n  const [fileTypeFilter, setFileTypeFilter] = useState('');\n  const [selectedDocuments, setSelectedDocuments] = useState([]);\n  const [showFilters, setShowFilters] = useState(false);\n  const [dateRangeFilter, setDateRangeFilter] = useState({\n    startDate: '',\n    endDate: ''\n  });\n\n  // Category editor state\n  const [categoryEditorOpen, setCategoryEditorOpen] = useState(false);\n  const [documentToEdit, setDocumentToEdit] = useState(null);\n\n  // Bulk category editor state\n  const [bulkCategoryEditorOpen, setBulkCategoryEditorOpen] = useState(false);\n\n  // Handle sorting\n  const handleSort = field => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  // Toggle selection of all documents\n  const toggleSelectAll = docs => {\n    if (selectedDocuments.length === docs.length) {\n      setSelectedDocuments([]);\n    } else {\n      setSelectedDocuments(docs.map(doc => doc.id));\n    }\n  };\n\n  // Toggle selection of a single document\n  const toggleSelectDocument = id => {\n    if (selectedDocuments.includes(id)) {\n      setSelectedDocuments(selectedDocuments.filter(docId => docId !== id));\n    } else {\n      setSelectedDocuments([...selectedDocuments, id]);\n    }\n  };\n\n  // Handle batch operations\n  const handleBatchOperation = operation => {\n    if (selectedDocuments.length === 0) return;\n    const selectedDocs = documents.filter(doc => selectedDocuments.includes(doc.id));\n    if (operation === 'delete') {\n      if (window.confirm(`Are you sure you want to delete ${selectedDocuments.length} selected document(s)?`)) {\n        selectedDocs.forEach(doc => onDelete(doc));\n        setSelectedDocuments([]);\n      }\n    } else if (operation === 'reprocess') {\n      selectedDocs.forEach(doc => onReprocess(doc));\n      setSelectedDocuments([]);\n    }\n  };\n\n  // Reset all filters\n  const resetFilters = () => {\n    setSearchQuery('');\n    setMainCategoryFilter('');\n    setCategoryFilter('');\n    setStatusFilter('');\n    setFileTypeFilter('');\n    setDateFilter('');\n    setDateRangeFilter({\n      startDate: '',\n      endDate: ''\n    });\n  };\n\n  // Handle date range filter changes\n  const handleDateRangeChange = (field, value) => {\n    setDateRangeFilter(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Handle category editing\n  const handleEditCategories = document => {\n    setDocumentToEdit(document);\n    setCategoryEditorOpen(true);\n  };\n  const handleCategoryEditorClose = () => {\n    setCategoryEditorOpen(false);\n    setDocumentToEdit(null);\n  };\n  const handleCategoryUpdate = updatedDocument => {\n    if (onCategoryUpdate) {\n      onCategoryUpdate(updatedDocument);\n    }\n  };\n\n  // Filter documents based on all filters\n  const filteredDocuments = documents.filter(doc => {\n    // Search query filter (check name and other fields)\n    if (searchQuery) {\n      var _doc$uploadedBy, _doc$category, _doc$mainCategory;\n      const query = searchQuery.toLowerCase();\n      const matchesQuery = doc.name.toLowerCase().includes(query) || ((_doc$uploadedBy = doc.uploadedBy) === null || _doc$uploadedBy === void 0 ? void 0 : _doc$uploadedBy.toLowerCase().includes(query)) || ((_doc$category = doc.category) === null || _doc$category === void 0 ? void 0 : _doc$category.toLowerCase().includes(query)) || ((_doc$mainCategory = doc.mainCategory) === null || _doc$mainCategory === void 0 ? void 0 : _doc$mainCategory.toLowerCase().includes(query));\n      if (!matchesQuery) return false;\n    }\n\n    // Main category filter\n    if (mainCategoryFilter && doc.mainCategory !== mainCategoryFilter) {\n      return false;\n    }\n\n    // Category filter\n    if (categoryFilter && !(doc.mainCategory === categoryFilter || doc.category === categoryFilter || doc.subCategory === categoryFilter || doc.minorCategory === categoryFilter)) {\n      return false;\n    }\n\n    // Status filter\n    if (statusFilter && doc.status !== statusFilter) {\n      return false;\n    }\n\n    // File type filter\n    if (fileTypeFilter && doc.fileType !== fileTypeFilter) {\n      return false;\n    }\n\n    // Date filter (last day, week, month)\n    if (dateFilter) {\n      const docDate = new Date(doc.uploadedAt);\n      const now = new Date();\n      if (dateFilter === 'day') {\n        // Last 24 hours\n        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n        if (docDate < yesterday) return false;\n      } else if (dateFilter === 'week') {\n        // Last 7 days\n        const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        if (docDate < lastWeek) return false;\n      } else if (dateFilter === 'month') {\n        // Last 30 days\n        const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        if (docDate < lastMonth) return false;\n      }\n    }\n\n    // Date range filter\n    if (dateRangeFilter.startDate || dateRangeFilter.endDate) {\n      const docDate = new Date(doc.uploadedAt);\n      if (dateRangeFilter.startDate) {\n        const startDate = new Date(dateRangeFilter.startDate);\n        // Set to beginning of day\n        startDate.setHours(0, 0, 0, 0);\n        if (docDate < startDate) return false;\n      }\n      if (dateRangeFilter.endDate) {\n        const endDate = new Date(dateRangeFilter.endDate);\n        // Set to end of day\n        endDate.setHours(23, 59, 59, 999);\n        if (docDate > endDate) return false;\n      }\n    }\n    return true;\n  });\n\n  // Sort documents\n  const sortedDocuments = [...filteredDocuments].sort((a, b) => {\n    var _a$sortField, _b$sortField;\n    // Use optional chaining and nullish coalescing to handle undefined\n    const aValue = (_a$sortField = a[sortField]) !== null && _a$sortField !== void 0 ? _a$sortField : '';\n    const bValue = (_b$sortField = b[sortField]) !== null && _b$sortField !== void 0 ? _b$sortField : '';\n    if (aValue < bValue) {\n      return sortDirection === 'asc' ? -1 : 1;\n    }\n    if (aValue > bValue) {\n      return sortDirection === 'asc' ? 1 : -1;\n    }\n    return 0;\n  });\n\n  // Format the date for display\n  const formatDate = dateStr => {\n    const date = new Date(dateStr);\n    return new Intl.DateTimeFormat('en-IN', {\n      day: '2-digit',\n      month: 'short',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    }).format(date);\n  };\n\n  // Render status badge\n  const renderStatusBadge = status => {\n    let bgColor;\n    switch (status) {\n      case 'Extracted':\n        bgColor = 'bg-green-100 text-green-800';\n        break;\n      case 'Pending':\n        bgColor = 'bg-yellow-100 text-yellow-800';\n        break;\n      case 'Manual Review':\n        bgColor = 'bg-red-100 text-red-800';\n        break;\n      default:\n        bgColor = 'bg-gray-100 text-gray-800';\n    }\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `${bgColor} px-2 py-1 rounded-full text-xs font-medium`,\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Get unique categories and other filter options\n  const uniqueMainCategories = Array.from(new Set(documents.map(doc => doc.mainCategory).filter(Boolean)));\n  const uniqueCategories = Array.from(new Set(documents.flatMap(doc => [doc.category, doc.subCategory, doc.minorCategory]).filter(Boolean)));\n  const uniqueFileTypes = Array.from(new Set(documents.map(doc => doc.fileType).filter(Boolean)));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          \"aria-label\": \"Filter controls\",\n          className: \"text-lg font-semibold\",\n          children: \"Manage Documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowFilters(!showFilters),\n            className: \"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md flex items-center text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: showFilters ? 'Hide Filters' : 'Show Filters'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-4 w-4 ml-1\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), selectedDocuments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleBatchOperation('reprocess'),\n              className: \"px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 rounded-md text-sm\",\n              children: [\"Reprocess (\", selectedDocuments.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleBatchOperation('delete'),\n              className: \"px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm\",\n              children: [\"Delete (\", selectedDocuments.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:w-1/2 lg:w-2/3\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search documents by name, category, or uploader...\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:w-1/2 lg:w-1/3\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            value: statusFilter,\n            onChange: e => setStatusFilter(e.target.value),\n            className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Statuses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Extracted\",\n              children: \"Extracted\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Pending\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Processing\",\n              children: \"Processing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Manual Review\",\n              children: \"Manual Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Failed\",\n              children: \"Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 bg-gray-100 rounded-lg mt-4 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: \"Main Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: mainCategoryFilter,\n                onChange: e => setMainCategoryFilter(e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Main Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), uniqueMainCategories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: categoryFilter,\n                onChange: e => setCategoryFilter(e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this), uniqueCategories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: \"File Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: fileTypeFilter,\n                onChange: e => setFileTypeFilter(e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All File Types\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this), uniqueFileTypes.map(fileType => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: fileType,\n                  children: fileType.toUpperCase()\n                }, fileType, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: \"Date Added\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: dateFilter,\n                onChange: e => setDateFilter(e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"day\",\n                  children: \"Last 24 Hours\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"week\",\n                  children: \"Last 7 Days\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"month\",\n                  children: \"Last 30 Days\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: \"Date Range\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1/2\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    id: \"start-date\",\n                    value: dateRangeFilter.startDate,\n                    onChange: e => handleDateRangeChange('startDate', e.target.value),\n                    className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                    placeholder: \"Start date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-1/2\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    id: \"end-date\",\n                    value: dateRangeFilter.endDate,\n                    onChange: e => handleDateRangeChange('endDate', e.target.value),\n                    className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                    placeholder: \"End date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/3 flex items-end\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: resetFilters,\n                className: \"px-3 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md text-sm w-full\",\n                children: \"Reset Filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500 mb-2\",\n        children: [\"Showing \", filteredDocuments.length, \" of \", documents.length, \" documents\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full divide-y divide-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-3 py-3 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500\",\n                checked: selectedDocuments.length > 0 && selectedDocuments.length === filteredDocuments.length,\n                onChange: () => toggleSelectAll(filteredDocuments)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",\n              onClick: () => handleSort('name'),\n              children: [\"Name\", sortField === 'name' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",\n              onClick: () => handleSort('uploadedAt'),\n              children: [\"Date Uploaded\", sortField === 'uploadedAt' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",\n              onClick: () => handleSort('mainCategory'),\n              children: [\"Category\", sortField === 'mainCategory' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",\n              onClick: () => handleSort('uploadedBy'),\n              children: [\"Uploaded By\", sortField === 'uploadedBy' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",\n              onClick: () => handleSort('qualityScore'),\n              children: [\"Quality\", sortField === 'qualityScore' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\",\n              onClick: () => handleSort('status'),\n              children: [\"Status\", sortField === 'status' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          className: \"bg-white divide-y divide-gray-200\",\n          children: sortedDocuments.length > 0 ? sortedDocuments.map(document => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: selectedDocuments.includes(document.id) ? 'bg-blue-50' : undefined,\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-3 py-4 whitespace-nowrap text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500\",\n                checked: selectedDocuments.includes(document.id),\n                onChange: () => toggleSelectDocument(document.id)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: document.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: document.fileType.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: formatDate(document.uploadedAt)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-900\",\n                children: [document.mainCategory || 'N/A', document.category && /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\" / \", document.category]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 21\n              }, this), document.subCategory && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: [document.subCategory, document.minorCategory && /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\" / \", document.minorCategory]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 52\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: document.uploadedBy || 'Unknown'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: document.qualityScore !== undefined ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-24 bg-gray-200 rounded-full h-2.5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-600 h-2.5 rounded-full\",\n                    style: {\n                      width: `${document.qualityScore}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-300 h-2.5 rounded-r-full\",\n                    style: {\n                      width: `${100 - document.qualityScore}%`,\n                      float: 'right'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700 ml-2\",\n                  children: [document.qualityScore, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: renderStatusBadge(document.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2 justify-end\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => onView(document),\n                  className: \"text-blue-600 hover:text-blue-900\",\n                  title: \"View document details\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => onEdit(document),\n                  className: \"text-indigo-600 hover:text-indigo-900\",\n                  title: \"Edit document\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 622,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEditCategories(document),\n                  className: \"text-purple-600 hover:text-purple-900\",\n                  title: \"Edit categories\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => onReprocess(document),\n                  className: \"text-yellow-600 hover:text-yellow-900\",\n                  title: \"Reprocess document\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 640,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => onDelete(document),\n                  className: \"text-red-600 hover:text-red-900\",\n                  title: \"Delete document\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 19\n            }, this)]\n          }, document.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: 8,\n              className: \"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\",\n              children: \"No documents found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 7\n    }, this), documentToEdit && /*#__PURE__*/_jsxDEV(DocumentCategoryEditor, {\n      document: documentToEdit,\n      isOpen: categoryEditorOpen,\n      onClose: handleCategoryEditorClose,\n      onSave: handleCategoryUpdate\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n};\nexport default DocumentsTable;", "map": {"version": 3, "names": ["React", "useState", "DocumentCategoryEditor", "jsxDEV", "_jsxDEV", "DocumentsTable", "documents", "onView", "onEdit", "onDelete", "onReprocess", "onCategoryUpdate", "sortField", "setSortField", "sortDirection", "setSortDirection", "searchQuery", "setSearch<PERSON>uery", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "statusFilter", "setStatus<PERSON>ilter", "mainCategoryFilter", "setMainCategory<PERSON><PERSON><PERSON>", "dateFilter", "setDateFilter", "fileTypeFilter", "setFileTypeFilter", "selectedDocuments", "setSelectedDocuments", "showFilters", "setShowFilters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setDateRangeFilter", "startDate", "endDate", "categoryEditorOpen", "setCategoryEditorOpen", "documentToEdit", "setDocumentToEdit", "bulkCategoryEditorOpen", "setBulkCategoryEditorOpen", "handleSort", "field", "toggleSelectAll", "docs", "length", "map", "doc", "id", "toggleSelectDocument", "includes", "filter", "docId", "handleBatchOperation", "operation", "selectedDocs", "window", "confirm", "for<PERSON>ach", "resetFilters", "handleDateRangeChange", "value", "prev", "handleEditCategories", "document", "handleCategoryEditorClose", "handleCategoryUpdate", "updatedDocument", "filteredDocuments", "_doc$uploadedBy", "_doc$category", "_doc$mainCategory", "query", "toLowerCase", "matchesQuery", "name", "uploadedBy", "category", "mainCategory", "subCategory", "minorCategory", "status", "fileType", "docDate", "Date", "uploadedAt", "now", "yesterday", "getTime", "lastWeek", "lastM<PERSON>h", "setHours", "sortedDocuments", "sort", "a", "b", "_a$sortField", "_b$sortField", "aValue", "bValue", "formatDate", "dateStr", "date", "Intl", "DateTimeFormat", "day", "month", "year", "hour", "minute", "format", "renderStatusBadge", "bgColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "uniqueMainCategories", "Array", "from", "Set", "Boolean", "uniqueCategories", "flatMap", "uniqueFileTypes", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "type", "placeholder", "onChange", "e", "target", "toUpperCase", "scope", "checked", "undefined", "qualityScore", "style", "width", "float", "title", "colSpan", "isOpen", "onClose", "onSave"], "sources": ["C:/IR App/frontend/src/components/documents/DocumentsTable.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Document } from '../../types/documents';\nimport DocumentCategoryEditor from './DocumentCategoryEditor';\nimport BulkCategoryEditor from './BulkCategoryEditor';\n\ninterface DocumentsTableProps {\n  documents: Document[];\n  onView: (document: Document) => void;\n  onEdit: (document: Document) => void;\n  onDelete: (document: Document) => void;\n  onReprocess: (document: Document) => void;\n  onCategoryUpdate?: (updatedDocument: Document) => void;\n}\n\nconst DocumentsTable: React.FC<DocumentsTableProps> = ({\n  documents,\n  onView,\n  onEdit,\n  onDelete,\n  onReprocess,\n  onCategoryUpdate,\n}) => {\n  const [sortField, setSortField] = useState<keyof Document>('uploadedAt');\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [mainCategoryFilter, setMainCategoryFilter] = useState('');\n  const [dateFilter, setDateFilter] = useState('');\n  const [fileTypeFilter, setFileTypeFilter] = useState('');\n  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);\n  const [showFilters, setShowFilters] = useState(false);\n  const [dateRangeFilter, setDateRangeFilter] = useState<{startDate: string; endDate: string}>({startDate: '', endDate: ''});\n\n  // Category editor state\n  const [categoryEditorOpen, setCategoryEditorOpen] = useState(false);\n  const [documentToEdit, setDocumentToEdit] = useState<Document | null>(null);\n\n  // Bulk category editor state\n  const [bulkCategoryEditorOpen, setBulkCategoryEditorOpen] = useState(false);\n\n  // Handle sorting\n  const handleSort = (field: keyof Document) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  // Toggle selection of all documents\n  const toggleSelectAll = (docs: Document[]) => {\n    if (selectedDocuments.length === docs.length) {\n      setSelectedDocuments([]);\n    } else {\n      setSelectedDocuments(docs.map(doc => doc.id));\n    }\n  };\n\n  // Toggle selection of a single document\n  const toggleSelectDocument = (id: string) => {\n    if (selectedDocuments.includes(id)) {\n      setSelectedDocuments(selectedDocuments.filter(docId => docId !== id));\n    } else {\n      setSelectedDocuments([...selectedDocuments, id]);\n    }\n  };\n\n  // Handle batch operations\n  const handleBatchOperation = (operation: 'delete' | 'reprocess') => {\n    if (selectedDocuments.length === 0) return;\n\n    const selectedDocs = documents.filter(doc => selectedDocuments.includes(doc.id));\n\n    if (operation === 'delete') {\n      if (window.confirm(`Are you sure you want to delete ${selectedDocuments.length} selected document(s)?`)) {\n        selectedDocs.forEach(doc => onDelete(doc));\n        setSelectedDocuments([]);\n      }\n    } else if (operation === 'reprocess') {\n      selectedDocs.forEach(doc => onReprocess(doc));\n      setSelectedDocuments([]);\n    }\n  };\n\n  // Reset all filters\n  const resetFilters = () => {\n    setSearchQuery('');\n    setMainCategoryFilter('');\n    setCategoryFilter('');\n    setStatusFilter('');\n    setFileTypeFilter('');\n    setDateFilter('');\n    setDateRangeFilter({ startDate: '', endDate: '' });\n  };\n\n  // Handle date range filter changes\n  const handleDateRangeChange = (field: 'startDate' | 'endDate', value: string) => {\n    setDateRangeFilter(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Handle category editing\n  const handleEditCategories = (document: Document) => {\n    setDocumentToEdit(document);\n    setCategoryEditorOpen(true);\n  };\n\n  const handleCategoryEditorClose = () => {\n    setCategoryEditorOpen(false);\n    setDocumentToEdit(null);\n  };\n\n  const handleCategoryUpdate = (updatedDocument: Document) => {\n    if (onCategoryUpdate) {\n      onCategoryUpdate(updatedDocument);\n    }\n  };\n\n  // Filter documents based on all filters\n  const filteredDocuments = documents.filter((doc) => {\n    // Search query filter (check name and other fields)\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      const matchesQuery =\n        doc.name.toLowerCase().includes(query) ||\n        doc.uploadedBy?.toLowerCase().includes(query) ||\n        doc.category?.toLowerCase().includes(query) ||\n        doc.mainCategory?.toLowerCase().includes(query);\n\n      if (!matchesQuery) return false;\n    }\n\n    // Main category filter\n    if (mainCategoryFilter && doc.mainCategory !== mainCategoryFilter) {\n      return false;\n    }\n\n    // Category filter\n    if (\n      categoryFilter &&\n      !(\n        doc.mainCategory === categoryFilter ||\n        doc.category === categoryFilter ||\n        doc.subCategory === categoryFilter ||\n        doc.minorCategory === categoryFilter\n      )\n    ) {\n      return false;\n    }\n\n    // Status filter\n    if (statusFilter && doc.status !== statusFilter) {\n      return false;\n    }\n\n    // File type filter\n    if (fileTypeFilter && doc.fileType !== fileTypeFilter) {\n      return false;\n    }\n\n    // Date filter (last day, week, month)\n    if (dateFilter) {\n      const docDate = new Date(doc.uploadedAt);\n      const now = new Date();\n\n      if (dateFilter === 'day') {\n        // Last 24 hours\n        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n        if (docDate < yesterday) return false;\n      } else if (dateFilter === 'week') {\n        // Last 7 days\n        const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        if (docDate < lastWeek) return false;\n      } else if (dateFilter === 'month') {\n        // Last 30 days\n        const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        if (docDate < lastMonth) return false;\n      }\n    }\n\n    // Date range filter\n    if (dateRangeFilter.startDate || dateRangeFilter.endDate) {\n      const docDate = new Date(doc.uploadedAt);\n\n      if (dateRangeFilter.startDate) {\n        const startDate = new Date(dateRangeFilter.startDate);\n        // Set to beginning of day\n        startDate.setHours(0, 0, 0, 0);\n        if (docDate < startDate) return false;\n      }\n\n      if (dateRangeFilter.endDate) {\n        const endDate = new Date(dateRangeFilter.endDate);\n        // Set to end of day\n        endDate.setHours(23, 59, 59, 999);\n        if (docDate > endDate) return false;\n      }\n    }\n\n    return true;\n  });\n\n  // Sort documents\n  const sortedDocuments = [...filteredDocuments].sort((a, b) => {\n    // Use optional chaining and nullish coalescing to handle undefined\n    const aValue = a[sortField] ?? '';\n    const bValue = b[sortField] ?? '';\n\n    if (aValue < bValue) {\n      return sortDirection === 'asc' ? -1 : 1;\n    }\n    if (aValue > bValue) {\n      return sortDirection === 'asc' ? 1 : -1;\n    }\n    return 0;\n  });\n\n  // Format the date for display\n  const formatDate = (dateStr: string) => {\n    const date = new Date(dateStr);\n    return new Intl.DateTimeFormat('en-IN', {\n      day: '2-digit',\n      month: 'short',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    }).format(date);\n  };\n\n  // Render status badge\n  const renderStatusBadge = (status: Document['status']) => {\n    let bgColor;\n    switch (status) {\n      case 'Extracted':\n        bgColor = 'bg-green-100 text-green-800';\n        break;\n      case 'Pending':\n        bgColor = 'bg-yellow-100 text-yellow-800';\n        break;\n      case 'Manual Review':\n        bgColor = 'bg-red-100 text-red-800';\n        break;\n      default:\n        bgColor = 'bg-gray-100 text-gray-800';\n    }\n\n    return (\n      <span className={`${bgColor} px-2 py-1 rounded-full text-xs font-medium`}>\n        {status}\n      </span>\n    );\n  };\n\n  // Get unique categories and other filter options\n  const uniqueMainCategories = Array.from(new Set(documents.map(doc => doc.mainCategory).filter(Boolean)));\n  const uniqueCategories = Array.from(\n    new Set(\n      documents.flatMap((doc) => [\n        doc.category,\n        doc.subCategory,\n        doc.minorCategory,\n      ]).filter(Boolean)\n    )\n  );\n  const uniqueFileTypes = Array.from(new Set(documents.map(doc => doc.fileType).filter(Boolean)));\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 aria-label=\"Filter controls\" className=\"text-lg font-semibold\">Manage Documents</h2>\n\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md flex items-center text-sm\"\n            >\n              <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 ml-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\" />\n              </svg>\n            </button>\n\n            {selectedDocuments.length > 0 && (\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => handleBatchOperation('reprocess')}\n                  className=\"px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 rounded-md text-sm\"\n                >\n                  Reprocess ({selectedDocuments.length})\n                </button>\n                <button\n                  onClick={() => handleBatchOperation('delete')}\n                  className=\"px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm\"\n                >\n                  Delete ({selectedDocuments.length})\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Basic Search */}\n        <div className=\"flex flex-col md:flex-row gap-4 mb-4\">\n          <div className=\"md:w-1/2 lg:w-2/3\">\n            <input\n              type=\"text\"\n              placeholder=\"Search documents by name, category, or uploader...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          <div className=\"md:w-1/2 lg:w-1/3\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">All Statuses</option>\n              <option value=\"Extracted\">Extracted</option>\n              <option value=\"Pending\">Pending</option>\n              <option value=\"Processing\">Processing</option>\n              <option value=\"Manual Review\">Manual Review</option>\n              <option value=\"Failed\">Failed</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Advanced Filters */}\n        {showFilters && (\n          <div className=\"p-4 bg-gray-100 rounded-lg mt-4 mb-6\">\n            <div className=\"flex flex-col space-y-4\">\n              <div className=\"flex flex-col md:flex-row gap-4\">\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">Main Category</label>\n                  <select\n                    value={mainCategoryFilter}\n                    onChange={(e) => setMainCategoryFilter(e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">All Main Categories</option>\n                    {uniqueMainCategories.map((category) => (\n                      <option key={category} value={category}>\n                        {category}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">Category</label>\n                  <select\n                    value={categoryFilter}\n                    onChange={(e) => setCategoryFilter(e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">All Categories</option>\n                    {uniqueCategories.map((category) => (\n                      <option key={category} value={category}>\n                        {category}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">File Type</label>\n                  <select\n                    value={fileTypeFilter}\n                    onChange={(e) => setFileTypeFilter(e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">All File Types</option>\n                    {uniqueFileTypes.map((fileType) => (\n                      <option key={fileType} value={fileType}>\n                        {fileType.toUpperCase()}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Second row with date filters */}\n              <div className=\"flex flex-col md:flex-row gap-4\">\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">Date Added</label>\n                  <select\n                    value={dateFilter}\n                    onChange={(e) => setDateFilter(e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">All Time</option>\n                    <option value=\"day\">Last 24 Hours</option>\n                    <option value=\"week\">Last 7 Days</option>\n                    <option value=\"month\">Last 30 Days</option>\n                  </select>\n                </div>\n\n                <div className=\"md:w-1/3\">\n                  <label className=\"block text-xs font-medium text-gray-700 mb-1\">Date Range</label>\n                  <div className=\"flex space-x-2\">\n                    <div className=\"w-1/2\">\n                      <input\n                        type=\"date\"\n                        id=\"start-date\"\n                        value={dateRangeFilter.startDate}\n                        onChange={(e) => handleDateRangeChange('startDate', e.target.value)}\n                        className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"Start date\"\n                      />\n                    </div>\n                    <div className=\"w-1/2\">\n                      <input\n                        type=\"date\"\n                        id=\"end-date\"\n                        value={dateRangeFilter.endDate}\n                        onChange={(e) => handleDateRangeChange('endDate', e.target.value)}\n                        className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"End date\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"md:w-1/3 flex items-end\">\n                  <button\n                    onClick={resetFilters}\n                    className=\"px-3 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md text-sm w-full\"\n                  >\n                    Reset Filters\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"text-sm text-gray-500 mb-2\">\n          Showing {filteredDocuments.length} of {documents.length} documents\n        </div>\n      </div>\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th scope=\"col\" className=\"px-3 py-3 text-center\">\n                <input\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500\"\n                  checked={selectedDocuments.length > 0 && selectedDocuments.length === filteredDocuments.length}\n                  onChange={() => toggleSelectAll(filteredDocuments)}\n                />\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('name')}\n              >\n                Name\n                {sortField === 'name' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('uploadedAt')}\n              >\n                Date Uploaded\n                {sortField === 'uploadedAt' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('mainCategory')}\n              >\n                Category\n                {sortField === 'mainCategory' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('uploadedBy')}\n              >\n                Uploaded By\n                {sortField === 'uploadedBy' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('qualityScore')}\n              >\n                Quality\n                {sortField === 'qualityScore' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th\n                scope=\"col\"\n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer\"\n                onClick={() => handleSort('status')}\n              >\n                Status\n                {sortField === 'status' && (\n                  <span className=\"ml-1\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th scope=\"col\" className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {sortedDocuments.length > 0 ? (\n              sortedDocuments.map((document) => (\n                <tr key={document.id} className={selectedDocuments.includes(document.id) ? 'bg-blue-50' : undefined}>\n                  <td className=\"px-3 py-4 whitespace-nowrap text-center\">\n                    <input\n                      type=\"checkbox\"\n                      className=\"h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500\"\n                      checked={selectedDocuments.includes(document.id)}\n                      onChange={() => toggleSelectDocument(document.id)}\n                    />\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {document.name}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">\n                      {document.fileType.toUpperCase()}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {formatDate(document.uploadedAt)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900\">\n                      {document.mainCategory || 'N/A'}\n                      {document.category && <span> / {document.category}</span>}\n                    </div>\n                    {document.subCategory && (\n                      <div className=\"text-xs text-gray-500\">\n                        {document.subCategory}\n                        {document.minorCategory && <span> / {document.minorCategory}</span>}\n                      </div>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {document.uploadedBy || 'Unknown'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {document.qualityScore !== undefined ? (\n                      <div className=\"flex items-center\">\n                        <div className=\"w-24 bg-gray-200 rounded-full h-2.5\">\n                          <div\n                            className=\"bg-blue-600 h-2.5 rounded-full\"\n                            style={{ width: `${document.qualityScore}%` }}\n                          ></div>\n                          <div\n                            className=\"bg-gray-300 h-2.5 rounded-r-full\"\n                            style={{\n                              width: `${100 - document.qualityScore}%`,\n                              float: 'right',\n                            }}\n                          ></div>\n                        </div>\n                        <span className=\"text-sm text-gray-700 ml-2\">\n                          {document.qualityScore}%\n                        </span>\n                      </div>\n                    ) : (\n                      <span className=\"text-sm text-gray-500\">N/A</span>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {renderStatusBadge(document.status)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                    <div className=\"flex space-x-2 justify-end\">\n                      <button\n                        onClick={() => onView(document)}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                        title=\"View document details\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                        </svg>\n                      </button>\n                      <button\n                        onClick={() => onEdit(document)}\n                        className=\"text-indigo-600 hover:text-indigo-900\"\n                        title=\"Edit document\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                        </svg>\n                      </button>\n                      <button\n                        onClick={() => handleEditCategories(document)}\n                        className=\"text-purple-600 hover:text-purple-900\"\n                        title=\"Edit categories\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\" />\n                        </svg>\n                      </button>\n                      <button\n                        onClick={() => onReprocess(document)}\n                        className=\"text-yellow-600 hover:text-yellow-900\"\n                        title=\"Reprocess document\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                        </svg>\n                      </button>\n                      <button\n                        onClick={() => onDelete(document)}\n                        className=\"text-red-600 hover:text-red-900\"\n                        title=\"Delete document\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                        </svg>\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))\n            ) : (\n              <tr>\n                <td colSpan={8} className=\"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\">\n                  No documents found\n                </td>\n              </tr>\n            )}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Category Editor Modal */}\n      {documentToEdit && (\n        <DocumentCategoryEditor\n          document={documentToEdit}\n          isOpen={categoryEditorOpen}\n          onClose={handleCategoryEditorClose}\n          onSave={handleCategoryUpdate}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default DocumentsTable;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,OAAOC,sBAAsB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY9D,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,SAAS;EACTC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,WAAW;EACXC;AACF,CAAC,KAAK;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAiB,YAAY,CAAC;EACxE,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAiB,MAAM,CAAC;EAC1E,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAuC;IAACiC,SAAS,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAE,CAAC,CAAC;;EAE1H;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAkB,IAAI,CAAC;;EAE3E;EACA,MAAM,CAACuC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAE3E;EACA,MAAMyC,UAAU,GAAIC,KAAqB,IAAK;IAC5C,IAAI/B,SAAS,KAAK+B,KAAK,EAAE;MACvB5B,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5D,CAAC,MAAM;MACLD,YAAY,CAAC8B,KAAK,CAAC;MACnB5B,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM6B,eAAe,GAAIC,IAAgB,IAAK;IAC5C,IAAIjB,iBAAiB,CAACkB,MAAM,KAAKD,IAAI,CAACC,MAAM,EAAE;MAC5CjB,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,MAAM;MACLA,oBAAoB,CAACgB,IAAI,CAACE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,CAAC,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAID,EAAU,IAAK;IAC3C,IAAIrB,iBAAiB,CAACuB,QAAQ,CAACF,EAAE,CAAC,EAAE;MAClCpB,oBAAoB,CAACD,iBAAiB,CAACwB,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAKJ,EAAE,CAAC,CAAC;IACvE,CAAC,MAAM;MACLpB,oBAAoB,CAAC,CAAC,GAAGD,iBAAiB,EAAEqB,EAAE,CAAC,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMK,oBAAoB,GAAIC,SAAiC,IAAK;IAClE,IAAI3B,iBAAiB,CAACkB,MAAM,KAAK,CAAC,EAAE;IAEpC,MAAMU,YAAY,GAAGlD,SAAS,CAAC8C,MAAM,CAACJ,GAAG,IAAIpB,iBAAiB,CAACuB,QAAQ,CAACH,GAAG,CAACC,EAAE,CAAC,CAAC;IAEhF,IAAIM,SAAS,KAAK,QAAQ,EAAE;MAC1B,IAAIE,MAAM,CAACC,OAAO,CAAC,mCAAmC9B,iBAAiB,CAACkB,MAAM,wBAAwB,CAAC,EAAE;QACvGU,YAAY,CAACG,OAAO,CAACX,GAAG,IAAIvC,QAAQ,CAACuC,GAAG,CAAC,CAAC;QAC1CnB,oBAAoB,CAAC,EAAE,CAAC;MAC1B;IACF,CAAC,MAAM,IAAI0B,SAAS,KAAK,WAAW,EAAE;MACpCC,YAAY,CAACG,OAAO,CAACX,GAAG,IAAItC,WAAW,CAACsC,GAAG,CAAC,CAAC;MAC7CnB,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAM+B,YAAY,GAAGA,CAAA,KAAM;IACzB3C,cAAc,CAAC,EAAE,CAAC;IAClBM,qBAAqB,CAAC,EAAE,CAAC;IACzBJ,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;IACnBM,iBAAiB,CAAC,EAAE,CAAC;IACrBF,aAAa,CAAC,EAAE,CAAC;IACjBQ,kBAAkB,CAAC;MAAEC,SAAS,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAM0B,qBAAqB,GAAGA,CAAClB,KAA8B,EAAEmB,KAAa,KAAK;IAC/E7B,kBAAkB,CAAC8B,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACpB,KAAK,GAAGmB;IACX,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAIC,QAAkB,IAAK;IACnD1B,iBAAiB,CAAC0B,QAAQ,CAAC;IAC3B5B,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM6B,yBAAyB,GAAGA,CAAA,KAAM;IACtC7B,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM4B,oBAAoB,GAAIC,eAAyB,IAAK;IAC1D,IAAIzD,gBAAgB,EAAE;MACpBA,gBAAgB,CAACyD,eAAe,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG/D,SAAS,CAAC8C,MAAM,CAAEJ,GAAG,IAAK;IAClD;IACA,IAAIhC,WAAW,EAAE;MAAA,IAAAsD,eAAA,EAAAC,aAAA,EAAAC,iBAAA;MACf,MAAMC,KAAK,GAAGzD,WAAW,CAAC0D,WAAW,CAAC,CAAC;MACvC,MAAMC,YAAY,GAChB3B,GAAG,CAAC4B,IAAI,CAACF,WAAW,CAAC,CAAC,CAACvB,QAAQ,CAACsB,KAAK,CAAC,MAAAH,eAAA,GACtCtB,GAAG,CAAC6B,UAAU,cAAAP,eAAA,uBAAdA,eAAA,CAAgBI,WAAW,CAAC,CAAC,CAACvB,QAAQ,CAACsB,KAAK,CAAC,OAAAF,aAAA,GAC7CvB,GAAG,CAAC8B,QAAQ,cAAAP,aAAA,uBAAZA,aAAA,CAAcG,WAAW,CAAC,CAAC,CAACvB,QAAQ,CAACsB,KAAK,CAAC,OAAAD,iBAAA,GAC3CxB,GAAG,CAAC+B,YAAY,cAAAP,iBAAA,uBAAhBA,iBAAA,CAAkBE,WAAW,CAAC,CAAC,CAACvB,QAAQ,CAACsB,KAAK,CAAC;MAEjD,IAAI,CAACE,YAAY,EAAE,OAAO,KAAK;IACjC;;IAEA;IACA,IAAIrD,kBAAkB,IAAI0B,GAAG,CAAC+B,YAAY,KAAKzD,kBAAkB,EAAE;MACjE,OAAO,KAAK;IACd;;IAEA;IACA,IACEJ,cAAc,IACd,EACE8B,GAAG,CAAC+B,YAAY,KAAK7D,cAAc,IACnC8B,GAAG,CAAC8B,QAAQ,KAAK5D,cAAc,IAC/B8B,GAAG,CAACgC,WAAW,KAAK9D,cAAc,IAClC8B,GAAG,CAACiC,aAAa,KAAK/D,cAAc,CACrC,EACD;MACA,OAAO,KAAK;IACd;;IAEA;IACA,IAAIE,YAAY,IAAI4B,GAAG,CAACkC,MAAM,KAAK9D,YAAY,EAAE;MAC/C,OAAO,KAAK;IACd;;IAEA;IACA,IAAIM,cAAc,IAAIsB,GAAG,CAACmC,QAAQ,KAAKzD,cAAc,EAAE;MACrD,OAAO,KAAK;IACd;;IAEA;IACA,IAAIF,UAAU,EAAE;MACd,MAAM4D,OAAO,GAAG,IAAIC,IAAI,CAACrC,GAAG,CAACsC,UAAU,CAAC;MACxC,MAAMC,GAAG,GAAG,IAAIF,IAAI,CAAC,CAAC;MAEtB,IAAI7D,UAAU,KAAK,KAAK,EAAE;QACxB;QACA,MAAMgE,SAAS,GAAG,IAAIH,IAAI,CAACE,GAAG,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC/D,IAAIL,OAAO,GAAGI,SAAS,EAAE,OAAO,KAAK;MACvC,CAAC,MAAM,IAAIhE,UAAU,KAAK,MAAM,EAAE;QAChC;QACA,MAAMkE,QAAQ,GAAG,IAAIL,IAAI,CAACE,GAAG,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAClE,IAAIL,OAAO,GAAGM,QAAQ,EAAE,OAAO,KAAK;MACtC,CAAC,MAAM,IAAIlE,UAAU,KAAK,OAAO,EAAE;QACjC;QACA,MAAMmE,SAAS,GAAG,IAAIN,IAAI,CAACE,GAAG,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QACpE,IAAIL,OAAO,GAAGO,SAAS,EAAE,OAAO,KAAK;MACvC;IACF;;IAEA;IACA,IAAI3D,eAAe,CAACE,SAAS,IAAIF,eAAe,CAACG,OAAO,EAAE;MACxD,MAAMiD,OAAO,GAAG,IAAIC,IAAI,CAACrC,GAAG,CAACsC,UAAU,CAAC;MAExC,IAAItD,eAAe,CAACE,SAAS,EAAE;QAC7B,MAAMA,SAAS,GAAG,IAAImD,IAAI,CAACrD,eAAe,CAACE,SAAS,CAAC;QACrD;QACAA,SAAS,CAAC0D,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC9B,IAAIR,OAAO,GAAGlD,SAAS,EAAE,OAAO,KAAK;MACvC;MAEA,IAAIF,eAAe,CAACG,OAAO,EAAE;QAC3B,MAAMA,OAAO,GAAG,IAAIkD,IAAI,CAACrD,eAAe,CAACG,OAAO,CAAC;QACjD;QACAA,OAAO,CAACyD,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;QACjC,IAAIR,OAAO,GAAGjD,OAAO,EAAE,OAAO,KAAK;MACrC;IACF;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAM0D,eAAe,GAAG,CAAC,GAAGxB,iBAAiB,CAAC,CAACyB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAAA,IAAAC,YAAA,EAAAC,YAAA;IAC5D;IACA,MAAMC,MAAM,IAAAF,YAAA,GAAGF,CAAC,CAACnF,SAAS,CAAC,cAAAqF,YAAA,cAAAA,YAAA,GAAI,EAAE;IACjC,MAAMG,MAAM,IAAAF,YAAA,GAAGF,CAAC,CAACpF,SAAS,CAAC,cAAAsF,YAAA,cAAAA,YAAA,GAAI,EAAE;IAEjC,IAAIC,MAAM,GAAGC,MAAM,EAAE;MACnB,OAAOtF,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IACzC;IACA,IAAIqF,MAAM,GAAGC,MAAM,EAAE;MACnB,OAAOtF,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IACzC;IACA,OAAO,CAAC;EACV,CAAC,CAAC;;EAEF;EACA,MAAMuF,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAG,IAAIlB,IAAI,CAACiB,OAAO,CAAC;IAC9B,OAAO,IAAIE,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MACtCC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,CAAC;EACjB,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAI9B,MAA0B,IAAK;IACxD,IAAI+B,OAAO;IACX,QAAQ/B,MAAM;MACZ,KAAK,WAAW;QACd+B,OAAO,GAAG,6BAA6B;QACvC;MACF,KAAK,SAAS;QACZA,OAAO,GAAG,+BAA+B;QACzC;MACF,KAAK,eAAe;QAClBA,OAAO,GAAG,yBAAyB;QACnC;MACF;QACEA,OAAO,GAAG,2BAA2B;IACzC;IAEA,oBACE7G,OAAA;MAAM8G,SAAS,EAAE,GAAGD,OAAO,6CAA8C;MAAAE,QAAA,EACtEjC;IAAM;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEX,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACrH,SAAS,CAACyC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC+B,YAAY,CAAC,CAAC3B,MAAM,CAACwE,OAAO,CAAC,CAAC,CAAC;EACxG,MAAMC,gBAAgB,GAAGJ,KAAK,CAACC,IAAI,CACjC,IAAIC,GAAG,CACLrH,SAAS,CAACwH,OAAO,CAAE9E,GAAG,IAAK,CACzBA,GAAG,CAAC8B,QAAQ,EACZ9B,GAAG,CAACgC,WAAW,EACfhC,GAAG,CAACiC,aAAa,CAClB,CAAC,CAAC7B,MAAM,CAACwE,OAAO,CACnB,CACF,CAAC;EACD,MAAMG,eAAe,GAAGN,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACrH,SAAS,CAACyC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACmC,QAAQ,CAAC,CAAC/B,MAAM,CAACwE,OAAO,CAAC,CAAC,CAAC;EAE/F,oBACExH,OAAA;IAAK8G,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAC5D/G,OAAA;MAAK8G,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3C/G,OAAA;QAAK8G,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD/G,OAAA;UAAI,cAAW,iBAAiB;UAAC8G,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAExFnH,OAAA;UAAK8G,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/G,OAAA;YACE4H,OAAO,EAAEA,CAAA,KAAMjG,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5CoF,SAAS,EAAC,4FAA4F;YAAAC,QAAA,gBAEtG/G,OAAA;cAAA+G,QAAA,EAAOrF,WAAW,GAAG,cAAc,GAAG;YAAc;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5DnH,OAAA;cAAK6H,KAAK,EAAC,4BAA4B;cAACf,SAAS,EAAC,cAAc;cAACgB,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAjB,QAAA,eACpH/G,OAAA;gBAAMiI,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyJ;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9N,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAER3F,iBAAiB,CAACkB,MAAM,GAAG,CAAC,iBAC3B1C,OAAA;YAAK8G,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/G,OAAA;cACE4H,OAAO,EAAEA,CAAA,KAAM1E,oBAAoB,CAAC,WAAW,CAAE;cACjD4D,SAAS,EAAC,gFAAgF;cAAAC,QAAA,GAC3F,aACY,EAACvF,iBAAiB,CAACkB,MAAM,EAAC,GACvC;YAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnH,OAAA;cACE4H,OAAO,EAAEA,CAAA,KAAM1E,oBAAoB,CAAC,QAAQ,CAAE;cAC9C4D,SAAS,EAAC,uEAAuE;cAAAC,QAAA,GAClF,UACS,EAACvF,iBAAiB,CAACkB,MAAM,EAAC,GACpC;YAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnH,OAAA;QAAK8G,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD/G,OAAA;UAAK8G,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC/G,OAAA;YACEqI,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oDAAoD;YAChE5E,KAAK,EAAE9C,WAAY;YACnB2H,QAAQ,EAAGC,CAAC,IAAK3H,cAAc,CAAC2H,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;YAChDoD,SAAS,EAAC;UAAkG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7G;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnH,OAAA;UAAK8G,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC/G,OAAA;YACE0D,KAAK,EAAE1C,YAAa;YACpBuH,QAAQ,EAAGC,CAAC,IAAKvH,eAAe,CAACuH,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;YACjDoD,SAAS,EAAC,kGAAkG;YAAAC,QAAA,gBAE5G/G,OAAA;cAAQ0D,KAAK,EAAC,EAAE;cAAAqD,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCnH,OAAA;cAAQ0D,KAAK,EAAC,WAAW;cAAAqD,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5CnH,OAAA;cAAQ0D,KAAK,EAAC,SAAS;cAAAqD,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxCnH,OAAA;cAAQ0D,KAAK,EAAC,YAAY;cAAAqD,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9CnH,OAAA;cAAQ0D,KAAK,EAAC,eAAe;cAAAqD,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpDnH,OAAA;cAAQ0D,KAAK,EAAC,QAAQ;cAAAqD,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzF,WAAW,iBACV1B,OAAA;QAAK8G,SAAS,EAAC,sCAAsC;QAAAC,QAAA,eACnD/G,OAAA;UAAK8G,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC/G,OAAA;YAAK8G,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C/G,OAAA;cAAK8G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/G,OAAA;gBAAO8G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFnH,OAAA;gBACE0D,KAAK,EAAExC,kBAAmB;gBAC1BqH,QAAQ,EAAGC,CAAC,IAAKrH,qBAAqB,CAACqH,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;gBACvDoD,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G/G,OAAA;kBAAQ0D,KAAK,EAAC,EAAE;kBAAAqD,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC5CC,oBAAoB,CAACzE,GAAG,CAAE+B,QAAQ,iBACjC1E,OAAA;kBAAuB0D,KAAK,EAAEgB,QAAS;kBAAAqC,QAAA,EACpCrC;gBAAQ,GADEA,QAAQ;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnH,OAAA;cAAK8G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/G,OAAA;gBAAO8G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFnH,OAAA;gBACE0D,KAAK,EAAE5C,cAAe;gBACtByH,QAAQ,EAAGC,CAAC,IAAKzH,iBAAiB,CAACyH,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;gBACnDoD,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G/G,OAAA;kBAAQ0D,KAAK,EAAC,EAAE;kBAAAqD,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvCM,gBAAgB,CAAC9E,GAAG,CAAE+B,QAAQ,iBAC7B1E,OAAA;kBAAuB0D,KAAK,EAAEgB,QAAS;kBAAAqC,QAAA,EACpCrC;gBAAQ,GADEA,QAAQ;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnH,OAAA;cAAK8G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/G,OAAA;gBAAO8G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjFnH,OAAA;gBACE0D,KAAK,EAAEpC,cAAe;gBACtBiH,QAAQ,EAAGC,CAAC,IAAKjH,iBAAiB,CAACiH,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;gBACnDoD,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G/G,OAAA;kBAAQ0D,KAAK,EAAC,EAAE;kBAAAqD,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvCQ,eAAe,CAAChF,GAAG,CAAEoC,QAAQ,iBAC5B/E,OAAA;kBAAuB0D,KAAK,EAAEqB,QAAS;kBAAAgC,QAAA,EACpChC,QAAQ,CAAC2D,WAAW,CAAC;gBAAC,GADZ3D,QAAQ;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnH,OAAA;YAAK8G,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C/G,OAAA;cAAK8G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/G,OAAA;gBAAO8G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFnH,OAAA;gBACE0D,KAAK,EAAEtC,UAAW;gBAClBmH,QAAQ,EAAGC,CAAC,IAAKnH,aAAa,CAACmH,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;gBAC/CoD,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G/G,OAAA;kBAAQ0D,KAAK,EAAC,EAAE;kBAAAqD,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCnH,OAAA;kBAAQ0D,KAAK,EAAC,KAAK;kBAAAqD,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnH,OAAA;kBAAQ0D,KAAK,EAAC,MAAM;kBAAAqD,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCnH,OAAA;kBAAQ0D,KAAK,EAAC,OAAO;kBAAAqD,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnH,OAAA;cAAK8G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/G,OAAA;gBAAO8G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFnH,OAAA;gBAAK8G,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B/G,OAAA;kBAAK8G,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpB/G,OAAA;oBACEqI,IAAI,EAAC,MAAM;oBACXxF,EAAE,EAAC,YAAY;oBACfa,KAAK,EAAE9B,eAAe,CAACE,SAAU;oBACjCyG,QAAQ,EAAGC,CAAC,IAAK/E,qBAAqB,CAAC,WAAW,EAAE+E,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;oBACpEoD,SAAS,EAAC,kGAAkG;oBAC5GwB,WAAW,EAAC;kBAAY;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNnH,OAAA;kBAAK8G,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpB/G,OAAA;oBACEqI,IAAI,EAAC,MAAM;oBACXxF,EAAE,EAAC,UAAU;oBACba,KAAK,EAAE9B,eAAe,CAACG,OAAQ;oBAC/BwG,QAAQ,EAAGC,CAAC,IAAK/E,qBAAqB,CAAC,SAAS,EAAE+E,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;oBAClEoD,SAAS,EAAC,kGAAkG;oBAC5GwB,WAAW,EAAC;kBAAU;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnH,OAAA;cAAK8G,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACtC/G,OAAA;gBACE4H,OAAO,EAAEpE,YAAa;gBACtBsD,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAC5F;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDnH,OAAA;QAAK8G,SAAS,EAAC,4BAA4B;QAAAC,QAAA,GAAC,UAClC,EAAC9C,iBAAiB,CAACvB,MAAM,EAAC,MAAI,EAACxC,SAAS,CAACwC,MAAM,EAAC,YAC1D;MAAA;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnH,OAAA;MAAK8G,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9B/G,OAAA;QAAO8G,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBACpD/G,OAAA;UAAO8G,SAAS,EAAC,YAAY;UAAAC,QAAA,eAC3B/G,OAAA;YAAA+G,QAAA,gBACE/G,OAAA;cAAI2I,KAAK,EAAC,KAAK;cAAC7B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAC/C/G,OAAA;gBACEqI,IAAI,EAAC,UAAU;gBACfvB,SAAS,EAAC,mEAAmE;gBAC7E8B,OAAO,EAAEpH,iBAAiB,CAACkB,MAAM,GAAG,CAAC,IAAIlB,iBAAiB,CAACkB,MAAM,KAAKuB,iBAAiB,CAACvB,MAAO;gBAC/F6F,QAAQ,EAAEA,CAAA,KAAM/F,eAAe,CAACyB,iBAAiB;cAAE;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACLnH,OAAA;cACE2I,KAAK,EAAC,KAAK;cACX7B,SAAS,EAAC,+FAA+F;cACzGc,OAAO,EAAEA,CAAA,KAAMtF,UAAU,CAAC,MAAM,CAAE;cAAAyE,QAAA,GACnC,MAEC,EAACvG,SAAS,KAAK,MAAM,iBACnBR,OAAA;gBAAM8G,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACnBrG,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLnH,OAAA;cACE2I,KAAK,EAAC,KAAK;cACX7B,SAAS,EAAC,+FAA+F;cACzGc,OAAO,EAAEA,CAAA,KAAMtF,UAAU,CAAC,YAAY,CAAE;cAAAyE,QAAA,GACzC,eAEC,EAACvG,SAAS,KAAK,YAAY,iBACzBR,OAAA;gBAAM8G,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACnBrG,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLnH,OAAA;cACE2I,KAAK,EAAC,KAAK;cACX7B,SAAS,EAAC,+FAA+F;cACzGc,OAAO,EAAEA,CAAA,KAAMtF,UAAU,CAAC,cAAc,CAAE;cAAAyE,QAAA,GAC3C,UAEC,EAACvG,SAAS,KAAK,cAAc,iBAC3BR,OAAA;gBAAM8G,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACnBrG,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLnH,OAAA;cACE2I,KAAK,EAAC,KAAK;cACX7B,SAAS,EAAC,+FAA+F;cACzGc,OAAO,EAAEA,CAAA,KAAMtF,UAAU,CAAC,YAAY,CAAE;cAAAyE,QAAA,GACzC,aAEC,EAACvG,SAAS,KAAK,YAAY,iBACzBR,OAAA;gBAAM8G,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACnBrG,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLnH,OAAA;cACE2I,KAAK,EAAC,KAAK;cACX7B,SAAS,EAAC,+FAA+F;cACzGc,OAAO,EAAEA,CAAA,KAAMtF,UAAU,CAAC,cAAc,CAAE;cAAAyE,QAAA,GAC3C,SAEC,EAACvG,SAAS,KAAK,cAAc,iBAC3BR,OAAA;gBAAM8G,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACnBrG,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLnH,OAAA;cACE2I,KAAK,EAAC,KAAK;cACX7B,SAAS,EAAC,+FAA+F;cACzGc,OAAO,EAAEA,CAAA,KAAMtF,UAAU,CAAC,QAAQ,CAAE;cAAAyE,QAAA,GACrC,QAEC,EAACvG,SAAS,KAAK,QAAQ,iBACrBR,OAAA;gBAAM8G,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACnBrG,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLnH,OAAA;cAAI2I,KAAK,EAAC,KAAK;cAAC7B,SAAS,EAAC,iFAAiF;cAAAC,QAAA,EAAC;YAE5G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRnH,OAAA;UAAO8G,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EACjDtB,eAAe,CAAC/C,MAAM,GAAG,CAAC,GACzB+C,eAAe,CAAC9C,GAAG,CAAEkB,QAAQ,iBAC3B7D,OAAA;YAAsB8G,SAAS,EAAEtF,iBAAiB,CAACuB,QAAQ,CAACc,QAAQ,CAAChB,EAAE,CAAC,GAAG,YAAY,GAAGgG,SAAU;YAAA9B,QAAA,gBAClG/G,OAAA;cAAI8G,SAAS,EAAC,yCAAyC;cAAAC,QAAA,eACrD/G,OAAA;gBACEqI,IAAI,EAAC,UAAU;gBACfvB,SAAS,EAAC,mEAAmE;gBAC7E8B,OAAO,EAAEpH,iBAAiB,CAACuB,QAAQ,CAACc,QAAQ,CAAChB,EAAE,CAAE;gBACjD0F,QAAQ,EAAEA,CAAA,KAAMzF,oBAAoB,CAACe,QAAQ,CAAChB,EAAE;cAAE;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACLnH,OAAA;cAAI8G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBACzC/G,OAAA;gBAAK8G,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/ClD,QAAQ,CAACW;cAAI;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACNnH,OAAA;gBAAK8G,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnClD,QAAQ,CAACkB,QAAQ,CAAC2D,WAAW,CAAC;cAAC;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLnH,OAAA;cAAI8G,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9Dd,UAAU,CAACpC,QAAQ,CAACqB,UAAU;YAAC;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACLnH,OAAA;cAAI8G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBACzC/G,OAAA;gBAAK8G,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACnClD,QAAQ,CAACc,YAAY,IAAI,KAAK,EAC9Bd,QAAQ,CAACa,QAAQ,iBAAI1E,OAAA;kBAAA+G,QAAA,GAAM,KAAG,EAAClD,QAAQ,CAACa,QAAQ;gBAAA;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,EACLtD,QAAQ,CAACe,WAAW,iBACnB5E,OAAA;gBAAK8G,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACnClD,QAAQ,CAACe,WAAW,EACpBf,QAAQ,CAACgB,aAAa,iBAAI7E,OAAA;kBAAA+G,QAAA,GAAM,KAAG,EAAClD,QAAQ,CAACgB,aAAa;gBAAA;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLnH,OAAA;cAAI8G,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DlD,QAAQ,CAACY,UAAU,IAAI;YAAS;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACLnH,OAAA;cAAI8G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EACxClD,QAAQ,CAACiF,YAAY,KAAKD,SAAS,gBAClC7I,OAAA;gBAAK8G,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/G,OAAA;kBAAK8G,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClD/G,OAAA;oBACE8G,SAAS,EAAC,gCAAgC;oBAC1CiC,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAGnF,QAAQ,CAACiF,YAAY;oBAAI;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACPnH,OAAA;oBACE8G,SAAS,EAAC,kCAAkC;oBAC5CiC,KAAK,EAAE;sBACLC,KAAK,EAAE,GAAG,GAAG,GAAGnF,QAAQ,CAACiF,YAAY,GAAG;sBACxCG,KAAK,EAAE;oBACT;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNnH,OAAA;kBAAM8G,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzClD,QAAQ,CAACiF,YAAY,EAAC,GACzB;gBAAA;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,gBAENnH,OAAA;gBAAM8G,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAClD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLnH,OAAA;cAAI8G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EACxCH,iBAAiB,CAAC/C,QAAQ,CAACiB,MAAM;YAAC;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACLnH,OAAA;cAAI8G,SAAS,EAAC,4DAA4D;cAAAC,QAAA,eACxE/G,OAAA;gBAAK8G,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC/G,OAAA;kBACE4H,OAAO,EAAEA,CAAA,KAAMzH,MAAM,CAAC0D,QAAQ,CAAE;kBAChCiD,SAAS,EAAC,mCAAmC;kBAC7CoC,KAAK,EAAC,uBAAuB;kBAAAnC,QAAA,eAE7B/G,OAAA;oBAAK6H,KAAK,EAAC,4BAA4B;oBAACf,SAAS,EAAC,SAAS;oBAACgB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAjB,QAAA,gBAC/G/G,OAAA;sBAAMiI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkC;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1GnH,OAAA;sBAAMiI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAyH;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9L;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACTnH,OAAA;kBACE4H,OAAO,EAAEA,CAAA,KAAMxH,MAAM,CAACyD,QAAQ,CAAE;kBAChCiD,SAAS,EAAC,uCAAuC;kBACjDoC,KAAK,EAAC,eAAe;kBAAAnC,QAAA,eAErB/G,OAAA;oBAAK6H,KAAK,EAAC,4BAA4B;oBAACf,SAAS,EAAC,SAAS;oBAACgB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAjB,QAAA,eAC/G/G,OAAA;sBAAMiI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAwH;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7L;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACTnH,OAAA;kBACE4H,OAAO,EAAEA,CAAA,KAAMhE,oBAAoB,CAACC,QAAQ,CAAE;kBAC9CiD,SAAS,EAAC,uCAAuC;kBACjDoC,KAAK,EAAC,iBAAiB;kBAAAnC,QAAA,eAEvB/G,OAAA;oBAAK6H,KAAK,EAAC,4BAA4B;oBAACf,SAAS,EAAC,SAAS;oBAACgB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAjB,QAAA,eAC/G/G,OAAA;sBAAMiI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA8H;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACTnH,OAAA;kBACE4H,OAAO,EAAEA,CAAA,KAAMtH,WAAW,CAACuD,QAAQ,CAAE;kBACrCiD,SAAS,EAAC,uCAAuC;kBACjDoC,KAAK,EAAC,oBAAoB;kBAAAnC,QAAA,eAE1B/G,OAAA;oBAAK6H,KAAK,EAAC,4BAA4B;oBAACf,SAAS,EAAC,SAAS;oBAACgB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAjB,QAAA,eAC/G/G,OAAA;sBAAMiI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA6G;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACTnH,OAAA;kBACE4H,OAAO,EAAEA,CAAA,KAAMvH,QAAQ,CAACwD,QAAQ,CAAE;kBAClCiD,SAAS,EAAC,iCAAiC;kBAC3CoC,KAAK,EAAC,iBAAiB;kBAAAnC,QAAA,eAEvB/G,OAAA;oBAAK6H,KAAK,EAAC,4BAA4B;oBAACf,SAAS,EAAC,SAAS;oBAACgB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAjB,QAAA,eAC/G/G,OAAA;sBAAMiI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA8H;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GA/GEtD,QAAQ,CAAChB,EAAE;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgHhB,CACL,CAAC,gBAEFnH,OAAA;YAAA+G,QAAA,eACE/G,OAAA;cAAImJ,OAAO,EAAE,CAAE;cAACrC,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLjF,cAAc,iBACblC,OAAA,CAACF,sBAAsB;MACrB+D,QAAQ,EAAE3B,cAAe;MACzBkH,MAAM,EAAEpH,kBAAmB;MAC3BqH,OAAO,EAAEvF,yBAA0B;MACnCwF,MAAM,EAAEvF;IAAqB;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAED,eAAelH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}