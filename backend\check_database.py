"""
<PERSON><PERSON><PERSON> to check the database content and verify if documents and websites exist.
"""
import os
import logging
import json
from typing import List, Dict, Any
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def check_database_content():
    """Check if documents and websites exist in the database."""
    logger.info("Checking database content...")

    # Check documents table
    doc_query = "SELECT COUNT(*) FROM documents"
    doc_result = supabase.execute_query(doc_query)

    if isinstance(doc_result, dict) and "error" in doc_result:
        logger.error(f"Error checking documents: {doc_result['error']}")
    else:
        logger.info(f"Document count result: {doc_result}")
        # Try to extract count - handle different result formats
        doc_count = 0
        if doc_result and isinstance(doc_result, list) and len(doc_result) > 0:
            if "count" in doc_result[0]:
                doc_count = doc_result[0]["count"]
            elif "COUNT" in doc_result[0]:
                doc_count = doc_result[0]["COUNT"]
            else:
                # Try to get the first value regardless of key
                doc_count = list(doc_result[0].values())[0] if doc_result[0] else 0
        logger.info(f"Found {doc_count} documents in the database")

    # Helper function to extract count from result
    def extract_count(result):
        if not result or not isinstance(result, list) or len(result) == 0:
            return 0

        if "count" in result[0]:
            count_val = result[0]["count"]
        elif "COUNT" in result[0]:
            count_val = result[0]["COUNT"]
        else:
            # Try to get the first value regardless of key
            count_val = list(result[0].values())[0] if result[0] else 0

        # Convert to int if it's a string
        if isinstance(count_val, str):
            try:
                return int(count_val)
            except ValueError:
                return 0
        return count_val

    # Check document_chunks table
    chunk_query = "SELECT COUNT(*) FROM document_chunks"
    chunk_result = supabase.execute_query(chunk_query)

    if isinstance(chunk_result, dict) and "error" in chunk_result:
        logger.error(f"Error checking document chunks: {chunk_result['error']}")
    else:
        logger.info(f"Document chunks result: {chunk_result}")
        chunk_count = extract_count(chunk_result)
        logger.info(f"Found {chunk_count} document chunks in the database")

    # Check document_chunks with embeddings
    embed_query = "SELECT COUNT(*) FROM document_chunks WHERE embedding IS NOT NULL"
    embed_result = supabase.execute_query(embed_query)

    if isinstance(embed_result, dict) and "error" in embed_result:
        logger.error(f"Error checking document chunks with embeddings: {embed_result['error']}")
    else:
        logger.info(f"Document chunks with embeddings result: {embed_result}")
        embed_count = extract_count(embed_result)
        logger.info(f"Found {embed_count} document chunks with embeddings in the database")

    # Check websites table
    web_query = "SELECT COUNT(*) FROM websites"
    web_result = supabase.execute_query(web_query)

    if isinstance(web_result, dict) and "error" in web_result:
        logger.error(f"Error checking websites: {web_result['error']}")
    else:
        logger.info(f"Websites result: {web_result}")
        web_count = extract_count(web_result)
        logger.info(f"Found {web_count} websites in the database")

    # Check website_chunks table
    web_chunk_query = "SELECT COUNT(*) FROM website_chunks"
    web_chunk_result = supabase.execute_query(web_chunk_query)

    if isinstance(web_chunk_result, dict) and "error" in web_chunk_result:
        logger.error(f"Error checking website chunks: {web_chunk_result['error']}")
    else:
        logger.info(f"Website chunks result: {web_chunk_result}")
        web_chunk_count = extract_count(web_chunk_result)
        logger.info(f"Found {web_chunk_count} website chunks in the database")

    # Check website_chunks with embeddings
    web_embed_query = "SELECT COUNT(*) FROM website_chunks WHERE embedding IS NOT NULL"
    web_embed_result = supabase.execute_query(web_embed_query)

    if isinstance(web_embed_result, dict) and "error" in web_embed_result:
        logger.error(f"Error checking website chunks with embeddings: {web_embed_result['error']}")
    else:
        logger.info(f"Website chunks with embeddings result: {web_embed_result}")
        web_embed_count = extract_count(web_embed_result)
        logger.info(f"Found {web_embed_count} website chunks with embeddings in the database")

    # Check for specific content related to test queries
    test_queries = [
        "ACP",
        "FSDS",
        "Rapid Response app",
        "VASP"
    ]

    for query in test_queries:
        logger.info(f"\nChecking for content related to '{query}'...")

        # Check documents
        doc_content_query = f"""
        SELECT COUNT(*) FROM document_chunks
        WHERE text ILIKE '%{query}%'
        """
        doc_content_result = supabase.execute_query(doc_content_query)

        if isinstance(doc_content_result, dict) and "error" in doc_content_result:
            logger.error(f"Error checking document content for '{query}': {doc_content_result['error']}")
        else:
            logger.info(f"Document content result for '{query}': {doc_content_result}")
            doc_content_count = extract_count(doc_content_result)
            logger.info(f"Found {doc_content_count} document chunks containing '{query}'")

            # If we found content, show a sample
            if doc_content_count > 0:
                sample_query = f"""
                SELECT id, document_id, text FROM document_chunks
                WHERE text ILIKE '%{query}%'
                LIMIT 1
                """
                sample_result = supabase.execute_query(sample_query)
                if not isinstance(sample_result, dict) and sample_result and len(sample_result) > 0:
                    logger.info(f"Sample document chunk containing '{query}':")
                    logger.info(f"ID: {sample_result[0].get('id')}")
                    logger.info(f"Document ID: {sample_result[0].get('document_id')}")
                    logger.info(f"Text: {sample_result[0].get('text')[:200]}...")

        # Check websites
        web_content_query = f"""
        SELECT COUNT(*) FROM website_chunks
        WHERE text ILIKE '%{query}%'
        """
        web_content_result = supabase.execute_query(web_content_query)

        if isinstance(web_content_result, dict) and "error" in web_content_result:
            logger.error(f"Error checking website content for '{query}': {web_content_result['error']}")
        else:
            logger.info(f"Website content result for '{query}': {web_content_result}")
            web_content_count = extract_count(web_content_result)
            logger.info(f"Found {web_content_count} website chunks containing '{query}'")

            # If we found content, show a sample
            if web_content_count > 0:
                sample_query = f"""
                SELECT id, website_id, text FROM website_chunks
                WHERE text ILIKE '%{query}%'
                LIMIT 1
                """
                sample_result = supabase.execute_query(sample_query)
                if not isinstance(sample_result, dict) and sample_result and len(sample_result) > 0:
                    logger.info(f"Sample website chunk containing '{query}':")
                    logger.info(f"ID: {sample_result[0].get('id')}")
                    logger.info(f"Website ID: {sample_result[0].get('website_id')}")
                    logger.info(f"Text: {sample_result[0].get('text')[:200]}...")

if __name__ == "__main__":
    check_database_content()
