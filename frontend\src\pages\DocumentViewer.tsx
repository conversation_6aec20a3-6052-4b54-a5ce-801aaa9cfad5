import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import SimplePDFViewer from '../components/documents/SimplePDFViewer';
import { API_URL, getDocumentContent } from '../services/api';

const DocumentViewer: React.FC = () => {
  const location = useLocation();
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [fileName, setFileName] = useState<string>('');
  const [fileType, setFileType] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [documentContent, setDocumentContent] = useState<string>('');
  const [showExtractedContent, setShowExtractedContent] = useState<boolean>(false);

  useEffect(() => {
    // Parse URL parameters
    const params = new URLSearchParams(location.search);
    const file = params.get('file');
    // Page parameter is passed to SimplePDFViewer via URL

    if (file) {
      console.log(`DocumentViewer: Loading file ${file}`);
      setFileName(file);

      // Determine file type from extension
      const extension = file.split('.').pop()?.toLowerCase() || '';
      console.log(`DocumentViewer: Detected file extension: ${extension}`);
      setFileType(extension);

      // For PDF files, try to load as PDF first
      if (extension === 'pdf') {
        console.log('DocumentViewer: PDF file detected, setting up PDF viewer');
        const url = `${API_URL}/api/documents/view/${encodeURIComponent(file)}`;
        setPdfUrl(url);
        setShowExtractedContent(false);

        // Also fetch extracted content in background for PDF toggle
        fetchDocumentContent(file);
      } else {
        console.log('DocumentViewer: Non-PDF file detected, showing extracted content');
        // For non-PDF files, show extracted content instead
        setShowExtractedContent(true);
        fetchDocumentContent(file);
      }

      // Page number is handled by the SimplePDFViewer component

      setLoading(false);
    } else {
      setError('No file specified in the URL');
      setLoading(false);
    }
  }, [location]);

  const fetchDocumentContent = async (filename: string) => {
    try {
      setLoading(true);
      console.log(`Fetching content for filename: ${filename}`);

      // Try to find the document by filename and get its content
      const response = await fetch(`${API_URL}/api/documents`);
      if (response.ok) {
        const documents = await response.json();
        console.log(`Found ${documents.length} documents in database`);

        // Try multiple matching strategies to find the document
        let document = null;

        // Strategy 1: Exact name match
        document = documents.find((doc: any) =>
          doc.name === filename
        );

        // Strategy 2: Name contains filename
        if (!document) {
          document = documents.find((doc: any) =>
            doc.name && doc.name.includes(filename.replace(/\.[^/.]+$/, ""))
          );
        }

        // Strategy 3: Filename contains document name
        if (!document) {
          document = documents.find((doc: any) =>
            doc.name && filename.includes(doc.name.replace(/\.[^/.]+$/, ""))
          );
        }

        // Strategy 4: Check file path
        if (!document) {
          document = documents.find((doc: any) =>
            doc.filePath && doc.filePath.includes(filename)
          );
        }

        // Strategy 5: Use the first document if URL params match any pattern
        if (!document && documents.length > 0) {
          console.log('No exact match found, checking if filename matches any document pattern...');
          // Check if the filename pattern matches any document
          for (const doc of documents) {
            console.log(`Checking document: ${doc.name} against ${filename}`);
            if (doc.name && (
              doc.name.toLowerCase().includes(filename.toLowerCase().split('.')[0]) ||
              filename.toLowerCase().includes(doc.name.toLowerCase().split('.')[0])
            )) {
              document = doc;
              break;
            }
          }
        }

        if (document) {
          console.log(`Found matching document: ${document.name} (ID: ${document.id})`);
          try {
            const content = await getDocumentContent(document.id);
            const extractedContent = content.content || content.extractedContent || 'No content available';
            setDocumentContent(extractedContent);

            // Update filename and file type based on found document
            setFileName(document.name);
            const detectedType = document.fileType || document.name.split('.').pop()?.toLowerCase() || '';
            setFileType(detectedType);
          } catch (contentError) {
            console.error('Error getting document content:', contentError);
            setDocumentContent(`Error loading content: ${contentError instanceof Error ? contentError.message : 'Unknown error'}`);
          }
        } else {
          console.log('No matching document found, showing file not found message');
          setDocumentContent(`Document "${filename}" not found in the database.\n\nAvailable documents:\n${documents.map((doc: any) => `- ${doc.name}`).join('\n')}`);
        }
      } else {
        console.error('Failed to fetch documents list:', response.status);
        setDocumentContent(`Failed to fetch documents list from server (${response.status})`);
      }
    } catch (error) {
      console.error('Error fetching document content:', error);
      setDocumentContent(`Error loading content for "${filename}": ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const toggleContentView = () => {
    if (fileType === 'pdf') {
      setShowExtractedContent(!showExtractedContent);
      if (!showExtractedContent && !documentContent) {
        fetchDocumentContent(fileName);
      }
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center p-8">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Document Viewer Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.history.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading document...</p>
        </div>
      </div>
    );
  }

  const getFileTypeInfo = (type: string) => {
    switch (type) {
      case 'pdf':
        return { icon: '📄', name: 'PDF Document' };
      case 'docx':
      case 'doc':
        return { icon: '📝', name: 'Word Document' };
      case 'xlsx':
      case 'xls':
        return { icon: '📊', name: 'Excel Spreadsheet' };
      case 'pptx':
      case 'ppt':
        return { icon: '📋', name: 'PowerPoint Presentation' };
      case 'txt':
        return { icon: '📄', name: 'Text Document' };
      default:
        return { icon: '📁', name: 'Document' };
    }
  };

  const fileInfo = getFileTypeInfo(fileType);

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-2xl mr-3">{fileInfo.icon}</span>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">{fileName}</h1>
              <p className="text-sm text-gray-500">{fileInfo.name}</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {fileType === 'pdf' && (
              <button
                onClick={toggleContentView}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
              >
                {showExtractedContent ? 'Show PDF' : 'Show Text'}
              </button>
            )}
            <button
              onClick={() => window.open(pdfUrl, '_blank')}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
            >
              Open in New Tab
            </button>
            <a
              href={pdfUrl}
              download={fileName}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
            >
              Download
            </a>
            <button
              onClick={() => window.close()}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50"
            >
              Close
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {fileType === 'pdf' && !showExtractedContent && pdfUrl ? (
          <div className="h-full">
            <SimplePDFViewer
              url={pdfUrl}
              fileName={fileName}
            />
          </div>
        ) : (
          <div className="h-full p-4 overflow-auto">
            <div className="max-w-4xl mx-auto">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 text-gray-800">
                  Extracted Content
                </h3>
                <div className="prose max-w-none">
                  <pre className="whitespace-pre-wrap text-sm bg-white p-4 rounded border overflow-auto max-h-[70vh]">
                    {documentContent || 'Loading content...'}
                  </pre>
                </div>
                {fileType !== 'pdf' && (
                  <div className="mt-4 p-3 bg-blue-50 rounded text-sm text-blue-700">
                    <p><strong>Note:</strong> This is the extracted text content from the {fileInfo.name}.
                    The original file formatting may not be preserved.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentViewer;