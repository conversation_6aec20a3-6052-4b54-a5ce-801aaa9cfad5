"""
<PERSON><PERSON><PERSON> to check document chunks in the database.
"""
import os
import logging
import json
from dotenv import load_dotenv
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def check_document_chunks():
    """Check document chunks in the database."""
    logger.info("Checking document chunks in the database...")
    
    # Query to get all document chunks
    chunks_query = """
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    LIMIT 10
    """
    
    result = supabase.execute_query(chunks_query)
    
    if isinstance(result, dict) and "error" in result:
        logger.error(f"Error getting document chunks: {result['error']}")
    else:
        logger.info(f"Found {len(result)} document chunks")
        
        # Check each chunk
        for i, chunk in enumerate(result):
            chunk_id = chunk.get("id")
            document_id = chunk.get("document_id")
            chunk_index = chunk.get("chunk_index")
            page_number = chunk.get("page_number")
            text = chunk.get("text")
            filename = chunk.get("filename")
            url = chunk.get("url")
            
            logger.info(f"Chunk {i+1}:")
            logger.info(f"  ID: {chunk_id}")
            logger.info(f"  Document ID: {document_id}")
            logger.info(f"  Chunk Index: {chunk_index}")
            logger.info(f"  Page Number: {page_number}")
            logger.info(f"  Filename: {filename}")
            logger.info(f"  URL: {url}")
            logger.info(f"  Text: {text[:100]}..." if text else "  Text: None")
            logger.info(f"  Text Length: {len(text) if text else 0}")
            logger.info("")

def check_website_chunks():
    """Check website chunks in the database."""
    logger.info("Checking website chunks in the database...")
    
    # Query to get all website chunks
    chunks_query = """
    SELECT
        wc.id,
        wc.website_id,
        wc.url,
        wc.text,
        w.name as website_name
    FROM
        website_chunks wc
    JOIN
        websites w ON wc.website_id = w.id
    LIMIT 10
    """
    
    result = supabase.execute_query(chunks_query)
    
    if isinstance(result, dict) and "error" in result:
        logger.error(f"Error getting website chunks: {result['error']}")
    else:
        logger.info(f"Found {len(result)} website chunks")
        
        # Check each chunk
        for i, chunk in enumerate(result):
            chunk_id = chunk.get("id")
            website_id = chunk.get("website_id")
            url = chunk.get("url")
            text = chunk.get("text")
            website_name = chunk.get("website_name")
            
            logger.info(f"Chunk {i+1}:")
            logger.info(f"  ID: {chunk_id}")
            logger.info(f"  Website ID: {website_id}")
            logger.info(f"  Website Name: {website_name}")
            logger.info(f"  URL: {url}")
            logger.info(f"  Text: {text[:100]}..." if text else "  Text: None")
            logger.info(f"  Text Length: {len(text) if text else 0}")
            logger.info("")

def main():
    """Main function to check document and website chunks."""
    check_document_chunks()
    check_website_chunks()

if __name__ == "__main__":
    main()
