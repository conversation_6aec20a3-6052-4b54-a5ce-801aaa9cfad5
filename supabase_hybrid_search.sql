-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS hybrid_search_document_chunks(text, vector, float, int);
DROP FUNCTION IF EXISTS hybrid_search_website_chunks(text, vector, float, int);

-- Create function for hybrid document search (combining vector similarity and text search)
CREATE OR REPLACE FUNCTION hybrid_search_document_chunks(
    query_text text,
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    url TEXT,
    domain TEXT,
    title TEXT,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        dc.metadata,
        d.file_path as url,
        d.main_category as domain,
        d.display_name as title,
        -- Combine vector similarity with text match score (70% vector, 30% text)
        (0.7 * (1 - (dc.embedding <=> query_embedding)) + 
         0.3 * ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', query_text))
        ) as similarity,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE 
        -- Vector similarity threshold
        (1 - (dc.embedding <=> query_embedding) > match_threshold)
        -- Text search condition (optional but helps with relevance)
        OR (dc.text ILIKE '%' || query_text || '%')
    ORDER BY similarity DESC
    LIMIT match_count;
END;
$$;

-- Create function for hybrid website search (combining vector similarity and text search)
CREATE OR REPLACE FUNCTION hybrid_search_website_chunks(
    query_text text,
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id uuid,
    website_id uuid,
    chunk_index int,
    text text,
    metadata jsonb,
    url text,
    domain text,
    title text,
    similarity float,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id,
        wc.website_id,
        wc.chunk_index,
        wc.text,
        wc.metadata,
        w.url,
        w.domain,
        w.title,
        -- Combine vector similarity with text match score (70% vector, 30% text)
        (0.7 * (1 - (wc.embedding <=> query_embedding)) + 
         0.3 * ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', query_text))
        ) as similarity,
        'website'::TEXT as source_type
    FROM website_chunks wc
    JOIN websites w ON wc.website_id = w.id
    WHERE 
        -- Vector similarity threshold
        (1 - (wc.embedding <=> query_embedding) > match_threshold)
        -- Text search condition (optional but helps with relevance)
        OR (wc.text ILIKE '%' || query_text || '%')
    ORDER BY similarity DESC
    LIMIT match_count;
END;
$$;
