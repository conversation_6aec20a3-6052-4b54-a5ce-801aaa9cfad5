# 🚀 Deploy RailGPT to GCP - Ready to Go!

## ✅ What's Already Done
- ✅ Environment file created (`.env`)
- ✅ All deployment scripts ready
- ✅ Database configured (Supabase)
- ✅ Docker files prepared

## 🔧 What You Need to Do Now

### Step 1: Install Google Cloud SDK (5 minutes)

**Windows Installation:**
1. Download: https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe
2. Run the installer
3. Follow the setup wizard
4. Restart your terminal/PowerShell

**Verify Installation:**
```bash
gcloud --version
```

### Step 2: Login to Google Cloud (2 minutes)

```bash
# Login to your Google account
gcloud auth login

# Set up application default credentials
gcloud auth application-default login
```

### Step 3: Get Your API Keys (3 minutes)

**🔑 Gemini API Key (Required):**
1. Go to: https://makersuite.google.com/app/apikey
2. Sign in with Google
3. Click "Create API Key"
4. Copy the key (starts with "AI...")

**🔑 Supabase Service Key (Required):**
1. Go to: https://supabase.com/dashboard/project/rkllidjktazafeinezgo/settings/api
2. Copy the "service_role" key (long key starting with "eyJ...")

### Step 4: Update Environment File (2 minutes)

Edit the `.env` file and replace these values:

```env
# Replace with your actual keys:
SUPABASE_KEY=your_actual_supabase_service_key_here
GEMINI_API_KEY=your_actual_gemini_api_key_here

# Optional: Add your domain if you have one
DOMAIN=your-domain.com

# Optional: Change project ID if you want
PROJECT_ID=railgpt-production-2025
```

### Step 5: Deploy Everything (10 minutes)

**Option A: One Command Deployment (Recommended)**
```bash
# Deploy everything automatically
./deploy/gcp/setup-complete.sh
```

**Option B: Step by Step**
```bash
# Deploy backend first
./deploy/gcp/deploy-backend.sh

# Then deploy frontend
./deploy/gcp/deploy-frontend.sh
```

## 🎯 Expected Results

After deployment, you'll get:

### Backend (Cloud Run)
- URL: `https://railgpt-backend-xxx.a.run.app`
- API Docs: `https://railgpt-backend-xxx.a.run.app/docs`
- Auto-scaling: 0-10 instances

### Frontend (Cloud Storage + CDN)
- URL: `https://storage.googleapis.com/your-bucket/index.html`
- Or custom domain if configured

### Costs
- **Development**: $0/month (free tier)
- **Light usage**: $5-15/month
- **Medium usage**: $20-40/month

## 🔍 Troubleshooting

### If deployment fails:

**1. Check Google Cloud SDK:**
```bash
gcloud --version
gcloud auth list
```

**2. Check API keys:**
- Verify Gemini key works: https://makersuite.google.com/app/apikey
- Verify Supabase key is the service_role key

**3. Check project permissions:**
```bash
gcloud projects list
gcloud config set project railgpt-production-2025
```

**4. Enable required APIs:**
```bash
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable storage.googleapis.com
```

## 📞 Need Help?

### Quick Fixes:
- **Permission denied**: Run `gcloud auth login` again
- **API not enabled**: The script will enable them automatically
- **Billing not enabled**: Go to Google Cloud Console and enable billing

### Check Status:
```bash
# Check backend
gcloud run services list

# Check frontend
gsutil ls

# Check logs
gcloud logs read --service=railgpt-backend --limit=10
```

## 🎉 After Deployment

1. **Test your application**
2. **Upload some documents**
3. **Try the chat functionality**
4. **Monitor costs in Google Cloud Console**

## 💡 Pro Tips

- Start with the free tier to test everything
- Enable billing alerts to monitor costs
- Use a custom domain for production
- Set up monitoring and alerts

---

## 🚀 Ready? Let's Deploy!

1. Install Google Cloud SDK
2. Login: `gcloud auth login`
3. Update `.env` with your API keys
4. Run: `./deploy/gcp/setup-complete.sh`

**Your RailGPT application will be live in ~10 minutes!**
