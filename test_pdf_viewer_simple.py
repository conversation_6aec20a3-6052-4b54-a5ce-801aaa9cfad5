#!/usr/bin/env python3
"""
Simple test script to verify PDF viewer functionality.
"""

import requests
import json

def test_query_and_sources():
    """Test that queries return document sources with proper links."""
    print("🔧 Testing Document Source Generation")
    print("=" * 50)
    
    test_queries = [
        "What is authority transfer?",
        "What is ACP?",
        "railway maintenance"
    ]
    
    for query in test_queries:
        print(f"\n📝 Testing query: '{query}'")
        
        try:
            response = requests.post(
                "http://localhost:8000/api/query",
                json={"query": query, "model": "gemini-2.0-flash"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                document_sources = data.get('document_sources', [])
                llm_fallback = data.get('llm_fallback', True)
                
                print(f"   ✅ Query successful")
                print(f"   📄 Document sources: {len(document_sources)}")
                print(f"   🤖 LLM fallback: {llm_fallback}")
                
                if document_sources:
                    print(f"   🎯 Found document sources!")
                    for i, source in enumerate(document_sources[:2]):
                        if isinstance(source, dict):
                            filename = source.get('filename', 'Unknown')
                            page = source.get('page', 1)
                            link = source.get('link', '')
                            print(f"      Source {i+1}: {filename} (Page {page})")
                            if '/viewer?file=' in link:
                                print(f"      ✅ Correct link: {link}")
                            else:
                                print(f"      ❌ Wrong link format: {link}")
                        else:
                            print(f"      Source {i+1}: {source}")
                else:
                    if llm_fallback:
                        print(f"   ⚠️  No document sources - using LLM fallback")
                    else:
                        print(f"   ❌ No document sources but not using LLM fallback")
                        
            else:
                print(f"   ❌ Query failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

def test_pdf_serving():
    """Test PDF file serving."""
    print(f"\n🔧 Testing PDF File Serving")
    print("=" * 50)
    
    test_files = ["ACP%20110V.pdf", "Authority%20Transfer%20Declaration.pdf"]
    
    for filename in test_files:
        print(f"\n📄 Testing: {filename}")
        
        try:
            url = f"http://localhost:8000/api/documents/view/{filename}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                size = len(response.content)
                is_pdf = response.content.startswith(b'%PDF')
                
                print(f"   ✅ Status: {response.status_code}")
                print(f"   📋 Content-Type: {content_type}")
                print(f"   📏 Size: {size:,} bytes")
                print(f"   🔍 Valid PDF: {is_pdf}")
                
                if 'pdf' in content_type.lower() and is_pdf:
                    print(f"   🎯 PDF serving works correctly!")
                else:
                    print(f"   ⚠️  PDF serving may have issues")
                    
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

def main():
    """Run simple PDF viewer tests."""
    print("🚀 RailGPT PDF Viewer Simple Test")
    print("=" * 60)
    
    # Test backend health
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running")
        else:
            print("❌ Backend server not responding")
            return
    except Exception as e:
        print(f"❌ Backend server not accessible: {str(e)}")
        return
    
    # Run tests
    test_query_and_sources()
    test_pdf_serving()
    
    print(f"\n🎯 Manual Testing Instructions:")
    print("1. Open: http://localhost:3000")
    print("2. Ask: 'What is authority transfer?' or 'What is ACP?'")
    print("3. Look for document source cards with clickable links")
    print("4. Click on source links to test PDF viewer")
    print("5. Test viewer options: Pro PDF, React PDF, Simple")
    print("6. Test zoom, navigation, and download features")
    
    print(f"\n📋 Expected PDF Viewer URLs:")
    print("   http://localhost:3000/viewer?file=ACP%20110V.pdf&page=1")
    print("   http://localhost:3000/viewer?file=Authority%20Transfer%20Declaration.pdf&page=1")

if __name__ == "__main__":
    main()
